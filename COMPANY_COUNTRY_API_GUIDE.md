# Company & Country API Documentation

## Overview

This guide documents the Company and Country management API endpoints for the HRMS system. These endpoints handle company registration, updates, and country management with full support for multi-tenant operations.

## 🏢 Company Management API

### Authentication & Authorization

All company endpoints require:
- **Authentication**: Bearer token (`Authorization: Bearer <token>`)
- **Roles**: Specific role requirements listed per endpoint

---

### 1. Create Company

**Endpoint**: `POST /add_company`  
**Roles**: `hr`

Creates a new company with automatic database provisioning and optional country assignment.

#### Request Body
```json
{
  "company_name": "Acme Corporation Ltd",
  "company_tin": "*********",
  "phone_number": "+************",
  "country_id": "uuid-of-country",
  "company_id": "custom-uuid",
  "database_name": "custom_db_name"
}
```

#### Field Details
- `company_name` *(required)*: Company name
- `company_tin` *(required)*: Tax Identification Number (must be unique)
- `phone_number` *(optional)*: Company phone number (must be unique if provided)
- `country_id` *(optional)*: UUID of country from countries table
- `company_id` *(optional)*: Custom company ID (auto-generated if not provided)
- `database_name` *(optional)*: Custom database name (auto-generated if not provided)

#### Response
```json
{
  "message": "Company 'Acme Corporation Ltd' added successfully. Rwanda leave policies configured automatically.",
  "company": {
    "company_id": "uuid",
    "company_name": "Acme Corporation Ltd",
    "database_name": "*********_abc123_db",
    "company_tin": "*********",
    "phone_number": "+************",
    "country_id": "country-uuid",
    "country": {
      "code": "RW",
      "name": "Rwanda",
      "currency": "RWF"
    },
    "devices": [],
    "created_at": "01-01-2024 10:00:00"
  },
  "user_assigned": true,
  "assignment_details": "User assigned successfully",
  "leave_setup": {
    "success": true,
    "message": "Successfully processed Rwanda leave setup. Created 12 new policies.",
    "created_policies": [...]
  }
}
```

#### Features
- ✅ **Auto-generates** company_id and database_name if not provided
- ✅ **Creates dedicated database** for the company
- ✅ **Validates country_id** if provided
- ✅ **Automatic leave setup** for supported countries (Rwanda)
- ✅ **Assigns current user** to the company
- ✅ **Unique constraints** on TIN and phone number

---

### 2. Update Company

**Endpoint**: `PATCH /api/companies/<company_id>`  
**Roles**: `admin`, `super-admin`, `hr`

Updates an existing company with partial data. All fields are optional.

#### Request Body
```json
{
  "company_name": "Updated Company Name",
  "company_tin": "*********",
  "phone_number": "+************",
  "country_id": "new-country-uuid"
}
```

#### Field Details
- `company_name` *(optional)*: New company name
- `company_tin` *(optional)*: New TIN (must be unique)
- `phone_number` *(optional)*: New phone number (must be unique)
- `country_id` *(optional)*: New country ID (validated if provided)

#### Response
```json
{
  "success": true,
  "message": "Company updated successfully",
  "company": {
    "company_id": "uuid",
    "company_name": "Updated Company Name",
    "database_name": "original_db_name",
    "company_tin": "*********",
    "phone_number": "+************",
    "country_id": "new-country-uuid",
    "country": {
      "code": "KE",
      "name": "Kenya",
      "currency": "KES"
    },
    "devices": [],
    "created_at": "01-01-2024 10:00:00"
  }
}
```

#### Features
- ✅ **Partial updates** - only provided fields are updated
- ✅ **Country serialization** - returns country code, name, and currency
- ✅ **Validation** - ensures unique constraints and country existence
- ✅ **Non-updatable fields** - company_id, database_name, created_at are protected

---

### 3. Get All Companies

**Endpoint**: `GET /get_companies`  
**Roles**: Any authenticated user

Retrieves all companies in the system.

#### Response
```json
{
  "companies": [
    {
      "company_id": "uuid",
      "company_name": "Company 1",
      "database_name": "company1_db",
      "company_tin": "*********",
      "phone_number": "+************",
      "country_id": "country-uuid",
      "country": {
        "code": "RW",
        "name": "Rwanda",
        "currency": "RWF"
      },
      "devices": [],
      "created_at": "01-01-2024 10:00:00"
    }
  ]
}
```

---

## 🌍 Country Management API

### 1. Get All Countries

**Endpoint**: `GET /api/countries`  
**Roles**: `admin`, `super-admin`, `hr`

Retrieves all countries with payroll readiness status.

#### Response
```json
{
  "success": true,
  "countries": [
    {
      "country_id": "uuid",
      "name": "Rwanda",
      "code": "RW",
      "currency": "RWF",
      "time_zone": "Africa/Kigali",
      "date_format": "dd/mm/yyyy",
      "created_at": "2024-01-01 10:00:00",
      "updated_at": "2024-01-01 10:00:00",
      "payroll_status": {
        "is_ready": true,
        "policies_count": 5,
        "employee_types_count": 3,
        "deduction_types_count": 4,
        "setup_completion": 100
      }
    }
  ],
  "total_count": 1
}
```

---

### 2. Get Country by ID

**Endpoint**: `GET /api/countries/<country_id>`  
**Roles**: `admin`, `super-admin`, `hr`

Retrieves detailed country information including payroll configuration.

---

### 3. Create Country

**Endpoint**: `POST /api/countries`  
**Roles**: `admin`, `super-admin`

Creates a new country in the system.

---

### 4. Update Country

**Endpoint**: `PUT /api/countries/<country_id>`  
**Roles**: `admin`, `super-admin`

Updates country details (name, currency, time_zone, date_format).

---

## 🔧 Integration Examples

### Frontend Usage Pattern

```javascript
// 1. Get countries for dropdown
const countriesResponse = await fetch('/api/countries', {
  headers: { 'Authorization': `Bearer ${token}` }
});
const countries = await countriesResponse.json();

// 2. Create company with country
const createResponse = await fetch('/add_company', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    company_name: 'New Company',
    company_tin: '*********',
    country_id: selectedCountryId
  })
});

// 3. Update company
const updateResponse = await fetch(`/api/companies/${companyId}`, {
  method: 'PATCH',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    company_name: 'Updated Name',
    country_id: newCountryId
  })
});
```

---

## 📋 Error Handling

### Common Error Responses

#### 400 - Bad Request
```json
{
  "success": false,
  "message": "Company TIN already exists"
}
```

#### 404 - Not Found
```json
{
  "success": false,
  "message": "Company not found"
}
```

#### 401 - Unauthorized
```json
{
  "message": "Token is missing or invalid"
}
```

#### 403 - Forbidden
```json
{
  "message": "Insufficient permissions"
}
```

---

## 🚀 Quick Start

1. **Get available countries**:
   ```bash
   GET /api/countries
   ```

2. **Create a company**:
   ```bash
   POST /add_company
   {
     "company_name": "Test Company",
     "company_tin": "*********",
     "country_id": "country-uuid"
   }
   ```

3. **Update the company**:
   ```bash
   PATCH /api/companies/{company_id}
   {
     "company_name": "Updated Name"
   }
   ```

---

## 📝 Notes

- **Database Creation**: Each company gets its own database automatically
- **Leave Setup**: Rwanda companies get automatic leave policy setup
- **Country Validation**: country_id is validated against the countries table
- **Unique Constraints**: TIN and phone numbers must be unique across companies
- **Role-Based Access**: Different endpoints require different permission levels
