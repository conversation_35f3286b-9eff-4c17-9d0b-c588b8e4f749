# Contact Us API Documentation

## Overview

The Contact Us API allows customers to submit inquiries through your Next.js frontend. The API handles form validation, rate limiting, and sends email notifications to both the admin team and the customer.

## Endpoint

### Submit Contact Form

**URL:** `POST /api/contact/submit`

**Description:** Submit a contact us form with customer inquiry

**Rate Limiting:** 5 requests per hour per IP address

#### Request Body

```json
{
  "name": "<PERSON>",                    // Required - Customer name (2-100 chars)
  "email": "<EMAIL>",           // Required - Valid email address
  "phone": "+**********",                // Required - Phone number (7-20 digits)
  "message": "I'm interested in...",     // Required - Message (10-2000 chars)
  "company": "Example Corp",             // Optional - Company name (max 100 chars)
  "subject": "Demo Request",             // Optional - Subject line (max 200 chars)
  "inquiry_type": "demo"                 // Optional - Type: demo, support, sales, partnership, other
}
```

#### Response

**Success (200):**
```json
{
  "success": true,
  "message": "Thank you for contacting us! We'll get back to you within 24 hours."
}
```

**Validation Error (400):**
```json
{
  "success": false,
  "message": "name is required; Invalid email address"
}
```

**Rate Limit Exceeded (429):**
```json
{
  "success": false,
  "message": "Rate limit exceeded. Please try again in 60 minutes."
}
```

**Server Error (500):**
```json
{
  "success": false,
  "message": "Internal server error. Please try again later."
}
```

## Field Validation

### Required Fields
- **name**: 2-100 characters, HTML sanitized
- **email**: Valid email format, converted to lowercase
- **phone**: 7-20 digits (allows common formatting characters)
- **message**: 10-2000 characters, HTML sanitized

### Optional Fields
- **company**: Max 100 characters, HTML sanitized
- **subject**: Max 200 characters, HTML sanitized
- **inquiry_type**: Must be one of: demo, support, sales, partnership, other (defaults to 'other')

## Email Notifications

### Admin Notification
- **Recipient:** <EMAIL>
- **Subject:** "New Contact Inquiry: [subject]"
- **Content:** Full customer details and message
- **Queue:** High priority email queue

### Customer Auto-Response
- **Recipient:** Customer's email
- **Subject:** "Thank you for contacting KaziSync"
- **Content:** Confirmation message with next steps
- **Queue:** Normal priority email queue

## Rate Limiting

- **Limit:** 5 requests per hour per IP address
- **Storage:** In-memory (for production, consider Redis)
- **Cleanup:** Automatic cleanup of expired rate limit entries

## Security Features

- HTML sanitization of all text inputs
- Email validation
- Phone number format validation
- Rate limiting to prevent spam
- IP address logging for tracking

## Integration with Existing Email System

The Contact API integrates seamlessly with your existing email infrastructure:
- Uses your configured SMTP settings (<EMAIL>)
- Leverages Redis + Celery for asynchronous email processing
- Follows the same email service patterns as other modules

## Health Check

### Health Check Endpoint

**URL:** `GET /api/contact/health`

**Response:**
```json
{
  "success": true,
  "message": "Contact service is running",
  "timestamp": "2025-01-01T12:00:00.000000"
}
```

## Frontend Integration Example

```javascript
// Example Next.js form submission
const submitContactForm = async (formData) => {
  try {
    const response = await fetch('/api/contact/submit', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(formData)
    });
    
    const result = await response.json();
    
    if (result.success) {
      // Show success message
      alert(result.message);
    } else {
      // Show error message
      alert(result.message);
    }
  } catch (error) {
    console.error('Error submitting form:', error);
    alert('An error occurred. Please try again.');
  }
};
```

## Error Handling

The API provides comprehensive error handling:
- Input validation errors with specific field messages
- Rate limiting with clear retry instructions
- Server errors with fallback contact information
- Detailed logging for debugging

## Monitoring

- All submissions are logged with IP addresses
- Email sending success/failure is tracked
- Rate limiting events are logged
- Error details are captured for debugging

## Production Considerations

1. **Rate Limiting Storage**: Consider using Redis instead of in-memory storage for production
2. **Email Monitoring**: Monitor email delivery success rates
3. **Spam Protection**: Consider adding CAPTCHA for additional protection
4. **Analytics**: Track inquiry types and response times
5. **Backup Contact**: Ensure fallback contact methods are available

## Testing

Use the health check endpoint to verify the service is running:
```bash
curl -X GET https://your-api-domain.com/api/contact/health
```

Test form submission:
```bash
curl -X POST https://your-api-domain.com/api/contact/submit \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>",
    "phone": "+**********",
    "message": "This is a test message"
  }'
```
