# Document Management System with Folder Organization

## 🎯 Overview

This enhanced Document Management System provides hierarchical folder organization, allowing users to create custom folder structures for better document organization. Users can create folders like "Employees > Alex Rugema > Contracts" and organize documents accordingly.

## 🏗️ Architecture

### Models Structure
```
Central Database:
├── CompanyStorageQuota (storage limits per company)
└── DocumentCategory (CONTRACT, ID, CERTIFICATE, etc.)

Tenant Database (per company):
├── CompanyDocument (documents with folder relationships)
├── DocumentFolder (hierarchical folder structure)
└── DocumentReminder (expiry notifications)
```

### API Structure
```
Folders API (/api/folders/*):
├── POST /api/folders - Create folder
├── GET /api/folders/tree - Get folder tree
├── GET /api/folders/{id} - Get folder details
├── PUT /api/folders/{id} - Update folder
├── DELETE /api/folders/{id} - Delete folder
├── PUT /api/folders/{id}/move - Move folder
├── GET /api/folders/{id}/documents - Get folder documents
└── GET /api/folders/search - Search folders

Documents API (/api/documents/*):
├── POST /api/documents/upload - Upload with folder assignment
├── PUT /api/documents/move-to-folder - Move documents to folder
└── GET /api/documents?folder_id={id} - Filter by folder
```

## 📁 Folder System Features

### Hierarchical Structure
- **Unlimited nesting**: Create subfolders within folders
- **Full path tracking**: `/Employees/Alex Rugema/Contracts`
- **Breadcrumb navigation**: Easy navigation through folder paths
- **Tree view**: Visual representation of folder hierarchy

### Folder Management
- **Create folders**: With custom names, colors, and icons
- **Private folders**: Role-based access control
- **Move folders**: Drag and drop folder reorganization
- **Delete folders**: With content handling options
- **Bulk operations**: Create multiple folders at once

### Document Organization
- **Folder assignment**: Documents can be placed in specific folders
- **Move documents**: Between folders with bulk operations
- **Folder filtering**: Search documents within specific folders
- **Recursive search**: Include subfolder documents in searches

## 🚀 Quick Start

### 1. Database Setup

Run the migration script on your central database and all tenant databases:

```bash
# Central database
psql -d your_central_db -f database_migrations_document_folders.sql

# Each tenant database
psql -d company_db_1 -f database_migrations_document_folders.sql
psql -d company_db_2 -f database_migrations_document_folders.sql
```

### 2. API Registration

Add the new blueprints to your Flask app:

```python
from application.Routes.documents.documents_api import documents_api
from application.Routes.documents.folders_api import folders_api

app.register_blueprint(documents_api, url_prefix='/api')
app.register_blueprint(folders_api, url_prefix='/api')
```

### 3. Environment Configuration

```bash
# Storage provider settings
STORAGE_PROVIDER=S3
S3_ENDPOINT=https://s3.contabo.com
S3_ACCESS_KEY=your_access_key
S3_SECRET_KEY=your_secret_key

# Notification settings
EMAIL_NOTIFICATIONS_ENABLED=true
WHATSAPP_NOTIFICATIONS_ENABLED=false
```

## 📋 API Usage Examples

### Create Folder Structure

```bash
# 1. Create root folder
POST /api/folders
{
  "folder_name": "Employees",
  "description": "Employee-related documents"
}

# 2. Create subfolder
POST /api/folders
{
  "folder_name": "Alex Rugema",
  "parent_folder_id": "uuid-of-employees-folder",
  "description": "Alex's personal documents"
}

# 3. Create contracts folder
POST /api/folders
{
  "folder_name": "Contracts",
  "parent_folder_id": "uuid-of-alex-folder",
  "color": "#4CAF50",
  "icon": "file-contract"
}
```

### Upload Document to Folder

```bash
POST /api/documents/upload
Content-Type: multipart/form-data

file: [contract.pdf]
document_name: "Employment Contract"
document_category: "CONTRACT"
folder_id: "uuid-of-contracts-folder"
employee_id: "uuid-of-alex"
expiry_date: "2025-12-31"
```

### Get Folder Tree

```bash
GET /api/folders/tree?include_documents=true

Response:
{
  "success": true,
  "tree": [
    {
      "folder_id": "uuid-1",
      "folder_name": "Employees",
      "full_path": "/Employees",
      "document_count": 0,
      "subfolders": [
        {
          "folder_id": "uuid-2",
          "folder_name": "Alex Rugema",
          "full_path": "/Employees/Alex Rugema",
          "document_count": 2,
          "subfolders": [
            {
              "folder_id": "uuid-3",
              "folder_name": "Contracts",
              "full_path": "/Employees/Alex Rugema/Contracts",
              "document_count": 5,
              "documents": [
                {
                  "document_id": "uuid-doc-1",
                  "document_name": "Employment Contract",
                  "folder_path": "/Employees/Alex Rugema/Contracts"
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

### Search Documents in Folder

```bash
# Search within specific folder
GET /api/documents/search?q=contract&folder_id=uuid-of-contracts-folder

# Include subfolders in search
GET /api/documents/search?q=contract&folder_id=uuid-of-employees-folder&include_subfolders=true

# Filter by folder in main listing
GET /api/documents?folder_id=uuid-of-contracts-folder&page=1&per_page=20
```

### Move Documents Between Folders

```bash
# Move single document
PUT /api/documents/{document_id}/move-to-folder
{
  "folder_id": "uuid-of-new-folder"
}

# Move multiple documents
PUT /api/documents/move-to-folder
{
  "document_ids": ["uuid-1", "uuid-2", "uuid-3"],
  "folder_id": "uuid-of-target-folder"
}
```

### Move Folders

```bash
PUT /api/folders/{folder_id}/move
{
  "new_parent_id": "uuid-of-new-parent"
}
```

## 🔐 Security & Permissions

### Folder Permissions
```python
# Private folders with role restrictions
{
  "folder_name": "Confidential",
  "is_private": true,
  "allowed_roles": ["hr", "admin"]
}
```

### Access Control
- **Public folders**: All company users can access
- **Private folders**: Only specified roles can access
- **Document inheritance**: Documents inherit folder permissions
- **Audit logging**: All folder operations are logged

## 📊 Advanced Features

### Bulk Operations
```bash
# Create multiple folders
POST /api/folders/bulk-create
{
  "folders": [
    {
      "folder_name": "HR Policies",
      "description": "Company policies"
    },
    {
      "folder_name": "Employee Handbook",
      "parent_folder_id": "uuid-of-hr-policies",
      "color": "#2196F3"
    }
  ]
}
```

### Folder Statistics
```sql
-- Get folder statistics using PostgreSQL function
SELECT * FROM get_folder_stats('your-company-id');
```

### Cleanup Operations
```sql
-- Cleanup old reminders
SELECT cleanup_old_reminders(90); -- Delete reminders older than 90 days
```

## 🔍 Search & Filtering

### Folder-Based Search
- **Exact folder**: Documents only in specified folder
- **Recursive search**: Include all subfolders
- **Path-based filtering**: Filter by folder path patterns
- **Combined filters**: Mix folder, category, date, employee filters

### Advanced Queries
```python
# Search in specific folder hierarchy
documents = CompanyDocument.search_documents(
    session=session,
    company_id=company_id,
    search_term="contract",
    folder_id=folder_id,
    include_subfolders=True,
    category="CONTRACT"
)
```

## 📈 Monitoring & Analytics

### Storage Tracking
```bash
GET /api/documents/storage/usage
# Returns company storage usage with folder breakdown
```

### Folder Analytics
- **Document count per folder**
- **Storage usage by folder**
- **Access patterns and popular folders**
- **Folder creation trends**

## 🔧 Configuration Options

### Environment Variables
```bash
# Storage settings
STORAGE_PROVIDER=S3|AZURE_BLOB|LOCAL
S3_ENDPOINT=https://your-s3-endpoint.com
S3_ACCESS_KEY=your_key
S3_SECRET_KEY=your_secret

# Folder settings
MAX_FOLDER_DEPTH=10  # Maximum nesting level
DEFAULT_FOLDER_COLOR=#757575
ENABLE_FOLDER_PERMISSIONS=true

# Notification settings
EMAIL_NOTIFICATIONS_ENABLED=true
WHATSAPP_NOTIFICATIONS_ENABLED=false
REMINDER_DAYS_BEFORE=[30,14,7,1]
```

### Database Configuration
```sql
-- Custom folder depth limits
ALTER TABLE document_folders ADD CONSTRAINT max_folder_depth
CHECK (get_folder_depth(folder_id) <= 10);

-- Automatic cleanup policies
CREATE POLICY folder_cleanup_policy ON document_folders
FOR DELETE USING (created_at < NOW() - INTERVAL '1 year' AND document_count = 0);
```

## 🚨 Best Practices

### Folder Organization
1. **Logical hierarchy**: `Company > Department > Employee > Document Type`
2. **Consistent naming**: Use clear, descriptive folder names
3. **Access levels**: Apply appropriate privacy settings
4. **Regular cleanup**: Archive old folders periodically

### Performance Optimization
1. **Indexing**: All foreign keys and search fields are indexed
2. **Pagination**: Use pagination for large folder listings
3. **Caching**: Cache frequently accessed folder trees
4. **Bulk operations**: Use bulk APIs for multiple operations

### Security Considerations
1. **Role validation**: Always check user permissions
2. **Audit logging**: Log all folder and document operations
3. **Data isolation**: Ensure tenant data separation
4. **Access patterns**: Monitor and restrict suspicious activity

## 🐛 Troubleshooting

### Common Issues

**"Parent folder not found"**
- Verify the `parent_folder_id` exists and belongs to the same company
- Check for circular references in folder hierarchy

**"Storage quota exceeded"**
- Check current storage usage: `GET /api/documents/storage/usage`
- Clean up old documents or request quota increase

**"Permission denied"**
- Verify user has required role for private folders
- Check folder's `allowed_roles` setting

### Database Issues

**Missing tables after migration:**
```sql
-- Verify tables exist
SELECT table_name FROM information_schema.tables
WHERE table_name IN ('document_folders', 'document_reminders');
```

**Index performance:**
```sql
-- Check index usage
SELECT * FROM pg_stat_user_indexes
WHERE tablename IN ('document_folders', 'company_documents');
```

## 📚 API Reference

### Folder Endpoints
- `POST /api/folders` - Create folder
- `GET /api/folders/tree` - Get folder tree
- `GET /api/folders/{id}` - Get folder details
- `PUT /api/folders/{id}` - Update folder
- `DELETE /api/folders/{id}` - Delete folder
- `PUT /api/folders/{id}/move` - Move folder
- `GET /api/folders/{id}/path` - Get folder path
- `GET /api/folders/{id}/documents` - Get folder documents
- `GET /api/folders/search` - Search folders
- `POST /api/folders/bulk-create` - Bulk create folders

### Document Endpoints
- `POST /api/documents/upload` - Upload document (with folder support)
- `PUT /api/documents/move-to-folder` - Move documents to folder
- `GET /api/documents?folder_id={id}` - Filter documents by folder

### Storage Endpoints
- `GET /api/documents/storage/usage` - Get storage usage
- `GET /api/documents/categories` - Get document categories

This folder system transforms your document management from a flat structure to a powerful hierarchical organization, making it easy for users to create custom folder structures like "Employees > Alex Rugema > Contracts" and efficiently manage their documents.
