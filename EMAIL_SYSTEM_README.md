# 📧 KaziSync HRMS - Email Management System

## Overview

The KaziSync HRMS Email Management System provides comprehensive email functionality including:

- **Email-based OTP Authentication** - Secure login with email verification
- **Template Management** - Professional, responsive email templates
- **Notification Engine** - Event-driven notifications for all HRMS modules
- **Background Processing** - Asynchronous email sending with Redis + Celery
- **Multi-tenant Support** - Company-specific email configurations and branding

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Request   │───▶│  Celery Task    │───▶│   cPanel SMTP   │
│                 │    │                 │    │                 │
│ • OTP Login     │    │ • Redis Queue   │    │ info@kazisync   │
│ • Notifications │    │ • Retry Logic   │    │ • Professional  │
│ • Bulk Emails   │    │ • Error Handle  │    │ • Reliable      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Configure Environment Variables

Create/update your `.env` file:

```env
# Email Configuration
MAIL_SERVER=mail.kazisync.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password
MAIL_DEFAULT_SENDER=KaziSync HRMS <<EMAIL>>

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# OTP Settings
OTP_EXPIRY_MINUTES=5
OTP_MAX_ATTEMPTS=3
```

### 3. Start Redis Server

```bash
redis-server
```

### 4. Start Celery Worker

```bash
celery -A app.celery worker --loglevel=info
```

### 5. Start Celery Beat (for scheduled tasks)

```bash
celery -A app.celery beat --loglevel=info
```

### 6. Test the System

```bash
python test_email_system.py
```

## 📱 Email-Based OTP Authentication

### Login Flow

1. **User submits credentials** → `POST /api/auth/login-with-otp`
2. **System validates credentials** → Generates OTP and sends email
3. **User receives email** → Contains 6-digit OTP code
4. **User submits OTP** → `POST /api/auth/verify-otp`
5. **System validates OTP** → Returns JWT token for authenticated session

### API Endpoints

#### Login with OTP
```http
POST /api/auth/login-with-otp
Content-Type: application/json

{
    "username": "<EMAIL>",
    "password": "password123"
}
```

**Response:**
```json
{
    "success": true,
    "message": "OTP sent to your email address",
    "otp_session_id": "session_uuid",
    "email_masked": "u***@company.com",
    "next_step": "verify_otp"
}
```

#### Verify OTP
```http
POST /api/auth/verify-otp
Content-Type: application/json

{
    "otp_session_id": "session_uuid",
    "otp_code": "123456"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Login successful",
    "access_token": "jwt_token",
    "user_info": {...}
}
```

## 📧 Email Templates

### Available Templates

- **Authentication**
  - `auth/otp_code.html` - OTP verification email
  - `auth/password_reset.html` - Password reset email
  - `auth/email_verification.html` - Email verification
  - `auth/security_alert.html` - Security notifications

- **Leave Management**
  - `leave/request_submitted.html` - Leave request notifications
  - `leave/request_approved.html` - Leave approval notifications
  - `leave/request_rejected.html` - Leave rejection notifications

- **Payroll**
  - `payroll/payslip_ready.html` - Payslip delivery
  - `payroll/salary_updated.html` - Salary change notifications

- **System**
  - `system/announcement.html` - Company announcements
  - `system/maintenance_notice.html` - Maintenance notifications

### Template Customization

Templates support company-specific branding:

```python
context = {
    'company_name': 'Your Company',
    'company_logo_url': 'https://company.com/logo.png',
    'company_primary_color': '#1e40af',
    'company_secondary_color': '#64748b'
}
```

## 🔔 Notification Engine

### Sending Notifications

```python
from application.services.email.notification_engine import NotificationEngine

notification_engine = NotificationEngine()

# Send leave request notification
notification_engine.send_leave_notification('request_submitted', {
    'employee_name': 'John Doe',
    'leave_type': 'Annual Leave',
    'start_date': '2024-01-15',
    'end_date': '2024-01-20',
    'approver_emails': ['<EMAIL>'],
    'company_id': 'company_uuid'
})
```

### Available Notification Types

- **Leave Management**: `request_submitted`, `request_approved`, `request_rejected`
- **Payroll**: `payslip_ready`, `salary_updated`, `payroll_processed`
- **Recruitment**: `application_received`, `interview_scheduled`, `offer_letter`
- **Onboarding**: `welcome_email`, `task_assigned`, `document_required`
- **System**: `announcement`, `maintenance_notice`, `policy_update`

## ⚙️ Configuration

### Email Priorities

- **High Priority** (OTP, Security): Processed immediately
- **Normal Priority** (Leave, Payroll): Standard processing
- **Low Priority** (Announcements): Background processing

### Rate Limiting

- **OTP Requests**: 5 per hour per user
- **Email Sending**: 100 per hour, 1000 per day
- **Failed Attempts**: Automatic retry with exponential backoff

## 🛠️ Development

### Adding New Email Templates

1. Create template file in `templates/emails/[category]/[template_name].html`
2. Extend the base template: `{% extends "emails/base/base.html" %}`
3. Add template configuration to `NotificationConfig`
4. Test with the notification engine

### Adding New Notification Types

1. Add notification configuration to `application/config/email_config.py`
2. Implement notification method in `NotificationEngine`
3. Create corresponding email template
4. Add API endpoint if needed

## 🔧 Troubleshooting

### Common Issues

1. **Emails not sending**
   - Check SMTP credentials in `.env`
   - Verify `MAIL_SUPPRESS_SEND=False`
   - Check Celery worker is running

2. **OTP not received**
   - Check email address validity
   - Verify rate limiting not exceeded
   - Check spam/junk folder

3. **Template rendering errors**
   - Verify template file exists
   - Check template syntax
   - Ensure all required context variables provided

### Logs

- **Email Service**: Check application logs for email sending status
- **Celery**: Monitor Celery worker logs for task processing
- **Redis**: Check Redis connection and queue status

## 📊 Monitoring

### Email Analytics

- Delivery rates by template type
- Failed delivery tracking
- Queue processing times
- User engagement metrics

### Health Checks

- SMTP connection status
- Redis connectivity
- Celery worker health
- Template availability

## 🔒 Security Features

- **Rate Limiting**: Prevents email spam and abuse
- **Input Validation**: Sanitizes all email content
- **Token Expiry**: OTP codes expire automatically
- **Audit Logging**: Complete email activity tracking
- **Encryption**: Sensitive data encrypted in transit

## 📈 Performance

- **Asynchronous Processing**: Non-blocking email sending
- **Queue Management**: Priority-based email processing
- **Template Caching**: Improved rendering performance
- **Connection Pooling**: Efficient SMTP connections

## 🤝 Integration

The email system integrates seamlessly with:

- **User Authentication**: OTP-based login
- **Leave Management**: Approval workflows
- **Payroll System**: Payslip delivery
- **Recruitment**: Application tracking
- **Onboarding**: Welcome and task emails

## 📞 Support

For issues or questions:

- **Email**: <EMAIL>
- **Documentation**: Check this README and code comments
- **Testing**: Run `python test_email_system.py`
