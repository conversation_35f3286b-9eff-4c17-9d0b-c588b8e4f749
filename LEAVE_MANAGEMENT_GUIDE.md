# 🇷🇼 Rwanda Leave Management System Guide

This guide explains how to set up and use the comprehensive leave management system for Rwanda-based companies.

## 📋 Table of Contents

1. [Overview](#overview)
2. [Rwanda Leave Laws Summary](#rwanda-leave-laws-summary)
3. [System Setup](#system-setup)
4. [Employee Leave Request Process](#employee-leave-request-process)
5. [HR/Admin Approval Process](#hradmin-approval-process)
6. [API Reference](#api-reference)
7. [Troubleshooting](#troubleshooting)

## 🎯 Overview

Your leave management system includes:

- ✅ **Complete CRUD operations** for leave requests
- ✅ **Rwanda-specific leave policies** based on labor laws
- ✅ **Automatic leave balance tracking**
- ✅ **Multi-level approval workflows**
- ✅ **Gender-specific leave types** (maternity/paternity)
- ✅ **Prorated leave calculations** for new employees
- ✅ **Documentation requirements** for certain leave types

## 📖 Rwanda Leave Laws Summary

Based on Rwanda Labor Law and Regulations:

### Annual Leave
- **Entitlement**: 18 working days per year
- **Eligibility**: After 1 year of continuous service
- **Accrual**: 1.5 days per month
- **Carryover**: Maximum 6 days to next year
- **Payment**: Paid leave

### Sick Leave
- **Entitlement**: 30 days per year
- **Eligibility**: Immediate (from start of employment)
- **Documentation**: Medical certificate required
- **Accrual**: 2.5 days per month
- **Payment**: Paid leave

### Maternity Leave
- **Entitlement**: 14 weeks (98 days)
- **Eligibility**: After 6 months of service
- **Gender**: Female employees only
- **Documentation**: Medical certificate required
- **Payment**: Paid leave

### Paternity Leave
- **Entitlement**: 1 week (7 days)
- **Eligibility**: After 6 months of service
- **Gender**: Male employees only
- **Documentation**: Birth certificate required
- **Payment**: Paid leave

### Public Holidays
- **Count**: 13 official public holidays per year
- **Compensation**: Time off in lieu if worked
- **Payment**: Double pay if worked

## 🚀 System Setup

### Step 1: Ensure Rwanda is in Countries Table

```sql
-- Check if Rwanda exists
SELECT * FROM countries WHERE code = 'RW';

-- If not exists, add Rwanda
INSERT INTO countries (country_id, name, code, currency, time_zone, date_format) 
VALUES (gen_random_uuid(), 'Rwanda', 'RW', 'RWF', 'Africa/Kigali', 'dd/mm/yyyy');
```

### Step 2: Set Up Leave Policies

Run the setup script with your company ID:

```bash
python setup_rwanda_leave_policies.py YOUR_COMPANY_ID
```

Example:
```bash
python setup_rwanda_leave_policies.py 12345678-1234-1234-1234-**********12
```

This creates:
- Leave types (Annual, Sick, Maternity, Paternity)
- Rwanda-specific policies for each leave type

### Step 3: Initialize Employee Leave Balances

```bash
python initialize_employee_leave_balances.py YOUR_COMPANY_ID [YEAR]
```

Examples:
```bash
# For current year
python initialize_employee_leave_balances.py 12345678-1234-1234-1234-**********12

# For specific year
python initialize_employee_leave_balances.py 12345678-1234-1234-1234-**********12 2024
```

## 👤 Employee Leave Request Process

### 1. View Available Leave Balances

```bash
GET /api/leave/balances?company_id=YOUR_COMPANY_ID&employee_id=EMPLOYEE_ID
```

### 2. Submit Leave Request

```bash
POST /api/leave/requests
Content-Type: application/json

{
  "company_id": "your-company-id",
  "employee_id": "employee-id",
  "leave_type_id": "annual-leave-type-id",
  "start_date": "2024-12-20",
  "end_date": "2024-12-27",
  "reason": "Christmas vacation"
}
```

### 3. Check Request Status

```bash
GET /api/leave/requests?company_id=YOUR_COMPANY_ID&employee_id=EMPLOYEE_ID
```

## 👨‍💼 HR/Admin Approval Process

### 1. View Pending Requests

```bash
GET /api/leave/requests?company_id=YOUR_COMPANY_ID&status=pending
```

### 2. Approve Request

```bash
POST /api/leave/requests/REQUEST_ID/approve
Content-Type: application/json

{
  "company_id": "your-company-id"
}
```

### 3. Reject Request

```bash
POST /api/leave/requests/REQUEST_ID/reject
Content-Type: application/json

{
  "company_id": "your-company-id",
  "rejection_reason": "Insufficient leave balance"
}
```

## 🔧 API Reference

### Leave Types
- `GET /api/leave/types` - List all leave types
- `POST /api/leave/types` - Create new leave type (Admin/HR only)
- `GET /api/leave/types/{id}` - Get specific leave type
- `PATCH /api/leave/types/{id}` - Update leave type (Admin/HR only)
- `DELETE /api/leave/types/{id}` - Delete leave type (Admin/HR only)

### Leave Policies
- `GET /api/leave/policies` - List all policies
- `POST /api/leave/policies` - Create new policy (Admin/HR only)
- `GET /api/leave/policies/{id}` - Get specific policy
- `PATCH /api/leave/policies/{id}` - Update policy (Admin/HR only)
- `DELETE /api/leave/policies/{id}` - Delete policy (Admin/HR only)

### Leave Balances
- `GET /api/leave/balances` - Get employee balances
- `POST /api/leave/balances` - Create balance (Admin/HR only)
- `PATCH /api/leave/balances/{id}` - Update balance (Admin/HR only)
- `POST /api/leave/balances/adjust` - Adjust balance (Admin/HR only)

### Leave Requests
- `GET /api/leave/requests` - List requests (filtered by user role)
- `POST /api/leave/requests` - Create new request
- `GET /api/leave/requests/{id}` - Get specific request
- `PATCH /api/leave/requests/{id}` - Update request
- `DELETE /api/leave/requests/{id}` - Cancel request
- `POST /api/leave/requests/{id}/approve` - Approve request (Admin/HR only)
- `POST /api/leave/requests/{id}/reject` - Reject request (Admin/HR only)

### Leave Analytics
- `GET /api/leave/analytics/overview` - Comprehensive leave analytics overview

#### **Leave Analytics Overview**
Get comprehensive leave statistics and insights for data-driven decision making.

```bash
GET /api/leave/analytics/overview?company_id={uuid}&period=month&year=2025&month=1
```

**Query Parameters:**
- **company_id**: Required - Company identifier
- **period**: 'month', 'quarter', 'year' (default: 'month')
- **year**: Specific year (default: current year)
- **month**: Specific month (default: current month, only if period='month')

**Response Example:**
```json
{
  "success": true,
  "data": {
    "period_info": {
      "period": "month",
      "year": 2025,
      "month": 1,
      "start_date": "2025-01-01",
      "end_date": "2025-01-31"
    },
    "overall_statistics": {
      "total_applications": 45,
      "approved_applications": 38,
      "pending_applications": 7,
      "approval_rate": 84.44
    },
    "period_statistics": {
      "applications": 25,
      "approved": 22,
      "approval_rate": 88.0,
      "total_days_taken": 125.0
    },
    "leave_type_breakdown": [
      {
        "leave_type": "Annual Leave",
        "code": "ANNUAL",
        "applications": 15,
        "total_days": 75.0,
        "average_days": 5.0
      },
      {
        "leave_type": "Sick Leave",
        "code": "SICK",
        "applications": 8,
        "total_days": 24.0,
        "average_days": 3.0
      }
    ],
    "department_breakdown": [
      {
        "department": "Engineering",
        "applications": 12,
        "total_days": 60.0,
        "unique_employees": 8
      }
    ],
    "status_breakdown": [
      {
        "status": "approved",
        "count": 22,
        "total_days": 110.0
      }
    ]
  }
}
```

#### **Analytics Use Cases**

**1. Monthly Leave Review**
```bash
# Get current month's leave statistics
GET /api/leave/analytics/overview?company_id={uuid}&period=month

# Compare with previous month
GET /api/leave/analytics/overview?company_id={uuid}&period=month&year=2024&month=12
```

**2. Quarterly Planning**
```bash
# Get Q1 leave patterns for planning
GET /api/leave/analytics/overview?company_id={uuid}&period=quarter&year=2025
```

**3. Annual Leave Analysis**
```bash
# Get full year leave statistics
GET /api/leave/analytics/overview?company_id={uuid}&period=year&year=2024
```

#### **Analytics Benefits**

**For HR Managers:**
- **Leave Pattern Analysis**: Identify peak leave periods and plan coverage
- **Department Comparison**: Compare leave usage across departments
- **Policy Effectiveness**: Monitor approval rates and processing efficiency
- **Compliance Monitoring**: Track leave entitlements and usage

**For Department Managers:**
- **Team Planning**: Understand team leave patterns
- **Coverage Planning**: Identify periods needing additional coverage
- **Approval Efficiency**: Monitor approval processing times
- **Employee Wellness**: Track leave utilization for work-life balance

**For Finance Teams:**
- **Leave Liability**: Calculate accrued leave financial impact
- **Budget Planning**: Forecast leave-related costs
- **Payroll Integration**: Understand leave impact on payroll
- **Cost Analysis**: Analyze leave costs by department

## 🔍 Troubleshooting

### Common Issues

1. **"Rwanda not found in countries table"**
   - Solution: Add Rwanda to the countries table (see Step 1 above)

2. **"No leave policies found"**
   - Solution: Run the setup script to create leave policies

3. **"Employee not eligible for leave type"**
   - Check employee hire date and minimum service requirements
   - Verify employee gender for gender-specific leave types

4. **"Leave balance already exists"**
   - This is normal - the script skips existing balances
   - Use the adjust balance API to modify existing balances

### Validation Rules

- Leave requests cannot be in the past
- End date must be after start date
- Employee must have sufficient leave balance
- Some leave types require documentation
- Minimum service requirements must be met

## 📞 Support

For technical support or questions about the leave management system:

1. Check the API documentation at `/api/docs`
2. Review the application logs for error details
3. Ensure all required fields are provided in API requests
4. Verify user permissions for admin/HR operations
