# Logging System Guide

This guide explains the new logging system that separates device operations from application logs.

## Log Files

### 1. Application Logs (`app.log`)
- Contains general application logs
- Includes API requests, business logic, errors
- **Now excludes** device synchronization noise

### 2. Device Logs (`logs/device.log`)
- Contains all device-related operations
- WebSocket communications
- Command processing
- Device status updates
- Automatically rotated (10MB max, 5 backups)

## Monitoring Tools

### 1. Monitor Logs Script (`monitor_logs.py`)

**View application logs (excluding device noise):**
```bash
python3 monitor_logs.py --type app --lines 50
```

**Follow application logs in real-time:**
```bash
python3 monitor_logs.py --type app --follow
```

**View device logs:**
```bash
python3 monitor_logs.py --type device --lines 50
```

**Follow device logs in real-time:**
```bash
python3 monitor_logs.py --type device --follow
```

**Show only errors:**
```bash
python3 monitor_logs.py --type errors --lines 100
```

**Show announcement-related logs:**
```bash
python3 monitor_logs.py --type announcements --lines 100
```

### 2. Filter Logs Script (`filter_logs.py`)

**Filter out device logs from app.log:**
```bash
python3 filter_logs.py --file app.log
```

**Follow filtered logs:**
```bash
python3 filter_logs.py --follow
```

**Show recent errors only:**
```bash
python3 filter_logs.py --errors --lines 50
```

## Debugging Announcement Creation

To debug the announcement creation issue:

1. **Start monitoring application logs:**
   ```bash
   python3 monitor_logs.py --type app --follow
   ```

2. **In another terminal, monitor announcement-specific logs:**
   ```bash
   python3 monitor_logs.py --type announcements --follow
   ```

3. **Try creating an announcement** with your payload

4. **Check for errors:**
   ```bash
   python3 monitor_logs.py --type errors --lines 20
   ```

## Enhanced Logging Features

### Device Logger Features
- **Automatic device identification** in log messages
- **Structured logging** for different types of device operations
- **Error tracking** with full tracebacks
- **Performance monitoring** for command processing
- **WebSocket activity tracking**

### Log Message Format
```
2024-06-19 21:30:47,025 - DEVICE - INFO - [DEVICE_SN] Message content - [filename:line]
```

### Log Levels
- **INFO**: Normal operations, command processing
- **DEBUG**: Detailed timing and status information
- **WARNING**: Retry attempts, non-critical issues
- **ERROR**: Failed operations with full tracebacks

## Benefits

1. **Cleaner application logs** - No more device sync noise
2. **Better debugging** - Separate concerns for easier troubleshooting
3. **Performance monitoring** - Track device operations separately
4. **Log rotation** - Automatic management of log file sizes
5. **Structured logging** - Consistent format for device operations

## Usage Examples

### Debug Announcement Creation
```bash
# Terminal 1: Monitor app logs
python3 monitor_logs.py --type app --follow

# Terminal 2: Try creating announcement
curl -X POST http://localhost:5000/api/announcements \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"title": "Test", "content": "Test content"}'

# Terminal 3: Check for errors
python3 monitor_logs.py --type errors --lines 10
```

### Monitor Device Operations
```bash
# Watch device operations in real-time
python3 monitor_logs.py --type device --follow
```

### Check Recent Application Activity
```bash
# See last 100 lines of application logs (no device noise)
python3 monitor_logs.py --type app --lines 100
```

This logging system will help you identify the exact cause of the "Internal server error" when creating announcements without being overwhelmed by device synchronization logs.
