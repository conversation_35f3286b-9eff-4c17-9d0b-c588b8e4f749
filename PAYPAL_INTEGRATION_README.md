# PayPal Subscription Integration

This document describes the comprehensive PayPal integration for automatic subscription billing in the KaziSync HRMS system.

## Overview

The PayPal integration enables automatic monthly billing for subscriptions based on employee count. It supports both PayPal accounts and credit/debit cards, providing maximum flexibility for customers while maintaining PayPal's trusted branding.

## Features

### ✅ Payment Methods Supported
- **PayPal Accounts**: Customers can pay using their existing PayPal accounts
- **Credit/Debit Cards**: Customers can pay with cards without needing a PayPal account
- **Automatic Billing**: Monthly charges based on current employee count
- **Single Transaction**: One charge per month to minimize PayPal fees

### ✅ Billing Features
- **Employee-Based Pricing**: Automatic calculation based on active employee count
- **Dynamic Billing**: Charges adjust automatically when employees are added/removed
- **Retry Logic**: Automatic retry for failed payments
- **Webhook Integration**: Real-time payment status updates
- **Comprehensive Logging**: Full audit trail of all billing activities

## Architecture

### Models
- **PayPalBillingAgreement**: Stores PayPal billing agreement details
- **CompanySubscription**: Enhanced with PayPal integration fields
- **SubscriptionInvoice**: Billing invoices for each charge
- **SubscriptionPayment**: Payment records with PayPal transaction details

### Services
- **PayPalService**: Core PayPal API integration
- **SubscriptionBillingService**: Automated billing logic
- **BillingAutomationJob**: Scheduled billing processes

### API Endpoints
- **Setup Billing**: `/api/paypal/setup-billing`
- **Execute Agreement**: `/api/paypal/execute-agreement`
- **Cancel Billing**: `/api/paypal/cancel-billing`
- **Billing Status**: `/api/paypal/billing-status/{company_id}`
- **Webhook Handler**: `/api/paypal/webhook`

## Setup Instructions

### 1. PayPal Developer Account Setup

1. **Create PayPal Developer Account**
   - Go to [PayPal Developer](https://developer.paypal.com/)
   - Sign up or log in with your PayPal business account

2. **Create Application**
   - Navigate to "My Apps & Credentials"
   - Click "Create App"
   - Choose "Default Application" type
   - Select your business account
   - Enable "Subscriptions" and "Reference Transactions" features

3. **Get API Credentials**
   - Copy your Client ID and Client Secret
   - Note the sandbox and live endpoints

### 2. Environment Configuration

Add the following environment variables to your `.env` file:

```bash
# PayPal Configuration
PAYPAL_MODE=sandbox                                    # 'sandbox' or 'live'
PAYPAL_CLIENT_ID=your_paypal_client_id_here
PAYPAL_CLIENT_SECRET=your_paypal_client_secret_here
PAYPAL_WEBHOOK_ID=your_webhook_id_here
PAYPAL_WEBHOOK_URL=https://yourdomain.com/api/paypal/webhook
PAYPAL_RETURN_URL=https://yourdomain.com/subscription/success
PAYPAL_CANCEL_URL=https://yourdomain.com/subscription/cancel
PAYPAL_CURRENCY=USD
PAYPAL_BRAND_NAME=KaziSync HRMS
PAYPAL_LOGO_URL=https://yourdomain.com/logo.png
PAYPAL_MAX_RETRIES=3
PAYPAL_RETRY_DELAY=300
PAYPAL_TIMEOUT=30
PAYPAL_VALIDATE_WEBHOOK=true
```

### 3. Database Migration

Run the database migration to add PayPal-related tables:

```bash
flask db upgrade
```

### 4. Webhook Setup

1. **Create Webhook in PayPal Developer Console**
   - Go to your app in PayPal Developer Console
   - Navigate to "Webhooks"
   - Add webhook URL: `https://yourdomain.com/api/paypal/webhook`
   - Select these events:
     - BILLING.SUBSCRIPTION.ACTIVATED
     - BILLING.SUBSCRIPTION.CANCELLED
     - BILLING.SUBSCRIPTION.SUSPENDED
     - PAYMENT.SALE.COMPLETED
     - PAYMENT.SALE.DENIED
     - BILLING.SUBSCRIPTION.PAYMENT.FAILED

2. **Update Environment Variables**
   - Copy the Webhook ID from PayPal console
   - Update `PAYPAL_WEBHOOK_ID` in your `.env` file

### 5. Start Billing Automation

Add to your application startup:

```python
from application.jobs.billing_automation_job import billing_automation_job

# Start billing automation when app starts
billing_automation_job.start()

# Stop billing automation when app shuts down
import atexit
atexit.register(billing_automation_job.stop)
```

## Usage

### Frontend Integration

1. **Include PayPal SDK**
   ```html
   <script src="/static/js/paypal-subscription.js"></script>
   ```

2. **Initialize PayPal Manager**
   ```javascript
   const paypalManager = new PayPalSubscriptionManager({
       clientId: 'your_client_id',
       environment: 'sandbox', // or 'live'
       apiBaseUrl: '/api/paypal'
   });
   ```

3. **Render Payment Options**
   ```javascript
   // PayPal buttons
   await paypalManager.renderPayPalButtons(
       'paypal-button-container',
       companyId,
       planId
   );

   // Credit card form
   await paypalManager.renderCreditCardForm(
       'card-form-container',
       companyId,
       planId
   );
   ```

### Backend Integration

1. **Setup Billing for New Subscription**
   ```python
   from application.services.billing.subscription_billing_service import SubscriptionBillingService

   billing_service = SubscriptionBillingService()
   result, error = billing_service.setup_paypal_billing_for_subscription(
       subscription, return_url, cancel_url
   )
   ```

2. **Process Monthly Billing**
   ```python
   # Manual billing
   success, error = billing_service.process_subscription_billing(subscription)

   # Automatic billing (handled by BillingAutomationJob)
   results = billing_service.process_all_due_subscriptions()
   ```

## Billing Flow

### 1. Subscription Setup
1. Customer selects subscription plan
2. Customer chooses payment method (PayPal or card)
3. PayPal billing agreement is created
4. Customer approves agreement in PayPal
5. Agreement is activated and stored in database

### 2. Monthly Billing
1. **Automated Job** runs daily at 2 AM
2. **Employee Count** is calculated from active employees
3. **Amount** is calculated: `employee_count × price_per_employee`
4. **PayPal Charge** is processed via billing agreement
5. **Invoice** and **Payment** records are created
6. **Subscription** is updated with new billing date

### 3. Employee Count Changes
1. **Daily Job** updates employee counts at 1 AM
2. **Weekly Job** processes immediate billing for count changes
3. **Prorated Billing** can be implemented for mid-month changes

## Error Handling

### Payment Failures
- **Automatic Retry**: Failed payments are retried up to 3 times
- **Retry Schedule**: Every 4 hours during business hours (9 AM - 5 PM)
- **Suspension**: Subscriptions are suspended after 3 failed attempts
- **Notifications**: Webhook events provide real-time failure notifications

### Common Error Scenarios
- **Insufficient Funds**: Customer notified, retry scheduled
- **Expired Card**: Customer must update payment method
- **Account Closed**: Billing agreement cancelled automatically
- **PayPal API Errors**: Logged and retried with exponential backoff

## Monitoring and Logging

### Logging Levels
- **INFO**: Successful operations, billing completions
- **WARNING**: Payment failures, retry attempts
- **ERROR**: API errors, system failures

### Key Metrics to Monitor
- **Daily Billing Success Rate**: Target >95%
- **Payment Retry Success Rate**: Target >80%
- **API Response Times**: Target <5 seconds
- **Webhook Processing**: Target <1 second

### Log Examples
```
2024-01-15 02:00:01 - INFO - Starting daily billing process
2024-01-15 02:00:15 - INFO - Calculated charge for subscription abc123: 25 employees × $12.00 = $300.00
2024-01-15 02:00:18 - INFO - Successfully processed PayPal payment of $300.00
2024-01-15 02:00:20 - INFO - Daily billing completed: 45 successful, 2 failed out of 47 total
```

## Security Considerations

### Data Protection
- **No Card Storage**: Credit card details never stored on your servers
- **PayPal Vault**: Secure tokenization for recurring payments
- **Webhook Verification**: All webhooks verified with PayPal signatures
- **HTTPS Required**: All PayPal communication over secure connections

### Access Control
- **API Authentication**: PayPal API credentials secured in environment variables
- **Webhook Validation**: Signature verification prevents spoofed webhooks
- **Admin Access**: Billing operations restricted to authorized users

## Testing

### Sandbox Testing
1. **Use Sandbox Credentials**: Set `PAYPAL_MODE=sandbox`
2. **Test Accounts**: Create test buyer accounts in PayPal Developer Console
3. **Test Cards**: Use PayPal's test credit card numbers
4. **Webhook Testing**: Use ngrok or similar for local webhook testing

### Test Scenarios
- ✅ PayPal account subscription setup
- ✅ Credit card subscription setup
- ✅ Successful monthly billing
- ✅ Failed payment handling
- ✅ Employee count changes
- ✅ Subscription cancellation
- ✅ Webhook event processing

## Troubleshooting

### Common Issues

1. **"Invalid client credentials"**
   - Check `PAYPAL_CLIENT_ID` and `PAYPAL_CLIENT_SECRET`
   - Ensure credentials match the environment (sandbox/live)

2. **"Webhook signature verification failed"**
   - Check `PAYPAL_WEBHOOK_ID` matches PayPal console
   - Ensure webhook URL is accessible from internet

3. **"Billing agreement not found"**
   - Check if agreement was properly executed
   - Verify agreement ID in database matches PayPal

4. **"Employee count not updating"**
   - Check if billing automation job is running
   - Verify employee status updates in company database

### Debug Mode
Enable debug logging:
```python
import logging
logging.getLogger('application.services.paypal').setLevel(logging.DEBUG)
```

## Production Deployment

### Pre-Launch Checklist
- [ ] PayPal live credentials configured
- [ ] Webhook URL accessible and verified
- [ ] SSL certificate installed and valid
- [ ] Database migrations applied
- [ ] Billing automation job configured
- [ ] Monitoring and alerting set up
- [ ] Error handling tested
- [ ] Backup and recovery procedures in place

### Go-Live Steps
1. **Switch to Live Mode**: Set `PAYPAL_MODE=live`
2. **Update Credentials**: Use live PayPal API credentials
3. **Test with Small Amount**: Process test subscription
4. **Monitor Closely**: Watch logs and metrics for first 24 hours
5. **Customer Communication**: Notify customers of new billing system

## Support and Maintenance

### Regular Maintenance
- **Monthly**: Review billing success rates and error logs
- **Quarterly**: Update PayPal SDK and dependencies
- **Annually**: Review and update webhook event subscriptions

### Support Contacts
- **PayPal Developer Support**: https://developer.paypal.com/support/
- **PayPal Merchant Support**: Contact through PayPal business account
- **Technical Issues**: Check application logs and PayPal Developer Console

---

For additional support or questions about this integration, please refer to the PayPal Developer Documentation or contact the development team.
