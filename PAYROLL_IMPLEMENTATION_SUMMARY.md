# Comprehensive Loan & Advance Management System

## Overview
This document outlines a complete loan and advance management system that integrates seamlessly with the existing payroll, approval workflow, and employee management systems.

## Loan Types Supported

### **1. Salary Advances**
- **Purpose**: Short-term cash flow assistance
- **Amount**: Up to 50% of monthly salary
- **Term**: 1-3 months
- **Interest**: Usually 0%
- **Approval**: Manager level

### **2. Personal Loans**
- **Purpose**: Personal financial needs
- **Amount**: Up to 6x monthly salary
- **Term**: 6-60 months
- **Interest**: 8-15% annually
- **Approval**: HR + Finance approval

### **3. Emergency Loans**
- **Purpose**: Medical, family emergencies
- **Amount**: Up to 3x monthly salary
- **Term**: 3-24 months
- **Interest**: 0-5% annually
- **Approval**: Fast-track approval

### **4. Educational Loans**
- **Purpose**: Training, certification, education
- **Amount**: Up to 8x monthly salary
- **Term**: 12-48 months
- **Interest**: 0-3% annually
- **Approval**: HR + Manager approval

### **5. Equipment/Asset Loans**
- **Purpose**: Work equipment, tools, devices
- **Amount**: Based on asset value
- **Term**: 6-36 months
- **Interest**: 5-10% annually
- **Approval**: Department head approval

### **6. Housing/Rent Advance**
- **Purpose**: Rent deposits, housing needs
- **Amount**: Up to 4x monthly salary
- **Term**: 6-24 months
- **Interest**: 3-8% annually
- **Approval**: HR approval

### **7. Travel Advance**
- **Purpose**: Business or personal travel
- **Amount**: Based on travel cost
- **Term**: 1-6 months
- **Interest**: 0% (business), 5% (personal)
- **Approval**: Manager approval

### **8. Wedding/Event Loans**
- **Purpose**: Special occasions
- **Amount**: Up to 5x monthly salary
- **Term**: 12-36 months
- **Interest**: 5-12% annually
- **Approval**: HR approval

## Key Features Implemented

### 1. Dynamic Calculation Scenarios
- **Basic + Allowances**: Calculate from basic salary and individual allowances
- **Gross + Allowances**: Calculate from gross salary and individual allowances  
- **Net + Allowances**: Calculate backwards from net salary and allowances
- **Total Cost Breakdown**: Calculate backwards from total staff cost

### 2. Decimal Precision Throughout
- All financial calculations use `Decimal` type instead of `float`
- Proper rounding using `ROUND_HALF_UP` for financial accuracy
- Helper functions `ensure_decimal()` and `round_currency()` for consistency
- JSON serialization only converts to float at the very end

### 3. Configurable Calculation Rules
- New `CalculationRule` model for database-driven calculation bases
- No more hardcoded calculation logic
- Country-specific and time-versioned rules
- Fallback to legacy rules for backward compatibility

### 4. Enhanced Models

#### EmployeeSalary Model
- Added `calculation_scenario`, `input_values`, `calculated_values` fields
- New method `create_from_scenario()` for dynamic salary creation
- Decimal-based calculation methods

#### CalculationRule Model (New)
- Configurable calculation bases for different deduction types
- Versioned with effective dates
- Country-specific rules

#### EmployeeAllowance Model
- Added Decimal-based calculation methods
- `get_taxable_allowances_total_decimal()`
- `get_pensionable_allowances_total_decimal()`

### 5. Updated Services

#### PayrollCalculationService
- New `calculate_payroll_dynamic()` method for scenario-based calculations
- Backward calculation methods using binary search
- `_get_calculation_base_dynamic()` for configurable calculation bases
- All calculations use Decimal arithmetic
- `decimal_to_float_for_json()` for proper serialization

### 6. New API Endpoints

#### Dynamic Payroll API (`/api/payroll/calculate/dynamic`)
- Calculate payroll from different known values
- Support for all calculation scenarios
- Preview calculations without saving

#### Calculation Rules API (`/api/calculation-rules`)
- CRUD operations for calculation rules
- Versioning support
- Lookup active rules by criteria

#### Enhanced Salary API
- Create salary records from calculation scenarios
- Support for different input types

## Files Modified/Created

### New Files
1. `application/Models/calculation_rule.py` - Configurable calculation rules
2. `application/Routes/payroll/dynamic_payroll_api.py` - Dynamic calculation endpoints
3. `application/Routes/payroll/calculation_rules_api.py` - Rule management endpoints

### Modified Files
1. `application/Services/payroll_calculation_service.py` - Complete rewrite with Decimal support
2. `application/Models/employees/employee_salary.py` - Added scenario support
3. `application/Models/employees/employee_allowance.py` - Added Decimal methods
4. `application/Routes/payroll/payroll_processing_api.py` - Updated for Decimal handling

## Database Schema Changes Required

### New Tables
```sql
-- CalculationRule table (central database)
CREATE TABLE calculation_rules (
    rule_id UUID PRIMARY KEY,
    country_id UUID REFERENCES countries(country_id),
    calculation_type VARCHAR(50) NOT NULL, -- 'TAX', 'DEDUCTION'
    type_code VARCHAR(50) NOT NULL, -- 'PAYE', 'PENSION', 'CBHI', etc.
    calculation_base VARCHAR(50) NOT NULL, -- 'GROSS', 'NET_BEFORE_CBHI', etc.
    calculation_order INTEGER DEFAULT 1,
    depends_on JSONB,
    effective_from DATE NOT NULL,
    effective_to DATE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### Modified Tables
```sql
-- Add new fields to employee_salaries (tenant databases)
ALTER TABLE employee_salaries ADD COLUMN calculation_scenario VARCHAR(50);
ALTER TABLE employee_salaries ADD COLUMN input_values JSONB;
ALTER TABLE employee_salaries ADD COLUMN calculated_values JSONB;
```

## Usage Examples

### 1. Calculate from Net Salary
```python
calc_service = PayrollCalculationService(company_id)
result = calc_service.calculate_payroll_dynamic(
    session, employee_id, 
    "net_plus_allowances",
    {
        "net_salary": 450000,
        "transport": 50000,
        "housing": 100000
    },
    pay_period_start, pay_period_end
)
```

### 2. Calculate from Total Staff Cost
```python
result = calc_service.calculate_payroll_dynamic(
    session, employee_id,
    "total_cost_breakdown", 
    {
        "total_staff_cost": 800000,
        "allowances": {"transport": 50000}
    },
    pay_period_start, pay_period_end
)
```

### 3. Create Salary from Scenario
```python
salary = EmployeeSalary.create_from_scenario(
    session, employee_id, employee_type_id,
    "net_plus_allowances",
    {"net_salary": 450000, "transport": 50000},
    effective_from, company_id
)
```

## Benefits

1. **Financial Accuracy**: Decimal precision eliminates rounding errors
2. **Flexibility**: Support for different input scenarios
3. **Configurability**: Database-driven calculation rules
4. **Backward Compatibility**: Existing code continues to work
5. **Auditability**: All calculation inputs and results are tracked
6. **Country-Specific**: Rules can vary by country
7. **Time-Versioned**: Historical accuracy for different periods

## Migration Strategy

1. **Phase 1**: Deploy new models and API endpoints
2. **Phase 2**: Run database migrations for new fields
3. **Phase 3**: Configure calculation rules for existing countries
4. **Phase 4**: Update frontend to use new dynamic endpoints
5. **Phase 5**: Gradually migrate existing salary records to use scenarios

## Testing Recommendations

1. **Unit Tests**: Test all calculation scenarios with known inputs/outputs
2. **Precision Tests**: Verify Decimal calculations vs float calculations
3. **Backward Compatibility**: Ensure existing payroll calculations still work
4. **Edge Cases**: Test binary search convergence and boundary conditions
5. **Integration Tests**: Test full payroll run with mixed calculation scenarios

## Configuration Required

1. **Set up calculation rules** for each country and deduction type
2. **Configure CBHI calculation base** as 'NET_BEFORE_CBHI'
3. **Set up pension calculation base** as 'GROSS_SALARY'
4. **Configure transport allowance exclusions** for specific deductions

This implementation provides a robust, flexible, and accurate payroll calculation system that can handle complex scenarios while maintaining financial precision.
