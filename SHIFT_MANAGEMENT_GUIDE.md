# Business-Focused Shift Management System

## Overview
The Shift Management System provides enterprise-grade functionality for creating, managing, and assigning work shifts using **business language**. Designed for any industry - from hospitals to manufacturing to offices - it eliminates technical complexity while providing powerful scheduling capabilities.

## 🎯 Business-First Approach

### Why This System is Different
- **Speaks Your Language** - No technical jargon, just business requirements
- **Industry Agnostic** - Works for hospitals, offices, manufacturing, retail, security
- **Flexible Staffing** - Supports any team size (3 people or 300 people)
- **Real Business Rules** - Max consecutive days, overtime policies, fair rotation
- **Smart Validation** - Catches scheduling conflicts before they happen

## 🏗️ System Architecture

### Core Models
- **`Shift`** - Basic work schedule definitions (9-5, night shift, etc.)
- **`EmployeeShift`** - Individual employee assignments with custom overrides
- **`ShiftTemplate`** - Business-friendly scheduling templates with structured requirements
- **`ShiftSchedule`** - Generated schedules with approval workflow
- **`ScheduleAssignment`** - Individual daily assignments within schedules

### Enterprise Features
- ✅ **Business Requirements Schema** - Structured but flexible business language
- ✅ **Smart Validation** - Comprehensive error checking with helpful messages
- ✅ **Industry Examples** - Pre-built templates for common scenarios
- ✅ **Staffing Analysis** - Automatic calculation of coverage needs
- ✅ **Work Rules Enforcement** - Max hours, rest periods, overtime policies
- ✅ **24/7 Coverage Detection** - Automatic identification of round-the-clock schedules
- ✅ **Fair Rotation Logic** - Ensures equitable distribution of shifts
- ✅ **Audit Trail** - Complete tracking of all scheduling decisions

## 📋 Business-Focused Scheduling API

### Authentication & Authorization
All endpoints require:
- **Authentication**: Bearer token (`Authorization: Bearer <token>`)
- **Roles**: `admin`, `hr` for template creation and schedule management

---

## 🚀 Business Scheduling Templates

### 1. Get Industry Examples
**Endpoint**: `GET /api/scheduling/examples/business-requirements`
**Roles**: Any authenticated user

Get real-world examples for different industries to use as templates.

```bash
curl -X GET "https://your-domain/api/scheduling/examples/business-requirements" \
  -H "Authorization: Bearer <token>"
```

**Response**: Examples for Hospital ICU, Customer Service, Manufacturing, etc.

### 2. Validate Business Requirements
**Endpoint**: `POST /api/scheduling/validate-requirements`
**Roles**: `admin`, `hr`

Validate your business requirements before creating a template.

```json
{
  "business_requirements": {
    "schedule_pattern": {"type": "weekly"},
    "shifts": [
      {
        "shift_name": "Day Shift",
        "time_range": {"start_time": "09:00", "end_time": "17:00"},
        "staffing": {"minimum_staff": 5, "preferred_staff": 7, "maximum_staff": 9},
        "days_of_week": [1, 2, 3, 4, 5]
      }
    ],
    "work_rules": {
      "max_consecutive_days": 5,
      "min_rest_days": 2,
      "max_hours_per_week": 40,
      "overtime_allowed": false
    }
  }
}
```

### 3. Get Available Shifts
**Endpoint**: `GET /api/scheduling/available-shifts?company_id={company_id}`
**Roles**: Any authenticated user

Get all available shifts for creating business templates. **Use this first** to get shift IDs for template creation.

**Response:**
```json
{
  "message": "Available shifts retrieved successfully",
  "shifts": [
    {
      "shift_id": "shift-uuid-123",
      "name": "Business Hours",
      "description": "Standard business hours shift",
      "start_time": "09:00",
      "end_time": "17:00",
      "is_night_shift": false,
      "working_days": "1,2,3,4,5",
      "break_duration": 60
    }
  ],
  "count": 1
}
```

### 4. Create Structured Template
**Endpoint**: `POST /api/scheduling/create-structured-template`
**Roles**: `admin`, `hr`

Create a scheduling template using business-friendly structured requirements with **shift_id references**.

```json
{
  "company_id": "company-uuid",
  "name": "Customer Service Coverage",
  "pattern_type": "weekly",
  "description": "Standard business hours customer service coverage",
  "business_requirements": {
    "schedule_pattern": {
      "type": "weekly",
      "cycle_length": 7,
      "description": "Standard weekly rotation"
    },
    "shifts": [
      {
        "shift_id": "shift-uuid-123",
        "staffing": {
          "minimum_staff": 8,
          "preferred_staff": 12,
          "maximum_staff": 15
        },
        "days_of_week": [1, 2, 3, 4, 5],
        "roles_required": [
          {"role": "Customer Service Rep", "minimum": 6, "preferred": 8},
          {"role": "Team Lead", "minimum": 1, "preferred": 2}
        ]
      }
    ],
    "work_rules": {
      "max_consecutive_days": 5,
      "min_rest_days": 2,
      "max_hours_per_week": 40,
      "overtime_allowed": false,
      "weekend_requirements": "both_off"
    },
    "employee_preferences": {
      "fair_rotation": true,
      "seniority_priority": true,
      "consider_employee_requests": true
    },
    "coverage_rules": {
      "minimum_coverage_percentage": 85,
      "allow_understaffing": true,
      "emergency_coverage_plan": "redistribute_workload"
    }
  }
}
```

**Enhanced Response with Shift Details:**
```json
{
  "message": "Structured shift template created successfully",
  "template": {
    "template_id": "template-uuid-456",
    "name": "Customer Service Coverage",
    "business_requirements": {
      "shifts": [
        {
          "shift_id": "shift-uuid-123",
          "shift_details": {
            "name": "Business Hours",
            "start_time": "09:00",
            "end_time": "17:00",
            "is_night_shift": false
          },
          "staffing": {"minimum_staff": 8, "preferred_staff": 12}
        }
      ]
    },
    "staffing_summary": {
      "total_staff_needed": 12,
      "shifts_per_day": 1
    }
  }
}
```

### 5. Get Template Analysis
**Endpoint**: `GET /api/scheduling/template-analysis/{template_id}?company_id={company_id}`
**Roles**: Any authenticated user

Get comprehensive analysis of a template's business requirements.

**Response includes:**
- Staffing summary and daily totals
- Work rules analysis
- Weekly coverage hours calculation
- 24/7 coverage detection
- Validation status

### 6. List Business-Friendly Templates
**Endpoint**: `GET /api/scheduling/templates/business-friendly?company_id={company_id}&active_only=true`
**Roles**: Any authenticated user

Get all templates with business summaries and metrics.

### 7. Get Business Requirements Examples
**Endpoint**: `GET /api/scheduling/examples/business-requirements`
**Roles**: Any authenticated user

Get example business requirements for different industries with usage instructions.

**Response includes:**
- Hospital ICU 24/7 coverage example
- Office customer service example
- Manufacturing 3-shift rotation example
- Step-by-step usage instructions

---

## 🔄 **NEW: shift_id-Based Workflow**

### **Key Changes (v2.0)**
- ✅ **Eliminated fragile name-based shift lookup**
- ✅ **Made `shift_id` required in business requirements**
- ✅ **Added comprehensive shift validation**
- ✅ **Enhanced responses with actual shift details**
- ✅ **Improved reliability and performance**

### **Recommended Workflow for Frontend**
1. **Get Available Shifts**: `GET /api/scheduling/available-shifts`
2. **Create Business Template**: Use actual `shift_id` values from step 1
3. **Validate Template**: `GET /api/scheduling/template-analysis/{template_id}`
4. **Generate Schedules**: `POST /api/schedules/generate`

### **Migration from v1.0**
- Old templates using `shift_name` still work (backward compatible)
- New templates must use `shift_id` for reliability
- Frontend should migrate to new workflow for better UX

---

## 🔧 Basic Shift Definition Management

> **IMPORTANT**: These are the **foundational shift definitions** that business templates reference via `shift_id`.
> **Create basic shifts first**, then reference them in business templates using their IDs.
> The workflow is: 1) Create basic shifts, 2) Get their IDs, 3) Use IDs in business templates.

### 1. Create Shift
**Endpoint**: `POST /api/shifts`  
**Roles**: `admin`, `hr`

Create a new work shift definition.

```json
{
  "company_id": "company-uuid",
  "name": "Morning Shift",
  "description": "Standard morning working hours",
  "start_time": "09:00",
  "end_time": "17:00",
  "grace_period_late": 15,
  "grace_period_early": 15,
  "break_duration": 60,
  "break_start_time": "12:00",
  "is_night_shift": false,
  "is_flexible": false,
  "working_days": "1,2,3,4,5"
}
```

### 2. Get Shifts
**Endpoint**: `GET /api/shifts`

Retrieve shifts for a company with pagination and filtering.

```bash
GET /api/shifts?company_id=uuid&page=1&per_page=10&active_only=true
```

### 3. Update Shift
**Endpoint**: `PUT /api/shifts/{shift_id}`  
**Roles**: `admin`, `hr`

Update an existing shift definition.

### 4. Delete Shift
**Endpoint**: `DELETE /api/shifts/{shift_id}`  
**Roles**: `admin`, `hr`

Soft delete a shift (marks as inactive).

---

## 🔄 Advanced Shift Templates & Rotation Patterns

### 1. Create Shift Template
**Endpoint**: `POST /api/shift-templates`
**Roles**: `admin`, `hr`

Create rotation templates for complex scheduling patterns.

```json
{
  "company_id": "company-uuid",
  "name": "4-on-2-off Rotation",
  "description": "4 consecutive days on, then 2 days off",
  "pattern_type": "weekly",
  "rotation_cycle": 6,
  "pattern_data": {
    "cycle_length": 6,
    "pattern": [
      {"week": 1, "shifts": ["day", "day", "day", "day", "off", "off", "off"]},
      {"week": 2, "shifts": ["off", "off", "day", "day", "day", "day", "off"]},
      {"week": 3, "shifts": ["day", "off", "off", "day", "day", "day", "day"]},
      {"week": 4, "shifts": ["day", "day", "off", "off", "day", "day", "day"]},
      {"week": 5, "shifts": ["day", "day", "day", "off", "off", "day", "day"]},
      {"week": 6, "shifts": ["off", "day", "day", "day", "off", "off", "day"]}
    ],
    "shift_definitions": {
      "day": {"start": "07:00", "end": "19:00", "duration": 12}
    }
  },
  "department_ids": ["dept-uuid-1", "dept-uuid-2"],
  "employee_group_size": 6
}
```

### 2. Generate Department Schedule
**Endpoint**: `POST /api/schedules/generate`
**Roles**: `admin`, `hr`

Generate complete schedules using templates.

```json
{
  "company_id": "company-uuid",
  "department_id": "dept-uuid",
  "template_id": "template-uuid",
  "start_date": "2024-02-01",
  "duration_weeks": 4,
  "employee_groups": [
    ["emp1", "emp2", "emp3"],
    ["emp4", "emp5", "emp6"]
  ]
}
```

### 3. Get Pending Review Schedules
**Endpoint**: `GET /api/schedules/pending-review`

Get schedules that need HR approval.

```bash
GET /api/schedules/pending-review?company_id=uuid
```

### 4. Publish Schedule
**Endpoint**: `PUT /api/schedules/{schedule_id}/publish`
**Roles**: `admin`, `hr`

Approve and publish a generated schedule.

---

## 👥 Employee Shift Assignment

### 1. Single Employee Assignment
**Endpoint**: `POST /api/employee-shifts`  
**Roles**: `admin`, `hr`

Assign a shift to a single employee.

```json
{
  "company_id": "company-uuid",
  "employee_id": "employee-uuid",
  "shift_id": "shift-uuid",
  "effective_start_date": "2024-01-15",
  "effective_end_date": "2024-12-31",
  "custom_start_time": "09:30",
  "custom_end_time": "17:30",
  "custom_break_duration": 45,
  "custom_working_days": "1,2,3,4",
  "is_active": true,
  "created_by": "user-uuid"
}
```

### 2. Bulk Employee Assignment
**Endpoint**: `POST /api/employee-shifts/bulk`  
**Roles**: `admin`, `hr`

Assign a shift to multiple employees simultaneously.

```json
{
  "company_id": "company-uuid",
  "employee_ids": [
    "employee-uuid-1",
    "employee-uuid-2",
    "employee-uuid-3"
  ],
  "shift_id": "shift-uuid",
  "effective_start_date": "2024-01-15",
  "effective_end_date": "2024-12-31",
  "custom_start_time": "09:00",
  "custom_end_time": "17:00",
  "is_active": true
}
```

**Bulk Assignment Features:**
- ✅ **Batch Processing** - Up to 100 employees per request
- ✅ **Validation** - Comprehensive employee and shift validation
- ✅ **Overlap Detection** - Prevents conflicting assignments
- ✅ **Partial Success** - Continues processing valid employees
- ✅ **Detailed Reporting** - Success/failure status for each employee

### 3. Get Employee Shifts
**Endpoint**: `GET /api/employee-shifts`

Retrieve shift assignments with filtering options.

```bash
# Get shifts for specific employee
GET /api/employee-shifts?company_id=uuid&employee_id=uuid&active_only=true

# Get employees for specific shift
GET /api/employee-shifts?company_id=uuid&shift_id=uuid&active_only=true
```

### 4. Update Assignment
**Endpoint**: `PUT /api/employee-shifts/{assignment_id}`  
**Roles**: `admin`, `hr`

Update an existing shift assignment.

### 5. Delete Assignment
**Endpoint**: `DELETE /api/employee-shifts/{assignment_id}`  
**Roles**: `admin`, `hr`

Remove a shift assignment.

---

## 📊 Bulk Assignment Response Format

### Success Response (201 - All Successful)
```json
{
  "message": "Bulk shift assignment completed. 3 successful, 0 failed.",
  "summary": {
    "total_requested": 3,
    "successful": 3,
    "failed": 0,
    "validation_errors": 0
  },
  "assignments": [
    {
      "assignment_id": "assignment-uuid",
      "employee_id": "employee-uuid",
      "shift_id": "shift-uuid",
      "effective_start_date": "2024-01-15",
      "employee": {
        "employee_id": "employee-uuid",
        "first_name": "John",
        "last_name": "Doe"
      },
      "shift": {
        "shift_id": "shift-uuid",
        "name": "Morning Shift",
        "start_time": "09:00",
        "end_time": "17:00"
      }
    }
  ],
  "validation_results": [
    {
      "employee_id": "employee-uuid",
      "status": "success",
      "message": "Shift assigned successfully",
      "assignment_id": "assignment-uuid"
    }
  ]
}
```

### Partial Success Response (207 - Multi-Status)
```json
{
  "message": "Bulk shift assignment completed. 2 successful, 1 failed.",
  "summary": {
    "total_requested": 3,
    "successful": 2,
    "failed": 1,
    "validation_errors": 1
  },
  "validation_results": [
    {
      "employee_id": "employee-uuid-1",
      "status": "success",
      "message": "Shift assigned successfully"
    },
    {
      "employee_id": "employee-uuid-2",
      "status": "error",
      "message": "Employee already has an active shift assignment during this period",
      "conflicting_assignment_id": "existing-assignment-uuid"
    }
  ]
}
```

---

## 🔍 Validation Rules

### Shift Validation
- **Name uniqueness** per company
- **Time format** validation (HH:MM)
- **Working days** format (comma-separated numbers 1-7)
- **Grace periods** must be positive integers

### Assignment Validation
- **Employee exists** and belongs to company
- **Shift exists** and belongs to company
- **Date format** validation (YYYY-MM-DD)
- **No overlapping assignments** for same employee
- **Effective dates** logical (start ≤ end)

### Bulk Assignment Limits
- **Maximum 100 employees** per bulk request
- **Rate limiting** to prevent system abuse
- **Transaction safety** with rollback on critical errors

---

## 🏭 Industry Use Cases

### Healthcare (Hospitals, Clinics)
- **24/7 Coverage**: Day, evening, night shift rotations
- **Minimum Staffing**: Ensure adequate coverage per department
- **Flexible Breaks**: 4-clock system for lunch tracking
- **Emergency Adjustments**: Quick schedule modifications

### Manufacturing
- **Production Lines**: Continuous operation scheduling
- **Shift Handovers**: Seamless transition between teams
- **Maintenance Windows**: Scheduled downtime coordination
- **Overtime Management**: Fair distribution of extra hours

### Retail & Hospitality
- **Peak Hours Coverage**: Busy period staffing
- **Part-time Scheduling**: Flexible hour assignments
- **Seasonal Adjustments**: Holiday and event scheduling
- **Cross-training**: Multi-department assignments

### Security & Emergency Services
- **Round-the-clock Coverage**: 24/7 security operations
- **Patrol Rotations**: Geographic area assignments
- **Emergency Response**: On-call scheduling
- **Compliance Tracking**: Regulatory requirement adherence

### Call Centers & Customer Service
- **Time Zone Coverage**: Global customer support
- **Peak Volume Handling**: Rush hour staffing
- **Skill-based Routing**: Specialized team assignments
- **Break Coordination**: Maintaining service levels

---

## 🎯 Usage Examples

### Example 1: Create Standard Office Shift
```bash
curl -X POST "https://api.kazisync.com/api/shifts" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "company_id": "company-uuid",
    "name": "Standard Office Hours",
    "start_time": "09:00",
    "end_time": "17:00",
    "working_days": "1,2,3,4,5",
    "break_duration": 60
  }'
```

### Example 2: Bulk Assign to Department
```bash
curl -X POST "https://api.kazisync.com/api/employee-shifts/bulk" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "company_id": "company-uuid",
    "employee_ids": ["emp1", "emp2", "emp3"],
    "shift_id": "shift-uuid",
    "effective_start_date": "2024-02-01"
  }'
```

### Example 3: Night Shift with Custom Times
```bash
curl -X POST "https://api.kazisync.com/api/employee-shifts" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "company_id": "company-uuid",
    "employee_id": "employee-uuid",
    "shift_id": "night-shift-uuid",
    "effective_start_date": "2024-02-01",
    "custom_start_time": "22:00",
    "custom_end_time": "06:00"
  }'
```

---

## 🛠️ Best Practices

### 1. Shift Design
- **Clear naming** conventions (e.g., "Morning Shift", "Night Shift")
- **Realistic grace periods** (10-15 minutes typical)
- **Appropriate break times** based on shift length
- **Consider time zones** for global companies

### 2. Assignment Strategy
- **Plan effective dates** to avoid conflicts
- **Use bulk assignment** for department-wide changes
- **Monitor overlap warnings** and resolve conflicts
- **Document custom overrides** for audit purposes

### 3. Performance Optimization
- **Batch assignments** in reasonable sizes (10-50 employees)
- **Use filtering** to reduce data transfer
- **Cache shift definitions** for frequent operations
- **Monitor bulk operation frequency**

### 4. Error Handling
- **Process validation results** for bulk operations
- **Handle partial failures** gracefully
- **Provide clear error messages** to users
- **Log assignment changes** for audit trails

---

## 📈 Integration Points

### Attendance System
- Shift definitions drive attendance calculations
- Grace periods affect late/early determinations
- Break times influence attendance reporting

### Payroll System
- Shift assignments determine pay periods
- Night shift premiums and overtime calculations
- Working day patterns affect salary computations

### Reporting & Analytics
- Shift coverage reports and analysis
- Employee scheduling efficiency metrics
- Attendance pattern analysis by shift type

---

## 🔒 Security & Compliance

### Access Control
- **Role-based permissions** for shift management
- **Company-level isolation** for multi-tenant security
- **Audit logging** for all shift changes

### Data Protection
- **Encrypted storage** of shift and assignment data
- **GDPR compliance** for employee scheduling data
- **Backup and recovery** procedures for shift configurations

---

## 🎯 Business-First Summary

### What Makes This System Different

**Traditional Scheduling Systems:**
- Force you to learn technical concepts
- Rigid constraints that don't match business needs
- Complex JSON configurations
- One-size-fits-all approach

**Our Business-Focused System:**
- ✅ **Speaks Your Language** - "We need 7 nurses per day shift"
- ✅ **Adapts to Your Business** - Any team size, any industry
- ✅ **Smart Validation** - Catches problems before they happen
- ✅ **Industry Examples** - Start with proven templates
- ✅ **Comprehensive Analysis** - Automatic staffing calculations

### Recommended Workflow

1. **Start with Examples** - `GET /api/scheduling/examples/business-requirements`
2. **Validate Your Requirements** - `POST /api/scheduling/validate-requirements`
3. **Create Your Template** - `POST /api/scheduling/create-structured-template`
4. **Analyze Results** - `GET /api/scheduling/template-analysis/{id}`
5. **Create Basic Shifts** - `POST /api/shifts` (as needed)
6. **Assign Employees** - `POST /api/employee-shifts`

### Enterprise Benefits

- **Time Savings**: 5 minutes vs. 5 hours for complex schedules
- **Error Reduction**: Smart validation prevents scheduling conflicts
- **Compliance**: Automatic work rules enforcement
- **Scalability**: Works for 5 employees or 500 employees
- **Flexibility**: Adapts to any industry or business model

This business-focused shift management system eliminates technical complexity while providing enterprise-grade scheduling capabilities for organizations of any size.
