# Subscription System Documentation

## Overview

This document describes the comprehensive subscription system implemented for the HR management platform. The system supports employee-based pricing, plan-based feature restrictions, manual payment entry, and is designed to scale for future online payment integration.

## Architecture

### Database Design

The subscription system uses the **central database** for all subscription-related data, providing:
- Centralized billing management across all tenants
- Easy subscription analytics and reporting
- Simplified payment processing integration
- Better scalability for multi-tenant billing

### Core Models

#### 1. SubscriptionPlan (Central DB)
Defines available subscription plans with features and pricing.

**Key Fields:**
- `plan_id`: Unique identifier
- `name`: Plan name (e.g., "Starter", "Professional", "Enterprise")
- `price_per_employee`: Monthly price per employee
- `billing_cycle`: MONTHLY, QUARTERLY, YEARLY
- `max_employees`: Maximum employees (NULL = unlimited)
- `features`: JSONB field with feature flags and limits
- `is_active`: Whether the plan is available for new subscriptions

#### 2. CompanySubscription (Central DB)
Represents a company's subscription to a plan.

**Key Fields:**
- `subscription_id`: Unique identifier
- `company_id`: Reference to the company
- `plan_id`: Reference to the subscription plan
- `status`: TRIAL, ACTIVE, SUSPENDED, CANCELLED
- `employee_count`: Current billable employee count
- `amount_due`: Calculated billing amount
- `trial_end_date`: When trial expires

#### 3. SubscriptionInvoice (Central DB)
Billing invoices for subscriptions.

**Key Fields:**
- `invoice_id`: Unique identifier
- `subscription_id`: Reference to subscription
- `invoice_number`: Human-readable invoice number
- `employee_count_billed`: Number of employees billed
- `subtotal`, `tax_amount`, `total_amount`: Pricing breakdown
- `status`: DRAFT, PENDING, PAID, OVERDUE, CANCELLED
- `due_date`, `paid_date`: Payment timing

#### 4. SubscriptionPayment (Central DB)
Payment records for invoices.

**Key Fields:**
- `payment_id`: Unique identifier
- `invoice_id`: Reference to invoice
- `amount`: Payment amount
- `payment_method`: MANUAL, STRIPE, PAYPAL, etc.
- `status`: PENDING, COMPLETED, FAILED, REFUNDED
- `recorded_by`: User who recorded the payment

#### 5. FeatureAccess (Central DB)
Controls feature access and usage limits per company.

**Key Fields:**
- `company_id`: Reference to company
- `feature_name`: Name of the feature
- `is_enabled`: Whether feature is enabled
- `usage_limit`: Maximum usage (NULL = unlimited)
- `current_usage`: Current usage count

## API Endpoints

### Subscription Plans Management

#### GET /api/subscription-plans
Get all subscription plans.

**Query Parameters:**
- `active_only`: boolean (default: true)
- `include_features`: boolean (default: true)

#### POST /api/subscription-plans (Admin only)
Create a new subscription plan.

**Request Body:**
```json
{
  "name": "Professional",
  "description": "Advanced features for growing businesses",
  "price_per_employee": 12.00,
  "billing_cycle": "MONTHLY",
  "max_employees": 100,
  "features": {
    "announcements": true,
    "payroll": true,
    "advanced_analytics": true
  }
}
```

#### PUT /api/subscription-plans/{plan_id} (Admin only)
Update a subscription plan.

#### DELETE /api/subscription-plans/{plan_id} (Admin only)
Deactivate a subscription plan.

### Company Subscriptions

#### POST /api/subscriptions
Create a new subscription for a company.

**Request Body:**
```json
{
  "company_id": "company-uuid",
  "plan_id": "plan-uuid",
  "trial_days": 14
}
```

#### GET /api/subscriptions/current
Get current subscription for authenticated user's company.

#### POST /api/subscriptions/current/upgrade
Upgrade to a new plan.

**Request Body:**
```json
{
  "new_plan_id": "new-plan-uuid"
}
```

#### POST /api/subscriptions/current/cancel
Cancel current subscription.

#### GET /api/subscriptions/current/features
Get feature access for current subscription.

### Invoice Management

#### POST /api/invoices (Admin/HR only)
Create an invoice for a subscription.

#### GET /api/invoices
Get invoices with filtering options.

#### POST /api/invoices/{invoice_id}/pay (Admin/HR only)
Record a manual payment.

**Request Body:**
```json
{
  "amount": 240.00,
  "payment_method": "BANK_TRANSFER",
  "payment_reference": "TXN123456",
  "notes": "Payment received via bank transfer"
}
```

### Analytics

#### GET /api/analytics/overview (Admin/HR only)
Get comprehensive subscription analytics.

#### GET /api/analytics/revenue (Admin/HR only)
Get detailed revenue analytics.

#### GET /api/analytics/subscriptions (Admin/HR only)
Get subscription metrics and trends.

## Feature Access Control

### Subscription Required Decorator

Use the `@subscription_required` decorator to protect endpoints:

```python
from application.decorators.subscription_required import subscription_required

@app.route('/api/announcements', methods=['POST'])
@token_required
@subscription_required('announcements', increment_usage=True)
def create_announcement():
    # Your endpoint logic
    pass
```

**Parameters:**
- `feature_name`: Name of the feature to check
- `increment_usage`: Whether to increment usage count

### Feature Usage Tracking

For analytics without blocking access:

```python
@feature_usage_tracking('reports')
def generate_report():
    # Your endpoint logic
    pass
```

### Employee Limit Check

For endpoints that add employees:

```python
@check_employee_limit()
def add_employee():
    # Your endpoint logic
    pass
```

## Default Subscription Plans

The system includes three default plans:

### Starter Plan
- **Price:** $5/employee/month
- **Max Employees:** 25
- **Features:** Basic employee management, attendance tracking, announcements, leave management

### Professional Plan
- **Price:** $12/employee/month
- **Max Employees:** 100
- **Features:** All Starter features + payroll, advanced analytics, performance management, API access

### Enterprise Plan
- **Price:** $20/employee/month
- **Max Employees:** Unlimited
- **Features:** All Professional features + custom integrations, priority support, custom fields

## Business Logic Services

### SubscriptionService

Central service for subscription operations:

- `create_company_subscription()`: Create new subscription
- `update_subscription_employee_count()`: Update billing based on employee count
- `generate_subscription_invoice()`: Create invoices
- `process_manual_payment()`: Record payments
- `upgrade_subscription()`: Change plans
- `cancel_subscription()`: Cancel subscriptions

### Automatic Processes

The system includes methods for automated tasks:

- **Subscription Status Updates:** Check for expired subscriptions
- **Trial Conversions:** Automatically activate trials
- **Invoice Generation:** Create recurring invoices
- **Employee Count Sync:** Update billing based on active employees

## Usage Examples

### Creating a Subscription

```python
from application.Services.subscription_service import SubscriptionService

# Create subscription for a company
subscription, error = SubscriptionService.create_company_subscription(
    company_id="company-123",
    plan_id="plan-456",
    trial_days=14
)
```

### Recording a Payment

```python
# Record manual payment
payment, error = SubscriptionService.process_manual_payment(
    invoice_id="invoice-789",
    amount=240.00,
    payment_method="BANK_TRANSFER",
    payment_reference="TXN123456",
    recorded_by="user-123"
)
```

### Checking Feature Access

```python
from application.Models.feature_access import FeatureAccess

# Check if company can use a feature
has_access = FeatureAccess.check_feature_access(
    company_id="company-123",
    feature_name="announcements"
)
```

## Migration Considerations

When implementing this system:

1. **Create Database Tables:** Run migrations for all subscription models
2. **Seed Default Plans:** Use the `seed_default_plans()` method
3. **Migrate Existing Companies:** Create default subscriptions for existing companies
4. **Update Existing Endpoints:** Add subscription decorators to protected features
5. **Employee Count Sync:** Run initial employee count updates

## Future Enhancements

The system is designed to support:

- **Online Payment Integration:** Stripe, PayPal, etc.
- **Automated Billing:** Recurring payment processing
- **Usage-Based Billing:** Track and bill for API calls, storage, etc.
- **Custom Plans:** Company-specific pricing
- **Discounts and Coupons:** Promotional pricing
- **Multi-Currency Support:** International billing

## Security Considerations

- All subscription data is stored in the central database
- Feature access is checked on every protected endpoint
- Payment information is logged for audit trails
- User permissions are enforced for administrative functions
- Subscription status is validated in real-time

## Monitoring and Analytics

The system provides comprehensive analytics:

- **Revenue Tracking:** Monthly recurring revenue, growth trends
- **Subscription Metrics:** Conversion rates, churn analysis
- **Feature Usage:** Most used features, adoption rates
- **Plan Performance:** Popular plans, upgrade patterns
