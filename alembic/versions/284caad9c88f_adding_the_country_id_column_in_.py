"""adding the country-id column in companies and other columns

Revision ID: 284caad9c88f
Revises: 7e58ec2b2fbf
Create Date: 2025-05-24 11:37:03.137297

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '284caad9c88f'
down_revision: Union[str, None] = '7e58ec2b2fbf'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('companies', sa.Column('country_id', sa.UUID(), nullable=True))
    op.create_foreign_key(None, 'companies', 'countries', ['country_id'], ['country_id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'companies', type_='foreignkey')
    op.drop_column('companies', 'country_id')
    # ### end Alembic commands ###
