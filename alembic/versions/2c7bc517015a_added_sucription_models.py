"""added sucription  models

Revision ID: 2c7bc517015a
Revises: 284caad9c88f
Create Date: 2025-06-21 16:22:56.555602

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '2c7bc517015a'
down_revision: Union[str, None] = '284caad9c88f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('subscription_plans',
    sa.Column('plan_id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('price_per_employee', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('billing_cycle', sa.String(length=20), nullable=False),
    sa.Column('max_employees', sa.Integer(), nullable=True),
    sa.Column('features', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('sort_order', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('plan_id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('company_subscriptions',
    sa.Column('subscription_id', sa.UUID(), nullable=False),
    sa.Column('company_id', sa.String(length=36), nullable=False),
    sa.Column('plan_id', sa.UUID(), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('current_period_start', sa.Date(), nullable=False),
    sa.Column('current_period_end', sa.Date(), nullable=False),
    sa.Column('employee_count', sa.Integer(), nullable=False),
    sa.Column('amount_due', sa.Numeric(precision=12, scale=2), nullable=False),
    sa.Column('trial_end_date', sa.Date(), nullable=True),
    sa.Column('auto_renew', sa.Boolean(), nullable=False),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('paypal_agreement_id', sa.String(length=255), nullable=True),
    sa.Column('last_billed_employee_count', sa.Integer(), nullable=False),
    sa.Column('next_billing_date', sa.Date(), nullable=True),
    sa.Column('payment_method', sa.String(length=50), nullable=False),
    sa.Column('auto_billing_enabled', sa.Boolean(), nullable=False),
    sa.Column('last_billing_attempt', sa.DateTime(), nullable=True),
    sa.Column('billing_retry_count', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['company_id'], ['companies.company_id'], ),
    sa.ForeignKeyConstraint(['plan_id'], ['subscription_plans.plan_id'], ),
    sa.PrimaryKeyConstraint('subscription_id'),
    sa.UniqueConstraint('company_id', name='uq_company_subscription')
    )
    op.create_index('idx_subscription_period', 'company_subscriptions', ['current_period_start', 'current_period_end'], unique=False)
    op.create_index('idx_subscription_status', 'company_subscriptions', ['status'], unique=False)
    op.create_table('feature_access',
    sa.Column('access_id', sa.UUID(), nullable=False),
    sa.Column('company_id', sa.String(length=36), nullable=False),
    sa.Column('feature_name', sa.String(length=100), nullable=False),
    sa.Column('is_enabled', sa.Boolean(), nullable=False),
    sa.Column('usage_limit', sa.Integer(), nullable=True),
    sa.Column('current_usage', sa.Integer(), nullable=False),
    sa.Column('reset_period', sa.String(length=20), nullable=True),
    sa.Column('last_reset_date', sa.Date(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['company_id'], ['companies.company_id'], ),
    sa.PrimaryKeyConstraint('access_id'),
    sa.UniqueConstraint('company_id', 'feature_name', name='uq_company_feature')
    )
    op.create_index('idx_feature_company', 'feature_access', ['company_id'], unique=False)
    op.create_index('idx_feature_enabled', 'feature_access', ['is_enabled'], unique=False)
    op.create_index('idx_feature_name', 'feature_access', ['feature_name'], unique=False)
    op.create_table('subscription_invoices',
    sa.Column('invoice_id', sa.UUID(), nullable=False),
    sa.Column('subscription_id', sa.UUID(), nullable=False),
    sa.Column('invoice_number', sa.String(length=50), nullable=False),
    sa.Column('billing_period_start', sa.Date(), nullable=False),
    sa.Column('billing_period_end', sa.Date(), nullable=False),
    sa.Column('employee_count_billed', sa.Integer(), nullable=False),
    sa.Column('subtotal', sa.Numeric(precision=12, scale=2), nullable=False),
    sa.Column('tax_rate', sa.Numeric(precision=5, scale=4), nullable=False),
    sa.Column('tax_amount', sa.Numeric(precision=12, scale=2), nullable=False),
    sa.Column('total_amount', sa.Numeric(precision=12, scale=2), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('due_date', sa.Date(), nullable=False),
    sa.Column('paid_date', sa.Date(), nullable=True),
    sa.Column('payment_method', sa.String(length=50), nullable=True),
    sa.Column('payment_reference', sa.String(length=255), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['subscription_id'], ['company_subscriptions.subscription_id'], ),
    sa.PrimaryKeyConstraint('invoice_id'),
    sa.UniqueConstraint('invoice_number')
    )
    op.create_index('idx_invoice_due_date', 'subscription_invoices', ['due_date'], unique=False)
    op.create_index('idx_invoice_period', 'subscription_invoices', ['billing_period_start', 'billing_period_end'], unique=False)
    op.create_index('idx_invoice_status', 'subscription_invoices', ['status'], unique=False)
    op.create_index('idx_invoice_subscription', 'subscription_invoices', ['subscription_id'], unique=False)
    op.create_table('subscription_payments',
    sa.Column('payment_id', sa.UUID(), nullable=False),
    sa.Column('invoice_id', sa.UUID(), nullable=False),
    sa.Column('amount', sa.Numeric(precision=12, scale=2), nullable=False),
    sa.Column('payment_method', sa.String(length=50), nullable=False),
    sa.Column('payment_reference', sa.String(length=255), nullable=True),
    sa.Column('payment_date', sa.DateTime(), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('transaction_id', sa.String(length=255), nullable=True),
    sa.Column('processor_response', sa.Text(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('recorded_by', sa.String(length=36), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['invoice_id'], ['subscription_invoices.invoice_id'], ),
    sa.PrimaryKeyConstraint('payment_id')
    )
    op.create_index('idx_payment_date', 'subscription_payments', ['payment_date'], unique=False)
    op.create_index('idx_payment_invoice', 'subscription_payments', ['invoice_id'], unique=False)
    op.create_index('idx_payment_method', 'subscription_payments', ['payment_method'], unique=False)
    op.create_index('idx_payment_status', 'subscription_payments', ['status'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_payment_status', table_name='subscription_payments')
    op.drop_index('idx_payment_method', table_name='subscription_payments')
    op.drop_index('idx_payment_invoice', table_name='subscription_payments')
    op.drop_index('idx_payment_date', table_name='subscription_payments')
    op.drop_table('subscription_payments')
    op.drop_index('idx_invoice_subscription', table_name='subscription_invoices')
    op.drop_index('idx_invoice_status', table_name='subscription_invoices')
    op.drop_index('idx_invoice_period', table_name='subscription_invoices')
    op.drop_index('idx_invoice_due_date', table_name='subscription_invoices')
    op.drop_table('subscription_invoices')
    op.drop_index('idx_feature_name', table_name='feature_access')
    op.drop_index('idx_feature_enabled', table_name='feature_access')
    op.drop_index('idx_feature_company', table_name='feature_access')
    op.drop_table('feature_access')
    op.drop_index('idx_subscription_status', table_name='company_subscriptions')
    op.drop_index('idx_subscription_period', table_name='company_subscriptions')
    op.drop_table('company_subscriptions')
    op.drop_table('subscription_plans')
    # ### end Alembic commands ###
