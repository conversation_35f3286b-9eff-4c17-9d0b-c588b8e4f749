"""added the flat_price to each plan

Revision ID: bdd487aa9913
Revises: 2c7bc517015a
Create Date: 2025-07-06 22:53:18.991341

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'bdd487aa9913'
down_revision: Union[str, None] = '2c7bc517015a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('subscription_plans', sa.Column('flat_price', sa.Numeric(precision=10, scale=2), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('subscription_plans', 'flat_price')
    # ### end Alembic commands ###
