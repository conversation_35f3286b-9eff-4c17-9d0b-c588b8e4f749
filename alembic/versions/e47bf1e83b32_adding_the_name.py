"""adding the name

Revision ID: e47bf1e83b32
Revises: bdd487aa9913
Create Date: 2025-10-04 17:27:21.761824

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'e47bf1e83b32'
down_revision: Union[str, None] = 'bdd487aa9913'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('api_clients', sa.Column('name', sa.String(length=255), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('api_clients', 'name')
    # ### end Alembic commands ###
