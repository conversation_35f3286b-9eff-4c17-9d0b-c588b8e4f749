import jwt, datetime, os
from dotenv import load_dotenv
load_dotenv()

SECRET_KEY = os.getenv("SECRET_KEY")

class APIToken:
    """Helper class for generating and decoding JWT tokens for API clients."""
    @staticmethod
    def generate(payload, expiry_minutes=60):
        payload["exp"] = datetime.datetime.utcnow() + datetime.timedelta(minutes=expiry_minutes)
        return jwt.encode(payload, SECRET_KEY, algorithm="HS256")

    @staticmethod
    def decode(token):
        try:
            return jwt.decode(token, SECRET_KEY, algorithms=["HS256"])
        except jwt.ExpiredSignatureError:
            return None
        except jwt.InvalidTokenError:
            return None
