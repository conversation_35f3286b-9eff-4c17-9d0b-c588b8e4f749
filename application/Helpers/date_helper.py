"""
This module contains helper methods for date calculations and manipulations.
"""
from datetime import datetime, date, timedelta
import calendar

class DateHelper:
    """
    A class containing helper methods for date calculations and manipulations.
    """

    @staticmethod
    def get_week_date_range(reference_date=None):
        """
        Get the date range for the week containing the reference date.
        Week is considered Monday to Sunday.

        Args:
            reference_date: Date to get the week for. Defaults to today.

        Returns:
            tuple: (start_date, end_date) for the week
        """
        if not reference_date:
            reference_date = date.today()
        elif isinstance(reference_date, str):
            try:
                reference_date = datetime.strptime(reference_date, '%Y-%m-%d').date()
            except ValueError:
                return None, None

        # Calculate the start of the week (Monday)
        start_date = reference_date - timedelta(days=reference_date.weekday())

        # Calculate the end of the week (Sunday)
        end_date = start_date + timedelta(days=6)

        return start_date, end_date

    @staticmethod
    def get_month_date_range(reference_date=None):
        """
        Get the date range for the month containing the reference date.

        Args:
            reference_date: Date to get the month for. Defaults to today.

        Returns:
            tuple: (start_date, end_date) for the month
        """
        if not reference_date:
            reference_date = date.today()
        elif isinstance(reference_date, str):
            try:
                reference_date = datetime.strptime(reference_date, '%Y-%m-%d').date()
            except ValueError:
                return None, None

        # Calculate the start of the month
        start_date = date(reference_date.year, reference_date.month, 1)

        # Calculate the end of the month
        _, last_day = calendar.monthrange(reference_date.year, reference_date.month)
        end_date = date(reference_date.year, reference_date.month, last_day)

        return start_date, end_date

    @staticmethod
    def get_year_date_range(reference_date=None):
        """
        Get the date range for the year containing the reference date.

        Args:
            reference_date: Date to get the year for. Defaults to today.

        Returns:
            tuple: (start_date, end_date) for the year
        """
        if not reference_date:
            reference_date = date.today()
        elif isinstance(reference_date, str):
            try:
                reference_date = datetime.strptime(reference_date, '%Y-%m-%d').date()
            except ValueError:
                return None, None

        # Calculate the start of the year (January 1st)
        start_date = date(reference_date.year, 1, 1)

        # Calculate the end of the year (December 31st)
        end_date = date(reference_date.year, 12, 31)

        return start_date, end_date

    @staticmethod
    def get_date_range_description(start_date, end_date):
        """
        Get a human-readable description of a date range.

        Args:
            start_date: Start date
            end_date: End date

        Returns:
            str: Description of the date range
        """
        if start_date == end_date:
            return start_date.strftime('%Y-%m-%d')

        # Check if it's a week
        if (end_date - start_date).days == 6 and start_date.weekday() == 0:
            return f"Week of {start_date.strftime('%Y-%m-%d')}"

        # Check if it's a month
        if (start_date.day == 1 and
            end_date.day == calendar.monthrange(end_date.year, end_date.month)[1] and
            start_date.month == end_date.month and
            start_date.year == end_date.year):
            return f"{start_date.strftime('%B %Y')}"

        # Default to date range
        return f"{start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}"

    @staticmethod
    def get_dates_in_range(start_date, end_date):
        """
        Get a list of all dates in a date range (inclusive).

        Args:
            start_date: Start date
            end_date: End date

        Returns:
            list: List of dates in the range
        """
        if not start_date or not end_date:
            return []

        dates = []
        current_date = start_date

        while current_date <= end_date:
            dates.append(current_date)
            current_date += timedelta(days=1)

        return dates
