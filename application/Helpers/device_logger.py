import logging
import os
from datetime import datetime
from logging.handlers import RotatingFileHandler

class DeviceLogger:
    """
    Dedicated logger for device-related operations to separate them from main application logs.
    """
    
    _instance = None
    _logger = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(<PERSON><PERSON><PERSON>og<PERSON>, cls).__new__(cls)
            cls._instance._setup_logger()
        return cls._instance
    
    def _setup_logger(self):
        """Setup the device logger with file rotation."""
        self._logger = logging.getLogger('device_operations')
        self._logger.setLevel(logging.INFO)
        
        # Prevent adding handlers multiple times
        if self._logger.handlers:
            return
        
        # Create logs directory if it doesn't exist
        log_dir = 'logs'
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # Setup rotating file handler for device logs
        device_log_file = os.path.join(log_dir, 'device.log')
        file_handler = RotatingFileHandler(
            device_log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        file_handler.setLevel(logging.INFO)
        
        # Setup console handler for device logs (optional, can be disabled)
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.WARNING)  # Only warnings and errors to console
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - DEVICE - %(levelname)s - %(message)s - [%(filename)s:%(lineno)d]'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # Add handlers to logger
        self._logger.addHandler(file_handler)
        self._logger.addHandler(console_handler)
        
        # Prevent propagation to root logger to avoid duplicate logs
        self._logger.propagate = False
    
    def info(self, message, device_sn=None):
        """Log info level message."""
        if device_sn:
            message = f"[{device_sn}] {message}"
        self._logger.info(message)
    
    def debug(self, message, device_sn=None):
        """Log debug level message."""
        if device_sn:
            message = f"[{device_sn}] {message}"
        self._logger.debug(message)
    
    def warning(self, message, device_sn=None):
        """Log warning level message."""
        if device_sn:
            message = f"[{device_sn}] {message}"
        self._logger.warning(message)
    
    def error(self, message, device_sn=None):
        """Log error level message."""
        if device_sn:
            message = f"[{device_sn}] {message}"
        self._logger.error(message)
    
    def critical(self, message, device_sn=None):
        """Log critical level message."""
        if device_sn:
            message = f"[{device_sn}] {message}"
        self._logger.critical(message)
    
    def log_command_processing(self, device_sn, command_count, action="processing"):
        """Log command processing activities."""
        self.info(f"Command {action}: {command_count} commands", device_sn)
    
    def log_websocket_activity(self, device_sn, action, details=None):
        """Log WebSocket related activities."""
        message = f"WebSocket {action}"
        if details:
            message += f": {details}"
        self.info(message, device_sn)
    
    def log_database_activity(self, device_sn, database_name, action):
        """Log database related activities."""
        self.debug(f"Database {action}: {database_name}", device_sn)
    
    def log_device_status(self, device_sn, status, details=None):
        """Log device status changes."""
        message = f"Status: {status}"
        if details:
            message += f" - {details}"
        self.info(message, device_sn)
    
    def log_error_with_traceback(self, message, device_sn=None, exception=None):
        """Log error with full traceback."""
        if device_sn:
            message = f"[{device_sn}] {message}"
        
        if exception:
            import traceback
            self._logger.error(f"{message}: {str(exception)}")
            self._logger.error(f"Traceback: {traceback.format_exc()}")
        else:
            self._logger.error(message)

# Singleton instance
device_logger = DeviceLogger()

# Convenience functions for easy import
def log_device_info(message, device_sn=None):
    """Convenience function for device info logging."""
    device_logger.info(message, device_sn)

def log_device_error(message, device_sn=None, exception=None):
    """Convenience function for device error logging."""
    if exception:
        device_logger.log_error_with_traceback(message, device_sn, exception)
    else:
        device_logger.error(message, device_sn)

def log_device_warning(message, device_sn=None):
    """Convenience function for device warning logging."""
    device_logger.warning(message, device_sn)

def log_device_debug(message, device_sn=None):
    """Convenience function for device debug logging."""
    device_logger.debug(message, device_sn)

def log_command_activity(device_sn, command_count, action="processing"):
    """Convenience function for command activity logging."""
    device_logger.log_command_processing(device_sn, command_count, action)

def log_websocket_activity(device_sn, action, details=None):
    """Convenience function for WebSocket activity logging."""
    device_logger.log_websocket_activity(device_sn, action, details)
