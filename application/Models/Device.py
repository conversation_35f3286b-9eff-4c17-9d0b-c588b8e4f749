

from flask import current_app
from flask_sqlalchemy import SQLAlchemy


from application.database import db
class Device(db.Model):
    """Device model for storing device information."""
    __tablename__ = 'device'
    id = db.Column(db.Integer, primary_key=True)
    serial_num = db.Column(db.String(80), unique=True, nullable=False)
    status = db.Column(db.Integer, nullable=False)

    def __str__(self):
        return "Device [id={}, serialNum={}, status={}]".format(self.id, self.serial_num, self.status)

    def to_dict(self):
        return {
            'id': self.id,
            'serialNum': self.serial_num,
            'status': self.status
        }

    # def __getitem__(self, index):
    #     print("self.contents[index]")
    #     return self.contents[index]
    def insert_device(serial_num, status):
        device = Device(serial_num=serial_num, status=status)
        db.session.add(device)
        db.session.commit()

    # 查询所有设备
    def get_all_devices(self):
        return db.session.query(Device).all()

    # 根据主键查询设备
    def get_device_by_id(self,id):
        return db.session.query(Device).filter_by(id)

    # 根据序列号查询设备
    def get_device_by_serial_num(self,serial_num):
        return db.session.query(Device).filter_by(serial_num=serial_num).first()

    # 更新设备信息
    def update_device(self,id, serial_num, status):
        device = db.session.query(Device).filter_by(id)
        if device:
            device.serial_num = serial_num
            device.status = status
            db.session.commit()

    # 插入新的设备
def insert_device(session, serial_num, status):
    """Insert a new device into the database using the provided session."""
    device = Device(serial_num=serial_num, status=status)
    try:
        session.add(device)
        session.commit()
        current_app.logger.info(f"Inserted new device with serial number '{serial_num}'.")
        return device.serial_num
    except Exception as e:
        session.rollback()
        current_app.logger.error(f"Failed to insert new device: {e}")
        return None

# 查询所有设备
def get_all_devices():

    return Device.query.all()

# 根据主键查询设备
def get_device_by_id(session, id):
    return session.query(Device).filter_by(id)

# 根据序列号查询设备
def get_device_by_serial_num(session,serial_num):
    """Retrieve a device by its serial number, using the provided session."""
    from app import app
    try:
        device = session.query(Device).filter_by(serial_num=serial_num).first()
        app.logger.info(f"Retrieved device by serial number '{serial_num}'. and the device is {device}")
        return device
    except Exception as e:
        app.logger.error(f"Failed to retrieve device by serial number: {e}")
        return None    


# 更新设备信息
def update_device(session, id, serial_num, status):
    device = session.query(Device).filter_by(id)
    if device:
        device.serial_num = serial_num
        device.status = status
        session.commit()
def update_status_by_primary_key(session, device_id, status):
    """Update the status of a device using the provided session."""
    from app import app
    device = session.query(Device).filter_by(id=device_id).first()
    if device:
        try:
            device.status = status
            session.commit()
            app.logger.info(f"Updated device status to '{status}'.")
            return True
        except Exception as e:
            app.logger.error(f"Failed to update device status: {e}")
            session.rollback()
            return False
