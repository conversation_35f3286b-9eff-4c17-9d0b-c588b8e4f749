from application.database import db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from flask import current_app
# class Person:
#     def __init__(self, id=None, name=None, roll_id=None):
#         self.id = id
#         self.name = name if name is not None else None
#         self.roll_id = roll_id
#
#     def __str__(self):
#         return f"Person [id={self.id}, name={self.name}, rollId={self.roll_id}]"
#

class Person(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(80), nullable=False)
    roll_id = db.Column(db.Integer, nullable=False)
    employee_id = db.Column(db.String(80),  unique=True, nullable=True)
    customer_id = db.Column(UUID(as_uuid=True), unique=True, nullable=True)  # NEW: Link to customer

    def __str__(self):
        return f"Person [id={self.id}, name={self.name}, rollId={self.roll_id}]"

    def to_dict(self):
        """Dictionary representation of the object."""
        employee_id = self.employee_id if self.employee_id is not None else None
        customer_id = str(self.customer_id) if self.customer_id is not None else None
        return {
            "id": self.id,
            "name": self.name,
            "roll_id": self.roll_id,
            "employee_id": employee_id,
            "customer_id": customer_id
        }
    
    @classmethod
    def get_person_given_id(cls,session, id):
        """Get a person given an id."""
        person = session.get(cls, id)
        return person 

    @classmethod
    def update_person(cls, session, id, employee_id):
        """Update a person's employee id."""
        person = session.get(cls, id)
        person.employee_id = employee_id
        session.commit()
        return person
            
    @classmethod
    def get_persons(cls, session):
        """Get all persons."""
        persons = session.query(cls).all()
        return persons

    # 插入数据
    def insert_person(self,session, **person):
        person = Person(**person)
        try:
            session.add(person)
            session.commit()
            current_app.logger.info(f"Inserted new person with name '{person.name}'.")
            return person.id
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Failed to insert new person: {e}")
            return None

    @classmethod
    def add_employee(cls,session, **kwargs):
        """Add an employee to the database."""
        try:
            employee = cls(**kwargs)
            session.add(employee)
            session.commit()
            # Return the object
            return employee
        except Exception as e:
            current_app.logger.error(f"Error adding employee: {e}")
            return None

    @classmethod
    def add_customer(cls, session, **kwargs):
        """Add a customer to the database.

        Args:
            session: Database session
            **kwargs: Person attributes (id, name, roll_id, customer_id)

        Returns:
            Person object or None if failed
        """
        try:
            customer = cls(**kwargs)
            session.add(customer)
            session.commit()
            current_app.logger.info(f"Added customer person: {customer.id} -> customer_id: {customer.customer_id}")
            return customer
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error adding customer: {e}")
            return None

    @classmethod
    def get_person_type(cls, session, enroll_id):
        """Determine if a person (by enroll_id) is an employee or customer.

        This is the KEY routing method that determines how to process biometric records.

        Args:
            session: Database session
            enroll_id: The biometric device enroll ID (person.id)

        Returns:
            tuple: (person_type, linked_id) where:
                - person_type is 'employee', 'customer', or None
                - linked_id is the employee_id or customer_id
        """
        try:
            current_app.logger.info(f"[PERSON_TYPE] Querying person table for enroll_id: {enroll_id}")
            person = session.query(cls).filter_by(id=enroll_id).first()

            if not person:
                current_app.logger.warning(f"[PERSON_TYPE] ❌ No person found with enroll_id: {enroll_id}")
                return None, None

            current_app.logger.info(f"[PERSON_TYPE] ✅ Person found: name='{person.name}', employee_id={person.employee_id}, customer_id={person.customer_id}")

            if person.employee_id:
                current_app.logger.info(f"[PERSON_TYPE] ✅ Person is EMPLOYEE with employee_id: {person.employee_id}")
                return 'employee', person.employee_id
            elif person.customer_id:
                current_app.logger.info(f"[PERSON_TYPE] ✅ Person is CUSTOMER with customer_id: {person.customer_id}")
                return 'customer', person.customer_id
            else:
                current_app.logger.warning(f"[PERSON_TYPE] ⚠️ Person {enroll_id} (name: {person.name}) has neither employee_id nor customer_id")
                return None, None
        except Exception as e:
            current_app.logger.error(f"[PERSON_TYPE] ❌ EXCEPTION determining person type: {e}", exc_info=True)
            return None, None

    @classmethod
    def get_linked_entity(cls, session, enroll_id):
        """Get the linked employee or customer entity for a person.

        Args:
            session: Database session
            enroll_id: The biometric device enroll ID (person.id)

        Returns:
            tuple: (entity_type, entity_object) where:
                - entity_type is 'employee', 'customer', or None
                - entity_object is the Employee or Customer object
        """
        try:
            person_type, linked_id = cls.get_person_type(session, enroll_id)

            if not person_type:
                return None, None

            if person_type == 'employee':
                from application.Models.employees.employee import Employee
                employee = Employee.get_employee_by_id(session, linked_id)
                return 'employee', employee
            elif person_type == 'customer':
                from application.Models.customers.customer import Customer
                customer = Customer.get_customer_by_id(session, linked_id)
                return 'customer', customer

            return None, None
        except Exception as e:
            current_app.logger.error(f"Error getting linked entity: {e}")
            return None, None

    @classmethod
    def select_person_by_their_id(cls, session, id):
        """select Person by Id."""
        person = session.query(cls).get(id)
        return person

    # 查询数据
    def select_all(self):
        """Legacy method - DO NOT USE for new code.

        This method uses the old Flask-SQLAlchemy pattern which can cause
        session context issues in multi-tenant applications.
        Use select_all_with_session(session) instead.
        """
        current_app.logger.warning(f"[PERSON_QUERY] ⚠️ Using legacy select_all() method - may cause session context issues")
        return Person.query.all()

    @classmethod
    def select_all_with_session(cls, session, database_name=None):
        """Get all persons using proper session context.

        Args:
            session: Database session for the specific company database
            database_name: Optional database name for logging context

        Returns:
            List of Person objects
        """
        if database_name:
            current_app.logger.info(f"[PERSON_QUERY] Querying all persons from database: {database_name}")

        try:
            persons = session.query(cls).all()
            current_app.logger.info(f"[PERSON_QUERY] Retrieved {len(persons)} persons from session-based query")

            # Log summary of person types
            employee_count = sum(1 for p in persons if p.employee_id)
            customer_count = sum(1 for p in persons if p.customer_id)
            unknown_count = len(persons) - employee_count - customer_count

            current_app.logger.info(f"[PERSON_QUERY] Person breakdown: {employee_count} employees, {customer_count} customers, {unknown_count} unknown")

            return persons
        except Exception as e:
            current_app.logger.error(f"[PERSON_QUERY] Error retrieving persons: {e}")
            return []

    def select_person_by_id(self,session, id):
        person = session.query(Person).get(id)
        return person

    # 删除数据
    def delete_person_by_id(self,id):
        person = select_person_by_id(id)
        if person:
            db.session.delete(person)
            db.session.commit()


        # 更新数据
    def update_person_by_id(self,id, name=None, roll_id=None):
        person = select_person_by_id(id)
        if name is not None:
            person.name = name
        if roll_id is not None:
            person.roll_id = roll_id
        db.session.commit()
    def delete_by_primary_key(self,id):
        person = select_person_by_id(id)
        if person:
            db.session.delete(person)
            db.session.commit()
def insert_person(session, **person):
    person = Person(**person)
    try:
        session.add(person)
        session.commit()
        current_app.logger.info(f"Inserted new person with name '{person.name}'.")
        return person.id
    except Exception as e:
        session.rollback()
        current_app.logger.error(f"Failed to insert new person: {e}")
        return None

def insert_person2(session, person):
    """Insert a new person into the database using the provided session."""
    try:
        session.add(person)
        session.commit()
        current_app.logger.info(f"Inserted new person with name '{person.name}'.")
        return person.id
    except Exception as e:
        session.rollback()
        current_app.logger.error(f"Failed to insert new person: {e}")
        return None

# 查询数据
def select_all(session, database_name=None):
    """Get all persons using proper session context.

    Args:
        session: Database session for the specific company database
        database_name: Optional database name for logging context

    Returns:
        List of Person objects or None if error
    """
    try:
        if database_name:
            current_app.logger.info(f"[PERSON_QUERY] Global select_all querying database: {database_name}")

        persons = session.query(Person).all()
        current_app.logger.info(f"[PERSON_QUERY] Global select_all retrieved {len(persons)} persons")

        # Log summary of person types
        employee_count = sum(1 for p in persons if p.employee_id)
        customer_count = sum(1 for p in persons if p.customer_id)
        unknown_count = len(persons) - employee_count - customer_count

        current_app.logger.info(f"[PERSON_QUERY] Global select_all breakdown: {employee_count} employees, {customer_count} customers, {unknown_count} unknown")

        return persons
    except Exception as e:
        current_app.logger.error(f"[PERSON_QUERY] Global select_all failed to retrieve persons: {e}")
        return None

def select_person_by_id(session, id):
    try:
        person = session.get(Person, id)
        current_app.logger.info(f"Retrieved person by id '{id}': {person}")
        return person
    except Exception as e:
        current_app.logger.error(f"Failed to retrieve person by id: {e}")
        return None
# 删除数据
def delete_person_by_id(session, id):
    person = select_person_by_id(session, id)
    session.delete(person)
    session.commit()

# 更新数据
def update_person_by_id(session, id, name=None, roll_id=None):
    person = select_person_by_id(session, id)
    if name is not None:
        person.name = name
    if roll_id is not None:
        person.roll_id = roll_id
    session.commit()
def update_by_primary_key(session, person):
    person = select_person_by_id(session, person.id)
    if person:
        person.name = person.name
        person.roll_id = person.roll_id
        session.commit()
