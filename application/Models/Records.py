from application.database import db
from datetime import datetime
from sqlalchemy import cast, Date
from application.Models.Person import Person
from flask import current_app as app
# class Records:
#     def __init__(self, id=None, enroll_id=None, records_time=None, mode=None,
#                  intout=None, event=None, device_serial_num=None, temperature=None, image=None):
#         self.id = id
#         self.enroll_id = enroll_id
#         self.records_time = records_time if records_time is not None else None
#         self.mode = mode
#         self.intout = intout
#         self.event = event
#         self.device_serial_num = device_serial_num if device_serial_num is not None else None
#         self.temperature = temperature
#         self.image = image if image is not None else None


class Record(db.Model):
    __tablename__ = 'records'
    id = db.Column(db.Integer, primary_key=True)
    enroll_id = db.Column(db.BigInteger)
    records_time = db.Column(db.DateTime)
    mode = db.Column(db.Integer)
    intOut = db.Column(db.Integer)
    event = db.Column(db.Integer)
    device_serial_num = db.Column(db.String(255))
    temperature = db.Column(db.Float)
    image = db.Column(db.String(255))

    def __str__(self):
        return f"Record [id={self.id}, enroll_id={self.enroll_id}, records_time={self.records_time}, mode={self.mode}, " \
               f"intOut={self.intOut}, event={self.event}, device_serial_num={self.device_serial_num}, " \
               f"temperature={self.temperature}, image={self.image}]"

    def to_dict(self, session):
        """Dictionary representation of the object."""
        try:
            person = Person.get_person_given_id(session, self.enroll_id)
            if person:
                name = person.name
                employee_id = person.employee_id
            else:
                name = None
                employee_id = None
        except Exception as e:
            app.logger.error(f"Error getting person name: {e}")
            name = None 
            employee_id = None           

        return {
            "id": self.id,
            "enroll_id": self.enroll_id,
            "records_time": str(self.records_time),
            "mode": self.mode,
            "intOut": self.intOut,
            "event": self.event,
            "device_serial_num": self.device_serial_num,
            "temperature": self.temperature,
            "image": self.image,
            "name": name,
            "employee_id": employee_id
        }

    def format_date(self,date_string):
        date = datetime.strptime(date_string, "%Y-%m-%dT%H:%M:%S%z")
        return date.strftime("%Y-%m-%d %H:%M:20")

    @classmethod
    def get_daily_records(cls, session, date):
        """Return all records for a given date.
        args:
            date: date in the format 'YYYY-MM-DD'
            session: database session
        """
        # Get the records based on the database session
        try:
            records = session.query(Record).filter(cast(Record.records_time, Date) == date).all()
            return records
        except Exception as e:
            app.logger.error(f"Error getting records for date {date}: {e}")
            return None
       
def insert_record(session, record):
    """Insert a new record."""
    try:
        session.add(record)
        session.commit()
        message = f"""
        New record inserted with the following details:
        record: {record}
        """
        app.logger.info(message)
        return message
    except Exception as e:
        session.rollback()
        app.logger.error(f"Failed to insert new record: {e}")
        return None
def insert_record2(session, **record):
    record = Record(**record)
    try:
        session.add(record)
        session.commit()
        app.logger.info(f"Inserted new record with enroll_id '{record.enroll_id}'.")
        message = f"""
        New record inserted with the following details:
        "record": {record}
        """
        app.logger.info(message)
        return message
    except Exception as e:
        session.rollback()
        app.logger.error(f"Failed to insert new record: {e}")
        return None

def select_record_by_id(session,id):
    return  session.query(Record).get(id)


def delete_record_by_id(session, id):
    record = select_record_by_id(session, id)
    session.delete(record)
    session.commit()


def update_record_by_id(session, id, **kwargs):
    record = select_record_by_id(session, id)
    for key, value in kwargs.items():
        setattr(record, key, value)
    session.commit()

def select_all_records(session):
    return session.query(Record).all()

# def format_date(date_string):
#     date = datetime.strptime(date_string, "%Y-%m-%dT%H:%M:%S%z")
#     return date.strftime("%Y-%m-%d %H:%M:%S")
# if "__main__" == __name__:
