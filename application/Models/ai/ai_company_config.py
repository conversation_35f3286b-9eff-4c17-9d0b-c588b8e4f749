from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUID
from flask import current_app as app
from datetime import datetime
import json

class AICompanyConfig(db.Model):
    """Model for company-specific AI preferences and settings"""
    __tablename__ = 'ai_company_configs'

    config_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    company_id = db.Column(db.String(36), nullable=False, unique=True)  # Reference to company
    
    # AI Provider preferences
    preferred_provider_id = db.Column(UUID(as_uuid=True), db.<PERSON>ey('ai_providers.provider_id'), nullable=True)
    fallback_provider_id = db.Column(UUID(as_uuid=True), db.ForeignKey('ai_providers.provider_id'), nullable=True)
    
    # Company context for AI insights
    company_size = db.Column(db.String(50), nullable=True)  # small, medium, large, enterprise
    industry = db.Column(db.String(100), nullable=True)  # technology, healthcare, manufacturing, etc.
    business_model = db.Column(db.String(100), nullable=True)  # b2b, b2c, saas, etc.
    company_culture = db.Column(db.String(100), nullable=True)  # formal, casual, startup, corporate
    
    # Geographic and regulatory context
    primary_location = db.Column(db.String(100), nullable=True)  # Country/region
    timezone = db.Column(db.String(50), nullable=True)
    regulatory_requirements = db.Column(db.Text, nullable=True)  # JSON array of compliance requirements
    
    # AI insight preferences
    insight_frequency = db.Column(db.String(50), default='daily')  # daily, weekly, monthly, on_demand
    preferred_insight_types = db.Column(db.Text, nullable=True)  # JSON array of preferred insight types
    excluded_insight_types = db.Column(db.Text, nullable=True)  # JSON array of excluded insight types
    
    # Notification preferences
    enable_ai_notifications = db.Column(db.Boolean, default=True)
    notification_channels = db.Column(db.Text, nullable=True)  # JSON array: email, dashboard, slack, etc.
    notification_recipients = db.Column(db.Text, nullable=True)  # JSON array of user roles/IDs
    urgent_alert_threshold = db.Column(db.String(50), default='high')  # low, medium, high, urgent
    
    # Content preferences
    preferred_tone = db.Column(db.String(50), default='professional')  # professional, casual, technical
    preferred_detail_level = db.Column(db.String(50), default='medium')  # brief, medium, detailed
    include_recommendations = db.Column(db.Boolean, default=True)
    include_benchmarks = db.Column(db.Boolean, default=True)
    include_predictions = db.Column(db.Boolean, default=True)
    
    # Custom prompts and templates
    custom_system_prompt = db.Column(db.Text, nullable=True)  # Custom system prompt for this company
    custom_context = db.Column(db.Text, nullable=True)  # Additional context to include in prompts
    custom_templates = db.Column(db.Text, nullable=True)  # JSON of custom template overrides
    
    # Usage limits and controls
    daily_request_limit = db.Column(db.Integer, default=100)
    monthly_request_limit = db.Column(db.Integer, default=2000)
    max_tokens_per_request = db.Column(db.Integer, default=2000)
    enable_caching = db.Column(db.Boolean, default=True)
    cache_duration_hours = db.Column(db.Integer, default=24)
    
    # Feature flags
    enable_predictive_insights = db.Column(db.Boolean, default=True)
    enable_cross_module_insights = db.Column(db.Boolean, default=True)
    enable_smart_announcements = db.Column(db.Boolean, default=True)
    enable_executive_summaries = db.Column(db.Boolean, default=True)
    
    # Privacy and security
    data_anonymization_level = db.Column(db.String(50), default='medium')  # none, low, medium, high
    exclude_sensitive_data = db.Column(db.Boolean, default=True)
    data_retention_days = db.Column(db.Integer, default=90)  # How long to keep AI request/response data
    
    # Relationships
    preferred_provider = db.relationship('AIProvider', foreign_keys=[preferred_provider_id])
    fallback_provider = db.relationship('AIProvider', foreign_keys=[fallback_provider_id])
    
    # Metadata
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    def __str__(self):
        return f"AICompanyConfig [config_id={self.config_id}, company_id={self.company_id}]"

    def to_dict(self):
        return {
            "config_id": str(self.config_id),
            "company_id": self.company_id,
            "preferred_provider_id": str(self.preferred_provider_id) if self.preferred_provider_id else None,
            "fallback_provider_id": str(self.fallback_provider_id) if self.fallback_provider_id else None,
            "company_size": self.company_size,
            "industry": self.industry,
            "business_model": self.business_model,
            "company_culture": self.company_culture,
            "primary_location": self.primary_location,
            "timezone": self.timezone,
            "regulatory_requirements": json.loads(self.regulatory_requirements) if self.regulatory_requirements else [],
            "insight_frequency": self.insight_frequency,
            "preferred_insight_types": json.loads(self.preferred_insight_types) if self.preferred_insight_types else [],
            "excluded_insight_types": json.loads(self.excluded_insight_types) if self.excluded_insight_types else [],
            "enable_ai_notifications": self.enable_ai_notifications,
            "notification_channels": json.loads(self.notification_channels) if self.notification_channels else [],
            "notification_recipients": json.loads(self.notification_recipients) if self.notification_recipients else [],
            "urgent_alert_threshold": self.urgent_alert_threshold,
            "preferred_tone": self.preferred_tone,
            "preferred_detail_level": self.preferred_detail_level,
            "include_recommendations": self.include_recommendations,
            "include_benchmarks": self.include_benchmarks,
            "include_predictions": self.include_predictions,
            "custom_system_prompt": self.custom_system_prompt,
            "custom_context": self.custom_context,
            "custom_templates": json.loads(self.custom_templates) if self.custom_templates else {},
            "daily_request_limit": self.daily_request_limit,
            "monthly_request_limit": self.monthly_request_limit,
            "max_tokens_per_request": self.max_tokens_per_request,
            "enable_caching": self.enable_caching,
            "cache_duration_hours": self.cache_duration_hours,
            "enable_predictive_insights": self.enable_predictive_insights,
            "enable_cross_module_insights": self.enable_cross_module_insights,
            "enable_smart_announcements": self.enable_smart_announcements,
            "enable_executive_summaries": self.enable_executive_summaries,
            "data_anonymization_level": self.data_anonymization_level,
            "exclude_sensitive_data": self.exclude_sensitive_data,
            "data_retention_days": self.data_retention_days,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

    @classmethod
    def get_config_by_company(cls, session, company_id):
        """Get AI configuration for a company."""
        try:
            config = session.query(cls).filter_by(company_id=company_id).first()
            
            # Create default config if none exists
            if not config:
                config = cls.create_default_config(session, company_id)
            
            return config
        except Exception as e:
            app.logger.error(f"Error getting AI config for company {company_id}: {e}")
            return None

    @classmethod
    def create_default_config(cls, session, company_id):
        """Create default AI configuration for a company using actual company data."""
        try:
            # Get actual company information
            from application.Models.company import Company
            company = Company.get_company_by_id(company_id)
            company_dict = company.to_dict(include_country_details=True) if company else {}

            # Extract company information for AI config
            primary_location = None
            if company_dict.get('country'):
                primary_location = company_dict['country']['name']

            # Determine company size based on employee count (if available)
            company_size = 'medium'  # Default
            # Note: We could enhance this by querying employee count from tenant DB

            config = cls(
                company_id=company_id,
                company_size=company_size,
                primary_location=primary_location,
                insight_frequency='daily',
                preferred_tone='professional',
                preferred_detail_level='medium',
                enable_ai_notifications=True,
                include_recommendations=True,
                include_benchmarks=True,
                include_predictions=True,
                daily_request_limit=100,
                monthly_request_limit=2000,
                enable_caching=True,
                cache_duration_hours=24,
                enable_predictive_insights=True,
                enable_cross_module_insights=True,
                enable_smart_announcements=True,
                enable_executive_summaries=True,
                data_anonymization_level='medium',
                exclude_sensitive_data=True,
                data_retention_days=90
            )
            
            session.add(config)
            session.commit()
            app.logger.info(f"Created default AI config for company: {company_id}")
            return config
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating default AI config: {e}")
            return None

    @classmethod
    def update_config(cls, session, company_id, **kwargs):
        """Update AI configuration for a company."""
        try:
            config = cls.get_config_by_company(session, company_id)
            if not config:
                return None

            # Update configuration attributes
            for key, value in kwargs.items():
                if hasattr(config, key):
                    setattr(config, key, value)

            session.commit()
            app.logger.info(f"Updated AI config for company: {company_id}")
            return config
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error updating AI config: {e}")
            return None

    def get_context_for_prompts(self):
        """Get company context formatted for AI prompts with enhanced company data."""
        context = []

        # Get fresh company data if available
        try:
            from application.Models.company import Company
            company = Company.get_company_by_id(self.company_id)
            if company:
                company_dict = company.to_dict(include_country_details=True)

                # Add company name
                context.append(f"Company: {company_dict.get('company_name', 'Unknown')}")

                # Add country information
                if company_dict.get('country'):
                    country_info = company_dict['country']
                    context.append(f"Location: {country_info['name']} ({country_info['currency']})")
                elif self.primary_location:
                    context.append(f"Primary location: {self.primary_location}")
        except Exception:
            # Fallback to stored config data
            pass

        if self.company_size:
            context.append(f"Company size: {self.company_size}")

        if self.industry:
            context.append(f"Industry: {self.industry}")

        if self.business_model:
            context.append(f"Business model: {self.business_model}")

        if self.company_culture:
            context.append(f"Company culture: {self.company_culture}")

        if self.custom_context:
            context.append(self.custom_context)

        return ". ".join(context) if context else "Medium-sized organization"

    def is_insight_type_enabled(self, insight_type):
        """Check if a specific insight type is enabled for this company."""
        excluded_types = json.loads(self.excluded_insight_types) if self.excluded_insight_types else []
        preferred_types = json.loads(self.preferred_insight_types) if self.preferred_insight_types else []
        
        # If excluded, return False
        if insight_type in excluded_types:
            return False
        
        # If preferred types are specified and this isn't in them, return False
        if preferred_types and insight_type not in preferred_types:
            return False
        
        return True
