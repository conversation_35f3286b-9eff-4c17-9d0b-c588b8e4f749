from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUI<PERSON>
from flask import current_app as app
from datetime import datetime
import json

class AIInsightRequest(db.Model):
    """Model for tracking AI insight requests for auditing and optimization"""
    __tablename__ = 'ai_insight_requests'

    request_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    
    # Request context
    company_id = db.Column(db.String(36), nullable=False)  # Reference to company
    user_id = db.Column(UUID(as_uuid=True), nullable=True)  # User who triggered the request
    module = db.Column(db.String(100), nullable=False)  # attendance, payroll, performance, etc.
    insight_type = db.Column(db.String(100), nullable=False)  # daily_summary, trend_analysis, etc.
    
    # AI Provider and template used
    provider_id = db.Column(UUID(as_uuid=True), db.ForeignKey('ai_providers.provider_id'), nullable=False)
    template_id = db.Column(UUID(as_uuid=True), db.ForeignKey('ai_insight_templates.template_id'), nullable=True)
    
    # Request details
    prompt_used = db.Column(db.Text, nullable=False)  # The actual prompt sent to AI
    input_data = db.Column(db.Text, nullable=True)  # JSON of input data (anonymized)
    context_data = db.Column(db.Text, nullable=True)  # Additional context (company size, industry, etc.)
    
    # Request parameters
    max_tokens = db.Column(db.Integer, nullable=True)
    temperature = db.Column(db.Numeric(3, 2), nullable=True)
    other_parameters = db.Column(db.Text, nullable=True)  # JSON of other AI parameters
    
    # Response tracking
    response_id = db.Column(UUID(as_uuid=True), nullable=True)  # Links to AIInsightResponse
    
    # Performance metrics
    request_time = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    response_time = db.Column(db.DateTime, nullable=True)
    processing_duration_ms = db.Column(db.Integer, nullable=True)  # Duration in milliseconds
    
    # Usage metrics
    tokens_used = db.Column(db.Integer, nullable=True)
    estimated_cost = db.Column(db.Numeric(10, 6), nullable=True)  # Cost in USD
    
    # Status and error handling
    status = db.Column(db.String(50), default='pending')  # pending, completed, failed, cached
    error_message = db.Column(db.Text, nullable=True)
    retry_count = db.Column(db.Integer, default=0)
    
    # Relationships
    provider = db.relationship('AIProvider', backref=db.backref('requests', lazy='dynamic'))
    template = db.relationship('AIInsightTemplate', backref=db.backref('requests', lazy='dynamic'))
    
    # Metadata
    created_at = db.Column(db.DateTime, server_default=db.func.now())

    def __str__(self):
        return f"AIInsightRequest [request_id={self.request_id}, module={self.module}, status={self.status}]"

    def to_dict(self):
        return {
            "request_id": str(self.request_id),
            "company_id": self.company_id,
            "user_id": str(self.user_id) if self.user_id else None,
            "module": self.module,
            "insight_type": self.insight_type,
            "provider_id": str(self.provider_id),
            "template_id": str(self.template_id) if self.template_id else None,
            "prompt_used": self.prompt_used,
            "input_data": json.loads(self.input_data) if self.input_data else None,
            "context_data": json.loads(self.context_data) if self.context_data else None,
            "max_tokens": self.max_tokens,
            "temperature": float(self.temperature) if self.temperature else None,
            "other_parameters": json.loads(self.other_parameters) if self.other_parameters else None,
            "response_id": str(self.response_id) if self.response_id else None,
            "request_time": self.request_time.strftime('%Y-%m-%d %H:%M:%S') if self.request_time else None,
            "response_time": self.response_time.strftime('%Y-%m-%d %H:%M:%S') if self.response_time else None,
            "processing_duration_ms": self.processing_duration_ms,
            "tokens_used": self.tokens_used,
            "estimated_cost": float(self.estimated_cost) if self.estimated_cost else 0.0,
            "status": self.status,
            "error_message": self.error_message,
            "retry_count": self.retry_count,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None
        }

    @classmethod
    def create_request(cls, session, **kwargs):
        """Create a new AI insight request."""
        try:
            request = cls(**kwargs)
            session.add(request)
            session.commit()
            app.logger.info(f"Created new AI insight request: {request.request_id}")
            return request
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating AI insight request: {e}")
            return None

    @classmethod
    def update_request_status(cls, session, request_id, status, **kwargs):
        """Update request status and related fields."""
        try:
            request = session.query(cls).filter_by(request_id=request_id).first()
            if not request:
                return None

            request.status = status
            
            # Update other fields if provided
            for key, value in kwargs.items():
                if hasattr(request, key):
                    setattr(request, key, value)

            # Set response time if completing
            if status in ['completed', 'failed'] and not request.response_time:
                request.response_time = datetime.utcnow()
                
                # Calculate processing duration
                if request.request_time:
                    duration = request.response_time - request.request_time
                    request.processing_duration_ms = int(duration.total_seconds() * 1000)

            session.commit()
            return request
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error updating AI insight request status: {e}")
            return None

    @classmethod
    def get_requests_by_company(cls, session, company_id, limit=100, status=None):
        """Get AI requests for a company."""
        try:
            query = session.query(cls).filter(cls.company_id == company_id)
            
            if status:
                query = query.filter(cls.status == status)
            
            return query.order_by(cls.created_at.desc()).limit(limit).all()
        except Exception as e:
            app.logger.error(f"Error getting AI requests for company {company_id}: {e}")
            return []

    @classmethod
    def get_usage_stats(cls, session, company_id=None, start_date=None, end_date=None):
        """Get usage statistics for AI requests."""
        try:
            query = session.query(cls)
            
            if company_id:
                query = query.filter(cls.company_id == company_id)
            
            if start_date:
                query = query.filter(cls.created_at >= start_date)
            
            if end_date:
                query = query.filter(cls.created_at <= end_date)
            
            requests = query.all()
            
            # Calculate statistics
            total_requests = len(requests)
            completed_requests = len([r for r in requests if r.status == 'completed'])
            failed_requests = len([r for r in requests if r.status == 'failed'])
            total_tokens = sum([r.tokens_used for r in requests if r.tokens_used])
            total_cost = sum([r.estimated_cost for r in requests if r.estimated_cost])
            avg_duration = sum([r.processing_duration_ms for r in requests if r.processing_duration_ms]) / max(completed_requests, 1)
            
            return {
                "total_requests": total_requests,
                "completed_requests": completed_requests,
                "failed_requests": failed_requests,
                "success_rate": (completed_requests / max(total_requests, 1)) * 100,
                "total_tokens_used": total_tokens,
                "total_estimated_cost": float(total_cost),
                "average_duration_ms": avg_duration
            }
        except Exception as e:
            app.logger.error(f"Error getting AI usage stats: {e}")
            return {}
