from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUI<PERSON>
from flask import current_app as app
from datetime import datetime, timedelta
import json

class AIInsightResponse(db.Model):
    """Model for storing AI-generated insights with metadata and user engagement tracking"""
    __tablename__ = 'ai_insight_responses'

    response_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    request_id = db.Column(UUID(as_uuid=True), db.ForeignKey('ai_insight_requests.request_id'), nullable=False)
    
    # Response content
    insight_content = db.Column(db.Text, nullable=False)  # The AI-generated insight
    insight_summary = db.Column(db.Text, nullable=True)  # Brief summary for quick view
    insight_type = db.Column(db.String(100), nullable=False)  # daily_summary, trend_analysis, etc.
    confidence_score = db.Column(db.Numeric(3, 2), nullable=True)  # AI confidence in the insight
    
    # Structured data extraction
    key_metrics = db.Column(db.Text, nullable=True)  # JSON of key metrics mentioned
    recommendations = db.Column(db.Text, nullable=True)  # JSON array of actionable recommendations
    alerts = db.Column(db.Text, nullable=True)  # JSON array of important alerts/warnings
    
    # Categorization and tagging
    category = db.Column(db.String(100), nullable=False)  # attendance, payroll, performance, etc.
    priority = db.Column(db.String(50), default='medium')  # low, medium, high, urgent
    tags = db.Column(db.Text, nullable=True)  # JSON array of tags for filtering
    
    # User engagement tracking
    view_count = db.Column(db.Integer, default=0)
    unique_viewers = db.Column(db.Text, nullable=True)  # JSON array of user IDs who viewed
    last_viewed = db.Column(db.DateTime, nullable=True)
    
    # User feedback
    user_rating = db.Column(db.Numeric(3, 2), nullable=True)  # Average user rating (1-5)
    rating_count = db.Column(db.Integer, default=0)
    feedback_comments = db.Column(db.Text, nullable=True)  # JSON array of user feedback
    
    # Action tracking
    actions_taken = db.Column(db.Text, nullable=True)  # JSON array of actions users took based on insight
    action_count = db.Column(db.Integer, default=0)
    
    # Caching and expiry
    cache_key = db.Column(db.String(255), nullable=True)  # For caching similar insights
    expires_at = db.Column(db.DateTime, nullable=True)  # When this insight becomes stale
    is_cached = db.Column(db.Boolean, default=False)
    
    # Status
    is_active = db.Column(db.Boolean, default=True)
    is_featured = db.Column(db.Boolean, default=False)  # Featured insights for dashboard
    is_archived = db.Column(db.Boolean, default=False)
    
    # Relationships
    request = db.relationship('AIInsightRequest', backref=db.backref('response', uselist=False))
    
    # Metadata
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    def __str__(self):
        return f"AIInsightResponse [response_id={self.response_id}, category={self.category}, priority={self.priority}]"

    def to_dict(self, include_content=True):
        result = {
            "response_id": str(self.response_id),
            "request_id": str(self.request_id),
            "insight_type": self.insight_type,
            "confidence_score": float(self.confidence_score) if self.confidence_score else None,
            "category": self.category,
            "priority": self.priority,
            "tags": json.loads(self.tags) if self.tags else [],
            "view_count": self.view_count,
            "last_viewed": self.last_viewed.strftime('%Y-%m-%d %H:%M:%S') if self.last_viewed else None,
            "user_rating": float(self.user_rating) if self.user_rating else None,
            "rating_count": self.rating_count,
            "action_count": self.action_count,
            "expires_at": self.expires_at.strftime('%Y-%m-%d %H:%M:%S') if self.expires_at else None,
            "is_cached": self.is_cached,
            "is_active": self.is_active,
            "is_featured": self.is_featured,
            "is_archived": self.is_archived,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }
        
        if include_content:
            result.update({
                "insight_content": self.insight_content,
                "insight_summary": self.insight_summary,
                "key_metrics": json.loads(self.key_metrics) if self.key_metrics else {},
                "recommendations": json.loads(self.recommendations) if self.recommendations else [],
                "alerts": json.loads(self.alerts) if self.alerts else [],
                "feedback_comments": json.loads(self.feedback_comments) if self.feedback_comments else [],
                "actions_taken": json.loads(self.actions_taken) if self.actions_taken else []
            })
        
        return result

    @classmethod
    def create_response(cls, session, **kwargs):
        """Create a new AI insight response."""
        try:
            # Set default expiry if not provided (24 hours for most insights)
            if 'expires_at' not in kwargs and kwargs.get('category') in ['attendance', 'daily_summary']:
                kwargs['expires_at'] = datetime.utcnow() + timedelta(hours=24)
            elif 'expires_at' not in kwargs:
                kwargs['expires_at'] = datetime.utcnow() + timedelta(days=7)

            response = cls(**kwargs)
            session.add(response)
            session.commit()
            app.logger.info(f"Created new AI insight response: {response.response_id}")
            return response
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating AI insight response: {e}")
            return None

    @classmethod
    def get_active_insights(cls, session, category=None, company_id=None, limit=50):
        """Get active insights, optionally filtered by category."""
        try:
            # Join with request to filter by company
            query = session.query(cls).join(AIInsightRequest)
            
            # Filter conditions
            query = query.filter(
                cls.is_active == True,
                cls.is_archived == False,
                db.or_(cls.expires_at.is_(None), cls.expires_at > datetime.utcnow())
            )
            
            if category:
                query = query.filter(cls.category == category)
            
            if company_id:
                query = query.filter(AIInsightRequest.company_id == company_id)
            
            return query.order_by(
                cls.is_featured.desc(),
                cls.priority.desc(),
                cls.created_at.desc()
            ).limit(limit).all()
        except Exception as e:
            app.logger.error(f"Error getting active insights: {e}")
            return []

    @classmethod
    def get_cached_insight(cls, session, cache_key):
        """Get a cached insight if it exists and is still valid."""
        try:
            insight = session.query(cls).filter(
                cls.cache_key == cache_key,
                cls.is_active == True,
                cls.is_cached == True,
                db.or_(cls.expires_at.is_(None), cls.expires_at > datetime.utcnow())
            ).first()
            
            return insight
        except Exception as e:
            app.logger.error(f"Error getting cached insight: {e}")
            return None

    def record_view(self, session, user_id=None):
        """Record that this insight was viewed."""
        try:
            self.view_count += 1
            self.last_viewed = datetime.utcnow()
            
            if user_id:
                # Track unique viewers
                unique_viewers = json.loads(self.unique_viewers) if self.unique_viewers else []
                if str(user_id) not in unique_viewers:
                    unique_viewers.append(str(user_id))
                    self.unique_viewers = json.dumps(unique_viewers)
            
            session.commit()
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error recording insight view: {e}")

    def add_user_rating(self, session, rating, comment=None):
        """Add user rating and feedback."""
        try:
            # Update average rating
            if self.user_rating is None:
                self.user_rating = rating
            else:
                total_rating = (self.user_rating * self.rating_count) + rating
                self.rating_count += 1
                self.user_rating = total_rating / self.rating_count
            
            if self.rating_count == 0:
                self.rating_count = 1
            
            # Add comment if provided
            if comment:
                feedback_comments = json.loads(self.feedback_comments) if self.feedback_comments else []
                feedback_comments.append({
                    "comment": comment,
                    "rating": rating,
                    "timestamp": datetime.utcnow().isoformat()
                })
                self.feedback_comments = json.dumps(feedback_comments)
            
            session.commit()
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error adding user rating: {e}")

    def record_action(self, session, action_type, action_details=None):
        """Record that an action was taken based on this insight."""
        try:
            actions_taken = json.loads(self.actions_taken) if self.actions_taken else []
            actions_taken.append({
                "action_type": action_type,
                "action_details": action_details,
                "timestamp": datetime.utcnow().isoformat()
            })
            self.actions_taken = json.dumps(actions_taken)
            self.action_count += 1
            
            session.commit()
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error recording action: {e}")

    def is_expired(self):
        """Check if this insight has expired."""
        if not self.expires_at:
            return False
        return datetime.utcnow() > self.expires_at
