from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUID
from flask import current_app as app
from datetime import datetime
import json

class AIInsightTemplate(db.Model):
    """Model for storing reusable AI prompt templates for different insight types"""
    __tablename__ = 'ai_insight_templates'

    template_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    name = db.Column(db.String(255), nullable=False)  # "Daily Attendance Summary"
    category = db.Column(db.String(100), nullable=False)  # attendance, payroll, performance, executive
    insight_type = db.Column(db.String(100), nullable=False)  # daily_summary, trend_analysis, recommendations
    
    # Template content
    prompt_template = db.Column(db.Text, nullable=False)  # The actual prompt with variables
    system_prompt = db.Column(db.Text, nullable=True)  # System/context prompt
    variables = db.Column(db.Text, nullable=True)  # JSON array of variable definitions
    
    # Configuration
    target_audience = db.Column(db.String(100), nullable=True)  # executives, managers, hr, employees
    output_format = db.Column(db.String(50), default='narrative')  # narrative, bullet_points, structured
    max_tokens = db.Column(db.Integer, default=1000)
    temperature = db.Column(db.Numeric(3, 2), default=0.7)  # AI creativity level
    
    # Usage and performance
    usage_count = db.Column(db.Integer, default=0)
    average_rating = db.Column(db.Numeric(3, 2), default=0.0)  # User feedback rating
    last_used = db.Column(db.DateTime, nullable=True)
    
    # Status
    is_active = db.Column(db.Boolean, default=True)
    is_default = db.Column(db.Boolean, default=False)
    is_system_template = db.Column(db.Boolean, default=True)  # System vs custom templates
    
    # Metadata
    created_by = db.Column(UUID(as_uuid=True), nullable=True)  # User who created custom template
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    def __str__(self):
        return f"AIInsightTemplate [template_id={self.template_id}, name={self.name}, category={self.category}]"

    def to_dict(self):
        return {
            "template_id": str(self.template_id),
            "name": self.name,
            "category": self.category,
            "insight_type": self.insight_type,
            "prompt_template": self.prompt_template,
            "system_prompt": self.system_prompt,
            "variables": json.loads(self.variables) if self.variables else [],
            "target_audience": self.target_audience,
            "output_format": self.output_format,
            "max_tokens": self.max_tokens,
            "temperature": float(self.temperature) if self.temperature else 0.7,
            "usage_count": self.usage_count,
            "average_rating": float(self.average_rating) if self.average_rating else 0.0,
            "last_used": self.last_used.strftime('%Y-%m-%d %H:%M:%S') if self.last_used else None,
            "is_active": self.is_active,
            "is_default": self.is_default,
            "is_system_template": self.is_system_template,
            "created_by": str(self.created_by) if self.created_by else None,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

    @classmethod
    def get_templates_by_category(cls, session, category, active_only=True):
        """Get all templates for a specific category."""
        try:
            query = session.query(cls).filter(cls.category == category)
            
            if active_only:
                query = query.filter(cls.is_active == True)
            
            return query.order_by(cls.is_default.desc(), cls.usage_count.desc()).all()
        except Exception as e:
            app.logger.error(f"Error getting templates by category {category}: {e}")
            return []

    @classmethod
    def get_default_template(cls, session, category, insight_type):
        """Get the default template for a specific category and insight type."""
        try:
            template = session.query(cls).filter(
                cls.category == category,
                cls.insight_type == insight_type,
                cls.is_active == True,
                cls.is_default == True
            ).first()
            
            if not template:
                # Fallback to any active template of the type
                template = session.query(cls).filter(
                    cls.category == category,
                    cls.insight_type == insight_type,
                    cls.is_active == True
                ).first()
            
            return template
        except Exception as e:
            app.logger.error(f"Error getting default template for {category}/{insight_type}: {e}")
            return None

    @classmethod
    def create_template(cls, session, **kwargs):
        """Create a new AI insight template."""
        try:
            # If this is set as default, unset other defaults of the same category/type
            if kwargs.get('is_default', False):
                category = kwargs.get('category')
                insight_type = kwargs.get('insight_type')
                session.query(cls).filter(
                    cls.category == category,
                    cls.insight_type == insight_type,
                    cls.is_default == True
                ).update({'is_default': False})

            template = cls(**kwargs)
            session.add(template)
            session.commit()
            app.logger.info(f"Created new AI insight template: {template.name}")
            return template
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating AI insight template: {e}")
            return None

    @classmethod
    def update_usage_stats(cls, session, template_id, rating=None):
        """Update template usage statistics."""
        try:
            template = session.query(cls).filter_by(template_id=template_id).first()
            if not template:
                return None

            # Update usage count and last used
            template.usage_count += 1
            template.last_used = datetime.now()
            
            # Update average rating if provided
            if rating is not None:
                if template.average_rating == 0:
                    template.average_rating = rating
                else:
                    # Simple moving average (could be improved with proper rating tracking)
                    template.average_rating = (template.average_rating + rating) / 2

            session.commit()
            return template
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error updating template usage stats: {e}")
            return None

    def render_prompt(self, variables_dict):
        """Render the prompt template with provided variables."""
        try:
            # Simple variable substitution (could be enhanced with Jinja2)
            rendered_prompt = self.prompt_template
            
            for key, value in variables_dict.items():
                placeholder = f"{{{key}}}"
                rendered_prompt = rendered_prompt.replace(placeholder, str(value))
            
            return rendered_prompt
        except Exception as e:
            app.logger.error(f"Error rendering prompt template: {e}")
            return self.prompt_template
