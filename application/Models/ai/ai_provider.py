from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUID
from flask import current_app as app
from datetime import datetime
import json

class AIProvider(db.Model):
    """Model for managing different AI providers (Gemini, OpenAI, Claude, etc.)"""
    __tablename__ = 'ai_providers'

    provider_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    name = db.Column(db.String(100), nullable=False, unique=True)  # gemini, openai, claude, etc.
    display_name = db.Column(db.String(255), nullable=False)  # "Google Gemini Flash 2.0"
    provider_type = db.Column(db.String(50), nullable=False)  # text_generation, image_analysis, etc.
    
    # Configuration
    api_endpoint = db.Column(db.String(500), nullable=True)
    api_version = db.Column(db.String(50), nullable=True)
    model_name = db.Column(db.String(100), nullable=False)  # gemini-2.0-flash-exp
    
    # Capabilities
    max_tokens = db.Column(db.Integer, default=8192)
    supports_streaming = db.Column(db.Boolean, default=False)
    supports_function_calling = db.Column(db.Boolean, default=False)
    supports_vision = db.Column(db.Boolean, default=False)
    
    # Rate limiting and costs
    requests_per_minute = db.Column(db.Integer, default=60)
    requests_per_day = db.Column(db.Integer, default=1500)
    cost_per_1k_tokens = db.Column(db.Numeric(10, 6), default=0.0)  # For cost tracking
    
    # Status and configuration
    is_active = db.Column(db.Boolean, default=True)
    is_default = db.Column(db.Boolean, default=False)
    configuration = db.Column(db.Text, nullable=True)  # JSON config for provider-specific settings
    
    # Metadata
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    def __str__(self):
        return f"AIProvider [provider_id={self.provider_id}, name={self.name}, model={self.model_name}]"

    def to_dict(self):
        return {
            "provider_id": str(self.provider_id),
            "name": self.name,
            "display_name": self.display_name,
            "provider_type": self.provider_type,
            "model_name": self.model_name,
            "max_tokens": self.max_tokens,
            "supports_streaming": self.supports_streaming,
            "supports_function_calling": self.supports_function_calling,
            "supports_vision": self.supports_vision,
            "requests_per_minute": self.requests_per_minute,
            "requests_per_day": self.requests_per_day,
            "cost_per_1k_tokens": float(self.cost_per_1k_tokens) if self.cost_per_1k_tokens else 0.0,
            "is_active": self.is_active,
            "is_default": self.is_default,
            "configuration": json.loads(self.configuration) if self.configuration else {},
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

    @classmethod
    def get_default_provider(cls, session, provider_type='text_generation'):
        """Get the default AI provider for a specific type."""
        try:
            provider = session.query(cls).filter(
                cls.provider_type == provider_type,
                cls.is_active == True,
                cls.is_default == True
            ).first()
            
            if not provider:
                # Fallback to any active provider of the type
                provider = session.query(cls).filter(
                    cls.provider_type == provider_type,
                    cls.is_active == True
                ).first()
            
            return provider
        except Exception as e:
            app.logger.error(f"Error getting default AI provider: {e}")
            return None

    @classmethod
    def get_provider_by_name(cls, session, name):
        """Get AI provider by name."""
        try:
            return session.query(cls).filter(
                cls.name == name,
                cls.is_active == True
            ).first()
        except Exception as e:
            app.logger.error(f"Error getting AI provider by name {name}: {e}")
            return None

    @classmethod
    def create_provider(cls, session, **kwargs):
        """Create a new AI provider."""
        try:
            # If this is set as default, unset other defaults of the same type
            if kwargs.get('is_default', False):
                provider_type = kwargs.get('provider_type')
                session.query(cls).filter(
                    cls.provider_type == provider_type,
                    cls.is_default == True
                ).update({'is_default': False})

            provider = cls(**kwargs)
            session.add(provider)
            session.commit()
            app.logger.info(f"Created new AI provider: {provider.name}")
            return provider
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating AI provider: {e}")
            return None

    @classmethod
    def update_provider(cls, session, provider_id, **kwargs):
        """Update an AI provider."""
        try:
            provider = session.query(cls).filter_by(provider_id=provider_id).first()
            if not provider:
                return None

            # If setting as default, unset other defaults of the same type
            if kwargs.get('is_default', False):
                session.query(cls).filter(
                    cls.provider_type == provider.provider_type,
                    cls.is_default == True,
                    cls.provider_id != provider_id
                ).update({'is_default': False})

            # Update provider attributes
            for key, value in kwargs.items():
                if hasattr(provider, key):
                    setattr(provider, key, value)

            session.commit()
            app.logger.info(f"Updated AI provider: {provider.name}")
            return provider
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error updating AI provider: {e}")
            return None
