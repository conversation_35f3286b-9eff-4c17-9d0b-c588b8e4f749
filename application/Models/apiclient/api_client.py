from application.database import central_db as db
import uuid
from sqlalchemy.dialects.postgresql import UUID
from flask import current_app as app
import bcrypt
from application.Helpers.apitoken import APIToken as TokenManager
from datetime import datetime
import secrets

class APIClient(db.Model):
    __tablename__ = "api_clients"

    client_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    name = db.Column(db.String(255), nullable=False)  # Client name for identification
    client_secret_hash = db.Column(db.String(255), nullable=False)  # hashed secret
    company_id = db.<PERSON>umn(db.String(36), db.<PERSON><PERSON>ey("companies.company_id"), nullable=False)
    is_active = db.Column(db.Bo<PERSON>, default=True, nullable=False)
    created_at = db.Column(db.DateTime, server_default=db.func.now(), nullable=False)
    last_used_at = db.Column(db.DateTime)

    # relationships
    company = db.relationship("Company", back_populates="api_clients")

    def verify_secret(self, secret: str) -> bool:
        """Verify client secret against stored hash"""
        return bcrypt.checkpw(secret.encode("utf-8"), self.client_secret_hash.encode("utf-8"))

    def update_last_used(self):
        self.last_used_at = datetime.utcnow()
        db.session.commit()

    def generate_jwt(self, expiry_minutes=60):
        payload = {
            "client_id": str(self.client_id),
            "company_id": str(self.company_id),
            "name": self.name
        }
        return TokenManager.generate(payload, expiry_minutes=expiry_minutes)

    @classmethod
    def authenticate(cls, client_id: str, client_secret: str):
        """
        Authenticate client_id + secret, update last_used, and return JWT.
        Raises ValueError on failure.
        """
        client = db.session.query(cls).filter_by(client_id=client_id, is_active=True).first()
        if not client:
            raise ValueError("Invalid client_id")
        if not client.verify_secret(client_secret):
            raise ValueError("Invalid client_secret")
        client.update_last_used()
        token = client.generate_jwt()
        return token
    
    @classmethod
    def create_client(cls, company_id: uuid.UUID, name: str, secret: str) -> "APIClient":
        """Create a new API client with hashed secret"""
        try:
            hashed_secret = bcrypt.hashpw(secret.encode("utf-8"), bcrypt.gensalt()).decode("utf-8")
            client = cls(company_id=company_id, name=name, client_secret_hash=hashed_secret)
            db.session.add(client)
            db.session.commit()
            app.logger.info(f"Created new API client: {name} for company {company_id}")
            return client
        except Exception as e:
            db.session.rollback()
            app.logger.error(f"Error creating API client: {e}")
            raise

    # Add this method to application/Models/apiclient/api_client.py

    @classmethod
    def create_client_auto(cls, company_id: uuid.UUID, name: str, created_by_user_id: str = None) -> tuple["APIClient", str]:
        """
        Create API client with automatically generated secure credentials.
        
        Args:
            company_id: UUID of the company this client belongs to
            name: Human-readable name for the API client
            created_by_user_id: Optional UUID of the user who created this client
        
        Returns:
            tuple: (APIClient object, plaintext client_secret)
            
        Raises:
            Exception: If client creation fails
        """
        
        try:
            # Generate cryptographically secure client secret
            client_secret = secrets.token_urlsafe(32)
            
            # Enhance name with creation context
            enhanced_name = f"{name}"
            if created_by_user_id:
                enhanced_name += f" (Created by user: {created_by_user_id})"
            
            # Create client using existing method (handles UUID generation and hashing)
            client = cls.create_client(
                company_id=company_id,
                name=enhanced_name,
                secret=client_secret
            )
            
            app.logger.info(f"Auto-created API client: {client.client_id} for company {company_id}")
            
            # Return both client object and plaintext secret (for one-time display)
            return client, client_secret
            
        except Exception as e:
            app.logger.error(f"Failed to auto-create API client for company {company_id}: {str(e)}")
            raise Exception(f"Failed to create API client: {str(e)}")

    @classmethod
    def reset_client_secret(cls, client_id: str) -> str:
        """
        Reset the client secret for an existing API client.

        Args:
            client_id: UUID string of the client to reset

        Returns:
            str: New plaintext client secret

        Raises:
            ValueError: If client not found or inactive
            Exception: If reset operation fails
        """
        try:
            # Find the active client
            client = db.session.query(cls).filter_by(client_id=client_id, is_active=True).first()
            if not client:
                raise ValueError(f"Active API client not found: {client_id}")

            # Generate new cryptographically secure secret
            new_secret = secrets.token_urlsafe(32)

            # Hash and update the secret
            hashed_secret = bcrypt.hashpw(new_secret.encode("utf-8"), bcrypt.gensalt()).decode("utf-8")
            client.client_secret_hash = hashed_secret

            # Update timestamp for audit purposes
            client.last_used_at = datetime.utcnow()

            # Commit the changes
            db.session.commit()

            app.logger.info(f"Successfully reset secret for API client: {client_id} ({client.name})")

            # Return the new plaintext secret (for one-time display)
            return new_secret

        except ValueError:
            # Re-raise validation errors as-is
            raise
        except Exception as e:
            db.session.rollback()
            app.logger.error(f"Failed to reset secret for API client {client_id}: {str(e)}")
            raise Exception(f"Failed to reset client secret: {str(e)}")
            