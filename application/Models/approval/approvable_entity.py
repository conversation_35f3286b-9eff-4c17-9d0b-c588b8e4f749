from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUID
from flask import current_app as app
from datetime import datetime

class ApprovableEntity(db.Model):
    """Model representing types of entities that can be approved."""
    __tablename__ = 'approvable_entities'

    entity_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    name = db.Column(db.String(255), nullable=False)  # e.g., "Leave Request", "Expense Claim"
    code = db.Column(db.String(50), nullable=True, unique=True)  # e.g., "leave_request"
    description = db.Column(db.Text, nullable=True)
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())
    
    def __str__(self):
        """Return a string representation of the object."""
        return f"ApprovableEntity [entity_id={self.entity_id}, name={self.name}, code={self.code}]"
    
    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "entity_id": str(self.entity_id),
            "name": self.name,
            "code": self.code,
            "description": self.description,
            "is_active": self.is_active,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }
    
    @classmethod
    def get_entity_by_id(cls, session, entity_id):
        """Get an approvable entity by ID."""
        return session.query(cls).filter_by(entity_id=entity_id).first()
    
    @classmethod
    def get_entity_by_code(cls, session, code):
        """Get an approvable entity by code."""
        return session.query(cls).filter_by(code=code).first()
    
    @classmethod
    def get_all_entities(cls, session, active_only=True):
        """Get all approvable entities."""
        query = session.query(cls)
        if active_only:
            query = query.filter_by(is_active=True)
        return query.all()
    
    @classmethod
    def create_entity(cls, session, **kwargs):
        """Create a new approvable entity."""
        try:
            entity = cls(**kwargs)
            session.add(entity)
            session.commit()
            app.logger.info(f"Created new approvable entity: {entity.name}")
            return entity
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating approvable entity: {e}")
            return None
    
    @classmethod
    def update_entity(cls, session, entity_id, **kwargs):
        """Update an approvable entity."""
        try:
            entity = cls.get_entity_by_id(session, entity_id)
            if not entity:
                app.logger.error(f"Approvable entity with ID {entity_id} not found")
                return None
            
            for key, value in kwargs.items():
                setattr(entity, key, value)
            
            entity.updated_at = datetime.now()
            session.commit()
            app.logger.info(f"Updated approvable entity: {entity.name}")
            return entity
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error updating approvable entity: {e}")
            return None
    
    @classmethod
    def delete_entity(cls, session, entity_id):
        """Delete an approvable entity."""
        try:
            entity = cls.get_entity_by_id(session, entity_id)
            if not entity:
                app.logger.error(f"Approvable entity with ID {entity_id} not found")
                return False
            
            # Instead of deleting, mark as inactive
            entity.is_active = False
            entity.updated_at = datetime.now()
            session.commit()
            app.logger.info(f"Marked approvable entity as inactive: {entity.name}")
            return True
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error deleting approvable entity: {e}")
            return False
