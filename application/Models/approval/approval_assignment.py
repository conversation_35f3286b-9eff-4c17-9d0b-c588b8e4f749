from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUID
from flask import current_app as app
from datetime import datetime

class ApprovalAssignment(db.Model):
    """Model representing assignments of approvers to specific levels."""
    __tablename__ = 'approval_assignments'

    assignment_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    level_id = db.Column(UUID(as_uuid=True), db.<PERSON>ey('approval_levels.level_id'), nullable=False)
    approver_id = db.Column(UUID(as_uuid=True), nullable=False)  # User ID who can approve at this level
    entity_id = db.Column(UUID(as_uuid=True), db.ForeignKey('approvable_entities.entity_id'), nullable=True)  # Optional: specific to entity type
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())
    
    # Relationships
    level = db.relationship('ApprovalLevel', backref=db.backref('assignments', lazy='dynamic'))
    entity = db.relationship('ApprovableEntity', backref=db.backref('assignments', lazy='dynamic'))
    
    def __str__(self):
        """Return a string representation of the object."""
        return f"ApprovalAssignment [assignment_id={self.assignment_id}, level={self.level.name if self.level else None}, approver_id={self.approver_id}]"
    
    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "assignment_id": str(self.assignment_id),
            "level_id": str(self.level_id),
            "level_name": self.level.name if self.level else None,
            "approver_id": str(self.approver_id),
            "entity_id": str(self.entity_id) if self.entity_id else None,
            "entity_name": self.entity.name if self.entity else None,
            "is_active": self.is_active,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }
    
    @classmethod
    def get_assignment_by_id(cls, session, assignment_id):
        """Get an approval assignment by ID."""
        return session.query(cls).filter_by(assignment_id=assignment_id).first()
    
    @classmethod
    def get_assignments_by_level(cls, session, level_id, active_only=True):
        """Get all approval assignments for a specific level."""
        query = session.query(cls).filter_by(level_id=level_id)
        if active_only:
            query = query.filter_by(is_active=True)
        return query.all()
    
    @classmethod
    def get_assignments_by_approver(cls, session, approver_id, active_only=True):
        """Get all approval assignments for a specific approver."""
        query = session.query(cls).filter_by(approver_id=approver_id)
        if active_only:
            query = query.filter_by(is_active=True)
        return query.all()
    
    @classmethod
    def get_assignments_by_entity(cls, session, entity_id, active_only=True):
        """Get all approval assignments for a specific entity type."""
        query = session.query(cls).filter_by(entity_id=entity_id)
        if active_only:
            query = query.filter_by(is_active=True)
        return query.all()
    
    @classmethod
    def can_approve(cls, session, approver_id, level_id, entity_id=None):
        """Check if a user can approve at a specific level for a specific entity type."""
        query = session.query(cls).filter_by(
            approver_id=approver_id,
            level_id=level_id,
            is_active=True
        )
        
        # Either the assignment is for all entities (entity_id is None)
        # or it's specifically for this entity type
        query = query.filter(
            db.or_(
                cls.entity_id == None,
                cls.entity_id == entity_id
            )
        )
        
        return query.first() is not None
    
    @classmethod
    def create_assignment(cls, session, **kwargs):
        """Create a new approval assignment."""
        try:
            # Check if an assignment already exists for this approver, level, and entity
            existing_assignment = session.query(cls).filter_by(
                approver_id=kwargs.get('approver_id'),
                level_id=kwargs.get('level_id'),
                entity_id=kwargs.get('entity_id'),
                is_active=True
            ).first()
            
            if existing_assignment:
                app.logger.warning(f"An approval assignment already exists for this approver, level, and entity")
                return None, "An approval assignment already exists for this approver, level, and entity"
            
            assignment = cls(**kwargs)
            session.add(assignment)
            session.commit()
            app.logger.info(f"Created new approval assignment for approver ID: {assignment.approver_id}, level ID: {assignment.level_id}")
            return assignment, None
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating approval assignment: {e}")
            return None, str(e)
    
    @classmethod
    def update_assignment(cls, session, assignment_id, **kwargs):
        """Update an approval assignment."""
        try:
            assignment = cls.get_assignment_by_id(session, assignment_id)
            if not assignment:
                app.logger.error(f"Approval assignment with ID {assignment_id} not found")
                return None, "Approval assignment not found"
            
            for key, value in kwargs.items():
                setattr(assignment, key, value)
            
            assignment.updated_at = datetime.now()
            session.commit()
            app.logger.info(f"Updated approval assignment ID: {assignment_id}")
            return assignment, None
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error updating approval assignment: {e}")
            return None, str(e)
    
    @classmethod
    def delete_assignment(cls, session, assignment_id):
        """Delete an approval assignment."""
        try:
            assignment = cls.get_assignment_by_id(session, assignment_id)
            if not assignment:
                app.logger.error(f"Approval assignment with ID {assignment_id} not found")
                return False, "Approval assignment not found"
            
            # Instead of deleting, mark as inactive
            assignment.is_active = False
            assignment.updated_at = datetime.now()
            session.commit()
            app.logger.info(f"Marked approval assignment as inactive: {assignment_id}")
            return True, None
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error deleting approval assignment: {e}")
            return False, str(e)
