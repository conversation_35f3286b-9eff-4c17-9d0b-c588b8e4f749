from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUID
from flask import current_app as app
from datetime import datetime

class ApprovalFlow(db.Model):
    """Model representing approval flows (which levels are required for which entity types)."""
    __tablename__ = 'approval_flows'

    flow_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    entity_id = db.Column(UUID(as_uuid=True), db.ForeignKey('approvable_entities.entity_id'), nullable=False)
    level_id = db.Column(UUID(as_uuid=True), db.Foreign<PERSON>ey('approval_levels.level_id'), nullable=False)
    order = db.Column(db.Integer, nullable=False)  # Order in the approval chain
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())
    
    # Relationships
    entity = db.relationship('ApprovableEntity', backref=db.backref('flows', lazy='dynamic'))
    level = db.relationship('ApprovalLevel', backref=db.backref('flows', lazy='dynamic'))
    
    def __str__(self):
        """Return a string representation of the object."""
        return f"ApprovalFlow [flow_id={self.flow_id}, entity={self.entity.name if self.entity else None}, level={self.level.name if self.level else None}, order={self.order}]"
    
    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "flow_id": str(self.flow_id),
            "entity_id": str(self.entity_id),
            "entity_name": self.entity.name if self.entity else None,
            "level_id": str(self.level_id),
            "level_name": self.level.name if self.level else None,
            "order": self.order,
            "is_active": self.is_active,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }
    
    @classmethod
    def get_flow_by_id(cls, session, flow_id):
        """Get an approval flow by ID."""
        return session.query(cls).filter_by(flow_id=flow_id).first()
    
    @classmethod
    def get_flows_by_entity(cls, session, entity_id, active_only=True):
        """Get all approval flows for a specific entity type, ordered by their order."""
        query = session.query(cls).filter_by(entity_id=entity_id)
        if active_only:
            query = query.filter_by(is_active=True)
        return query.order_by(cls.order).all()
    
    @classmethod
    def get_next_flow(cls, session, entity_id, current_order, active_only=True):
        """Get the next approval flow in the sequence for a specific entity type."""
        query = session.query(cls).filter_by(entity_id=entity_id)
        if active_only:
            query = query.filter_by(is_active=True)
        return query.filter(cls.order > current_order).order_by(cls.order).first()
    
    @classmethod
    def get_first_flow(cls, session, entity_id, active_only=True):
        """Get the first approval flow in the sequence for a specific entity type."""
        query = session.query(cls).filter_by(entity_id=entity_id)
        if active_only:
            query = query.filter_by(is_active=True)
        return query.order_by(cls.order).first()
    
    @classmethod
    def create_flow(cls, session, **kwargs):
        """Create a new approval flow."""
        try:
            # Check if a flow with the same entity, level, and order already exists
            existing_flow = session.query(cls).filter_by(
                entity_id=kwargs.get('entity_id'),
                order=kwargs.get('order'),
                is_active=True
            ).first()
            
            if existing_flow:
                app.logger.warning(f"An approval flow with the same entity and order already exists")
                return None, "An approval flow with the same entity and order already exists"
            
            flow = cls(**kwargs)
            session.add(flow)
            session.commit()
            app.logger.info(f"Created new approval flow for entity ID: {flow.entity_id}, level ID: {flow.level_id}")
            return flow, None
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating approval flow: {e}")
            return None, str(e)
    
    @classmethod
    def update_flow(cls, session, flow_id, **kwargs):
        """Update an approval flow."""
        try:
            flow = cls.get_flow_by_id(session, flow_id)
            if not flow:
                app.logger.error(f"Approval flow with ID {flow_id} not found")
                return None, "Approval flow not found"
            
            # If changing the order, check if another flow already has that order
            if 'order' in kwargs and kwargs['order'] != flow.order:
                existing_flow = session.query(cls).filter_by(
                    entity_id=flow.entity_id,
                    order=kwargs['order'],
                    is_active=True
                ).first()
                
                if existing_flow and existing_flow.flow_id != flow_id:
                    app.logger.warning(f"An approval flow with the same entity and order already exists")
                    return None, "An approval flow with the same entity and order already exists"
            
            for key, value in kwargs.items():
                setattr(flow, key, value)
            
            flow.updated_at = datetime.now()
            session.commit()
            app.logger.info(f"Updated approval flow ID: {flow_id}")
            return flow, None
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error updating approval flow: {e}")
            return None, str(e)
    
    @classmethod
    def delete_flow(cls, session, flow_id):
        """Delete an approval flow."""
        try:
            flow = cls.get_flow_by_id(session, flow_id)
            if not flow:
                app.logger.error(f"Approval flow with ID {flow_id} not found")
                return False, "Approval flow not found"
            
            # Instead of deleting, mark as inactive
            flow.is_active = False
            flow.updated_at = datetime.now()
            session.commit()
            app.logger.info(f"Marked approval flow as inactive: {flow_id}")
            return True, None
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error deleting approval flow: {e}")
            return False, str(e)
