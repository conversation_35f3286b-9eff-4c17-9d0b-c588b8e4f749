from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUID
from flask import current_app as app
from datetime import datetime

class ApprovalLevel(db.Model):
    """Model representing approval levels in a workflow."""
    __tablename__ = 'approval_levels'

    level_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    name = db.Column(db.String(255), nullable=False)  # e.g., Supervisor, Department Head, HR
    description = db.Column(db.Text, nullable=True)
    is_active = db.Column(db.<PERSON><PERSON>an, default=True, nullable=False)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())
    
    def __str__(self):
        """Return a string representation of the object."""
        return f"ApprovalLevel [level_id={self.level_id}, name={self.name}]"
    
    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "level_id": str(self.level_id),
            "name": self.name,
            "description": self.description,
            "is_active": self.is_active,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }
    
    @classmethod
    def get_level_by_id(cls, session, level_id):
        """Get an approval level by ID."""
        return session.query(cls).filter_by(level_id=level_id).first()
    
    @classmethod
    def get_all_levels(cls, session, active_only=True):
        """Get all approval levels."""
        query = session.query(cls)
        if active_only:
            query = query.filter_by(is_active=True)
        return query.all()
    
    @classmethod
    def create_level(cls, session, **kwargs):
        """Create a new approval level."""
        try:
            level = cls(**kwargs)
            session.add(level)
            session.commit()
            app.logger.info(f"Created new approval level: {level.name}")
            return level
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating approval level: {e}")
            return None
    
    @classmethod
    def update_level(cls, session, level_id, **kwargs):
        """Update an approval level."""
        try:
            level = cls.get_level_by_id(session, level_id)
            if not level:
                app.logger.error(f"Approval level with ID {level_id} not found")
                return None
            
            for key, value in kwargs.items():
                setattr(level, key, value)
            
            level.updated_at = datetime.now()
            session.commit()
            app.logger.info(f"Updated approval level: {level.name}")
            return level
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error updating approval level: {e}")
            return None
    
    @classmethod
    def delete_level(cls, session, level_id):
        """Delete an approval level."""
        try:
            level = cls.get_level_by_id(session, level_id)
            if not level:
                app.logger.error(f"Approval level with ID {level_id} not found")
                return False
            
            # Instead of deleting, mark as inactive
            level.is_active = False
            level.updated_at = datetime.now()
            session.commit()
            app.logger.info(f"Marked approval level as inactive: {level.name}")
            return True
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error deleting approval level: {e}")
            return False
