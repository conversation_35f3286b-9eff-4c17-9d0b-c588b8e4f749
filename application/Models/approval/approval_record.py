from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUI<PERSON>
from flask import current_app as app
from datetime import datetime

class ApprovalRecord(db.Model):
    """Model representing individual approval records in a workflow."""
    __tablename__ = 'approval_records'

    record_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    workflow_id = db.Column(UUID(as_uuid=True), db.<PERSON>ey('approval_workflows.workflow_id'), nullable=False)
    flow_id = db.Column(UUID(as_uuid=True), db.<PERSON>ey('approval_flows.flow_id'), nullable=False)
    approver_id = db.Column(UUID(as_uuid=True), nullable=False)  # User ID who approved/rejected
    status = db.Column(db.String(50), nullable=False)  # approved, rejected
    comments = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    
    # Relationships
    workflow = db.relationship('ApprovalWorkflow', backref=db.backref('records', lazy='dynamic'))
    flow = db.relationship('ApprovalFlow', backref=db.backref('records', lazy='dynamic'))
    
    def __str__(self):
        """Return a string representation of the object."""
        return f"ApprovalRecord [record_id={self.record_id}, workflow_id={self.workflow_id}, status={self.status}]"
    
    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "record_id": str(self.record_id),
            "workflow_id": str(self.workflow_id),
            "flow_id": str(self.flow_id),
            "flow": self.flow.to_dict() if self.flow else None,
            "approver_id": str(self.approver_id),
            "status": self.status,
            "comments": self.comments,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None
        }
    
    @classmethod
    def get_record_by_id(cls, session, record_id):
        """Get an approval record by ID."""
        return session.query(cls).filter_by(record_id=record_id).first()
    
    @classmethod
    def get_records_by_workflow(cls, session, workflow_id):
        """Get all approval records for a workflow."""
        return session.query(cls).filter_by(workflow_id=workflow_id).order_by(cls.created_at).all()
    
    @classmethod
    def get_records_by_approver(cls, session, approver_id):
        """Get all approval records for a specific approver."""
        return session.query(cls).filter_by(approver_id=approver_id).order_by(cls.created_at.desc()).all()
    
    @classmethod
    def create_record(cls, session, **kwargs):
        """Create a new approval record."""
        try:
            record = cls(**kwargs)
            session.add(record)
            session.commit()
            app.logger.info(f"Created new approval record for workflow ID: {record.workflow_id}")
            return record
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating approval record: {e}")
            return None
