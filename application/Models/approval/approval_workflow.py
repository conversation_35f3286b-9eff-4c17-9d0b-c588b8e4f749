from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUI<PERSON>
from flask import current_app as app
from datetime import datetime

class ApprovalWorkflow(db.Model):
    """Model representing approval workflows for specific entities."""
    __tablename__ = 'approval_workflows'

    workflow_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    entity_type = db.Column(db.String(50), nullable=False)  # e.g., "leave_request"
    entity_id = db.Column(UUID(as_uuid=True), nullable=False)  # ID of the entity being approved
    current_flow_id = db.Column(UUID(as_uuid=True), db.<PERSON>ey('approval_flows.flow_id'), nullable=True)
    status = db.Column(db.String(50), default='pending', nullable=False)  # pending, approved, rejected
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())
    
    # Relationships
    current_flow = db.relationship('ApprovalFlow', backref=db.backref('workflows', lazy='dynamic'))
    
    def __str__(self):
        """Return a string representation of the object."""
        return f"ApprovalWorkflow [workflow_id={self.workflow_id}, entity_type={self.entity_type}, entity_id={self.entity_id}, status={self.status}]"
    
    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "workflow_id": str(self.workflow_id),
            "entity_type": self.entity_type,
            "entity_id": str(self.entity_id),
            "current_flow_id": str(self.current_flow_id) if self.current_flow_id else None,
            "current_flow": self.current_flow.to_dict() if self.current_flow else None,
            "status": self.status,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }
    
    @classmethod
    def get_workflow_by_id(cls, session, workflow_id):
        """Get an approval workflow by ID."""
        return session.query(cls).filter_by(workflow_id=workflow_id).first()
    
    @classmethod
    def get_workflow_by_entity(cls, session, entity_type, entity_id):
        """Get an approval workflow for a specific entity."""
        return session.query(cls).filter_by(entity_type=entity_type, entity_id=entity_id).first()
    
    @classmethod
    def get_workflows_by_status(cls, session, status):
        """Get all approval workflows with a specific status."""
        return session.query(cls).filter_by(status=status).all()
    
    @classmethod
    def create_workflow(cls, session, entity_type, entity_id):
        """Create a new approval workflow for an entity."""
        try:
            # Check if a workflow already exists for this entity
            existing_workflow = cls.get_workflow_by_entity(session, entity_type, entity_id)
            if existing_workflow:
                app.logger.warning(f"An approval workflow already exists for this entity: {entity_type} {entity_id}")
                return existing_workflow, "An approval workflow already exists for this entity"
            
            # Get the approvable entity by code
            from application.Models.approval.approvable_entity import ApprovableEntity
            entity = ApprovableEntity.get_entity_by_code(session, entity_type)
            if not entity:
                app.logger.error(f"Approvable entity with code {entity_type} not found")
                return None, f"Approvable entity with code {entity_type} not found"
            
            # Get the first approval flow for this entity type
            from application.Models.approval.approval_flow import ApprovalFlow
            first_flow = ApprovalFlow.get_first_flow(session, entity.entity_id)
            if not first_flow:
                app.logger.warning(f"No approval flows defined for entity type: {entity_type}")
                # Create the workflow without a current flow
                workflow = cls(
                    entity_type=entity_type,
                    entity_id=entity_id,
                    status='approved'  # Auto-approve if no flows defined
                )
                session.add(workflow)
                session.commit()
                return workflow, "No approval flows defined, auto-approved"
            
            # Create the workflow with the first flow
            workflow = cls(
                entity_type=entity_type,
                entity_id=entity_id,
                current_flow_id=first_flow.flow_id,
                status='pending'
            )
            session.add(workflow)
            session.commit()
            
            app.logger.info(f"Created new approval workflow for {entity_type} ID: {entity_id}")
            return workflow, None
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating approval workflow: {e}")
            return None, str(e)
    
    @classmethod
    def approve_current_level(cls, session, workflow_id, approver_id, comments=None):
        """Approve the current level of an approval workflow and move to the next level."""
        try:
            workflow = cls.get_workflow_by_id(session, workflow_id)
            if not workflow:
                app.logger.error(f"Approval workflow with ID {workflow_id} not found")
                return None, "Approval workflow not found"
            
            if workflow.status != 'pending':
                app.logger.warning(f"Cannot approve workflow with ID {workflow_id} because it is not pending")
                return None, f"Cannot approve workflow because it is {workflow.status}"
            
            # Create an approval record
            from application.Models.approval.approval_record import ApprovalRecord
            record = ApprovalRecord(
                workflow_id=workflow_id,
                flow_id=workflow.current_flow_id,
                approver_id=approver_id,
                status='approved',
                comments=comments
            )
            session.add(record)
            
            # Get the next flow
            from application.Models.approval.approval_flow import ApprovalFlow
            from application.Models.approval.approvable_entity import ApprovableEntity
            
            entity = ApprovableEntity.get_entity_by_code(session, workflow.entity_type)
            if not entity:
                app.logger.error(f"Approvable entity with code {workflow.entity_type} not found")
                return None, f"Approvable entity with code {workflow.entity_type} not found"
            
            current_flow = ApprovalFlow.get_flow_by_id(session, workflow.current_flow_id)
            if not current_flow:
                app.logger.error(f"Current approval flow not found")
                return None, "Current approval flow not found"
            
            next_flow = ApprovalFlow.get_next_flow(session, entity.entity_id, current_flow.order)
            
            if next_flow:
                # Move to the next flow
                workflow.current_flow_id = next_flow.flow_id
                workflow.updated_at = datetime.now()
                session.commit()
                
                app.logger.info(f"Moved workflow ID {workflow_id} to next level: {next_flow.level.name if next_flow.level else None}")
                return workflow, None
            else:
                # This was the final level, approve the workflow
                workflow.status = 'approved'
                workflow.current_flow_id = None
                workflow.updated_at = datetime.now()
                session.commit()
                
                # Handle entity-specific approval logic
                cls._handle_entity_approval(session, workflow)
                
                app.logger.info(f"Workflow ID {workflow_id} completed and approved")
                return workflow, None
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error approving workflow: {e}")
            return None, str(e)
    
    @classmethod
    def reject_workflow(cls, session, workflow_id, approver_id, rejection_reason):
        """Reject an approval workflow at the current level."""
        try:
            workflow = cls.get_workflow_by_id(session, workflow_id)
            if not workflow:
                app.logger.error(f"Approval workflow with ID {workflow_id} not found")
                return None, "Approval workflow not found"
            
            if workflow.status != 'pending':
                app.logger.warning(f"Cannot reject workflow with ID {workflow_id} because it is not pending")
                return None, f"Cannot reject workflow because it is {workflow.status}"
            
            # Create a rejection record
            from application.Models.approval.approval_record import ApprovalRecord
            record = ApprovalRecord(
                workflow_id=workflow_id,
                flow_id=workflow.current_flow_id,
                approver_id=approver_id,
                status='rejected',
                comments=rejection_reason
            )
            session.add(record)
            
            # Update the workflow status
            workflow.status = 'rejected'
            workflow.updated_at = datetime.now()
            session.commit()
            
            # Handle entity-specific rejection logic
            cls._handle_entity_rejection(session, workflow, rejection_reason)
            
            app.logger.info(f"Workflow ID {workflow_id} rejected")
            return workflow, None
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error rejecting workflow: {e}")
            return None, str(e)
    
    @classmethod
    def _handle_entity_approval(cls, session, workflow):
        """Handle entity-specific approval logic."""
        entity_type = workflow.entity_type
        entity_id = workflow.entity_id
        
        if entity_type == 'leave_request':
            # Handle leave request approval
            from application.Models.employees.leave_request import LeaveRequest
            leave_request = LeaveRequest.get_request_by_id(session, entity_id)
            if leave_request:
                leave_request, error = LeaveRequest.approve_request(session, entity_id, None)
                if not leave_request:
                    app.logger.error(f"Error approving leave request: {error}")
        
        # Add more entity types as needed
    
    @classmethod
    def _handle_entity_rejection(cls, session, workflow, rejection_reason):
        """Handle entity-specific rejection logic."""
        entity_type = workflow.entity_type
        entity_id = workflow.entity_id
        
        if entity_type == 'leave_request':
            # Handle leave request rejection
            from application.Models.employees.leave_request import LeaveRequest
            leave_request = LeaveRequest.get_request_by_id(session, entity_id)
            if leave_request:
                leave_request, error = LeaveRequest.reject_request(session, entity_id, rejection_reason, None)
                if not leave_request:
                    app.logger.error(f"Error rejecting leave request: {error}")
        
        # Add more entity types as needed
