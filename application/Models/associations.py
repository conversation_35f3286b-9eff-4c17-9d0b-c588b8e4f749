"""
Module containing association tables for many-to-many relationships.
This module helps avoid circular imports between models.
"""

from application.database import central_db as db

# Association table for User and Company many-to-many relationship
user_company_association = db.Table(
    'user_company_association',
    db.<PERSON>umn('user_id', db.<PERSON>(36), db.<PERSON>('users.user_id'), primary_key=True),
    db.<PERSON>('company_id', db.String(36), db.<PERSON>('companies.company_id'), primary_key=True),
    db.<PERSON>('added_at', db.DateTime, server_default=db.func.now())
)
