"""
OTP Token Model for KaziSync HRMS
Stores email-based OTP tokens for secure authentication
"""

import uuid
from datetime import datetime, timedelta
from sqlalchemy.dialects.postgresql import UUID
from application.database import central_db as db


class OTPToken(db.Model):
    """Model for storing OTP tokens for email-based authentication"""
    __tablename__ = 'otp_tokens'
    
    # Primary key
    token_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    
    # Session and user identification
    session_id = db.Column(db.String(255), unique=True, nullable=False, index=True)
    user_id = db.Column(db.String(255), nullable=False, index=True)
    email = db.Column(db.String(255), nullable=False, index=True)
    
    # OTP details
    otp_code = db.Column(db.String(10), nullable=False)
    purpose = db.Column(db.String(50), nullable=False, default='login')  # login, password_reset, verification
    
    # Timing and expiry
    created_at = db.Column(db.DateTime, server_default=db.func.now(), nullable=False)
    expires_at = db.Column(db.DateTime, nullable=False)
    used_at = db.Column(db.DateTime, nullable=True)
    
    # Usage tracking
    attempts = db.Column(db.Integer, default=0, nullable=False)
    is_used = db.Column(db.Boolean, default=False, nullable=False)
    
    # Security tracking
    ip_address = db.Column(db.String(45), nullable=True)  # IPv4 or IPv6
    user_agent = db.Column(db.Text, nullable=True)
    
    # Indexes for performance
    __table_args__ = (
        db.Index('idx_otp_session_id', 'session_id'),
        db.Index('idx_otp_user_id', 'user_id'),
        db.Index('idx_otp_email', 'email'),
        db.Index('idx_otp_expires_at', 'expires_at'),
        db.Index('idx_otp_created_at', 'created_at'),
        db.Index('idx_otp_user_created', 'user_id', 'created_at'),
    )
    
    def __init__(self, session_id, user_id, email, otp_code, purpose='login', 
                 expires_at=None, ip_address=None, user_agent=None):
        self.session_id = session_id
        self.user_id = user_id
        self.email = email
        self.otp_code = otp_code
        self.purpose = purpose
        self.expires_at = expires_at or (datetime.utcnow() + timedelta(minutes=5))
        self.ip_address = ip_address
        self.user_agent = user_agent
    
    def is_expired(self) -> bool:
        """Check if the OTP token has expired"""
        return datetime.utcnow() > self.expires_at
    
    def is_valid(self) -> bool:
        """Check if the OTP token is valid (not used and not expired)"""
        return not self.is_used and not self.is_expired()
    
    def can_attempt(self, max_attempts: int = 3) -> bool:
        """Check if more attempts are allowed"""
        return self.attempts < max_attempts and self.is_valid()
    
    def increment_attempts(self):
        """Increment the attempt counter"""
        self.attempts += 1
    
    def mark_as_used(self):
        """Mark the token as used"""
        self.is_used = True
        self.used_at = datetime.utcnow()
    
    def to_dict(self, include_sensitive=False) -> dict:
        """Convert to dictionary representation"""
        data = {
            'token_id': str(self.token_id),
            'session_id': self.session_id,
            'user_id': self.user_id,
            'email': self.email,
            'purpose': self.purpose,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'used_at': self.used_at.isoformat() if self.used_at else None,
            'attempts': self.attempts,
            'is_used': self.is_used,
            'is_expired': self.is_expired(),
            'is_valid': self.is_valid(),
            'ip_address': self.ip_address,
        }
        
        # Include sensitive data only if explicitly requested
        if include_sensitive:
            data['otp_code'] = self.otp_code
            data['user_agent'] = self.user_agent
        
        return data
    
    @classmethod
    def create_token(cls, session, user_id, email, otp_code, purpose='login', 
                    expiry_minutes=5, ip_address=None, user_agent=None):
        """Create a new OTP token"""
        import secrets
        
        session_id = secrets.token_urlsafe(32)
        expires_at = datetime.utcnow() + timedelta(minutes=expiry_minutes)
        
        token = cls(
            session_id=session_id,
            user_id=user_id,
            email=email,
            otp_code=otp_code,
            purpose=purpose,
            expires_at=expires_at,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        session.add(token)
        session.flush()  # Get the ID without committing
        
        return token
    
    @classmethod
    def find_by_session_id(cls, session, session_id):
        """Find OTP token by session ID"""
        return session.query(cls).filter_by(session_id=session_id).first()
    
    @classmethod
    def find_valid_by_session_id(cls, session, session_id):
        """Find valid (unused and not expired) OTP token by session ID"""
        return session.query(cls).filter_by(
            session_id=session_id,
            is_used=False
        ).filter(
            cls.expires_at > datetime.utcnow()
        ).first()
    
    @classmethod
    def get_user_tokens(cls, session, user_id, limit=10):
        """Get recent OTP tokens for a user"""
        return session.query(cls).filter_by(
            user_id=user_id
        ).order_by(
            cls.created_at.desc()
        ).limit(limit).all()
    
    @classmethod
    def cleanup_expired(cls, session):
        """Remove expired OTP tokens"""
        expired_tokens = session.query(cls).filter(
            cls.expires_at < datetime.utcnow()
        )
        count = expired_tokens.count()
        expired_tokens.delete()
        return count
    
    @classmethod
    def get_recent_attempts(cls, session, user_id=None, email=None, hours=1):
        """Get recent OTP attempts for rate limiting"""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        query = session.query(cls).filter(cls.created_at >= cutoff_time)
        
        if user_id:
            query = query.filter(cls.user_id == user_id)
        elif email:
            query = query.filter(cls.email == email)
        
        return query.count()
    
    @classmethod
    def get_statistics(cls, session, user_id=None, days=30):
        """Get OTP usage statistics"""
        cutoff_time = datetime.utcnow() - timedelta(days=days)
        query = session.query(cls).filter(cls.created_at >= cutoff_time)
        
        if user_id:
            query = query.filter(cls.user_id == user_id)
        
        total = query.count()
        successful = query.filter(cls.is_used == True).count()
        failed = query.filter(cls.attempts > 0, cls.is_used == False).count()
        
        return {
            'total_requests': total,
            'successful_validations': successful,
            'failed_attempts': failed,
            'success_rate': (successful / total * 100) if total > 0 else 0
        }
    
    def __repr__(self):
        return f"<OTPToken {self.session_id} for {self.email} ({self.purpose})>"
