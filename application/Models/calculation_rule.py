from application.database import central_db as db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from flask import current_app
from datetime import datetime

class CalculationRule(db.Model):
    """Model representing configurable calculation rules for payroll (stored in central database)."""
    __tablename__ = 'calculation_rules'

    rule_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    country_id = db.Column(UUID(as_uuid=True), db.ForeignKey('countries.country_id'), nullable=False)
    calculation_type = db.Column(db.String(50), nullable=False)  # 'TAX', 'DEDUCTION'
    type_code = db.Column(db.String(50), nullable=False)  # 'PAYE', 'PENSION', 'CBHI', etc.
    calculation_base = db.Column(db.String(50), nullable=False)  # 'GROSS', 'NET_BEFORE_CBHI', 'GROSS_MINUS_TRANSPORT'
    calculation_order = db.Column(db.Integer, nullable=False, default=1)  # Order of calculation
    depends_on = db.Column(db.JSON, nullable=True)  # What this calculation depends on
    effective_from = db.Column(db.Date, nullable=False)
    effective_to = db.Column(db.Date, nullable=True)  # null means currently active
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    # Relationships
    country = db.relationship('Country', backref=db.backref('calculation_rules', lazy='dynamic'))

    # Unique constraint: one active rule per country/type/code at any time
    __table_args__ = (
        db.UniqueConstraint('country_id', 'calculation_type', 'type_code', 'effective_from', 
                          name='uq_calculation_rule_country_type_code_date'),
        db.Index('idx_calculation_rule_lookup', 'country_id', 'calculation_type', 'type_code', 'effective_from'),
    )

    def __str__(self):
        """Return a string representation of the object."""
        return f"CalculationRule [rule_id={self.rule_id}, type={self.calculation_type}, code={self.type_code}]"

    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "rule_id": str(self.rule_id),
            "country_id": str(self.country_id),
            "calculation_type": self.calculation_type,
            "type_code": self.type_code,
            "calculation_base": self.calculation_base,
            "calculation_order": self.calculation_order,
            "depends_on": self.depends_on,
            "effective_from": self.effective_from.strftime('%Y-%m-%d') if self.effective_from else None,
            "effective_to": self.effective_to.strftime('%Y-%m-%d') if self.effective_to else None,
            "is_active": self.is_active,
            "country_name": self.country.name if self.country else None,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

    @classmethod
    def create_rule(cls, **kwargs):
        """Create a new calculation rule."""
        try:
            rule = cls(**kwargs)
            db.session.add(rule)
            db.session.commit()
            current_app.logger.info(f"Created calculation rule: {rule.calculation_type} - {rule.type_code}")
            return rule
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error creating calculation rule: {e}")
            return None

    @classmethod
    def get_active_rule(cls, country_id, calculation_type, type_code, date=None):
        """Get the active calculation rule for specific criteria on a given date."""
        if date is None:
            date = datetime.now().date()

        query = cls.query.filter(
            cls.country_id == country_id,
            cls.calculation_type == calculation_type,
            cls.type_code == type_code,
            cls.effective_from <= date,
            cls.is_active == True
        )

        # Handle effective_to (null means currently active)
        query = query.filter(
            db.or_(
                cls.effective_to.is_(None),
                cls.effective_to >= date
            )
        )

        return query.first()

    @classmethod
    def get_active_rules_for_country(cls, country_id, calculation_type=None, date=None):
        """Get all active calculation rules for a country."""
        if date is None:
            date = datetime.now().date()

        query = cls.query.filter(
            cls.country_id == country_id,
            cls.effective_from <= date,
            cls.is_active == True
        )

        if calculation_type:
            query = query.filter(cls.calculation_type == calculation_type)

        # Handle effective_to
        query = query.filter(
            db.or_(
                cls.effective_to.is_(None),
                cls.effective_to >= date
            )
        )

        return query.order_by(cls.calculation_order).all()

    @classmethod
    def create_new_version(cls, existing_rule_id, new_effective_from, **kwargs):
        """Create a new version of an existing calculation rule."""
        try:
            # Get the existing rule
            existing_rule = cls.query.filter_by(rule_id=existing_rule_id).first()
            if not existing_rule:
                return None

            # Set end date for existing rule
            existing_rule.effective_to = new_effective_from
            
            # Create new rule version
            new_rule_data = {
                'country_id': existing_rule.country_id,
                'calculation_type': existing_rule.calculation_type,
                'type_code': existing_rule.type_code,
                'effective_from': new_effective_from,
                'effective_to': None,
                **kwargs
            }

            new_rule = cls.create_rule(**new_rule_data)
            
            if new_rule:
                db.session.commit()
                current_app.logger.info(f"Created new calculation rule version")
                return new_rule
            else:
                db.session.rollback()
                return None

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error creating new calculation rule version: {e}")
            return None

    @classmethod
    def get_by_country(cls, country_id):
        """Get all calculation rules for a specific country."""
        return cls.query.filter_by(country_id=country_id, is_active=True).order_by(
            cls.calculation_type, cls.calculation_order
        ).all()

    @classmethod
    def deactivate_rule(cls, rule_id):
        """Deactivate a calculation rule."""
        try:
            rule = cls.query.filter_by(rule_id=rule_id).first()
            if rule:
                rule.is_active = False
                db.session.commit()
                current_app.logger.info(f"Deactivated calculation rule: {rule.type_code}")
                return True
            return False
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error deactivating calculation rule: {e}")
            return False
