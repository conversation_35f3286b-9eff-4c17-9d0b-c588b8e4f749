from application.database import central_db as db
import os
import uuid
from sqlalchemy.dialects.postgresql import UUID
from dotenv import load_dotenv
from application.Models.associations import user_company_association

load_dotenv()

class Company(db.Model):
    __tablename__ = 'companies'
    company_id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()), nullable=False)
    company_name = db.Column(db.String(255), nullable=False)
    database_name = db.Column(db.String(255), unique=True, nullable=False)
    company_tin = db.Column(db.String(255), nullable=True, unique=True)
    phone_number = db.Column(db.String(255), nullable=True, unique=True)
    country_id = db.Column(UUID(as_uuid=True), db.ForeignKey('countries.country_id'), nullable=True)
    created_at = db.Column(db.DateTime, server_default=db.func.now())

    # Many-to-many relationship
    users = db.relationship(
        'User',
        secondary=user_company_association,
        back_populates='companies'
    )

    # Relationship with Country model
    country = db.relationship('Country', backref=db.backref('companies', lazy='dynamic'))

    # Relationship with APIClient model
    api_clients = db.relationship('APIClient', back_populates='company', lazy='dynamic')

    def __str__(self):
        """Return a string representation of this object."""
        return "Company [company_id={}, company_name={}, database_name={}, created_at={}]".format(self.company_id,
                                                                                                  self.company_name,
                                                                                                  self.database_name,
                                                                                                  self.created_at)

    def to_dict(self, include_country_details=True):
        """Return a dictionary representation of this object."""
        # Format the  created_at in dd-mm-yyyy hh:mm:ss format
        created_at = self.created_at.strftime('%d-%m-%Y %H:%M:%S') if self.created_at else None

        result = {
            "company_id": self.company_id,
            "company_name": self.company_name,
            "database_name": self.database_name,
            "company_tin": self.company_tin,
            "phone_number": self.phone_number,
            "country_id": str(self.country_id) if self.country_id else None,
            "created_at": created_at,
            "country_code": self.country.code if self.country else None,
            "country_name": self.country.name if self.country else None,
            "country_currency": self.country.currency if self.country else None,
            "time_zone": self.country.time_zone if self.country else None,
            "date_format": self.country.date_format if self.country else None
        }

        # Include country details if requested and country exists
        if include_country_details and self.country:
            result["country"] = {
                "code": self.country.code,
                "name": self.country.name,
                "currency": self.country.currency
            }
        else:
            result["country"] = None

        # Include devices if they exist
        if hasattr(self, 'devices'):
            result["devices"] = [device.to_dict() for device in self.devices]
        else:
            result["devices"] = []

        return result

    @classmethod
    def create_company(cls, **kwargs):
        """Create a new company."""
        company = cls(**kwargs)
        db.session.add(company)
        db.session.commit()
        return company

    @classmethod
    def get_company_by_tin(cls, company_tin):
        """Return a company by its tin."""
        company = cls.query.filter_by(company_tin=company_tin).first()
        return company

    @classmethod
    def get_companies(cls):
        """Return all companies."""
        companies = cls.query.all()
        return companies

    @classmethod
    def get_company_by_id(cls, company_id):
        """Return a company by its ID."""
        try:
            company = cls.query.filter_by(company_id=company_id).first()
            return company
        except Exception as e:
            from app import app
            app.logger.error(f"Error fetching company by ID {company_id}: {str(e)}")
            return None

    @classmethod
    def get_database_given_company_id(cls, company_id):
        """Return the database URI for a given company ID."""
        from app import app
        try:
            company = cls.query.filter_by(company_id=company_id).first()
            database_name = company.database_name if company else None
            return database_name
        except Exception as e:
            app.logger.error(f"Error fetching database name for company ID {company_id}: {str(e)}")
            return None

    @classmethod
    def get_database_names(cls):
        """Return all database names."""
        database_names = [company.database_name for company in cls.query.all()]
        return database_names

    @classmethod
    def update_company(cls, company_id, **kwargs):
        """Update an existing company."""
        from app import app
        try:
            company = cls.query.filter_by(company_id=company_id).first()
            if not company:
                return None, "Company not found"

            # Validate country_id if provided
            if 'country_id' in kwargs and kwargs['country_id'] is not None:
                from application.Models.country import Country
                country = Country.get_country_by_id(kwargs['country_id'])
                if not country:
                    return None, "Invalid country_id provided"

            # Check for unique constraints
            if 'company_tin' in kwargs and kwargs['company_tin'] != company.company_tin:
                existing_tin = cls.query.filter_by(company_tin=kwargs['company_tin']).first()
                if existing_tin:
                    return None, "Company TIN already exists"

            if 'phone_number' in kwargs and kwargs['phone_number'] != company.phone_number:
                existing_phone = cls.query.filter_by(phone_number=kwargs['phone_number']).first()
                if existing_phone:
                    return None, "Phone number already exists"

            # Update allowed fields
            allowed_fields = ['company_name', 'company_tin', 'phone_number', 'country_id']
            for field in allowed_fields:
                if field in kwargs:
                    setattr(company, field, kwargs[field])

            db.session.commit()
            app.logger.info(f"Company {company_id} updated successfully")
            return company, "Company updated successfully"

        except Exception as e:
            db.session.rollback()
            app.logger.error(f"Error updating company {company_id}: {str(e)}")
            return None, f"Error updating company: {str(e)}"

class CompanyDevice(db.Model):
    """Company device model."""
    __tablename__ = 'company_devices'

    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    company_id = db.Column(db.String(36), db.ForeignKey('companies.company_id'), nullable=False)
    device_sn = db.Column(db.String(80), unique=True, nullable=False)  # One device per company
    created_at = db.Column(db.DateTime, server_default=db.func.now())

    # define a relationship with the company model
    company = db.relationship('Company', backref=db.backref('devices', lazy=True))

    def __str__(self):
        """Return a string representation of this object."""
        return "CompanyDevice [id={}, company_id={}, device_sn={}]".format(self.id, self.company_id, self.device_sn)

    def to_dict(self):
        """Return a dictionary representation of this object."""
        # Format the  created_at in dd-mm-yyyy hh:mm:ss format
        created_at = self.created_at.strftime('%d-%m-%Y %H:%M:%S') if self.created_at else None
        return {
            "id": self.id,
            "company_id": self.company_id,
            "device_sn": self.device_sn,
            "company_name": self.company.company_name,
            "database_name": self.company.database_name,
            "created_at": created_at
        }

    @classmethod
    def create_company_device(cls, **kwargs):
        """Create a new company device."""
        company_device = cls(**kwargs)
        #First check if the device exists
        device = cls.query.filter_by(device_sn=kwargs['device_sn']).first()
        if device:
            return "Device already Registered with another company"
        try:
            db.session.add(company_device)
            db.session.commit()
            return company_device
        except Exception as e:
            db.session.rollback()
            return None

    @classmethod
    def get_company_devices(cls, company_id):
        """Return all devices for a company."""
        devices = cls.query.filter_by(company_id=company_id).all()
        return devices

    @classmethod
    def get_database_name_by_sn(cls, device_sn):
        """Return the database name for a device."""
        device = cls.query.filter_by(device_sn=device_sn).first()
        return device.company.database_name if device else None

