from application.database import central_db as db
import uuid
from sqlalchemy.dialects.postgresql import UUID
from datetime import datetime

class CompanyStorageQuota(db.Model):
    """Storage quotas and usage tracking for companies."""
    __tablename__ = 'company_storage_quotas'

    quota_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    company_id = db.Column(db.String(36), db.ForeignKey('companies.company_id'), nullable=False)
    storage_quota_gb = db.Column(db.Integer, default=5, nullable=False)  # 5GB default
    storage_used_bytes = db.Column(db.BigInteger, default=0, nullable=False)
    alert_threshold_percent = db.Column(db.Integer, default=80)  # Alert at 80%
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # Relationship
    company = db.relationship('Company', backref=db.backref('storage_quota', uselist=False))

    def __str__(self):
        """Return a string representation of the object."""
        return f"CompanyStorageQuota [quota_id={self.quota_id}, company_id={self.company_id}, quota={self.storage_quota_gb}GB]"

    def to_dict(self):
        """Dictionary representation."""
        quota_bytes = self.storage_quota_gb * 1024 * 1024 * 1024  # Convert GB to bytes
        usage_percentage = (self.storage_used_bytes / quota_bytes * 100) if quota_bytes > 0 else 0

        return {
            "quota_id": str(self.quota_id),
            "company_id": self.company_id,
            "storage_quota_gb": self.storage_quota_gb,
            "storage_used_bytes": self.storage_used_bytes,
            "storage_used_mb": round(self.storage_used_bytes / (1024 * 1024), 2),
            "usage_percentage": round(usage_percentage, 2),
            "alert_threshold_percent": self.alert_threshold_percent,
            "is_near_limit": self.is_near_limit(),
            "remaining_bytes": max(0, quota_bytes - self.storage_used_bytes),
            "remaining_mb": round(max(0, quota_bytes - self.storage_used_bytes) / (1024 * 1024), 2),
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

    def is_near_limit(self):
        """Check if company is near storage limit."""
        if self.storage_quota_gb == 0:
            return False
        quota_bytes = self.storage_quota_gb * 1024 * 1024 * 1024
        usage_percent = (self.storage_used_bytes / quota_bytes) * 100
        return usage_percent >= self.alert_threshold_percent

    def can_upload_file(self, file_size_bytes):
        """Check if company can upload a file of given size."""
        quota_bytes = self.storage_quota_gb * 1024 * 1024 * 1024
        return (self.storage_used_bytes + file_size_bytes) <= quota_bytes

    @classmethod
    def get_quota_by_company_id(cls, company_id):
        """Get storage quota for a company."""
        return cls.query.filter_by(company_id=company_id).first()

    @classmethod
    def create_or_update_quota(cls, company_id, storage_quota_gb, alert_threshold_percent=80):
        """Create or update storage quota for company."""
        try:
            quota = cls.get_quota_by_company_id(company_id)
            if not quota:
                quota = cls(
                    company_id=company_id,
                    storage_quota_gb=storage_quota_gb,
                    alert_threshold_percent=alert_threshold_percent
                )
                db.session.add(quota)
            else:
                quota.storage_quota_gb = storage_quota_gb
                quota.alert_threshold_percent = alert_threshold_percent
                quota.updated_at = datetime.now()

            db.session.commit()
            return quota, None
        except Exception as e:
            db.session.rollback()
            return None, str(e)

    @classmethod
    def update_usage(cls, company_id, bytes_added):
        """Update storage usage for company."""
        try:
            quota = cls.get_quota_by_company_id(company_id)
            if not quota:
                return None, "Company quota not found"

            if not quota.can_upload_file(bytes_added):
                return None, "Storage quota exceeded"

            quota.storage_used_bytes += bytes_added
            quota.updated_at = datetime.now()
            db.session.commit()
            return quota, None
        except Exception as e:
            db.session.rollback()
            return None, str(e)

    @classmethod
    def reduce_usage(cls, company_id, bytes_removed):
        """Reduce storage usage for company."""
        try:
            quota = cls.get_quota_by_company_id(company_id)
            if not quota:
                return None, "Company quota not found"

            quota.storage_used_bytes = max(0, quota.storage_used_bytes - bytes_removed)
            quota.updated_at = datetime.now()
            db.session.commit()
            return quota, None
        except Exception as e:
            db.session.rollback()
            return None, str(e)

    @classmethod
    def get_companies_near_limit(cls, threshold_percent=80):
        """Get companies approaching storage limits."""
        try:
            quotas = cls.query.all()
            near_limit = []

            for quota in quotas:
                if quota.storage_quota_gb > 0:
                    quota_bytes = quota.storage_quota_gb * 1024 * 1024 * 1024
                    usage_percent = (quota.storage_used_bytes / quota_bytes) * 100
                    if usage_percent >= threshold_percent:
                        near_limit.append(quota)

            return near_limit
        except Exception as e:
            return []

    @classmethod
    def get_all_quotas(cls):
        """Get all storage quotas."""
        return cls.query.order_by(cls.created_at.desc()).all()

    @classmethod
    def get_quota_stats(cls):
        """Get overall storage statistics."""
        try:
            quotas = cls.query.all()
            total_quota_gb = sum(q.storage_quota_gb for q in quotas)
            total_used_bytes = sum(q.storage_used_bytes for q in quotas)

            return {
                "total_companies": len(quotas),
                "total_quota_gb": total_quota_gb,
                "total_used_gb": round(total_used_bytes / (1024 * 1024 * 1024), 2),
                "total_usage_percentage": round((total_used_bytes / (total_quota_gb * 1024 * 1024 * 1024)) * 100, 2) if total_quota_gb > 0 else 0,
                "companies_near_limit": len(cls.get_companies_near_limit())
            }
        except Exception as e:
            return {
                "total_companies": 0,
                "total_quota_gb": 0,
                "total_used_gb": 0,
                "total_usage_percentage": 0,
                "companies_near_limit": 0
            }
