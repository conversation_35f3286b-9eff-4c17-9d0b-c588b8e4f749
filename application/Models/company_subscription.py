from application.database import central_db as db
import uuid
from sqlalchemy.dialects.postgresql import UUID
from flask import current_app
from datetime import datetime, date, timedelta
from decimal import Decimal

class CompanySubscription(db.Model):
    """Model representing company subscriptions (stored in central database)."""
    __tablename__ = 'company_subscriptions'

    subscription_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    company_id = db.Column(db.String(36), db.<PERSON><PERSON><PERSON>('companies.company_id'), nullable=False)
    plan_id = db.Column(UUID(as_uuid=True), db.ForeignKey('subscription_plans.plan_id'), nullable=False)
    status = db.Column(db.String(20), default='TRIAL', nullable=False)  # TRIAL, ACTIVE, SUSPENDED, CANCELLED
    current_period_start = db.Column(db.Date, nullable=False)
    current_period_end = db.Column(db.Date, nullable=False)
    employee_count = db.Column(db.Integer, default=0, nullable=False)  # Current billable employee count
    amount_due = db.Column(db.Numeric(12, 2), default=0, nullable=False)  # Calculated amount for current period
    trial_end_date = db.Column(db.Date, nullable=True)
    auto_renew = db.Column(db.Boolean, default=True, nullable=False)
    notes = db.Column(db.Text, nullable=True)

    # PayPal integration fields
    paypal_agreement_id = db.Column(db.String(255), nullable=True)  # Link to PayPal billing agreement
    last_billed_employee_count = db.Column(db.Integer, default=0, nullable=False)  # Track employee count changes
    next_billing_date = db.Column(db.Date, nullable=True)  # When to bill next
    payment_method = db.Column(db.String(50), default='MANUAL', nullable=False)  # MANUAL, PAYPAL, STRIPE, etc.
    auto_billing_enabled = db.Column(db.Boolean, default=False, nullable=False)  # Whether auto billing is set up
    last_billing_attempt = db.Column(db.DateTime, nullable=True)  # Last time we tried to bill
    billing_retry_count = db.Column(db.Integer, default=0, nullable=False)  # Number of failed billing attempts

    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    # Relationships
    company = db.relationship('Company', backref=db.backref('subscription', uselist=False))
    invoices = db.relationship('SubscriptionInvoice', backref='subscription', lazy='dynamic')

    # Unique constraint: one subscription per company
    __table_args__ = (
        db.UniqueConstraint('company_id', name='uq_company_subscription'),
        db.Index('idx_subscription_status', 'status'),
        db.Index('idx_subscription_period', 'current_period_start', 'current_period_end'),
    )

    def __str__(self):
        """Return a string representation of the object."""
        return f"CompanySubscription [subscription_id={self.subscription_id}, company_id={self.company_id}, status={self.status}]"

    def to_dict(self, include_company=False, include_plan=False, include_paypal=False):
        """Dictionary representation of the object."""
        data = {
            "subscription_id": str(self.subscription_id),
            "company_id": self.company_id,
            "plan_id": str(self.plan_id),
            "status": self.status,
            "current_period_start": self.current_period_start.strftime('%Y-%m-%d') if self.current_period_start else None,
            "current_period_end": self.current_period_end.strftime('%Y-%m-%d') if self.current_period_end else None,
            "employee_count": self.employee_count,
            "amount_due": float(self.amount_due),
            "trial_end_date": self.trial_end_date.strftime('%Y-%m-%d') if self.trial_end_date else None,
            "auto_renew": self.auto_renew,
            "notes": self.notes,
            "payment_method": self.payment_method,
            "auto_billing_enabled": self.auto_billing_enabled,
            "next_billing_date": self.next_billing_date.strftime('%Y-%m-%d') if self.next_billing_date else None,
            "last_billed_employee_count": self.last_billed_employee_count,
            "billing_retry_count": self.billing_retry_count,
            "last_billing_attempt": self.last_billing_attempt.strftime('%Y-%m-%d %H:%M:%S') if self.last_billing_attempt else None,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            "is_trial": self.is_trial(),
            "is_active": self.is_active(),
            "days_until_renewal": self.days_until_renewal(),
            "has_paypal_billing": self.has_paypal_billing()
        }

        # Add calculated pricing information
        if self.plan:
            monthly_cost = float(self.calculate_current_amount_due())
            data.update({
                "monthly_cost": monthly_cost,
                "pricing_breakdown": {
                    "flat_price": float(self.plan.flat_price) if self.plan.flat_price else 0.0,
                    "price_per_employee": float(self.plan.price_per_employee),
                    "employee_count": self.employee_count,
                    "employee_cost": float(self.plan.price_per_employee) * self.employee_count,
                    "total_monthly": monthly_cost,
                    "billing_cycle": self.plan.billing_cycle,
                    "currency": "USD"  # This could be configurable based on company country
                }
            })
        
        if include_company and self.company:
            data["company"] = {
                "company_name": self.company.company_name,
                "company_tin": self.company.company_tin,
                "phone_number": self.company.phone_number
            }
            
        if include_plan and self.plan:
            data["plan"] = self.plan.to_dict()
            
        return data

    def is_trial(self):
        """Check if subscription is in trial period."""
        return self.status == 'TRIAL' and self.trial_end_date and self.trial_end_date >= date.today()

    def is_active(self):
        """Check if subscription is active (including trial)."""
        return self.status in ['TRIAL', 'ACTIVE'] and self.current_period_end >= date.today()

    def is_expired(self):
        """Check if subscription has expired."""
        return self.current_period_end < date.today()

    def days_until_renewal(self):
        """Get days until next renewal."""
        if self.current_period_end:
            delta = self.current_period_end - date.today()
            return max(0, delta.days)
        return 0

    def days_until_trial_end(self):
        """Get days until trial ends."""
        if self.trial_end_date and self.is_trial():
            delta = self.trial_end_date - date.today()
            return max(0, delta.days)
        return 0

    def has_paypal_billing(self):
        """Check if PayPal billing is set up."""
        return bool(self.paypal_agreement_id and self.auto_billing_enabled)

    def can_auto_bill(self):
        """Check if subscription can be automatically billed."""
        return (self.has_paypal_billing() and
                self.status in ['TRIAL', 'ACTIVE'] and
                self.auto_renew)

    def needs_billing(self):
        """Check if subscription needs to be billed."""
        if not self.next_billing_date:
            return False
        return date.today() >= self.next_billing_date

    def employee_count_changed(self):
        """Check if employee count has changed since last billing."""
        return self.employee_count != self.last_billed_employee_count

    def calculate_current_amount_due(self):
        """Calculate amount due based on current employee count."""
        if self.plan:
            return Decimal(str(self.plan.calculate_price(self.employee_count)))
        return Decimal('0.00')

    @classmethod
    def create_subscription(cls, company_id, plan_id, trial_days=14, **kwargs):
        """Create a new company subscription."""
        try:
            from application.Models.subscription_plan import SubscriptionPlan
            
            # Validate plan exists
            plan = SubscriptionPlan.get_plan_by_id(plan_id)
            if not plan:
                return None, "Subscription plan not found"

            # Set up trial period
            today = date.today()
            trial_end = today + timedelta(days=trial_days)
            
            # Calculate billing period based on plan's billing cycle
            if plan.billing_cycle == 'MONTHLY':
                period_end = today + timedelta(days=30)
            elif plan.billing_cycle == 'QUARTERLY':
                period_end = today + timedelta(days=90)
            elif plan.billing_cycle == 'YEARLY':
                period_end = today + timedelta(days=365)
            else:
                period_end = today + timedelta(days=30)  # Default to monthly

            subscription_data = {
                'company_id': company_id,
                'plan_id': plan_id,
                'status': 'TRIAL',
                'current_period_start': today,
                'current_period_end': period_end,
                'trial_end_date': trial_end,
                'employee_count': 0,
                'amount_due': Decimal('0.00'),
                **kwargs
            }

            subscription = cls(**subscription_data)
            db.session.add(subscription)
            db.session.commit()
            
            current_app.logger.info(f"Created subscription for company {company_id} with plan {plan.name}")
            return subscription, None
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error creating subscription: {e}")
            return None, str(e)

    @classmethod
    def get_subscription_by_company(cls, company_id):
        """Get subscription for a company."""
        try:
            return cls.query.filter_by(company_id=company_id).first()
        except Exception as e:
            current_app.logger.error(f"Error getting subscription by company: {e}")
            return None

    @classmethod
    def get_subscription_by_id(cls, subscription_id):
        """Get subscription by ID."""
        try:
            if isinstance(subscription_id, str):
                subscription_id = uuid.UUID(subscription_id)
            return cls.query.filter_by(subscription_id=subscription_id).first()
        except Exception as e:
            current_app.logger.error(f"Error getting subscription by ID: {e}")
            return None

    @classmethod
    def get_subscriptions_by_status(cls, status):
        """Get all subscriptions with a specific status."""
        try:
            return cls.query.filter_by(status=status).all()
        except Exception as e:
            current_app.logger.error(f"Error getting subscriptions by status: {e}")
            return []

    @classmethod
    def get_expiring_subscriptions(cls, days_ahead=7):
        """Get subscriptions expiring within specified days."""
        try:
            expiry_date = date.today() + timedelta(days=days_ahead)
            return cls.query.filter(
                cls.current_period_end <= expiry_date,
                cls.status.in_(['TRIAL', 'ACTIVE'])
            ).all()
        except Exception as e:
            current_app.logger.error(f"Error getting expiring subscriptions: {e}")
            return []

    def update_employee_count(self, new_count):
        """Update employee count and recalculate amount due."""
        try:
            self.employee_count = new_count

            # Recalculate amount due based on plan
            if self.plan:
                self.amount_due = Decimal(str(self.plan.calculate_price(new_count)))

            db.session.commit()
            current_app.logger.info(f"Updated employee count to {new_count} for subscription {self.subscription_id}")
            return True, None
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error updating employee count: {e}")
            return False, str(e)

    def change_plan(self, new_plan_id):
        """Change subscription plan."""
        try:
            from application.Models.subscription_plan import SubscriptionPlan

            new_plan = SubscriptionPlan.get_plan_by_id(new_plan_id)
            if not new_plan:
                return False, "New plan not found"

            # Check if employee count exceeds new plan limit
            if new_plan.max_employees and self.employee_count > new_plan.max_employees:
                return False, f"Employee count ({self.employee_count}) exceeds new plan limit ({new_plan.max_employees})"

            old_plan_name = self.plan.name if self.plan else "Unknown"
            self.plan_id = new_plan_id

            # Recalculate amount due
            self.amount_due = Decimal(str(new_plan.calculate_price(self.employee_count)))

            db.session.commit()
            current_app.logger.info(f"Changed plan from {old_plan_name} to {new_plan.name} for subscription {self.subscription_id}")
            return True, None
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error changing plan: {e}")
            return False, str(e)

    def activate_subscription(self):
        """Activate subscription (convert from trial to active)."""
        try:
            if self.status != 'TRIAL':
                return False, "Can only activate trial subscriptions"

            self.status = 'ACTIVE'
            self.trial_end_date = None

            db.session.commit()
            current_app.logger.info(f"Activated subscription {self.subscription_id}")
            return True, None
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error activating subscription: {e}")
            return False, str(e)

    def suspend_subscription(self, reason=None):
        """Suspend subscription."""
        try:
            self.status = 'SUSPENDED'
            if reason:
                self.notes = f"{self.notes or ''}\nSuspended: {reason}".strip()

            db.session.commit()
            current_app.logger.info(f"Suspended subscription {self.subscription_id}")
            return True, None
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error suspending subscription: {e}")
            return False, str(e)

    def cancel_subscription(self, reason=None):
        """Cancel subscription."""
        try:
            self.status = 'CANCELLED'
            self.auto_renew = False
            if reason:
                self.notes = f"{self.notes or ''}\nCancelled: {reason}".strip()

            db.session.commit()
            current_app.logger.info(f"Cancelled subscription {self.subscription_id}")
            return True, None
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error cancelling subscription: {e}")
            return False, str(e)

    def renew_subscription(self):
        """Renew subscription for next period."""
        try:
            if not self.plan:
                return False, "No plan associated with subscription"

            # Calculate new period dates
            if self.plan.billing_cycle == 'MONTHLY':
                new_end = self.current_period_end + timedelta(days=30)
            elif self.plan.billing_cycle == 'QUARTERLY':
                new_end = self.current_period_end + timedelta(days=90)
            elif self.plan.billing_cycle == 'YEARLY':
                new_end = self.current_period_end + timedelta(days=365)
            else:
                new_end = self.current_period_end + timedelta(days=30)

            self.current_period_start = self.current_period_end + timedelta(days=1)
            self.current_period_end = new_end

            # Recalculate amount due
            self.amount_due = Decimal(str(self.plan.calculate_price(self.employee_count)))

            db.session.commit()
            current_app.logger.info(f"Renewed subscription {self.subscription_id} until {new_end}")
            return True, None
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error renewing subscription: {e}")
            return False, str(e)

    def setup_paypal_billing(self, paypal_agreement_id):
        """Set up PayPal billing for this subscription."""
        try:
            self.paypal_agreement_id = paypal_agreement_id
            self.payment_method = 'PAYPAL'
            self.auto_billing_enabled = True
            self.billing_retry_count = 0

            # Set next billing date
            if not self.next_billing_date:
                if self.is_trial():
                    self.next_billing_date = self.trial_end_date
                else:
                    self.next_billing_date = self.current_period_end

            db.session.commit()
            current_app.logger.info(f"Set up PayPal billing for subscription {self.subscription_id}")
            return True, None
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error setting up PayPal billing: {e}")
            return False, str(e)

    def disable_paypal_billing(self, reason=None):
        """Disable PayPal billing for this subscription."""
        try:
            self.auto_billing_enabled = False
            self.payment_method = 'MANUAL'
            if reason:
                self.notes = f"{self.notes or ''}\nPayPal billing disabled: {reason}".strip()

            db.session.commit()
            current_app.logger.info(f"Disabled PayPal billing for subscription {self.subscription_id}")
            return True, None
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error disabling PayPal billing: {e}")
            return False, str(e)

    def record_billing_attempt(self, success=False, amount=None, error_message=None):
        """Record a billing attempt."""
        try:
            self.last_billing_attempt = datetime.now()

            if success:
                self.billing_retry_count = 0
                self.last_billed_employee_count = self.employee_count
                if amount:
                    self.amount_due = Decimal(str(amount))

                # Set next billing date
                if self.plan:
                    if self.plan.billing_cycle == 'MONTHLY':
                        self.next_billing_date = date.today() + timedelta(days=30)
                    elif self.plan.billing_cycle == 'QUARTERLY':
                        self.next_billing_date = date.today() + timedelta(days=90)
                    elif self.plan.billing_cycle == 'YEARLY':
                        self.next_billing_date = date.today() + timedelta(days=365)
                    else:
                        self.next_billing_date = date.today() + timedelta(days=30)
            else:
                self.billing_retry_count += 1
                if error_message:
                    self.notes = f"{self.notes or ''}\nBilling failed: {error_message}".strip()

                # Disable auto billing after 3 failed attempts
                if self.billing_retry_count >= 3:
                    self.auto_billing_enabled = False
                    current_app.logger.warning(f"Disabled auto billing for subscription {self.subscription_id} after 3 failed attempts")

            db.session.commit()
            return True, None
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error recording billing attempt: {e}")
            return False, str(e)

    @classmethod
    def get_subscriptions_for_billing(cls):
        """Get subscriptions that need to be billed."""
        try:
            today = date.today()
            return cls.query.filter(
                cls.auto_billing_enabled == True,
                cls.paypal_agreement_id.isnot(None),
                cls.status.in_(['TRIAL', 'ACTIVE']),
                cls.next_billing_date <= today
            ).all()
        except Exception as e:
            current_app.logger.error(f"Error getting subscriptions for billing: {e}")
            return []

    @classmethod
    def get_subscriptions_with_employee_changes(cls):
        """Get subscriptions where employee count has changed."""
        try:
            return cls.query.filter(
                cls.auto_billing_enabled == True,
                cls.paypal_agreement_id.isnot(None),
                cls.status.in_(['ACTIVE']),
                cls.employee_count != cls.last_billed_employee_count
            ).all()
        except Exception as e:
            current_app.logger.error(f"Error getting subscriptions with employee changes: {e}")
            return []
