from application.database import central_db as db
import uuid
from sqlalchemy.dialects.postgresql import UUID
from flask import current_app as app

class Country(db.Model):
    """Model representing countries and their specific settings."""
    __tablename__ = 'countries'

    country_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    name = db.Column(db.String(255), nullable=False)
    code = db.Column(db.String(3), nullable=False, unique=True)  # ISO country code
    currency = db.Column(db.String(3), nullable=True)  # Currency code
    time_zone = db.Column(db.String(50), nullable=True)
    date_format = db.Column(db.String(50), nullable=True)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())
    
    def __str__(self):
        """Return a string representation of the object."""
        return f"Country [country_id={self.country_id}, name={self.name}, code={self.code}]"
    
    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "country_id": str(self.country_id),
            "name": self.name,
            "code": self.code,
            "currency": self.currency,
            "time_zone": self.time_zone,
            "date_format": self.date_format,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }
    
    @classmethod
    def get_country_by_code(cls, code):
        """Get a country by its code."""
        return cls.query.filter_by(code=code).first()
    
    @classmethod
    def get_country_by_id(cls, country_id):
        """Get a country by its ID."""
        return cls.query.filter_by(country_id=country_id).first()
    
    @classmethod
    def get_all_countries(cls):
        """Get all countries."""
        return cls.query.all()
    
    @classmethod
    def create_country(cls, **kwargs):
        """Create a new country."""
        try:
            country = cls(**kwargs)
            db.session.add(country)
            db.session.commit()
            app.logger.info(f"Created new country: {country.name}")
            return country
        except Exception as e:
            db.session.rollback()
            app.logger.error(f"Error creating country: {e}")
            return None
