"""Customer model for managing sauna/massage facility customers."""

from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUID
from flask import current_app as app
from datetime import datetime, date

class Customer(db.Model):
    """Model representing a customer in the sauna/massage facility.
    
    This model is completely separate from the Employee model to maintain
    clear domain boundaries and avoid confusion between employees and customers.
    """
    __tablename__ = 'customers'
    
    customer_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    
    # Basic Information
    first_name = db.Column(db.String(255), nullable=False)
    last_name = db.Column(db.String(255), nullable=False)
    email = db.Column(db.String(255), unique=True, nullable=True)
    phone_number = db.Column(db.String(20), unique=True, nullable=True)
    
    # Customer-Specific Fields
    membership_number = db.Column(db.String(50), unique=True, nullable=True)
    customer_segment = db.Column(db.String(50), default='Regular', nullable=False)  # VIP, Regular, Student, Corporate
    registration_date = db.Column(db.Date, nullable=False, default=date.today)
    date_of_birth = db.Column(db.Date, nullable=True)
    
    # Status
    status = db.Column(db.String(50), default='active', nullable=False)  # active, inactive, suspended, banned
    
    # Preferences
    preferred_contact_method = db.Column(db.String(50), nullable=True)  # email, sms, phone
    marketing_consent = db.Column(db.Boolean, default=False, nullable=False)
    
    # Metadata
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())
    created_by = db.Column(UUID(as_uuid=True), nullable=True)
    
    # Relationships
    visits = db.relationship('CustomerVisit', backref='customer', lazy='dynamic', cascade='all, delete-orphan')
    loyalty_balances = db.relationship('CustomerLoyaltyBalance', backref='customer', lazy='dynamic', cascade='all, delete-orphan')
    
    def __str__(self):
        """Return a string representation of the object."""
        return f"Customer [customer_id={self.customer_id}, name={self.first_name} {self.last_name}, segment={self.customer_segment}]"
    
    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "customer_id": str(self.customer_id),
            "first_name": self.first_name,
            "last_name": self.last_name,
            "full_name": f"{self.first_name} {self.last_name}",
            "email": self.email,
            "phone_number": self.phone_number,
            "membership_number": self.membership_number,
            "customer_segment": self.customer_segment,
            "registration_date": self.registration_date.strftime('%Y-%m-%d') if self.registration_date else None,
            "date_of_birth": self.date_of_birth.strftime('%Y-%m-%d') if self.date_of_birth else None,
            "status": self.status,
            "preferred_contact_method": self.preferred_contact_method,
            "marketing_consent": self.marketing_consent,
            "notes": self.notes,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }
    
    @classmethod
    def get_customer_by_id(cls, session, customer_id):
        """Get a customer by customer_id.
        
        Args:
            session: Database session
            customer_id: UUID or string representation of customer_id
            
        Returns:
            Customer object or None if not found
        """
        try:
            if isinstance(customer_id, str):
                try:
                    customer_id = uuid.UUID(customer_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {customer_id}")
                    return None
            
            customer = session.query(cls).filter_by(customer_id=customer_id).first()
            return customer
        except Exception as e:
            app.logger.error(f"Error getting customer by ID: {e}")
            return None
    
    @classmethod
    def get_customer_by_email(cls, session, email):
        """Get a customer by email address.
        
        Args:
            session: Database session
            email: Email address
            
        Returns:
            Customer object or None if not found
        """
        try:
            customer = session.query(cls).filter_by(email=email).first()
            return customer
        except Exception as e:
            app.logger.error(f"Error getting customer by email: {e}")
            return None
    
    @classmethod
    def get_customer_by_phone(cls, session, phone_number):
        """Get a customer by phone number.
        
        Args:
            session: Database session
            phone_number: Phone number
            
        Returns:
            Customer object or None if not found
        """
        try:
            customer = session.query(cls).filter_by(phone_number=phone_number).first()
            return customer
        except Exception as e:
            app.logger.error(f"Error getting customer by phone: {e}")
            return None
    
    @classmethod
    def get_customer_by_membership(cls, session, membership_number):
        """Get a customer by membership number.
        
        Args:
            session: Database session
            membership_number: Membership number
            
        Returns:
            Customer object or None if not found
        """
        try:
            customer = session.query(cls).filter_by(membership_number=membership_number).first()
            return customer
        except Exception as e:
            app.logger.error(f"Error getting customer by membership number: {e}")
            return None
    
    @classmethod
    def get_all_customers(cls, session, status=None, segment=None):
        """Get all customers with optional filtering.
        
        Args:
            session: Database session
            status: Optional status filter (active, inactive, suspended, banned)
            segment: Optional segment filter (VIP, Regular, Student, Corporate)
            
        Returns:
            List of Customer objects
        """
        try:
            query = session.query(cls)
            
            if status:
                query = query.filter_by(status=status)
            
            if segment:
                query = query.filter_by(customer_segment=segment)
            
            return query.order_by(cls.last_name, cls.first_name).all()
        except Exception as e:
            app.logger.error(f"Error getting customers: {e}")
            return []
    
    @classmethod
    def get_paginated_customers(cls, session, page=1, per_page=10, status=None, segment=None, search_term=None):
        """Get paginated customers with optional filtering.
        
        Args:
            session: Database session
            page: Page number (starting from 1)
            per_page: Number of items per page
            status: Optional status filter
            segment: Optional segment filter
            search_term: Search in first_name, last_name, email, phone_number, membership_number
            
        Returns:
            tuple: (total_count, customers)
        """
        try:
            query = session.query(cls)
            
            if status:
                query = query.filter_by(status=status)
            
            if segment:
                query = query.filter_by(customer_segment=segment)
            
            if search_term:
                search_pattern = f"%{search_term}%"
                query = query.filter(
                    db.or_(
                        cls.first_name.ilike(search_pattern),
                        cls.last_name.ilike(search_pattern),
                        cls.email.ilike(search_pattern),
                        cls.phone_number.ilike(search_pattern),
                        cls.membership_number.ilike(search_pattern)
                    )
                )
            
            total_count = query.count()
            
            offset = (page - 1) * per_page
            customers = query.order_by(cls.last_name, cls.first_name).offset(offset).limit(per_page).all()
            
            return total_count, customers
        except Exception as e:
            app.logger.error(f"Error getting paginated customers: {e}")
            return 0, []
    
    @classmethod
    def create_customer(cls, session, **kwargs):
        """Create a new customer.
        
        Args:
            session: Database session
            **kwargs: Customer attributes
            
        Returns:
            Customer object or None if creation failed
        """
        try:
            customer = cls(**kwargs)
            session.add(customer)
            session.commit()
            app.logger.info(f"Created new customer: {customer.first_name} {customer.last_name} (ID: {customer.customer_id})")
            return customer
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating customer: {e}")
            return None
    
    @classmethod
    def update_customer(cls, session, customer_id, **kwargs):
        """Update an existing customer.
        
        Args:
            session: Database session
            customer_id: UUID of the customer to update
            **kwargs: Fields to update
            
        Returns:
            Updated Customer object or None if update failed
        """
        try:
            customer = cls.get_customer_by_id(session, customer_id)
            if not customer:
                app.logger.error(f"Customer not found: {customer_id}")
                return None
            
            for key, value in kwargs.items():
                if hasattr(customer, key) and key not in ['customer_id', 'created_at']:
                    setattr(customer, key, value)
            
            session.commit()
            app.logger.info(f"Updated customer: {customer_id}")
            return customer
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error updating customer: {e}")
            return None
    
    @classmethod
    def delete_customer(cls, session, customer_id):
        """Delete a customer (soft delete by setting status to inactive).
        
        Args:
            session: Database session
            customer_id: UUID of the customer to delete
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            customer = cls.get_customer_by_id(session, customer_id)
            if not customer:
                app.logger.error(f"Customer not found: {customer_id}")
                return False
            
            customer.status = 'inactive'
            session.commit()
            app.logger.info(f"Soft deleted customer: {customer_id}")
            return True
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error deleting customer: {e}")
            return False
    
    def get_visit_count(self, session, start_date=None, end_date=None):
        """Get the total number of visits for this customer.
        
        Args:
            session: Database session
            start_date: Optional start date filter
            end_date: Optional end date filter
            
        Returns:
            int: Number of visits
        """
        try:
            from application.Models.customers.customer_visit import CustomerVisit
            
            query = session.query(CustomerVisit).filter_by(customer_id=self.customer_id)
            
            if start_date:
                query = query.filter(CustomerVisit.visit_date >= start_date)
            
            if end_date:
                query = query.filter(CustomerVisit.visit_date <= end_date)
            
            return query.count()
        except Exception as e:
            app.logger.error(f"Error getting visit count for customer {self.customer_id}: {e}")
            return 0
    
    def get_available_rewards(self, session):
        """Get the total number of available rewards for this customer.
        
        Args:
            session: Database session
            
        Returns:
            int: Number of available rewards
        """
        try:
            from application.Models.customers.customer_loyalty_balance import CustomerLoyaltyBalance
            
            balances = session.query(CustomerLoyaltyBalance).filter_by(customer_id=self.customer_id).all()
            total_rewards = sum(balance.rewards_available for balance in balances)
            
            return total_rewards
        except Exception as e:
            app.logger.error(f"Error getting available rewards for customer {self.customer_id}: {e}")
            return 0

