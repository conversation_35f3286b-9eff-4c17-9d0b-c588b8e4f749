"""Customer loyalty balance model for tracking progress toward rewards."""

from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUID
from flask import current_app as app
from datetime import datetime, date, timedelta

class CustomerLoyaltyBalance(db.Model):
    """Model representing a customer's progress toward earning a reward.
    
    Each customer can have multiple balances (one per active promotion rule).
    """
    __tablename__ = 'customer_loyalty_balances'
    
    balance_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    
    # Core Fields
    customer_id = db.Column(UUID(as_uuid=True), db.ForeignKey('customers.customer_id', ondelete='CASCADE'), nullable=False)
    rule_id = db.Column(UUID(as_uuid=True), db.ForeignKey('promotion_rules.rule_id', ondelete='CASCADE'), nullable=False)
    
    # Progress Tracking
    current_count = db.Column(db.Integer, default=0, nullable=False)  # Current progress (e.g., 7 visits)
    target_count = db.Column(db.Integer, nullable=False)  # Target to reach (e.g., 10 visits)
    
    # Reward Tracking
    rewards_earned = db.Column(db.Integer, default=0, nullable=False)  # Total rewards earned
    rewards_redeemed = db.Column(db.Integer, default=0, nullable=False)  # Total rewards redeemed
    rewards_available = db.Column(db.Integer, default=0, nullable=False)  # Available rewards (earned - redeemed)
    rewards_expired = db.Column(db.Integer, default=0, nullable=False)  # Expired rewards
    
    # Period Tracking (for time-based rules)
    period_start_date = db.Column(db.Date, nullable=True)  # Start of current tracking period
    period_end_date = db.Column(db.Date, nullable=True)  # End of current tracking period
    
    # Status
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    last_activity_date = db.Column(db.Date, nullable=True)
    
    # Metadata
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())
    
    # Unique constraint: one balance per customer per rule
    __table_args__ = (
        db.UniqueConstraint('customer_id', 'rule_id', name='uq_customer_rule'),
    )
    
    def __str__(self):
        """Return a string representation of the object."""
        return f"CustomerLoyaltyBalance [balance_id={self.balance_id}, customer_id={self.customer_id}, rule_id={self.rule_id}, progress={self.current_count}/{self.target_count}]"
    
    def to_dict(self):
        """Dictionary representation of the object."""
        progress_percentage = (self.current_count / self.target_count * 100) if self.target_count > 0 else 0
        
        return {
            "balance_id": str(self.balance_id),
            "customer_id": str(self.customer_id),
            "rule_id": str(self.rule_id),
            "rule": self.rule.to_dict() if self.rule else None,
            "current_count": self.current_count,
            "target_count": self.target_count,
            "progress_percentage": round(progress_percentage, 2),
            "rewards_earned": self.rewards_earned,
            "rewards_redeemed": self.rewards_redeemed,
            "rewards_available": self.rewards_available,
            "rewards_expired": self.rewards_expired,
            "period_start_date": self.period_start_date.strftime('%Y-%m-%d') if self.period_start_date else None,
            "period_end_date": self.period_end_date.strftime('%Y-%m-%d') if self.period_end_date else None,
            "is_active": self.is_active,
            "last_activity_date": self.last_activity_date.strftime('%Y-%m-%d') if self.last_activity_date else None,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }
    
    @classmethod
    def get_balance_by_id(cls, session, balance_id):
        """Get a loyalty balance by balance_id.
        
        Args:
            session: Database session
            balance_id: UUID or string representation of balance_id
            
        Returns:
            CustomerLoyaltyBalance object or None if not found
        """
        try:
            if isinstance(balance_id, str):
                try:
                    balance_id = uuid.UUID(balance_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {balance_id}")
                    return None
            
            balance = session.query(cls).filter_by(balance_id=balance_id).first()
            return balance
        except Exception as e:
            app.logger.error(f"Error getting loyalty balance by ID: {e}")
            return None
    
    @classmethod
    def get_balance_by_customer_and_rule(cls, session, customer_id, rule_id):
        """Get a loyalty balance for a specific customer and rule.
        
        Args:
            session: Database session
            customer_id: UUID of the customer
            rule_id: UUID of the promotion rule
            
        Returns:
            CustomerLoyaltyBalance object or None if not found
        """
        try:
            if isinstance(customer_id, str):
                customer_id = uuid.UUID(customer_id)
            if isinstance(rule_id, str):
                rule_id = uuid.UUID(rule_id)
            
            balance = session.query(cls).filter_by(
                customer_id=customer_id,
                rule_id=rule_id
            ).first()
            return balance
        except Exception as e:
            app.logger.error(f"Error getting loyalty balance by customer and rule: {e}")
            return None
    
    @classmethod
    def get_balances_by_customer(cls, session, customer_id, active_only=True):
        """Get all loyalty balances for a customer.
        
        Args:
            session: Database session
            customer_id: UUID of the customer
            active_only: If True, only return active balances
            
        Returns:
            List of CustomerLoyaltyBalance objects
        """
        try:
            if isinstance(customer_id, str):
                customer_id = uuid.UUID(customer_id)
            
            query = session.query(cls).filter_by(customer_id=customer_id)
            
            if active_only:
                query = query.filter_by(is_active=True)
            
            return query.all()
        except Exception as e:
            app.logger.error(f"Error getting loyalty balances by customer: {e}")
            return []
    
    @classmethod
    def create_balance(cls, session, **kwargs):
        """Create a new loyalty balance.
        
        Args:
            session: Database session
            **kwargs: Balance attributes
            
        Returns:
            CustomerLoyaltyBalance object or None if creation failed
        """
        try:
            balance = cls(**kwargs)
            session.add(balance)
            session.commit()
            app.logger.info(f"Created new loyalty balance: {balance.balance_id} for customer {balance.customer_id}")
            return balance
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating loyalty balance: {e}")
            return None
    
    @classmethod
    def get_or_create_balance(cls, session, customer_id, rule_id, target_count, period_days=None):
        """Get existing balance or create a new one.
        
        Args:
            session: Database session
            customer_id: UUID of the customer
            rule_id: UUID of the promotion rule
            target_count: Target count for the rule
            period_days: Optional period in days for time-based rules
            
        Returns:
            CustomerLoyaltyBalance object
        """
        try:
            balance = cls.get_balance_by_customer_and_rule(session, customer_id, rule_id)
            
            if balance:
                return balance
            
            # Create new balance
            balance_data = {
                'customer_id': customer_id,
                'rule_id': rule_id,
                'target_count': target_count,
                'current_count': 0,
                'rewards_available': 0
            }
            
            if period_days:
                balance_data['period_start_date'] = date.today()
                balance_data['period_end_date'] = date.today() + timedelta(days=period_days)
            
            balance = cls.create_balance(session, **balance_data)
            return balance
        except Exception as e:
            app.logger.error(f"Error getting or creating loyalty balance: {e}")
            return None
    
    def increment_count(self, session, increment=1):
        """Increment the current count and check if reward should be earned.
        
        Args:
            session: Database session
            increment: Amount to increment (default: 1)
            
        Returns:
            bool: True if a reward was earned, False otherwise
        """
        try:
            self.current_count += increment
            self.last_activity_date = date.today()
            
            reward_earned = False
            
            # Check if target reached
            if self.current_count >= self.target_count:
                # Award reward
                self.rewards_earned += 1
                self.rewards_available += 1
                
                # Reset counter for next reward
                self.current_count = self.current_count - self.target_count
                
                reward_earned = True
                app.logger.info(f"Customer {self.customer_id} earned a reward for rule {self.rule_id}")
            
            session.commit()
            return reward_earned
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error incrementing loyalty count: {e}")
            return False
    
    def redeem_reward(self, session):
        """Redeem one available reward.
        
        Args:
            session: Database session
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if self.rewards_available <= 0:
                app.logger.warning(f"No rewards available to redeem for balance {self.balance_id}")
                return False
            
            self.rewards_available -= 1
            self.rewards_redeemed += 1
            self.last_activity_date = date.today()
            
            session.commit()
            app.logger.info(f"Redeemed reward for customer {self.customer_id}, rule {self.rule_id}")
            return True
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error redeeming reward: {e}")
            return False
    
    def reset_period(self, session, period_days):
        """Reset the tracking period for time-based rules.
        
        Args:
            session: Database session
            period_days: Number of days in the new period
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.current_count = 0
            self.period_start_date = date.today()
            self.period_end_date = date.today() + timedelta(days=period_days)
            
            session.commit()
            app.logger.info(f"Reset period for balance {self.balance_id}")
            return True
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error resetting period: {e}")
            return False
    
    def is_period_expired(self):
        """Check if the tracking period has expired.
        
        Returns:
            bool: True if expired, False otherwise
        """
        if not self.period_end_date:
            return False
        
        return date.today() > self.period_end_date
    
    def get_progress_percentage(self):
        """Calculate progress percentage toward next reward.
        
        Returns:
            float: Progress percentage (0-100)
        """
        if self.target_count <= 0:
            return 0.0
        
        return round((self.current_count / self.target_count) * 100, 2)

