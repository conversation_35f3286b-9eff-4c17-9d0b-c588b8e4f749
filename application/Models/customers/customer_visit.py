"""Customer visit model for tracking facility visits."""

from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUID
from flask import current_app as app
from datetime import datetime, date

class CustomerVisit(db.Model):
    """Model representing a customer's visit to the sauna/massage facility.
    
    This is simpler than the Attendance model since customers only check IN
    (no check-out, no shifts, no overtime tracking).
    """
    __tablename__ = 'customer_visits'
    
    visit_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    
    # Core Fields
    customer_id = db.Column(UUID(as_uuid=True), db.ForeignKey('customers.customer_id', ondelete='CASCADE'), nullable=False)
    visit_date = db.Column(db.Date, nullable=False)
    visit_time = db.Column(db.DateTime, nullable=False)
    
    # Source Tracking
    source = db.Column(db.String(50), nullable=False)  # biometric, manual, qr_code, mobile_app
    source_record_id = db.Column(db.String(255), nullable=True)  # ID from records table
    device_serial_num = db.Column(db.String(255), nullable=True)
    
    # Visit Details
    visit_type = db.Column(db.String(50), default='regular', nullable=False)  # regular, complimentary, reward_redemption
    duration_minutes = db.Column(db.Integer, nullable=True)  # Optional: track how long they stayed
    
    # Loyalty Integration
    is_loyalty_visit = db.Column(db.Boolean, default=True, nullable=False)  # Count toward loyalty program?
    loyalty_points_earned = db.Column(db.Integer, default=0, nullable=False)  # For future point-based system
    reward_redeemed = db.Column(db.Boolean, default=False, nullable=False)  # Was a reward used for this visit?
    redemption_id = db.Column(UUID(as_uuid=True), db.ForeignKey('reward_redemptions.redemption_id', ondelete='SET NULL'), nullable=True)
    
    # Metadata
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())
    
    def __str__(self):
        """Return a string representation of the object."""
        return f"CustomerVisit [visit_id={self.visit_id}, customer_id={self.customer_id}, date={self.visit_date}, time={self.visit_time}]"
    
    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "visit_id": str(self.visit_id),
            "customer_id": str(self.customer_id),
            "customer": self.customer.to_dict() if self.customer else None,
            "visit_date": self.visit_date.strftime('%Y-%m-%d') if self.visit_date else None,
            "visit_time": self.visit_time.strftime('%Y-%m-%d %H:%M:%S') if self.visit_time else None,
            "source": self.source,
            "source_record_id": self.source_record_id,
            "device_serial_num": self.device_serial_num,
            "visit_type": self.visit_type,
            "duration_minutes": self.duration_minutes,
            "is_loyalty_visit": self.is_loyalty_visit,
            "loyalty_points_earned": self.loyalty_points_earned,
            "reward_redeemed": self.reward_redeemed,
            "redemption_id": str(self.redemption_id) if self.redemption_id else None,
            "notes": self.notes,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }
    
    @classmethod
    def get_visit_by_id(cls, session, visit_id):
        """Get a visit by visit_id.
        
        Args:
            session: Database session
            visit_id: UUID or string representation of visit_id
            
        Returns:
            CustomerVisit object or None if not found
        """
        try:
            if isinstance(visit_id, str):
                try:
                    visit_id = uuid.UUID(visit_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {visit_id}")
                    return None
            
            visit = session.query(cls).filter_by(visit_id=visit_id).first()
            return visit
        except Exception as e:
            app.logger.error(f"Error getting visit by ID: {e}")
            return None
    
    @classmethod
    def get_visits_by_customer(cls, session, customer_id, start_date=None, end_date=None, limit=None):
        """Get all visits for a specific customer.
        
        Args:
            session: Database session
            customer_id: UUID of the customer
            start_date: Optional start date filter
            end_date: Optional end date filter
            limit: Optional limit on number of results
            
        Returns:
            List of CustomerVisit objects
        """
        try:
            if isinstance(customer_id, str):
                customer_id = uuid.UUID(customer_id)
            
            query = session.query(cls).filter_by(customer_id=customer_id)
            
            if start_date:
                query = query.filter(cls.visit_date >= start_date)
            
            if end_date:
                query = query.filter(cls.visit_date <= end_date)
            
            query = query.order_by(cls.visit_date.desc(), cls.visit_time.desc())
            
            if limit:
                query = query.limit(limit)
            
            return query.all()
        except Exception as e:
            app.logger.error(f"Error getting visits by customer: {e}")
            return []
    
    @classmethod
    def get_visits_by_date(cls, session, visit_date):
        """Get all visits for a specific date.
        
        Args:
            session: Database session
            visit_date: Date to filter by
            
        Returns:
            List of CustomerVisit objects
        """
        try:
            visits = session.query(cls).filter_by(visit_date=visit_date).order_by(cls.visit_time).all()
            return visits
        except Exception as e:
            app.logger.error(f"Error getting visits by date: {e}")
            return []
    
    @classmethod
    def get_visits_by_date_range(cls, session, start_date, end_date):
        """Get all visits within a date range.
        
        Args:
            session: Database session
            start_date: Start date (inclusive)
            end_date: End date (inclusive)
            
        Returns:
            List of CustomerVisit objects
        """
        try:
            visits = session.query(cls).filter(
                cls.visit_date >= start_date,
                cls.visit_date <= end_date
            ).order_by(cls.visit_date.desc(), cls.visit_time.desc()).all()
            return visits
        except Exception as e:
            app.logger.error(f"Error getting visits by date range: {e}")
            return []
    
    @classmethod
    def create_visit(cls, session, **kwargs):
        """Create a new customer visit.
        
        Args:
            session: Database session
            **kwargs: Visit attributes
            
        Returns:
            CustomerVisit object or None if creation failed
        """
        try:
            visit = cls(**kwargs)
            session.add(visit)
            session.commit()
            app.logger.info(f"Created new customer visit: {visit.visit_id} for customer {visit.customer_id}")
            return visit
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating customer visit: {e}")
            return None
    
    @classmethod
    def update_visit(cls, session, visit_id, **kwargs):
        """Update an existing visit.
        
        Args:
            session: Database session
            visit_id: UUID of the visit to update
            **kwargs: Fields to update
            
        Returns:
            Updated CustomerVisit object or None if update failed
        """
        try:
            visit = cls.get_visit_by_id(session, visit_id)
            if not visit:
                app.logger.error(f"Visit not found: {visit_id}")
                return None
            
            for key, value in kwargs.items():
                if hasattr(visit, key) and key not in ['visit_id', 'created_at']:
                    setattr(visit, key, value)
            
            session.commit()
            app.logger.info(f"Updated visit: {visit_id}")
            return visit
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error updating visit: {e}")
            return None
    
    @classmethod
    def count_customer_visits(cls, session, customer_id, start_date=None, end_date=None, loyalty_only=True):
        """Count visits for a customer within a date range.
        
        Args:
            session: Database session
            customer_id: UUID of the customer
            start_date: Optional start date filter
            end_date: Optional end date filter
            loyalty_only: If True, only count visits where is_loyalty_visit=True
            
        Returns:
            int: Number of visits
        """
        try:
            if isinstance(customer_id, str):
                customer_id = uuid.UUID(customer_id)
            
            query = session.query(cls).filter_by(customer_id=customer_id)
            
            if loyalty_only:
                query = query.filter_by(is_loyalty_visit=True)
            
            if start_date:
                query = query.filter(cls.visit_date >= start_date)
            
            if end_date:
                query = query.filter(cls.visit_date <= end_date)
            
            return query.count()
        except Exception as e:
            app.logger.error(f"Error counting customer visits: {e}")
            return 0
    
    @classmethod
    def get_recent_visits(cls, session, limit=10):
        """Get the most recent visits across all customers.
        
        Args:
            session: Database session
            limit: Maximum number of visits to return
            
        Returns:
            List of CustomerVisit objects
        """
        try:
            visits = session.query(cls).order_by(
                cls.visit_date.desc(),
                cls.visit_time.desc()
            ).limit(limit).all()
            return visits
        except Exception as e:
            app.logger.error(f"Error getting recent visits: {e}")
            return []
    
    @classmethod
    def check_duplicate_visit(cls, session, customer_id, visit_date, visit_time, tolerance_minutes=5):
        """Check if a duplicate visit exists within a time tolerance.
        
        This prevents creating duplicate visits if the biometric device sends
        the same record multiple times.
        
        Args:
            session: Database session
            customer_id: UUID of the customer
            visit_date: Date of the visit
            visit_time: Time of the visit
            tolerance_minutes: Time tolerance in minutes
            
        Returns:
            CustomerVisit object if duplicate found, None otherwise
        """
        try:
            from datetime import timedelta
            
            if isinstance(customer_id, str):
                customer_id = uuid.UUID(customer_id)
            
            time_lower = visit_time - timedelta(minutes=tolerance_minutes)
            time_upper = visit_time + timedelta(minutes=tolerance_minutes)
            
            duplicate = session.query(cls).filter(
                cls.customer_id == customer_id,
                cls.visit_date == visit_date,
                cls.visit_time >= time_lower,
                cls.visit_time <= time_upper
            ).first()
            
            return duplicate
        except Exception as e:
            app.logger.error(f"Error checking duplicate visit: {e}")
            return None

