"""Promotion audit log model for tracking all loyalty program activities."""

from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUID, JSONB
from flask import current_app as app
from datetime import datetime

class PromotionAuditLog(db.Model):
    """Model representing an audit log entry for loyalty program activities.
    
    Provides complete audit trail for compliance and debugging.
    """
    __tablename__ = 'promotion_audit_logs'
    
    log_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    
    # Core Fields
    customer_id = db.Column(UUID(as_uuid=True), db.ForeignKey('customers.customer_id', ondelete='CASCADE'), nullable=True)
    
    # Action Details
    action_type = db.Column(db.String(50), nullable=False)  # visit_recorded, reward_earned, reward_redeemed, balance_updated, rule_created, rule_updated
    entity_type = db.Column(db.String(50), nullable=False)  # customer_visit, loyalty_balance, redemption, promotion_rule
    entity_id = db.Column(UUID(as_uuid=True), nullable=True)  # ID of the affected entity
    
    # Change Tracking
    old_value = db.Column(JSONB, nullable=True)  # Previous state (JSON)
    new_value = db.Column(JSONB, nullable=True)  # New state (JSON)
    
    # Context
    description = db.Column(db.Text, nullable=True)
    performed_by = db.Column(UUID(as_uuid=True), nullable=True)  # User ID who performed the action
    ip_address = db.Column(db.String(45), nullable=True)
    user_agent = db.Column(db.String(255), nullable=True)
    
    # Metadata
    timestamp = db.Column(db.DateTime, server_default=db.func.now(), nullable=False)
    
    def __str__(self):
        """Return a string representation of the object."""
        return f"PromotionAuditLog [log_id={self.log_id}, action_type={self.action_type}, entity_type={self.entity_type}, timestamp={self.timestamp}]"
    
    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "log_id": str(self.log_id),
            "customer_id": str(self.customer_id) if self.customer_id else None,
            "action_type": self.action_type,
            "entity_type": self.entity_type,
            "entity_id": str(self.entity_id) if self.entity_id else None,
            "old_value": self.old_value,
            "new_value": self.new_value,
            "description": self.description,
            "performed_by": str(self.performed_by) if self.performed_by else None,
            "ip_address": self.ip_address,
            "user_agent": self.user_agent,
            "timestamp": self.timestamp.strftime('%Y-%m-%d %H:%M:%S') if self.timestamp else None
        }
    
    @classmethod
    def create_log(cls, session, **kwargs):
        """Create a new audit log entry.
        
        Args:
            session: Database session
            **kwargs: Log attributes
            
        Returns:
            PromotionAuditLog object or None if creation failed
        """
        try:
            log = cls(**kwargs)
            session.add(log)
            session.commit()
            return log
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating audit log: {e}")
            return None
    
    @classmethod
    def log_visit_recorded(cls, session, customer_id, visit_id, visit_data, performed_by=None):
        """Log a customer visit being recorded.
        
        Args:
            session: Database session
            customer_id: UUID of the customer
            visit_id: UUID of the visit
            visit_data: Dictionary of visit data
            performed_by: Optional user ID who performed the action
            
        Returns:
            PromotionAuditLog object or None
        """
        return cls.create_log(
            session,
            customer_id=customer_id,
            action_type='visit_recorded',
            entity_type='customer_visit',
            entity_id=visit_id,
            new_value=visit_data,
            description=f"Customer visit recorded",
            performed_by=performed_by
        )
    
    @classmethod
    def log_reward_earned(cls, session, customer_id, balance_id, rule_id, old_balance, new_balance, performed_by=None):
        """Log a reward being earned.
        
        Args:
            session: Database session
            customer_id: UUID of the customer
            balance_id: UUID of the loyalty balance
            rule_id: UUID of the promotion rule
            old_balance: Dictionary of old balance state
            new_balance: Dictionary of new balance state
            performed_by: Optional user ID who performed the action
            
        Returns:
            PromotionAuditLog object or None
        """
        return cls.create_log(
            session,
            customer_id=customer_id,
            action_type='reward_earned',
            entity_type='loyalty_balance',
            entity_id=balance_id,
            old_value=old_balance,
            new_value=new_balance,
            description=f"Customer earned a reward for rule {rule_id}",
            performed_by=performed_by
        )
    
    @classmethod
    def log_reward_redeemed(cls, session, customer_id, redemption_id, redemption_data, performed_by=None):
        """Log a reward being redeemed.
        
        Args:
            session: Database session
            customer_id: UUID of the customer
            redemption_id: UUID of the redemption
            redemption_data: Dictionary of redemption data
            performed_by: Optional user ID who performed the action
            
        Returns:
            PromotionAuditLog object or None
        """
        return cls.create_log(
            session,
            customer_id=customer_id,
            action_type='reward_redeemed',
            entity_type='redemption',
            entity_id=redemption_id,
            new_value=redemption_data,
            description=f"Customer redeemed a reward",
            performed_by=performed_by
        )
    
    @classmethod
    def log_balance_updated(cls, session, customer_id, balance_id, old_balance, new_balance, performed_by=None):
        """Log a loyalty balance being updated.
        
        Args:
            session: Database session
            customer_id: UUID of the customer
            balance_id: UUID of the loyalty balance
            old_balance: Dictionary of old balance state
            new_balance: Dictionary of new balance state
            performed_by: Optional user ID who performed the action
            
        Returns:
            PromotionAuditLog object or None
        """
        return cls.create_log(
            session,
            customer_id=customer_id,
            action_type='balance_updated',
            entity_type='loyalty_balance',
            entity_id=balance_id,
            old_value=old_balance,
            new_value=new_balance,
            description=f"Loyalty balance updated",
            performed_by=performed_by
        )
    
    @classmethod
    def log_rule_created(cls, session, rule_id, rule_data, performed_by=None):
        """Log a promotion rule being created.
        
        Args:
            session: Database session
            rule_id: UUID of the promotion rule
            rule_data: Dictionary of rule data
            performed_by: Optional user ID who performed the action
            
        Returns:
            PromotionAuditLog object or None
        """
        return cls.create_log(
            session,
            action_type='rule_created',
            entity_type='promotion_rule',
            entity_id=rule_id,
            new_value=rule_data,
            description=f"Promotion rule created",
            performed_by=performed_by
        )
    
    @classmethod
    def log_rule_updated(cls, session, rule_id, old_rule, new_rule, performed_by=None):
        """Log a promotion rule being updated.
        
        Args:
            session: Database session
            rule_id: UUID of the promotion rule
            old_rule: Dictionary of old rule state
            new_rule: Dictionary of new rule state
            performed_by: Optional user ID who performed the action
            
        Returns:
            PromotionAuditLog object or None
        """
        return cls.create_log(
            session,
            action_type='rule_updated',
            entity_type='promotion_rule',
            entity_id=rule_id,
            old_value=old_rule,
            new_value=new_rule,
            description=f"Promotion rule updated",
            performed_by=performed_by
        )
    
    @classmethod
    def get_logs_by_customer(cls, session, customer_id, limit=100):
        """Get audit logs for a specific customer.
        
        Args:
            session: Database session
            customer_id: UUID of the customer
            limit: Maximum number of logs to return
            
        Returns:
            List of PromotionAuditLog objects
        """
        try:
            if isinstance(customer_id, str):
                customer_id = uuid.UUID(customer_id)
            
            logs = session.query(cls).filter_by(customer_id=customer_id).order_by(
                cls.timestamp.desc()
            ).limit(limit).all()
            return logs
        except Exception as e:
            app.logger.error(f"Error getting logs by customer: {e}")
            return []
    
    @classmethod
    def get_logs_by_entity(cls, session, entity_type, entity_id, limit=100):
        """Get audit logs for a specific entity.
        
        Args:
            session: Database session
            entity_type: Type of entity
            entity_id: UUID of the entity
            limit: Maximum number of logs to return
            
        Returns:
            List of PromotionAuditLog objects
        """
        try:
            if isinstance(entity_id, str):
                entity_id = uuid.UUID(entity_id)
            
            logs = session.query(cls).filter_by(
                entity_type=entity_type,
                entity_id=entity_id
            ).order_by(cls.timestamp.desc()).limit(limit).all()
            return logs
        except Exception as e:
            app.logger.error(f"Error getting logs by entity: {e}")
            return []
    
    @classmethod
    def get_logs_by_action_type(cls, session, action_type, start_date=None, end_date=None, limit=100):
        """Get audit logs by action type.
        
        Args:
            session: Database session
            action_type: Type of action
            start_date: Optional start date filter
            end_date: Optional end date filter
            limit: Maximum number of logs to return
            
        Returns:
            List of PromotionAuditLog objects
        """
        try:
            query = session.query(cls).filter_by(action_type=action_type)
            
            if start_date:
                query = query.filter(cls.timestamp >= start_date)
            
            if end_date:
                query = query.filter(cls.timestamp <= end_date)
            
            logs = query.order_by(cls.timestamp.desc()).limit(limit).all()
            return logs
        except Exception as e:
            app.logger.error(f"Error getting logs by action type: {e}")
            return []

