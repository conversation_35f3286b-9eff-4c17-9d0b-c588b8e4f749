"""Promotion rule model for loyalty program configuration."""

from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUI<PERSON>, JSONB
from flask import current_app as app
from datetime import datetime, date

class PromotionRule(db.Model):
    """Model representing a loyalty program promotion rule.
    
    Defines the rules for earning rewards (e.g., "10 visits = 1 free session").
    """
    __tablename__ = 'promotion_rules'
    
    rule_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    
    # Rule Definition
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text, nullable=True)
    rule_type = db.Column(db.String(50), nullable=False)  # visit_count, spend_amount, time_based, referral
    
    # Trigger Conditions
    trigger_value = db.Column(db.Integer, nullable=False)  # e.g., 10 visits
    trigger_period_days = db.Column(db.Integer, nullable=True)  # e.g., within 30 days (null = no time limit)
    
    # Reward Definition
    reward_type = db.Column(db.String(50), nullable=False)  # free_visit, discount_percentage, discount_amount, points
    reward_value = db.Column(db.Numeric(10, 2), nullable=False)  # e.g., 1 (free visit), 20 (20% discount)
    reward_description = db.Column(db.String(255), nullable=True)
    
    # Applicability
    applicable_customer_segments = db.Column(JSONB, nullable=True)  # ["VIP", "Regular"] or null for all
    applicable_visit_types = db.Column(JSONB, nullable=True)  # ["regular"] or null for all
    
    # Status and Validity
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    valid_from = db.Column(db.Date, nullable=True)
    valid_until = db.Column(db.Date, nullable=True)
    
    # Limits
    max_redemptions_per_customer = db.Column(db.Integer, nullable=True)  # null = unlimited
    reward_expiry_days = db.Column(db.Integer, nullable=True)  # Days until reward expires (null = no expiry)
    
    # Priority (for multiple rules)
    priority = db.Column(db.Integer, default=0, nullable=False)  # Higher priority rules processed first
    
    # Metadata
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())
    created_by = db.Column(UUID(as_uuid=True), nullable=True)
    
    # Relationships
    loyalty_balances = db.relationship('CustomerLoyaltyBalance', backref='rule', lazy='dynamic', cascade='all, delete-orphan')
    
    def __str__(self):
        """Return a string representation of the object."""
        return f"PromotionRule [rule_id={self.rule_id}, name={self.name}, type={self.rule_type}]"
    
    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "rule_id": str(self.rule_id),
            "name": self.name,
            "description": self.description,
            "rule_type": self.rule_type,
            "trigger_value": self.trigger_value,
            "trigger_period_days": self.trigger_period_days,
            "reward_type": self.reward_type,
            "reward_value": float(self.reward_value) if self.reward_value else None,
            "reward_description": self.reward_description,
            "applicable_customer_segments": self.applicable_customer_segments,
            "applicable_visit_types": self.applicable_visit_types,
            "is_active": self.is_active,
            "valid_from": self.valid_from.strftime('%Y-%m-%d') if self.valid_from else None,
            "valid_until": self.valid_until.strftime('%Y-%m-%d') if self.valid_until else None,
            "max_redemptions_per_customer": self.max_redemptions_per_customer,
            "reward_expiry_days": self.reward_expiry_days,
            "priority": self.priority,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }
    
    @classmethod
    def get_rule_by_id(cls, session, rule_id):
        """Get a promotion rule by rule_id.
        
        Args:
            session: Database session
            rule_id: UUID or string representation of rule_id
            
        Returns:
            PromotionRule object or None if not found
        """
        try:
            if isinstance(rule_id, str):
                try:
                    rule_id = uuid.UUID(rule_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {rule_id}")
                    return None
            
            rule = session.query(cls).filter_by(rule_id=rule_id).first()
            return rule
        except Exception as e:
            app.logger.error(f"Error getting promotion rule by ID: {e}")
            return None
    
    @classmethod
    def get_all_rules(cls, session, active_only=True):
        """Get all promotion rules.
        
        Args:
            session: Database session
            active_only: If True, only return active rules
            
        Returns:
            List of PromotionRule objects
        """
        try:
            query = session.query(cls)
            
            if active_only:
                query = query.filter_by(is_active=True)
            
            return query.order_by(cls.priority.desc(), cls.name).all()
        except Exception as e:
            app.logger.error(f"Error getting promotion rules: {e}")
            return []
    
    @classmethod
    def get_active_rules_for_customer(cls, session, customer_segment, current_date=None):
        """Get active promotion rules applicable to a customer segment.
        
        Args:
            session: Database session
            customer_segment: Customer segment (VIP, Regular, Student, Corporate)
            current_date: Date to check validity (defaults to today)
            
        Returns:
            List of PromotionRule objects
        """
        try:
            if current_date is None:
                current_date = date.today()
            
            query = session.query(cls).filter_by(is_active=True)
            
            # Check date validity
            query = query.filter(
                db.or_(
                    cls.valid_from.is_(None),
                    cls.valid_from <= current_date
                )
            ).filter(
                db.or_(
                    cls.valid_until.is_(None),
                    cls.valid_until >= current_date
                )
            )
            
            rules = query.order_by(cls.priority.desc()).all()
            
            # Filter by customer segment
            applicable_rules = []
            for rule in rules:
                if rule.applicable_customer_segments is None:
                    # Rule applies to all segments
                    applicable_rules.append(rule)
                elif customer_segment in rule.applicable_customer_segments:
                    # Rule applies to this segment
                    applicable_rules.append(rule)
            
            return applicable_rules
        except Exception as e:
            app.logger.error(f"Error getting active rules for customer: {e}")
            return []
    
    @classmethod
    def create_rule(cls, session, **kwargs):
        """Create a new promotion rule.
        
        Args:
            session: Database session
            **kwargs: Rule attributes
            
        Returns:
            PromotionRule object or None if creation failed
        """
        try:
            rule = cls(**kwargs)
            session.add(rule)
            session.commit()
            app.logger.info(f"Created new promotion rule: {rule.name} (ID: {rule.rule_id})")
            return rule
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating promotion rule: {e}")
            return None
    
    @classmethod
    def update_rule(cls, session, rule_id, **kwargs):
        """Update an existing promotion rule.
        
        Args:
            session: Database session
            rule_id: UUID of the rule to update
            **kwargs: Fields to update
            
        Returns:
            Updated PromotionRule object or None if update failed
        """
        try:
            rule = cls.get_rule_by_id(session, rule_id)
            if not rule:
                app.logger.error(f"Promotion rule not found: {rule_id}")
                return None
            
            for key, value in kwargs.items():
                if hasattr(rule, key) and key not in ['rule_id', 'created_at']:
                    setattr(rule, key, value)
            
            session.commit()
            app.logger.info(f"Updated promotion rule: {rule_id}")
            return rule
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error updating promotion rule: {e}")
            return None
    
    @classmethod
    def deactivate_rule(cls, session, rule_id):
        """Deactivate a promotion rule.
        
        Args:
            session: Database session
            rule_id: UUID of the rule to deactivate
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            rule = cls.get_rule_by_id(session, rule_id)
            if not rule:
                app.logger.error(f"Promotion rule not found: {rule_id}")
                return False
            
            rule.is_active = False
            session.commit()
            app.logger.info(f"Deactivated promotion rule: {rule_id}")
            return True
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error deactivating promotion rule: {e}")
            return False
    
    def is_applicable_to_customer(self, customer_segment):
        """Check if this rule is applicable to a customer segment.
        
        Args:
            customer_segment: Customer segment to check
            
        Returns:
            bool: True if applicable, False otherwise
        """
        if self.applicable_customer_segments is None:
            return True
        
        return customer_segment in self.applicable_customer_segments
    
    def is_valid_on_date(self, check_date=None):
        """Check if this rule is valid on a specific date.
        
        Args:
            check_date: Date to check (defaults to today)
            
        Returns:
            bool: True if valid, False otherwise
        """
        if check_date is None:
            check_date = date.today()
        
        if self.valid_from and check_date < self.valid_from:
            return False
        
        if self.valid_until and check_date > self.valid_until:
            return False
        
        return True

