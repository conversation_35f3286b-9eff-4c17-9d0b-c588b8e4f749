"""Reward redemption model for tracking when customers use their rewards."""

from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUID
from flask import current_app as app
from datetime import datetime, date

class RewardRedemption(db.Model):
    """Model representing a customer's redemption of an earned reward.
    
    Tracks when and how customers use their loyalty rewards.
    """
    __tablename__ = 'reward_redemptions'
    
    redemption_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    
    # Core Fields
    customer_id = db.Column(UUID(as_uuid=True), db.ForeignKey('customers.customer_id', ondelete='CASCADE'), nullable=False)
    rule_id = db.Column(UUID(as_uuid=True), db.ForeignKey('promotion_rules.rule_id', ondelete='CASCADE'), nullable=False)
    balance_id = db.Column(UUID(as_uuid=True), db.<PERSON><PERSON><PERSON>('customer_loyalty_balances.balance_id', ondelete='CASCADE'), nullable=True)
    
    # Redemption Details
    visit_id = db.Column(UUID(as_uuid=True), db.<PERSON>ey('customer_visits.visit_id', ondelete='SET NULL'), nullable=True)
    redemption_date = db.Column(db.Date, nullable=False)
    redemption_time = db.Column(db.DateTime, nullable=False)
    
    # Reward Details
    reward_type = db.Column(db.String(50), nullable=False)  # free_visit, discount_percentage, discount_amount, points
    reward_value = db.Column(db.Numeric(10, 2), nullable=False)
    reward_description = db.Column(db.String(255), nullable=True)
    
    # Status
    status = db.Column(db.String(50), default='completed', nullable=False)  # pending, completed, cancelled, expired
    
    # Processing
    redeemed_by = db.Column(UUID(as_uuid=True), nullable=True)  # User ID who processed the redemption
    redemption_method = db.Column(db.String(50), nullable=True)  # manual, automatic, self_service
    
    # Metadata
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())
    
    # Relationships
    customer = db.relationship('Customer', backref='redemptions', foreign_keys=[customer_id])
    rule = db.relationship('PromotionRule', backref='redemptions', foreign_keys=[rule_id])
    
    def __str__(self):
        """Return a string representation of the object."""
        return f"RewardRedemption [redemption_id={self.redemption_id}, customer_id={self.customer_id}, reward_type={self.reward_type}, status={self.status}]"
    
    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "redemption_id": str(self.redemption_id),
            "customer_id": str(self.customer_id),
            "customer": self.customer.to_dict() if self.customer else None,
            "rule_id": str(self.rule_id),
            "rule": self.rule.to_dict() if self.rule else None,
            "balance_id": str(self.balance_id) if self.balance_id else None,
            "visit_id": str(self.visit_id) if self.visit_id else None,
            "redemption_date": self.redemption_date.strftime('%Y-%m-%d') if self.redemption_date else None,
            "redemption_time": self.redemption_time.strftime('%Y-%m-%d %H:%M:%S') if self.redemption_time else None,
            "reward_type": self.reward_type,
            "reward_value": float(self.reward_value) if self.reward_value else None,
            "reward_description": self.reward_description,
            "status": self.status,
            "redeemed_by": str(self.redeemed_by) if self.redeemed_by else None,
            "redemption_method": self.redemption_method,
            "notes": self.notes,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }
    
    @classmethod
    def get_redemption_by_id(cls, session, redemption_id):
        """Get a redemption by redemption_id.
        
        Args:
            session: Database session
            redemption_id: UUID or string representation of redemption_id
            
        Returns:
            RewardRedemption object or None if not found
        """
        try:
            if isinstance(redemption_id, str):
                try:
                    redemption_id = uuid.UUID(redemption_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {redemption_id}")
                    return None
            
            redemption = session.query(cls).filter_by(redemption_id=redemption_id).first()
            return redemption
        except Exception as e:
            app.logger.error(f"Error getting redemption by ID: {e}")
            return None
    
    @classmethod
    def get_redemptions_by_customer(cls, session, customer_id, start_date=None, end_date=None, status=None):
        """Get all redemptions for a specific customer.
        
        Args:
            session: Database session
            customer_id: UUID of the customer
            start_date: Optional start date filter
            end_date: Optional end date filter
            status: Optional status filter
            
        Returns:
            List of RewardRedemption objects
        """
        try:
            if isinstance(customer_id, str):
                customer_id = uuid.UUID(customer_id)
            
            query = session.query(cls).filter_by(customer_id=customer_id)
            
            if start_date:
                query = query.filter(cls.redemption_date >= start_date)
            
            if end_date:
                query = query.filter(cls.redemption_date <= end_date)
            
            if status:
                query = query.filter_by(status=status)
            
            return query.order_by(cls.redemption_date.desc(), cls.redemption_time.desc()).all()
        except Exception as e:
            app.logger.error(f"Error getting redemptions by customer: {e}")
            return []
    
    @classmethod
    def get_redemptions_by_rule(cls, session, rule_id, start_date=None, end_date=None):
        """Get all redemptions for a specific promotion rule.
        
        Args:
            session: Database session
            rule_id: UUID of the promotion rule
            start_date: Optional start date filter
            end_date: Optional end date filter
            
        Returns:
            List of RewardRedemption objects
        """
        try:
            if isinstance(rule_id, str):
                rule_id = uuid.UUID(rule_id)
            
            query = session.query(cls).filter_by(rule_id=rule_id)
            
            if start_date:
                query = query.filter(cls.redemption_date >= start_date)
            
            if end_date:
                query = query.filter(cls.redemption_date <= end_date)
            
            return query.order_by(cls.redemption_date.desc()).all()
        except Exception as e:
            app.logger.error(f"Error getting redemptions by rule: {e}")
            return []
    
    @classmethod
    def create_redemption(cls, session, **kwargs):
        """Create a new reward redemption.
        
        Args:
            session: Database session
            **kwargs: Redemption attributes
            
        Returns:
            RewardRedemption object or None if creation failed
        """
        try:
            redemption = cls(**kwargs)
            session.add(redemption)
            session.commit()
            app.logger.info(f"Created new reward redemption: {redemption.redemption_id} for customer {redemption.customer_id}")
            return redemption
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating reward redemption: {e}")
            return None
    
    @classmethod
    def update_redemption(cls, session, redemption_id, **kwargs):
        """Update an existing redemption.
        
        Args:
            session: Database session
            redemption_id: UUID of the redemption to update
            **kwargs: Fields to update
            
        Returns:
            Updated RewardRedemption object or None if update failed
        """
        try:
            redemption = cls.get_redemption_by_id(session, redemption_id)
            if not redemption:
                app.logger.error(f"Redemption not found: {redemption_id}")
                return None
            
            for key, value in kwargs.items():
                if hasattr(redemption, key) and key not in ['redemption_id', 'created_at']:
                    setattr(redemption, key, value)
            
            session.commit()
            app.logger.info(f"Updated redemption: {redemption_id}")
            return redemption
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error updating redemption: {e}")
            return None
    
    @classmethod
    def cancel_redemption(cls, session, redemption_id):
        """Cancel a redemption.
        
        Args:
            session: Database session
            redemption_id: UUID of the redemption to cancel
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            redemption = cls.get_redemption_by_id(session, redemption_id)
            if not redemption:
                app.logger.error(f"Redemption not found: {redemption_id}")
                return False
            
            redemption.status = 'cancelled'
            session.commit()
            app.logger.info(f"Cancelled redemption: {redemption_id}")
            return True
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error cancelling redemption: {e}")
            return False
    
    @classmethod
    def count_redemptions_by_customer(cls, session, customer_id, rule_id=None, start_date=None, end_date=None):
        """Count redemptions for a customer.
        
        Args:
            session: Database session
            customer_id: UUID of the customer
            rule_id: Optional rule_id filter
            start_date: Optional start date filter
            end_date: Optional end date filter
            
        Returns:
            int: Number of redemptions
        """
        try:
            if isinstance(customer_id, str):
                customer_id = uuid.UUID(customer_id)
            
            query = session.query(cls).filter_by(customer_id=customer_id, status='completed')
            
            if rule_id:
                if isinstance(rule_id, str):
                    rule_id = uuid.UUID(rule_id)
                query = query.filter_by(rule_id=rule_id)
            
            if start_date:
                query = query.filter(cls.redemption_date >= start_date)
            
            if end_date:
                query = query.filter(cls.redemption_date <= end_date)
            
            return query.count()
        except Exception as e:
            app.logger.error(f"Error counting redemptions: {e}")
            return 0

