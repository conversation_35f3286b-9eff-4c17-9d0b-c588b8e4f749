"""Service and storage management models for customer operations."""

from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUI<PERSON>
from flask import current_app as app
from datetime import datetime, date

class Service(db.Model):
    """Model representing a service offered to customers.

    Services include sauna sessions, massage therapy, food, beverages, etc.
    """
    __tablename__ = 'services'

    service_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text, nullable=True)
    is_active = db.Column(db.<PERSON>an, default=True, nullable=False)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())
    created_by = db.Column(UUID(as_uuid=True), nullable=True)

    # Relationships
    prices = db.relationship('ServicePrice', backref='service', lazy='dynamic', cascade='all, delete-orphan')
    consumptions = db.relationship('CustomerServiceConsumption', backref='service', lazy='dynamic')


    def __str__(self):
        """Return a string representation of the object."""
        return f"Service [service_id={self.service_id}, name={self.name}]"

    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "service_id": str(self.service_id),
            "name": self.name,
            "description": self.description,
            "is_active": self.is_active,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            "created_by": str(self.created_by) if self.created_by else None
        }

    @classmethod
    def get_service_by_id(cls, session, service_id):
        """Get a service by service_id.

        Args:
            session: Database session
            service_id: UUID or string representation of service_id

        Returns:
            Service object or None if not found
        """
        try:
            if isinstance(service_id, str):
                try:
                    service_id = uuid.UUID(service_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {service_id}")
                    return None

            service = session.query(cls).filter_by(service_id=service_id).first()
            return service
        except Exception as e:
            app.logger.error(f"Error getting service by ID: {e}")
            return None

    @classmethod
    def get_all_services(cls, session, active_only=True):
        """Get all services with optional filtering.

        Args:
            session: Database session
            active_only: If True, only return active services

        Returns:
            List of Service objects
        """
        try:
            query = session.query(cls)

            if active_only:
                query = query.filter_by(is_active=True)

            return query.order_by(cls.name).all()
        except Exception as e:
            app.logger.error(f"Error getting services: {e}")
            return []

    @classmethod
    def create_service(cls, session, **kwargs):
        """Create a new service.

        Args:
            session: Database session
            **kwargs: Service attributes

        Returns:
            Service object or None if creation failed
        """
        try:
            # Validate required fields
            if not kwargs.get('name'):
                raise ValueError("Service name is required")

            service = cls(**kwargs)
            session.add(service)
            session.commit()
            app.logger.info(f"Created new service: {service.name}")
            return service
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating service: {e}")
            return None

    @classmethod
    def update_service(cls, session, service_id, **kwargs):
        """Update an existing service.

        Args:
            session: Database session
            service_id: UUID of the service to update
            **kwargs: Fields to update

        Returns:
            Updated Service object or None if update failed
        """
        try:
            service = cls.get_service_by_id(session, service_id)
            if not service:
                app.logger.error(f"Service not found: {service_id}")
                return None

            for key, value in kwargs.items():
                if hasattr(service, key) and key not in ['service_id', 'created_at']:
                    setattr(service, key, value)

            session.commit()
            app.logger.info(f"Updated service: {service.name}")
            return service
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error updating service: {e}")
            return None

    @classmethod
    def deactivate_service(cls, session, service_id):
        """Deactivate a service (soft delete).

        Args:
            session: Database session
            service_id: UUID of the service to deactivate

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            service = cls.get_service_by_id(session, service_id)
            if not service:
                app.logger.error(f"Service not found: {service_id}")
                return False

            service.is_active = False
            session.commit()
            app.logger.info(f"Deactivated service: {service.name}")
            return True
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error deactivating service: {e}")
            return False


class ServicePrice(db.Model):
    """Model representing the price of a service with historical tracking.

    Supports price changes over time while maintaining historical pricing data.
    """
    __tablename__ = 'service_prices'

    price_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    service_id = db.Column(UUID(as_uuid=True), db.ForeignKey('services.service_id', ondelete='CASCADE'), nullable=False)
    price_amount = db.Column(db.Numeric(10, 2), nullable=False)
    currency = db.Column(db.String(3), default='RWF', nullable=False)
    effective_from = db.Column(db.Date, nullable=False, default=date.today)
    effective_to = db.Column(db.Date, nullable=True)  # NULL means current price
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())
    created_by = db.Column(UUID(as_uuid=True), nullable=True)

    def __str__(self):
        """Return a string representation of the object."""
        return f"ServicePrice [price_id={self.price_id}, service_id={self.service_id}, amount={self.price_amount}]"

    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "price_id": str(self.price_id),
            "service_id": str(self.service_id),
            "service": self.service.to_dict() if self.service else None,
            "price_amount": float(self.price_amount) if self.price_amount else None,
            "currency": self.currency,
            "effective_from": self.effective_from.strftime('%Y-%m-%d') if self.effective_from else None,
            "effective_to": self.effective_to.strftime('%Y-%m-%d') if self.effective_to else None,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            "created_by": str(self.created_by) if self.created_by else None
        }

    @classmethod
    def get_price_by_id(cls, session, price_id):
        """Get a service price by price_id.

        Args:
            session: Database session
            price_id: UUID or string representation of price_id

        Returns:
            ServicePrice object or None if not found
        """
        try:
            if isinstance(price_id, str):
                try:
                    price_id = uuid.UUID(price_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {price_id}")
                    return None

            price = session.query(cls).filter_by(price_id=price_id).first()
            return price
        except Exception as e:
            app.logger.error(f"Error getting service price by ID: {e}")
            return None

    @classmethod
    def get_all_prices(cls, session, service_id=None, active_only=True):
        """Get all service prices with optional filtering.

        Args:
            session: Database session
            service_id: Optional service_id filter
            active_only: If True, only return current prices (effective_to is NULL)

        Returns:
            List of ServicePrice objects
        """
        try:
            query = session.query(cls)

            if service_id:
                if isinstance(service_id, str):
                    service_id = uuid.UUID(service_id)
                query = query.filter_by(service_id=service_id)

            if active_only:
                query = query.filter(cls.effective_to.is_(None))

            return query.order_by(cls.effective_from.desc()).all()
        except Exception as e:
            app.logger.error(f"Error getting service prices: {e}")
            return []

    @classmethod
    def create_price(cls, session, **kwargs):
        """Create a new service price.

        Args:
            session: Database session
            **kwargs: Price attributes

        Returns:
            ServicePrice object or None if creation failed
        """
        try:
            # Validate required fields
            if not kwargs.get('service_id'):
                raise ValueError("Service ID is required")
            if not kwargs.get('price_amount'):
                raise ValueError("Price amount is required")

            # Validate date logic
            effective_from = kwargs.get('effective_from', date.today())
            effective_to = kwargs.get('effective_to')

            if effective_to and effective_to < effective_from:
                raise ValueError("Effective to date cannot be before effective from date")

            # Set default effective_from if not provided
            if 'effective_from' not in kwargs:
                kwargs['effective_from'] = effective_from

            price = cls(**kwargs)
            session.add(price)
            session.commit()
            app.logger.info(f"Created new service price: {price.price_id} for service {price.service_id}")
            return price
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating service price: {e}")
            return None

    @classmethod
    def update_price(cls, session, price_id, **kwargs):
        """Update an existing service price.

        Args:
            session: Database session
            price_id: UUID of the price to update
            **kwargs: Fields to update

        Returns:
            Updated ServicePrice object or None if update failed
        """
        try:
            price = cls.get_price_by_id(session, price_id)
            if not price:
                app.logger.error(f"Service price not found: {price_id}")
                return None

            for key, value in kwargs.items():
                if hasattr(price, key) and key not in ['price_id', 'created_at']:
                    setattr(price, key, value)

            session.commit()
            app.logger.info(f"Updated service price: {price_id}")
            return price
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error updating service price: {e}")
            return None

    @classmethod
    def get_current_price(cls, session, service_id, date_check=None):
        """Get the current price for a service on a specific date.

        Args:
            session: Database session
            service_id: UUID of the service
            date_check: Date to check price for (defaults to today)

        Returns:
            ServicePrice object or None if not found
        """
        try:
            if isinstance(service_id, str):
                service_id = uuid.UUID(service_id)

            if date_check is None:
                date_check = date.today()

            price = session.query(cls).filter(
                cls.service_id == service_id,
                cls.effective_from <= date_check,
                (cls.effective_to.is_(None) | (cls.effective_to >= date_check))
            ).order_by(cls.effective_from.desc()).first()

            return price
        except Exception as e:
            app.logger.error(f"Error getting current price for service: {e}")
            return None

    @classmethod
    def expire_price(cls, session, price_id, effective_to_date=None):
        """Expire a price by setting its effective_to date.

        Args:
            session: Database session
            price_id: UUID of the price to expire
            effective_to_date: Date when price expires (defaults to today)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            price = cls.get_price_by_id(session, price_id)
            if not price:
                app.logger.error(f"Service price not found: {price_id}")
                return False

            if effective_to_date is None:
                effective_to_date = date.today()

            price.effective_to = effective_to_date
            session.commit()
            app.logger.info(f"Expired service price: {price_id}")
            return True
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error expiring service price: {e}")
            return False

    # Relationships
    consumptions = db.relationship('CustomerServiceConsumption', backref='price', lazy='dynamic')

    

class CustomerServiceConsumption(db.Model):
    """Model representing the consumption of a service by a customer during a visit.

    Tracks which services customers consume and links to pricing for billing.
    """
    __tablename__ = 'customer_service_consumption'

    consumption_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    visit_id = db.Column(UUID(as_uuid=True), db.ForeignKey('customer_visits.visit_id', ondelete='CASCADE'), nullable=True)
    customer_id = db.Column(UUID(as_uuid=True), db.ForeignKey('customers.customer_id', ondelete='CASCADE'), nullable=False)
    service_id = db.Column(UUID(as_uuid=True), db.ForeignKey('services.service_id', ondelete='RESTRICT'), nullable=False)
    price_id = db.Column(UUID(as_uuid=True), db.ForeignKey('service_prices.price_id', ondelete='RESTRICT'), nullable=False)
    quantity = db.Column(db.Integer, default=1, nullable=False)
    total_amount = db.Column(db.Numeric(10, 2), nullable=False)
    consumed_at = db.Column(db.DateTime, server_default=db.func.now())
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    def __str__(self):
        """Return a string representation of the object."""
        return f"CustomerServiceConsumption [consumption_id={self.consumption_id}, customer_id={self.customer_id}, service_id={self.service_id}]"

    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "consumption_id": str(self.consumption_id),
            "visit_id": str(self.visit_id) if self.visit_id else None,
            "customer_id": str(self.customer_id),
            "customer": self.customer.to_dict() if self.customer else None,
            "service_id": str(self.service_id),
            "service": self.service.to_dict() if self.service else None,
            "price_id": str(self.price_id),
            "price": self.price.to_dict() if self.price else None,
            "quantity": self.quantity,
            "total_amount": float(self.total_amount) if self.total_amount else None,
            "consumed_at": self.consumed_at.strftime('%Y-%m-%d %H:%M:%S') if self.consumed_at else None,
            "notes": self.notes,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

    @classmethod
    def get_consumption_by_id(cls, session, consumption_id):
        """Get a service consumption by consumption_id.

        Args:
            session: Database session
            consumption_id: UUID or string representation of consumption_id

        Returns:
            CustomerServiceConsumption object or None if not found
        """
        try:
            if isinstance(consumption_id, str):
                try:
                    consumption_id = uuid.UUID(consumption_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {consumption_id}")
                    return None

            consumption = session.query(cls).filter_by(consumption_id=consumption_id).first()
            return consumption
        except Exception as e:
            app.logger.error(f"Error getting service consumption by ID: {e}")
            return None

    @classmethod
    def get_all_consumptions(cls, session, customer_id=None, service_id=None, visit_id=None, start_date=None, end_date=None):
        """Get all service consumptions with optional filtering.

        Args:
            session: Database session
            customer_id: Optional customer_id filter
            service_id: Optional service_id filter
            visit_id: Optional visit_id filter
            start_date: Optional start date filter
            end_date: Optional end date filter

        Returns:
            List of CustomerServiceConsumption objects
        """
        try:
            query = session.query(cls)

            if customer_id:
                if isinstance(customer_id, str):
                    customer_id = uuid.UUID(customer_id)
                query = query.filter_by(customer_id=customer_id)

            if service_id:
                if isinstance(service_id, str):
                    service_id = uuid.UUID(service_id)
                query = query.filter_by(service_id=service_id)

            if visit_id:
                if isinstance(visit_id, str):
                    visit_id = uuid.UUID(visit_id)
                query = query.filter_by(visit_id=visit_id)

            if start_date:
                query = query.filter(cls.consumed_at >= start_date)

            if end_date:
                query = query.filter(cls.consumed_at <= end_date)

            return query.order_by(cls.consumed_at.desc()).all()
        except Exception as e:
            app.logger.error(f"Error getting service consumptions: {e}")
            return []

    @classmethod
    def create_consumption(cls, session, **kwargs):
        """Create a new service consumption record.

        Automatically determines the current price for the service based on consumption date.

        Args:
            session: Database session
            **kwargs: Consumption attributes (service_id required, price_id optional)

        Returns:
            CustomerServiceConsumption object or None if creation failed
        """
        try:
            # Validate required fields
            if not kwargs.get('customer_id'):
                raise ValueError("Customer ID is required")
            if not kwargs.get('service_id'):
                raise ValueError("Service ID is required")

            # Determine consumption date (use provided date or current time)
            consumed_at = kwargs.get('consumed_at')
            if consumed_at:
                if isinstance(consumed_at, str):
                    consumed_at = datetime.strptime(consumed_at, '%Y-%m-%d %H:%M:%S')
                consumption_date = consumed_at.date()
            else:
                consumption_date = date.today()
                kwargs['consumed_at'] = datetime.now()

            # Auto-determine price if not provided
            if not kwargs.get('price_id'):
                current_price = ServicePrice.get_current_price(
                    session,
                    kwargs['service_id'],
                    consumption_date
                )
                if not current_price:
                    raise ValueError(f"No active price found for service {kwargs['service_id']} on {consumption_date}")

                kwargs['price_id'] = current_price.price_id
                app.logger.info(f"Auto-determined price {current_price.price_id} for service {kwargs['service_id']}")

            # Get the price for total calculation
            price = ServicePrice.get_price_by_id(session, kwargs['price_id'])
            if not price:
                raise ValueError(f"Price with ID {kwargs['price_id']} not found")

            # Calculate total amount if not provided
            quantity = kwargs.get('quantity', 1)
            if 'total_amount' not in kwargs:
                kwargs['total_amount'] = quantity * price.price_amount

            consumption = cls(**kwargs)
            session.add(consumption)
            session.commit()
            app.logger.info(f"Created new service consumption: {consumption.consumption_id}")
            return consumption
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating service consumption: {e}")
            return None

    @classmethod
    def update_consumption(cls, session, consumption_id, **kwargs):
        """Update an existing service consumption.

        Args:
            session: Database session
            consumption_id: UUID of the consumption to update
            **kwargs: Fields to update

        Returns:
            Updated CustomerServiceConsumption object or None if update failed
        """
        try:
            consumption = cls.get_consumption_by_id(session, consumption_id)
            if not consumption:
                app.logger.error(f"Service consumption not found: {consumption_id}")
                return None

            for key, value in kwargs.items():
                if hasattr(consumption, key) and key not in ['consumption_id', 'created_at']:
                    setattr(consumption, key, value)

            session.commit()
            app.logger.info(f"Updated service consumption: {consumption_id}")
            return consumption
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error updating service consumption: {e}")
            return None

    @classmethod
    def delete_consumption(cls, session, consumption_id):
        """Delete a service consumption record.

        Args:
            session: Database session
            consumption_id: UUID of the consumption to delete

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            consumption = cls.get_consumption_by_id(session, consumption_id)
            if not consumption:
                app.logger.error(f"Service consumption not found: {consumption_id}")
                return False

            session.delete(consumption)
            session.commit()
            app.logger.info(f"Deleted service consumption: {consumption_id}")
            return True
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error deleting service consumption: {e}")
            return False

    @classmethod
    def create_bulk_consumption(cls, session, customer_id, services, **common_data):
        """Create multiple service consumption records in a single transaction.

        Args:
            session: Database session
            customer_id: UUID of the customer
            services: List of service dictionaries with service_id, quantity, notes, etc.
            **common_data: Common data for all consumptions (visit_id, consumed_at, etc.)

        Returns:
            dict: Result with created consumptions, total amount, and summary
        """
        try:
            if not customer_id:
                raise ValueError("Customer ID is required")
            if not services or not isinstance(services, list):
                raise ValueError("Services list is required and must be a list")
            if len(services) == 0:
                raise ValueError("At least one service must be provided")

            created_consumptions = []
            total_amount = 0
            service_summary = []

            # Validate all services first (fail fast)
            for i, service_data in enumerate(services):
                if not service_data.get('service_id'):
                    raise ValueError(f"Service {i+1}: service_id is required")

                # Validate service exists
                from application.Models.customers.services import Service
                service = Service.get_service_by_id(session, service_data['service_id'])
                if not service:
                    raise ValueError(f"Service {i+1}: Service with ID {service_data['service_id']} not found")

                if not service.is_active:
                    raise ValueError(f"Service {i+1}: Service '{service.name}' is not active")

            # Create consumption records
            for i, service_data in enumerate(services):
                try:
                    # Prepare consumption data
                    consumption_data = {
                        'customer_id': customer_id,
                        'service_id': service_data['service_id'],
                        'quantity': service_data.get('quantity', 1),
                        'notes': service_data.get('notes'),
                        **common_data  # Include visit_id, consumed_at, etc.
                    }

                    # Override with service-specific data if provided
                    if service_data.get('price_id'):
                        consumption_data['price_id'] = service_data['price_id']
                    if service_data.get('consumed_at'):
                        consumption_data['consumed_at'] = service_data['consumed_at']

                    consumption = cls.create_consumption(session=session, **consumption_data)

                    if not consumption:
                        raise ValueError(f"Failed to create consumption for service {i+1}")

                    created_consumptions.append(consumption)
                    total_amount += float(consumption.total_amount)

                    # Add to summary
                    service_summary.append({
                        'service_id': str(consumption.service_id),
                        'service_name': consumption.service.name if hasattr(consumption, 'service') else 'Unknown',
                        'quantity': consumption.quantity,
                        'unit_price': float(consumption.total_amount) / consumption.quantity,
                        'total_price': float(consumption.total_amount),
                        'consumption_id': str(consumption.consumption_id)
                    })

                except Exception as e:
                    app.logger.error(f"Error creating consumption for service {i+1}: {e}")
                    raise ValueError(f"Service {i+1}: {str(e)}")

            # All consumptions created successfully
            app.logger.info(f"Created {len(created_consumptions)} service consumptions for customer {customer_id}")

            return {
                'success': True,
                'consumptions': [c.to_dict() for c in created_consumptions],
                'summary': {
                    'total_services': len(created_consumptions),
                    'total_amount': round(total_amount, 2),
                    'services': service_summary
                },
                'customer_id': str(customer_id),
                'visit_id': str(common_data.get('visit_id')) if common_data.get('visit_id') else None,
                'consumed_at': common_data.get('consumed_at').isoformat() if common_data.get('consumed_at') else None
            }

        except Exception as e:
            session.rollback()
            app.logger.error(f"Error in bulk consumption creation: {e}")
            raise e

    @classmethod
    def get_consumption_total(cls, session, customer_id=None, visit_id=None, start_date=None, end_date=None):
        """Calculate total consumption amount with optional filtering.

        Args:
            session: Database session
            customer_id: Optional customer_id filter
            visit_id: Optional visit_id filter
            start_date: Optional start date filter
            end_date: Optional end date filter

        Returns:
            Decimal: Total consumption amount
        """
        try:
            query = session.query(db.func.sum(cls.total_amount))

            if customer_id:
                if isinstance(customer_id, str):
                    customer_id = uuid.UUID(customer_id)
                query = query.filter(cls.customer_id == customer_id)

            if visit_id:
                if isinstance(visit_id, str):
                    visit_id = uuid.UUID(visit_id)
                query = query.filter(cls.visit_id == visit_id)

            if start_date:
                query = query.filter(cls.consumed_at >= start_date)

            if end_date:
                query = query.filter(cls.consumed_at <= end_date)

            result = query.scalar()
            return result or 0
        except Exception as e:
            app.logger.error(f"Error calculating consumption total: {e}")
            return 0

    # Relationships
    customer = db.relationship('Customer', backref='service_consumptions', foreign_keys=[customer_id])
    visit = db.relationship('CustomerVisit', backref='service_consumptions', foreign_keys=[visit_id])
    
class StorageLocation(db.Model):
    """Model representing storage locations (lockers, boxes, cabinets) for customer items.

    Tracks available storage spaces and their current availability status.
    """
    __tablename__ = 'storage_locations'

    location_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    location_number = db.Column(db.String(50), unique=True, nullable=False)
    is_available = db.Column(db.Boolean, default=True, nullable=False)
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    def __str__(self):
        """Return a string representation of the object."""
        return f"StorageLocation [location_id={self.location_id}, number={self.location_number}"

    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "location_id": str(self.location_id),
            "location_number": self.location_number,
            "is_available": self.is_available,
            "notes": self.notes,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

    @classmethod
    def get_location_by_id(cls, session, location_id):
        """Get a storage location by location_id.

        Args:
            session: Database session
            location_id: UUID or string representation of location_id

        Returns:
            StorageLocation object or None if not found
        """
        try:
            if isinstance(location_id, str):
                try:
                    location_id = uuid.UUID(location_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {location_id}")
                    return None

            location = session.query(cls).filter_by(location_id=location_id).first()
            return location
        except Exception as e:
            app.logger.error(f"Error getting storage location by ID: {e}")
            return None

    @classmethod
    def get_all_locations(cls, session, available_only=True):
        """Get all storage locations with optional filtering.

        Args:
            session: Database session
            available_only: If True, only return available locations

        Returns:
            List of StorageLocation objects
        """
        try:
            query = session.query(cls)

            if available_only:
                query = query.filter_by(is_available=available_only)

            return query.order_by(cls.location_number).all()
        except Exception as e:
            app.logger.error(f"Error getting storage locations: {e}")
            return []

    @classmethod
    def create_location(cls, session, **kwargs):
        """Create a new storage location.

        Args:
            session: Database session
            **kwargs: Location attributes

        Returns:
            StorageLocation object or None if creation failed
        """
        try:
            # Validate required fields
            if not kwargs.get('location_number'):
                raise ValueError("Location number is required")

            # Check if location number already exists
            existing_location = session.query(cls).filter_by(location_number=kwargs['location_number']).first()
            if existing_location:
                raise ValueError(f"Location number {kwargs['location_number']} already exists")

            location = cls(**kwargs)
            session.add(location)
            session.commit()
            app.logger.info(f"Created new storage location: {location.location_number}")
            return location
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating storage location: {e}")
            return None

    @classmethod
    def update_location(cls, session, location_id, **kwargs):
        """Update an existing storage location.

        Args:
            session: Database session
            location_id: UUID of the location to update
            **kwargs: Fields to update

        Returns:
            Updated StorageLocation object or None if update failed
        """
        try:
            location = cls.get_location_by_id(session, location_id)
            if not location:
                app.logger.error(f"Storage location not found: {location_id}")
                return None

            for key, value in kwargs.items():
                if hasattr(location, key) and key not in ['location_id', 'created_at']:
                    setattr(location, key, value)

            session.commit()
            app.logger.info(f"Updated storage location: {location.location_number}")
            return location
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error updating storage location: {e}")
            return None

    @classmethod
    def delete_location(cls, session, location_id):
        """Delete a storage location.

        Args:
            session: Database session
            location_id: UUID of the location to delete

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            location = cls.get_location_by_id(session, location_id)
            if not location:
                app.logger.error(f"Storage location not found: {location_id}")
                return False

            session.delete(location)
            session.commit()
            app.logger.info(f"Deleted storage location: {location.location_number}")
            return True
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error deleting storage location: {e}")
            return False

    @classmethod
    def get_location_by_number(cls, session, location_number):
        """Get a storage location by its number.

        Args:
            session: Database session
            location_number: Location number

        Returns:
            StorageLocation object or None if not found
        """
        try:
            location = session.query(cls).filter_by(location_number=location_number).first()
            return location
        except Exception as e:
            app.logger.error(f"Error getting storage location by number: {e}")
            return None

    @classmethod
    def get_available_locations(cls, session):
        """Get all available storage locations.

        Args:
            session: Database session

        Returns:
            List of available StorageLocation objects
        """
        try:
            query = session.query(cls).filter_by(is_available=True)
            return query.order_by(cls.location_number).all()
        except Exception as e:
            app.logger.error(f"Error getting available storage locations: {e}")
            return []

    @classmethod
    def set_availability(cls, session, location_id, is_available):
        """Set the availability status of a storage location.

        Args:
            session: Database session
            location_id: UUID of the location
            is_available: Boolean availability status

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            location = cls.get_location_by_id(session, location_id)
            if not location:
                app.logger.error(f"Storage location not found: {location_id}")
                return False

            location.is_available = is_available
            session.commit()
            app.logger.info(f"Set storage location {location.location_number} availability to {is_available}")
            return True
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error setting storage location availability: {e}")
            return False

    # Relationships
    item_storages = db.relationship('CustomerItemStorage', backref='location', lazy='dynamic', cascade='all, delete-orphan')

class CustomerItemStorage(db.Model):
    """Model representing items stored by customers in storage locations.

    Tracks customer belongings stored in numbered boxes/lockers during visits.
    """
    __tablename__ = 'customer_item_storage'

    storage_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    customer_id = db.Column(UUID(as_uuid=True), db.ForeignKey('customers.customer_id', ondelete='CASCADE'), nullable=False)
    location_id = db.Column(UUID(as_uuid=True), db.ForeignKey('storage_locations.location_id', ondelete='RESTRICT'), nullable=False)
    visit_id = db.Column(UUID(as_uuid=True), db.ForeignKey('customer_visits.visit_id', ondelete='SET NULL'), nullable=True)
    items_description = db.Column(db.Text, nullable=False)
    stored_at = db.Column(db.DateTime, server_default=db.func.now())
    retrieved_at = db.Column(db.DateTime, nullable=True)
    status = db.Column(db.String(50), default='stored', nullable=False)  # 'stored', 'retrieved', 'abandoned'
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    def __str__(self):
        """Return a string representation of the object."""
        return f"CustomerItemStorage [storage_id={self.storage_id}, customer_id={self.customer_id}, location_id={self.location_id}, status={self.status}]"

    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "storage_id": str(self.storage_id),
            "customer_id": str(self.customer_id),
            "customer": self.customer.to_dict() if self.customer else None,
            "location_id": str(self.location_id),
            "location": self.location.to_dict() if self.location else None,
            "visit_id": str(self.visit_id) if self.visit_id else None,
            "visit": self.visit.to_dict() if self.visit else None,
            "items_description": self.items_description,
            "stored_at": self.stored_at.strftime('%Y-%m-%d %H:%M:%S') if self.stored_at else None,
            "retrieved_at": self.retrieved_at.strftime('%Y-%m-%d %H:%M:%S') if self.retrieved_at else None,
            "status": self.status,
            "notes": self.notes,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

    @classmethod
    def get_storage_by_id(cls, session, storage_id):
        """Get a customer item storage by storage_id.

        Args:
            session: Database session
            storage_id: UUID or string representation of storage_id

        Returns:
            CustomerItemStorage object or None if not found
        """
        try:
            if isinstance(storage_id, str):
                try:
                    storage_id = uuid.UUID(storage_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {storage_id}")
                    return None

            storage = session.query(cls).filter_by(storage_id=storage_id).first()
            return storage
        except Exception as e:
            app.logger.error(f"Error getting customer item storage by ID: {e}")
            return None

    @classmethod
    def get_all_storages(cls, session, customer_id=None, location_id=None, status=None, visit_id=None):
        """Get all customer item storages with optional filtering.

        Args:
            session: Database session
            customer_id: Optional customer_id filter
            location_id: Optional location_id filter
            status: Optional status filter
            visit_id: Optional visit_id filter

        Returns:
            List of CustomerItemStorage objects
        """
        try:
            query = session.query(cls)

            if customer_id:
                if isinstance(customer_id, str):
                    customer_id = uuid.UUID(customer_id)
                query = query.filter_by(customer_id=customer_id)

            if location_id:
                if isinstance(location_id, str):
                    location_id = uuid.UUID(location_id)
                query = query.filter_by(location_id=location_id)

            if status:
                query = query.filter_by(status=status)

            if visit_id:
                if isinstance(visit_id, str):
                    visit_id = uuid.UUID(visit_id)
                query = query.filter_by(visit_id=visit_id)

            return query.order_by(cls.stored_at.desc()).all()
        except Exception as e:
            app.logger.error(f"Error getting customer item storages: {e}")
            return []

    @classmethod
    def create_storage(cls, session, **kwargs):
        """Create a new customer item storage record.

        Args:
            session: Database session
            **kwargs: Storage attributes

        Returns:
            CustomerItemStorage object or None if creation failed
        """
        try:
            # Validate required fields
            if not kwargs.get('customer_id'):
                return  "Customer is required"
            if not kwargs.get('location_id'):
                return "Location is required"
            if not kwargs.get('items_description'):
                return "Items description is required"

            # Check if location is available
            from application.Models.customers.services import StorageLocation
            location = StorageLocation.get_location_by_id(session, kwargs['location_id'])
            if not location:
                return "Storage location not found"
            if not location.is_available:
                return "Storage location is not available"

            storage = cls(**kwargs)
            session.add(storage)

            # Mark location as unavailable
            location.is_available = False

            session.commit()
            app.logger.info(f"Created new customer item storage: {storage.storage_id}")
            return storage
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating customer item storage: {e}")
            return None

    @classmethod
    def update_storage(cls, session, storage_id, **kwargs):
        """Update an existing customer item storage.

        Args:
            session: Database session
            storage_id: UUID of the storage to update
            **kwargs: Fields to update

        Returns:
            Updated CustomerItemStorage object or None if update failed
        """
        try:
            storage = cls.get_storage_by_id(session, storage_id)
            if not storage:
                app.logger.error(f"Customer item storage not found: {storage_id}")
                return None

            for key, value in kwargs.items():
                if hasattr(storage, key) and key not in ['storage_id', 'created_at']:
                    setattr(storage, key, value)

            session.commit()
            app.logger.info(f"Updated customer item storage: {storage_id}")
            return storage
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error updating customer item storage: {e}")
            return None

    @classmethod
    def retrieve_items(cls, session, storage_id, retrieved_at=None):
        """Mark items as retrieved from storage.

        Args:
            session: Database session
            storage_id: UUID of the storage
            retrieved_at: Timestamp when items were retrieved (defaults to now)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            storage = cls.get_storage_by_id(session, storage_id)
            if not storage:
                app.logger.error(f"Customer item storage not found: {storage_id}")
                return False

            if storage.status != 'stored':
                app.logger.error(f"Items already retrieved or abandoned: {storage_id}")
                return False

            storage.status = 'retrieved'
            storage.retrieved_at = retrieved_at or datetime.now()

            # Mark location as available again
            storage.location.is_available = True

            session.commit()
            app.logger.info(f"Marked items as retrieved: {storage_id}")
            return True
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error retrieving items: {e}")
            return False

    @classmethod
    def abandon_items(cls, session, storage_id):
        """Mark items as abandoned.

        Args:
            session: Database session
            storage_id: UUID of the storage

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            storage = cls.get_storage_by_id(session, storage_id)
            if not storage:
                app.logger.error(f"Customer item storage not found: {storage_id}")
                return False

            storage.status = 'abandoned'

            # Mark location as available again
            storage.location.is_available = True

            session.commit()
            app.logger.info(f"Marked items as abandoned: {storage_id}")
            return True
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error abandoning items: {e}")
            return False

    @classmethod
    def get_active_storages_by_customer(cls, session, customer_id):
        """Get all active (stored) items for a customer.

        Args:
            session: Database session
            customer_id: UUID of the customer

        Returns:
            List of CustomerItemStorage objects with status 'stored'
        """
        try:
            if isinstance(customer_id, str):
                customer_id = uuid.UUID(customer_id)

            storages = session.query(cls).filter_by(
                customer_id=customer_id,
                status='stored'
            ).order_by(cls.stored_at.desc()).all()

            return storages
        except Exception as e:
            app.logger.error(f"Error getting active storages for customer: {e}")
            return []

    @classmethod
    def get_storages_by_location(cls, session, location_id, status=None):
        """Get all storages for a specific location.

        Args:
            session: Database session
            location_id: UUID of the location
            status: Optional status filter

        Returns:
            List of CustomerItemStorage objects
        """
        try:
            if isinstance(location_id, str):
                location_id = uuid.UUID(location_id)

            query = session.query(cls).filter_by(location_id=location_id)

            if status:
                query = query.filter_by(status=status)

            return query.order_by(cls.stored_at.desc()).all()
        except Exception as e:
            app.logger.error(f"Error getting storages by location: {e}")
            return []

    # Relationships
    customer = db.relationship('Customer', backref='item_storages', foreign_keys=[customer_id])
    visit = db.relationship('CustomerVisit', backref='item_storages', foreign_keys=[visit_id])