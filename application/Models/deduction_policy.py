from application.database import central_db as db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime
from flask import current_app

class DeductionPolicy(db.Model):
    """Model representing time-versioned deduction policies (stored in central database)."""
    __tablename__ = 'deduction_policies'

    policy_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    deduction_type_id = db.Column(UUID(as_uuid=True), db.Foreign<PERSON>ey('deduction_types.deduction_type_id'), nullable=False)
    country_id = db.Column(UUID(as_uuid=True), db.ForeignKey('countries.country_id'), nullable=False)
    employee_type_id = db.Column(UUID(as_uuid=True), db.<PERSON><PERSON>('employee_types.employee_type_id'), nullable=True)  # null = applies to all
    version_number = db.Column(db.Integer, nullable=False, default=1)
    employee_rate = db.Column(db.Numeric(5, 4), nullable=True)  # Employee contribution rate (e.g., 0.06 for 6%)
    employer_rate = db.Column(db.Numeric(5, 4), nullable=True)  # Employer contribution rate (e.g., 0.06 for 6%)
    effective_from = db.Column(db.Date, nullable=False)
    effective_to = db.Column(db.Date, nullable=True)  # null means currently active
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())
    created_by = db.Column(UUID(as_uuid=True), nullable=True)  # User who created this version
    change_reason = db.Column(db.Text, nullable=True)  # Reason for the policy change

    # Relationships
    deduction_type = db.relationship('DeductionType', backref=db.backref('policies', lazy='dynamic'))
    country = db.relationship('Country', backref=db.backref('deduction_policies', lazy='dynamic'))
    employee_type = db.relationship('EmployeeType', backref=db.backref('deduction_policies', lazy='dynamic'))

    # Index for efficient lookups
    __table_args__ = (
        db.Index('idx_deduction_policy_lookup', 'deduction_type_id', 'country_id', 'employee_type_id', 'effective_from', 'effective_to'),
    )

    def __str__(self):
        """Return a string representation of the object."""
        return f"DeductionPolicy [policy_id={self.policy_id}, type={self.deduction_type.name if self.deduction_type else None}, version={self.version_number}]"

    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "policy_id": str(self.policy_id),
            "deduction_type_id": str(self.deduction_type_id),
            "deduction_type_name": self.deduction_type.name if self.deduction_type else None,
            "deduction_type_code": self.deduction_type.code if self.deduction_type else None,
            "country_id": str(self.country_id),
            "country_name": self.country.name if self.country else None,
            "employee_type_id": str(self.employee_type_id) if self.employee_type_id else None,
            "employee_type_name": self.employee_type.name if self.employee_type else None,
            "version_number": self.version_number,
            "employee_rate": float(self.employee_rate) if self.employee_rate else None,
            "employer_rate": float(self.employer_rate) if self.employer_rate else None,
            "employee_rate_percentage": float(self.employee_rate * 100) if self.employee_rate else None,
            "employer_rate_percentage": float(self.employer_rate * 100) if self.employer_rate else None,
            "effective_from": self.effective_from.strftime('%Y-%m-%d') if self.effective_from else None,
            "effective_to": self.effective_to.strftime('%Y-%m-%d') if self.effective_to else None,
            "is_active": self.is_active,
            "change_reason": self.change_reason,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

    @classmethod
    def create_policy(cls, **kwargs):
        """Create a new deduction policy."""
        try:
            policy = cls(**kwargs)
            db.session.add(policy)
            db.session.commit()
            current_app.logger.info(f"Created deduction policy: {policy.deduction_type.name if policy.deduction_type else 'Unknown'} v{policy.version_number}")
            return policy
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error creating deduction policy: {e}")
            return None

    @classmethod
    def get_active_policy(cls, deduction_type_code, country_id, employee_type_id=None, date=None):
        """Get the active deduction policy for specific criteria on a given date."""
        if date is None:
            date = datetime.now().date()

        query = cls.query.join(cls.deduction_type).filter(
            cls.deduction_type.has(code=deduction_type_code),
            cls.country_id == country_id,
            cls.effective_from <= date,
            cls.is_active == True
        )

        # Handle employee type filter (null means applies to all employee types)
        if employee_type_id:
            query = query.filter(
                db.or_(
                    cls.employee_type_id == employee_type_id,
                    cls.employee_type_id.is_(None)
                )
            )
        else:
            query = query.filter(cls.employee_type_id.is_(None))

        # Handle effective_to (null means currently active)
        query = query.filter(
            db.or_(
                cls.effective_to.is_(None),
                cls.effective_to >= date
            )
        )

        # Order by specificity (employee-type-specific policies first, then general)
        query = query.order_by(cls.employee_type_id.desc().nullslast())

        return query.first()

    @classmethod
    def get_active_policies_for_country(cls, country_id, employee_type_id=None, date=None):
        """Get all active deduction policies for a country and employee type."""
        if date is None:
            date = datetime.now().date()

        query = cls.query.filter(
            cls.country_id == country_id,
            cls.effective_from <= date,
            cls.is_active == True
        )

        # Handle employee type filter
        if employee_type_id:
            query = query.filter(
                db.or_(
                    cls.employee_type_id == employee_type_id,
                    cls.employee_type_id.is_(None)
                )
            )

        # Handle effective_to
        query = query.filter(
            db.or_(
                cls.effective_to.is_(None),
                cls.effective_to >= date
            )
        )

        return query.all()

    @classmethod
    def get_policy_history(cls, deduction_type_code, country_id, employee_type_id=None):
        """Get the history of deduction policies for specific criteria."""
        query = cls.query.join(cls.deduction_type).filter(
            cls.deduction_type.has(code=deduction_type_code),
            cls.country_id == country_id
        )

        if employee_type_id:
            query = query.filter(cls.employee_type_id == employee_type_id)
        else:
            query = query.filter(cls.employee_type_id.is_(None))

        return query.order_by(cls.version_number.desc()).all()

    @classmethod
    def create_new_version(cls, existing_policy_id, new_effective_from, **kwargs):
        """Create a new version of an existing deduction policy."""
        try:
            # Get the existing policy
            existing_policy = cls.query.filter_by(policy_id=existing_policy_id).first()
            if not existing_policy:
                return None

            # Set end date for existing policy
            existing_policy.effective_to = new_effective_from
            
            # Get the next version number
            max_version = cls.query.filter(
                cls.deduction_type_id == existing_policy.deduction_type_id,
                cls.country_id == existing_policy.country_id,
                cls.employee_type_id == existing_policy.employee_type_id
            ).order_by(cls.version_number.desc()).first()
            
            next_version = (max_version.version_number + 1) if max_version else 1

            # Create new policy version
            new_policy_data = {
                'deduction_type_id': existing_policy.deduction_type_id,
                'country_id': existing_policy.country_id,
                'employee_type_id': existing_policy.employee_type_id,
                'version_number': next_version,
                'effective_from': new_effective_from,
                'effective_to': None,
                **kwargs
            }

            new_policy = cls.create_policy(**new_policy_data)
            
            if new_policy:
                db.session.commit()
                current_app.logger.info(f"Created new deduction policy version: v{next_version}")
                return new_policy
            else:
                db.session.rollback()
                return None

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error creating new deduction policy version: {e}")
            return None
