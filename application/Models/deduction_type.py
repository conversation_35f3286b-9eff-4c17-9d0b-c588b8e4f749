from application.database import central_db as db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from flask import current_app

class DeductionType(db.Model):
    """Model representing types of deductions (stored in central database)."""
    __tablename__ = 'deduction_types'

    deduction_type_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    country_id = db.Column(UUID(as_uuid=True), db.ForeignKey('countries.country_id'), nullable=False)
    name = db.Column(db.String(255), nullable=False)  # "Pension", "CBHI", "RAMA", "Occupational Hazard"
    code = db.Column(db.String(50), nullable=False)   # "PENSION", "CBHI", "RAMA", "OCC_HAZARD"
    description = db.Column(db.Text, nullable=True)
    has_employee_contribution = db.Column(db.<PERSON>, default=True, nullable=False)
    has_employer_contribution = db.Column(db.Bo<PERSON>, default=False, nullable=False)
    calculation_base = db.Column(db.String(50), nullable=False)  # "BASIC_SALARY", "GROSS_SALARY", "PENSIONABLE_SALARY"
    is_mandatory = db.Column(db.Boolean, default=True, nullable=False)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    # Relationship with Country model
    country = db.relationship('Country', backref=db.backref('deduction_types', lazy='dynamic'))

    # Unique constraint: code must be unique per country
    __table_args__ = (
        db.UniqueConstraint('country_id', 'code', name='uq_deduction_type_country_code'),
    )

    def __str__(self):
        """Return a string representation of the object."""
        return f"DeductionType [deduction_type_id={self.deduction_type_id}, name={self.name}, code={self.code}]"

    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "deduction_type_id": str(self.deduction_type_id),
            "country_id": str(self.country_id),
            "name": self.name,
            "code": self.code,
            "description": self.description,
            "has_employee_contribution": self.has_employee_contribution,
            "has_employer_contribution": self.has_employer_contribution,
            "calculation_base": self.calculation_base,
            "is_mandatory": self.is_mandatory,
            "is_active": self.is_active,
            "country_name": self.country.name if self.country else None,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

    @classmethod
    def create_deduction_type(cls, **kwargs):
        """Create a new deduction type."""
        try:
            deduction_type = cls(**kwargs)
            db.session.add(deduction_type)
            db.session.commit()
            current_app.logger.info(f"Created deduction type: {deduction_type.name} ({deduction_type.code})")
            return deduction_type
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error creating deduction type: {e}")
            return None

    @classmethod
    def get_by_country(cls, country_id):
        """Get all deduction types for a specific country."""
        return cls.query.filter_by(country_id=country_id, is_active=True).order_by(cls.name).all()

    @classmethod
    def get_by_code(cls, country_id, code):
        """Get deduction type by country and code."""
        return cls.query.filter_by(country_id=country_id, code=code, is_active=True).first()

    @classmethod
    def get_by_id(cls, deduction_type_id):
        """Get deduction type by ID."""
        return cls.query.filter_by(deduction_type_id=deduction_type_id, is_active=True).first()

    @classmethod
    def get_mandatory_for_country(cls, country_id):
        """Get all mandatory deduction types for a country."""
        return cls.query.filter_by(country_id=country_id, is_mandatory=True, is_active=True).all()

    @classmethod
    def update_deduction_type(cls, deduction_type_id, **kwargs):
        """Update a deduction type."""
        try:
            deduction_type = cls.query.filter_by(deduction_type_id=deduction_type_id).first()
            if not deduction_type:
                return None

            for key, value in kwargs.items():
                if hasattr(deduction_type, key):
                    setattr(deduction_type, key, value)

            db.session.commit()
            current_app.logger.info(f"Updated deduction type: {deduction_type.name}")
            return deduction_type
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error updating deduction type: {e}")
            return None

    @classmethod
    def deactivate_deduction_type(cls, deduction_type_id):
        """Deactivate a deduction type (soft delete)."""
        try:
            deduction_type = cls.query.filter_by(deduction_type_id=deduction_type_id).first()
            if not deduction_type:
                return False

            deduction_type.is_active = False
            db.session.commit()
            current_app.logger.info(f"Deactivated deduction type: {deduction_type.name}")
            return True
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error deactivating deduction type: {e}")
            return False
