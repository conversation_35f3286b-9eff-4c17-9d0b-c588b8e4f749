from application.database import central_db as db
import uuid
from sqlalchemy.dialects.postgresql import UUID
from datetime import datetime

class DocumentCategory(db.Model):
    """Simple document categories (no templates needed)."""
    __tablename__ = 'document_categories'

    category_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    category_name = db.Column(db.String(100), nullable=False)
    category_code = db.Column(db.String(50), unique=True, nullable=False)  # CONTRACT, ID, CERTIFICATE, etc.
    description = db.Column(db.Text)
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.now)

    def __str__(self):
        """Return a string representation of the object."""
        return f"DocumentCategory [category_id={self.category_id}, code={self.category_code}, name={self.category_name}]"

    def to_dict(self):
        """Dictionary representation."""
        return {
            "category_id": str(self.category_id),
            "category_name": self.category_name,
            "category_code": self.category_code,
            "description": self.description,
            "is_active": self.is_active,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None
        }

    @classmethod
    def get_all_active_categories(cls):
        """Get all active document categories."""
        return cls.query.filter_by(is_active=True).order_by(cls.category_name).all()

    @classmethod
    def get_category_by_code(cls, category_code):
        """Get category by code."""
        return cls.query.filter_by(category_code=category_code, is_active=True).first()

    @classmethod
    def get_category_by_id(cls, category_id):
        """Get category by ID."""
        try:
            if isinstance(category_id, str):
                category_id = uuid.UUID(category_id)
            return cls.query.filter_by(category_id=category_id, is_active=True).first()
        except ValueError:
            return None

    @classmethod
    def create_category(cls, category_code, category_name, description=None):
        """Create a new document category."""
        try:
            # Check if category code already exists
            existing = cls.get_category_by_code(category_code)
            if existing:
                return None, f"Category code '{category_code}' already exists"

            category = cls(
                category_code=category_code.upper(),
                category_name=category_name,
                description=description
            )
            db.session.add(category)
            db.session.commit()
            return category, None
        except Exception as e:
            db.session.rollback()
            return None, str(e)

    @classmethod
    def update_category(cls, category_id, **kwargs):
        """Update an existing category."""
        try:
            category = cls.get_category_by_id(category_id)
            if not category:
                return None, "Category not found"

            # Check for duplicate code if code is being updated
            if 'category_code' in kwargs and kwargs['category_code'] != category.category_code:
                existing = cls.get_category_by_code(kwargs['category_code'])
                if existing:
                    return None, f"Category code '{kwargs['category_code']}' already exists"

            # Update allowed fields
            allowed_fields = ['category_name', 'category_code', 'description', 'is_active']
            for field in allowed_fields:
                if field in kwargs:
                    if field == 'category_code':
                        setattr(category, field, kwargs[field].upper())
                    else:
                        setattr(category, field, kwargs[field])

            db.session.commit()
            return category, None
        except Exception as e:
            db.session.rollback()
            return None, str(e)

    @classmethod
    def deactivate_category(cls, category_id):
        """Deactivate a category (soft delete)."""
        try:
            category = cls.get_category_by_id(category_id)
            if not category:
                return False, "Category not found"

            category.is_active = False
            db.session.commit()
            return True, None
        except Exception as e:
            db.session.rollback()
            return False, str(e)

    @classmethod
    def create_default_categories(cls):
        """Create default document categories."""
        default_categories = [
            ("CONTRACT", "Employment Contract"),
            ("CERTIFICATE", "Certificate/Document"),
            ("ID", "Identification Document"),
            ("PASSPORT", "Passport"),
            ("VISA", "Visa/Work Permit"),
            ("QUALIFICATION", "Qualification/Credential"),
            ("MEDICAL", "Medical Document"),
            ("LEGAL", "Legal Document"),
            ("OTHER", "Other Document")
        ]

        created = []
        errors = []

        for code, name in default_categories:
            if not cls.get_category_by_code(code):
                category, error = cls.create_category(code, name)
                if category:
                    created.append(category)
                else:
                    errors.append(f"Failed to create {code}: {error}")

        return created, errors

    @classmethod
    def get_categories_list(cls):
        """Get simplified list of active categories for dropdowns."""
        categories = cls.get_all_active_categories()
        return [
            {
                "code": cat.category_code,
                "name": cat.category_name,
                "description": cat.description
            }
            for cat in categories
        ]

    @classmethod
    def search_categories(cls, search_term=None, include_inactive=False):
        """Search categories by name or code."""
        query = cls.query

        if not include_inactive:
            query = query.filter_by(is_active=True)

        if search_term:
            search_pattern = f"%{search_term}%"
            query = query.filter(
                db.or_(
                    cls.category_name.ilike(search_pattern),
                    cls.category_code.ilike(search_pattern),
                    cls.description.ilike(search_pattern)
                )
            )

        return query.order_by(cls.category_name).all()
