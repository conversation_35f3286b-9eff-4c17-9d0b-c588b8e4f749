from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from flask import current_app as app
from datetime import datetime, date
import json

class CompanyDocument(db.Model):
    """Document metadata for company-specific files."""
    __tablename__ = 'company_documents'

    document_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    employee_id = db.Column(UUID(as_uuid=True), db.<PERSON>ey('employees.employee_id'))
    # Folder organization
    folder_id = db.Column(UUID(as_uuid=True), db.ForeignKey('document_folders.folder_id'))

    # Document info
    document_name = db.Column(db.String(255), nullable=False)
    document_description = db.Column(db.Text)
    document_category = db.Column(db.String(50), nullable=False)  # CONTRACT, ID, CERTIFICATE, etc.

    # File info
    original_filename = db.Column(db.String(255), nullable=False)
    file_size_bytes = db.Column(db.BigInteger, nullable=False)
    file_type = db.Column(db.String(100))
    mime_type = db.Column(db.String(100))

    # Storage info (following your existing pattern)
    storage_provider = db.Column(db.String(100), default='LOCAL')  # LOCAL, S3, AZURE_BLOB
    storage_bucket = db.Column(db.String(255))
    storage_key = db.Column(db.String(500))
    storage_url = db.Column(db.Text)

    # Document lifecycle
    expiry_date = db.Column(db.Date)  # NULL for permanent documents
    is_confidential = db.Column(db.Boolean, default=False)
    access_level = db.Column(db.String(50), default='STANDARD')  # STANDARD, CONFIDENTIAL

    # Status
    status = db.Column(db.String(50), default='ACTIVE')  # ACTIVE, ARCHIVED, DELETED
    uploaded_by = db.Column(UUID(as_uuid=True), nullable=False)
    uploaded_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # Search
    search_tags = db.Column(ARRAY(db.Text()))
    # Relationships
    employee = db.relationship('Employee', backref=db.backref('documents', lazy='dynamic'))
    folder = db.relationship('DocumentFolder', back_populates='documents')

    def __str__(self):
        """Return a string representation of the object."""
        return f"CompanyDocument [document_id={self.document_id}, name={self.document_name}, company={self.company_id}]"

    def to_dict(self):
        """Dictionary representation."""
        return {
            "document_id": str(self.document_id),
            "employee_id": str(self.employee_id) if self.employee_id else None,
            "employee_name": f"{self.employee.first_name} {self.employee.last_name}" if self.employee else None,
            "folder_id": str(self.folder_id) if self.folder_id else None,
            "folder_name": self.folder.folder_name if self.folder else None,
            "folder_path": self.folder.get_full_path() if self.folder else None,
            "document_name": self.document_name,
            "document_description": self.document_description,
            "document_category": self.document_category,
            "original_filename": self.original_filename,
            "file_size_bytes": self.file_size_bytes,
            "file_size_mb": round(self.file_size_bytes / (1024 * 1024), 2) if self.file_size_bytes else 0,
            "file_type": self.file_type,
            "mime_type": self.mime_type,
            "storage_provider": self.storage_provider,
            "storage_bucket": self.storage_bucket,
            "storage_key": self.storage_key,
            "storage_url": self.storage_url,
            "expiry_date": self.expiry_date.strftime('%Y-%m-%d') if self.expiry_date else None,
            "is_expired": self.is_expired(),
            "days_until_expiry": self.get_days_until_expiry(),
            "is_confidential": self.is_confidential,
            "access_level": self.access_level,
            "status": self.status,
            "uploaded_by": str(self.uploaded_by),
            "uploaded_at": self.uploaded_at.strftime('%Y-%m-%d %H:%M:%S') if self.uploaded_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            "download_url": self.get_download_url()
        }

    def is_expired(self):
        """Check if document is expired."""
        if not self.expiry_date:
            return False
        return date.today() > self.expiry_date

    def get_days_until_expiry(self):
        """Get days until document expires."""
        if not self.expiry_date:
            return None
        return (self.expiry_date - date.today()).days

    def get_download_url(self):
        """Generate download URL based on storage provider."""
        # This will integrate with your DocumentStorageService
        return f"/api/documents/{self.document_id}/download"

    @classmethod
    def get_documents_by_company(cls, session,  status='ACTIVE'):
        """Get all documents for a company."""
        return session.query(cls).filter_by(
            status=status
        ).order_by(cls.uploaded_at.desc()).all()

    @classmethod
    def get_documents_by_employee(cls, session, employee_id):
        """Get documents for specific employee."""
        return session.query(cls).filter_by(
            employee_id=employee_id,
            status='ACTIVE'
        ).order_by(cls.uploaded_at.desc()).all()

    @classmethod
    def get_documents_by_folder(cls, session, folder_id):
        """Get documents in a specific folder."""
        return session.query(cls).filter_by(
            folder_id=folder_id,
            status='ACTIVE'
        ).order_by(cls.uploaded_at.desc()).all()

    @classmethod
    def get_documents_by_folder_recursive(cls, session, folder_id):
        """Get documents in a folder and all its subfolders."""
        from application.Models.documents.document_folder import DocumentFolder

        # Get all subfolder IDs recursively
        folder_ids = [folder_id]
        to_process = [folder_id]

        while to_process:
            current_id = to_process.pop(0)
            subfolders = session.query(DocumentFolder.folder_id).filter_by(
                parent_folder_id=current_id
            ).all()

            for subfolder in subfolders:
                folder_ids.append(subfolder.folder_id)
                to_process.append(subfolder.folder_id)

        return session.query(cls).filter(
            cls.folder_id.in_(folder_ids),
            cls.status == 'ACTIVE'
        ).order_by(cls.uploaded_at.desc()).all()

    @classmethod
    def get_documents_by_category(cls, session, category):
        """Get documents by category."""
        return session.query(cls).filter_by(
            document_category=category,
            status='ACTIVE'
        ).order_by(cls.uploaded_at.desc()).all()

    @classmethod
    def get_expiring_documents(cls, session, days_ahead=30):
        """Get documents expiring soon."""
        from datetime import timedelta
        expiry_threshold = date.today() + timedelta(days=days_ahead)

        return session.query(cls).filter(
            cls.expiry_date.isnot(None),
            cls.expiry_date <= expiry_threshold,
            cls.status == 'ACTIVE'
        ).order_by(cls.expiry_date).all()

    @classmethod
    def search_documents(cls, session, search_term=None, category=None,
                        employee_id=None, folder_id=None, include_subfolders=False, status='ACTIVE'):
        """Search documents with filters."""
        query = session.query(cls)

        if status:
            query = query.filter_by(status=status)

        if search_term:
            # Search in document name, description, and tags
            search_pattern = f"%{search_term}%"
            query = query.filter(
                db.or_(
                    cls.document_name.ilike(search_pattern),
                    cls.document_description.ilike(search_pattern),
                    cls.original_filename.ilike(search_pattern), # Add original_filename
                    cls.search_tags.contains([search_term])
                )
            )

        if category:
            query = query.filter_by(document_category=category)

        if employee_id:
            query = query.filter_by(employee_id=employee_id)

        if folder_id:
            if include_subfolders:
                # Get documents from folder and all subfolders
                documents = cls.get_documents_by_folder_recursive(session, folder_id)
                return documents
            else:
                query = query.filter_by(folder_id=folder_id)

        return query.order_by(cls.uploaded_at.desc()).all()

    @classmethod
    def move_document_to_folder(cls, session, document_id, new_folder_id):
        """Move document to a different folder."""
        try:
            document = cls.get_document_by_id(session, document_id)
            if not document:
                return None, "Document not found"

            # If new_folder_id is provided, validate the folder exists
            if new_folder_id:
                from application.Models.documents.document_folder import DocumentFolder
                folder = session.query(DocumentFolder).filter_by(
                    folder_id=new_folder_id
                ).first()
                if not folder:
                    return None, "Target folder not found"

            document.folder_id = new_folder_id
            document.updated_at = datetime.now()
            session.commit()

            return document, None

        except Exception as e:
            session.rollback()
            return None, str(e)

    @classmethod
    def move_documents_to_folder(cls, session, document_ids, new_folder_id):
        """Move multiple documents to a folder."""
        try:
            # Validate target folder if provided
            if new_folder_id:
                from application.Models.documents.document_folder import DocumentFolder
                folder = session.query(DocumentFolder).filter_by(
                    folder_id=new_folder_id
                ).first()
                if not folder:
                    return 0, "Target folder not found"

            # Update documents
            updated_count = session.query(cls).filter(
                cls.document_id.in_(document_ids)
            ).update({
                'folder_id': new_folder_id,
                'updated_at': datetime.now()
            })

            session.commit()
            return updated_count, None

        except Exception as e:
            session.rollback()
            return 0, str(e)

    @classmethod
    def create_document(cls, session, **kwargs):
        """Create new document record."""
        try:
            document = cls(**kwargs)
            session.add(document)
            session.commit()
            app.logger.info(f"Created document: {document.document_name}")
            return document, None
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating document: {e}")
            return None, str(e)

    @classmethod
    def get_document_by_id(cls, session, document_id):
        """Get document by ID with company isolation."""
        return session.query(cls).filter_by(
            document_id=document_id
        ).first()

    @classmethod
    def update_document(cls, session, document_id, **kwargs):
        """Update document."""
        try:
            document = cls.get_document_by_id(session, document_id)
            if not document:
                return None, "Document not found"

            # Update allowed fields
            allowed_fields = [
                'document_name', 'document_description', 'document_category',
                'expiry_date', 'is_confidential', 'access_level', 'search_tags'
            ]

            for field in allowed_fields:
                if field in kwargs:
                    setattr(document, field, kwargs[field])

            document.updated_at = datetime.now()
            session.commit()
            return document, None
        except Exception as e:
            session.rollback()
            return None, str(e)

    @classmethod
    def archive_document(cls, session, document_id):
        """Archive a document."""
        try:
            document = cls.get_document_by_id(session, document_id)
            if not document:
                return False, "Document not found"

            document.status = 'ARCHIVED'
            document.updated_at = datetime.now()
            session.commit()
            return True, None
        except Exception as e:
            session.rollback()
            return False, str(e)

    @classmethod
    def delete_document(cls, session, document_id):
        """Soft delete a document."""
        try:
            document = cls.get_document_by_id(session, document_id)
            if not document:
                return False, "Document not found"

            document.status = 'DELETED'
            document.updated_at = datetime.now()
            session.commit()
            return True, None
        except Exception as e:
            session.rollback()
            return False, str(e)

    @classmethod
    def get_document_statistics(cls, session, date_from=None, date_to=None):
        """Get document statistics for company."""
        try:
            query = session.query(cls).filter_by(status='ACTIVE')

            if date_from:
                query = query.filter(cls.uploaded_at >= date_from)
            if date_to:
                query = query.filter(cls.uploaded_at <= date_to)

            documents = query.all()
            total_size = sum(doc.file_size_bytes or 0 for doc in documents)

            # Category breakdown
            category_breakdown = {}
            for doc in documents:
                cat = doc.document_category
                category_breakdown[cat] = category_breakdown.get(cat, 0) + 1

            # Expiring soon (30 days)
            expiring_soon = len(cls.get_expiring_documents(session, 30))

            return {
                "total_documents": len(documents),
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "category_breakdown": category_breakdown,
                "expiring_soon": expiring_soon,
                "avg_document_size_mb": round(total_size / (1024 * 1024) / len(documents), 2) if documents else 0
            }
        except Exception as e:
            app.logger.error(f"Error getting document statistics: {e}")
            return {
                "total_documents": 0,
                "total_size_mb": 0,
                "category_breakdown": {},
                "expiring_soon": 0,
                "avg_document_size_mb": 0
            }
