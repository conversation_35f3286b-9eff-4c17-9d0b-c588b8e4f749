from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from flask import current_app as app
from datetime import datetime

class DocumentFolder(db.Model):
    """Hierarchical folder structure for document organization."""
    __tablename__ = 'document_folders'

    folder_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    folder_name = db.Column(db.String(255), nullable=False)

    # Hierarchical structure
    parent_folder_id = db.Column(UUID(as_uuid=True), db.ForeignKey('document_folders.folder_id'))

    # Folder metadata
    description = db.Column(db.Text)
    color = db.Column(db.String(7))  # Hex color code for UI
    icon = db.Column(db.String(50))  # Icon identifier for UI

    # Permissions and access
    is_private = db.Column(db.<PERSON>, default=False)
    allowed_roles = db.Column(ARRAY(db.Text))  
    created_by = db.Column(UUID(as_uuid=True), nullable=False)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # Relationships
    # Self-referencing hierarchy
    parent = db.relationship(
        'DocumentFolder',
        remote_side=[folder_id],
        backref=db.backref('subfolders', lazy='dynamic')
    )

    # Relationship with CompanyDocument
    documents = db.relationship('CompanyDocument', back_populates='folder')

    def __str__(self):
        """Return a string representation of the object."""
        return f"DocumentFolder [folder_id={self.folder_id}, name={self.folder_name}]"

    def to_dict(self):
        """Dictionary representation."""
        return {
            "folder_id": str(self.folder_id),
            "folder_name": self.folder_name,
            "parent_folder_id": str(self.parent_folder_id) if self.parent_folder_id else None,
            "description": self.description,
            "color": self.color,
            "icon": self.icon,
            "is_private": self.is_private,
            "allowed_roles": self.allowed_roles or [],
            "created_by": str(self.created_by),
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            "full_path": self.get_full_path(),
            "depth": self.get_depth(),
            "has_subfolders": self.has_subfolders(),
            "document_count": self.get_document_count()
        }

    def get_full_path(self):
        """Get the full path of the folder."""
        if not self.parent_folder_id:
            return f"/{self.folder_name}"

        path_parts = []
        current = self

        while current:
            path_parts.insert(0, current.folder_name)
            current = current.parent

        return "/" + "/".join(path_parts)

    def get_depth(self):
        """Get the depth level of the folder in the hierarchy."""
        depth = 0
        current = self.parent
        while current:
            depth += 1
            current = current.parent
        return depth

    def has_subfolders(self):
        """Check if folder has subfolders."""
        return self.subfolders.count() > 0

    def get_document_count(self):
        """Get count of documents in this folder."""
        return self.documents.count() if self.documents else 0

    def can_access(self, user):
        """Check if user can access this folder."""
        # If folder is not private, everyone can access
        if not self.is_private:
            return True

        # Check user's role against allowed roles
        user_role = user.get('role', 'employee')
        return user_role in (self.allowed_roles or [])

    @classmethod
    def create_folder(cls, session, folder_name, parent_folder_id=None,
                     created_by=None, description=None, color=None, icon=None,
                     is_private=False, allowed_roles=None):
        """Create a new folder."""
        try:
            # Validate parent folder exists and belongs to same company
            if parent_folder_id:
                parent = session.query(cls).filter_by(
                    folder_id=parent_folder_id,
                ).first()
                if not parent:
                    return None, "Parent folder not found"

            # Check for duplicate names in the same parent
            existing = session.query(cls).filter_by(
                folder_name=folder_name,
                parent_folder_id=parent_folder_id
            ).first()

            if existing:
                return None, f"Folder '{folder_name}' already exists in this location"

            folder = cls(
                folder_name=folder_name,
                parent_folder_id=parent_folder_id,
                description=description,
                color=color,
                icon=icon,
                is_private=is_private,
                allowed_roles=allowed_roles or ['hr', 'admin', 'employee'],
                created_by=created_by
            )

            session.add(folder)
            session.commit()

            app.logger.info(f"Created folder: {folder_name} for company ")
            return folder, None

        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating folder: {e}")
            return None, str(e)

    @classmethod
    def get_folder_tree(cls, session, parent_folder_id=None, include_documents=False):
        """Get folder tree structure."""
        try:
            # Get root folders or subfolders
            query = session.query(cls).filter_by(
                parent_folder_id=parent_folder_id
            ).order_by(cls.folder_name)

            folders = query.all()
            tree = []

            for folder in folders:
                folder_dict = folder.to_dict()

                # Include documents if requested
                if include_documents:
                    folder_dict['documents'] = [
                        doc.to_dict() for doc in folder.documents
                    ]

                # Recursively get subfolders
                folder_dict['subfolders'] = cls.get_folder_tree(
                    session, folder.folder_id, include_documents
                )

                tree.append(folder_dict)

            return tree

        except Exception as e:
            app.logger.error(f"Error getting folder tree: {e}")
            return []

    @classmethod
    def get_folder_by_id(cls, session, folder_id):
        """Get folder by ID with company isolation."""
        return session.query(cls).filter_by(
            folder_id=folder_id
        ).first()

    @classmethod
    def update_folder(cls, session, folder_id, **kwargs):
        """Update folder details."""
        try:
            folder = cls.get_folder_by_id(session, folder_id)
            if not folder:
                return None, "Folder not found"

            # Validate parent folder if being changed
            if 'parent_folder_id' in kwargs and kwargs['parent_folder_id']:
                parent = session.query(cls).filter_by(
                    folder_id=kwargs['parent_folder_id'],
                ).first()
                if not parent:
                    return None, "Parent folder not found"

                # Prevent circular references
                if str(kwargs['parent_folder_id']) == str(folder_id):
                    return None, "Cannot set folder as its own parent"

            # Check for duplicate names if name is being changed
            if 'folder_name' in kwargs:
                existing = session.query(cls).filter_by(
                    folder_name=kwargs['folder_name'],
                    parent_folder_id=kwargs.get('parent_folder_id', folder.parent_folder_id)
                ).filter(cls.folder_id != folder_id).first()

                if existing:
                    return None, f"Folder '{kwargs['folder_name']}' already exists in this location"

            # Update allowed fields
            allowed_fields = [
                'folder_name', 'parent_folder_id', 'description',
                'color', 'icon', 'is_private', 'allowed_roles'
            ]

            for field in allowed_fields:
                if field in kwargs:
                    setattr(folder, field, kwargs[field])

            folder.updated_at = datetime.now()
            session.commit()

            return folder, None

        except Exception as e:
            session.rollback()
            return None, str(e)

    @classmethod
    def delete_folder(cls, session, folder_id, force_delete=False):
        """Delete folder and optionally its contents."""
        try:
            folder = cls.get_folder_by_id(session, folder_id)
            if not folder:
                return False, "Folder not found"

            # Check if folder has contents
            has_subfolders = folder.has_subfolders()
            document_count = folder.get_document_count()

            if (has_subfolders or document_count > 0) and not force_delete:
                return False, f"Folder contains {document_count} documents and {folder.subfolders.count()} subfolders. Use force_delete=True to delete anyway."

            # If force deleting, move contents to parent or root
            if force_delete and (has_subfolders or document_count > 0):
                # Move subfolders to parent
                for subfolder in folder.subfolders:
                    subfolder.parent_folder_id = folder.parent_folder_id

                # Move documents to parent (or remove folder reference)
                for document in folder.documents:
                    document.folder_id = folder.parent_folder_id

            # Delete the folder
            session.delete(folder)
            session.commit()

            app.logger.info(f"Deleted folder: {folder_id}")
            return True, None

        except Exception as e:
            session.rollback()
            app.logger.error(f"Error deleting folder: {e}")
            return False, str(e)

    @classmethod
    def move_folder(cls, session, folder_id, new_parent_id):
        """Move folder to a new parent."""
        try:
            folder = cls.get_folder_by_id(session, folder_id)
            if not folder:
                return False, "Source folder not found"

            # Validate destination
            if new_parent_id:
                parent = session.query(cls).filter_by(
                    folder_id=new_parent_id
                ).first()
                if not parent:
                    return False, "Destination folder not found"

                # Prevent circular references
                if str(new_parent_id) == str(folder_id):
                    return False, "Cannot move folder into itself"

            # Check for name conflicts
            existing = session.query(cls).filter_by(
                folder_name=folder.folder_name,
                parent_folder_id=new_parent_id
            ).filter(cls.folder_id != folder_id).first()

            if existing:
                return False, f"A folder named '{folder.folder_name}' already exists in the destination"

            folder.parent_folder_id = new_parent_id
            folder.updated_at = datetime.now()
            session.commit()

            return True, None

        except Exception as e:
            session.rollback()
            return False, str(e)

    @classmethod
    def get_folder_path(cls, session, folder_id):
        """Get the full path components for a folder."""
        try:
            folder = cls.get_folder_by_id(session, folder_id)
            if not folder:
                return []

            path = []
            current = folder

            while current:
                path.insert(0, {
                    'folder_id': str(current.folder_id),
                    'folder_name': current.folder_name
                })
                current = current.parent

            return path

        except Exception as e:
            app.logger.error(f"Error getting folder path: {e}")
            return []

    @classmethod
    def search_folders(cls, session, search_term=None, include_private=False):
        """Search folders by name."""
        try:
            query = session.query(cls)

            if not include_private:
                query = query.filter_by(is_private=False)

            if search_term:
                search_pattern = f"%{search_term}%"
                query = query.filter(cls.folder_name.ilike(search_pattern))

            return query.order_by(cls.folder_name).all()

        except Exception as e:
            app.logger.error(f"Error searching folders: {e}")
            return []
