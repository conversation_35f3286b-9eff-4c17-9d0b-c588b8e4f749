from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUID
from flask import current_app as app
from datetime import datetime, date, timedelta

class DocumentReminder(db.Model):
    """Document expiry reminders for HR notifications."""
    __tablename__ = 'document_reminders'

    reminder_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    document_id = db.Column(UUID(as_uuid=True), db.<PERSON>ey('company_documents.document_id'), nullable=False)

    # Reminder settings
    reminder_type = db.Column(db.String(50), default='EMAIL')  # EMAIL, WHATSAPP, BOTH
    days_before_expiry = db.Column(db.Integer, nullable=False)  # e.g., 30, 14, 7, 1
    reminder_date = db.Column(db.Date, nullable=False)  # Calculated: expiry_date - days_before_expiry

    # Status
    is_sent = db.Column(db.<PERSON>, default=False)
    sent_at = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.now)

    # Relationships
    document = db.relationship('CompanyDocument', backref=db.backref('reminders', lazy='dynamic'))

    def __str__(self):
        """Return a string representation of the object."""
        return f"DocumentReminder [reminder_id={self.reminder_id}, document_id={self.document_id}, days={self.days_before_expiry}]"

    def to_dict(self):
        """Dictionary representation."""
        return {
            "reminder_id": str(self.reminder_id),
            "document_id": str(self.document_id),
            "document_name": self.document.document_name if self.document else None,
            "employee_name": f"{self.document.employee.first_name} {self.document.employee.last_name}" if self.document and self.document.employee else None,
            "reminder_type": self.reminder_type,
            "days_before_expiry": self.days_before_expiry,
            "reminder_date": self.reminder_date.strftime('%Y-%m-%d') if self.reminder_date else None,
            "is_sent": self.is_sent,
            "sent_at": self.sent_at.strftime('%Y-%m-%d %H:%M:%S') if self.sent_at else None,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "is_overdue": self.is_overdue()
        }

    def is_overdue(self):
        """Check if reminder is overdue (past reminder date and not sent)."""
        if self.is_sent or not self.reminder_date:
            return False
        return date.today() > self.reminder_date

    @classmethod
    def create_reminders_for_document(cls, session, document_id):
        """Create reminder entries for a document."""
        try:
            # Get the document
            from application.Models.documents.company_document import CompanyDocument
            document = session.query(CompanyDocument).filter_by(
                document_id=document_id
            ).first()

            if not document or not document.expiry_date:
                return []  # No reminders for permanent documents

            # Create reminders for different timeframes
            reminder_days = [30, 14, 7, 1]
            created_reminders = []

            for days in reminder_days:
                reminder_date = document.expiry_date - timedelta(days=days)

                # Only create if reminder date is in the future
                if reminder_date > date.today():
                    reminder = cls(
                        document_id=document_id,
                        days_before_expiry=days,
                        reminder_date=reminder_date
                    )
                    session.add(reminder)
                    created_reminders.append(reminder)

            session.commit()
            app.logger.info(f"Created {len(created_reminders)} reminders for document {document_id}")
            return created_reminders

        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating reminders: {e}")
            return []

    @classmethod
    def get_pending_reminders(cls, session, reminder_date=None):
        """Get reminders that need to be sent."""
        query = session.query(cls).filter_by(
            is_sent=False
        )

        if reminder_date:
            query = query.filter(cls.reminder_date <= reminder_date)
        else:
            query = query.filter(cls.reminder_date <= date.today())

        return query.order_by(cls.reminder_date).all()

    @classmethod
    def get_overdue_reminders(cls, session):
        """Get overdue reminders that haven't been sent."""
        today = date.today()
        return session.query(cls).filter(
            cls.is_sent == False,
            cls.reminder_date < today
        ).order_by(cls.reminder_date).all()

    @classmethod
    def mark_reminder_sent(cls, session, reminder_id):
        """Mark a reminder as sent."""
        try:
            reminder = session.query(cls).filter_by(reminder_id=reminder_id).first()
            if reminder:
                reminder.is_sent = True
                reminder.sent_at = datetime.now()
                session.commit()
                return True
            return False
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error marking reminder sent: {e}")
            return False

    @classmethod
    def mark_multiple_reminders_sent(cls, session, reminder_ids):
        """Mark multiple reminders as sent."""
        try:
            reminders = session.query(cls).filter(cls.reminder_id.in_(reminder_ids)).all()
            updated_count = 0

            for reminder in reminders:
                if not reminder.is_sent:
                    reminder.is_sent = True
                    reminder.sent_at = datetime.now()
                    updated_count += 1

            session.commit()
            app.logger.info(f"Marked {updated_count} reminders as sent")
            return updated_count
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error marking multiple reminders sent: {e}")
            return 0

    @classmethod
    def get_reminders_by_document(cls, session, document_id):
        """Get all reminders for a document."""
        return session.query(cls).filter_by(
            document_id=document_id
        ).order_by(cls.days_before_expiry).all()

    @classmethod
    def get_reminders_by_company_and_date_range(cls, session, start_date, end_date):
        """Get reminders within a date range."""
        return session.query(cls).filter(
            cls.reminder_date >= start_date,
            cls.reminder_date <= end_date
        ).order_by(cls.reminder_date).all()

    @classmethod
    def get_reminder_statistics(cls, session):
        """Get reminder statistics for a company."""
        try:
            total_reminders = session.query(cls).count()
            sent_reminders = session.query(cls).filter_by(is_sent=True).count()
            pending_reminders = session.query(cls).filter_by( is_sent=False).count()
            overdue_reminders = len(cls.get_overdue_reminders(session))

            # Today's pending reminders
            today_pending = len(cls.get_pending_reminders(session, date.today()))

            return {
                "total_reminders": total_reminders,
                "sent_reminders": sent_reminders,
                "pending_reminders": pending_reminders,
                "overdue_reminders": overdue_reminders,
                "today_pending": today_pending,
                "sent_percentage": round(sent_reminders / total_reminders * 100, 2) if total_reminders > 0 else 0
            }
        except Exception as e:
            app.logger.error(f"Error getting reminder statistics: {e}")
            return {
                "total_reminders": 0,
                "sent_reminders": 0,
                "pending_reminders": 0,
                "overdue_reminders": 0,
                "today_pending": 0,
                "sent_percentage": 0
            }

    @classmethod
    def cleanup_old_reminders(cls, session, days_old=90):
        """Clean up old sent reminders (older than specified days)."""
        try:
            cutoff_date = date.today() - timedelta(days=days_old)

            old_reminders = session.query(cls).filter(
                cls.is_sent == True,
                cls.sent_at < cutoff_date
            ).all()

            deleted_count = 0
            for reminder in old_reminders:
                session.delete(reminder)
                deleted_count += 1

            session.commit()
            app.logger.info(f"Cleaned up {deleted_count} old reminders for company")
            return deleted_count
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error cleaning up old reminders: {e}")
            return 0

    @classmethod
    def reschedule_reminders_for_document(cls, session, document_id, new_expiry_date):
        """Reschedule reminders when document expiry date changes."""
        try:
            # Delete existing reminders
            existing_reminders = session.query(cls).filter_by(
                document_id=document_id
            ).all()

            for reminder in existing_reminders:
                session.delete(reminder)

            # Create new reminders if expiry date is set
            if new_expiry_date:
                new_reminders = cls.create_reminders_for_document(session, document_id)
                return len(new_reminders)
            else:
                session.commit()
                return 0

        except Exception as e:
            session.rollback()
            app.logger.error(f"Error rescheduling reminders: {e}")
            return 0
