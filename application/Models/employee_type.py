from application.database import central_db as db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from flask import current_app

class EmployeeType(db.Model):
    """Model representing employee types for different countries (stored in central database)."""
    __tablename__ = 'employee_types'

    employee_type_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    country_id = db.Column(UUID(as_uuid=True), db.ForeignKey('countries.country_id'), nullable=False)
    name = db.Column(db.String(255), nullable=False)  # "Permanent Employee", "Casual Worker"
    code = db.Column(db.String(50), nullable=False)   # "PERMANENT", "CASUAL", "SECOND_EMPLOYEE"
    description = db.Column(db.Text, nullable=True)
    is_default = db.Column(db.<PERSON>, default=False, nullable=False)  # Default type for new employees
    sort_order = db.Column(db.Integer, default=1, nullable=False)      # Display order
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    # Relationship with Country model
    country = db.relationship('Country', backref=db.backref('employee_types', lazy='dynamic'))

    # Unique constraint: code must be unique per country
    __table_args__ = (
        db.UniqueConstraint('country_id', 'code', name='uq_employee_type_country_code'),
    )

    def __str__(self):
        """Return a string representation of the object."""
        return f"EmployeeType [employee_type_id={self.employee_type_id}, name={self.name}, code={self.code}]"

    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "employee_type_id": str(self.employee_type_id),
            "country_id": str(self.country_id),
            "name": self.name,
            "code": self.code,
            "description": self.description,
            "is_default": self.is_default,
            "sort_order": self.sort_order,
            "is_active": self.is_active,
            "country_name": self.country.name if self.country else None,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

    @classmethod
    def create_employee_type(cls, **kwargs):
        """Create a new employee type."""
        try:
            employee_type = cls(**kwargs)
            db.session.add(employee_type)
            db.session.commit()
            current_app.logger.info(f"Created employee type: {employee_type.name} ({employee_type.code})")
            return employee_type
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error creating employee type: {e}")
            return None

    @classmethod
    def get_by_country(cls, country_id):
        """Get all employee types for a specific country."""
        return cls.query.filter_by(country_id=country_id, is_active=True).order_by(cls.sort_order).all()

    @classmethod
    def get_by_code(cls, country_id, code):
        """Get employee type by country and code."""
        return cls.query.filter_by(country_id=country_id, code=code, is_active=True).first()

    @classmethod
    def get_by_id(cls, employee_type_id):
        """Get employee type by ID."""
        return cls.query.filter_by(employee_type_id=employee_type_id, is_active=True).first()

    @classmethod
    def get_default_for_country(cls, country_id):
        """Get the default employee type for a country."""
        return cls.query.filter_by(country_id=country_id, is_default=True, is_active=True).first()

    @classmethod
    def update_employee_type(cls, employee_type_id, **kwargs):
        """Update an employee type."""
        try:
            employee_type = cls.query.filter_by(employee_type_id=employee_type_id).first()
            if not employee_type:
                return None

            for key, value in kwargs.items():
                if hasattr(employee_type, key):
                    setattr(employee_type, key, value)

            db.session.commit()
            current_app.logger.info(f"Updated employee type: {employee_type.name}")
            return employee_type
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error updating employee type: {e}")
            return None

    @classmethod
    def deactivate_employee_type(cls, employee_type_id):
        """Deactivate an employee type (soft delete)."""
        try:
            employee_type = cls.query.filter_by(employee_type_id=employee_type_id).first()
            if not employee_type:
                return False

            employee_type.is_active = False
            db.session.commit()
            current_app.logger.info(f"Deactivated employee type: {employee_type.name}")
            return True
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error deactivating employee type: {e}")
            return False
