from application.database import db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from flask import current_app

class AllowanceType(db.Model):
    """Model representing types of allowances (stored in tenant database)."""
    __tablename__ = 'allowance_types'

    allowance_type_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    name = db.Column(db.String(255), nullable=False)  # "Transport Allowance", "Housing Allowance"
    code = db.Column(db.String(50), unique=True, nullable=False)  # "TRANSPORT", "HOUSING", "MEDICAL"
    description = db.Column(db.Text, nullable=True)
    is_taxable = db.Column(db.Bo<PERSON>an, default=True, nullable=False)  # Whether this allowance is subject to tax
    is_pensionable = db.Column(db.Boolean, default=True, nullable=False)  # Whether included in pension calculations
    calculation_type = db.Column(db.String(50), default='FIXED_AMOUNT', nullable=False)  # FIXED_AMOUNT, PERCENTAGE_OF_BASIC
    default_amount = db.Column(db.Numeric(15, 2), nullable=True)  # Default amount for this allowance type
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    def __str__(self):
        """Return a string representation of the object."""
        return f"AllowanceType [allowance_type_id={self.allowance_type_id}, name={self.name}, code={self.code}]"

    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "allowance_type_id": str(self.allowance_type_id),
            "name": self.name,
            "code": self.code,
            "description": self.description,
            "is_taxable": self.is_taxable,
            "is_pensionable": self.is_pensionable,
            "calculation_type": self.calculation_type,
            "default_amount": float(self.default_amount) if self.default_amount else None,
            "is_active": self.is_active,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

    @classmethod
    def create_allowance_type(cls, session, **kwargs):
        """Create a new allowance type."""
        try:
            allowance_type = cls(**kwargs)
            session.add(allowance_type)
            session.commit()
            current_app.logger.info(f"Created allowance type: {allowance_type.name} ({allowance_type.code})")
            return allowance_type
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error creating allowance type: {e}")
            return None

    @classmethod
    def get_all_active(cls, session):
        """Get all active allowance types."""
        return session.query(cls).filter_by(is_active=True).order_by(cls.name).all()

    @classmethod
    def get_by_code(cls, session, code):
        """Get allowance type by code."""
        return session.query(cls).filter_by(code=code, is_active=True).first()

    @classmethod
    def get_by_id(cls, session, allowance_type_id):
        """Get allowance type by ID."""
        return session.query(cls).filter_by(allowance_type_id=allowance_type_id, is_active=True).first()

    @classmethod
    def get_taxable_allowances(cls, session):
        """Get all taxable allowance types."""
        return session.query(cls).filter_by(is_taxable=True, is_active=True).all()

    @classmethod
    def get_pensionable_allowances(cls, session):
        """Get all pensionable allowance types."""
        return session.query(cls).filter_by(is_pensionable=True, is_active=True).all()

    @classmethod
    def update_allowance_type(cls, session, allowance_type_id, **kwargs):
        """Update an allowance type."""
        try:
            allowance_type = session.query(cls).filter_by(allowance_type_id=allowance_type_id).first()
            if not allowance_type:
                return None

            for key, value in kwargs.items():
                if hasattr(allowance_type, key):
                    setattr(allowance_type, key, value)

            session.commit()
            current_app.logger.info(f"Updated allowance type: {allowance_type.name}")
            return allowance_type
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error updating allowance type: {e}")
            return None

    @classmethod
    def deactivate_allowance_type(cls, session, allowance_type_id):
        """Deactivate an allowance type (soft delete)."""
        try:
            allowance_type = session.query(cls).filter_by(allowance_type_id=allowance_type_id).first()
            if not allowance_type:
                return False

            allowance_type.is_active = False
            session.commit()
            current_app.logger.info(f"Deactivated allowance type: {allowance_type.name}")
            return True
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error deactivating allowance type: {e}")
            return False

    @classmethod
    def create_default_allowance_types(cls, session):
        """Create default allowance types for a new company."""
        default_types = [
            {
                'name': 'Transport Allowance',
                'code': 'TRANSPORT',
                'description': 'Monthly transport allowance',
                'is_taxable': True,
                'is_pensionable': True,
                'calculation_type': 'FIXED_AMOUNT',
                'default_amount': 20000
            },
            {
                'name': 'Housing Allowance',
                'code': 'HOUSING',
                'description': 'Monthly housing allowance',
                'is_taxable': True,
                'is_pensionable': True,
                'calculation_type': 'FIXED_AMOUNT',
                'default_amount': 50000
            },
            {
                'name': 'Medical Allowance',
                'code': 'MEDICAL',
                'description': 'Monthly medical allowance',
                'is_taxable': False,
                'is_pensionable': False,
                'calculation_type': 'FIXED_AMOUNT',
                'default_amount': 15000
            },
            {
                'name': 'Meal Allowance',
                'code': 'MEAL',
                'description': 'Daily meal allowance',
                'is_taxable': True,
                'is_pensionable': False,
                'calculation_type': 'FIXED_AMOUNT',
                'default_amount': 5000
            }
        ]

        created_types = []
        try:
            for type_data in default_types:
                # Check if already exists
                existing = cls.get_by_code(session, type_data['code'])
                if not existing:
                    allowance_type = cls.create_allowance_type(session, **type_data)
                    if allowance_type:
                        created_types.append(allowance_type)

            current_app.logger.info(f"Created {len(created_types)} default allowance types")
            return created_types

        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error creating default allowance types: {e}")
            return []
