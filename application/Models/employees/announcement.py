import uuid
from datetime import datetime, date
from sqlalchemy.dialects.postgresql import UUID
from application.database import db
from flask import current_app as app
import json
from sqlalchemy import and_, or_, func


class Announcement(db.Model):
    """Model representing company announcements for employees."""
    __tablename__ = 'announcements'

    announcement_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    title = db.Column(db.String(255), nullable=False)
    content = db.Column(db.Text, nullable=False)
    summary = db.Column(db.String(500), nullable=True)  # Brief summary for previews
    
    # Categorization
    announcement_type = db.Column(db.String(50), nullable=False, default='GENERAL')  # GENERAL, POLICY, EVENT, EMERGENCY, TRAINING, BENEFITS, COMPANY_NEWS
    priority = db.Column(db.String(20), nullable=False, default='MEDIUM')  # LOW, MEDIUM, HIGH, URGENT
    category = db.Column(db.String(100), nullable=True)  # Custom category
    tags = db.Column(db.Text, nullable=True)  # JSON array of tags
    
    # Targeting
    target_audience = db.Column(db.String(50), nullable=False, default='ALL')  # ALL, DEPARTMENT_SPECIFIC, ROLE_SPECIFIC, CUSTOM
    department_ids = db.Column(db.Text, nullable=True)  # JSON array of department IDs
    role_targets = db.Column(db.Text, nullable=True)  # JSON array of roles
    employee_ids = db.Column(db.Text, nullable=True)  # JSON array of specific employee IDs
    
    # Publishing and scheduling
    is_published = db.Column(db.Boolean, default=False, nullable=False)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    publish_date = db.Column(db.DateTime, nullable=True)
    expiry_date = db.Column(db.DateTime, nullable=True)
    scheduled_publish = db.Column(db.Boolean, default=False, nullable=False)
    
    # Content features
    is_pinned = db.Column(db.Boolean, default=False, nullable=False)
    allows_comments = db.Column(db.Boolean, default=False, nullable=False)
    requires_acknowledgment = db.Column(db.Boolean, default=False, nullable=False)
    attachment_urls = db.Column(db.Text, nullable=True)  # JSON array of attachment URLs
    
    # Analytics and tracking
    view_count = db.Column(db.Integer, default=0, nullable=False)
    read_count = db.Column(db.Integer, default=0, nullable=False)
    acknowledgment_count = db.Column(db.Integer, default=0, nullable=False)
    
    # Metadata
    created_by = db.Column(UUID(as_uuid=True), nullable=False)  # Reference to company_users
    updated_by = db.Column(UUID(as_uuid=True), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.now, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now, nullable=False)
    
    # Relationships
    reads = db.relationship('AnnouncementRead', backref='announcement', lazy='dynamic', cascade='all, delete-orphan')

    def __str__(self):
        """Return a string representation of the object."""
        return f"Announcement [announcement_id={self.announcement_id}, title={self.title}, priority={self.priority}]"

    def to_dict(self, include_content=True, include_analytics=False):
        """Convert the announcement to a dictionary."""
        data = {
            'announcement_id': str(self.announcement_id),
            'title': self.title,
            'summary': self.summary,
            'announcement_type': self.announcement_type,
            'priority': self.priority,
            'category': self.category,
            'tags': self.get_tags_list(),
            'target_audience': self.target_audience,
            'department_ids': self.get_department_ids_list(),
            'role_targets': self.get_role_targets_list(),
            'employee_ids': self.get_employee_ids_list(),
            'is_published': self.is_published,
            'is_active': self.is_active,
            'publish_date': self.publish_date.isoformat() if self.publish_date else None,
            'expiry_date': self.expiry_date.isoformat() if self.expiry_date else None,
            'scheduled_publish': self.scheduled_publish,
            'is_pinned': self.is_pinned,
            'allows_comments': self.allows_comments,
            'requires_acknowledgment': self.requires_acknowledgment,
            'attachment_urls': self.get_attachment_urls_list(),
            'view_count': self.view_count,
            'created_by': str(self.created_by),
            'updated_by': str(self.updated_by) if self.updated_by else None,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
        
        if include_content:
            data['content'] = self.content
            
        if include_analytics:
            data['read_count'] = self.read_count
            data['acknowledgment_count'] = self.acknowledgment_count
            data['total_target_employees'] = self.get_target_employee_count()
            data['read_percentage'] = self.get_read_percentage()
            
        return data

    # JSON field helper methods
    def get_tags_list(self):
        """Get tags as a list."""
        try:
            return json.loads(self.tags) if self.tags else []
        except (json.JSONDecodeError, TypeError):
            return []

    def set_tags_list(self, tags_list):
        """Set tags from a list."""
        self.tags = json.dumps(tags_list) if tags_list else None

    def get_department_ids_list(self):
        """Get department IDs as a list."""
        try:
            return json.loads(self.department_ids) if self.department_ids else []
        except (json.JSONDecodeError, TypeError):
            return []

    def set_department_ids_list(self, dept_ids_list):
        """Set department IDs from a list."""
        self.department_ids = json.dumps(dept_ids_list) if dept_ids_list else None

    def get_role_targets_list(self):
        """Get role targets as a list."""
        try:
            return json.loads(self.role_targets) if self.role_targets else []
        except (json.JSONDecodeError, TypeError):
            return []

    def set_role_targets_list(self, roles_list):
        """Set role targets from a list."""
        self.role_targets = json.dumps(roles_list) if roles_list else None

    def get_employee_ids_list(self):
        """Get employee IDs as a list."""
        try:
            return json.loads(self.employee_ids) if self.employee_ids else []
        except (json.JSONDecodeError, TypeError):
            return []

    def set_employee_ids_list(self, emp_ids_list):
        """Set employee IDs from a list."""
        self.employee_ids = json.dumps(emp_ids_list) if emp_ids_list else None

    def get_attachment_urls_list(self):
        """Get attachment URLs as a list."""
        try:
            return json.loads(self.attachment_urls) if self.attachment_urls else []
        except (json.JSONDecodeError, TypeError):
            return []

    def set_attachment_urls_list(self, urls_list):
        """Set attachment URLs from a list."""
        self.attachment_urls = json.dumps(urls_list) if urls_list else None

    # Business logic methods
    def is_visible_to_employee(self, employee):
        """Check if this announcement is visible to a specific employee."""
        if not self.is_published or not self.is_active:
            return False
            
        # Check if announcement has expired
        if self.expiry_date and datetime.now() > self.expiry_date:
            return False
            
        # Check if announcement is scheduled for future
        if self.publish_date and datetime.now() < self.publish_date:
            return False
            
        # Check targeting
        if self.target_audience == 'ALL':
            return True
            
        if self.target_audience == 'DEPARTMENT_SPECIFIC':
            dept_ids = self.get_department_ids_list()
            return str(employee.department_id) in dept_ids if employee.department_id else False
            
        if self.target_audience == 'ROLE_SPECIFIC':
            # This would need to be implemented based on your role system
            return True  # Placeholder
            
        if self.target_audience == 'CUSTOM':
            emp_ids = self.get_employee_ids_list()
            return str(employee.employee_id) in emp_ids
            
        return False

    def increment_view_count(self, session):
        """Increment the view count."""
        try:
            self.view_count += 1
            session.commit()
            return True
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error incrementing view count for announcement {self.announcement_id}: {e}")
            return False

    def get_target_employee_count(self):
        """Get the total number of employees this announcement targets."""
        # This would need to be implemented with actual employee queries
        # For now, return the read count as a placeholder
        return self.read_count

    def get_read_percentage(self):
        """Get the percentage of target employees who have read this announcement."""
        total_targets = self.get_target_employee_count()
        if total_targets == 0:
            return 0
        return round((self.read_count / total_targets) * 100, 2)

    def can_be_edited_by(self, user_id):
        """Check if a user can edit this announcement."""
        return str(self.created_by) == str(user_id)

    def publish(self, session, user_id):
        """Publish the announcement."""
        try:
            self.is_published = True
            self.updated_by = user_id
            if not self.publish_date:
                self.publish_date = datetime.now()
            session.commit()
            app.logger.info(f"Published announcement: {self.announcement_id}")
            return True, None
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error publishing announcement {self.announcement_id}: {e}")
            return False, str(e)

    def unpublish(self, session, user_id):
        """Unpublish the announcement."""
        try:
            self.is_published = False
            self.updated_by = user_id
            session.commit()
            app.logger.info(f"Unpublished announcement: {self.announcement_id}")
            return True, None
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error unpublishing announcement {self.announcement_id}: {e}")
            return False, str(e)

    def archive(self, session, user_id):
        """Archive the announcement."""
        try:
            self.is_active = False
            self.updated_by = user_id
            session.commit()
            app.logger.info(f"Archived announcement: {self.announcement_id}")
            return True, None
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error archiving announcement {self.announcement_id}: {e}")
            return False, str(e)

    # Class methods for database operations
    @classmethod
    def create_announcement(cls, session, **kwargs):
        """Create a new announcement."""
        try:
            # Handle JSON fields
            json_fields = ['tags', 'department_ids', 'role_targets', 'employee_ids', 'attachment_urls']
            for field in json_fields:
                if field in kwargs and isinstance(kwargs[field], list):
                    kwargs[field] = json.dumps(kwargs[field])

            # Convert string UUIDs to UUID objects
            uuid_fields = ['created_by', 'updated_by']
            for field in uuid_fields:
                if field in kwargs and isinstance(kwargs[field], str):
                    try:
                        kwargs[field] = uuid.UUID(kwargs[field])
                    except ValueError:
                        app.logger.error(f"Invalid {field} UUID format: {kwargs[field]}")
                        return None, f"Invalid {field} format"

            # Create the announcement
            announcement = cls(**kwargs)
            session.add(announcement)
            session.flush()  # Get the ID without committing

            app.logger.info(f"Created announcement: {announcement.title}")
            return announcement, None

        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating announcement: {e}")
            return None, str(e)

    @classmethod
    def get_announcement_by_id(cls, session, announcement_id):
        """Get an announcement by ID."""
        try:
            if isinstance(announcement_id, str):
                announcement_id = uuid.UUID(announcement_id)
            return session.query(cls).filter_by(announcement_id=announcement_id).first()
        except ValueError:
            app.logger.error(f"Invalid announcement ID format: {announcement_id}")
            return None
        except Exception as e:
            app.logger.error(f"Error getting announcement by ID: {e}")
            return None

    @classmethod
    def get_announcements_for_employee(cls, session, employee, include_expired=False, limit=None, offset=0):
        """Get announcements visible to a specific employee."""
        try:
            query = session.query(cls).filter(
                cls.is_published == True,
                cls.is_active == True
            )

            # Filter by publish date
            query = query.filter(
                or_(cls.publish_date.is_(None), cls.publish_date <= datetime.now())
            )

            # Filter by expiry date if not including expired
            if not include_expired:
                query = query.filter(
                    or_(cls.expiry_date.is_(None), cls.expiry_date > datetime.now())
                )

            # Apply targeting filters
            targeting_conditions = []

            # All employees
            targeting_conditions.append(cls.target_audience == 'ALL')

            # Department specific
            if employee.department_id:
                dept_condition = and_(
                    cls.target_audience == 'DEPARTMENT_SPECIFIC',
                    cls.department_ids.contains(f'"{str(employee.department_id)}"')
                )
                targeting_conditions.append(dept_condition)

            # Custom employee targeting
            emp_condition = and_(
                cls.target_audience == 'CUSTOM',
                cls.employee_ids.contains(f'"{str(employee.employee_id)}"')
            )
            targeting_conditions.append(emp_condition)

            query = query.filter(or_(*targeting_conditions))

            # Order by priority and creation date
            priority_order = db.case(
                (cls.priority == 'URGENT', 1),
                (cls.priority == 'HIGH', 2),
                (cls.priority == 'MEDIUM', 3),
                (cls.priority == 'LOW', 4),
                else_=5
            )
            query = query.order_by(cls.is_pinned.desc(), priority_order, cls.created_at.desc())

            # Apply pagination
            if limit:
                query = query.offset(offset).limit(limit)

            return query.all()

        except Exception as e:
            app.logger.error(f"Error getting announcements for employee: {e}")
            return []

    @classmethod
    def get_announcements_by_filters(cls, session, filters=None, limit=None, offset=0):
        """Get announcements with various filters."""
        try:
            query = session.query(cls)

            if filters:
                # Filter by status
                if 'is_published' in filters:
                    query = query.filter(cls.is_published == filters['is_published'])
                if 'is_active' in filters:
                    query = query.filter(cls.is_active == filters['is_active'])

                # Filter by type and priority
                if 'announcement_type' in filters:
                    query = query.filter(cls.announcement_type == filters['announcement_type'])
                if 'priority' in filters:
                    query = query.filter(cls.priority == filters['priority'])

                # Filter by creator
                if 'created_by' in filters:
                    created_by = filters['created_by']
                    if isinstance(created_by, str):
                        created_by = uuid.UUID(created_by)
                    query = query.filter(cls.created_by == created_by)

                # Filter by department
                if 'department_id' in filters:
                    dept_id = str(filters['department_id'])
                    query = query.filter(
                        or_(
                            cls.target_audience == 'ALL',
                            and_(
                                cls.target_audience == 'DEPARTMENT_SPECIFIC',
                                cls.department_ids.contains(f'"{dept_id}"')
                            )
                        )
                    )

                # Filter by date range
                if 'start_date' in filters:
                    query = query.filter(cls.created_at >= filters['start_date'])
                if 'end_date' in filters:
                    query = query.filter(cls.created_at <= filters['end_date'])

                # Search in title and content
                if 'search' in filters:
                    search_term = f"%{filters['search']}%"
                    query = query.filter(
                        or_(
                            cls.title.ilike(search_term),
                            cls.content.ilike(search_term),
                            cls.summary.ilike(search_term)
                        )
                    )

            # Order by creation date (newest first)
            query = query.order_by(cls.created_at.desc())

            # Apply pagination
            if limit:
                query = query.offset(offset).limit(limit)

            return query.all()

        except Exception as e:
            app.logger.error(f"Error getting announcements by filters: {e}")
            return []

    @classmethod
    def get_announcement_analytics(cls, session, announcement_id):
        """Get analytics for a specific announcement."""
        try:
            if isinstance(announcement_id, str):
                announcement_id = uuid.UUID(announcement_id)

            announcement = session.query(cls).filter_by(announcement_id=announcement_id).first()
            if not announcement:
                return None

            # Get read statistics
            from application.Models.employees.announcement_read import AnnouncementRead

            total_reads = session.query(AnnouncementRead).filter_by(
                announcement_id=announcement_id
            ).count()

            unique_readers = session.query(AnnouncementRead.employee_id).filter_by(
                announcement_id=announcement_id
            ).distinct().count()

            # Get read timeline (last 30 days)
            from datetime import timedelta
            thirty_days_ago = datetime.now() - timedelta(days=30)
            daily_reads = session.query(
                func.date(AnnouncementRead.read_at).label('read_date'),
                func.count(AnnouncementRead.read_id).label('read_count')
            ).filter(
                AnnouncementRead.announcement_id == announcement_id,
                AnnouncementRead.read_at >= thirty_days_ago
            ).group_by(func.date(AnnouncementRead.read_at)).all()

            return {
                'announcement': announcement.to_dict(include_analytics=True),
                'total_reads': total_reads,
                'unique_readers': unique_readers,
                'daily_reads': [{'date': str(dr.read_date), 'count': dr.read_count} for dr in daily_reads]
            }

        except Exception as e:
            app.logger.error(f"Error getting announcement analytics: {e}")
            return None

    @classmethod
    def update_announcement(cls, session, announcement_id, **kwargs):
        """Update an announcement."""
        try:
            if isinstance(announcement_id, str):
                announcement_id = uuid.UUID(announcement_id)

            announcement = session.query(cls).filter_by(announcement_id=announcement_id).first()
            if not announcement:
                return None, "Announcement not found"

            # Handle JSON fields
            json_fields = ['tags', 'department_ids', 'role_targets', 'employee_ids', 'attachment_urls']
            for field in json_fields:
                if field in kwargs and isinstance(kwargs[field], list):
                    kwargs[field] = json.dumps(kwargs[field])

            # Convert string UUIDs to UUID objects
            uuid_fields = ['updated_by']
            for field in uuid_fields:
                if field in kwargs and isinstance(kwargs[field], str):
                    try:
                        kwargs[field] = uuid.UUID(kwargs[field])
                    except ValueError:
                        app.logger.error(f"Invalid {field} UUID format: {kwargs[field]}")
                        return None, f"Invalid {field} format"

            # Update fields
            for key, value in kwargs.items():
                if hasattr(announcement, key):
                    setattr(announcement, key, value)

            announcement.updated_at = datetime.now()
            session.commit()

            app.logger.info(f"Updated announcement: {announcement_id}")
            return announcement, None

        except Exception as e:
            session.rollback()
            app.logger.error(f"Error updating announcement: {e}")
            return None, str(e)

    @classmethod
    def delete_announcement(cls, session, announcement_id):
        """Delete an announcement."""
        try:
            if isinstance(announcement_id, str):
                announcement_id = uuid.UUID(announcement_id)

            announcement = session.query(cls).filter_by(announcement_id=announcement_id).first()
            if not announcement:
                return False, "Announcement not found"

            session.delete(announcement)
            session.commit()

            app.logger.info(f"Deleted announcement: {announcement_id}")
            return True, None

        except Exception as e:
            session.rollback()
            app.logger.error(f"Error deleting announcement: {e}")
            return False, str(e)
