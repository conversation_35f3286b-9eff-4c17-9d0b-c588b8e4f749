import uuid
from datetime import datetime
from sqlalchemy.dialects.postgresql import UUID
from application.database import db
from flask import current_app as app
from sqlalchemy import and_, func


class AnnouncementRead(db.Model):
    """Model representing employee reads of announcements."""
    __tablename__ = 'announcement_reads'

    read_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    announcement_id = db.Column(UUID(as_uuid=True), db.<PERSON>ey('announcements.announcement_id'), nullable=False)
    employee_id = db.Column(UUID(as_uuid=True), db.<PERSON><PERSON>('employees.employee_id'), nullable=False)
    
    # Read tracking
    read_at = db.Column(db.DateTime, default=datetime.now, nullable=False)
    is_acknowledged = db.Column(db.Boolean, default=False, nullable=False)
    acknowledged_at = db.Column(db.DateTime, nullable=True)
    
    # Engagement tracking
    time_spent_reading = db.Column(db.Integer, nullable=True)  # Time in seconds
    device_type = db.Column(db.String(50), nullable=True)  # WEB, MOBILE, TABLET
    ip_address = db.Column(db.String(45), nullable=True)  # IPv4 or IPv6
    user_agent = db.Column(db.String(500), nullable=True)
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.now, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now, nullable=False)

    # Relationships
    employee = db.relationship('Employee', backref='announcement_reads')

    def __str__(self):
        """Return a string representation of the object."""
        return f"AnnouncementRead [read_id={self.read_id}, announcement_id={self.announcement_id}, employee_id={self.employee_id}]"

    def to_dict(self):
        """Convert the announcement read to a dictionary."""
        return {
            'read_id': str(self.read_id),
            'announcement_id': str(self.announcement_id),
            'employee_id': str(self.employee_id),
            'read_at': self.read_at.isoformat(),
            'is_acknowledged': self.is_acknowledged,
            'acknowledged_at': self.acknowledged_at.isoformat() if self.acknowledged_at else None,
            'time_spent_reading': self.time_spent_reading,
            'device_type': self.device_type,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

    def acknowledge(self, session):
        """Mark the announcement as acknowledged."""
        try:
            self.is_acknowledged = True
            self.acknowledged_at = datetime.now()
            session.commit()
            
            # Update the announcement's acknowledgment count
            from application.Models.employees.announcement import Announcement
            announcement = session.query(Announcement).filter_by(
                announcement_id=self.announcement_id
            ).first()
            if announcement:
                announcement.acknowledgment_count += 1
                session.commit()
            
            app.logger.info(f"Acknowledged announcement read: {self.read_id}")
            return True, None
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error acknowledging announcement read {self.read_id}: {e}")
            return False, str(e)

    # Class methods for database operations
    @classmethod
    def create_read_record(cls, session, announcement_id, employee_id, **kwargs):
        """Create a new announcement read record."""
        try:
            # Convert string UUIDs to UUID objects
            if isinstance(announcement_id, str):
                announcement_id = uuid.UUID(announcement_id)
            if isinstance(employee_id, str):
                employee_id = uuid.UUID(employee_id)

            # Check if read record already exists
            existing_read = session.query(cls).filter_by(
                announcement_id=announcement_id,
                employee_id=employee_id
            ).first()
            
            if existing_read:
                # Update the existing read record with new read time
                existing_read.read_at = datetime.now()
                existing_read.updated_at = datetime.now()
                
                # Update additional fields if provided
                for key, value in kwargs.items():
                    if hasattr(existing_read, key):
                        setattr(existing_read, key, value)
                
                session.commit()
                app.logger.info(f"Updated existing read record: {existing_read.read_id}")
                return existing_read, None

            # Create new read record
            read_record = cls(
                announcement_id=announcement_id,
                employee_id=employee_id,
                **kwargs
            )
            session.add(read_record)
            session.flush()  # Get the ID without committing
            
            # Update the announcement's read count
            from application.Models.employees.announcement import Announcement
            announcement = session.query(Announcement).filter_by(
                announcement_id=announcement_id
            ).first()
            if announcement:
                announcement.read_count += 1
                session.commit()
            
            app.logger.info(f"Created announcement read record: {read_record.read_id}")
            return read_record, None
            
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating announcement read record: {e}")
            return None, str(e)

    @classmethod
    def get_read_by_announcement_and_employee(cls, session, announcement_id, employee_id):
        """Get a read record by announcement and employee."""
        try:
            if isinstance(announcement_id, str):
                announcement_id = uuid.UUID(announcement_id)
            if isinstance(employee_id, str):
                employee_id = uuid.UUID(employee_id)
                
            return session.query(cls).filter_by(
                announcement_id=announcement_id,
                employee_id=employee_id
            ).first()
        except ValueError:
            app.logger.error(f"Invalid UUID format in get_read_by_announcement_and_employee")
            return None
        except Exception as e:
            app.logger.error(f"Error getting read record: {e}")
            return None

    @classmethod
    def get_reads_by_announcement(cls, session, announcement_id, limit=None, offset=0):
        """Get all read records for an announcement."""
        try:
            if isinstance(announcement_id, str):
                announcement_id = uuid.UUID(announcement_id)
                
            query = session.query(cls).filter_by(announcement_id=announcement_id)
            query = query.order_by(cls.read_at.desc())
            
            if limit:
                query = query.offset(offset).limit(limit)
                
            return query.all()
        except ValueError:
            app.logger.error(f"Invalid announcement ID format: {announcement_id}")
            return []
        except Exception as e:
            app.logger.error(f"Error getting reads by announcement: {e}")
            return []

    @classmethod
    def get_reads_by_employee(cls, session, employee_id, limit=None, offset=0):
        """Get all read records for an employee."""
        try:
            if isinstance(employee_id, str):
                employee_id = uuid.UUID(employee_id)
                
            query = session.query(cls).filter_by(employee_id=employee_id)
            query = query.order_by(cls.read_at.desc())
            
            if limit:
                query = query.offset(offset).limit(limit)
                
            return query.all()
        except ValueError:
            app.logger.error(f"Invalid employee ID format: {employee_id}")
            return []
        except Exception as e:
            app.logger.error(f"Error getting reads by employee: {e}")
            return []

    @classmethod
    def get_unread_announcements_count(cls, session, employee_id):
        """Get count of unread announcements for an employee."""
        try:
            if isinstance(employee_id, str):
                employee_id = uuid.UUID(employee_id)
                
            from application.Models.employees.announcement import Announcement
            from application.Models.employees.employee import Employee
            
            # Get the employee to check department targeting
            employee = session.query(Employee).filter_by(employee_id=employee_id).first()
            if not employee:
                return 0
                
            # Get all published announcements that target this employee
            announcements_query = session.query(Announcement).filter(
                Announcement.is_published == True,
                Announcement.is_active == True
            )
            
            # Filter by publish date
            announcements_query = announcements_query.filter(
                db.or_(Announcement.publish_date.is_(None), Announcement.publish_date <= datetime.now())
            )
            
            # Filter by expiry date
            announcements_query = announcements_query.filter(
                db.or_(Announcement.expiry_date.is_(None), Announcement.expiry_date > datetime.now())
            )
            
            # Apply targeting filters
            targeting_conditions = []
            
            # All employees
            targeting_conditions.append(Announcement.target_audience == 'ALL')
            
            # Department specific
            if employee.department_id:
                dept_condition = and_(
                    Announcement.target_audience == 'DEPARTMENT_SPECIFIC',
                    Announcement.department_ids.contains(f'"{str(employee.department_id)}"')
                )
                targeting_conditions.append(dept_condition)
            
            # Custom employee targeting
            emp_condition = and_(
                Announcement.target_audience == 'CUSTOM',
                Announcement.employee_ids.contains(f'"{str(employee.employee_id)}"')
            )
            targeting_conditions.append(emp_condition)
            
            announcements_query = announcements_query.filter(db.or_(*targeting_conditions))
            
            # Get announcement IDs that this employee has read
            read_announcement_ids = session.query(cls.announcement_id).filter_by(
                employee_id=employee_id
            ).subquery()
            
            # Count announcements not in the read list
            unread_count = announcements_query.filter(
                ~Announcement.announcement_id.in_(read_announcement_ids)
            ).count()
            
            return unread_count
            
        except Exception as e:
            app.logger.error(f"Error getting unread announcements count: {e}")
            return 0

    @classmethod
    def get_read_statistics(cls, session, announcement_id):
        """Get read statistics for an announcement."""
        try:
            if isinstance(announcement_id, str):
                announcement_id = uuid.UUID(announcement_id)
                
            # Total reads
            total_reads = session.query(cls).filter_by(announcement_id=announcement_id).count()
            
            # Unique readers
            unique_readers = session.query(cls.employee_id).filter_by(
                announcement_id=announcement_id
            ).distinct().count()
            
            # Acknowledged reads
            acknowledged_reads = session.query(cls).filter_by(
                announcement_id=announcement_id,
                is_acknowledged=True
            ).count()
            
            # Average time spent reading (if tracked)
            avg_time_spent = session.query(func.avg(cls.time_spent_reading)).filter(
                cls.announcement_id == announcement_id,
                cls.time_spent_reading.isnot(None)
            ).scalar()
            
            # Device breakdown
            device_stats = session.query(
                cls.device_type,
                func.count(cls.read_id).label('count')
            ).filter_by(announcement_id=announcement_id).group_by(cls.device_type).all()
            
            return {
                'total_reads': total_reads,
                'unique_readers': unique_readers,
                'acknowledged_reads': acknowledged_reads,
                'acknowledgment_rate': round((acknowledged_reads / unique_readers * 100), 2) if unique_readers > 0 else 0,
                'average_time_spent': float(avg_time_spent) if avg_time_spent else None,
                'device_breakdown': [{'device': ds.device_type, 'count': ds.count} for ds in device_stats]
            }
            
        except Exception as e:
            app.logger.error(f"Error getting read statistics: {e}")
            return None

    @classmethod
    def delete_reads_by_announcement(cls, session, announcement_id):
        """Delete all read records for an announcement."""
        try:
            if isinstance(announcement_id, str):
                announcement_id = uuid.UUID(announcement_id)
                
            deleted_count = session.query(cls).filter_by(announcement_id=announcement_id).delete()
            session.commit()
            
            app.logger.info(f"Deleted {deleted_count} read records for announcement: {announcement_id}")
            return True, deleted_count
            
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error deleting read records: {e}")
            return False, str(e)
