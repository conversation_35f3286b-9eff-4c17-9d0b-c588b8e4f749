from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUID
from flask import current_app as app
from datetime import datetime, date, timedelta
from sqlalchemy import cast, func

class Attendance(db.Model):
    """
    Model representing employee attendance.

    Status Values:
    - 'present': Full day, on time, stayed until shift end (within grace period)
    - 'absent': No attendance record
    - 'late': Late arrival (beyond grace period)
    - 'early-departure': Left early (beyond grace period)
    - 'late-early-departure': Both late arrival AND early departure
    """
    __tablename__ = 'attendance'

    attendance_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    employee_id = db.Column(UUID(as_uuid=True),db.<PERSON>ey('employees.employee_id'), nullable=False)  # Reference to employee
    date = db.Column(db.Date, nullable=False)
    check_in_time = db.Column(db.DateTime, nullable=True)
    check_out_time = db.Column(db.DateTime, nullable=True)
    total_hours = db.Column(db.Float, nullable=True)
    status = db.Column(db.String(50), default='present', nullable=False)  # present, absent, late, early-departure, late-early-departure
    source = db.Column(db.String(50), nullable=False)  # biometric, manual, facial_recognition
    source_record_id = db.Column(db.String(255), nullable=True)  # ID from the source system (e.g., Records table ID)
    notes = db.Column(db.Text, nullable=True)

    # Shift-related fields
    shift_id = db.Column(UUID(as_uuid=True), db.ForeignKey('shifts.shift_id'), nullable=True)  # Foreign key reference to the shift table
    expected_start_time = db.Column(db.Time, nullable=True)  # Expected start time based on shift
    expected_end_time = db.Column(db.Time, nullable=True)  # Expected end time based on shift
    is_overtime = db.Column(db.Boolean, default=False, nullable=True)  # Whether this attendance includes overtime
    overtime_hours = db.Column(db.Float, nullable=True)  # Hours of overtime

    # Relationship with Shift model
    shift = db.relationship('Shift', backref=db.backref('attendances', lazy='dynamic'))

    # Relationship with Employee model
    employee = db.relationship('Employee', backref=db.backref('attendances', lazy='dynamic'))

    created_by = db.Column(UUID(as_uuid=True), nullable=True)  # User ID who created this record (for manual entries)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    def __str__(self):
        """Return a string representation of the object."""
        return f"Attendance [attendance_id={self.attendance_id}, employee_id={self.employee_id}, date={self.date}, status={self.status}]"

    def update_status_safely(self, new_status, reason=""):
        """
        Safely update attendance status with error handling.

        Args:
            new_status (str): New status to set
            reason (str): Reason for status change (for logging)

        Returns:
            bool: True if successful, False if failed
        """
        try:
            old_status = self.status
            self.status = new_status
            app.logger.info(f"Updated attendance status from '{old_status}' to '{new_status}' for employee {self.employee_id}. Reason: {reason}")
            return True
        except Exception as e:
            app.logger.error(f"Failed to update attendance status to '{new_status}' for employee {self.employee_id}: {e}")
            return False

    def add_note_safely(self, note):
        """
        Safely add a note to attendance record with error handling.

        Args:
            note (str): Note to add

        Returns:
            bool: True if successful, False if failed
        """
        try:
            if self.notes:
                self.notes = f"{self.notes} [{note}]"
            else:
                self.notes = f"[{note}]"
            app.logger.debug(f"Added note to attendance {self.attendance_id}: {note}")
            return True
        except Exception as e:
            app.logger.error(f"Failed to add note to attendance {self.attendance_id}: {e}")
            return False

    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "attendance_id": str(self.attendance_id),
            "employee_id": str(self.employee_id),
            "employee": self.employee.to_dict() if self.employee else [],
            "date": self.date.strftime('%Y-%m-%d') if self.date else None,
            "check_in_time": self.check_in_time.strftime('%Y-%m-%d %H:%M:%S') if self.check_in_time else None,
            "check_out_time": self.check_out_time.strftime('%Y-%m-%d %H:%M:%S') if self.check_out_time else None,
            "total_hours": self.total_hours,
            "status": self.status,
            "source": self.source,
            "source_record_id": self.source_record_id,
            "notes": self.notes,
            "shift_id": str(self.shift_id) if self.shift_id else None,
            "expected_start_time": self.expected_start_time.strftime('%H:%M') if self.expected_start_time else None,
            "expected_end_time": self.expected_end_time.strftime('%H:%M') if self.expected_end_time else None,
            "is_overtime": self.is_overtime,
            "overtime_hours": self.overtime_hours,
            "created_by": str(self.created_by) if self.created_by else None,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }
    def public_to_dict(self):
        """Public dictionary representation of the object, excluding sensitive fields."""
        # we wanna reduce decimals in total_hours and overtime_hours to 2
        total_hours = round(self.total_hours, 2) if self.total_hours else 0
        overtime_hours = round(self.overtime_hours, 2) if self.overtime_hours else 0
        return {
            "attendance_id": str(self.attendance_id),
            "employee_name": self.employee.first_name + " " + self.employee.last_name if self.employee else None,
            "department_name": self.employee.department.name if self.employee and self.employee.department else None,
            "date": self.date.isoformat() if self.date else None,
            "check_in_time": self.check_in_time.isoformat() if self.check_in_time else None,
            "check_out_time": self.check_out_time.isoformat() if self.check_out_time else None,
            "total_hours": total_hours,
            "status": self.status,
            "overtime_hours": overtime_hours
        }

    @classmethod
    def get_attendance_by_id(cls, session, attendance_id):
        """Get attendance record by ID."""
        try:
            # Convert string to UUID if needed
            if isinstance(attendance_id, str):
                try:
                    attendance_id = uuid.UUID(attendance_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {attendance_id}")
                    return None

            attendance = session.query(cls).filter_by(attendance_id=attendance_id).first()
            return attendance
        except Exception as e:
            app.logger.error(f"Error getting attendance by ID: {e}")
            return None

    @classmethod
    def get_attendance_by_employee_id(cls, session, employee_id, start_date=None, end_date=None, active_employees_only=True):
        """Get attendance records for an employee within a date range, optionally filtered by active employees only."""
        try:
            # Convert string to UUID if needed
            if isinstance(employee_id, str):
                try:
                    employee_id = uuid.UUID(employee_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {employee_id}")
                    return []

            if active_employees_only:
                # Join with Employee table and filter by active status
                from application.Models.employees.employee import Employee
                query = session.query(cls).join(
                    Employee, cls.employee_id == Employee.employee_id
                ).filter(
                    cls.employee_id == employee_id,
                    Employee.status == 'active'
                )
            else:
                query = session.query(cls).filter_by(employee_id=employee_id)

            if start_date:
                query = query.filter(cls.date >= start_date)

            if end_date:
                query = query.filter(cls.date <= end_date)

            return query.order_by(cls.date.desc()).all()
        except Exception as e:
            app.logger.error(f"Error getting attendance by employee ID: {e}")
            return []

    @classmethod
    def get_attendance_by_date(cls, session, date_value, active_employees_only=True):
        """Get all attendance records for a specific date, optionally filtered by active employees only."""
        try:
            if active_employees_only:
                # Join with Employee table and filter by active status
                from application.Models.employees.employee import Employee
                attendance_records = session.query(cls).join(
                    Employee, cls.employee_id == Employee.employee_id
                ).filter(
                    cls.date == date_value,
                    Employee.status == 'active'
                ).all()
            else:
                attendance_records = session.query(cls).filter_by(date=date_value).all()
            return attendance_records
        except Exception as e:
            app.logger.error(f"Error getting attendance by date: {e}")
            return []

    @classmethod
    def get_attendance_records(cls, session, limit=100, active_employees_only=True):
        """Get attendance records with optional limit, filtered by active employees only."""
        try:
            if active_employees_only:
                # Join with Employee table and filter by active status
                from application.Models.employees.employee import Employee
                attendance_records = session.query(cls).join(
                    Employee, cls.employee_id == Employee.employee_id
                ).filter(
                    Employee.status == 'active'
                ).order_by(cls.date.desc()).limit(limit).all()
            else:
                attendance_records = session.query(cls).order_by(cls.date.desc()).limit(limit).all()
            return attendance_records
        except Exception as e:
            app.logger.error(f"Error getting attendance records: {e}")
            return []

    @classmethod
    def get_attendance_by_date_range(cls, session, start_date, end_date, active_employees_only=True):
        """Get attendance records within a date range, optionally filtered by active employees only."""
        try:
            if active_employees_only:
                # Join with Employee table and filter by active status
                from application.Models.employees.employee import Employee
                attendance_records = session.query(cls).join(
                    Employee, cls.employee_id == Employee.employee_id
                ).filter(
                    cls.date >= start_date,
                    cls.date <= end_date,
                    Employee.status == 'active'
                ).order_by(cls.date.desc()).all()
            else:
                attendance_records = session.query(cls).filter(
                    cls.date >= start_date,
                    cls.date <= end_date
                ).order_by(cls.date.desc()).all()
            return attendance_records
        except Exception as e:
            app.logger.error(f"Error getting attendance by date range: {e}")
            return []

    @classmethod
    def create_attendance(cls, session, **kwargs):
        """Create a new attendance record."""
        try:
            # Convert employee_id to UUID if it's a string
            if 'employee_id' in kwargs and isinstance(kwargs['employee_id'], str):
                try:
                    kwargs['employee_id'] = uuid.UUID(kwargs['employee_id'])
                except ValueError:
                    app.logger.error(f"Invalid employee_id UUID format: {kwargs['employee_id']}")
                    return None

            # Convert created_by to UUID if it's a string
            if 'created_by' in kwargs and isinstance(kwargs['created_by'], str):
                try:
                    kwargs['created_by'] = uuid.UUID(kwargs['created_by'])
                except ValueError:
                    app.logger.error(f"Invalid created_by UUID format: {kwargs['created_by']}")
                    return None

            attendance = cls(**kwargs)
            session.add(attendance)
            session.commit()
            app.logger.info(f"Created new attendance record for employee ID: {attendance.employee_id}")
            return attendance
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating attendance record: {e}")
            return None

    @classmethod
    def update_attendance(cls, session, attendance_id, **kwargs):
        """Update an attendance record."""
        try:
            # Convert attendance_id to UUID if it's a string
            if isinstance(attendance_id, str):
                try:
                    attendance_id = uuid.UUID(attendance_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {attendance_id}")
                    return None

            # Convert employee_id to UUID if it's a string
            if 'employee_id' in kwargs and isinstance(kwargs['employee_id'], str):
                try:
                    kwargs['employee_id'] = uuid.UUID(kwargs['employee_id'])
                except ValueError:
                    app.logger.error(f"Invalid employee_id UUID format: {kwargs['employee_id']}")
                    return None

            # Convert created_by to UUID if it's a string
            if 'created_by' in kwargs and isinstance(kwargs['created_by'], str):
                try:
                    kwargs['created_by'] = uuid.UUID(kwargs['created_by'])
                except ValueError:
                    app.logger.error(f"Invalid created_by UUID format: {kwargs['created_by']}")
                    return None

            attendance = session.query(cls).filter_by(attendance_id=attendance_id).first()
            if not attendance:
                app.logger.warning(f"Attendance record with ID {attendance_id} not found")
                return None

            # Update attendance attributes
            for key, value in kwargs.items():
                if hasattr(attendance, key):
                    setattr(attendance, key, value)

            # Recalculate total hours if check-in and check-out times are provided
            if attendance.check_in_time and attendance.check_out_time:
                time_diff = attendance.check_out_time - attendance.check_in_time
                attendance.total_hours = time_diff.total_seconds() / 3600  # Convert to hours

            session.commit()
            app.logger.info(f"Updated attendance record with ID: {attendance_id}")
            return attendance
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error updating attendance record: {e}")
            return None

    @classmethod
    def delete_attendance(cls, session, attendance_id):
        """Delete an attendance record."""
        try:
            # Convert attendance_id to UUID if it's a string
            if isinstance(attendance_id, str):
                try:
                    attendance_id = uuid.UUID(attendance_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {attendance_id}")
                    return False

            attendance = session.query(cls).filter_by(attendance_id=attendance_id).first()
            if not attendance:
                app.logger.warning(f"Attendance record with ID {attendance_id} not found")
                return False

            session.delete(attendance)
            session.commit()
            app.logger.info(f"Deleted attendance record with ID: {attendance_id}")
            return True
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error deleting attendance record: {e}")
            return False

    @classmethod
    def cleanup_old_open_records(cls, session, employee_id=None, max_hours=48):
        """
        Clean up old open attendance records that are older than max_hours.

        Args:
            session: Database session
            employee_id: Specific employee ID to clean up (optional, if None cleans all)
            max_hours: Maximum hours to consider a record as valid open record

        Returns:
            int: Number of records cleaned up
        """
        try:
            current_time = datetime.now()
            cutoff_time = current_time - timedelta(hours=max_hours)

            # Build query for old open records
            query = session.query(cls).filter(
                cls.check_in_time.isnot(None),
                cls.check_out_time.is_(None),
                cls.check_in_time < cutoff_time
            )

            if employee_id:
                # Convert employee_id to UUID if it's a string
                if isinstance(employee_id, str):
                    try:
                        employee_id = uuid.UUID(employee_id)
                    except ValueError:
                        app.logger.error(f"Invalid employee_id UUID format: {employee_id}")
                        return 0
                query = query.filter(cls.employee_id == employee_id)

            old_open_records = query.all()
            cleaned_count = 0

            for record in old_open_records:
                try:
                    # Set check-out time based on shift information
                    checkin_date = record.check_in_time.date()

                    # Check if employee has shift information
                    if record.expected_end_time:
                        # Employee HAS shift - use shift end time
                        auto_checkout_time = datetime.combine(checkin_date, record.expected_end_time)
                        checkout_reason = f"shift end time ({record.expected_end_time})"
                        app.logger.info(f"Cleanup: Using shift end time {auto_checkout_time} for employee {record.employee_id} with shift")
                    else:
                        # Employee has NO shift - use midnight
                        auto_checkout_time = datetime.combine(checkin_date, datetime.min.time().replace(hour=23, minute=59, second=59))
                        checkout_reason = "midnight (no shift assigned)"
                        app.logger.info(f"Cleanup: Using midnight for employee {record.employee_id} without shift")

                    # Calculate total hours
                    time_diff = auto_checkout_time - record.check_in_time
                    total_hours = time_diff.total_seconds() / 3600

                    # Update the record with enhanced note
                    record.check_out_time = auto_checkout_time
                    record.total_hours = total_hours
                    record.notes = f"{record.notes or ''} [Auto-closed: cleanup of old open record at {checkout_reason}]".strip()

                    cleaned_count += 1
                    app.logger.info(f"Cleaned up old open attendance record {record.attendance_id} for employee {record.employee_id}, total hours: {total_hours:.2f}")

                except Exception as e:
                    app.logger.error(f"Error cleaning up attendance record {record.attendance_id}: {e}")
                    continue

            if cleaned_count > 0:
                session.commit()
                app.logger.info(f"Successfully cleaned up {cleaned_count} old open attendance records")

            return cleaned_count

        except Exception as e:
            session.rollback()
            app.logger.error(f"Error in cleanup_old_open_records: {e}")
            return 0

    @classmethod
    def should_auto_close_based_on_shift(cls, open_attendance, current_time):
        """
        Check if an open attendance record should be auto-closed based on shift timing.

        Args:
            open_attendance: Attendance record with check_in but no check_out
            current_time: Current datetime from biometric record

        Returns:
            tuple: (should_close: bool, auto_close_time: datetime or None)
        """
        try:
            # Must have shift info to use shift-based logic
            if not (open_attendance.shift_id and open_attendance.expected_end_time):
                app.logger.debug(f"No shift info available for attendance {open_attendance.attendance_id}, skipping shift-based auto-close")
                return False, None

            # Get the date of the open attendance record
            attendance_date = open_attendance.check_in_time.date()

            # Convert expected_end_time (Time) to full datetime using attendance date
            expected_end_datetime = datetime.combine(
                attendance_date,
                open_attendance.expected_end_time
            )

            # Handle night shifts that cross midnight
            if open_attendance.shift and open_attendance.shift.is_night_shift:
                # If current time is next day and it's a night shift,
                # the shift end is actually next day
                if current_time.date() > attendance_date:
                    expected_end_datetime = datetime.combine(
                        attendance_date + timedelta(days=1),
                        open_attendance.expected_end_time
                    )
                    app.logger.debug(f"Night shift detected, adjusted end time to next day: {expected_end_datetime}")

            # Calculate hours past expected shift end
            time_diff = current_time - expected_end_datetime
            hours_past_shift = time_diff.total_seconds() / 3600

            app.logger.info(f"Employee {open_attendance.employee_id}: {hours_past_shift:.1f} hours past shift end ({expected_end_datetime})")

            # If more than 5 hours past shift end, auto-close at shift end time
            if hours_past_shift > 5:
                app.logger.info(f"Auto-closing attendance {open_attendance.attendance_id}: >5 hours past shift end")
                return True, expected_end_datetime

            # Within 5-hour window - treat as legitimate overtime checkout
            app.logger.debug(f"Within 5-hour window, treating as legitimate checkout")
            return False, None

        except Exception as e:
            app.logger.error(f"Error in shift-based auto-close check: {e}")
            return False, None

    @classmethod
    def process_attendance_from_record(cls, session, record):
        """Process a biometric record and update attendance accordingly.

        Args:
            session: Database session
            record: Dictionary containing record data (enroll_id, records_time, etc.)

        Returns:
            dict: Result of the operation with status and message
        """
        try:
            # Import here to avoid circular imports
            from application.Models.Person import Person
            from application.Models.employees.employee import Employee

            # Extract data from record
            enroll_id = record.get('enroll_id')
            record_time_str = record.get('records_time')
            device_sn = record.get('device_serial_num')
            record_id = record.get('id', None)

            # Validate required data
            if not enroll_id or not record_time_str:
                app.logger.error(f"Missing required data in record: {record}")
                return {"success": False, "message": "Missing required data in record"}

            # Convert record time to datetime if it's a string
            if isinstance(record_time_str, str):
                try:
                    record_time = datetime.strptime(record_time_str, '%Y-%m-%d %H:%M:%S')
                except ValueError:
                    app.logger.error(f"Invalid record time format: {record_time_str}")
                    return {"success": False, "message": "Invalid record time format"}
            else:
                record_time = record_time_str

            # Get the record date (without time)
            record_date = record_time.date()

            # Find the person associated with this enroll_id
            app.logger.info(f"Looking up person with enroll_id: {enroll_id}")
            person = Person.get_person_given_id(session, enroll_id)

            if not person:
                app.logger.warning(f"No person found for enroll_id {enroll_id}")
                return {"success": False, "message": f"No person found for enroll_id {enroll_id}"}

            app.logger.info(f"Found person: {person.name} (ID: {person.id})")

            if not person.employee_id:
                app.logger.warning(f"Person with enroll_id {enroll_id} has no employee_id")
                return {"success": False, "message": f"Person with enroll_id {enroll_id} has no employee_id"}

            employee_id = person.employee_id
            app.logger.info(f"Found employee_id: {employee_id}")

            # Check if this employee exists in the Employee table
            employee = Employee.get_employee_by_id(session, employee_id)
            if not employee:
                app.logger.warning(f"Employee with ID {employee_id} not found in Employee table")
                return {"success": False, "message": f"Employee with ID {employee_id} not found"}

            # Proactively clean up any old open records for this employee (older than 48 hours)
            try:
                cleaned_count = cls.cleanup_old_open_records(session, employee_id, max_hours=48)
                if cleaned_count > 0:
                    app.logger.info(f"Cleaned up {cleaned_count} old open records for employee {employee_id}")
            except Exception as cleanup_error:
                app.logger.error(f"Error during cleanup of old records: {cleanup_error}")
                # Continue processing even if cleanup fails

            # Define time window to prevent multiple check-ins/check-outs (reduced to 5 minutes for better accuracy)
            time_window_minutes = 8

            # Check for any recent records within the time window to prevent false attendance
            # Only check if there's a SUCCESSFUL attendance record created recently
            try:
                recent_time_threshold = record_time - timedelta(minutes=time_window_minutes)
                recent_records = session.query(cls).filter(
                    cls.employee_id == employee_id,
                    cls.date == record_date,
                    db.or_(
                        cls.check_in_time > recent_time_threshold,
                        cls.check_out_time > recent_time_threshold
                    )
                ).all()

                if recent_records:
                    app.logger.info(f"Ignoring record within {time_window_minutes} minute window for employee {employee_id}. Recent attendance exists.")
                    return {"success": True, "message": "Record ignored (within time window)"}
            except Exception as e:
                app.logger.error(f"Error checking for recent records: {e}")
                # Continue processing even if this check fails

            # Check if there's an open attendance record (has check-in but no check-out)
            # Remove date restriction to allow for cross-day shifts and overnight work
            try:
                app.logger.info(f"Checking for open attendance record for employee {employee_id} (any date)")
                open_attendance = session.query(cls).filter(
                    cls.employee_id == employee_id,
                    cls.check_in_time.isnot(None),
                    cls.check_out_time.is_(None)
                ).order_by(cls.check_in_time.desc()).first()

                if open_attendance:
                    # First, check if we should auto-close based on shift timing (5-hour rule)
                    should_close_shift, shift_auto_close_time = cls.should_auto_close_based_on_shift(open_attendance, record_time)

                    if should_close_shift:
                        app.logger.info(f"Auto-closing attendance {open_attendance.attendance_id} based on shift timing (>5 hours past shift end)")

                        try:
                            # Auto-close at shift end time
                            open_attendance.check_out_time = shift_auto_close_time

                            # Calculate total hours worked (from actual check-in to shift end time)
                            # This preserves the actual hours worked during the shift
                            time_diff = shift_auto_close_time - open_attendance.check_in_time
                            total_hours = time_diff.total_seconds() / 3600
                            open_attendance.total_hours = total_hours

                            # Add note indicating shift-based auto-close with actual hours preserved
                            open_attendance.notes = f"{open_attendance.notes or ''} [Auto-closed: >5hrs past shift end, assumed forgotten checkout. Hours: {total_hours:.2f}]".strip()

                            session.commit()
                            app.logger.info(f"Auto-closed attendance record {open_attendance.attendance_id} at shift end time {shift_auto_close_time}, total hours: {total_hours:.2f}")

                        except Exception as close_error:
                            app.logger.error(f"Error auto-closing attendance record based on shift: {close_error}")
                            session.rollback()

                        # Set to None so we create a new check-in record
                        open_attendance = None

                    else:
                        # Check if the open attendance is reasonable (not too old) - 48-hour fallback
                        hours_since_checkin = (record_time - open_attendance.check_in_time).total_seconds() / 3600

                        # Allow up to 48 hours for very long shifts, but log if it's unusually long
                        if hours_since_checkin > 48:
                            app.logger.warning(f"Found very old open attendance record ({hours_since_checkin:.1f} hours old) for employee {employee_id}. Auto-closing and treating current record as new check-in.")

                            # Auto-close the old open record using 48-hour logic
                            try:
                                # Set check-out time based on shift information
                                old_checkin_date = open_attendance.check_in_time.date()

                                # Check if employee has shift information
                                if open_attendance.expected_end_time:
                                    # Employee HAS shift - use shift end time
                                    auto_checkout_time = datetime.combine(old_checkin_date, open_attendance.expected_end_time)
                                    checkout_reason = f"shift end time ({open_attendance.expected_end_time})"
                                    app.logger.info(f"48hr rule: Using shift end time {auto_checkout_time} for employee {employee_id} with shift")
                                else:
                                    # Employee has NO shift - use midnight
                                    auto_checkout_time = datetime.combine(old_checkin_date, datetime.min.time().replace(hour=23, minute=59, second=59))
                                    checkout_reason = "midnight (no shift assigned)"
                                    app.logger.info(f"48hr rule: Using midnight for employee {employee_id} without shift")

                                # Calculate total hours for the auto-closed record
                                time_diff = auto_checkout_time - open_attendance.check_in_time
                                total_hours = time_diff.total_seconds() / 3600

                                # Update the old record with enhanced note
                                open_attendance.check_out_time = auto_checkout_time
                                open_attendance.total_hours = total_hours
                                open_attendance.notes = f"{open_attendance.notes or ''} [Auto-closed: old open record detected (48hr rule) at {checkout_reason}]".strip()

                                session.commit()
                                app.logger.info(f"Auto-closed old attendance record {open_attendance.attendance_id} with checkout time {auto_checkout_time}, total hours: {total_hours:.2f}")

                            except Exception as close_error:
                                app.logger.error(f"Error auto-closing old attendance record: {close_error}")
                                session.rollback()

                            # Set to None so we create a new record
                            open_attendance = None
                        else:
                            app.logger.info(f"Found open attendance record: {open_attendance.attendance_id} ({hours_since_checkin:.1f} hours ago)")
                            # This is a check-out for an existing open record (within 5-hour window and within 48 hours)
                            open_attendance.check_out_time = record_time
                            open_attendance.source_record_id = str(record_id) if record_id else open_attendance.source_record_id

                            # Calculate total hours for this session
                            time_diff = open_attendance.check_out_time - open_attendance.check_in_time
                            total_hours = time_diff.total_seconds() / 3600  # Convert to hours
                            open_attendance.total_hours = total_hours

                            # Check for overtime if shift is assigned
                            if open_attendance.shift_id and open_attendance.expected_end_time:
                                from application.Models.employees.shift import Shift

                                # Get the shift
                                shift = Shift.get_shift_by_id(session, open_attendance.shift_id)

                                if shift:
                                    # Calculate expected end time as datetime
                                    expected_end_datetime = datetime.combine(open_attendance.date, open_attendance.expected_end_time)

                                    # Add grace period for early departure
                                    grace_minutes = shift.grace_period_early
                                    earliest_departure = expected_end_datetime - timedelta(minutes=grace_minutes)

                                    # Check if checkout is after expected end time (overtime)
                                    if open_attendance.check_out_time > expected_end_datetime:
                                        overtime_diff = open_attendance.check_out_time - expected_end_datetime
                                        overtime_hours = overtime_diff.total_seconds() / 3600

                                        # Only count as overtime if it's significant (e.g., more than 15 minutes)
                                        if overtime_hours > 0.25:  # 15 minutes = 0.25 hours
                                            open_attendance.is_overtime = True
                                            open_attendance.overtime_hours = overtime_hours
                                            app.logger.info(f"Overtime detected: {overtime_hours:.2f} hours")

                                    # Check if checkout is before expected end time (early departure)
                                    elif open_attendance.check_out_time < earliest_departure:
                                        # Employee left early, beyond grace period
                                        early_minutes = (earliest_departure - open_attendance.check_out_time).total_seconds() / 60
                                        app.logger.info(f"Early departure detected. Left at {open_attendance.check_out_time.time()}, expected after {earliest_departure.time()}, {int(early_minutes)} minutes early")

                                        # Update status with try-catch to ensure attendance recording continues
                                        try:
                                            # Determine combined status based on current status
                                            if open_attendance.status == 'late':
                                                # Employee was late AND left early
                                                status_updated = open_attendance.update_status_safely(
                                                    'late-early-departure',
                                                    f"Late arrival + early departure ({int(early_minutes)} min early)"
                                                )
                                            else:
                                                # Employee was on time but left early
                                                status_updated = open_attendance.update_status_safely(
                                                    'early-departure',
                                                    f"Early departure ({int(early_minutes)} min early)"
                                                )

                                            # Add note about early departure
                                            early_note = f"Left {int(early_minutes)} minutes early"
                                            note_added = open_attendance.add_note_safely(early_note)

                                            if not status_updated:
                                                app.logger.warning(f"Status update failed for early departure, but attendance will still be recorded")
                                            if not note_added:
                                                app.logger.warning(f"Note addition failed for early departure, but attendance will still be recorded")

                                        except Exception as status_error:
                                            # Log the error but don't fail the attendance recording
                                            app.logger.error(f"Error updating status for early departure: {status_error}")
                                            app.logger.info("Continuing with attendance recording despite status update failure")

                            try:
                                session.commit()
                                app.logger.info(f"Updated attendance record with check-out at {record_time} for employee {employee_id}")
                                return {
                                    "success": True,
                                    "message": "Updated attendance with check-out time",
                                    "attendance": open_attendance.to_dict()
                                }
                            except Exception as e:
                                session.rollback()
                                app.logger.error(f"Error updating attendance record: {e}")
                                return {"success": False, "message": f"Error updating attendance: {str(e)}"}

                # If no open attendance found (or it was too old), create new check-in
                if not open_attendance:
                    app.logger.info(f"No valid open attendance record found, creating new check-in")
                    # This is a new check-in (either first of the day or after a completed check-in/check-out cycle)
                    # Check if employee has an assigned shift for this day
                    from application.Models.employees.employee_shift import EmployeeShift
                    from application.Models.employees.shift import Shift

                    # Get current day of week (1-7, where 1 is Monday)
                    day_of_week = record_date.isoweekday()

                    # Find active shift assignment for this employee
                    shift_assignments = EmployeeShift.get_employee_shifts(
                        session,
                        employee_id,
                        active_only=True,
                        current_date=record_date
                    )

                    shift_id = None
                    expected_start_time = None
                    expected_end_time = None
                    status = 'present'  # Default status

                    if shift_assignments:
                        # Use the first active shift assignment
                        shift_assignment = shift_assignments[0]
                        shift = Shift.get_shift_by_id(session, shift_assignment.shift_id)

                        if shift:
                            # Check if this day is a working day for this shift
                            working_days = shift_assignment.custom_working_days or shift.working_days
                            working_days = [int(d) for d in working_days.split(',') if d]

                            if day_of_week in working_days:
                                shift_id = shift.shift_id

                                # Use custom times if set, otherwise use shift default times
                                start_time = shift_assignment.custom_start_time or shift.start_time
                                end_time = shift_assignment.custom_end_time or shift.end_time

                                # Set expected times
                                expected_start_time = start_time
                                expected_end_time = end_time

                                # Determine status based on arrival time
                                if start_time:
                                    # Convert datetime to time for comparison
                                    record_time_only = record_time.time()

                                    # Calculate late threshold with grace period
                                    grace_minutes = shift.grace_period_late
                                    late_threshold = datetime.combine(record_date, start_time) + timedelta(minutes=grace_minutes)
                                    late_threshold_time = late_threshold.time()

                                    # Check if employee is late
                                    if record_time_only > late_threshold_time:
                                        try:
                                            status = 'late'
                                            late_minutes = (record_time_only.hour * 60 + record_time_only.minute) - (late_threshold_time.hour * 60 + late_threshold_time.minute)
                                            app.logger.info(f"Employee {employee_id} is late. Arrived at {record_time_only}, expected before {late_threshold_time}, {late_minutes} minutes late")
                                        except Exception as late_calc_error:
                                            # If late calculation fails, still mark as late but with basic info
                                            status = 'late'
                                            app.logger.error(f"Error calculating late minutes for employee {employee_id}: {late_calc_error}")
                                            app.logger.info(f"Employee {employee_id} is late. Arrived at {record_time_only}, expected before {late_threshold_time}")

                    attendance_data = {
                        'employee_id': employee_id,
                        'date': record_date,
                        'check_in_time': record_time,
                        'check_out_time': None,  # Will be updated with later records
                        'total_hours': None,     # Will be calculated when check-out is recorded
                        'status': status,        # Status based on shift rules
                        'source': 'biometric',   # Source is biometric
                        'source_record_id': str(record_id) if record_id else None,
                        'shift_id': shift_id,    # Reference to the shift
                        'expected_start_time': expected_start_time,
                        'expected_end_time': expected_end_time,
                        'notes': f"Check-in from device {device_sn}"
                    }

                    app.logger.info(f"Creating new attendance with data: {attendance_data}")
                    try:
                        new_attendance = cls.create_attendance(session, **attendance_data)
                        if new_attendance:
                            app.logger.info(f"Created new attendance record with ID: {new_attendance.attendance_id}")
                            return {
                                "success": True,
                                "message": "Created new attendance record with check-in",
                                "attendance": new_attendance.to_dict()
                            }
                        else:
                            app.logger.error("create_attendance returned None")
                            return {"success": False, "message": "Failed to create attendance record"}
                    except Exception as e:
                        app.logger.error(f"Error creating attendance record: {e}")
                        import traceback
                        app.logger.error(traceback.format_exc())
                        return {"success": False, "message": f"Error creating attendance: {str(e)}"}
            except Exception as e:
                app.logger.error(f"Error in attendance processing: {e}")
                import traceback
                app.logger.error(traceback.format_exc())
                return {"success": False, "message": f"Error in attendance processing: {str(e)}"}

        except Exception as e:
            session.rollback()
            app.logger.error(f"Error processing attendance from record: {e}")
            import traceback
            app.logger.error(traceback.format_exc())
            return {"success": False, "message": f"Error processing attendance: {str(e)}"}
