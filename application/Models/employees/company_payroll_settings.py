from application.database import db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime, date
from flask import current_app

class CompanyPayrollSettings(db.Model):
    """Model representing company-specific payroll configurations (stored in tenant database)."""
    __tablename__ = 'company_payroll_settings'

    settings_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    company_id = db.Column(db.String(36), nullable=False)  # Reference to central DB companies
    default_pay_frequency = db.Column(db.String(20), default='MONTHLY', nullable=False)  # MONTHLY, WEEKLY, DAILY
    payroll_cutoff_day = db.Column(db.Integer, default=25, nullable=False)  # Day of month for payroll cutoff
    payment_day = db.Column(db.Integer, default=30, nullable=False)  # Day of month for salary payment
    overtime_enabled = db.Column(db.<PERSON>, default=False, nullable=False)
    overtime_rate_multiplier = db.Column(db.Numeric(3, 2), default=1.5, nullable=False)  # e.g., 1.5 for time and half
    weekend_overtime_multiplier = db.Column(db.Numeric(3, 2), default=2.0, nullable=False)  # e.g., 2.0 for double time
    holiday_overtime_multiplier = db.Column(db.Numeric(3, 2), default=2.5, nullable=False)  # e.g., 2.5 for holiday work
    auto_generate_payslips = db.Column(db.Boolean, default=True, nullable=False)
    require_approval = db.Column(db.Boolean, default=True, nullable=False)
    effective_from = db.Column(db.Date, nullable=False)
    effective_to = db.Column(db.Date, nullable=True)  # null means currently active
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    # Unique constraint: one active setting per company at any time
    __table_args__ = (
        db.Index('idx_company_payroll_settings_active', 'company_id', 'effective_from', 'effective_to'),
    )

    def __str__(self):
        """Return a string representation of the object."""
        return f"CompanyPayrollSettings [settings_id={self.settings_id}, company_id={self.company_id}, pay_frequency={self.default_pay_frequency}]"

    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "settings_id": str(self.settings_id),
            "company_id": self.company_id,
            "default_pay_frequency": self.default_pay_frequency,
            "payroll_cutoff_day": self.payroll_cutoff_day,
            "payment_day": self.payment_day,
            "overtime_enabled": self.overtime_enabled,
            "overtime_rate_multiplier": float(self.overtime_rate_multiplier),
            "weekend_overtime_multiplier": float(self.weekend_overtime_multiplier),
            "holiday_overtime_multiplier": float(self.holiday_overtime_multiplier),
            "auto_generate_payslips": self.auto_generate_payslips,
            "require_approval": self.require_approval,
            "effective_from": self.effective_from.strftime('%Y-%m-%d') if self.effective_from else None,
            "effective_to": self.effective_to.strftime('%Y-%m-%d') if self.effective_to else None,
            "is_active": self.is_active,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

    @classmethod
    def create_settings(cls, session, **kwargs):
        """Create new company payroll settings."""
        try:
            settings = cls(**kwargs)
            session.add(settings)
            session.commit()
            current_app.logger.info(f"Created payroll settings for company {settings.company_id}")
            return settings
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error creating company payroll settings: {e}")
            return None

    @classmethod
    def get_current_settings(cls, session, company_id, date=None):
        """Get the current active settings for a company on a specific date."""
        if date is None:
            date = datetime.now().date()

        return session.query(cls).filter(
            cls.company_id == company_id,
            cls.effective_from <= date,
            db.or_(
                cls.effective_to.is_(None),
                cls.effective_to >= date
            ),
            cls.is_active == True
        ).first()

    @classmethod
    def get_settings_history(cls, session, company_id):
        """Get the settings history for a company."""
        return session.query(cls).filter(
            cls.company_id == company_id
        ).order_by(cls.effective_from.desc()).all()

    @classmethod
    def update_settings(cls, session, company_id, new_settings, effective_from=None):
        """Update company settings by creating a new record and ending the current one."""
        try:
            if effective_from is None:
                effective_from = datetime.now().date()

            # Get current settings
            current_settings = cls.get_current_settings(session, company_id, effective_from)
            if current_settings:
                # End the current settings record
                current_settings.effective_to = effective_from
                current_settings.is_active = False

            # Create new settings record
            new_settings_data = {
                'company_id': company_id,
                'effective_from': effective_from,
                'effective_to': None,
                'is_active': True,
                **new_settings
            }

            # If updating existing settings, copy unchanged values
            if current_settings:
                for key, value in current_settings.to_dict().items():
                    if key not in new_settings_data and key not in ['settings_id', 'effective_from', 'effective_to', 'is_active', 'created_at', 'updated_at']:
                        new_settings_data[key] = value

            new_settings_record = cls.create_settings(session, **new_settings_data)
            
            if new_settings_record:
                session.commit()
                current_app.logger.info(f"Updated payroll settings for company {company_id}")
                return new_settings_record
            else:
                session.rollback()
                return None

        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error updating company payroll settings: {e}")
            return None

    @classmethod
    def create_default_settings(cls, session, company_id):
        """Create default payroll settings for a new company."""
        default_settings = {
            'company_id': company_id,
            'default_pay_frequency': 'MONTHLY',
            'payroll_cutoff_day': 25,
            'payment_day': 30,
            'overtime_enabled': False,
            'overtime_rate_multiplier': 1.5,
            'weekend_overtime_multiplier': 2.0,
            'holiday_overtime_multiplier': 2.5,
            'auto_generate_payslips': True,
            'require_approval': True,
            'effective_from': date.today(),
            'effective_to': None,
            'is_active': True
        }

        try:
            settings = cls.create_settings(session, **default_settings)
            current_app.logger.info(f"Created default payroll settings for company {company_id}")
            return settings
        except Exception as e:
            current_app.logger.error(f"Error creating default payroll settings: {e}")
            return None

    @classmethod
    def deactivate_settings(cls, session, settings_id):
        """Deactivate payroll settings (soft delete)."""
        try:
            settings = session.query(cls).filter_by(settings_id=settings_id).first()
            if not settings:
                return False

            settings.is_active = False
            settings.effective_to = datetime.now().date()
            session.commit()
            current_app.logger.info(f"Deactivated payroll settings: {settings_id}")
            return True
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error deactivating payroll settings: {e}")
            return False

    def get_next_payroll_cutoff(self, from_date=None):
        """Get the next payroll cutoff date."""
        if from_date is None:
            from_date = date.today()
        
        # Calculate next cutoff based on cutoff day
        year = from_date.year
        month = from_date.month
        
        # If we're past this month's cutoff, move to next month
        if from_date.day > self.payroll_cutoff_day:
            if month == 12:
                year += 1
                month = 1
            else:
                month += 1
        
        try:
            cutoff_date = date(year, month, self.payroll_cutoff_day)
        except ValueError:
            # Handle months with fewer days (e.g., February 30)
            import calendar
            last_day = calendar.monthrange(year, month)[1]
            cutoff_date = date(year, month, min(self.payroll_cutoff_day, last_day))
        
        return cutoff_date

    def get_next_payment_date(self, from_date=None):
        """Get the next payment date."""
        if from_date is None:
            from_date = date.today()
        
        # Calculate next payment based on payment day
        year = from_date.year
        month = from_date.month
        
        # If we're past this month's payment day, move to next month
        if from_date.day > self.payment_day:
            if month == 12:
                year += 1
                month = 1
            else:
                month += 1
        
        try:
            payment_date = date(year, month, self.payment_day)
        except ValueError:
            # Handle months with fewer days
            import calendar
            last_day = calendar.monthrange(year, month)[1]
            payment_date = date(year, month, min(self.payment_day, last_day))
        
        return payment_date

    def calculate_overtime_rate(self, base_rate, overtime_type='REGULAR'):
        """Calculate overtime rate based on type."""
        multipliers = {
            'REGULAR': self.overtime_rate_multiplier,
            'WEEKEND': self.weekend_overtime_multiplier,
            'HOLIDAY': self.holiday_overtime_multiplier
        }
        
        multiplier = multipliers.get(overtime_type, self.overtime_rate_multiplier)
        return float(base_rate) * float(multiplier)
