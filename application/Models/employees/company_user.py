from application.database import db
import uuid
import hashlib
import secrets
from sqlalchemy.dialects.postgresql import UUID
from flask import current_app as app
from datetime import datetime
from application.Models.employees.employee import Employee

class CompanyUser(db.Model):
    """Model representing a company-specific user."""
    __tablename__ = 'company_users'

    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    username = db.Column(db.String(255), unique=True, nullable=False)
    email = db.Column(db.String(255), unique=True, nullable=True)
    first_name = db.Column(db.String(255), nullable=True)
    last_name = db.Column(db.String(255), nullable=False)
    password = db.Column(db.String(255), nullable=False)
    salt = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(50), nullable=False)  # admin, hr, employee, etc.
    employee_id = db.Column(UUID(as_uuid=True), db.ForeignKey('employees.employee_id'), nullable=True)
    phone_number = db.Column(db.String(20), nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, onupdate=db.func.now())
    last_login = db.Column(db.DateTime, nullable=True)

    # Relationship with Employee model
    employee = db.relationship('Employee', backref=db.backref('user', lazy=True, uselist=False))

    def __repr__(self):
        """Return a string representation of the object."""
        return f"CompanyUser(id={self.id}, username={self.username}, role={self.role})"

    def to_dict(self):
        """Convert user object to dictionary."""
        user_dict = {
            "id": str(self.id),
            "username": self.username,
            "email": self.email,
            "first_name": self.first_name,
            "last_name": self.last_name,
            "full_name": f"{self.first_name} {self.last_name}" if self.first_name else self.last_name,
            "role": self.role,
            "employee_id": str(self.employee_id) if self.employee_id else None,
            "phone_number": self.phone_number,
            "is_active": self.is_active,
            "created_at": self.created_at.strftime("%Y-%m-%d %H:%M:%S") if self.created_at else None,
            "updated_at": self.updated_at.strftime("%Y-%m-%d %H:%M:%S") if self.updated_at else None
        }

        # Include employee information if available
        if self.employee:
            user_dict["employee"] = {
                "id": str(self.employee.employee_id),
                "first_name": self.employee.first_name,
                "last_name": self.employee.last_name,
                "full_name": f"{self.employee.first_name} {self.employee.last_name}",
                "department_id": str(self.employee.department_id) if self.employee.department_id else None
            }

        return user_dict

    @staticmethod
    def hash_password(password, salt=None):
        """Hash a password with a salt."""
        if not salt:
            salt = secrets.token_hex(16)

        salted_password = salt + password
        hashed_password = hashlib.sha256(salted_password.encode()).hexdigest()

        return hashed_password, salt

    @staticmethod
    def verify_password(user, password):
        """Verify a password against a user's stored hash."""
        salted_password = user.salt + password
        hashed_password = hashlib.sha256(salted_password.encode()).hexdigest()
        return hashed_password == user.password

    @classmethod
    def create_user(cls, session, username, password, role, last_name, first_name=None,
                   email=None, phone_number=None, employee_id=None, is_active=True):
        """Create a new user in the company database."""
        try:
            # Check if username already exists
            existing_user = session.query(cls).filter_by(username=username).first()
            if existing_user:
                return None, "Username already exists"

            # Check if email already exists (if provided)
            if email:
                existing_email = session.query(cls).filter_by(email=email).first()
                if existing_email:
                    return None, "Email already exists"

            # Hash the password
            hashed_password, salt = cls.hash_password(password)

            # Convert employee_id to UUID if it's a string
            if employee_id and isinstance(employee_id, str):
                try:
                    employee_id = uuid.UUID(employee_id)
                except ValueError:
                    app.logger.error(f"Invalid employee_id UUID format: {employee_id}")
                    return None, "Invalid employee ID format"

            # Create new user
            new_user = cls(
                username=username,
                password=hashed_password,
                salt=salt,
                role=role,
                first_name=first_name,
                last_name=last_name,
                email=email,
                phone_number=phone_number,
                employee_id=employee_id,
                is_active=is_active
            )

            session.add(new_user)
            session.commit()

            app.logger.info(f"Created new company user: {username}")
            return new_user, None

        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating company user: {str(e)}")
            return None, str(e)

    @classmethod
    def get_user_by_id(cls, session, user_id):
        """Get a user by ID."""
        try:
            if isinstance(user_id, str):
                user_id = uuid.UUID(user_id)
            return session.query(cls).filter_by(id=user_id).first()
        except Exception as e:
            app.logger.error(f"Error getting user by ID: {str(e)}")
            return None

    @classmethod
    def get_user_by_username(cls, session, username):
        """Get a user by username."""
        try:
            return session.query(cls).filter_by(username=username).first()
        except Exception as e:
            app.logger.error(f"Error getting user by username: {str(e)}")
            return None

    @classmethod
    def get_user_by_email(cls, session, email):
        """Get a user by email."""
        try:
            return session.query(cls).filter_by(email=email).first()
        except Exception as e:
            app.logger.error(f"Error getting user by email: {str(e)}")
            return None

    @classmethod
    def get_user_by_employee_id(cls, session, employee_id):
        """Get a user by employee ID."""
        try:
            if isinstance(employee_id, str):
                employee_id = uuid.UUID(employee_id)
            return session.query(cls).filter_by(employee_id=employee_id).first()
        except Exception as e:
            app.logger.error(f"Error getting user by employee ID: {str(e)}")
            return None

    @classmethod
    def get_all_users(cls, session, page=1, per_page=10):
        """Get all users with pagination."""
        try:
            users = session.query(cls).order_by(cls.created_at.desc())

            # Apply pagination
            total = users.count()
            users = users.limit(per_page).offset((page - 1) * per_page).all()

            return users, total
        except Exception as e:
            app.logger.error(f"Error getting all users: {str(e)}")
            return [], 0

    @classmethod
    def update_user(cls, session, user_id, **kwargs):
        """Update a user's information."""
        try:
            user = cls.get_user_by_id(session, user_id)
            if not user:
                return False, "User not found"

            # Handle password update separately
            if 'password' in kwargs:
                hashed_password, salt = cls.hash_password(kwargs.pop('password'))
                user.password = hashed_password
                user.salt = salt

            # Handle employee_id conversion if it's a string
            if 'employee_id' in kwargs and isinstance(kwargs['employee_id'], str):
                try:
                    kwargs['employee_id'] = uuid.UUID(kwargs['employee_id'])
                except ValueError:
                    return False, "Invalid employee ID format"

            # Update other fields
            for key, value in kwargs.items():
                if hasattr(user, key):
                    setattr(user, key, value)

            session.commit()
            return True, None
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error updating user: {str(e)}")
            return False, str(e)

    @classmethod
    def delete_user(cls, session, user_id):
        """Delete a user."""
        try:
            user = cls.get_user_by_id(session, user_id)
            if not user:
                return False, "User not found"

            session.delete(user)
            session.commit()
            return True, None
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error deleting user: {str(e)}")
            return False, str(e)
