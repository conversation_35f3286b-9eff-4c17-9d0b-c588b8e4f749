from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUID
from flask import current_app as app
from datetime import datetime

class Department(db.Model):
    """Model representing a department."""
    __tablename__ = 'departments'

    department_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text, nullable=True)
    manager_id = db.Column(UUID(as_uuid=True), nullable=True)  # Reference to an employee who manages this department
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    def __str__(self):
        """Return a string representation of the object."""
        return f"Department [department_id={self.department_id}, name={self.name}]"

    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "department_id": str(self.department_id),
            "name": self.name,
            "description": self.description,
            "manager_id": str(self.manager_id) if self.manager_id else None,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

    @classmethod
    def get_department_by_id(cls, session, department_id):
        """Get a department by ID."""
        try:
            # Convert string to UUID if needed
            if isinstance(department_id, str):
                try:
                    department_id = uuid.UUID(department_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {department_id}")
                    return None

            department = session.query(cls).filter_by(department_id=department_id).first()
            return department
        except Exception as e:
            app.logger.error(f"Error getting department by ID: {e}")
            return None

    @classmethod
    def get_all_departments(cls, session):
        """Get all departments."""
        try:
            departments = session.query(cls).all()
            return departments
        except Exception as e:
            app.logger.error(f"Error getting all departments: {e}")
            return []

    @classmethod
    def get_paginated_departments(cls, session, page=1, per_page=10, search_term=None, sort_by='name', sort_order='asc'):
        """Get paginated departments with optional filtering and sorting.

        Args:
            session: Database session
            page: Page number (starting from 1)
            per_page: Number of items per page
            search_term: Search in department name and description
            sort_by: Field to sort by (name, created_at)
            sort_order: Sort order (asc, desc)

        Returns:
            tuple: (total_count, departments)
        """
        try:
            query = session.query(cls)

            # Apply search filter if provided
            if search_term:
                search_pattern = f"%{search_term}%"
                query = query.filter(
                    db.or_(
                        cls.name.ilike(search_pattern),
                        cls.description.ilike(search_pattern)
                    )
                )

            # Apply sorting
            if sort_by == 'name':
                order_column = cls.name
            elif sort_by == 'created_at':
                order_column = cls.created_at
            else:
                order_column = cls.name  # Default sort by name

            if sort_order.lower() == 'desc':
                order_column = order_column.desc()
            else:
                order_column = order_column.asc()

            query = query.order_by(order_column)

            # Get total count before pagination
            total_count = query.count()

            # Apply pagination
            departments = query.offset((page - 1) * per_page).limit(per_page).all()

            return total_count, departments
        except Exception as e:
            app.logger.error(f"Error getting paginated departments: {e}")
            return 0, []

    @classmethod
    def create_department(cls, session, **kwargs):
        """Create a new department."""
        try:
            # Convert manager_id to UUID if it's a string
            if 'manager_id' in kwargs and isinstance(kwargs['manager_id'], str):
                try:
                    kwargs['manager_id'] = uuid.UUID(kwargs['manager_id'])
                except ValueError:
                    app.logger.error(f"Invalid manager_id UUID format: {kwargs['manager_id']}")
                    return None

            department = cls(**kwargs)
            session.add(department)
            session.commit()
            app.logger.info(f"Created new department: {department.name}")
            return department
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating department: {e}")
            return None

    @classmethod
    def update_department(cls, session, department_id, **kwargs):
        """Update a department."""
        try:
            # Convert department_id to UUID if it's a string
            if isinstance(department_id, str):
                try:
                    department_id = uuid.UUID(department_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {department_id}")
                    return None

            # Convert manager_id to UUID if it's a string
            if 'manager_id' in kwargs and isinstance(kwargs['manager_id'], str):
                try:
                    kwargs['manager_id'] = uuid.UUID(kwargs['manager_id'])
                except ValueError:
                    app.logger.error(f"Invalid manager_id UUID format: {kwargs['manager_id']}")
                    return None

            department = session.query(cls).filter_by(department_id=department_id).first()
            if not department:
                app.logger.warning(f"Department with ID {department_id} not found")
                return None

            # Update department attributes
            for key, value in kwargs.items():
                if hasattr(department, key):
                    setattr(department, key, value)

            session.commit()
            app.logger.info(f"Updated department: {department.name}")
            return department
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error updating department: {e}")
            return None

    @classmethod
    def delete_department(cls, session, department_id):
        """Delete a department."""
        try:
            # Convert department_id to UUID if it's a string
            if isinstance(department_id, str):
                try:
                    department_id = uuid.UUID(department_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {department_id}")
                    return False

            department = session.query(cls).filter_by(department_id=department_id).first()
            if not department:
                app.logger.warning(f"Department with ID {department_id} not found")
                return False

            session.delete(department)
            session.commit()
            app.logger.info(f"Deleted department with ID: {department_id}")
            return True
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error deleting department: {e}")
            return False

    @classmethod
    def get_department_by_name(cls, session, name):
        """Get a department by name (case-insensitive)."""
        try:
            department = session.query(cls).filter(cls.name.ilike(name)).first()
            return department
        except Exception as e:
            app.logger.error(f"Error getting department by name: {e}")
            return None
