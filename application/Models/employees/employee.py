from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUID
from flask import current_app as app
from datetime import datetime
import traceback
from datetime import datetime, date, timedelta


class Employee(db.Model):
    """Model representing an employee."""
    __tablename__ = 'employees'

    employee_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    first_name = db.Column(db.String(255), nullable=False)
    last_name = db.Column(db.String(255), nullable=False)
    id_number = db.Column(db.String(255), unique=True, nullable=True)
    email = db.Column(db.String(255), unique=True, nullable=True)
    phone_number = db.Column(db.String(20), unique=True, nullable=True)
    department_id = db.Column(UUID(as_uuid=True), db.ForeignKey('departments.department_id'), nullable=True)  # Reference to department
    employee_type_id = db.Column(UUID(as_uuid=True), nullable=True)  # Reference to central DB employee_types
    position = db.Column(db.String(255), nullable=True)
    hire_date = db.Column(db.Date, nullable=True)
    date_of_birth = db.Column(db.Date, nullable=True) 
    status = db.Column(db.String(50), default='active', nullable=False)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    # Relationship with Department model
    department = db.relationship('Department', backref=db.backref('employees', lazy='dynamic'))

    def __str__(self):
        """Return a string representation of the object."""
        return f"Employee [employee_id={self.employee_id}, name={self.first_name} {self.last_name}]"

    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "employee_id": str(self.employee_id),
            "first_name": self.first_name,
            "last_name": self.last_name,
            "full_name": f"{self.first_name} {self.last_name}",
            "id_number": self.id_number,
            "email": self.email,
            "phone_number": self.phone_number,
            "department_id": str(self.department_id) if self.department_id else None,
            "employee_type_id": str(self.employee_type_id) if self.employee_type_id else None,
            "position": self.position,
            "hire_date": self.hire_date.strftime('%d-%m-%Y') if self.hire_date else None,
            "date_of_birth": self.date_of_birth.strftime('%d-%m-%Y') if self.date_of_birth else None,
            "status": self.status,
            "created_at": self.created_at.strftime('%d-%m-%Y %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%d-%m-%Y %H:%M:%S') if self.updated_at else None
        }

    @classmethod
    def get_employee_by_id(cls, session, employee_id):
        """Get an employee by employee_id.

        Args:
            session: Database session
            employee_id: UUID or string representation of employee_id

        Returns:
            Employee object or None if not found

        Raises:
            Exception: If there's a database error (does NOT return None on errors)
        """
        # Convert string to UUID if needed
        if isinstance(employee_id, str):
            try:
                employee_id = uuid.UUID(employee_id)
            except ValueError:
                app.logger.error(f"Invalid UUID format: {employee_id}")
                return None

        try:
            employee = session.query(cls).filter_by(employee_id=employee_id).first()
            return employee
        except Exception as e:
            app.logger.error(f"Database error getting employee by ID {employee_id}: {e}")
            # ✅ FIX: Raise the exception instead of returning None
            # This ensures the caller knows there was an error, not that the employee doesn't exist
            raise

    @classmethod
    def get_all_employees(cls, session):
        """Get all employees."""
        try:
            employees = session.query(cls).all()
            return employees
        except Exception as e:
            app.logger.error(f"Error getting all employees: {e}")
            return []

    @classmethod
    def get_by_department(cls, session, department_id):
        """Get all employees in a specific department.

        Args:
            session: Database session
            department_id: Department UUID (string or UUID object)

        Returns:
            list: List of Employee objects
        """
        try:
            # Convert department_id to UUID if it's a string
            if isinstance(department_id, str):
                try:
                    department_id = uuid.UUID(department_id)
                except ValueError:
                    app.logger.error(f"Invalid department_id UUID format: {department_id}")
                    return []

            employees = session.query(cls).filter(cls.department_id == department_id).all()
            return employees
        except Exception as e:
            app.logger.error(f"Error getting employees by department: {e}")
            return []

    @classmethod
    def get_paginated_employees(cls, session, page=1, per_page=10, department_id=None, status=None, search_term=None):
        """Get paginated employees with optional filtering.

        Args:
            session: Database session
            page: Page number (starting from 1)
            per_page: Number of items per page
            department_id: Filter by department ID
            status: Filter by employee status
            search_term: Search in first_name, last_name, email, id_number

        Returns:
            tuple: (total_count, employees)
        """
        try:
            query = session.query(cls)

            # Apply filters if provided
            if department_id:
                try:
                    if isinstance(department_id, str):
                        department_id = uuid.UUID(department_id)
                    query = query.filter(cls.department_id == department_id)
                except ValueError:
                    app.logger.error(f"Invalid department_id UUID format: {department_id}")

            if status:
                query = query.filter(cls.status == status)

            if search_term:
                search_pattern = f"%{search_term}%"
                query = query.filter(
                    db.or_(
                        cls.first_name.ilike(search_pattern),
                        cls.last_name.ilike(search_pattern),
                        cls.email.ilike(search_pattern),
                        cls.id_number.ilike(search_pattern)
                    )
                )

            # Get total count before pagination
            total_count = query.count()

            # Apply pagination
            employees = query.order_by(cls.created_at.desc()) \
                        .offset((page - 1) * per_page) \
                        .limit(per_page) \
                        .all()

            return total_count, employees
        except Exception as e:
            app.logger.error(f"Error getting paginated employees: {e}")
            return 0, []

    @classmethod
    def create_employee(cls, session, **kwargs):
        """Create a new employee."""
        try:
            # Convert department_id to UUID if it's a string
            if 'department_id' in kwargs and isinstance(kwargs['department_id'], str):
                try:
                    kwargs['department_id'] = uuid.UUID(kwargs['department_id'])
                except ValueError:
                    app.logger.error(f"Invalid department_id UUID format: {kwargs['department_id']}")
                    return None

            employee = cls(**kwargs)
            session.add(employee)
            session.commit()
            app.logger.info(f"Created new employee: {employee.first_name} {employee.last_name}")
            return employee
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating employee: {e}")
            return None

    @classmethod
    def update_employee(cls, session, employee_id, **kwargs):
        """Update an employee."""
        try:
            # Convert employee_id to UUID if it's a string
            if isinstance(employee_id, str):
                try:
                    employee_id = uuid.UUID(employee_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {employee_id}")
                    return None

            # Convert department_id to UUID if it's a string
            if 'department_id' in kwargs and isinstance(kwargs['department_id'], str):
                try:
                    kwargs['department_id'] = uuid.UUID(kwargs['department_id'])
                except ValueError:
                    app.logger.error(f"Invalid department_id UUID format: {kwargs['department_id']}")
                    return None

            employee = session.query(cls).filter_by(employee_id=employee_id).first()
            if not employee:
                app.logger.warning(f"Employee with ID {employee_id} not found")
                return None

            # Update employee attributes
            for key, value in kwargs.items():
                if hasattr(employee, key):
                    setattr(employee, key, value)

            session.commit()
            app.logger.info(f"Updated employee: {employee.first_name} {employee.last_name}")
            return employee
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error updating employee: {e}")
            return None

    @classmethod
    def delete_employee(cls, session, employee_id):
        """Delete an employee."""
        try:
            # Convert employee_id to UUID if it's a string
            if isinstance(employee_id, str):
                try:
                    employee_id = uuid.UUID(employee_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {employee_id}")
                    return False

            employee = session.query(cls).filter_by(employee_id=employee_id).first()
            if not employee:
                app.logger.warning(f"Employee with ID {employee_id} not found")
                return False

            session.delete(employee)
            session.commit()
            app.logger.info(f"Deleted employee with ID: {employee_id}")
            return True
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error deleting employee: {e}")
            return False

    @classmethod
    def count_employees_in_department(cls, session, department_id):
        """Count the number of employees in a department.

        Args:
            session: Database session
            department_id: Department ID (UUID or string)

        Returns:
            int: Number of employees in the department
        """
        try:
            # Convert department_id to UUID if it's a string
            if isinstance(department_id, str):
                try:
                    department_id = uuid.UUID(department_id)
                except ValueError:
                    app.logger.error(f"Invalid department_id UUID format: {department_id}")
                    return 0

            count = session.query(cls).filter_by(department_id=department_id).count()
            return count
        except Exception as e:
            app.logger.error(f"Error counting employees in department: {e}")
            return 0

    @classmethod
    def get_employees_by_type(cls, session, employee_type_id):
        """Get all employees of a specific type.

        Args:
            session: Database session
            employee_type_id: Employee type ID (UUID or string)

        Returns:
            list: List of employees of the specified type
        """
        try:
            # Convert employee_type_id to UUID if it's a string
            if isinstance(employee_type_id, str):
                try:
                    employee_type_id = uuid.UUID(employee_type_id)
                except ValueError:
                    app.logger.error(f"Invalid employee_type_id UUID format: {employee_type_id}")
                    return []

            employees = session.query(cls).filter_by(employee_type_id=employee_type_id, status='active').all()
            return employees
        except Exception as e:
            app.logger.error(f"Error getting employees by type: {e}")
            return []

    @classmethod
    def count_employees_by_type(cls, session, employee_type_id):
        """Count employees of a specific type.

        Args:
            session: Database session
            employee_type_id: Employee type ID (UUID or string)

        Returns:
            int: Number of employees of the specified type
        """
        try:
            # Convert employee_type_id to UUID if it's a string
            if isinstance(employee_type_id, str):
                try:
                    employee_type_id = uuid.UUID(employee_type_id)
                except ValueError:
                    app.logger.error(f"Invalid employee_type_id UUID format: {employee_type_id}")
                    return 0

            count = session.query(cls).filter_by(employee_type_id=employee_type_id, status='active').count()
            return count
        except Exception as e:
            app.logger.error(f"Error counting employees by type: {e}")
            return 0

    def get_employee_type_info(self, company_id):
        """Get employee type information from central DB.

        Args:
            company_id: Company ID to get country context

        Returns:
            EmployeeType object or None
        """
        if not self.employee_type_id:
            return None

        try:
            from application.Models.company import Company
            from application.Models.employee_type import EmployeeType

            company = Company.get_company_by_id(company_id)
            if not company:
                return None

            return EmployeeType.query.filter_by(
                employee_type_id=self.employee_type_id,
                country_id=company.country_id,
                is_active=True
            ).first()
        except Exception as e:
            app.logger.error(f"Error getting employee type info: {e}")
            return None

    @classmethod
    def sync_with_devices(cls, session, employee, company_id, action="create"):
        """
        Synchronize an employee or customer with biometric devices by creating/updating Person and EnrollInfo records
        and sending the appropriate commands to all devices associated with the company.

        Args:
            session: Database session
            employee: Employee object or Customer object (parameter name kept for backward compatibility)
            company_id: ID of the company
            action: Action being performed ("create", "update", or "delete")

        Returns:
            dict: Result of the operation with status and message
        """
        try:
            # Detect whether we're dealing with an Employee or Customer object
            is_customer = hasattr(employee, 'customer_id')
            entity_type = 'customer' if is_customer else 'employee'
            entity_id = employee.customer_id if is_customer else employee.employee_id

            app.logger.info(f"[DEVICE_SYNC] Starting sync_with_devices for {entity_type} {entity_id}, action: {action}, company: {company_id}")

            # Get database name for logging context
            from application.Models.company import Company
            database_name = Company.get_database_given_company_id(company_id)
            if database_name:
                app.logger.info(f"[DEVICE_SYNC] Using database context: {database_name}")
            else:
                app.logger.warning(f"[DEVICE_SYNC] Could not determine database name for company {company_id}")

            app.logger.info(f"Syncing {entity_type} with devices: action={action}, {entity_type}_id={entity_id}")

            # Get all devices for this company
            from application.Models.company import CompanyDevice
            devices = CompanyDevice.get_company_devices(company_id)

            if not devices:
                app.logger.info(f"No devices found for company {company_id}")
                return {"success": True, "message": "No devices to sync with", "devices_synced": 0}

            # Different handling based on action
            if action == "create":
                # Create Person record
                from application.Models.Person import Person
                person_data = {
                    'name': f"{employee.first_name} {employee.last_name}",
                    'roll_id': 0,  # Default role (non-admin)
                }

                # Set the appropriate ID field based on entity type
                if is_customer:
                    person_data['customer_id'] = entity_id
                    app.logger.info(f"[DEVICE_SYNC] Creating Person record for customer: {person_data}")
                    new_person = Person.add_customer(session, **person_data)
                else:
                    person_data['employee_id'] = str(entity_id)
                    app.logger.info(f"[DEVICE_SYNC] Creating Person record for employee: {person_data}")
                    new_person = Person.add_employee(session, **person_data)

                if not new_person:
                    app.logger.error(f"[DEVICE_SYNC] Failed to create Person record for {entity_type} {entity_id}")
                    return {"success": False, "message": "Failed to create Person record", "devices_synced": 0}

                app.logger.info(f"[DEVICE_SYNC] Successfully created Person record: ID={new_person.id}, name='{new_person.name}', {entity_type}_id={entity_id}")

                # Create EnrollInfo record
                from application.Models.EnrollInfo import insert_enroll_info
                enroll_info_data = {
                    'enroll_id': new_person.id,
                    'backupnum': -1,  # -1 means only name (no biometric data yet)
                    'imagepath': '',
                    'signatures': ''
                }
                enroll_info = insert_enroll_info(session, **enroll_info_data)

                if not enroll_info:
                    app.logger.error(f"Failed to create EnrollInfo record for person {new_person.id}")
                    return {"success": False, "message": "Failed to create EnrollInfo record", "devices_synced": 0}

                # Send to devices
                return cls._send_person_to_devices(session, new_person, devices)

            elif action == "update":
                # Find existing Person record
                from application.Models.Person import Person
                if is_customer:
                    existing_person = session.query(Person).filter_by(customer_id=entity_id).first()
                else:
                    existing_person = session.query(Person).filter_by(employee_id=str(entity_id)).first()

                if not existing_person:
                    app.logger.warning(f"No Person record found for {entity_type} {entity_id}, creating new one")
                    # Create new Person record if it doesn't exist
                    return cls.sync_with_devices(session, employee, company_id, "create")

                # Update Person record
                existing_person.name = f"{employee.first_name} {employee.last_name}"
                session.commit()

                # Send updated info to devices
                return cls._send_person_to_devices(session, existing_person, devices)

            elif action == "delete":
                # Find existing Person record
                from application.Models.Person import Person
                if is_customer:
                    existing_person = session.query(Person).filter_by(customer_id=entity_id).first()
                else:
                    existing_person = session.query(Person).filter_by(employee_id=str(entity_id)).first()

                if not existing_person:
                    app.logger.warning(f"No Person record found for {entity_type} {entity_id} to delete")
                    return {"success": True, "message": "No Person record to delete", "devices_synced": 0}

                # Delete from devices first
                result = cls._delete_person_from_devices(session, existing_person, devices)

                # Then delete Person and EnrollInfo records
                from application.Models.EnrollInfo import EnrollInfo
                session.query(EnrollInfo).filter_by(enroll_id=existing_person.id).delete()
                session.delete(existing_person)
                session.commit()

                return result

            else:
                app.logger.error(f"Unknown action: {action}")
                return {"success": False, "message": f"Unknown action: {action}", "devices_synced": 0}

        except Exception as e:
            app.logger.error(f"Error syncing {entity_type if 'entity_type' in locals() else 'entity'} with devices: {str(e)}")
            app.logger.error(traceback.format_exc())
            return {"success": False, "message": f"Error: {str(e)}", "devices_synced": 0}

    @classmethod
    def _send_person_to_devices(cls, session, person, devices):
        """Helper method to send a person to multiple devices with biometric data."""
        app.logger.info(f"[DEVICE_SYNC] _send_person_to_devices called for person ID={person.id}, name='{person.name}' to {len(devices)} devices")

        # Log person details
        person_type = "employee" if person.employee_id else ("customer" if person.customer_id else "unknown")
        app.logger.info(f"[DEVICE_SYNC] Person details: type={person_type}, employee_id={person.employee_id}, customer_id={person.customer_id}")

        # Import the necessary classes
        from application.Services.PersonService import PersonServiceImpl
        from application.Models.Person import Person as PersonModel
        from application.Services.EnrollInfoService import EnrollInfoService
        from application.Models.EnrollInfo import EnrollInfo
        from application.Models.MachineCommand import MachineCommand
        from application.utils.biometric_sync_logger import biometric_sync_logger

        # Log start of employee distribution
        biometric_sync_logger.log_employee_distribution_start(person.id, person.name, len(devices))

        # Create the required objects for PersonServiceImpl
        person_model = PersonModel()
        enroll_info_model = EnrollInfo()
        enroll_info_service = EnrollInfoService(enroll_info=enroll_info_model, person=person_model)

        # Instantiate PersonServiceImpl with the required parameters
        personService = PersonServiceImpl(
            person=person_model,
            enroll_info=enroll_info_service,
            machine_command=MachineCommand()
        )

        devices_synced = 0
        total_biometric_synced = 0

        for device in devices:
            device_biometric_count = 0

            try:
                # STEP 1: Send name first (existing functionality - must work)
                from application.Models.EnrollInfo import selectByBackupnum
                enroll_info = selectByBackupnum(session, person.id, -1)
                signatures = enroll_info.signatures if enroll_info else ""

                personService.set_user_to_device(
                    session,
                    person.id,
                    person.name,
                    -1,  # backupnum (-1 for name only)
                    person.roll_id,
                    signatures,
                    device.device_sn
                )

                # Log successful name sync
                biometric_sync_logger.log_name_sync_success(person.id, person.name, device.device_sn)
                devices_synced += 1
                app.logger.info(f"Sent person {person.name} to device {device.device_sn}")

            except Exception as e:
                # Log name sync failure
                biometric_sync_logger.log_name_sync_failure(person.id, person.name, device.device_sn, str(e))
                app.logger.error(f"Error sending person {person.id} to device {device.device_sn}: {str(e)}")
                app.logger.error(traceback.format_exc())
                continue  # Skip biometric sync for this device if name sync fails

            # STEP 2: Send biometric data (NEW FEATURE - failures don't affect name sync)
            try:
                # Log start of biometric scanning for this person
                biometric_sync_logger.log_biometric_scan_start(person.id, person.name)

                # Define biometric types to sync: fingerprints (0-9) + face image (50)
                biometric_backupnums = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 50]

                for backupnum in biometric_backupnums:
                    try:
                        # Check if this biometric type exists for the person
                        biometric_enroll_info = selectByBackupnum(session, person.id, backupnum)

                        if biometric_enroll_info and biometric_enroll_info.signatures:
                            # Log found biometric data
                            data_size = len(biometric_enroll_info.signatures) if biometric_enroll_info.signatures else 0
                            biometric_sync_logger.log_biometric_found(person.id, backupnum, data_size)

                            # Send biometric data to device
                            personService.set_user_to_device(
                                session,
                                person.id,
                                person.name,
                                backupnum,  # Specific biometric type
                                person.roll_id,
                                biometric_enroll_info.signatures,  # Base64 biometric data
                                device.device_sn
                            )

                            # Log successful biometric sync
                            biometric_sync_logger.log_biometric_sync_success(
                                person.id, person.name, backupnum, device.device_sn
                            )
                            device_biometric_count += 1

                    except Exception as biometric_error:
                        # Log biometric sync failure (doesn't affect other biometric types or name sync)
                        biometric_sync_logger.log_biometric_sync_failure(
                            person.id, person.name, backupnum, device.device_sn, str(biometric_error)
                        )
                        app.logger.error(f"Error sending biometric data (backupnum {backupnum}) for person {person.id} to device {device.device_sn}: {str(biometric_error)}")
                        continue  # Continue with next biometric type

                total_biometric_synced += device_biometric_count

            except Exception as e:
                # Log general biometric sync failure for this device
                biometric_sync_logger.log_biometric_sync_failure(
                    person.id, person.name, "ALL", device.device_sn, str(e)
                )
                app.logger.error(f"Error during biometric sync for person {person.id} to device {device.device_sn}: {str(e)}")
                # Continue to next device - name sync already succeeded

        # Log final summary
        biometric_sync_logger.log_employee_distribution_summary(
            person.id, person.name, devices_synced, len(devices), total_biometric_synced
        )

        return {
            "success": devices_synced > 0,
            "message": f"Synced with {devices_synced} out of {len(devices)} devices, {total_biometric_synced} biometric templates sent",
            "devices_synced": devices_synced,
            "biometric_templates_synced": total_biometric_synced
        }

    @classmethod
    def _delete_person_from_devices(cls, session, person, devices):
        """Helper method to delete a person from multiple devices."""
        # Import the necessary classes
        from application.Services.PersonService import PersonServiceImpl
        from application.Models.Person import Person as PersonModel
        from application.Services.EnrollInfoService import EnrollInfoService
        from application.Models.EnrollInfo import EnrollInfo
        from application.Models.MachineCommand import MachineCommand

        # Create the required objects for PersonServiceImpl
        person_model = PersonModel()
        enroll_info_model = EnrollInfo()
        enroll_info_service = EnrollInfoService(enroll_info=enroll_info_model, person=person_model)

        # Instantiate PersonServiceImpl with the required parameters
        personService = PersonServiceImpl(
            person=person_model,
            enroll_info=enroll_info_service,
            machine_command=MachineCommand()
        )

        devices_synced = 0
        for device in devices:
            try:
                # TODO: Implement actual deletion command to device using personService
                # For now, we'll just log it
                app.logger.info(f"Would delete person {person.name} from device {device.device_sn}")
                # Example of how it might be implemented:
                # personService.delete_user_info_from_device(person.id, device.device_sn)
                devices_synced += 1
            except Exception as e:
                app.logger.error(f"Error deleting person {person.id} from device {device.device_sn}: {str(e)}")
                app.logger.error(traceback.format_exc())

        return {
            "success": devices_synced > 0,
            "message": f"Deleted from {devices_synced} out of {len(devices)} devices",
            "devices_synced": devices_synced
        }

    @classmethod
    def get_upcoming_birthdays_with_days_left(cls, session, start_date, end_date):
        """
        Get employees with upcoming birthdays and days left within a date range.

        Args:
            session: SQLAlchemy session
            start_date: datetime.date
            end_date: datetime.date

        Returns:
            list of dicts: [{'employee': Employee, 'days_left': int}, ...]
        """
        try:
            # Step 1: Get all employees
            all_employees = session.query(cls).filter(cls.date_of_birth.isnot(None)).all()

            upcoming = []

            for emp in all_employees:
                birth_month = emp.date_of_birth.month
                birth_day = emp.date_of_birth.day

                # Create a birthday this year
                today = date.today()
                current_year_birthday = date(today.year, birth_month, birth_day)

                # If birthday has passed this year, take next year's
                if current_year_birthday < today:
                    current_year_birthday = date(today.year + 1, birth_month, birth_day)

                # Check if within the start_date - end_date window
                if start_date <= current_year_birthday <= end_date:
                    days_left = (current_year_birthday - today).days
                    upcoming.append({
                        "employee": emp,
                        "days_left": days_left
                    })

            return upcoming
        except Exception as e:
            app.logger.error(f"Error getting upcoming birthdays with countdown: {e}")
            return []
