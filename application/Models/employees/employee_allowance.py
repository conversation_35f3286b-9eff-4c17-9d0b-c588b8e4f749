from application.database import db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime
from flask import current_app
from decimal import Decimal

class EmployeeAllowance(db.Model):
    """Model representing specific allowances for each employee (stored in tenant database)."""
    __tablename__ = 'employee_allowances'

    employee_allowance_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    employee_id = db.Column(UUID(as_uuid=True), db.ForeignKey('employees.employee_id'), nullable=False)
    allowance_type_id = db.Column(UUID(as_uuid=True), db.ForeignKey('allowance_types.allowance_type_id'), nullable=False)
    amount = db.Column(db.Numeric(15, 2), nullable=False)
    percentage = db.Column(db.Numeric(5, 2), nullable=True)  # If percentage-based calculation
    effective_from = db.Column(db.Date, nullable=False)
    effective_to = db.Column(db.Date, nullable=True)  # null means currently active
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    # Relationships
    employee = db.relationship('Employee', backref=db.backref('allowances', lazy='dynamic'))
    allowance_type = db.relationship('AllowanceType', backref=db.backref('employee_allowances', lazy='dynamic'))

    # Unique constraint: one active allowance per employee per type at any time
    __table_args__ = (
        db.Index('idx_employee_allowance_active', 'employee_id', 'allowance_type_id', 'effective_from', 'effective_to'),
    )

    def __str__(self):
        """Return a string representation of the object."""
        return f"EmployeeAllowance [employee_allowance_id={self.employee_allowance_id}, employee_id={self.employee_id}, amount={self.amount}]"

    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "employee_allowance_id": str(self.employee_allowance_id),
            "employee_id": str(self.employee_id),
            "allowance_type_id": str(self.allowance_type_id),
            "allowance_type_name": self.allowance_type.name if self.allowance_type else None,
            "allowance_type_code": self.allowance_type.code if self.allowance_type else None,
            "amount": float(self.amount),
            "percentage": float(self.percentage) if self.percentage else None,
            "is_taxable": self.allowance_type.is_taxable if self.allowance_type else True,
            "is_pensionable": self.allowance_type.is_pensionable if self.allowance_type else True,
            "effective_from": self.effective_from.strftime('%Y-%m-%d') if self.effective_from else None,
            "effective_to": self.effective_to.strftime('%Y-%m-%d') if self.effective_to else None,
            "is_active": self.is_active,
            "employee_name": f"{self.employee.first_name} {self.employee.last_name}" if self.employee else None,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

    @classmethod
    def create_allowance(cls, session, **kwargs):
        """Create a new employee allowance."""
        try:
            allowance = cls(**kwargs)
            session.add(allowance)
            session.commit()
            current_app.logger.info(f"Created allowance for employee {allowance.employee_id}: {allowance.amount}")
            return allowance
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error creating employee allowance: {e}")
            return None

    @classmethod
    def get_active_allowances(cls, session, employee_id, date=None):
        """Get all active allowances for an employee on a specific date."""
        if date is None:
            date = datetime.now().date()

        return session.query(cls).filter(
            cls.employee_id == employee_id,
            cls.effective_from <= date,
            db.or_(
                cls.effective_to.is_(None),
                cls.effective_to >= date
            ),
            cls.is_active == True
        ).all()

    @classmethod
    def get_allowance_by_type(cls, session, employee_id, allowance_type_id, date=None):
        """Get a specific allowance for an employee by type."""
        if date is None:
            date = datetime.now().date()

        return session.query(cls).filter(
            cls.employee_id == employee_id,
            cls.allowance_type_id == allowance_type_id,
            cls.effective_from <= date,
            db.or_(
                cls.effective_to.is_(None),
                cls.effective_to >= date
            ),
            cls.is_active == True
        ).first()

    @classmethod
    def get_allowance_history(cls, session, employee_id, allowance_type_id=None):
        """Get allowance history for an employee."""
        query = session.query(cls).filter(cls.employee_id == employee_id)
        
        if allowance_type_id:
            query = query.filter(cls.allowance_type_id == allowance_type_id)
        
        return query.order_by(cls.effective_from.desc()).all()

    @classmethod
    def update_allowance(cls, session, employee_id, allowance_type_id, new_amount, effective_from=None):
        """Update employee allowance by creating a new record and ending the current one."""
        try:
            if effective_from is None:
                effective_from = datetime.now().date()

            # Get current allowance
            current_allowance = cls.get_allowance_by_type(session, employee_id, allowance_type_id, effective_from)
            if not current_allowance:
                current_app.logger.error(f"No current allowance found for employee {employee_id}, type {allowance_type_id}")
                return None

            # End the current allowance record
            current_allowance.effective_to = effective_from
            current_allowance.is_active = False

            # Create new allowance record
            new_allowance_data = {
                'employee_id': employee_id,
                'allowance_type_id': allowance_type_id,
                'amount': new_amount,
                'percentage': current_allowance.percentage,
                'effective_from': effective_from,
                'effective_to': None,
                'is_active': True
            }

            new_allowance = cls.create_allowance(session, **new_allowance_data)
            
            if new_allowance:
                session.commit()
                current_app.logger.info(f"Updated allowance for employee {employee_id}: {new_amount}")
                return new_allowance
            else:
                session.rollback()
                return None

        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error updating employee allowance: {e}")
            return None

    @classmethod
    def add_allowance_to_employee(cls, session, employee_id, allowance_type_id, amount, effective_from=None):
        """Add a new allowance to an employee."""
        try:
            if effective_from is None:
                effective_from = datetime.now().date()

            # Check if employee already has this allowance type
            existing = cls.get_allowance_by_type(session, employee_id, allowance_type_id, effective_from)
            if existing:
                current_app.logger.warning(f"Employee {employee_id} already has allowance type {allowance_type_id}")
                return None

            allowance_data = {
                'employee_id': employee_id,
                'allowance_type_id': allowance_type_id,
                'amount': amount,
                'effective_from': effective_from,
                'effective_to': None,
                'is_active': True
            }

            return cls.create_allowance(session, **allowance_data)

        except Exception as e:
            current_app.logger.error(f"Error adding allowance to employee: {e}")
            return None

    @classmethod
    def remove_allowance_from_employee(cls, session, employee_id, allowance_type_id, effective_from=None):
        """Remove an allowance from an employee."""
        try:
            if effective_from is None:
                effective_from = datetime.now().date()

            # Get current allowance
            current_allowance = cls.get_allowance_by_type(session, employee_id, allowance_type_id, effective_from)
            if not current_allowance:
                current_app.logger.warning(f"No active allowance found for employee {employee_id}, type {allowance_type_id}")
                return False

            # End the allowance
            current_allowance.effective_to = effective_from
            current_allowance.is_active = False
            
            session.commit()
            current_app.logger.info(f"Removed allowance from employee {employee_id}, type {allowance_type_id}")
            return True

        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error removing allowance from employee: {e}")
            return False

    @classmethod
    def get_taxable_allowances_total(cls, session, employee_id, date=None):
        """Get total taxable allowances for an employee."""
        allowances = cls.get_active_allowances(session, employee_id, date)
        total = 0
        
        for allowance in allowances:
            if allowance.allowance_type and allowance.allowance_type.is_taxable:
                total += float(allowance.amount)
        
        return total

    @classmethod
    def get_pensionable_allowances_total(cls, session, employee_id, date=None):
        """Get total pensionable allowances for an employee."""
        allowances = cls.get_active_allowances(session, employee_id, date)
        total = 0

        for allowance in allowances:
            if allowance.allowance_type and allowance.allowance_type.is_pensionable:
                total += float(allowance.amount)

        return total

    @classmethod
    def get_taxable_allowances_total_decimal(cls, session, employee_id, date=None):
        """Get total taxable allowances for an employee as Decimal."""
        allowances = cls.get_active_allowances(session, employee_id, date)
        total = Decimal('0')

        for allowance in allowances:
            if allowance.allowance_type and allowance.allowance_type.is_taxable:
                total += Decimal(str(allowance.amount))

        return total

    @classmethod
    def get_pensionable_allowances_total_decimal(cls, session, employee_id, date=None):
        """Get total pensionable allowances for an employee as Decimal."""
        allowances = cls.get_active_allowances(session, employee_id, date)
        total = Decimal('0')

        for allowance in allowances:
            if allowance.allowance_type and allowance.allowance_type.is_pensionable:
                total += Decimal(str(allowance.amount))

        return total

    @classmethod
    def get_allowances_total_decimal(cls, session, employee_id, date=None):
        """Get total of all allowances for an employee as Decimal."""
        allowances = cls.get_active_allowances(session, employee_id, date)
        total = Decimal('0')

        for allowance in allowances:
            total += Decimal(str(allowance.amount))

        return total
