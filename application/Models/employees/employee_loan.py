from application.database import db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime, date, timedelta
from flask import current_app
from decimal import Decimal, ROUND_HALF_UP
import calendar


def ensure_decimal(value):
    """Ensure a value is a Decimal for financial calculations."""
    if isinstance(value, Decimal):
        return value
    elif isinstance(value, (int, float)):
        return Decimal(str(value))
    elif isinstance(value, str):
        return Decimal(value)
    else:
        return Decimal('0')


def round_currency(amount, places=2):
    """Round currency to specified decimal places using financial rounding."""
    if not isinstance(amount, Decimal):
        amount = ensure_decimal(amount)
    return amount.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

class EmployeeLoan(db.Model):
    """Model representing employee loans and advances (stored in tenant database)."""
    __tablename__ = 'employee_loans'

    loan_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    employee_id = db.Column(UUID(as_uuid=True), db.ForeignKey('employees.employee_id'), nullable=False)
    loan_type_id = db.Column(UUID(as_uuid=True), db.ForeignKey('loan_types.loan_type_id'), nullable=False)
    
    # Loan Details
    loan_number = db.Column(db.String(50), unique=True, nullable=False)  # Auto-generated: ADV-2025-001
    principal_amount = db.Column(db.Numeric(15, 2), nullable=False)
    interest_rate = db.Column(db.Numeric(5, 4), nullable=False)  # Snapshot from loan_type
    total_interest = db.Column(db.Numeric(15, 2), default=0, nullable=False)
    total_amount = db.Column(db.Numeric(15, 2), nullable=False)  # Principal + Interest
    
    # Terms
    term_months = db.Column(db.Integer, nullable=False)
    monthly_deduction = db.Column(db.Numeric(15, 2), nullable=False)
    
    # Balances
    amount_disbursed = db.Column(db.Numeric(15, 2), default=0, nullable=False)
    amount_repaid = db.Column(db.Numeric(15, 2), default=0, nullable=False)
    outstanding_balance = db.Column(db.Numeric(15, 2), nullable=False)
    
    # Dates
    application_date = db.Column(db.Date, nullable=False, default=date.today)
    approval_date = db.Column(db.Date, nullable=True)
    disbursement_date = db.Column(db.Date, nullable=True)
    first_deduction_date = db.Column(db.Date, nullable=True)
    expected_completion_date = db.Column(db.Date, nullable=True)
    actual_completion_date = db.Column(db.Date, nullable=True)
    
    # Status
    status = db.Column(db.String(20), nullable=False, default='PENDING')
    # 'PENDING', 'APPROVED', 'REJECTED', 'DISBURSED', 'ACTIVE', 'COMPLETED', 'DEFAULTED', 'CANCELLED'
    
    # Approval Info
    approved_by = db.Column(UUID(as_uuid=True), nullable=True)  # User ID
    rejected_reason = db.Column(db.Text, nullable=True)
    
    # Additional Info
    purpose = db.Column(db.Text, nullable=True)
    notes = db.Column(db.Text, nullable=True)
    
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    # Relationships
    employee = db.relationship('Employee', backref=db.backref('loans', lazy='dynamic'))
    loan_type = db.relationship('LoanType', backref=db.backref('loans', lazy='dynamic'))

    # Constraints
    __table_args__ = (
        db.CheckConstraint('principal_amount > 0', name='chk_positive_principal'),
        db.CheckConstraint('total_amount >= principal_amount', name='chk_total_amount'),
        db.CheckConstraint('monthly_deduction > 0', name='chk_positive_monthly_deduction'),
        db.CheckConstraint('term_months > 0', name='chk_positive_term'),
    )

    def __str__(self):
        """Return a string representation of the object."""
        return f"EmployeeLoan [loan_id={self.loan_id}, loan_number={self.loan_number}, status={self.status}]"

    def to_dict(self, convert_decimals_to_float=True):
        """Dictionary representation of the object."""
        data = {
            "loan_id": str(self.loan_id),
            "employee_id": str(self.employee_id),
            "employee_name": f"{self.employee.first_name} {self.employee.last_name}" if self.employee else None,
            "loan_type_id": str(self.loan_type_id),
            "loan_type_name": self.loan_type.name if self.loan_type else None,
            "loan_number": self.loan_number,
            "principal_amount": self.principal_amount,
            "interest_rate": self.interest_rate,
            "total_interest": self.total_interest,
            "total_amount": self.total_amount,
            "term_months": self.term_months,
            "monthly_deduction": self.monthly_deduction,
            "amount_disbursed": self.amount_disbursed,
            "amount_repaid": self.amount_repaid,
            "outstanding_balance": self.outstanding_balance,
            "application_date": self.application_date.strftime('%Y-%m-%d') if self.application_date else None,
            "approval_date": self.approval_date.strftime('%Y-%m-%d') if self.approval_date else None,
            "disbursement_date": self.disbursement_date.strftime('%Y-%m-%d') if self.disbursement_date else None,
            "first_deduction_date": self.first_deduction_date.strftime('%Y-%m-%d') if self.first_deduction_date else None,
            "expected_completion_date": self.expected_completion_date.strftime('%Y-%m-%d') if self.expected_completion_date else None,
            "actual_completion_date": self.actual_completion_date.strftime('%Y-%m-%d') if self.actual_completion_date else None,
            "status": self.status,
            "approved_by": str(self.approved_by) if self.approved_by else None,
            "rejected_reason": self.rejected_reason,
            "purpose": self.purpose,
            "notes": self.notes,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

        # Convert Decimals to float only for JSON serialization if requested
        if convert_decimals_to_float:
            decimal_fields = ['principal_amount', 'interest_rate', 'total_interest', 'total_amount',
                            'monthly_deduction', 'amount_disbursed', 'amount_repaid', 'outstanding_balance']
            for field in decimal_fields:
                if data[field] is not None:
                    data[field] = float(data[field])

        return data

    @classmethod
    def generate_loan_number(cls, session, loan_type_code):
        """Generate unique loan number."""
        current_year = datetime.now().year
        
        # Count existing loans for this year and type
        count = session.query(cls).join(cls.loan_type).filter(
            cls.loan_type.has(code=loan_type_code),
            db.extract('year', cls.created_at) == current_year
        ).count()
        
        return f"{loan_type_code}-{current_year}-{count + 1:03d}"

    @classmethod
    def create_loan_application(cls, session, employee_id, loan_type_id, principal_amount, 
                              term_months, purpose=None, **kwargs):
        """Create a new loan application."""
        try:
            from application.Models.employees.loan_type import LoanType
            from application.Models.employees.employee_salary import EmployeeSalary
            
            # Get loan type
            loan_type = LoanType.get_loan_type_by_id(session, loan_type_id)
            if not loan_type:
                return None, "Loan type not found"
            
            # Get employee salary for validation
            salary = EmployeeSalary.get_current_salary(session, employee_id)
            if not salary:
                return None, "Employee salary not found"
            
            # Validate loan terms
            is_valid, errors = loan_type.validate_loan_terms(
                principal_amount, term_months, salary.basic_salary
            )
            if not is_valid:
                return None, "; ".join(errors)
            
            # Calculate loan details using Decimal precision
            principal = ensure_decimal(principal_amount)
            total_interest = loan_type.calculate_interest(principal, term_months)
            total_amount = round_currency(principal + total_interest)
            monthly_deduction = round_currency(loan_type.calculate_monthly_payment(principal, term_months))
            
            # Generate loan number
            loan_number = cls.generate_loan_number(session, loan_type.code)
            
            # Create loan record
            loan_data = {
                'employee_id': employee_id,
                'loan_type_id': loan_type_id,
                'loan_number': loan_number,
                'principal_amount': principal,
                'interest_rate': loan_type.interest_rate,
                'total_interest': total_interest,
                'total_amount': total_amount,
                'term_months': term_months,
                'monthly_deduction': monthly_deduction,
                'outstanding_balance': total_amount,
                'purpose': purpose,
                **kwargs
            }
            
            loan = cls(**loan_data)
            session.add(loan)
            session.commit()
            
            current_app.logger.info(f"Created loan application: {loan.loan_number}")
            return loan, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error creating loan application: {e}")
            return None, str(e)

    def approve_loan(self, session, approved_by, disbursement_date=None):
        """Approve the loan and set up repayment schedule."""
        try:
            self.status = 'APPROVED'
            self.approved_by = approved_by
            self.approval_date = date.today()
            
            if disbursement_date:
                self.disbursement_date = disbursement_date
                self.status = 'DISBURSED'
                self.amount_disbursed = self.principal_amount
                
                # Set first deduction date (next month)
                next_month = disbursement_date.replace(day=1) + timedelta(days=32)
                self.first_deduction_date = next_month.replace(day=1)
                
                # Calculate expected completion date
                self.expected_completion_date = self.first_deduction_date + timedelta(
                    days=30 * self.term_months
                )
                
                # Generate repayment schedule
                self._generate_repayment_schedule(session)
            
            session.commit()
            current_app.logger.info(f"Approved loan: {self.loan_number}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error approving loan: {e}")
            return False, str(e)

    def _generate_repayment_schedule(self, session):
        """Generate repayment schedule for the loan."""
        from application.Models.employees.loan_repayment_schedule import LoanRepaymentSchedule
        
        current_date = self.first_deduction_date
        
        for installment in range(1, self.term_months + 1):
            # Calculate due date (first day of each month)
            due_date = current_date.replace(day=1)
            
            # For the last installment, adjust amount to handle rounding
            if installment == self.term_months:
                remaining_balance = self.total_amount - (self.monthly_deduction * (self.term_months - 1))
                installment_amount = remaining_balance
            else:
                installment_amount = self.monthly_deduction
            
            # Create schedule entry
            schedule_data = {
                'loan_id': self.loan_id,
                'installment_number': installment,
                'due_date': due_date,
                'amount': installment_amount,
                'status': 'PENDING'
            }
            
            schedule = LoanRepaymentSchedule.create_schedule(session, **schedule_data)
            
            # Move to next month
            if current_date.month == 12:
                current_date = current_date.replace(year=current_date.year + 1, month=1)
            else:
                current_date = current_date.replace(month=current_date.month + 1)

    @classmethod
    def get_active_loans_for_employee(cls, session, employee_id, as_of_date=None):
        """Get active loans for an employee."""
        if as_of_date is None:
            as_of_date = date.today()
        
        return session.query(cls).filter(
            cls.employee_id == employee_id,
            cls.status.in_(['ACTIVE', 'DISBURSED']),
            cls.outstanding_balance > 0
        ).all()

    @classmethod
    def get_loans_for_payroll_deduction(cls, session, employee_id, payroll_date):
        """Get loans that need deduction in current payroll."""
        return session.query(cls).filter(
            cls.employee_id == employee_id,
            cls.status == 'ACTIVE',
            cls.first_deduction_date <= payroll_date,
            cls.outstanding_balance > 0
        ).all()

    def get_current_repayment(self, session, for_date=None):
        """Get current month's repayment schedule."""
        from application.Models.employees.loan_repayment_schedule import LoanRepaymentSchedule
        
        if for_date is None:
            for_date = date.today()
        
        return session.query(LoanRepaymentSchedule).filter(
            LoanRepaymentSchedule.loan_id == self.loan_id,
            LoanRepaymentSchedule.due_date <= for_date,
            LoanRepaymentSchedule.status == 'PENDING'
        ).order_by(LoanRepaymentSchedule.due_date).first()

    def make_payment(self, session, amount, payment_date=None, payslip_id=None):
        """Record a loan payment."""
        try:
            from application.Models.employees.loan_transaction import LoanTransaction
            from application.Models.employees.loan_repayment_schedule import LoanRepaymentSchedule
            
            if payment_date is None:
                payment_date = date.today()
            
            amount = Decimal(str(amount))
            
            # Get pending repayments in order
            pending_repayments = session.query(LoanRepaymentSchedule).filter(
                LoanRepaymentSchedule.loan_id == self.loan_id,
                LoanRepaymentSchedule.status.in_(['PENDING', 'PARTIAL'])
            ).order_by(LoanRepaymentSchedule.due_date).all()
            
            remaining_payment = amount
            
            for repayment in pending_repayments:
                if remaining_payment <= 0:
                    break
                
                outstanding_amount = repayment.amount - repayment.amount_paid
                payment_for_this_installment = min(remaining_payment, outstanding_amount)
                
                # Update repayment record
                repayment.amount_paid += payment_for_this_installment
                if repayment.amount_paid >= repayment.amount:
                    repayment.status = 'PAID'
                    repayment.payment_date = payment_date
                else:
                    repayment.status = 'PARTIAL'
                
                remaining_payment -= payment_for_this_installment
            
            # Update loan balances
            self.amount_repaid += amount
            self.outstanding_balance -= amount
            
            if self.outstanding_balance <= 0:
                self.status = 'COMPLETED'
                self.actual_completion_date = payment_date
            
            # Create transaction record
            transaction_data = {
                'loan_id': self.loan_id,
                'transaction_type': 'REPAYMENT',
                'amount': amount,
                'transaction_date': payment_date,
                'payslip_id': payslip_id,
                'description': f'Loan repayment for {self.loan_number}'
            }
            
            LoanTransaction.create_transaction(session, **transaction_data)
            
            session.commit()
            current_app.logger.info(f"Recorded payment of {amount} for loan {self.loan_number}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error recording loan payment: {e}")
            return False, str(e)
