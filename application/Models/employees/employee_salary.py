from application.database import db
from sqlalchemy.dialects.postgresql import UUID, JSONB
import uuid
from datetime import datetime
from flask import current_app
from decimal import Decimal

class EmployeeSalary(db.Model):
    """Model representing employee salary information (stored in tenant database)."""
    __tablename__ = 'employee_salaries'

    salary_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    employee_id = db.Column(UUID(as_uuid=True), db.<PERSON>ey('employees.employee_id'), nullable=False)
    # employee_type_id is now accessed through Employee relationship
    basic_salary = db.Column(db.Numeric(15, 2), nullable=False)
    currency = db.Column(db.String(3), default='RWF', nullable=False)
    pay_frequency = db.Column(db.String(20), default='MONTHLY', nullable=False)  # MONTHLY, WEEKLY, DAILY
    effective_from = db.Column(db.Date, nullable=False)
    effective_to = db.Column(db.Date, nullable=True)  # null means currently active
    is_active = db.Column(db.<PERSON>an, default=True, nullable=False)

    # New fields for dynamic calculation scenarios
    calculation_scenario = db.Column(db.String(50), nullable=True)  # Which scenario was used
    input_values = db.Column(JSONB, nullable=True)  # What values were provided as input
    calculated_values = db.Column(JSONB, nullable=True)  # What values were calculated

    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    # Relationship with Employee model
    employee = db.relationship('Employee', backref=db.backref('salaries', lazy='dynamic'))

    # Unique constraint: one active salary per employee at any time
    __table_args__ = (
        db.Index('idx_employee_salary_active', 'employee_id', 'effective_from', 'effective_to'),
    )

    @property
    def employee_type_id(self):
        """Get employee type ID from the associated employee."""
        return self.employee.employee_type_id if self.employee else None

    def __str__(self):
        """Return a string representation of the object."""
        return f"EmployeeSalary [salary_id={self.salary_id}, employee_id={self.employee_id}, basic_salary={self.basic_salary}]"

    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "salary_id": str(self.salary_id),
            "employee_id": str(self.employee_id),
            "employee_type_id": str(self.employee_type_id) if self.employee_type_id else None,
            "basic_salary": float(self.basic_salary),
            "currency": self.currency,
            "pay_frequency": self.pay_frequency,
            "effective_from": self.effective_from.strftime('%Y-%m-%d') if self.effective_from else None,
            "effective_to": self.effective_to.strftime('%Y-%m-%d') if self.effective_to else None,
            "is_active": self.is_active,
            "employee_name": f"{self.employee.first_name} {self.employee.last_name}" if self.employee else None,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

    @classmethod
    def create_salary(cls, session, **kwargs):
        """Create a new employee salary record."""
        try:
            salary = cls(**kwargs)
            session.add(salary)
            session.commit()
            current_app.logger.info(f"Created salary record for employee {salary.employee_id}: {salary.basic_salary} {salary.currency}")
            return salary
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error creating employee salary: {e}")
            return None

    @classmethod
    def get_current_salary(cls, session, employee_id, date=None):
        """Get the current active salary for an employee on a specific date."""
        if date is None:
            date = datetime.now().date()

        return session.query(cls).filter(
            cls.employee_id == employee_id,
            cls.effective_from <= date,
            db.or_(
                cls.effective_to.is_(None),
                cls.effective_to >= date
            ),
            cls.is_active == True
        ).first()

    @classmethod
    def get_salary_history(cls, session, employee_id):
        """Get the salary history for an employee."""
        return session.query(cls).filter(
            cls.employee_id == employee_id
        ).order_by(cls.effective_from.desc()).all()

    @classmethod
    def update_salary(cls, session, employee_id, new_salary, effective_from=None):
        """Update employee salary by creating a new record and ending the current one.

        Note: Employee type is now managed at the Employee level, not salary level.
        """
        try:
            if effective_from is None:
                effective_from = datetime.now().date()

            # Get current salary
            current_salary = cls.get_current_salary(session, employee_id, effective_from)
            if not current_salary:
                current_app.logger.error(f"No current salary found for employee {employee_id}")
                return None

            # End the current salary record
            current_salary.effective_to = effective_from
            current_salary.is_active = False

            # Create new salary record (employee_type_id comes from Employee now)
            new_salary_data = {
                'employee_id': employee_id,
                'basic_salary': new_salary,
                'currency': current_salary.currency,
                'pay_frequency': current_salary.pay_frequency,
                'effective_from': effective_from,
                'effective_to': None,
                'is_active': True
            }

            new_salary_record = cls.create_salary(session, **new_salary_data)

            if new_salary_record:
                session.commit()
                current_app.logger.info(f"Updated salary for employee {employee_id}: {new_salary} {current_salary.currency}")
                return new_salary_record
            else:
                session.rollback()
                return None

        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error updating employee salary: {e}")
            return None

    @classmethod
    def get_employees_by_salary_range(cls, session, min_salary, max_salary, date=None):
        """Get employees within a specific salary range."""
        if date is None:
            date = datetime.now().date()

        return session.query(cls).filter(
            cls.basic_salary >= min_salary,
            cls.basic_salary <= max_salary,
            cls.effective_from <= date,
            db.or_(
                cls.effective_to.is_(None),
                cls.effective_to >= date
            ),
            cls.is_active == True
        ).all()

    @classmethod
    def get_employees_by_type(cls, session, employee_type_id, date=None):
        """Get all salary records for employees of a specific type."""
        if date is None:
            date = datetime.now().date()

        from application.Models.employees.employee import Employee

        return session.query(cls).join(Employee).filter(
            Employee.employee_type_id == employee_type_id,
            Employee.status == 'active',
            cls.effective_from <= date,
            db.or_(
                cls.effective_to.is_(None),
                cls.effective_to >= date
            ),
            cls.is_active == True
        ).all()

    @classmethod
    def deactivate_salary(cls, session, salary_id):
        """Deactivate a salary record (soft delete)."""
        try:
            salary = session.query(cls).filter_by(salary_id=salary_id).first()
            if not salary:
                return False

            salary.is_active = False
            salary.effective_to = datetime.now().date()
            session.commit()
            current_app.logger.info(f"Deactivated salary record: {salary_id}")
            return True
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error deactivating salary record: {e}")
            return False

    def calculate_gross_salary(self, session, pay_period_start, pay_period_end):
        """Calculate gross salary including allowances for a specific pay period."""
        try:
            # Start with basic salary
            gross_salary = float(self.basic_salary)
            
            # Add allowances
            from application.Models.employees.employee_allowance import EmployeeAllowance
            allowances = EmployeeAllowance.get_active_allowances(session, self.employee_id, pay_period_start)
            
            total_allowances = 0
            for allowance in allowances:
                total_allowances += float(allowance.amount)
            
            gross_salary += total_allowances
            
            current_app.logger.debug(f"Calculated gross salary for employee {self.employee_id}: {gross_salary} (basic: {self.basic_salary}, allowances: {total_allowances})")
            return gross_salary
            
        except Exception as e:
            current_app.logger.error(f"Error calculating gross salary: {e}")
            return float(self.basic_salary)  # Return basic salary as fallback

    @classmethod
    def create_from_scenario(cls, session, employee_id, scenario_type, known_values, effective_from, company_id):
        """Create salary record from any calculation scenario and automatically create allowance records.

        Note: employee_type_id should be set on the Employee record before calling this method.
        """
        try:
            # Get employee to check employee_type_id
            from application.Models.employees.employee import Employee
            employee = Employee.get_employee_by_id(session, employee_id)
            if not employee or not employee.employee_type_id:
                raise ValueError("Employee must have employee_type_id set before creating salary record")

            # Calculate basic salary based on scenario without requiring existing salary record
            calculation_result = cls._calculate_salary_from_scenario(
                session, employee_id, employee.employee_type_id, scenario_type, known_values, effective_from, company_id
            )

            # Create salary record with calculated basic salary
            salary_data = {
                'employee_id': employee_id,
                'basic_salary': calculation_result['basic_salary'],
                'calculation_scenario': scenario_type,
                'input_values': known_values,
                'calculated_values': calculation_result,
                'effective_from': effective_from
            }

            # Create the salary record
            salary = cls.create_salary(session, **salary_data)
            if not salary:
                return None

            # Create allowance records for any allowances in known_values
            cls._create_allowances_from_known_values(session, employee_id, known_values, effective_from)

            return salary

        except Exception as e:
            current_app.logger.error(f"Error creating salary from scenario: {e}")
            return None

    @classmethod
    def _calculate_salary_from_scenario(cls, session, employee_id, employee_type_id, scenario_type, known_values, effective_from, company_id):
        """Calculate salary components from scenario without requiring existing salary record."""
        try:
            from decimal import Decimal
            from application.Models.company import Company
            from application.Models.tax_bracket import TaxBracket
            from application.Models.payroll_policy import PayrollPolicy

            # Get company and country information
            company = Company.get_company_by_id(company_id)
            if not company or not company.country_id:
                raise ValueError("Company must have country_id set for payroll calculations")

            # Get active payroll policy for this employee type and country (Income tax policy)
            policy = PayrollPolicy.get_active_policy(
                policy_type_code='INCOME_TAX',
                country_id=company.country_id,
                employee_type_id=employee_type_id,
                date=effective_from
            )
            if not policy:
                raise ValueError(f"No active INCOME_TAX policy found for country {company.country_id} and employee type {employee_type_id}")

            # Extract allowances from known_values
            allowances = {}
            total_allowances = Decimal('0')
            for key, value in known_values.items():
                if key not in ['basic_salary', 'gross_salary', 'net_salary', 'total_staff_cost']:
                    allowances[key] = Decimal(str(value))
                    total_allowances += Decimal(str(value))

            if scenario_type == "net_plus_allowances":
                return cls._calculate_backward_from_net_standalone(
                    known_values, allowances, total_allowances, policy, effective_from
                )
            elif scenario_type == "basic_plus_allowances":
                return cls._calculate_forward_from_basic_standalone(
                    known_values, allowances, total_allowances, policy, effective_from
                )
            elif scenario_type == "gross_plus_allowances":
                return cls._calculate_from_gross_standalone(
                    known_values, allowances, total_allowances, policy, effective_from
                )
            elif scenario_type == "total_cost_breakdown":
                return cls._calculate_backward_from_total_cost_standalone(
                    known_values, allowances, total_allowances, policy, effective_from
                )
            else:
                raise ValueError(f"Unknown calculation scenario: {scenario_type}")

        except Exception as e:
            current_app.logger.error(f"Error in salary calculation from scenario: {e}")
            raise

    @classmethod
    def _calculate_backward_from_net_standalone(cls, known_values, allowances, total_allowances, policy, effective_from):
        """Calculate basic salary from net salary + allowances (backward calculation) including statutory deductions."""
        from decimal import Decimal
        from application.Models.tax_bracket import TaxBracket
        from application.Services.payroll_calculation_service import PayrollCalculationService

        target_net = Decimal(str(known_values['net_salary']))

        # Use iterative approach to find basic salary that yields target net
        # Start with an estimate: net + estimated tax + estimated deductions
        basic_salary_estimate = target_net * Decimal('1.5')  # Start with 50% estimate for tax + deductions

        for iteration in range(20):  # Max 20 iterations
            # Calculate gross salary
            gross_salary = basic_salary_estimate + total_allowances

            # Calculate tax on gross salary
            total_tax = TaxBracket.calculate_progressive_tax(policy.policy_id, gross_salary)

            # Calculate statutory deductions (simplified for estimation)
            # This is a rough estimate - actual calculation would need full payroll service
            pensionable_salary = gross_salary - allowances.get('transport', Decimal('0'))
            estimated_pension = pensionable_salary * Decimal('0.06')  # 6% pension
            estimated_maternity = pensionable_salary * Decimal('0.003')  # 0.3% maternity
            estimated_cbhi = Decimal('3000')  # Rough CBHI estimate

            total_deductions = estimated_pension + estimated_maternity + estimated_cbhi

            # Calculate resulting net salary
            calculated_net = gross_salary - total_tax - total_deductions

            # Check if we're close enough (within 10 RWF due to deduction estimates)
            if abs(calculated_net - target_net) < Decimal('10'):
                break

            # Adjust basic salary estimate
            difference = target_net - calculated_net
            basic_salary_estimate += difference * Decimal('0.7')  # Smaller damping factor

            # Ensure basic salary doesn't go negative
            if basic_salary_estimate < Decimal('0'):
                basic_salary_estimate = target_net * Decimal('0.5')

        return {
            'basic_salary': float(basic_salary_estimate),
            'gross_salary': float(gross_salary),
            'total_allowances': float(total_allowances),
            'total_tax': float(total_tax),
            'estimated_deductions': float(total_deductions),
            'net_salary': float(calculated_net),
            'allowances': {k: float(v) for k, v in allowances.items()},
            'tax_breakdown': {'total_tax': float(total_tax)},
            'note': 'Deductions are estimated. Run full payroll calculation for exact amounts.'
        }

    @classmethod
    def _calculate_forward_from_basic_standalone(cls, known_values, allowances, total_allowances, policy, effective_from):
        """Calculate forward from basic salary + allowances."""
        from decimal import Decimal
        from application.Models.tax_bracket import TaxBracket

        basic_salary = Decimal(str(known_values['basic_salary']))
        gross_salary = basic_salary + total_allowances

        # Calculate tax
        total_tax = TaxBracket.calculate_progressive_tax(policy.policy_id, gross_salary)
        net_salary = gross_salary - total_tax

        return {
            'basic_salary': float(basic_salary),
            'gross_salary': float(gross_salary),
            'total_allowances': float(total_allowances),
            'total_tax': float(total_tax),
            'net_salary': float(net_salary),
            'allowances': {k: float(v) for k, v in allowances.items()},
            'tax_breakdown': {'total_tax': float(total_tax)}
        }

    @classmethod
    def _calculate_from_gross_standalone(cls, known_values, allowances, total_allowances, policy, effective_from):
        """Calculate from gross salary + allowances."""
        from decimal import Decimal
        from application.Models.tax_bracket import TaxBracket

        gross_salary = Decimal(str(known_values['gross_salary']))
        basic_salary = gross_salary - total_allowances

        # Calculate tax
        total_tax = TaxBracket.calculate_progressive_tax(policy.policy_id, gross_salary)
        net_salary = gross_salary - total_tax

        return {
            'basic_salary': float(basic_salary),
            'gross_salary': float(gross_salary),
            'total_allowances': float(total_allowances),
            'total_tax': float(total_tax),
            'net_salary': float(net_salary),
            'allowances': {k: float(v) for k, v in allowances.items()},
            'tax_breakdown': {'total_tax': float(total_tax)}
        }

    @classmethod
    def _calculate_backward_from_total_cost_standalone(cls, known_values, allowances, total_allowances, policy, effective_from):
        """Calculate backward from total staff cost."""
        from decimal import Decimal
        from application.Models.tax_bracket import TaxBracket

        total_cost = Decimal(str(known_values['total_staff_cost']))

        # Estimate: total_cost = gross_salary + employer_contributions
        # For now, assume employer contributions are ~10% of gross
        estimated_gross = total_cost / Decimal('1.1')
        basic_salary = estimated_gross - total_allowances

        # Calculate tax
        total_tax = TaxBracket.calculate_progressive_tax(policy.policy_id, estimated_gross)
        net_salary = estimated_gross - total_tax

        return {
            'basic_salary': float(basic_salary),
            'gross_salary': float(estimated_gross),
            'total_allowances': float(total_allowances),
            'total_tax': float(total_tax),
            'net_salary': float(net_salary),
            'total_staff_cost': float(total_cost),
            'allowances': {k: float(v) for k, v in allowances.items()},
            'tax_breakdown': {'total_tax': float(total_tax)}
        }

    @classmethod
    def _create_allowances_from_known_values(cls, session, employee_id, known_values, effective_from):
        """Create allowance records based on known_values from salary scenario.

        Args:
            session: Database session
            employee_id: Employee ID
            known_values: Dict containing allowance amounts (e.g., {"transport": 50000, "housing": 100000})
            effective_from: Effective date for allowances
        """
        try:
            from application.Models.employees.allowance_type import AllowanceType
            from application.Models.employees.employee_allowance import EmployeeAllowance

            # Define allowance code mappings
            allowance_mappings = {
                'transport': 'TRANSPORT',
                'housing': 'HOUSING',
                'communication': 'COMMUNICATION',
                'medical': 'MEDICAL',
                'meal': 'MEAL',
                'overtime': 'OVERTIME',
                'bonus': 'BONUS'
            }

            created_allowances = []

            for allowance_key, amount in known_values.items():
                # Skip non-allowance fields
                if allowance_key in ['basic_salary', 'gross_salary', 'net_salary', 'total_staff_cost']:
                    continue

                # Get the allowance type code
                allowance_code = allowance_mappings.get(allowance_key, allowance_key.upper())

                # Find or create allowance type
                allowance_type = AllowanceType.get_by_code(session, allowance_code)
                if not allowance_type:
                    # Create allowance type if it doesn't exist
                    allowance_type_data = {
                        'name': f"{allowance_key.title()} Allowance",
                        'code': allowance_code,
                        'description': f"Monthly {allowance_key} allowance",
                        'is_taxable': True,
                        'is_pensionable': True,
                        'calculation_type': 'FIXED_AMOUNT'
                    }
                    allowance_type = AllowanceType.create_allowance_type(session, **allowance_type_data)
                    if not allowance_type:
                        current_app.logger.error(f"Failed to create allowance type: {allowance_code}")
                        continue

                # Check if employee already has this allowance type
                existing_allowance = EmployeeAllowance.get_allowance_by_type(
                    session, employee_id, allowance_type.allowance_type_id, effective_from
                )

                if existing_allowance:
                    current_app.logger.warning(f"Employee {employee_id} already has {allowance_code} allowance, skipping...")
                    continue

                # Create the allowance record
                allowance_data = {
                    'employee_id': employee_id,
                    'allowance_type_id': allowance_type.allowance_type_id,
                    'amount': amount,
                    'effective_from': effective_from,
                    'effective_to': None,
                    'is_active': True
                }

                allowance = EmployeeAllowance.create_allowance(session, **allowance_data)
                if allowance:
                    created_allowances.append(allowance)
                    current_app.logger.info(f"Created {allowance_code} allowance for employee {employee_id}: {amount}")

            current_app.logger.info(f"Created {len(created_allowances)} allowance records for employee {employee_id}")
            return created_allowances

        except Exception as e:
            current_app.logger.error(f"Error creating allowances from known values: {e}")
            return []

    def calculate_gross_salary_decimal(self, session, pay_period_start, pay_period_end):
        """Calculate gross salary including allowances using Decimal arithmetic."""
        try:
            # Start with basic salary as Decimal
            gross_salary = Decimal(str(self.basic_salary))

            # Add allowances
            from application.Models.employees.employee_allowance import EmployeeAllowance
            allowances = EmployeeAllowance.get_active_allowances(session, self.employee_id, pay_period_start)

            total_allowances = Decimal('0')
            for allowance in allowances:
                total_allowances += Decimal(str(allowance.amount))

            gross_salary += total_allowances

            current_app.logger.debug(f"Calculated gross salary for employee {self.employee_id}: {gross_salary} (basic: {self.basic_salary}, allowances: {total_allowances})")
            return gross_salary

        except Exception as e:
            current_app.logger.error(f"Error calculating gross salary: {e}")
            return Decimal(str(self.basic_salary))  # Return basic salary as fallback

    def to_dict_with_scenario(self):
        """Dictionary representation including scenario information."""
        base_dict = self.to_dict()
        base_dict.update({
            "calculation_scenario": self.calculation_scenario,
            "input_values": self.input_values,
            "calculated_values": self.calculated_values
        })
        return base_dict
