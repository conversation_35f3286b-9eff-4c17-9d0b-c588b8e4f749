from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUI<PERSON>
from flask import current_app as app
from datetime import datetime, date

class EmployeeShift(db.Model):
    """Model representing the assignment of shifts to employees."""
    __tablename__ = 'employee_shifts'

    assignment_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    employee_id = db.Column(UUID(as_uuid=True), db.<PERSON>ey('employees.employee_id'), nullable=False)  # Foreign key reference to employee
    shift_id = db.Column(UUID(as_uuid=True), db.<PERSON>ey('shifts.shift_id'), nullable=False)  # Foreign key reference to shift

    # Relationships
    employee = db.relationship('Employee', backref=db.backref('shift_assignments', lazy='dynamic'))
    shift = db.relationship('Shift', backref=db.backref('employee_assignments', lazy='dynamic'))

    # Assignment period
    effective_start_date = db.Column(db.Date, nullable=False)  # When this assignment starts
    effective_end_date = db.Column(db.Date, nullable=True)  # When this assignment ends (null = indefinite)

    # Override default shift settings for this employee (all nullable)
    custom_start_time = db.Column(db.Time, nullable=True)
    custom_end_time = db.Column(db.Time, nullable=True)
    custom_break_duration = db.Column(db.Integer, nullable=True)  # In minutes
    custom_working_days = db.Column(db.String(20), nullable=True)  # e.g., "1,2,3,4,5" for Mon-Fri

    # Status
    is_active = db.Column(db.Boolean, default=True, nullable=False)

    # Metadata
    created_by = db.Column(UUID(as_uuid=True), nullable=True)  # User who created this assignment
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    def __str__(self):
        """Return a string representation of the object."""
        return f"EmployeeShift [assignment_id={self.assignment_id}, employee_id={self.employee_id}, shift_id={self.shift_id}]"

    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "assignment_id": str(self.assignment_id),
            "employee_id": str(self.employee_id),
            "shift_id": str(self.shift_id),
            "effective_start_date": self.effective_start_date.strftime('%Y-%m-%d') if self.effective_start_date else None,
            "effective_end_date": self.effective_end_date.strftime('%Y-%m-%d') if self.effective_end_date else None,
            "custom_start_time": self.custom_start_time.strftime('%H:%M') if self.custom_start_time else None,
            "custom_end_time": self.custom_end_time.strftime('%H:%M') if self.custom_end_time else None,
            "custom_break_duration": self.custom_break_duration,
            "custom_working_days": self.custom_working_days,
            "is_active": self.is_active,
            "created_by": str(self.created_by) if self.created_by else None,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

    @classmethod
    def get_assignment_by_id(cls, session, assignment_id):
        """Get a shift assignment by ID."""
        try:
            # Convert string to UUID if needed
            if isinstance(assignment_id, str):
                try:
                    assignment_id = uuid.UUID(assignment_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {assignment_id}")
                    return None

            assignment = session.query(cls).filter_by(assignment_id=assignment_id).first()
            return assignment
        except Exception as e:
            app.logger.error(f"Error getting shift assignment by ID: {e}")
            return None

    @classmethod
    def get_employee_shifts(cls, session, employee_id, active_only=True, current_date=None):
        """Get all shift assignments for an employee."""
        try:
            # Convert string to UUID if needed
            if isinstance(employee_id, str):
                try:
                    employee_id = uuid.UUID(employee_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {employee_id}")
                    return []

            query = session.query(cls).filter_by(employee_id=employee_id)

            if active_only:
                query = query.filter_by(is_active=True)

            if current_date:
                if isinstance(current_date, str):
                    try:
                        current_date = datetime.strptime(current_date, '%Y-%m-%d').date()
                    except ValueError:
                        app.logger.error(f"Invalid date format: {current_date}")
                        current_date = date.today()
                elif not isinstance(current_date, date):
                    current_date = date.today()

                # Filter for assignments that are effective on the current date
                query = query.filter(
                    cls.effective_start_date <= current_date,
                    (cls.effective_end_date >= current_date) | (cls.effective_end_date.is_(None))
                )

            assignments = query.all()
            return assignments
        except Exception as e:
            app.logger.error(f"Error getting employee shifts: {e}")
            return []

    @classmethod
    def get_shift_employees(cls, session, shift_id, active_only=True, current_date=None):
        """Get all employees assigned to a shift."""
        try:
            # Convert string to UUID if needed
            if isinstance(shift_id, str):
                try:
                    shift_id = uuid.UUID(shift_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {shift_id}")
                    return []

            query = session.query(cls).filter_by(shift_id=shift_id)

            if active_only:
                query = query.filter_by(is_active=True)

            if current_date:
                if isinstance(current_date, str):
                    try:
                        current_date = datetime.strptime(current_date, '%Y-%m-%d').date()
                    except ValueError:
                        app.logger.error(f"Invalid date format: {current_date}")
                        current_date = date.today()
                elif not isinstance(current_date, date):
                    current_date = date.today()

                # Filter for assignments that are effective on the current date
                query = query.filter(
                    cls.effective_start_date <= current_date,
                    (cls.effective_end_date >= current_date) | (cls.effective_end_date.is_(None))
                )

            assignments = query.all()
            return assignments
        except Exception as e:
            app.logger.error(f"Error getting shift employees: {e}")
            return []

    @classmethod
    def get_active_assignments_for_employee(cls, session, employee_id, reference_date=None):
        """Get active shift assignments for an employee on or around a reference date."""
        try:
            # Convert employee_id to UUID if needed
            if isinstance(employee_id, str):
                try:
                    employee_id = uuid.UUID(employee_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {employee_id}")
                    return []

            # Convert reference_date to date object if needed
            if reference_date and isinstance(reference_date, str):
                try:
                    reference_date = datetime.strptime(reference_date, '%Y-%m-%d').date()
                except ValueError:
                    app.logger.error(f"Invalid date format: {reference_date}")
                    return []

            if not reference_date:
                reference_date = date.today()

            # Query for active assignments that overlap with the reference date
            query = session.query(cls).filter(
                cls.employee_id == employee_id,
                cls.is_active == True,
                cls.effective_start_date <= reference_date
            )

            # Add condition for end date (either null or >= reference_date)
            query = query.filter(
                db.or_(
                    cls.effective_end_date.is_(None),
                    cls.effective_end_date >= reference_date
                )
            )

            assignments = query.all()
            return assignments
        except Exception as e:
            app.logger.error(f"Error getting active assignments for employee: {e}")
            return []

    @classmethod
    def create_assignment(cls, session, **kwargs):
        """Create a new shift assignment."""
        try:
            # Convert employee_id to UUID if it's a string
            if 'employee_id' in kwargs and isinstance(kwargs['employee_id'], str):
                try:
                    kwargs['employee_id'] = uuid.UUID(kwargs['employee_id'])
                except ValueError:
                    app.logger.error(f"Invalid employee_id UUID format: {kwargs['employee_id']}")
                    return None

            # Convert shift_id to UUID if it's a string
            if 'shift_id' in kwargs and isinstance(kwargs['shift_id'], str):
                try:
                    kwargs['shift_id'] = uuid.UUID(kwargs['shift_id'])
                except ValueError:
                    app.logger.error(f"Invalid shift_id UUID format: {kwargs['shift_id']}")
                    return None

            # Convert created_by to UUID if it's a string
            if 'created_by' in kwargs and isinstance(kwargs['created_by'], str):
                try:
                    kwargs['created_by'] = uuid.UUID(kwargs['created_by'])
                except ValueError:
                    app.logger.error(f"Invalid created_by UUID format: {kwargs['created_by']}")
                    return None

            # Convert date strings to date objects
            if 'effective_start_date' in kwargs and isinstance(kwargs['effective_start_date'], str):
                try:
                    kwargs['effective_start_date'] = datetime.strptime(kwargs['effective_start_date'], '%Y-%m-%d').date()
                except ValueError:
                    app.logger.error(f"Invalid effective_start_date format: {kwargs['effective_start_date']}")
                    return None

            if 'effective_end_date' in kwargs and kwargs['effective_end_date'] and isinstance(kwargs['effective_end_date'], str):
                try:
                    kwargs['effective_end_date'] = datetime.strptime(kwargs['effective_end_date'], '%Y-%m-%d').date()
                except ValueError:
                    app.logger.error(f"Invalid effective_end_date format: {kwargs['effective_end_date']}")
                    return None

            # Convert time strings to time objects
            if 'custom_start_time' in kwargs and kwargs['custom_start_time'] and isinstance(kwargs['custom_start_time'], str):
                try:
                    kwargs['custom_start_time'] = datetime.strptime(kwargs['custom_start_time'], '%H:%M').time()
                except ValueError:
                    app.logger.error(f"Invalid custom_start_time format: {kwargs['custom_start_time']}")
                    return None

            if 'custom_end_time' in kwargs and kwargs['custom_end_time'] and isinstance(kwargs['custom_end_time'], str):
                try:
                    kwargs['custom_end_time'] = datetime.strptime(kwargs['custom_end_time'], '%H:%M').time()
                except ValueError:
                    app.logger.error(f"Invalid custom_end_time format: {kwargs['custom_end_time']}")
                    return None

            assignment = cls(**kwargs)
            session.add(assignment)
            session.commit()
            app.logger.info(f"Created new shift assignment: {assignment.assignment_id}")
            return assignment
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating shift assignment: {e}")
            return None

    @classmethod
    def update_assignment(cls, session, assignment_id, **kwargs):
        """Update a shift assignment."""
        try:
            # Convert assignment_id to UUID if it's a string
            if isinstance(assignment_id, str):
                try:
                    assignment_id = uuid.UUID(assignment_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {assignment_id}")
                    return None

            # Convert date strings to date objects
            if 'effective_start_date' in kwargs and isinstance(kwargs['effective_start_date'], str):
                try:
                    kwargs['effective_start_date'] = datetime.strptime(kwargs['effective_start_date'], '%Y-%m-%d').date()
                except ValueError:
                    app.logger.error(f"Invalid effective_start_date format: {kwargs['effective_start_date']}")
                    return None

            if 'effective_end_date' in kwargs and kwargs['effective_end_date'] and isinstance(kwargs['effective_end_date'], str):
                try:
                    kwargs['effective_end_date'] = datetime.strptime(kwargs['effective_end_date'], '%Y-%m-%d').date()
                except ValueError:
                    app.logger.error(f"Invalid effective_end_date format: {kwargs['effective_end_date']}")
                    return None

            # Convert time strings to time objects
            if 'custom_start_time' in kwargs and kwargs['custom_start_time'] and isinstance(kwargs['custom_start_time'], str):
                try:
                    kwargs['custom_start_time'] = datetime.strptime(kwargs['custom_start_time'], '%H:%M').time()
                except ValueError:
                    app.logger.error(f"Invalid custom_start_time format: {kwargs['custom_start_time']}")
                    return None

            if 'custom_end_time' in kwargs and kwargs['custom_end_time'] and isinstance(kwargs['custom_end_time'], str):
                try:
                    kwargs['custom_end_time'] = datetime.strptime(kwargs['custom_end_time'], '%H:%M').time()
                except ValueError:
                    app.logger.error(f"Invalid custom_end_time format: {kwargs['custom_end_time']}")
                    return None

            assignment = session.query(cls).filter_by(assignment_id=assignment_id).first()
            if not assignment:
                app.logger.warning(f"Shift assignment with ID {assignment_id} not found")
                return None

            # Update assignment attributes
            for key, value in kwargs.items():
                if hasattr(assignment, key):
                    setattr(assignment, key, value)

            session.commit()
            app.logger.info(f"Updated shift assignment: {assignment.assignment_id}")
            return assignment
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error updating shift assignment: {e}")
            return None

    @classmethod
    def delete_assignment(cls, session, assignment_id):
        """Delete a shift assignment."""
        try:
            # Convert assignment_id to UUID if it's a string
            if isinstance(assignment_id, str):
                try:
                    assignment_id = uuid.UUID(assignment_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {assignment_id}")
                    return False

            assignment = session.query(cls).filter_by(assignment_id=assignment_id).first()
            if not assignment:
                app.logger.warning(f"Shift assignment with ID {assignment_id} not found")
                return False

            session.delete(assignment)
            session.commit()
            app.logger.info(f"Deleted shift assignment with ID: {assignment_id}")
            return True
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error deleting shift assignment: {e}")
            return False
