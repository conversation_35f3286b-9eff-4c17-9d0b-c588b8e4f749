from application.database import db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime
from flask import current_app as app

class LeaveAuditLog(db.Model):
    """
    Audit trail for all leave-related changes (stored in tenant database).
    Tracks balance changes, request status changes, and administrative actions.
    """
    __tablename__ = 'leave_audit_logs'
    
    audit_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    employee_id = db.Column(UUID(as_uuid=True), db.ForeignKey('employees.employee_id'), nullable=False)
    action_type = db.Column(db.String(50), nullable=False)  # 'balance_created', 'balance_adjusted', 'request_approved', etc.
    entity_type = db.Column(db.String(50), nullable=False)  # 'leave_balance', 'leave_request', 'employee'
    entity_id = db.Column(UUID(as_uuid=True), nullable=True)  # ID of the affected entity
    old_value = db.Column(db.JSON, nullable=True)  # Previous state
    new_value = db.Column(db.JSON, nullable=True)  # New state
    reason = db.Column(db.Text, nullable=True)  # Reason for change
    performed_by = db.Column(db.String(36), nullable=False)  # User ID from central database
    ip_address = db.Column(db.String(45), nullable=True)  # IP address of user
    user_agent = db.Column(db.Text, nullable=True)  # Browser/client info
    created_at = db.Column(db.DateTime, server_default=db.func.now(), nullable=False)
    
    # Relationships
    employee = db.relationship('Employee', backref=db.backref('audit_logs', lazy='dynamic'))
    
    # Indexes for efficient querying
    __table_args__ = (
        db.Index('idx_audit_employee_date', 'employee_id', 'created_at'),
        db.Index('idx_audit_action_type', 'action_type'),
        db.Index('idx_audit_entity', 'entity_type', 'entity_id'),
        db.Index('idx_audit_performed_by', 'performed_by'),
    )
    
    def __str__(self):
        """Return a string representation of the object."""
        return f"LeaveAuditLog [audit_id={self.audit_id}, action={self.action_type}, employee={self.employee_id}]"
    
    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "audit_id": str(self.audit_id),
            "employee_id": str(self.employee_id),
            "action_type": self.action_type,
            "entity_type": self.entity_type,
            "entity_id": str(self.entity_id) if self.entity_id else None,
            "old_value": self.old_value,
            "new_value": self.new_value,
            "reason": self.reason,
            "performed_by": self.performed_by,
            "ip_address": self.ip_address,
            "user_agent": self.user_agent,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None
        }
    
    @classmethod
    def log_action(cls, session, employee_id, action_type, entity_type, entity_id=None, 
                   old_value=None, new_value=None, reason=None, performed_by=None, 
                   ip_address=None, user_agent=None):
        """
        Log an audit action.
        
        Args:
            session: Database session
            employee_id: ID of the affected employee
            action_type: Type of action (e.g., 'balance_created', 'request_approved')
            entity_type: Type of entity (e.g., 'leave_balance', 'leave_request')
            entity_id: ID of the affected entity
            old_value: Previous state (dict)
            new_value: New state (dict)
            reason: Reason for the change
            performed_by: User ID who performed the action
            ip_address: IP address of the user
            user_agent: Browser/client information
        """
        try:
            audit_log = cls(
                employee_id=employee_id,
                action_type=action_type,
                entity_type=entity_type,
                entity_id=entity_id,
                old_value=old_value,
                new_value=new_value,
                reason=reason,
                performed_by=performed_by,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            session.add(audit_log)
            session.commit()
            
            app.logger.info(f"Logged audit action: {action_type} for employee {employee_id}")
            return audit_log
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error logging audit action: {e}")
            return None

    @classmethod
    def get_employee_audit_trail(cls, session, employee_id, limit=50, offset=0):
        """Get audit trail for a specific employee."""
        return session.query(cls).filter_by(employee_id=employee_id)\
                     .order_by(cls.created_at.desc())\
                     .limit(limit).offset(offset).all()

    @classmethod
    def get_audit_trail_by_action(cls, session, action_type, limit=50, offset=0):
        """Get audit trail for a specific action type."""
        return session.query(cls).filter_by(action_type=action_type)\
                     .order_by(cls.created_at.desc())\
                     .limit(limit).offset(offset).all()

    @classmethod
    def get_recent_audit_logs(cls, session, hours=24, limit=100):
        """Get recent audit logs within specified hours."""
        from datetime import timedelta
        cutoff_time = datetime.now() - timedelta(hours=hours)

        return session.query(cls).filter(cls.created_at >= cutoff_time)\
                     .order_by(cls.created_at.desc())\
                     .limit(limit).all()
