from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUID
from flask import current_app as app
from datetime import datetime

class LeaveBalance(db.Model):
    """Model representing employee leave balances."""
    __tablename__ = 'leave_balances'

    balance_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    employee_id = db.Column(UUID(as_uuid=True), db.Foreign<PERSON>ey('employees.employee_id'), nullable=False)
    leave_type_id = db.Column(UUID(as_uuid=True), db.ForeignKey('leave_types.leave_type_id'), nullable=False)
    year = db.Column(db.Integer, nullable=False)  # Year for which this balance applies
    total_days = db.Column(db.Float, nullable=False)  # Total days allocated
    used_days = db.Column(db.Float, default=0, nullable=False)  # Days used
    pending_days = db.Column(db.Float, default=0, nullable=False)  # Days pending approval
    carried_over_days = db.Column(db.Float, default=0, nullable=False)  # Days carried over from previous year
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())
    
    # Relationships
    employee = db.relationship('Employee', backref=db.backref('leave_balances', lazy='dynamic'))
    leave_type = db.relationship('LeaveType', backref=db.backref('balances', lazy='dynamic'))
    
    def __str__(self):
        """Return a string representation of the object."""
        return f"LeaveBalance [balance_id={self.balance_id}, employee_id={self.employee_id}, leave_type={self.leave_type.name if self.leave_type else None}]"
    
    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "balance_id": str(self.balance_id),
            "employee_id": str(self.employee_id),
            "leave_type_id": str(self.leave_type_id),
            "leave_type_name": self.leave_type.name if self.leave_type else None,
            "year": self.year,
            "total_days": self.total_days,
            "used_days": self.used_days,
            "pending_days": self.pending_days,
            "carried_over_days": self.carried_over_days,
            "available_days": self.total_days - self.used_days - self.pending_days,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }
    
    @classmethod
    def get_balance_by_id(cls, session, balance_id):
        """Get a leave balance by ID."""
        return session.query(cls).filter_by(balance_id=balance_id).first()
    
    @classmethod
    def get_employee_balance(cls, session, employee_id, leave_type_id, year=None):
        """Get a leave balance for a specific employee and leave type."""
        if year is None:
            year = datetime.now().year
        
        return session.query(cls).filter_by(
            employee_id=employee_id,
            leave_type_id=leave_type_id,
            year=year
        ).first()
    
    @classmethod
    def get_employee_balances(cls, session, employee_id, year=None):
        """Get all leave balances for a specific employee."""
        if year is None:
            year = datetime.now().year
        
        return session.query(cls).filter_by(
            employee_id=employee_id,
            year=year
        ).all()
    
    @classmethod
    def create_balance(cls, session, **kwargs):
        """Create a new leave balance."""
        try:
            balance = cls(**kwargs)
            session.add(balance)
            session.commit()
            app.logger.info(f"Created new leave balance for employee ID: {balance.employee_id}")
            return balance
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating leave balance: {e}")
            return None
    
    @classmethod
    def update_balance(cls, session, balance_id, **kwargs):
        """Update a leave balance."""
        try:
            balance = cls.get_balance_by_id(session, balance_id)
            if not balance:
                app.logger.error(f"Leave balance with ID {balance_id} not found")
                return None
            
            for key, value in kwargs.items():
                setattr(balance, key, value)
            
            balance.updated_at = datetime.now()
            session.commit()
            app.logger.info(f"Updated leave balance ID: {balance_id}")
            return balance
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error updating leave balance: {e}")
            return None
    
    @classmethod
    def adjust_balance(cls, session, employee_id, leave_type_id, adjustment_days, reason=None, year=None):
        """Adjust a leave balance by adding or subtracting days."""
        if year is None:
            year = datetime.now().year
        
        balance = cls.get_employee_balance(session, employee_id, leave_type_id, year)
        if not balance:
            app.logger.error(f"Leave balance not found for employee ID {employee_id} and leave type ID {leave_type_id}")
            return None
        
        try:
            balance.total_days += adjustment_days
            balance.updated_at = datetime.now()
            
            # Log the adjustment
            app.logger.info(f"Adjusted leave balance for employee ID {employee_id}: {adjustment_days} days ({reason})")
            
            session.commit()
            return balance
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error adjusting leave balance: {e}")
            return None
