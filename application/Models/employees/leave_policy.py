from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUID
from flask import current_app as app

class LeavePolicy(db.Model):
    """Model representing country-specific leave policies."""
    __tablename__ = 'leave_policies'

    policy_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    leave_type_id = db.Column(UUID(as_uuid=True), db.Foreign<PERSON>ey('leave_types.leave_type_id'), nullable=False)
    country_id = db.Column(UUID(as_uuid=True), nullable=False)  # Reference to country in central DB
    days_allowed = db.Column(db.Float, nullable=False)  # Number of days allowed for this leave type
    accrual_period = db.Column(db.String(50), nullable=True)  # e.g., monthly, yearly, none
    accrual_rate = db.Column(db.Float, nullable=True)  # Rate at which leave accrues
    max_carryover = db.Column(db.Float, nullable=True)  # Maximum days that can be carried over to next year
    min_service_days = db.Column(db.Integer, default=0, nullable=False)  # Minimum service days required to be eligible
    is_prorated = db.Column(db.Boolean, default=False, nullable=False)  # Whether leave is prorated for new employees
    gender_specific = db.Column(db.String(10), nullable=True)  # male, female, or null for both
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())
    
    # Relationship with LeaveType
    leave_type = db.relationship('LeaveType', backref=db.backref('policies', lazy='dynamic'))
    
    def __str__(self):
        """Return a string representation of the object."""
        return f"LeavePolicy [policy_id={self.policy_id}, leave_type={self.leave_type.name if self.leave_type else None}]"
    
    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "policy_id": str(self.policy_id),
            "leave_type_id": str(self.leave_type_id),
            "country_id": str(self.country_id),
            "days_allowed": self.days_allowed,
            "accrual_period": self.accrual_period,
            "accrual_rate": self.accrual_rate,
            "max_carryover": self.max_carryover,
            "min_service_days": self.min_service_days,
            "is_prorated": self.is_prorated,
            "gender_specific": self.gender_specific,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }
    
    @classmethod
    def get_policy_by_id(cls, session, policy_id):
        """Get a leave policy by ID."""
        return session.query(cls).filter_by(policy_id=policy_id).first()
    
    @classmethod
    def get_policy_for_leave_type_and_country(cls, session, leave_type_id, country_id, gender=None):
        """Get a leave policy for a specific leave type and country, optionally filtered by gender."""
        query = session.query(cls).filter_by(leave_type_id=leave_type_id, country_id=country_id)
        
        if gender:
            # If gender is specified, get policies that are either for that gender or not gender-specific
            query = query.filter((cls.gender_specific == gender) | (cls.gender_specific == None))
        
        return query.first()
    
    @classmethod
    def get_policies_for_country(cls, session, country_id):
        """Get all leave policies for a specific country."""
        return session.query(cls).filter_by(country_id=country_id).all()
    
    @classmethod
    def create_policy(cls, session, **kwargs):
        """Create a new leave policy."""
        try:
            policy = cls(**kwargs)
            session.add(policy)
            session.commit()
            app.logger.info(f"Created new leave policy for leave type ID: {policy.leave_type_id}")
            return policy
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating leave policy: {e}")
            return None
    
    @classmethod
    def update_policy(cls, session, policy_id, **kwargs):
        """Update a leave policy."""
        try:
            policy = cls.get_policy_by_id(session, policy_id)
            if not policy:
                app.logger.error(f"Leave policy with ID {policy_id} not found")
                return None
            
            for key, value in kwargs.items():
                setattr(policy, key, value)
            
            session.commit()
            app.logger.info(f"Updated leave policy ID: {policy_id}")
            return policy
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error updating leave policy: {e}")
            return None
    
    @classmethod
    def delete_policy(cls, session, policy_id):
        """Delete a leave policy."""
        try:
            policy = cls.get_policy_by_id(session, policy_id)
            if not policy:
                app.logger.error(f"Leave policy with ID {policy_id} not found")
                return False
            
            session.delete(policy)
            session.commit()
            app.logger.info(f"Deleted leave policy ID: {policy_id}")
            return True
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error deleting leave policy: {e}")
            return False
