from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUID
from flask import current_app as app
from datetime import datetime, date, timedelta
from application.Models.employees.leave_balance import LeaveBalance

class LeaveRequest(db.Model):
    """Model representing leave requests."""
    __tablename__ = 'leave_requests'

    request_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    employee_id = db.Column(UUID(as_uuid=True), db.<PERSON>Key('employees.employee_id'), nullable=False)
    leave_type_id = db.Column(UUID(as_uuid=True), db.ForeignKey('leave_types.leave_type_id'), nullable=False)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    total_days = db.Column(db.Float, nullable=False)  # Calculated days (may account for half days)
    reason = db.Column(db.Text, nullable=True)
    status = db.Column(db.String(50), default='pending', nullable=False)  # pending, approved, rejected, cancelled
    approved_by = db.Column(UUID(as_uuid=True), nullable=True)  # User ID who approved/rejected
    approved_at = db.Column(db.DateTime, nullable=True)
    rejection_reason = db.Column(db.Text, nullable=True)
    documentation_path = db.Column(db.String(255), nullable=True)  # Path to supporting documents
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())
    
    # Relationships
    employee = db.relationship('Employee', backref=db.backref('leave_requests', lazy='dynamic'))
    leave_type = db.relationship('LeaveType', backref=db.backref('requests', lazy='dynamic'))
    
    def __str__(self):
        """Return a string representation of the object."""
        return f"LeaveRequest [request_id={self.request_id}, employee_id={self.employee_id}, status={self.status}]"
    
    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "request_id": str(self.request_id),
            "employee_id": str(self.employee_id),
            "employee_name": f"{self.employee.first_name} {self.employee.last_name}" if self.employee else None,
            "leave_type_id": str(self.leave_type_id),
            "leave_type_name": self.leave_type.name if self.leave_type else None,
            "start_date": self.start_date.strftime('%Y-%m-%d') if self.start_date else None,
            "end_date": self.end_date.strftime('%Y-%m-%d') if self.end_date else None,
            "total_days": self.total_days,
            "reason": self.reason,
            "status": self.status,
            "approved_by": str(self.approved_by) if self.approved_by else None,
            "approved_at": self.approved_at.strftime('%Y-%m-%d %H:%M:%S') if self.approved_at else None,
            "rejection_reason": self.rejection_reason,
            "documentation_path": self.documentation_path,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }
    
    @classmethod
    def get_request_by_id(cls, session, request_id):
        """Get a leave request by ID."""
        return session.query(cls).filter_by(request_id=request_id).first()
    
    @classmethod
    def get_employee_requests(cls, session, employee_id, status=None, year=None):
        """Get leave requests for a specific employee."""
        query = session.query(cls).filter_by(employee_id=employee_id)
        
        if status:
            query = query.filter_by(status=status)
        
        if year:
            # Filter requests that have start_date or end_date in the specified year
            query = query.filter(
                db.or_(
                    db.extract('year', cls.start_date) == year,
                    db.extract('year', cls.end_date) == year
                )
            )
        
        return query.order_by(cls.start_date.desc()).all()
    
    @classmethod
    def get_pending_requests(cls, session):
        """Get all pending leave requests."""
        return session.query(cls).filter_by(status='pending').order_by(cls.created_at).all()
    
    @classmethod
    def calculate_business_days(cls, start_date, end_date):
        """Calculate the number of business days between two dates."""
        if start_date > end_date:
            return 0
        
        # Calculate the number of days between the dates
        days = (end_date - start_date).days + 1
        
        # Calculate the number of complete weeks
        weeks = days // 7
        
        # Calculate the remaining days
        remaining_days = days % 7
        
        # Calculate the start day of the week (0 = Monday, 6 = Sunday)
        start_day_of_week = start_date.weekday()
        
        # Calculate business days in complete weeks (5 business days per week)
        business_days = weeks * 5
        
        # Add remaining business days
        for i in range(remaining_days):
            day_of_week = (start_day_of_week + i) % 7
            if day_of_week < 5:  # Monday to Friday
                business_days += 1
        
        return business_days
    
    @classmethod
    def create_request(cls, session, **kwargs):
        """Create a new leave request."""
        try:
            # Calculate total days if not provided
            if 'total_days' not in kwargs and 'start_date' in kwargs and 'end_date' in kwargs:
                start_date = kwargs['start_date']
                end_date = kwargs['end_date']
                
                if isinstance(start_date, str):
                    start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                if isinstance(end_date, str):
                    end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                
                kwargs['total_days'] = cls.calculate_business_days(start_date, end_date)
            
            # Check if employee has enough leave balance
            employee_id = kwargs.get('employee_id')
            leave_type_id = kwargs.get('leave_type_id')
            total_days = kwargs.get('total_days')
            
            if employee_id and leave_type_id and total_days:
                year = datetime.now().year
                balance = LeaveBalance.get_employee_balance(session, employee_id, leave_type_id, year)
                
                if balance:
                    available_days = balance.total_days - balance.used_days - balance.pending_days
                    if total_days > available_days:
                        app.logger.warning(f"Insufficient leave balance for employee ID {employee_id}: requested {total_days} days, available {available_days} days")
                        return None, f"Insufficient leave balance. You have {available_days} days available."
                    
                    # Update pending days in balance
                    balance.pending_days += total_days
                    
                else:
                    app.logger.warning(f"No leave balance found for employee ID {employee_id} and leave type ID {leave_type_id}")
                    return None, "No leave balance found for this leave type."
            
            # Create the leave request
            request = cls(**kwargs)
            session.add(request)
            session.commit()
            
            app.logger.info(f"Created new leave request for employee ID: {request.employee_id}")
            return request, None
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating leave request: {e}")
            return None, str(e)
    
    @classmethod
    def update_request(cls, session, request_id, **kwargs):
        """Update a leave request."""
        try:
            request = cls.get_request_by_id(session, request_id)
            if not request:
                app.logger.error(f"Leave request with ID {request_id} not found")
                return None, "Leave request not found."
            
            # Only allow updates if the request is pending
            if request.status != 'pending':
                app.logger.warning(f"Cannot update leave request with ID {request_id} because it is not pending")
                return None, f"Cannot update leave request because it is {request.status}."
            
            # If changing dates or total days, update the balance
            old_total_days = request.total_days
            new_total_days = kwargs.get('total_days', old_total_days)
            
            if new_total_days != old_total_days:
                # Update the balance
                balance = LeaveBalance.get_employee_balance(
                    session, 
                    request.employee_id, 
                    request.leave_type_id, 
                    request.start_date.year
                )
                
                if balance:
                    # Check if employee has enough leave balance for the new total days
                    available_days = balance.total_days - balance.used_days - balance.pending_days + old_total_days
                    if new_total_days > available_days:
                        app.logger.warning(f"Insufficient leave balance for employee ID {request.employee_id}: requested {new_total_days} days, available {available_days} days")
                        return None, f"Insufficient leave balance. You have {available_days} days available."
                    
                    # Update pending days in balance
                    balance.pending_days = balance.pending_days - old_total_days + new_total_days
            
            # Update the request
            for key, value in kwargs.items():
                setattr(request, key, value)
            
            request.updated_at = datetime.now()
            session.commit()
            
            app.logger.info(f"Updated leave request ID: {request_id}")
            return request, None
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error updating leave request: {e}")
            return None, str(e)
    
    @classmethod
    def approve_request(cls, session, request_id, approved_by):
        """Approve a leave request."""
        try:
            request = cls.get_request_by_id(session, request_id)
            if not request:
                app.logger.error(f"Leave request with ID {request_id} not found")
                return None, "Leave request not found."
            
            # Only allow approval if the request is pending
            if request.status != 'pending':
                app.logger.warning(f"Cannot approve leave request with ID {request_id} because it is not pending")
                return None, f"Cannot approve leave request because it is {request.status}."
            
            # Update the request
            request.status = 'approved'
            request.approved_by = approved_by
            request.approved_at = datetime.now()
            
            # Update the balance
            balance = LeaveBalance.get_employee_balance(
                session, 
                request.employee_id, 
                request.leave_type_id, 
                request.start_date.year
            )
            
            if balance:
                balance.pending_days -= request.total_days
                balance.used_days += request.total_days
            
            session.commit()
            
            app.logger.info(f"Approved leave request ID: {request_id}")
            return request, None
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error approving leave request: {e}")
            return None, str(e)
    
    @classmethod
    def reject_request(cls, session, request_id, rejection_reason, rejected_by):
        """Reject a leave request."""
        try:
            request = cls.get_request_by_id(session, request_id)
            if not request:
                app.logger.error(f"Leave request with ID {request_id} not found")
                return None, "Leave request not found."
            
            # Only allow rejection if the request is pending
            if request.status != 'pending':
                app.logger.warning(f"Cannot reject leave request with ID {request_id} because it is not pending")
                return None, f"Cannot reject leave request because it is {request.status}."
            
            # Update the request
            request.status = 'rejected'
            request.rejection_reason = rejection_reason
            request.approved_by = rejected_by  # Using the same field for the person who rejected
            request.approved_at = datetime.now()
            
            # Update the balance
            balance = LeaveBalance.get_employee_balance(
                session, 
                request.employee_id, 
                request.leave_type_id, 
                request.start_date.year
            )
            
            if balance:
                balance.pending_days -= request.total_days
            
            session.commit()
            
            app.logger.info(f"Rejected leave request ID: {request_id}")
            return request, None
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error rejecting leave request: {e}")
            return None, str(e)
    
    @classmethod
    def cancel_request(cls, session, request_id):
        """Cancel a leave request."""
        try:
            request = cls.get_request_by_id(session, request_id)
            if not request:
                app.logger.error(f"Leave request with ID {request_id} not found")
                return None, "Leave request not found."
            
            # Only allow cancellation if the request is pending or approved
            if request.status not in ['pending', 'approved']:
                app.logger.warning(f"Cannot cancel leave request with ID {request_id} because it is {request.status}")
                return None, f"Cannot cancel leave request because it is {request.status}."
            
            old_status = request.status
            
            # Update the request
            request.status = 'cancelled'
            request.updated_at = datetime.now()
            
            # Update the balance
            balance = LeaveBalance.get_employee_balance(
                session, 
                request.employee_id, 
                request.leave_type_id, 
                request.start_date.year
            )
            
            if balance:
                if old_status == 'pending':
                    balance.pending_days -= request.total_days
                elif old_status == 'approved':
                    balance.used_days -= request.total_days
            
            session.commit()
            
            app.logger.info(f"Cancelled leave request ID: {request_id}")
            return request, None
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error cancelling leave request: {e}")
            return None, str(e)
