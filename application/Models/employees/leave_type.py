from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUID
from flask import current_app as app

class LeaveType(db.Model):
    """Model representing types of leave available."""
    __tablename__ = 'leave_types'

    leave_type_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    name = db.Column(db.String(255), nullable=False)  # e.g., Annual, Maternity, Paternity, Sick, Circumstantial
    code = db.Column(db.String(50), nullable=False, unique=True)  # Unique code for programmatic reference
    description = db.Column(db.Text, nullable=True)
    is_paid = db.Column(db.<PERSON>, default=True, nullable=False)
    requires_approval = db.Column(db.<PERSON>, default=True, nullable=False)
    requires_documentation = db.Column(db.<PERSON>, default=False, nullable=False)  # e.g., medical certificate for sick leave
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())
    
    def __str__(self):
        """Return a string representation of the object."""
        return f"LeaveType [leave_type_id={self.leave_type_id}, name={self.name}]"
    
    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "leave_type_id": str(self.leave_type_id),
            "name": self.name,
            "code": self.code,
            "description": self.description,
            "is_paid": self.is_paid,
            "requires_approval": self.requires_approval,
            "requires_documentation": self.requires_documentation,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }
    
    @classmethod
    def get_leave_type_by_id(cls, session, leave_type_id):
        """Get a leave type by ID."""
        return session.query(cls).filter_by(leave_type_id=leave_type_id).first()
    
    @classmethod
    def get_leave_type_by_code(cls, session, code):
        """Get a leave type by code."""
        return session.query(cls).filter_by(code=code).first()
    
    @classmethod
    def get_all_leave_types(cls, session):
        """Get all leave types."""
        return session.query(cls).all()
    
    @classmethod
    def create_leave_type(cls, session, **kwargs):
        """Create a new leave type."""
        try:
            leave_type = cls(**kwargs)
            session.add(leave_type)
            session.commit()
            app.logger.info(f"Created new leave type: {leave_type.name}")
            return leave_type
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating leave type: {e}")
            return None
    
    @classmethod
    def update_leave_type(cls, session, leave_type_id, **kwargs):
        """Update a leave type."""
        try:
            leave_type = cls.get_leave_type_by_id(session, leave_type_id)
            if not leave_type:
                app.logger.error(f"Leave type with ID {leave_type_id} not found")
                return None
            
            for key, value in kwargs.items():
                setattr(leave_type, key, value)
            
            session.commit()
            app.logger.info(f"Updated leave type: {leave_type.name}")
            return leave_type
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error updating leave type: {e}")
            return None
    
    @classmethod
    def delete_leave_type(cls, session, leave_type_id):
        """Delete a leave type if it has no associated data."""
        try:
            leave_type = cls.get_leave_type_by_id(session, leave_type_id)
            if not leave_type:
                app.logger.error(f"Leave type with ID {leave_type_id} not found")
                return False, "Leave type not found"

            # Check for associated data before deletion
            from application.Models.employees.leave_policy import LeavePolicy
            from application.Models.employees.leave_balance import LeaveBalance
            from application.Models.employees.leave_request import LeaveRequest

            # Check leave policies
            policies_count = session.query(LeavePolicy).filter_by(leave_type_id=leave_type_id).count()
            if policies_count > 0:
                app.logger.warning(f"Cannot delete leave type {leave_type.name}: {policies_count} policies exist")
                return False, f"Cannot delete leave type. {policies_count} leave policies are using this leave type."

            # Check leave balances
            balances_count = session.query(LeaveBalance).filter_by(leave_type_id=leave_type_id).count()
            if balances_count > 0:
                app.logger.warning(f"Cannot delete leave type {leave_type.name}: {balances_count} balances exist")
                return False, f"Cannot delete leave type. {balances_count} employee leave balances exist for this leave type."

            # Check leave requests
            requests_count = session.query(LeaveRequest).filter_by(leave_type_id=leave_type_id).count()
            if requests_count > 0:
                app.logger.warning(f"Cannot delete leave type {leave_type.name}: {requests_count} requests exist")
                return False, f"Cannot delete leave type. {requests_count} leave requests exist for this leave type."

            # Safe to delete - no associated data
            session.delete(leave_type)
            session.commit()
            app.logger.info(f"Safely deleted leave type: {leave_type.name}")
            return True, None
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error deleting leave type: {e}")
            return False, str(e)
