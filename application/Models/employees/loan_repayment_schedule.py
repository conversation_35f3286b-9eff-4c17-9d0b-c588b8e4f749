from application.database import db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime, date
from flask import current_app
from decimal import Decimal

class LoanRepaymentSchedule(db.Model):
    """Model representing loan repayment schedule (stored in tenant database)."""
    __tablename__ = 'loan_repayment_schedule'

    schedule_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    loan_id = db.Column(UUID(as_uuid=True), db.Foreign<PERSON>ey('employee_loans.loan_id'), nullable=False)
    
    installment_number = db.Column(db.Integer, nullable=False)
    due_date = db.Column(db.Date, nullable=False)
    
    # Amounts
    principal_amount = db.Column(db.Numeric(15, 2), nullable=False, default=0)
    interest_amount = db.Column(db.Numeric(15, 2), nullable=False, default=0)
    amount = db.Column(db.Numeric(15, 2), nullable=False)  # Total installment amount
    
    # Payment Status
    status = db.Column(db.String(20), default='PENDING', nullable=False)  # 'PENDING', 'PAID', 'PARTIAL', 'OVERDUE'
    amount_paid = db.Column(db.Numeric(15, 2), default=0, nullable=False)
    payment_date = db.Column(db.Date, nullable=True)
    
    # Late Payment
    days_overdue = db.Column(db.Integer, default=0, nullable=False)
    late_fee = db.Column(db.Numeric(15, 2), default=0, nullable=False)
    
    # Payroll Integration
    payslip_id = db.Column(UUID(as_uuid=True), nullable=True)  # Link to payslip when paid via payroll
    
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    # Relationships
    loan = db.relationship('EmployeeLoan', backref=db.backref('repayment_schedule', lazy='dynamic', order_by='LoanRepaymentSchedule.installment_number'))

    # Unique constraint
    __table_args__ = (
        db.UniqueConstraint('loan_id', 'installment_number', name='uq_loan_installment'),
        db.CheckConstraint('amount > 0', name='chk_positive_amount'),
        db.CheckConstraint('amount_paid >= 0', name='chk_non_negative_paid'),
        db.CheckConstraint('days_overdue >= 0', name='chk_non_negative_overdue'),
    )

    def __str__(self):
        """Return a string representation of the object."""
        return f"LoanRepaymentSchedule [schedule_id={self.schedule_id}, loan_id={self.loan_id}, installment={self.installment_number}]"

    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "schedule_id": str(self.schedule_id),
            "loan_id": str(self.loan_id),
            "loan_number": self.loan.loan_number if self.loan else None,
            "installment_number": self.installment_number,
            "due_date": self.due_date.strftime('%Y-%m-%d') if self.due_date else None,
            "principal_amount": float(self.principal_amount),
            "interest_amount": float(self.interest_amount),
            "amount": float(self.amount),
            "status": self.status,
            "amount_paid": float(self.amount_paid),
            "amount_outstanding": float(self.amount - self.amount_paid),
            "payment_date": self.payment_date.strftime('%Y-%m-%d') if self.payment_date else None,
            "days_overdue": self.days_overdue,
            "late_fee": float(self.late_fee),
            "payslip_id": str(self.payslip_id) if self.payslip_id else None,
            "is_overdue": self.is_overdue(),
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

    @classmethod
    def create_schedule(cls, session, **kwargs):
        """Create a new repayment schedule entry."""
        try:
            schedule = cls(**kwargs)
            session.add(schedule)
            session.commit()
            current_app.logger.info(f"Created repayment schedule for loan {kwargs.get('loan_id')}, installment {kwargs.get('installment_number')}")
            return schedule
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error creating repayment schedule: {e}")
            return None

    def is_overdue(self, as_of_date=None):
        """Check if this installment is overdue."""
        if as_of_date is None:
            as_of_date = date.today()
        
        return (self.status in ['PENDING', 'PARTIAL'] and 
                self.due_date < as_of_date)

    def calculate_days_overdue(self, as_of_date=None):
        """Calculate number of days overdue."""
        if as_of_date is None:
            as_of_date = date.today()
        
        if self.is_overdue(as_of_date):
            return (as_of_date - self.due_date).days
        return 0

    def update_overdue_status(self, session, as_of_date=None):
        """Update overdue status and calculate late fees."""
        try:
            if as_of_date is None:
                as_of_date = date.today()
            
            if self.is_overdue(as_of_date):
                self.days_overdue = self.calculate_days_overdue(as_of_date)
                self.status = 'OVERDUE'
                
                # Calculate late fee if applicable
                # You can customize this logic based on your late fee policy
                if self.days_overdue > 7 and self.late_fee == 0:  # Grace period of 7 days
                    late_fee_rate = Decimal('0.01')  # 1% late fee
                    self.late_fee = self.amount * late_fee_rate
                
                session.commit()
                current_app.logger.info(f"Updated overdue status for schedule {self.schedule_id}")
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error updating overdue status: {e}")

    @classmethod
    def get_due_installments(cls, session, employee_id=None, due_date=None):
        """Get installments due for payment."""
        if due_date is None:
            due_date = date.today()
        
        query = session.query(cls).join(cls.loan).filter(
            cls.due_date <= due_date,
            cls.status.in_(['PENDING', 'PARTIAL'])
        )
        
        if employee_id:
            query = query.filter(cls.loan.has(employee_id=employee_id))
        
        return query.order_by(cls.due_date, cls.installment_number).all()

    @classmethod
    def get_overdue_installments(cls, session, employee_id=None, as_of_date=None):
        """Get overdue installments."""
        if as_of_date is None:
            as_of_date = date.today()
        
        query = session.query(cls).join(cls.loan).filter(
            cls.due_date < as_of_date,
            cls.status.in_(['PENDING', 'PARTIAL', 'OVERDUE'])
        )
        
        if employee_id:
            query = query.filter(cls.loan.has(employee_id=employee_id))
        
        return query.order_by(cls.due_date, cls.installment_number).all()

    @classmethod
    def get_schedule_for_loan(cls, session, loan_id):
        """Get complete repayment schedule for a loan."""
        return session.query(cls).filter_by(loan_id=loan_id).order_by(cls.installment_number).all()

    @classmethod
    def get_upcoming_installments(cls, session, employee_id=None, days_ahead=30):
        """Get installments due in the next N days."""
        from datetime import timedelta
        
        end_date = date.today() + timedelta(days=days_ahead)
        
        query = session.query(cls).join(cls.loan).filter(
            cls.due_date.between(date.today(), end_date),
            cls.status == 'PENDING'
        )
        
        if employee_id:
            query = query.filter(cls.loan.has(employee_id=employee_id))
        
        return query.order_by(cls.due_date, cls.installment_number).all()

    def mark_as_paid(self, session, payment_amount, payment_date=None, payslip_id=None):
        """Mark installment as paid."""
        try:
            if payment_date is None:
                payment_date = date.today()
            
            payment_amount = Decimal(str(payment_amount))
            self.amount_paid += payment_amount
            
            if self.amount_paid >= self.amount:
                self.status = 'PAID'
                self.payment_date = payment_date
            else:
                self.status = 'PARTIAL'
            
            if payslip_id:
                self.payslip_id = payslip_id
            
            session.commit()
            current_app.logger.info(f"Marked installment {self.installment_number} as paid: {payment_amount}")
            return True
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error marking installment as paid: {e}")
            return False

    @classmethod
    def update_all_overdue_statuses(cls, session, as_of_date=None):
        """Update overdue status for all pending installments."""
        try:
            if as_of_date is None:
                as_of_date = date.today()
            
            overdue_installments = session.query(cls).filter(
                cls.due_date < as_of_date,
                cls.status.in_(['PENDING', 'PARTIAL'])
            ).all()
            
            for installment in overdue_installments:
                installment.update_overdue_status(session, as_of_date)
            
            current_app.logger.info(f"Updated overdue status for {len(overdue_installments)} installments")
            
        except Exception as e:
            current_app.logger.error(f"Error updating overdue statuses: {e}")

    def get_payment_summary(self):
        """Get payment summary for this installment."""
        outstanding = self.amount - self.amount_paid
        
        return {
            "installment_number": self.installment_number,
            "due_date": self.due_date.strftime('%Y-%m-%d'),
            "total_amount": float(self.amount),
            "amount_paid": float(self.amount_paid),
            "outstanding_amount": float(outstanding),
            "late_fee": float(self.late_fee),
            "total_due": float(outstanding + self.late_fee),
            "status": self.status,
            "days_overdue": self.days_overdue,
            "is_overdue": self.is_overdue()
        }

    @classmethod
    def get_employee_payment_summary(cls, session, employee_id, as_of_date=None):
        """Get payment summary for all employee loans."""
        if as_of_date is None:
            as_of_date = date.today()
        
        installments = session.query(cls).join(cls.loan).filter(
            cls.loan.has(employee_id=employee_id),
            cls.status.in_(['PENDING', 'PARTIAL', 'OVERDUE'])
        ).order_by(cls.due_date).all()
        
        total_outstanding = Decimal('0')
        total_overdue = Decimal('0')
        total_late_fees = Decimal('0')
        
        summary = []
        
        for installment in installments:
            outstanding = installment.amount - installment.amount_paid
            total_outstanding += outstanding
            
            if installment.is_overdue(as_of_date):
                total_overdue += outstanding
                total_late_fees += installment.late_fee
            
            summary.append(installment.get_payment_summary())
        
        return {
            "employee_id": str(employee_id),
            "as_of_date": as_of_date.strftime('%Y-%m-%d'),
            "total_outstanding": float(total_outstanding),
            "total_overdue": float(total_overdue),
            "total_late_fees": float(total_late_fees),
            "total_due": float(total_outstanding + total_late_fees),
            "installments": summary
        }
