from application.database import db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime, date
from flask import current_app
from decimal import Decimal

class LoanTransaction(db.Model):
    """Model representing all loan transactions - disbursements and repayments (stored in tenant database)."""
    __tablename__ = 'loan_transactions'

    transaction_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    loan_id = db.Column(UUID(as_uuid=True), db.<PERSON><PERSON>ey('employee_loans.loan_id'), nullable=False)
    schedule_id = db.Column(UUID(as_uuid=True), db.ForeignKey('loan_repayment_schedule.schedule_id'), nullable=True)
    payslip_id = db.Column(UUID(as_uuid=True), nullable=True)  # Link to payslip for payroll deductions
    
    transaction_type = db.Column(db.String(20), nullable=False)  # 'DISBURSEMENT', 'REPAYMENT', 'ADJUSTMENT', 'REVERSAL'
    amount = db.Column(db.Numeric(15, 2), nullable=False)
    
    # Breakdown for repayments
    principal_portion = db.Column(db.Numeric(15, 2), default=0, nullable=False)
    interest_portion = db.Column(db.Numeric(15, 2), default=0, nullable=False)
    late_fee_portion = db.Column(db.Numeric(15, 2), default=0, nullable=False)
    
    transaction_date = db.Column(db.Date, nullable=False, default=date.today)
    reference_number = db.Column(db.String(100), nullable=True)
    description = db.Column(db.Text, nullable=True)
    
    # Processing Info
    processed_by = db.Column(UUID(as_uuid=True), nullable=True)  # User ID
    processing_method = db.Column(db.String(20), nullable=True)  # 'PAYROLL_DEDUCTION', 'MANUAL_PAYMENT', 'BANK_TRANSFER'
    
    # Status
    status = db.Column(db.String(20), default='COMPLETED', nullable=False)  # 'PENDING', 'COMPLETED', 'FAILED', 'REVERSED'
    
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    # Relationships
    loan = db.relationship('EmployeeLoan', backref=db.backref('transactions', lazy='dynamic', order_by='LoanTransaction.transaction_date.desc()'))
    schedule = db.relationship('LoanRepaymentSchedule', backref=db.backref('transactions', lazy='dynamic'))

    # Constraints
    __table_args__ = (
        db.CheckConstraint('amount > 0', name='chk_positive_amount'),
        db.CheckConstraint('principal_portion >= 0', name='chk_non_negative_principal'),
        db.CheckConstraint('interest_portion >= 0', name='chk_non_negative_interest'),
        db.CheckConstraint('late_fee_portion >= 0', name='chk_non_negative_late_fee'),
    )

    def __str__(self):
        """Return a string representation of the object."""
        return f"LoanTransaction [transaction_id={self.transaction_id}, type={self.transaction_type}, amount={self.amount}]"

    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "transaction_id": str(self.transaction_id),
            "loan_id": str(self.loan_id),
            "loan_number": self.loan.loan_number if self.loan else None,
            "schedule_id": str(self.schedule_id) if self.schedule_id else None,
            "installment_number": self.schedule.installment_number if self.schedule else None,
            "payslip_id": str(self.payslip_id) if self.payslip_id else None,
            "transaction_type": self.transaction_type,
            "amount": float(self.amount),
            "principal_portion": float(self.principal_portion),
            "interest_portion": float(self.interest_portion),
            "late_fee_portion": float(self.late_fee_portion),
            "transaction_date": self.transaction_date.strftime('%Y-%m-%d') if self.transaction_date else None,
            "reference_number": self.reference_number,
            "description": self.description,
            "processed_by": str(self.processed_by) if self.processed_by else None,
            "processing_method": self.processing_method,
            "status": self.status,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

    @classmethod
    def create_transaction(cls, session, **kwargs):
        """Create a new loan transaction."""
        try:
            transaction = cls(**kwargs)
            session.add(transaction)
            session.commit()
            current_app.logger.info(f"Created loan transaction: {transaction.transaction_type} - {transaction.amount}")
            return transaction
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error creating loan transaction: {e}")
            return None

    @classmethod
    def create_disbursement(cls, session, loan_id, amount, disbursement_date=None, 
                          processed_by=None, reference_number=None, **kwargs):
        """Create a disbursement transaction."""
        transaction_data = {
            'loan_id': loan_id,
            'transaction_type': 'DISBURSEMENT',
            'amount': amount,
            'transaction_date': disbursement_date or date.today(),
            'processed_by': processed_by,
            'reference_number': reference_number,
            'processing_method': 'BANK_TRANSFER',
            'description': f'Loan disbursement',
            **kwargs
        }
        
        return cls.create_transaction(session, **transaction_data)

    @classmethod
    def create_repayment(cls, session, loan_id, amount, schedule_id=None, payslip_id=None,
                        payment_date=None, processed_by=None, **kwargs):
        """Create a repayment transaction."""
        transaction_data = {
            'loan_id': loan_id,
            'schedule_id': schedule_id,
            'payslip_id': payslip_id,
            'transaction_type': 'REPAYMENT',
            'amount': amount,
            'transaction_date': payment_date or date.today(),
            'processed_by': processed_by,
            'processing_method': 'PAYROLL_DEDUCTION' if payslip_id else 'MANUAL_PAYMENT',
            'description': f'Loan repayment',
            **kwargs
        }
        
        return cls.create_transaction(session, **transaction_data)

    @classmethod
    def get_transactions_for_loan(cls, session, loan_id, transaction_type=None):
        """Get all transactions for a specific loan."""
        query = session.query(cls).filter_by(loan_id=loan_id)
        
        if transaction_type:
            query = query.filter_by(transaction_type=transaction_type)
        
        return query.order_by(cls.transaction_date.desc()).all()

    @classmethod
    def get_transactions_for_employee(cls, session, employee_id, start_date=None, end_date=None):
        """Get all loan transactions for an employee."""
        query = session.query(cls).join(cls.loan).filter(
            cls.loan.has(employee_id=employee_id)
        )
        
        if start_date:
            query = query.filter(cls.transaction_date >= start_date)
        
        if end_date:
            query = query.filter(cls.transaction_date <= end_date)
        
        return query.order_by(cls.transaction_date.desc()).all()

    @classmethod
    def get_payroll_deductions(cls, session, payroll_date=None, employee_id=None):
        """Get loan deductions for payroll processing."""
        if payroll_date is None:
            payroll_date = date.today()
        
        query = session.query(cls).filter(
            cls.transaction_type == 'REPAYMENT',
            cls.processing_method == 'PAYROLL_DEDUCTION',
            cls.transaction_date == payroll_date
        )
        
        if employee_id:
            query = query.join(cls.loan).filter(cls.loan.has(employee_id=employee_id))
        
        return query.all()

    @classmethod
    def get_transaction_summary(cls, session, loan_id):
        """Get transaction summary for a loan."""
        transactions = cls.get_transactions_for_loan(session, loan_id)
        
        total_disbursed = Decimal('0')
        total_repaid = Decimal('0')
        total_principal_repaid = Decimal('0')
        total_interest_repaid = Decimal('0')
        total_late_fees_repaid = Decimal('0')
        
        disbursements = []
        repayments = []
        
        for transaction in transactions:
            if transaction.transaction_type == 'DISBURSEMENT':
                total_disbursed += transaction.amount
                disbursements.append(transaction.to_dict())
            elif transaction.transaction_type == 'REPAYMENT':
                total_repaid += transaction.amount
                total_principal_repaid += transaction.principal_portion
                total_interest_repaid += transaction.interest_portion
                total_late_fees_repaid += transaction.late_fee_portion
                repayments.append(transaction.to_dict())
        
        return {
            "loan_id": str(loan_id),
            "summary": {
                "total_disbursed": float(total_disbursed),
                "total_repaid": float(total_repaid),
                "total_principal_repaid": float(total_principal_repaid),
                "total_interest_repaid": float(total_interest_repaid),
                "total_late_fees_repaid": float(total_late_fees_repaid),
                "outstanding_balance": float(total_disbursed - total_repaid)
            },
            "disbursements": disbursements,
            "repayments": repayments,
            "transaction_count": len(transactions)
        }

    def reverse_transaction(self, session, reversed_by, reason=None):
        """Reverse a transaction."""
        try:
            # Create reversal transaction
            reversal_data = {
                'loan_id': self.loan_id,
                'transaction_type': 'REVERSAL',
                'amount': self.amount,
                'transaction_date': date.today(),
                'processed_by': reversed_by,
                'reference_number': f'REV-{self.reference_number}' if self.reference_number else None,
                'description': f'Reversal of {self.transaction_type} - {reason or "No reason provided"}'
            }
            
            reversal = self.__class__.create_transaction(session, **reversal_data)
            
            if reversal:
                # Mark original transaction as reversed
                self.status = 'REVERSED'
                session.commit()
                
                current_app.logger.info(f"Reversed transaction {self.transaction_id}")
                return reversal
            
            return None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error reversing transaction: {e}")
            return None

    @classmethod
    def get_monthly_summary(cls, session, year, month, employee_id=None):
        """Get monthly transaction summary."""
        from calendar import monthrange
        
        start_date = date(year, month, 1)
        end_date = date(year, month, monthrange(year, month)[1])
        
        query = session.query(cls).filter(
            cls.transaction_date.between(start_date, end_date),
            cls.status == 'COMPLETED'
        )
        
        if employee_id:
            query = query.join(cls.loan).filter(cls.loan.has(employee_id=employee_id))
        
        transactions = query.all()
        
        disbursements = Decimal('0')
        repayments = Decimal('0')
        
        for transaction in transactions:
            if transaction.transaction_type == 'DISBURSEMENT':
                disbursements += transaction.amount
            elif transaction.transaction_type == 'REPAYMENT':
                repayments += transaction.amount
        
        return {
            "period": f"{year}-{month:02d}",
            "start_date": start_date.strftime('%Y-%m-%d'),
            "end_date": end_date.strftime('%Y-%m-%d'),
            "total_disbursements": float(disbursements),
            "total_repayments": float(repayments),
            "net_flow": float(repayments - disbursements),
            "transaction_count": len(transactions)
        }

    @classmethod
    def generate_reference_number(cls, transaction_type, loan_number=None):
        """Generate a reference number for the transaction."""
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        
        if transaction_type == 'DISBURSEMENT':
            prefix = 'DISB'
        elif transaction_type == 'REPAYMENT':
            prefix = 'REPAY'
        elif transaction_type == 'REVERSAL':
            prefix = 'REV'
        else:
            prefix = 'TXN'
        
        if loan_number:
            return f"{prefix}-{loan_number}-{timestamp}"
        else:
            return f"{prefix}-{timestamp}"
