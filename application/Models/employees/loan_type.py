from application.database import db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime
from flask import current_app
from decimal import Decimal

class LoanType(db.Model):
    """Model representing different types of loans/advances available to employees (stored in tenant database)."""
    __tablename__ = 'loan_types'

    loan_type_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    name = db.Column(db.String(255), nullable=False)  # 'Salary Advance', 'Personal Loan', etc.
    code = db.Column(db.String(50), nullable=False, unique=True)  # 'SALARY_ADV', 'PERSONAL', etc.
    description = db.Column(db.Text, nullable=True)
    
    # Loan Limits
    min_amount = db.Column(db.Numeric(15, 2), nullable=False, default=0)
    max_amount = db.Column(db.Numeric(15, 2), nullable=True)  # NULL = no limit
    max_salary_multiple = db.Column(db.Numeric(5, 2), nullable=True)  # e.g., 3.0 = 3x monthly salary
    
    # Interest & Terms
    interest_rate = db.Column(db.Numeric(5, 4), default=0, nullable=False)  # Annual rate, e.g., 0.12 = 12%
    interest_calculation_method = db.Column(db.String(20), default='SIMPLE', nullable=False)  # 'SIMPLE', 'COMPOUND'
    min_term_months = db.Column(db.Integer, default=1, nullable=False)
    max_term_months = db.Column(db.Integer, default=60, nullable=False)
    
    # Repayment Rules
    max_deduction_percentage = db.Column(db.Numeric(5, 2), default=0.33, nullable=False)  # Max 33% of salary
    allow_early_repayment = db.Column(db.Boolean, default=True, nullable=False)
    early_repayment_penalty_rate = db.Column(db.Numeric(5, 4), default=0, nullable=False)
    
    # Approval Rules
    requires_approval = db.Column(db.Boolean, default=True, nullable=False)
    auto_approve_limit = db.Column(db.Numeric(15, 2), nullable=True)  # Auto-approve below this amount
    approval_levels = db.Column(db.Integer, default=1, nullable=False)  # Number of approval levels required
    
    # Eligibility Rules
    min_employment_months = db.Column(db.Integer, default=6, nullable=False)  # Minimum employment period
    max_active_loans = db.Column(db.Integer, default=1, nullable=False)  # Max concurrent loans of this type
    requires_guarantor = db.Column(db.Boolean, default=False, nullable=False)
    
    # Status & Dates
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    def __str__(self):
        """Return a string representation of the object."""
        return f"LoanType [loan_type_id={self.loan_type_id}, name={self.name}, code={self.code}]"

    def to_dict(self, convert_decimals_to_float=True):
        """Dictionary representation of the object."""
        data = {
            "loan_type_id": str(self.loan_type_id),
            "name": self.name,
            "code": self.code,
            "description": self.description,
            "min_amount": self.min_amount,
            "max_amount": self.max_amount,
            "max_salary_multiple": self.max_salary_multiple,
            "interest_rate": self.interest_rate,
            "interest_calculation_method": self.interest_calculation_method,
            "min_term_months": self.min_term_months,
            "max_term_months": self.max_term_months,
            "max_deduction_percentage": self.max_deduction_percentage,
            "allow_early_repayment": self.allow_early_repayment,
            "early_repayment_penalty_rate": self.early_repayment_penalty_rate,
            "requires_approval": self.requires_approval,
            "auto_approve_limit": self.auto_approve_limit,
            "approval_levels": self.approval_levels,
            "min_employment_months": self.min_employment_months,
            "max_active_loans": self.max_active_loans,
            "requires_guarantor": self.requires_guarantor,
            "is_active": self.is_active,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

        # Convert Decimals to float only for JSON serialization if requested
        if convert_decimals_to_float:
            decimal_fields = ['min_amount', 'max_amount', 'max_salary_multiple', 'interest_rate',
                            'max_deduction_percentage', 'early_repayment_penalty_rate', 'auto_approve_limit']
            for field in decimal_fields:
                if data[field] is not None:
                    data[field] = float(data[field])

        return data

    @classmethod
    def create_loan_type(cls, session, **kwargs):
        """Create a new loan type."""
        try:
            loan_type = cls(**kwargs)
            session.add(loan_type)
            session.commit()
            current_app.logger.info(f"Created loan type: {loan_type.name}")
            return loan_type
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error creating loan type: {e}")
            return None

    @classmethod
    def get_active_loan_types(cls, session):
        """Get all active loan types."""
        return session.query(cls).filter_by(is_active=True).order_by(cls.name).all()

    @classmethod
    def get_loan_type_by_code(cls, session, code):
        """Get loan type by code."""
        return session.query(cls).filter_by(code=code, is_active=True).first()

    @classmethod
    def get_loan_type_by_id(cls, session, loan_type_id):
        """Get loan type by ID."""
        return session.query(cls).filter_by(loan_type_id=loan_type_id, is_active=True).first()

    def calculate_max_loan_amount(self, employee_salary):
        """Calculate maximum loan amount for an employee based on salary."""
        if not employee_salary:
            return Decimal('0')
        
        salary_based_max = None
        if self.max_salary_multiple:
            salary_based_max = Decimal(str(employee_salary)) * self.max_salary_multiple
        
        if self.max_amount and salary_based_max:
            return min(self.max_amount, salary_based_max)
        elif self.max_amount:
            return self.max_amount
        elif salary_based_max:
            return salary_based_max
        else:
            return Decimal(str(employee_salary)) * Decimal('3')  # Default to 3x salary

    def calculate_interest(self, principal, term_months):
        """Calculate total interest for a loan."""
        principal = Decimal(str(principal))
        term_years = Decimal(str(term_months)) / Decimal('12')
        
        if self.interest_calculation_method == 'SIMPLE':
            return principal * self.interest_rate * term_years
        elif self.interest_calculation_method == 'COMPOUND':
            # Monthly compounding
            monthly_rate = self.interest_rate / Decimal('12')
            return principal * ((Decimal('1') + monthly_rate) ** Decimal(str(term_months)) - Decimal('1'))
        else:
            return Decimal('0')

    def calculate_monthly_payment(self, principal, term_months):
        """Calculate monthly payment amount."""
        principal = Decimal(str(principal))
        total_interest = self.calculate_interest(principal, term_months)
        total_amount = principal + total_interest
        return total_amount / Decimal(str(term_months))

    def validate_loan_terms(self, amount, term_months, employee_salary=None):
        """Validate if loan terms are acceptable."""
        errors = []
        
        # Check amount limits
        if amount < self.min_amount:
            errors.append(f"Minimum loan amount is {self.min_amount}")
        
        max_allowed = self.calculate_max_loan_amount(employee_salary) if employee_salary else self.max_amount
        if max_allowed and amount > max_allowed:
            errors.append(f"Maximum loan amount is {max_allowed}")
        
        # Check term limits
        if term_months < self.min_term_months:
            errors.append(f"Minimum term is {self.min_term_months} months")
        
        if term_months > self.max_term_months:
            errors.append(f"Maximum term is {self.max_term_months} months")
        
        # Check repayment capacity
        if employee_salary:
            monthly_payment = self.calculate_monthly_payment(amount, term_months)
            max_deduction = Decimal(str(employee_salary)) * self.max_deduction_percentage
            if monthly_payment > max_deduction:
                errors.append(f"Monthly payment ({monthly_payment}) exceeds maximum allowed deduction ({max_deduction})")
        
        return len(errors) == 0, errors

    @classmethod
    def seed_default_loan_types(cls, session):
        """Create default loan types."""
        default_types = [
            {
                'name': 'Salary Advance',
                'code': 'SALARY_ADV',
                'description': 'Short-term salary advance for immediate cash flow needs',
                'max_salary_multiple': Decimal('0.5'),
                'interest_rate': Decimal('0'),
                'min_term_months': 1,
                'max_term_months': 3,
                'max_deduction_percentage': Decimal('0.5'),
                'auto_approve_limit': Decimal('100000'),
                'min_employment_months': 3
            },
            {
                'name': 'Personal Loan',
                'code': 'PERSONAL',
                'description': 'Personal loan for various financial needs',
                'max_salary_multiple': Decimal('6'),
                'interest_rate': Decimal('0.12'),
                'min_term_months': 6,
                'max_term_months': 60,
                'max_deduction_percentage': Decimal('0.33'),
                'approval_levels': 2,
                'min_employment_months': 12
            },
            {
                'name': 'Emergency Loan',
                'code': 'EMERGENCY',
                'description': 'Emergency loan for urgent financial needs',
                'max_salary_multiple': Decimal('3'),
                'interest_rate': Decimal('0.03'),
                'min_term_months': 3,
                'max_term_months': 24,
                'max_deduction_percentage': Decimal('0.4'),
                'auto_approve_limit': Decimal('200000'),
                'min_employment_months': 6
            },
            {
                'name': 'Educational Loan',
                'code': 'EDUCATION',
                'description': 'Loan for education, training, and professional development',
                'max_salary_multiple': Decimal('8'),
                'interest_rate': Decimal('0.02'),
                'min_term_months': 12,
                'max_term_months': 48,
                'max_deduction_percentage': Decimal('0.25'),
                'approval_levels': 2,
                'min_employment_months': 12
            }
        ]
        
        for loan_type_data in default_types:
            existing = cls.get_loan_type_by_code(session, loan_type_data['code'])
            if not existing:
                cls.create_loan_type(session, **loan_type_data)
