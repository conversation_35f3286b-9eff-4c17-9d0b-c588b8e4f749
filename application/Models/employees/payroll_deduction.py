from application.database import db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from flask import current_app

class PayrollDeduction(db.Model):
    """Model representing calculated deductions for each payslip (stored in tenant database)."""
    __tablename__ = 'payroll_deductions'

    deduction_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    payslip_id = db.Column(UUID(as_uuid=True), db.<PERSON>ey('payslips.payslip_id'), nullable=False)
    deduction_type_id = db.Column(UUID(as_uuid=True), nullable=False)  # Reference to central DB deduction_types
    deduction_name = db.Column(db.String(255), nullable=False)  # Snapshot of deduction name
    deduction_code = db.Column(db.String(50), nullable=False)   # Snapshot of deduction code
    calculation_base = db.Column(db.Numeric(15, 2), nullable=False)  # Amount used for calculation
    employee_rate_applied = db.Column(db.Numeric(5, 4), nullable=True)  # Employee rate used at time of calculation
    employer_rate_applied = db.Column(db.Numeric(5, 4), nullable=True)  # Employer rate used at time of calculation
    employee_amount = db.Column(db.Numeric(15, 2), default=0, nullable=False)  # Employee contribution amount
    employer_amount = db.Column(db.Numeric(15, 2), default=0, nullable=False)  # Employer contribution amount
    created_at = db.Column(db.DateTime, server_default=db.func.now())

    # Relationship
    payslip = db.relationship('Payslip', backref=db.backref('deductions', lazy='dynamic'))

    # Index for efficient lookups
    __table_args__ = (
        db.Index('idx_payroll_deduction_payslip', 'payslip_id'),
        db.Index('idx_payroll_deduction_type', 'deduction_type_id'),
    )

    def __str__(self):
        """Return a string representation of the object."""
        return f"PayrollDeduction [deduction_id={self.deduction_id}, type={self.deduction_name}, employee_amount={self.employee_amount}]"

    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "deduction_id": str(self.deduction_id),
            "payslip_id": str(self.payslip_id),
            "deduction_type_id": str(self.deduction_type_id),
            "deduction_name": self.deduction_name,
            "deduction_code": self.deduction_code,
            "calculation_base": float(self.calculation_base),
            "employee_rate_applied": float(self.employee_rate_applied) if self.employee_rate_applied else None,
            "employer_rate_applied": float(self.employer_rate_applied) if self.employer_rate_applied else None,
            "employee_rate_percentage": float(self.employee_rate_applied * 100) if self.employee_rate_applied else None,
            "employer_rate_percentage": float(self.employer_rate_applied * 100) if self.employer_rate_applied else None,
            "employee_amount": float(self.employee_amount),
            "employer_amount": float(self.employer_amount),
            "total_amount": float(self.employee_amount + self.employer_amount),
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None
        }

    @classmethod
    def create_deduction(cls, session, **kwargs):
        """Create a new payroll deduction."""
        try:
            deduction = cls(**kwargs)
            session.add(deduction)
            session.commit()
            current_app.logger.info(f"Created payroll deduction: {deduction.deduction_name} - Employee: {deduction.employee_amount}, Employer: {deduction.employer_amount}")
            return deduction
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error creating payroll deduction: {e}")
            return None

    @classmethod
    def get_deductions_for_payslip(cls, session, payslip_id):
        """Get all deductions for a specific payslip."""
        return session.query(cls).filter_by(payslip_id=payslip_id).order_by(cls.deduction_name).all()

    @classmethod
    def get_deduction_by_type(cls, session, payslip_id, deduction_type_id):
        """Get a specific deduction for a payslip by type."""
        return session.query(cls).filter_by(
            payslip_id=payslip_id,
            deduction_type_id=deduction_type_id
        ).first()

    @classmethod
    def create_deductions_for_payslip(cls, session, payslip_id, deductions_data):
        """Create multiple deductions for a payslip."""
        try:
            created_deductions = []
            
            for deduction_data in deductions_data:
                deduction_data['payslip_id'] = payslip_id
                deduction = cls.create_deduction(session, **deduction_data)
                if deduction:
                    created_deductions.append(deduction)
                else:
                    # If any deduction fails, rollback all
                    session.rollback()
                    return None

            session.commit()
            current_app.logger.info(f"Created {len(created_deductions)} deductions for payslip {payslip_id}")
            return created_deductions

        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error creating deductions for payslip: {e}")
            return None

    @classmethod
    def update_deduction(cls, session, deduction_id, **kwargs):
        """Update a payroll deduction."""
        try:
            deduction = session.query(cls).filter_by(deduction_id=deduction_id).first()
            if not deduction:
                return None

            for key, value in kwargs.items():
                if hasattr(deduction, key):
                    setattr(deduction, key, value)

            session.commit()
            current_app.logger.info(f"Updated payroll deduction: {deduction.deduction_id}")
            return deduction
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error updating payroll deduction: {e}")
            return None

    @classmethod
    def delete_deductions_for_payslip(cls, session, payslip_id):
        """Delete all deductions for a payslip."""
        try:
            deductions = session.query(cls).filter_by(payslip_id=payslip_id).all()
            
            for deduction in deductions:
                session.delete(deduction)

            session.commit()
            current_app.logger.info(f"Deleted {len(deductions)} deductions for payslip {payslip_id}")
            return True
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error deleting deductions for payslip: {e}")
            return False

    @classmethod
    def get_deduction_summary_by_type(cls, session, start_date, end_date):
        """Get deduction summary by type for a period."""
        from sqlalchemy import func
        from application.Models.employees.payslip import Payslip
        
        results = session.query(
            cls.deduction_code,
            cls.deduction_name,
            func.sum(cls.employee_amount).label('total_employee_amount'),
            func.sum(cls.employer_amount).label('total_employer_amount'),
            func.count(cls.deduction_id).label('count')
        ).join(
            Payslip, cls.payslip_id == Payslip.payslip_id
        ).filter(
            Payslip.pay_period_start >= start_date,
            Payslip.pay_period_end <= end_date,
            Payslip.status.in_(['APPROVED', 'PAID'])
        ).group_by(
            cls.deduction_code,
            cls.deduction_name
        ).all()

        summary = []
        for result in results:
            summary.append({
                'deduction_code': result.deduction_code,
                'deduction_name': result.deduction_name,
                'total_employee_amount': float(result.total_employee_amount or 0),
                'total_employer_amount': float(result.total_employer_amount or 0),
                'total_amount': float((result.total_employee_amount or 0) + (result.total_employer_amount or 0)),
                'count': result.count
            })

        return summary

    @classmethod
    def get_employee_deduction_history(cls, session, employee_id, deduction_type_id=None, limit=None):
        """Get deduction history for an employee."""
        from application.Models.employees.payslip import Payslip
        
        query = session.query(cls).join(
            Payslip, cls.payslip_id == Payslip.payslip_id
        ).filter(
            Payslip.employee_id == employee_id
        )
        
        if deduction_type_id:
            query = query.filter(cls.deduction_type_id == deduction_type_id)
        
        query = query.order_by(Payslip.pay_period_end.desc())
        
        if limit:
            query = query.limit(limit)
        
        return query.all()

    def calculate_total_contribution(self):
        """Calculate total contribution (employee + employer)."""
        return float(self.employee_amount + self.employer_amount)

    @classmethod
    def get_deductions_for_run(cls, session, run_id):
        """Get all deductions for a payroll run."""
        from application.Models.employees.payslip import Payslip
        
        return session.query(cls).join(
            Payslip, cls.payslip_id == Payslip.payslip_id
        ).filter(
            Payslip.run_id == run_id
        ).all()

    @classmethod
    def calculate_deduction_totals_for_run(cls, session, run_id):
        """Calculate deduction totals for a payroll run."""
        deductions = cls.get_deductions_for_run(session, run_id)
        
        totals = {
            'total_employee_deductions': 0,
            'total_employer_contributions': 0,
            'deduction_breakdown': {}
        }
        
        for deduction in deductions:
            totals['total_employee_deductions'] += float(deduction.employee_amount)
            totals['total_employer_contributions'] += float(deduction.employer_amount)
            
            # Breakdown by deduction type
            if deduction.deduction_code not in totals['deduction_breakdown']:
                totals['deduction_breakdown'][deduction.deduction_code] = {
                    'name': deduction.deduction_name,
                    'employee_total': 0,
                    'employer_total': 0
                }
            
            totals['deduction_breakdown'][deduction.deduction_code]['employee_total'] += float(deduction.employee_amount)
            totals['deduction_breakdown'][deduction.deduction_code]['employer_total'] += float(deduction.employer_amount)
        
        return totals
