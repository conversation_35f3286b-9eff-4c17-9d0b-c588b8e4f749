from application.database import db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime, date
from flask import current_app

class PayrollRun(db.Model):
    """Model representing payroll processing runs for each pay period (stored in tenant database)."""
    __tablename__ = 'payroll_runs'

    run_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    company_id = db.Column(db.String(36), nullable=False)  # Reference to central DB companies
    pay_period_start = db.Column(db.Date, nullable=False)
    pay_period_end = db.Column(db.Date, nullable=False)
    run_date = db.Column(db.Date, nullable=False, default=date.today)
    status = db.Column(db.String(20), default='DRAFT', nullable=False)  # DRAFT, PROCESSING, COMPLETED, APPROVED, CANCELLED
    total_employees = db.Column(db.Integer, default=0, nullable=False)
    total_gross_pay = db.Column(db.Numeric(15, 2), default=0, nullable=False)
    total_tax = db.Column(db.Numeric(15, 2), default=0, nullable=False)
    total_employee_deductions = db.Column(db.Numeric(15, 2), default=0, nullable=False)
    total_employer_contributions = db.Column(db.Numeric(15, 2), default=0, nullable=False)
    total_net_pay = db.Column(db.Numeric(15, 2), default=0, nullable=False)
    notes = db.Column(db.Text, nullable=True)
    created_by = db.Column(UUID(as_uuid=True), nullable=True)  # User who created the run
    approved_by = db.Column(UUID(as_uuid=True), nullable=True)  # User who approved the run
    approved_at = db.Column(db.DateTime, nullable=True)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    # Unique constraint: one payroll run per company per pay period
    __table_args__ = (
        db.UniqueConstraint('company_id', 'pay_period_start', 'pay_period_end', name='uq_payroll_run_period'),
        db.Index('idx_payroll_run_company_period', 'company_id', 'pay_period_start', 'pay_period_end'),
    )

    def __str__(self):
        """Return a string representation of the object."""
        return f"PayrollRun [run_id={self.run_id}, period={self.pay_period_start} to {self.pay_period_end}, status={self.status}]"

    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "run_id": str(self.run_id),
            "company_id": self.company_id,
            "pay_period_start": self.pay_period_start.strftime('%Y-%m-%d') if self.pay_period_start else None,
            "pay_period_end": self.pay_period_end.strftime('%Y-%m-%d') if self.pay_period_end else None,
            "run_date": self.run_date.strftime('%Y-%m-%d') if self.run_date else None,
            "status": self.status,
            "total_employees": self.total_employees,
            "total_gross_pay": float(self.total_gross_pay),
            "total_tax": float(self.total_tax),
            "total_employee_deductions": float(self.total_employee_deductions),
            "total_employer_contributions": float(self.total_employer_contributions),
            "total_net_pay": float(self.total_net_pay),
            "notes": self.notes,
            "created_by": str(self.created_by) if self.created_by else None,
            "approved_by": str(self.approved_by) if self.approved_by else None,
            "approved_at": self.approved_at.strftime('%Y-%m-%d %H:%M:%S') if self.approved_at else None,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

    @classmethod
    def create_payroll_run(cls, session, **kwargs):
        """Create a new payroll run."""
        try:
            payroll_run = cls(**kwargs)
            session.add(payroll_run)
            session.commit()
            current_app.logger.info(f"Created payroll run for period {payroll_run.pay_period_start} to {payroll_run.pay_period_end}")
            return payroll_run
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error creating payroll run: {e}")
            return None

    @classmethod
    def get_by_id(cls, session, run_id):
        """Get payroll run by ID."""
        return session.query(cls).filter_by(run_id=run_id).first()

    @classmethod
    def get_by_company_and_period(cls, session, company_id, pay_period_start, pay_period_end):
        """Get payroll run by company and pay period."""
        return session.query(cls).filter_by(
            company_id=company_id,
            pay_period_start=pay_period_start,
            pay_period_end=pay_period_end
        ).first()

    @classmethod
    def get_runs_for_company(cls, session, company_id, limit=None):
        """Get payroll runs for a company."""
        query = session.query(cls).filter_by(company_id=company_id).order_by(cls.pay_period_start.desc())
        
        if limit:
            query = query.limit(limit)
        
        return query.all()

    @classmethod
    def get_runs_by_status(cls, session, company_id, status):
        """Get payroll runs by status."""
        return session.query(cls).filter_by(company_id=company_id, status=status).order_by(cls.pay_period_start.desc()).all()

    @classmethod
    def update_status(cls, session, run_id, new_status, user_id=None):
        """Update payroll run status."""
        try:
            payroll_run = session.query(cls).filter_by(run_id=run_id).first()
            if not payroll_run:
                return None

            old_status = payroll_run.status
            payroll_run.status = new_status

            # Handle approval
            if new_status == 'APPROVED' and user_id:
                payroll_run.approved_by = user_id
                payroll_run.approved_at = datetime.now()

            session.commit()
            current_app.logger.info(f"Updated payroll run {run_id} status from {old_status} to {new_status}")
            return payroll_run
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error updating payroll run status: {e}")
            return None

    @classmethod
    def update_totals(cls, session, run_id, totals):
        """Update payroll run totals."""
        try:
            payroll_run = session.query(cls).filter_by(run_id=run_id).first()
            if not payroll_run:
                return None

            payroll_run.total_employees = totals.get('total_employees', 0)
            payroll_run.total_gross_pay = totals.get('total_gross_pay', 0)
            payroll_run.total_tax = totals.get('total_tax', 0)
            payroll_run.total_employee_deductions = totals.get('total_employee_deductions', 0)
            payroll_run.total_employer_contributions = totals.get('total_employer_contributions', 0)
            payroll_run.total_net_pay = totals.get('total_net_pay', 0)

            session.commit()
            current_app.logger.info(f"Updated payroll run {run_id} totals")
            return payroll_run
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error updating payroll run totals: {e}")
            return None

    @classmethod
    def delete_payroll_run(cls, session, run_id):
        """Delete a payroll run (only if in DRAFT status)."""
        try:
            payroll_run = session.query(cls).filter_by(run_id=run_id).first()
            if not payroll_run:
                return False

            if payroll_run.status != 'DRAFT':
                current_app.logger.error(f"Cannot delete payroll run {run_id} - status is {payroll_run.status}")
                return False

            # Delete associated payslips first
            from application.Models.employees.payslip import Payslip
            payslips = session.query(Payslip).filter_by(run_id=run_id).all()
            for payslip in payslips:
                session.delete(payslip)

            session.delete(payroll_run)
            session.commit()
            current_app.logger.info(f"Deleted payroll run {run_id}")
            return True
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error deleting payroll run: {e}")
            return False

    def can_be_modified(self):
        """Check if payroll run can be modified."""
        return self.status in ['DRAFT', 'PROCESSING']

    def can_be_approved(self):
        """Check if payroll run can be approved."""
        return self.status == 'COMPLETED'

    def can_be_cancelled(self):
        """Check if payroll run can be cancelled."""
        return self.status in ['DRAFT', 'PROCESSING', 'COMPLETED']

    @classmethod
    def get_latest_run_for_company(cls, session, company_id):
        """Get the latest payroll run for a company."""
        return session.query(cls).filter_by(company_id=company_id).order_by(cls.pay_period_end.desc()).first()

    @classmethod
    def get_runs_for_year(cls, session, company_id, year):
        """Get all payroll runs for a specific year."""
        start_date = date(year, 1, 1)
        end_date = date(year, 12, 31)
        
        return session.query(cls).filter(
            cls.company_id == company_id,
            cls.pay_period_start >= start_date,
            cls.pay_period_end <= end_date
        ).order_by(cls.pay_period_start).all()
