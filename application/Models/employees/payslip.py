from application.database import db
from sqlalchemy.dialects.postgresql import UUID, JSONB
import uuid
from datetime import datetime, date
from flask import current_app

class Payslip(db.Model):
    """Model representing individual employee payslips for each pay period (stored in tenant database)."""
    __tablename__ = 'payslips'

    payslip_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    run_id = db.Column(UUID(as_uuid=True), db.ForeignKey('payroll_runs.run_id'), nullable=False)
    employee_id = db.Column(UUID(as_uuid=True), db.ForeignKey('employees.employee_id'), nullable=False)
    pay_period_start = db.Column(db.Date, nullable=False)
    pay_period_end = db.Column(db.Date, nullable=False)
    employee_type_id = db.Column(UUID(as_uuid=True), nullable=False)  # Snapshot from calculation time
    basic_salary = db.Column(db.Numeric(15, 2), nullable=False)
    allowances = db.Column(JSONB, nullable=True)  # Breakdown of all allowances
    gross_pay = db.Column(db.Numeric(15, 2), nullable=False)
    tax_calculations = db.Column(JSONB, nullable=True)  # Detailed PAYE breakdown by bracket
    employee_deductions = db.Column(JSONB, nullable=True)  # All employee deductions breakdown
    employer_contributions = db.Column(JSONB, nullable=True)  # All employer contributions breakdown
    net_pay = db.Column(db.Numeric(15, 2), nullable=False)
    status = db.Column(db.String(20), default='DRAFT', nullable=False)  # DRAFT, APPROVED, PAID
    payment_date = db.Column(db.Date, nullable=True)
    payment_reference = db.Column(db.String(255), nullable=True)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    # Relationships
    payroll_run = db.relationship('PayrollRun', backref=db.backref('payslips', lazy='dynamic'))
    employee = db.relationship('Employee', backref=db.backref('payslips', lazy='dynamic'))

    # Unique constraint: one payslip per employee per payroll run
    __table_args__ = (
        db.UniqueConstraint('run_id', 'employee_id', name='uq_payslip_run_employee'),
        db.Index('idx_payslip_employee_period', 'employee_id', 'pay_period_start', 'pay_period_end'),
    )

    def __str__(self):
        """Return a string representation of the object."""
        return f"Payslip [payslip_id={self.payslip_id}, employee_id={self.employee_id}, net_pay={self.net_pay}]"

    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "payslip_id": str(self.payslip_id),
            "run_id": str(self.run_id),
            "employee_id": str(self.employee_id),
            "employee_name": f"{self.employee.first_name} {self.employee.last_name}" if self.employee else None,
            "employee_type_id": str(self.employee_type_id),
            "pay_period_start": self.pay_period_start.strftime('%Y-%m-%d') if self.pay_period_start else None,
            "pay_period_end": self.pay_period_end.strftime('%Y-%m-%d') if self.pay_period_end else None,
            "basic_salary": float(self.basic_salary),
            "allowances": self.allowances,
            "gross_pay": float(self.gross_pay),
            "tax_calculations": self.tax_calculations,
            "employee_deductions": self.employee_deductions,
            "employer_contributions": self.employer_contributions,
            "net_pay": float(self.net_pay),
            "status": self.status,
            "payment_date": self.payment_date.strftime('%Y-%m-%d') if self.payment_date else None,
            "payment_reference": self.payment_reference,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

    @classmethod
    def create_payslip(cls, session, **kwargs):
        """Create a new payslip."""
        try:
            payslip = cls(**kwargs)
            session.add(payslip)
            session.commit()
            current_app.logger.info(f"Created payslip for employee {payslip.employee_id}: net pay {payslip.net_pay}")
            return payslip
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error creating payslip: {e}")
            return None

    @classmethod
    def get_by_id(cls, session, payslip_id):
        """Get payslip by ID."""
        return session.query(cls).filter_by(payslip_id=payslip_id).first()

    @classmethod
    def get_by_run_and_employee(cls, session, run_id, employee_id):
        """Get payslip by run and employee."""
        return session.query(cls).filter_by(run_id=run_id, employee_id=employee_id).first()

    @classmethod
    def get_payslips_for_run(cls, session, run_id):
        """Get all payslips for a payroll run."""
        return session.query(cls).filter_by(run_id=run_id).order_by(cls.employee_id).all()

    @classmethod
    def get_payslips_for_employee(cls, session, employee_id, limit=None):
        """Get payslips for an employee."""
        query = session.query(cls).filter_by(employee_id=employee_id).order_by(cls.pay_period_end.desc())
        
        if limit:
            query = query.limit(limit)
        
        return query.all()

    @classmethod
    def get_payslips_for_period(cls, session, employee_id, start_date, end_date):
        """Get payslips for an employee within a date range."""
        return session.query(cls).filter(
            cls.employee_id == employee_id,
            cls.pay_period_start >= start_date,
            cls.pay_period_end <= end_date
        ).order_by(cls.pay_period_start).all()

    @classmethod
    def update_status(cls, session, payslip_id, new_status):
        """Update payslip status."""
        try:
            payslip = session.query(cls).filter_by(payslip_id=payslip_id).first()
            if not payslip:
                return None

            old_status = payslip.status
            payslip.status = new_status

            session.commit()
            current_app.logger.info(f"Updated payslip {payslip_id} status from {old_status} to {new_status}")
            return payslip
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error updating payslip status: {e}")
            return None

    @classmethod
    def mark_as_paid(cls, session, payslip_id, payment_date=None, payment_reference=None):
        """Mark payslip as paid."""
        try:
            payslip = session.query(cls).filter_by(payslip_id=payslip_id).first()
            if not payslip:
                return None

            payslip.status = 'PAID'
            payslip.payment_date = payment_date or date.today()
            payslip.payment_reference = payment_reference

            session.commit()
            current_app.logger.info(f"Marked payslip {payslip_id} as paid")
            return payslip
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error marking payslip as paid: {e}")
            return None

    @classmethod
    def delete_payslips_for_run(cls, session, run_id):
        """Delete all payslips for a payroll run."""
        try:
            payslips = session.query(cls).filter_by(run_id=run_id).all()
            
            for payslip in payslips:
                # Delete associated deductions first
                from application.Models.employees.payroll_deduction import PayrollDeduction
                deductions = session.query(PayrollDeduction).filter_by(payslip_id=payslip.payslip_id).all()
                for deduction in deductions:
                    session.delete(deduction)
                
                session.delete(payslip)

            session.commit()
            current_app.logger.info(f"Deleted {len(payslips)} payslips for run {run_id}")
            return True
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error deleting payslips for run: {e}")
            return False

    def get_total_tax(self):
        """Get total tax from tax calculations."""
        if not self.tax_calculations:
            return 0
        
        return sum(bracket.get('tax_amount', 0) for bracket in self.tax_calculations.get('brackets', []))

    def get_total_employee_deductions(self):
        """Get total employee deductions."""
        if not self.employee_deductions:
            return 0
        
        return sum(deduction.get('employee_amount', 0) for deduction in self.employee_deductions.values())

    def get_total_employer_contributions(self):
        """Get total employer contributions."""
        if not self.employer_contributions:
            return 0
        
        return sum(contribution.get('employer_amount', 0) for contribution in self.employer_contributions.values())

    def get_allowances_total(self):
        """Get total allowances."""
        if not self.allowances:
            return 0
        
        return sum(allowance.get('amount', 0) for allowance in self.allowances.values())

    @classmethod
    def get_payroll_summary_for_period(cls, session, start_date, end_date):
        """Get payroll summary for a period."""
        payslips = session.query(cls).filter(
            cls.pay_period_start >= start_date,
            cls.pay_period_end <= end_date,
            cls.status.in_(['APPROVED', 'PAID'])
        ).all()

        summary = {
            'total_employees': len(payslips),
            'total_gross_pay': sum(float(p.gross_pay) for p in payslips),
            'total_net_pay': sum(float(p.net_pay) for p in payslips),
            'total_tax': sum(p.get_total_tax() for p in payslips),
            'total_employee_deductions': sum(p.get_total_employee_deductions() for p in payslips),
            'total_employer_contributions': sum(p.get_total_employer_contributions() for p in payslips)
        }

        return summary
