from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUI<PERSON>
from flask import current_app as app
from datetime import datetime, date
from sqlalchemy import and_, or_


class ScheduleAssignment(db.Model):
    """Model representing individual shift assignments within a schedule."""
    __tablename__ = 'schedule_assignments'

    assignment_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    schedule_id = db.Column(UUID(as_uuid=True), db.Foreign<PERSON>ey('shift_schedules.schedule_id'), nullable=False)
    employee_id = db.Column(UUID(as_uuid=True), nullable=False)  # Reference to employee
    
    # Assignment details
    date = db.Column(db.Date, nullable=False)
    shift_id = db.Column(UUID(as_uuid=True), nullable=False)  # Reference to shift
    
    # Assignment metadata
    assignment_type = db.Column(db.String(50), default='scheduled', nullable=False)  # scheduled, overtime, swap, cover
    status = db.Column(db.String(50), default='assigned', nullable=False)  # assigned, confirmed, swapped, cancelled
    notes = db.Column(db.Text, nullable=True)
    
    # Tracking fields
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    # Relationships
    schedule = db.relationship('ShiftSchedule', backref='assignments', lazy='select')

    def __str__(self):
        """Return a string representation of the object."""
        return f"ScheduleAssignment [assignment_id={self.assignment_id}, employee_id={self.employee_id}, date={self.date}]"

    def to_dict(self, include_employee=False, include_shift=False, include_schedule=False):
        """Dictionary representation of the object."""
        data = {
            "assignment_id": str(self.assignment_id),
            "schedule_id": str(self.schedule_id),
            "employee_id": str(self.employee_id),
            "date": self.date.strftime('%Y-%m-%d') if self.date else None,
            "shift_id": str(self.shift_id),
            "assignment_type": self.assignment_type,
            "status": self.status,
            "notes": self.notes,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

        # These would be populated by the service layer with actual employee/shift data
        if include_employee:
            data['employee'] = {}  # Would be populated with employee details
        
        if include_shift:
            data['shift'] = {}  # Would be populated with shift details
            
        if include_schedule and self.schedule:
            data['schedule'] = self.schedule.to_dict()

        return data

    @classmethod
    def get_by_id(cls, session, assignment_id):
        """Get a schedule assignment by ID."""
        try:
            if isinstance(assignment_id, str):
                try:
                    assignment_id = uuid.UUID(assignment_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {assignment_id}")
                    return None

            assignment = session.query(cls).filter_by(assignment_id=assignment_id).first()
            return assignment
        except Exception as e:
            app.logger.error(f"Error getting schedule assignment by ID: {e}")
            return None

    @classmethod
    def get_by_schedule(cls, session, schedule_id, status=None):
        """Get all assignments for a specific schedule."""
        try:
            if isinstance(schedule_id, str):
                try:
                    schedule_id = uuid.UUID(schedule_id)
                except ValueError:
                    app.logger.error(f"Invalid schedule UUID format: {schedule_id}")
                    return []

            query = session.query(cls).filter_by(schedule_id=schedule_id)
            
            if status:
                if isinstance(status, list):
                    query = query.filter(cls.status.in_(status))
                else:
                    query = query.filter_by(status=status)
            
            assignments = query.order_by(cls.date, cls.employee_id).all()
            return assignments
        except Exception as e:
            app.logger.error(f"Error getting assignments for schedule: {e}")
            return []

    @classmethod
    def get_by_employee_date_range(cls, session, employee_id, start_date, end_date, status=None):
        """Get assignments for an employee within a date range."""
        try:
            if isinstance(employee_id, str):
                try:
                    employee_id = uuid.UUID(employee_id)
                except ValueError:
                    app.logger.error(f"Invalid employee UUID format: {employee_id}")
                    return []

            # Convert date strings if needed
            if isinstance(start_date, str):
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            if isinstance(end_date, str):
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

            query = session.query(cls).filter(
                cls.employee_id == employee_id,
                cls.date >= start_date,
                cls.date <= end_date
            )
            
            if status:
                if isinstance(status, list):
                    query = query.filter(cls.status.in_(status))
                else:
                    query = query.filter_by(status=status)
            
            assignments = query.order_by(cls.date).all()
            return assignments
        except Exception as e:
            app.logger.error(f"Error getting employee assignments for date range: {e}")
            return []

    @classmethod
    def get_by_date(cls, session, schedule_id, target_date, status=None):
        """Get all assignments for a specific date within a schedule."""
        try:
            if isinstance(schedule_id, str):
                try:
                    schedule_id = uuid.UUID(schedule_id)
                except ValueError:
                    app.logger.error(f"Invalid schedule UUID format: {schedule_id}")
                    return []

            if isinstance(target_date, str):
                target_date = datetime.strptime(target_date, '%Y-%m-%d').date()

            query = session.query(cls).filter_by(schedule_id=schedule_id, date=target_date)
            
            if status:
                if isinstance(status, list):
                    query = query.filter(cls.status.in_(status))
                else:
                    query = query.filter_by(status=status)
            
            assignments = query.order_by(cls.employee_id).all()
            return assignments
        except Exception as e:
            app.logger.error(f"Error getting assignments for date: {e}")
            return []

    @classmethod
    def bulk_create(cls, session, assignments_data):
        """Create multiple schedule assignments in bulk."""
        try:
            assignments = []
            
            for assignment_data in assignments_data:
                # Convert UUID fields if they're strings
                uuid_fields = ['schedule_id', 'employee_id', 'shift_id']
                for field in uuid_fields:
                    if field in assignment_data and isinstance(assignment_data[field], str):
                        try:
                            assignment_data[field] = uuid.UUID(assignment_data[field])
                        except ValueError:
                            app.logger.error(f"Invalid {field} UUID format: {assignment_data[field]}")
                            continue

                # Convert date field if it's a string
                if 'date' in assignment_data and isinstance(assignment_data['date'], str):
                    try:
                        assignment_data['date'] = datetime.strptime(assignment_data['date'], '%Y-%m-%d').date()
                    except ValueError:
                        app.logger.error(f"Invalid date format: {assignment_data['date']}")
                        continue

                assignment = cls(**assignment_data)
                assignments.append(assignment)

            # Add all assignments to session
            session.add_all(assignments)
            session.commit()
            
            app.logger.info(f"Created {len(assignments)} schedule assignments in bulk")
            return assignments
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating bulk schedule assignments: {e}")
            return []

    @classmethod
    def update_assignment(cls, session, assignment_id, **kwargs):
        """Update an existing schedule assignment."""
        try:
            assignment = cls.get_by_id(session, assignment_id)
            if not assignment:
                return None

            # Update fields
            updatable_fields = ['assignment_type', 'status', 'notes']
            
            for field in updatable_fields:
                if field in kwargs:
                    setattr(assignment, field, kwargs[field])

            session.commit()
            app.logger.info(f"Updated schedule assignment: {assignment.assignment_id}")
            return assignment
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error updating schedule assignment: {e}")
            return None

    @classmethod
    def cancel_assignment(cls, session, assignment_id, reason=None):
        """Cancel a schedule assignment."""
        try:
            assignment = cls.get_by_id(session, assignment_id)
            if not assignment:
                return False

            assignment.status = 'cancelled'
            if reason:
                assignment.notes = f"Cancelled: {reason}"

            session.commit()
            app.logger.info(f"Cancelled schedule assignment: {assignment.assignment_id}")
            return True
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error cancelling schedule assignment: {e}")
            return False

    @classmethod
    def swap_assignments(cls, session, assignment1_id, assignment2_id, reason=None):
        """Swap two schedule assignments between employees."""
        try:
            assignment1 = cls.get_by_id(session, assignment1_id)
            assignment2 = cls.get_by_id(session, assignment2_id)
            
            if not assignment1 or not assignment2:
                return False

            # Swap the employee assignments
            temp_employee_id = assignment1.employee_id
            assignment1.employee_id = assignment2.employee_id
            assignment2.employee_id = temp_employee_id
            
            # Update assignment type and notes
            assignment1.assignment_type = 'swap'
            assignment2.assignment_type = 'swap'
            
            if reason:
                swap_note = f"Swapped: {reason}"
                assignment1.notes = swap_note
                assignment2.notes = swap_note

            session.commit()
            app.logger.info(f"Swapped assignments: {assignment1_id} <-> {assignment2_id}")
            return True
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error swapping assignments: {e}")
            return False

    @classmethod
    def get_employee_workload(cls, session, employee_id, start_date, end_date):
        """Calculate workload statistics for an employee over a date range."""
        try:
            assignments = cls.get_by_employee_date_range(
                session, employee_id, start_date, end_date, status=['assigned', 'confirmed']
            )
            
            workload_stats = {
                "total_assignments": len(assignments),
                "working_days": len(assignments),
                "days_off": 0,  # Would need to calculate based on date range
                "assignment_types": {},
                "shift_distribution": {}
            }
            
            # Count assignment types
            for assignment in assignments:
                assignment_type = assignment.assignment_type
                workload_stats["assignment_types"][assignment_type] = \
                    workload_stats["assignment_types"].get(assignment_type, 0) + 1
            
            return workload_stats
        except Exception as e:
            app.logger.error(f"Error calculating employee workload: {e}")
            return None

    @classmethod
    def check_assignment_conflicts(cls, session, employee_id, target_date, shift_id):
        """Check if an employee has conflicting assignments on a specific date."""
        try:
            if isinstance(employee_id, str):
                employee_id = uuid.UUID(employee_id)
            if isinstance(shift_id, str):
                shift_id = uuid.UUID(shift_id)
            if isinstance(target_date, str):
                target_date = datetime.strptime(target_date, '%Y-%m-%d').date()

            # Check for existing assignments on the same date
            existing_assignments = session.query(cls).filter(
                cls.employee_id == employee_id,
                cls.date == target_date,
                cls.status.in_(['assigned', 'confirmed'])
            ).all()
            
            conflicts = []
            for assignment in existing_assignments:
                if assignment.shift_id != shift_id:  # Different shift on same day
                    conflicts.append({
                        "assignment_id": str(assignment.assignment_id),
                        "shift_id": str(assignment.shift_id),
                        "type": "different_shift_same_day"
                    })
            
            return conflicts
        except Exception as e:
            app.logger.error(f"Error checking assignment conflicts: {e}")
            return []

    def is_editable(self):
        """Check if this assignment can still be edited."""
        # Can edit if status is assigned and date is in the future
        return self.status == 'assigned' and self.date > date.today()

    def is_swappable(self):
        """Check if this assignment can be swapped."""
        # Can swap if status is assigned/confirmed and date is in the future
        return self.status in ['assigned', 'confirmed'] and self.date > date.today()

    @classmethod
    def get_coverage_for_date_shift(cls, session, schedule_id, target_date, shift_id):
        """Get coverage information for a specific date and shift."""
        try:
            if isinstance(schedule_id, str):
                schedule_id = uuid.UUID(schedule_id)
            if isinstance(shift_id, str):
                shift_id = uuid.UUID(shift_id)
            if isinstance(target_date, str):
                target_date = datetime.strptime(target_date, '%Y-%m-%d').date()

            assignments = session.query(cls).filter(
                cls.schedule_id == schedule_id,
                cls.date == target_date,
                cls.shift_id == shift_id,
                cls.status.in_(['assigned', 'confirmed'])
            ).all()
            
            return {
                "date": target_date.strftime('%Y-%m-%d'),
                "shift_id": str(shift_id),
                "assigned_count": len(assignments),
                "assignments": [assignment.to_dict() for assignment in assignments]
            }
        except Exception as e:
            app.logger.error(f"Error getting coverage for date/shift: {e}")
            return None
