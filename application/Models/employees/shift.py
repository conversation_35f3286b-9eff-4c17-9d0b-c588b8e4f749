from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUID
from flask import current_app as app
from datetime import datetime, time, timedelta

class Shift(db.Model):
    """Model representing a work shift."""
    __tablename__ = 'shifts'

    shift_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text, nullable=True)
    
    # Shift times
    start_time = db.Column(db.Time, nullable=False)
    end_time = db.Column(db.Time, nullable=False)
    
    # Grace periods (in minutes)
    grace_period_late = db.Column(db.Integer, default=15, nullable=False)  # Minutes allowed to be late
    grace_period_early = db.Column(db.Integer, default=15, nullable=False)  # Minutes allowed to leave early
    
    # Break details
    break_duration = db.Column(db.Integer, default=60, nullable=False)  # Break duration in minutes
    break_start_time = db.Column(db.Time, nullable=True)  # Optional fixed break start time
    
    # Shift type
    is_night_shift = db.Column(db.Boolean, default=False, nullable=False)
    is_flexible = db.Column(db.Boolean, default=False, nullable=False)  # Flexible hours or fixed
    
    # Working days (stored as a string of numbers, e.g., "1,2,3,4,5" for Mon-Fri)
    working_days = db.Column(db.String(20), default="1,2,3,4,5", nullable=False)
    
    # Company reference
    company_id = db.Column(db.String(36), nullable=False)  # Reference to company
    
    # Metadata
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    def __str__(self):
        """Return a string representation of the object."""
        return f"Shift [shift_id={self.shift_id}, name={self.name}, start_time={self.start_time}, end_time={self.end_time}]"

    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "shift_id": str(self.shift_id),
            "name": self.name,
            "description": self.description,
            "start_time": self.start_time.strftime('%H:%M') if self.start_time else None,
            "end_time": self.end_time.strftime('%H:%M') if self.end_time else None,
            "grace_period_late": self.grace_period_late,
            "grace_period_early": self.grace_period_early,
            "break_duration": self.break_duration,
            "break_start_time": self.break_start_time.strftime('%H:%M') if self.break_start_time else None,
            "is_night_shift": self.is_night_shift,
            "is_flexible": self.is_flexible,
            "working_days": self.working_days,
            "company_id": self.company_id,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

    @classmethod
    def get_shift_by_id(cls, session, shift_id):
        """Get a shift by ID."""
        try:
            # Convert string to UUID if needed
            if isinstance(shift_id, str):
                try:
                    shift_id = uuid.UUID(shift_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {shift_id}")
                    return None

            shift = session.query(cls).filter_by(shift_id=shift_id).first()
            return shift
        except Exception as e:
            app.logger.error(f"Error getting shift by ID: {e}")
            return None

    @classmethod
    def get_all_shifts(cls, session, company_id=None):
        """Get all shifts, optionally filtered by company."""
        try:
            query = session.query(cls)
            
            if company_id:
                query = query.filter_by(company_id=company_id)
                
            shifts = query.all()
            return shifts
        except Exception as e:
            app.logger.error(f"Error getting all shifts: {e}")
            return []

    @classmethod
    def create_shift(cls, session, **kwargs):
        """Create a new shift."""
        try:
            # Convert time strings to time objects if needed
            if 'start_time' in kwargs and isinstance(kwargs['start_time'], str):
                try:
                    kwargs['start_time'] = datetime.strptime(kwargs['start_time'], '%H:%M').time()
                except ValueError:
                    app.logger.error(f"Invalid start_time format: {kwargs['start_time']}")
                    return None

            if 'end_time' in kwargs and isinstance(kwargs['end_time'], str):
                try:
                    kwargs['end_time'] = datetime.strptime(kwargs['end_time'], '%H:%M').time()
                except ValueError:
                    app.logger.error(f"Invalid end_time format: {kwargs['end_time']}")
                    return None

            if 'break_start_time' in kwargs and kwargs['break_start_time'] and isinstance(kwargs['break_start_time'], str):
                try:
                    kwargs['break_start_time'] = datetime.strptime(kwargs['break_start_time'], '%H:%M').time()
                except ValueError:
                    app.logger.error(f"Invalid break_start_time format: {kwargs['break_start_time']}")
                    return None

            shift = cls(**kwargs)
            session.add(shift)
            session.commit()
            app.logger.info(f"Created new shift: {shift.name}")
            return shift
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating shift: {e}")
            return None

    @classmethod
    def update_shift(cls, session, shift_id, **kwargs):
        """Update a shift."""
        try:
            # Convert shift_id to UUID if it's a string
            if isinstance(shift_id, str):
                try:
                    shift_id = uuid.UUID(shift_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {shift_id}")
                    return None

            # Convert time strings to time objects if needed
            if 'start_time' in kwargs and isinstance(kwargs['start_time'], str):
                try:
                    kwargs['start_time'] = datetime.strptime(kwargs['start_time'], '%H:%M').time()
                except ValueError:
                    app.logger.error(f"Invalid start_time format: {kwargs['start_time']}")
                    return None

            if 'end_time' in kwargs and isinstance(kwargs['end_time'], str):
                try:
                    kwargs['end_time'] = datetime.strptime(kwargs['end_time'], '%H:%M').time()
                except ValueError:
                    app.logger.error(f"Invalid end_time format: {kwargs['end_time']}")
                    return None

            if 'break_start_time' in kwargs and kwargs['break_start_time'] and isinstance(kwargs['break_start_time'], str):
                try:
                    kwargs['break_start_time'] = datetime.strptime(kwargs['break_start_time'], '%H:%M').time()
                except ValueError:
                    app.logger.error(f"Invalid break_start_time format: {kwargs['break_start_time']}")
                    return None

            shift = session.query(cls).filter_by(shift_id=shift_id).first()
            if not shift:
                app.logger.warning(f"Shift with ID {shift_id} not found")
                return None

            # Update shift attributes
            for key, value in kwargs.items():
                if hasattr(shift, key):
                    setattr(shift, key, value)

            session.commit()
            app.logger.info(f"Updated shift: {shift.name}")
            return shift
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error updating shift: {e}")
            return None

    @classmethod
    def delete_shift(cls, session, shift_id):
        """Delete a shift."""
        try:
            # Convert shift_id to UUID if it's a string
            if isinstance(shift_id, str):
                try:
                    shift_id = uuid.UUID(shift_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {shift_id}")
                    return False

            shift = session.query(cls).filter_by(shift_id=shift_id).first()
            if not shift:
                app.logger.warning(f"Shift with ID {shift_id} not found")
                return False

            session.delete(shift)
            session.commit()
            app.logger.info(f"Deleted shift with ID: {shift_id}")
            return True
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error deleting shift: {e}")
            return False
