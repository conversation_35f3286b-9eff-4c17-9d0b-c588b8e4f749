from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUI<PERSON>, JSONB
from flask import current_app as app
from datetime import datetime, date, timedelta
import json


class ShiftSchedule(db.Model):
    """Model representing generated shift schedules for departments."""
    __tablename__ = 'shift_schedules'

    schedule_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    company_id = db.Column(db.String(36), nullable=False)  # Reference to company
    department_id = db.Column(UUID(as_uuid=True), nullable=True)  # Reference to department
    template_id = db.Column(UUID(as_uuid=True), db.<PERSON>ey('shift_templates.template_id'), nullable=True)
    
    # Schedule metadata
    schedule_name = db.Column(db.String(255), nullable=False)  # "ICU January 2024", "Emergency Q1 2024"
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    
    # Schedule status and workflow
    status = db.Column(db.String(50), default='draft', nullable=False)  # draft, draft_ready_for_review, draft_needs_attention, published, active, archived
    issues = db.Column(JSONB, nullable=True)  # Any issues found during generation
    coverage_analysis = db.Column(JSONB, nullable=True)  # Coverage statistics
    
    # Workflow tracking
    generated_by = db.Column(UUID(as_uuid=True), nullable=True)  # User who generated the schedule
    reviewed_by = db.Column(UUID(as_uuid=True), nullable=True)  # User who reviewed the schedule
    published_by = db.Column(UUID(as_uuid=True), nullable=True)  # User who published the schedule
    published_at = db.Column(db.DateTime, nullable=True)
    
    # Timestamps
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    # Relationships
    template = db.relationship('ShiftTemplate', backref='schedules', lazy='select')

    def __str__(self):
        """Return a string representation of the object."""
        return f"ShiftSchedule [schedule_id={self.schedule_id}, name={self.schedule_name}, status={self.status}]"

    def to_dict(self, include_template=False, include_assignments=False):
        """Dictionary representation of the object."""
        data = {
            "schedule_id": str(self.schedule_id),
            "company_id": self.company_id,
            "department_id": str(self.department_id) if self.department_id else None,
            "template_id": str(self.template_id) if self.template_id else None,
            "schedule_name": self.schedule_name,
            "start_date": self.start_date.strftime('%Y-%m-%d') if self.start_date else None,
            "end_date": self.end_date.strftime('%Y-%m-%d') if self.end_date else None,
            "status": self.status,
            "issues": self.issues,
            "coverage_analysis": self.coverage_analysis,
            "generated_by": str(self.generated_by) if self.generated_by else None,
            "reviewed_by": str(self.reviewed_by) if self.reviewed_by else None,
            "published_by": str(self.published_by) if self.published_by else None,
            "published_at": self.published_at.strftime('%Y-%m-%d %H:%M:%S') if self.published_at else None,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

        if include_template and self.template:
            data['template'] = self.template.to_dict()

        if include_assignments:
            # This would be populated by the service layer
            data['assignments'] = []

        return data

    @classmethod
    def get_by_id(cls, session, schedule_id):
        """Get a shift schedule by ID."""
        try:
            if isinstance(schedule_id, str):
                try:
                    schedule_id = uuid.UUID(schedule_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {schedule_id}")
                    return None

            schedule = session.query(cls).filter_by(schedule_id=schedule_id).first()
            return schedule
        except Exception as e:
            app.logger.error(f"Error getting shift schedule by ID: {e}")
            return None

    @classmethod
    def get_by_company(cls, session, company_id, status=None, limit=50, offset=0):
        """Get shift schedules for a company with optional filtering."""
        try:
            query = session.query(cls).filter_by(company_id=company_id)
            
            if status:
                if isinstance(status, list):
                    query = query.filter(cls.status.in_(status))
                else:
                    query = query.filter_by(status=status)
            
            schedules = query.order_by(cls.created_at.desc()).offset(offset).limit(limit).all()
            return schedules
        except Exception as e:
            app.logger.error(f"Error getting shift schedules for company: {e}")
            return []

    @classmethod
    def get_by_department(cls, session, company_id, department_id, status=None):
        """Get shift schedules for a specific department."""
        try:
            if isinstance(department_id, str):
                try:
                    department_id = uuid.UUID(department_id)
                except ValueError:
                    app.logger.error(f"Invalid department UUID format: {department_id}")
                    return []

            query = session.query(cls).filter_by(company_id=company_id, department_id=department_id)
            
            if status:
                if isinstance(status, list):
                    query = query.filter(cls.status.in_(status))
                else:
                    query = query.filter_by(status=status)
            
            schedules = query.order_by(cls.created_at.desc()).all()
            return schedules
        except Exception as e:
            app.logger.error(f"Error getting shift schedules for department: {e}")
            return []

    @classmethod
    def get_pending_review(cls, session, company_id):
        """Get schedules that need HR review."""
        try:
            schedules = session.query(cls).filter_by(
                company_id=company_id
            ).filter(
                cls.status.in_(['draft_ready_for_review', 'draft_needs_attention'])
            ).order_by(cls.created_at.asc()).all()
            
            return schedules
        except Exception as e:
            app.logger.error(f"Error getting pending review schedules: {e}")
            return []

    @classmethod
    def create_schedule(cls, session, **kwargs):
        """Create a new shift schedule."""
        try:
            # Convert UUID fields if they're strings
            uuid_fields = ['department_id', 'template_id', 'generated_by']
            for field in uuid_fields:
                if field in kwargs and kwargs[field] and isinstance(kwargs[field], str):
                    try:
                        kwargs[field] = uuid.UUID(kwargs[field])
                    except ValueError:
                        app.logger.error(f"Invalid {field} UUID format: {kwargs[field]}")
                        return None

            # Convert date fields if they're strings
            date_fields = ['start_date', 'end_date']
            for field in date_fields:
                if field in kwargs and isinstance(kwargs[field], str):
                    try:
                        kwargs[field] = datetime.strptime(kwargs[field], '%Y-%m-%d').date()
                    except ValueError:
                        app.logger.error(f"Invalid {field} date format: {kwargs[field]}")
                        return None

            schedule = cls(**kwargs)
            session.add(schedule)
            session.commit()
            app.logger.info(f"Created new shift schedule: {schedule.schedule_name}")
            return schedule
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating shift schedule: {e}")
            return None

    @classmethod
    def update_schedule(cls, session, schedule_id, **kwargs):
        """Update an existing shift schedule."""
        try:
            schedule = cls.get_by_id(session, schedule_id)
            if not schedule:
                return None

            # Update fields
            updatable_fields = ['schedule_name', 'start_date', 'end_date', 'status', 'issues', 
                              'coverage_analysis', 'reviewed_by', 'published_by', 'published_at']
            
            for field in updatable_fields:
                if field in kwargs:
                    value = kwargs[field]
                    
                    # Handle UUID fields
                    if field in ['reviewed_by', 'published_by'] and value and isinstance(value, str):
                        try:
                            value = uuid.UUID(value)
                        except ValueError:
                            app.logger.error(f"Invalid {field} UUID format: {value}")
                            continue
                    
                    # Handle date fields
                    if field in ['start_date', 'end_date'] and isinstance(value, str):
                        try:
                            value = datetime.strptime(value, '%Y-%m-%d').date()
                        except ValueError:
                            app.logger.error(f"Invalid {field} date format: {value}")
                            continue
                    
                    # Handle datetime fields
                    if field == 'published_at' and isinstance(value, str):
                        try:
                            value = datetime.strptime(value, '%Y-%m-%d %H:%M:%S')
                        except ValueError:
                            app.logger.error(f"Invalid {field} datetime format: {value}")
                            continue
                    
                    setattr(schedule, field, value)

            session.commit()
            app.logger.info(f"Updated shift schedule: {schedule.schedule_name}")
            return schedule
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error updating shift schedule: {e}")
            return None

    def publish_schedule(self, session, published_by):
        """Publish a schedule (make it active)."""
        try:
            if isinstance(published_by, str):
                published_by = uuid.UUID(published_by)

            self.status = 'published'
            self.published_by = published_by
            self.published_at = datetime.now()
            
            session.commit()
            app.logger.info(f"Published shift schedule: {self.schedule_name}")
            return True
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error publishing shift schedule: {e}")
            return False

    def archive_schedule(self, session):
        """Archive a schedule."""
        try:
            self.status = 'archived'
            session.commit()
            app.logger.info(f"Archived shift schedule: {self.schedule_name}")
            return True
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error archiving shift schedule: {e}")
            return False

    def get_duration_weeks(self):
        """Calculate the duration of the schedule in weeks."""
        if self.start_date and self.end_date:
            delta = self.end_date - self.start_date
            return (delta.days + 1) // 7
        return 0

    def get_date_range(self):
        """Get list of all dates in the schedule."""
        if not self.start_date or not self.end_date:
            return []
        
        dates = []
        current_date = self.start_date
        while current_date <= self.end_date:
            dates.append(current_date)
            current_date += timedelta(days=1)
        
        return dates

    def is_editable(self):
        """Check if the schedule can still be edited."""
        return self.status in ['draft', 'draft_ready_for_review', 'draft_needs_attention']

    def is_active(self):
        """Check if the schedule is currently active."""
        if self.status != 'published':
            return False
        
        today = date.today()
        return self.start_date <= today <= self.end_date

    @classmethod
    def get_active_schedules(cls, session, company_id, target_date=None):
        """Get schedules that are active on a specific date."""
        try:
            if not target_date:
                target_date = date.today()

            schedules = session.query(cls).filter_by(
                company_id=company_id,
                status='published'
            ).filter(
                cls.start_date <= target_date,
                cls.end_date >= target_date
            ).all()
            
            return schedules
        except Exception as e:
            app.logger.error(f"Error getting active schedules: {e}")
            return []

    def calculate_coverage_stats(self, assignments):
        """Calculate coverage statistics for this schedule."""
        try:
            # This would be implemented by the service layer
            # with access to assignments and staffing requirements
            stats = {
                "total_shifts": 0,
                "assigned_shifts": 0,
                "coverage_percentage": 0,
                "understaffed_shifts": [],
                "overstaffed_shifts": []
            }
            
            # Update the schedule with calculated stats
            self.coverage_analysis = stats
            return stats
        except Exception as e:
            app.logger.error(f"Error calculating coverage stats: {e}")
            return None
