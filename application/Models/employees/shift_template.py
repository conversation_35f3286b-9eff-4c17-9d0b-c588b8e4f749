from application.database import db
import uuid
from sqlalchemy.dialects.postgresql import UUID, JSONB
from flask import current_app as app
from datetime import datetime, date
import json


class ShiftTemplate(db.Model):
    """Model representing shift rotation templates for hospital scheduling."""
    __tablename__ = 'shift_templates'

    template_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    company_id = db.Column(db.String(36), nullable=False)  # Reference to company
    name = db.Column(db.String(255), nullable=False)  # "4-on-2-off Rotation", "Weekly Day-Evening-Night"
    description = db.Column(db.Text, nullable=True)
    
    # Business requirements - completely flexible
    business_requirements = db.Column(JSONB, nullable=True)  # Pure business data - no constraints

    # Legacy fields - kept for backward compatibility but made optional
    pattern_type = db.Column(db.String(50), nullable=True, default='business_driven')
    rotation_cycle = db.Column(db.Integer, nullable=True, default=1)
    pattern_data = db.Column(JSONB, nullable=True, default=lambda: {"type": "business_requirements"})
    department_ids = db.Column(JSONB, nullable=True)
    coverage_requirements = db.Column(JSONB, nullable=True)
    employee_group_size = db.Column(db.Integer, nullable=True)  # DEPRECATED
    
    # Status and metadata
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_by = db.Column(UUID(as_uuid=True), nullable=True)  # User who created this template
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    def __str__(self):
        """Return a string representation of the object."""
        return f"ShiftTemplate [template_id={self.template_id}, name={self.name}, company_id={self.company_id}]"

    def to_dict(self, include_legacy=True):
        """Dictionary representation of the object."""
        result = {
            "template_id": str(self.template_id),
            "company_id": self.company_id,
            "name": self.name,
            "description": self.description,
            "business_requirements": self.business_requirements,  # Primary data
            "is_active": self.is_active,
            "created_by": str(self.created_by) if self.created_by else None,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

        # Include legacy fields only if requested (for backward compatibility)
        if include_legacy:
            result.update({
                "pattern_type": self.pattern_type,
                "rotation_cycle": self.rotation_cycle,
                "pattern_data": self.pattern_data,
                "department_ids": self.department_ids,
                "coverage_requirements": self.coverage_requirements,
                "employee_group_size": self.employee_group_size
            })

        return result

    def validate_business_requirements_structure(self):
        """Validate that business requirements follow expected schema."""
        if not self.business_requirements:
            return False, "No business requirements defined"

        try:
            from application.Services.business_requirements_processor import BusinessRequirementsProcessor
            return BusinessRequirementsProcessor.validate_schema(self.business_requirements)
        except Exception as e:
            app.logger.error(f"Error validating business requirements structure: {e}")
            return False, f"Validation error: {str(e)}"

    def get_total_staff_needed_per_day(self):
        """Calculate total staff needed per day from business requirements."""
        if not self.business_requirements:
            return {}

        try:
            daily_totals = {}
            shifts = self.business_requirements.get('shifts', [])

            # Initialize all days
            for day in range(1, 8):  # Monday=1 to Sunday=7
                daily_totals[day] = {
                    "min_staff": 0,
                    "preferred_staff": 0,
                    "max_staff": 0,
                    "shifts": []
                }

            # Calculate totals for each day
            for shift in shifts:
                days_of_week = shift.get('days_of_week', [1, 2, 3, 4, 5, 6, 7])
                staffing = shift['staffing']

                for day in days_of_week:
                    daily_totals[day]["min_staff"] += staffing['minimum_staff']
                    daily_totals[day]["preferred_staff"] += staffing['preferred_staff']
                    daily_totals[day]["max_staff"] += staffing.get('maximum_staff', staffing['preferred_staff'] + 2)
                    daily_totals[day]["shifts"].append({
                        "shift_name": shift['shift_name'],
                        "time_range": shift['time_range'],
                        "staff_needed": staffing['preferred_staff']
                    })

            return daily_totals

        except Exception as e:
            app.logger.error(f"Error calculating daily staff totals: {e}")
            return {}

    def get_staffing_summary(self):
        """Get comprehensive staffing summary from business requirements."""
        if not self.business_requirements:
            return {}

        try:
            from application.Services.business_requirements_processor import BusinessRequirementsProcessor
            return BusinessRequirementsProcessor.get_staffing_summary(self.business_requirements)
        except Exception as e:
            app.logger.error(f"Error getting staffing summary: {e}")
            return {}

    def get_work_rules_summary(self):
        """Extract and summarize work rules from business requirements."""
        if not self.business_requirements:
            return {}

        work_rules = self.business_requirements.get('work_rules', {})
        return {
            "max_consecutive_days": work_rules.get('max_consecutive_days', 'Not specified'),
            "min_rest_days": work_rules.get('min_rest_days', 'Not specified'),
            "max_hours_per_week": work_rules.get('max_hours_per_week', 'Not specified'),
            "max_hours_per_day": work_rules.get('max_hours_per_day', 'Not specified'),
            "overtime_allowed": work_rules.get('overtime_allowed', False),
            "weekend_requirements": work_rules.get('weekend_requirements', 'flexible')
        }

    def calculate_weekly_coverage_hours(self):
        """Calculate total coverage hours needed per week."""
        if not self.business_requirements:
            return 0

        try:
            total_hours = 0
            shifts = self.business_requirements.get('shifts', [])

            for shift in shifts:
                # Calculate shift duration
                start_time = datetime.strptime(shift['time_range']['start_time'], '%H:%M').time()
                end_time = datetime.strptime(shift['time_range']['end_time'], '%H:%M').time()

                # Handle overnight shifts
                if end_time < start_time:
                    # Overnight shift
                    shift_hours = (24 - start_time.hour + end_time.hour) + (end_time.minute - start_time.minute) / 60
                else:
                    # Regular shift
                    shift_hours = (end_time.hour - start_time.hour) + (end_time.minute - start_time.minute) / 60

                # Calculate total hours for this shift per week
                days_per_week = len(shift.get('days_of_week', [1, 2, 3, 4, 5, 6, 7]))
                staff_needed = shift['staffing']['preferred_staff']

                total_hours += shift_hours * days_per_week * staff_needed

            return round(total_hours, 2)

        except Exception as e:
            app.logger.error(f"Error calculating weekly coverage hours: {e}")
            return 0

    def is_24_7_coverage(self):
        """Check if this template provides 24/7 coverage."""
        if not self.business_requirements:
            return False

        try:
            shifts = self.business_requirements.get('shifts', [])

            # Check if we have coverage for all 7 days
            covered_days = set()
            for shift in shifts:
                days_of_week = shift.get('days_of_week', [])
                covered_days.update(days_of_week)

            if len(covered_days) < 7:
                return False

            # Check if we have 24-hour coverage
            # This is a simplified check - could be more sophisticated
            has_night_coverage = False
            has_day_coverage = False

            for shift in shifts:
                start_time = datetime.strptime(shift['time_range']['start_time'], '%H:%M').time()
                end_time = datetime.strptime(shift['time_range']['end_time'], '%H:%M').time()

                # Check for night coverage (covers midnight)
                if end_time < start_time or start_time.hour >= 22 or end_time.hour <= 6:
                    has_night_coverage = True

                # Check for day coverage
                if start_time.hour <= 12 and end_time.hour >= 12:
                    has_day_coverage = True

            return has_night_coverage and has_day_coverage

        except Exception as e:
            app.logger.error(f"Error checking 24/7 coverage: {e}")
            return False

    @classmethod
    def get_by_id(cls, session, template_id):
        """Get a shift template by ID."""
        try:
            if isinstance(template_id, str):
                try:
                    template_id = uuid.UUID(template_id)
                except ValueError:
                    app.logger.error(f"Invalid UUID format: {template_id}")
                    return None

            template = session.query(cls).filter_by(template_id=template_id).first()
            return template
        except Exception as e:
            app.logger.error(f"Error getting shift template by ID: {e}")
            return None

    @classmethod
    def get_by_company(cls, session, company_id, active_only=True):
        """Get all shift templates for a company."""
        try:
            app.logger.info(f"Querying shift templates for company_id: {company_id}, active_only: {active_only}")
            query = session.query(cls).filter_by(company_id=company_id)

            if active_only:
                query = query.filter_by(is_active=True)

            templates = query.order_by(cls.name).all()
            app.logger.info(f"Query returned {len(templates)} templates")

            # Debug: Log first few templates if any
            for i, template in enumerate(templates[:3]):
                app.logger.info(f"Template {i+1}: {template.name}, company_id: {template.company_id}, is_active: {template.is_active}")

            return templates
        except Exception as e:
            app.logger.error(f"Error getting shift templates for company: {e}")
            return []

    @classmethod
    def get_by_department(cls, session, company_id, department_id, active_only=True):
        """Get shift templates available for a specific department."""
        try:
            query = session.query(cls).filter_by(company_id=company_id)
            
            if active_only:
                query = query.filter_by(is_active=True)
            
            # Filter by department_ids JSON array
            query = query.filter(
                db.or_(
                    cls.department_ids.is_(None),  # Available to all departments
                    cls.department_ids.contains([str(department_id)])  # Specific to this department
                )
            )
            
            templates = query.order_by(cls.name).all()
            return templates
        except Exception as e:
            app.logger.error(f"Error getting shift templates for department: {e}")
            return []

    @classmethod
    def create_template(cls, session, **kwargs):
        """Create a new shift template."""
        try:
            # Convert created_by to UUID if it's a string
            if 'created_by' in kwargs and isinstance(kwargs['created_by'], str):
                try:
                    kwargs['created_by'] = uuid.UUID(kwargs['created_by'])
                except ValueError:
                    app.logger.error(f"Invalid created_by UUID format: {kwargs['created_by']}")
                    return None

            # Validate pattern_data is valid JSON
            if 'pattern_data' in kwargs:
                if isinstance(kwargs['pattern_data'], str):
                    try:
                        kwargs['pattern_data'] = json.loads(kwargs['pattern_data'])
                    except json.JSONDecodeError:
                        app.logger.error("Invalid JSON in pattern_data")
                        return None

            # Validate department_ids is valid JSON array
            if 'department_ids' in kwargs and kwargs['department_ids']:
                if isinstance(kwargs['department_ids'], str):
                    try:
                        kwargs['department_ids'] = json.loads(kwargs['department_ids'])
                    except json.JSONDecodeError:
                        app.logger.error("Invalid JSON in department_ids")
                        return None

            template = cls(**kwargs)
            session.add(template)
            session.commit()
            app.logger.info(f"Created new shift template: {template.name}")
            return template
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error creating shift template: {e}")
            return None

    @classmethod
    def update_template(cls, session, template_id, **kwargs):
        """Update an existing shift template."""
        try:
            template = cls.get_by_id(session, template_id)
            if not template:
                return None

            # Update fields
            for field in ['name', 'description', 'pattern_type', 'rotation_cycle', 
                         'pattern_data', 'department_ids', 'employee_group_size', 'is_active']:
                if field in kwargs:
                    if field == 'pattern_data' and isinstance(kwargs[field], str):
                        try:
                            kwargs[field] = json.loads(kwargs[field])
                        except json.JSONDecodeError:
                            app.logger.error("Invalid JSON in pattern_data")
                            continue
                    
                    if field == 'department_ids' and isinstance(kwargs[field], str):
                        try:
                            kwargs[field] = json.loads(kwargs[field])
                        except json.JSONDecodeError:
                            app.logger.error("Invalid JSON in department_ids")
                            continue
                    
                    setattr(template, field, kwargs[field])

            session.commit()
            app.logger.info(f"Updated shift template: {template.name}")
            return template
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error updating shift template: {e}")
            return None

    @classmethod
    def delete_template(cls, session, template_id):
        """Soft delete a shift template (mark as inactive)."""
        try:
            template = cls.get_by_id(session, template_id)
            if not template:
                return False

            template.is_active = False
            session.commit()
            app.logger.info(f"Deleted shift template: {template.name}")
            return True
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error deleting shift template: {e}")
            return False

    def validate_pattern_data(self):
        """Validate that pattern_data has required structure."""
        try:
            required_fields = ['cycle_length', 'pattern']
            
            for field in required_fields:
                if field not in self.pattern_data:
                    return False, f"Missing required field: {field}"
            
            # Validate pattern structure
            pattern = self.pattern_data['pattern']
            if not isinstance(pattern, list):
                return False, "Pattern must be a list"
            
            cycle_length = self.pattern_data['cycle_length']
            if len(pattern) != cycle_length:
                return False, f"Pattern length ({len(pattern)}) doesn't match cycle_length ({cycle_length})"
            
            return True, "Valid pattern data"
        except Exception as e:
            return False, f"Error validating pattern data: {str(e)}"

    def get_pattern_for_week(self, week_number):
        """Get the shift pattern for a specific week in the rotation."""
        try:
            cycle_length = self.pattern_data['cycle_length']
            pattern = self.pattern_data['pattern']
            
            # Calculate which week in the cycle
            cycle_week = week_number % cycle_length
            
            if cycle_week < len(pattern):
                return pattern[cycle_week]
            else:
                return None
        except Exception as e:
            app.logger.error(f"Error getting pattern for week {week_number}: {e}")
            return None

    @classmethod
    def create_default_templates(cls, session, company_id):
        """Create default shift templates for a new company."""
        default_templates = [
            {
                "name": "4-on-2-off Day Shift",
                "description": "4 consecutive days on day shift, then 2 days off",
                "pattern_type": "weekly",
                "rotation_cycle": 6,
                "pattern_data": {
                    "cycle_length": 6,
                    "pattern": [
                        {"week": 1, "shifts": ["day", "day", "day", "day", "off", "off", "off"]},
                        {"week": 2, "shifts": ["off", "off", "day", "day", "day", "day", "off"]},
                        {"week": 3, "shifts": ["day", "off", "off", "day", "day", "day", "day"]},
                        {"week": 4, "shifts": ["day", "day", "off", "off", "day", "day", "day"]},
                        {"week": 5, "shifts": ["day", "day", "day", "off", "off", "day", "day"]},
                        {"week": 6, "shifts": ["off", "day", "day", "day", "off", "off", "day"]}
                    ],
                    "shift_definitions": {
                        "day": {"start": "07:00", "end": "19:00", "duration": 12}
                    }
                },
                "employee_group_size": 6
            },
            {
                "name": "3-Shift Rotation (Day-Evening-Night)",
                "description": "Rotating through day, evening, and night shifts",
                "pattern_type": "weekly", 
                "rotation_cycle": 3,
                "pattern_data": {
                    "cycle_length": 3,
                    "pattern": [
                        {"week": 1, "shifts": ["day", "day", "day", "day", "day", "off", "off"]},
                        {"week": 2, "shifts": ["evening", "evening", "evening", "evening", "evening", "off", "off"]},
                        {"week": 3, "shifts": ["night", "night", "night", "night", "night", "off", "off"]}
                    ],
                    "shift_definitions": {
                        "day": {"start": "07:00", "end": "19:00", "duration": 12},
                        "evening": {"start": "19:00", "end": "07:00", "duration": 12},
                        "night": {"start": "23:00", "end": "07:00", "duration": 8}
                    }
                },
                "employee_group_size": 3
            }
        ]
        
        created_templates = []
        for template_data in default_templates:
            template_data['company_id'] = company_id
            template = cls.create_template(session, **template_data)
            if template:
                created_templates.append(template)
        
        return created_templates
