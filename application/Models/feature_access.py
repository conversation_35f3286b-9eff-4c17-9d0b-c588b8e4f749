from application.database import central_db as db
import uuid
from sqlalchemy.dialects.postgresql import UUID
from flask import current_app
from datetime import datetime

class FeatureAccess(db.Model):
    """Model representing feature access control for companies (stored in central database)."""
    __tablename__ = 'feature_access'

    access_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    company_id = db.Column(db.String(36), db.<PERSON>ey('companies.company_id'), nullable=False)
    feature_name = db.Column(db.String(100), nullable=False)
    is_enabled = db.Column(db.<PERSON>an, default=True, nullable=False)
    usage_limit = db.Column(db.Integer, nullable=True)  # NULL = unlimited
    current_usage = db.Column(db.Integer, default=0, nullable=False)
    reset_period = db.Column(db.String(20), nullable=True)  # DAILY, WEEKLY, MONTHLY, YEARLY
    last_reset_date = db.Column(db.Date, nullable=True)
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    # Relationships
    company = db.relationship('Company', backref=db.backref('feature_access', lazy='dynamic'))

    # Unique constraint: one record per company per feature
    __table_args__ = (
        db.UniqueConstraint('company_id', 'feature_name', name='uq_company_feature'),
        db.Index('idx_feature_company', 'company_id'),
        db.Index('idx_feature_name', 'feature_name'),
        db.Index('idx_feature_enabled', 'is_enabled'),
    )

    def __str__(self):
        """Return a string representation of the object."""
        return f"FeatureAccess [company_id={self.company_id}, feature_name={self.feature_name}, is_enabled={self.is_enabled}]"

    def to_dict(self, include_company=False):
        """Dictionary representation of the object."""
        data = {
            "access_id": str(self.access_id),
            "company_id": self.company_id,
            "feature_name": self.feature_name,
            "is_enabled": self.is_enabled,
            "usage_limit": self.usage_limit,
            "current_usage": self.current_usage,
            "reset_period": self.reset_period,
            "last_reset_date": self.last_reset_date.strftime('%Y-%m-%d') if self.last_reset_date else None,
            "notes": self.notes,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            "usage_percentage": self.get_usage_percentage(),
            "is_limit_exceeded": self.is_limit_exceeded(),
            "remaining_usage": self.get_remaining_usage()
        }
        
        if include_company and self.company:
            data["company"] = {
                "company_name": self.company.company_name,
                "company_tin": self.company.company_tin
            }
            
        return data

    def get_usage_percentage(self):
        """Get usage as percentage of limit."""
        if self.usage_limit is None or self.usage_limit == 0:
            return 0
        return min(100, (self.current_usage / self.usage_limit) * 100)

    def is_limit_exceeded(self):
        """Check if usage limit is exceeded."""
        if self.usage_limit is None:
            return False
        return self.current_usage >= self.usage_limit

    def get_remaining_usage(self):
        """Get remaining usage before hitting limit."""
        if self.usage_limit is None:
            return None
        return max(0, self.usage_limit - self.current_usage)

    def can_use_feature(self):
        """Check if feature can be used (enabled and within limits)."""
        return self.is_enabled and not self.is_limit_exceeded()

    @classmethod
    def check_feature_access(cls, company_id, feature_name):
        """Check if a company has access to a specific feature."""
        try:
            access = cls.query.filter_by(company_id=company_id, feature_name=feature_name).first()
            
            if not access:
                # If no specific access record, check subscription plan
                from application.Models.company_subscription import CompanySubscription
                subscription = CompanySubscription.get_subscription_by_company(company_id)
                
                if subscription and subscription.is_active() and subscription.plan:
                    return subscription.plan.has_feature(feature_name)
                return False
            
            return access.can_use_feature()
        except Exception as e:
            current_app.logger.error(f"Error checking feature access: {e}")
            return False

    @classmethod
    def increment_usage(cls, company_id, feature_name, increment=1):
        """Increment usage count for a feature."""
        try:
            access = cls.query.filter_by(company_id=company_id, feature_name=feature_name).first()
            
            if not access:
                # Create access record if it doesn't exist
                access = cls.create_feature_access(company_id, feature_name)
                if not access:
                    return False, "Failed to create feature access record"

            # Check if usage would exceed limit
            if access.usage_limit and (access.current_usage + increment) > access.usage_limit:
                return False, f"Usage limit exceeded. Limit: {access.usage_limit}, Current: {access.current_usage}"

            access.current_usage += increment
            db.session.commit()
            
            current_app.logger.info(f"Incremented usage for {feature_name} by {increment} for company {company_id}")
            return True, None
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error incrementing usage: {e}")
            return False, str(e)

    @classmethod
    def reset_usage(cls, company_id, feature_name):
        """Reset usage count for a feature."""
        try:
            access = cls.query.filter_by(company_id=company_id, feature_name=feature_name).first()
            
            if access:
                access.current_usage = 0
                access.last_reset_date = datetime.now().date()
                db.session.commit()
                
                current_app.logger.info(f"Reset usage for {feature_name} for company {company_id}")
                return True, None
            
            return False, "Feature access record not found"
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error resetting usage: {e}")
            return False, str(e)

    @classmethod
    def create_feature_access(cls, company_id, feature_name, is_enabled=True, usage_limit=None, **kwargs):
        """Create a new feature access record."""
        try:
            # Check if record already exists
            existing = cls.query.filter_by(company_id=company_id, feature_name=feature_name).first()
            if existing:
                return existing

            access_data = {
                'company_id': company_id,
                'feature_name': feature_name,
                'is_enabled': is_enabled,
                'usage_limit': usage_limit,
                **kwargs
            }

            access = cls(**access_data)
            db.session.add(access)
            db.session.commit()
            
            current_app.logger.info(f"Created feature access for {feature_name} for company {company_id}")
            return access
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error creating feature access: {e}")
            return None

    @classmethod
    def update_feature_access(cls, company_id, feature_name, **kwargs):
        """Update feature access settings."""
        try:
            access = cls.query.filter_by(company_id=company_id, feature_name=feature_name).first()
            
            if not access:
                return None, "Feature access record not found"

            for key, value in kwargs.items():
                if hasattr(access, key):
                    setattr(access, key, value)

            db.session.commit()
            current_app.logger.info(f"Updated feature access for {feature_name} for company {company_id}")
            return access, None
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error updating feature access: {e}")
            return None, str(e)

    @classmethod
    def get_company_features(cls, company_id):
        """Get all feature access records for a company."""
        try:
            return cls.query.filter_by(company_id=company_id).all()
        except Exception as e:
            current_app.logger.error(f"Error getting company features: {e}")
            return []

    @classmethod
    def sync_features_with_plan(cls, company_id):
        """Sync feature access with subscription plan."""
        try:
            from application.Models.company_subscription import CompanySubscription
            
            subscription = CompanySubscription.get_subscription_by_company(company_id)
            if not subscription or not subscription.plan:
                return False, "No active subscription found"

            plan_features = subscription.plan.features
            
            for feature_name, feature_config in plan_features.items():
                if isinstance(feature_config, bool):
                    # Simple boolean feature
                    is_enabled = feature_config
                    usage_limit = None
                elif isinstance(feature_config, dict):
                    # Complex feature with limits
                    is_enabled = feature_config.get('enabled', True)
                    usage_limit = feature_config.get('limit')
                else:
                    continue

                # Get or create feature access record
                access = cls.query.filter_by(company_id=company_id, feature_name=feature_name).first()
                
                if access:
                    # Update existing record
                    access.is_enabled = is_enabled
                    access.usage_limit = usage_limit
                else:
                    # Create new record
                    cls.create_feature_access(
                        company_id=company_id,
                        feature_name=feature_name,
                        is_enabled=is_enabled,
                        usage_limit=usage_limit
                    )

            db.session.commit()
            current_app.logger.info(f"Synced features with plan for company {company_id}")
            return True, None
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error syncing features with plan: {e}")
            return False, str(e)

    @classmethod
    def get_feature_usage_summary(cls, feature_name=None):
        """Get usage summary for features."""
        try:
            query = cls.query
            if feature_name:
                query = query.filter_by(feature_name=feature_name)
            
            features = query.all()
            
            summary = {}
            for feature in features:
                fname = feature.feature_name
                if fname not in summary:
                    summary[fname] = {
                        'total_companies': 0,
                        'enabled_companies': 0,
                        'total_usage': 0,
                        'companies_at_limit': 0
                    }
                
                summary[fname]['total_companies'] += 1
                if feature.is_enabled:
                    summary[fname]['enabled_companies'] += 1
                summary[fname]['total_usage'] += feature.current_usage
                if feature.is_limit_exceeded():
                    summary[fname]['companies_at_limit'] += 1
            
            return summary
        except Exception as e:
            current_app.logger.error(f"Error getting feature usage summary: {e}")
            return {}
