from application.database import db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime, date
from flask import current_app
from decimal import Decimal
import json


class OnboardingAsset(db.Model):
    """
    Assets (equipment, access cards, etc.) assigned to employees during onboarding.
    Tracks what has been assigned and returned.
    """
    __tablename__ = 'onboarding_assets'

    assignment_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    instance_id = db.Column(UUID(as_uuid=True), db.ForeignKey('onboarding_instances.instance_id'), nullable=False)
    
    # Asset details
    asset_type = db.Column(db.String(100), nullable=False)  # LAPTOP, PHONE, ACCESS_CARD, UNIFORM, etc.
    asset_category = db.Column(db.String(100))  # IT_EQUIPMENT, SECURITY, OFFICE_SUPPLIES, SAFETY
    asset_name = db.Column(db.String(255), nullable=False)
    asset_description = db.Column(db.Text)
    asset_serial_number = db.Column(db.String(100))
    asset_model = db.Column(db.String(100))
    asset_brand = db.Column(db.String(100))
    
    # Asset identification
    asset_tag = db.Column(db.String(100))  # Company asset tag
    barcode = db.Column(db.String(100))
    qr_code = db.Column(db.String(255))
    
    # Financial details
    purchase_cost = db.Column(db.Numeric(15, 2))
    current_value = db.Column(db.Numeric(15, 2))
    depreciation_rate = db.Column(db.Numeric(5, 2))  # Annual depreciation percentage
    
    # Assignment details
    assigned_date = db.Column(db.Date, default=date.today)
    assigned_by = db.Column(UUID(as_uuid=True), nullable=False)
    assignment_reason = db.Column(db.String(255), default='ONBOARDING')
    expected_return_date = db.Column(db.Date)
    
    # Condition tracking
    condition_at_assignment = db.Column(db.String(50), default='GOOD')  # NEW, GOOD, FAIR, POOR
    assignment_notes = db.Column(db.Text)
    assignment_photos = db.Column(db.Text)  # JSON array of photo URLs
    
    # Return details
    return_date = db.Column(db.Date)
    returned_by = db.Column(UUID(as_uuid=True))
    return_condition = db.Column(db.String(50))
    return_notes = db.Column(db.Text)
    return_photos = db.Column(db.Text)  # JSON array of photo URLs
    damage_assessment = db.Column(db.Text)
    damage_cost = db.Column(db.Numeric(10, 2))
    
    # Status tracking
    status = db.Column(db.String(50), default='ASSIGNED')  # ASSIGNED, RETURNED, LOST, DAMAGED, REPLACED
    is_returnable = db.Column(db.Boolean, default=True)  # Some assets like uniforms might not be returnable
    requires_training = db.Column(db.Boolean, default=False)  # Asset requires training to use
    training_completed = db.Column(db.Boolean, default=False)
    training_date = db.Column(db.Date)
    
    # Location tracking
    current_location = db.Column(db.String(255))
    assigned_location = db.Column(db.String(255))  # Where asset should be used
    
    # Maintenance and warranty
    warranty_expiry_date = db.Column(db.Date)
    last_maintenance_date = db.Column(db.Date)
    next_maintenance_date = db.Column(db.Date)
    maintenance_notes = db.Column(db.Text)
    
    # Security and compliance
    security_level = db.Column(db.String(50), default='STANDARD')  # STANDARD, CONFIDENTIAL, SECRET
    requires_background_check = db.Column(db.Boolean, default=False)
    background_check_completed = db.Column(db.Boolean, default=False)
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    def to_dict(self):
        """Dictionary representation of the asset assignment."""
        return {
            "assignment_id": str(self.assignment_id),
            "instance_id": str(self.instance_id),
            "asset_type": self.asset_type,
            "asset_category": self.asset_category,
            "asset_name": self.asset_name,
            "asset_description": self.asset_description,
            "asset_serial_number": self.asset_serial_number,
            "asset_model": self.asset_model,
            "asset_brand": self.asset_brand,
            "asset_tag": self.asset_tag,
            "barcode": self.barcode,
            "qr_code": self.qr_code,
            "purchase_cost": float(self.purchase_cost) if self.purchase_cost else None,
            "current_value": float(self.current_value) if self.current_value else None,
            "depreciation_rate": float(self.depreciation_rate) if self.depreciation_rate else None,
            "assigned_date": self.assigned_date.strftime('%Y-%m-%d') if self.assigned_date else None,
            "assigned_by": str(self.assigned_by),
            "assignment_reason": self.assignment_reason,
            "expected_return_date": self.expected_return_date.strftime('%Y-%m-%d') if self.expected_return_date else None,
            "condition_at_assignment": self.condition_at_assignment,
            "assignment_notes": self.assignment_notes,
            "assignment_photos": json.loads(self.assignment_photos) if self.assignment_photos else [],
            "return_date": self.return_date.strftime('%Y-%m-%d') if self.return_date else None,
            "returned_by": str(self.returned_by) if self.returned_by else None,
            "return_condition": self.return_condition,
            "return_notes": self.return_notes,
            "return_photos": json.loads(self.return_photos) if self.return_photos else [],
            "damage_assessment": self.damage_assessment,
            "damage_cost": float(self.damage_cost) if self.damage_cost else None,
            "status": self.status,
            "is_returnable": self.is_returnable,
            "requires_training": self.requires_training,
            "training_completed": self.training_completed,
            "training_date": self.training_date.strftime('%Y-%m-%d') if self.training_date else None,
            "current_location": self.current_location,
            "assigned_location": self.assigned_location,
            "warranty_expiry_date": self.warranty_expiry_date.strftime('%Y-%m-%d') if self.warranty_expiry_date else None,
            "last_maintenance_date": self.last_maintenance_date.strftime('%Y-%m-%d') if self.last_maintenance_date else None,
            "next_maintenance_date": self.next_maintenance_date.strftime('%Y-%m-%d') if self.next_maintenance_date else None,
            "maintenance_notes": self.maintenance_notes,
            "security_level": self.security_level,
            "requires_background_check": self.requires_background_check,
            "background_check_completed": self.background_check_completed,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            "days_assigned": self.get_days_assigned(),
            "is_overdue_return": self.is_overdue_return(),
            "current_depreciated_value": self.get_current_depreciated_value()
        }

    @classmethod
    def create_assignment(cls, session, **kwargs):
        """Create a new asset assignment."""
        try:
            # Handle JSON fields
            if 'assignment_photos' in kwargs and isinstance(kwargs['assignment_photos'], list):
                kwargs['assignment_photos'] = json.dumps(kwargs['assignment_photos'])
            
            if 'return_photos' in kwargs and isinstance(kwargs['return_photos'], list):
                kwargs['return_photos'] = json.dumps(kwargs['return_photos'])
            
            assignment = cls(**kwargs)
            session.add(assignment)
            session.flush()
            
            current_app.logger.info(f"Created asset assignment: {assignment.asset_name}")
            return assignment, None
            
        except Exception as e:
            current_app.logger.error(f"Error creating asset assignment: {e}")
            return None, str(e)

    @classmethod
    def get_assignments_by_instance(cls, session, instance_id, status=None):
        """Get all asset assignments for an onboarding instance."""
        query = session.query(cls).filter_by(instance_id=instance_id)
        
        if status:
            if isinstance(status, list):
                query = query.filter(cls.status.in_(status))
            else:
                query = query.filter_by(status=status)
        
        return query.order_by(cls.assigned_date.desc()).all()

    @classmethod
    def get_assignment_by_id(cls, session, assignment_id):
        """Get assignment by ID."""
        return session.query(cls).filter_by(assignment_id=assignment_id).first()

    @classmethod
    def get_assignments_by_asset_type(cls, session, asset_type, status=None):
        """Get assignments by asset type."""
        query = session.query(cls).filter_by(asset_type=asset_type)
        
        if status:
            query = query.filter_by(status=status)
        
        return query.order_by(cls.assigned_date.desc()).all()

    def return_asset(self, session, returned_by, return_condition, return_notes=None, damage_cost=None, return_photos=None):
        """Process asset return."""
        try:
            if self.status != 'ASSIGNED':
                return False, f"Asset cannot be returned. Current status: {self.status}"
            
            self.return_date = date.today()
            self.returned_by = returned_by
            self.return_condition = return_condition
            self.return_notes = return_notes
            self.status = 'RETURNED'
            
            if damage_cost:
                self.damage_cost = Decimal(str(damage_cost))
            
            if return_photos:
                self.return_photos = json.dumps(return_photos)
            
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Returned asset: {self.assignment_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error returning asset: {e}")
            return False, str(e)

    def mark_lost(self, session, reported_by, notes=None):
        """Mark asset as lost."""
        try:
            self.status = 'LOST'
            self.return_notes = f"Reported lost by {reported_by}. {notes or ''}"
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Marked asset as lost: {self.assignment_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error marking asset as lost: {e}")
            return False, str(e)

    def mark_damaged(self, session, reported_by, damage_assessment, damage_cost=None):
        """Mark asset as damaged."""
        try:
            self.status = 'DAMAGED'
            self.damage_assessment = damage_assessment
            
            if damage_cost:
                self.damage_cost = Decimal(str(damage_cost))
            
            self.return_notes = f"Damage reported by {reported_by}"
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Marked asset as damaged: {self.assignment_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error marking asset as damaged: {e}")
            return False, str(e)

    def complete_training(self, session, training_date=None):
        """Mark training as completed for the asset."""
        try:
            self.training_completed = True
            self.training_date = training_date or date.today()
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Completed training for asset: {self.assignment_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error completing training: {e}")
            return False, str(e)

    def update_location(self, session, new_location, updated_by=None):
        """Update asset location."""
        try:
            old_location = self.current_location
            self.current_location = new_location
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Updated asset location from {old_location} to {new_location}: {self.assignment_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error updating asset location: {e}")
            return False, str(e)

    def get_days_assigned(self):
        """Get number of days asset has been assigned."""
        if not self.assigned_date:
            return 0
        
        end_date = self.return_date or date.today()
        return (end_date - self.assigned_date).days

    def is_overdue_return(self):
        """Check if asset return is overdue."""
        if not self.expected_return_date or self.status in ['RETURNED', 'LOST']:
            return False
        
        return date.today() > self.expected_return_date

    def get_current_depreciated_value(self):
        """Calculate current depreciated value."""
        if not self.purchase_cost or not self.depreciation_rate:
            return self.current_value or self.purchase_cost
        
        years_assigned = self.get_days_assigned() / 365.25
        depreciation_amount = self.purchase_cost * (self.depreciation_rate / 100) * Decimal(str(years_assigned))
        
        current_value = self.purchase_cost - depreciation_amount
        return max(current_value, Decimal('0'))

    def is_warranty_expired(self):
        """Check if warranty has expired."""
        if not self.warranty_expiry_date:
            return False
        
        return date.today() > self.warranty_expiry_date

    def needs_maintenance(self):
        """Check if asset needs maintenance."""
        if not self.next_maintenance_date:
            return False
        
        return date.today() >= self.next_maintenance_date

    @classmethod
    def get_overdue_returns(cls, session, days_overdue=0):
        """Get assets with overdue returns."""
        overdue_date = date.today() - timedelta(days=days_overdue)
        
        return session.query(cls).filter(
            cls.expected_return_date < overdue_date,
            cls.status == 'ASSIGNED'
        ).order_by(cls.expected_return_date).all()

    @classmethod
    def get_assets_needing_maintenance(cls, session):
        """Get assets that need maintenance."""
        return session.query(cls).filter(
            cls.next_maintenance_date <= date.today(),
            cls.status == 'ASSIGNED'
        ).order_by(cls.next_maintenance_date).all()

    @classmethod
    def get_expiring_warranties(cls, session, days_threshold=30):
        """Get assets with warranties expiring soon."""
        threshold_date = date.today() + timedelta(days=days_threshold)
        
        return session.query(cls).filter(
            cls.warranty_expiry_date.isnot(None),
            cls.warranty_expiry_date <= threshold_date,
            cls.warranty_expiry_date >= date.today(),
            cls.status == 'ASSIGNED'
        ).order_by(cls.warranty_expiry_date).all()

    def update_maintenance(self, session, maintenance_date=None, next_maintenance_date=None, notes=None):
        """Update maintenance information."""
        try:
            if maintenance_date:
                self.last_maintenance_date = maintenance_date
            
            if next_maintenance_date:
                self.next_maintenance_date = next_maintenance_date
            
            if notes:
                self.maintenance_notes = notes
            
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Updated maintenance for asset: {self.assignment_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error updating maintenance: {e}")
            return False, str(e)
