from application.database import db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime, date
from flask import current_app
import json
import os


class OnboardingDocument(db.Model):
    """
    Documents uploaded by employees during onboarding process.
    Links to document requirements and tracks verification status.
    """
    __tablename__ = 'onboarding_documents'

    document_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    instance_id = db.Column(UUID(as_uuid=True), db.ForeignKey('onboarding_instances.instance_id'), nullable=False)
    requirement_id = db.Column(UUID(as_uuid=True), db.ForeignKey('onboarding_document_requirements.requirement_id'), nullable=False)
    
    # File details
    original_filename = db.Column(db.String(255), nullable=False)
    stored_filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(500), nullable=False)  # Path in Azure Blob Storage
    file_size_bytes = db.Column(db.BigInteger)
    file_type = db.Column(db.String(50))
    mime_type = db.Column(db.String(100))
    
    # Upload details
    upload_date = db.Column(db.DateTime, default=datetime.now)
    uploaded_by = db.Column(UUID(as_uuid=True), nullable=False)
    upload_ip_address = db.Column(db.String(45))  # IPv4 or IPv6
    upload_user_agent = db.Column(db.String(500))
    
    # Document metadata
    document_title = db.Column(db.String(255))
    document_description = db.Column(db.Text)
    document_date = db.Column(db.Date)  # Date on the document (e.g., issue date)
    expiry_date = db.Column(db.Date)  # Document expiry date if applicable
    document_number = db.Column(db.String(100))  # Document number/ID if applicable
    issuing_authority = db.Column(db.String(255))  # Who issued the document
    
    # Verification status
    verification_status = db.Column(db.String(50), default='PENDING')  # PENDING, VERIFIED, REJECTED, EXPIRED
    verified_by = db.Column(UUID(as_uuid=True))
    verification_date = db.Column(db.DateTime)
    verification_notes = db.Column(db.Text)
    rejection_reason = db.Column(db.Text)
    
    # Security and compliance
    is_sensitive = db.Column(db.Boolean, default=False)
    access_level = db.Column(db.String(50), default='STANDARD')  # STANDARD, CONFIDENTIAL, RESTRICTED
    retention_period_years = db.Column(db.Integer, default=7)
    
    # Version control
    version = db.Column(db.Integer, default=1)
    replaces_document_id = db.Column(UUID(as_uuid=True))  # If this replaces another document
    is_current_version = db.Column(db.Boolean, default=True)
    
    # Processing status
    virus_scan_status = db.Column(db.String(50), default='PENDING')  # PENDING, CLEAN, INFECTED, FAILED
    virus_scan_date = db.Column(db.DateTime)
    ocr_processed = db.Column(db.Boolean, default=False)
    ocr_text = db.Column(db.Text)  # Extracted text from OCR
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    def to_dict(self):
        """Dictionary representation of the document."""
        return {
            "document_id": str(self.document_id),
            "instance_id": str(self.instance_id),
            "requirement_id": str(self.requirement_id),
            "original_filename": self.original_filename,
            "stored_filename": self.stored_filename,
            "file_path": self.file_path,
            "file_size_bytes": self.file_size_bytes,
            "file_size_mb": round(self.file_size_bytes / (1024 * 1024), 2) if self.file_size_bytes else 0,
            "file_type": self.file_type,
            "mime_type": self.mime_type,
            "upload_date": self.upload_date.strftime('%Y-%m-%d %H:%M:%S') if self.upload_date else None,
            "uploaded_by": str(self.uploaded_by),
            "upload_ip_address": self.upload_ip_address,
            "document_title": self.document_title,
            "document_description": self.document_description,
            "document_date": self.document_date.strftime('%Y-%m-%d') if self.document_date else None,
            "expiry_date": self.expiry_date.strftime('%Y-%m-%d') if self.expiry_date else None,
            "document_number": self.document_number,
            "issuing_authority": self.issuing_authority,
            "verification_status": self.verification_status,
            "verified_by": str(self.verified_by) if self.verified_by else None,
            "verification_date": self.verification_date.strftime('%Y-%m-%d %H:%M:%S') if self.verification_date else None,
            "verification_notes": self.verification_notes,
            "rejection_reason": self.rejection_reason,
            "is_sensitive": self.is_sensitive,
            "access_level": self.access_level,
            "retention_period_years": self.retention_period_years,
            "version": self.version,
            "replaces_document_id": str(self.replaces_document_id) if self.replaces_document_id else None,
            "is_current_version": self.is_current_version,
            "virus_scan_status": self.virus_scan_status,
            "virus_scan_date": self.virus_scan_date.strftime('%Y-%m-%d %H:%M:%S') if self.virus_scan_date else None,
            "ocr_processed": self.ocr_processed,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            "is_expired": self.is_expired(),
            "days_until_expiry": self.get_days_until_expiry(),
            "download_url": self.get_download_url()
        }

    @classmethod
    def create_document(cls, session, **kwargs):
        """Create a new document record."""
        try:
            document = cls(**kwargs)
            session.add(document)
            session.flush()
            
            current_app.logger.info(f"Created document record: {document.original_filename}")
            return document, None
            
        except Exception as e:
            current_app.logger.error(f"Error creating document record: {e}")
            return None, str(e)

    @classmethod
    def get_documents_by_instance(cls, session, instance_id, requirement_id=None):
        """Get all documents for an onboarding instance."""
        query = session.query(cls).filter_by(instance_id=instance_id, is_current_version=True)
        
        if requirement_id:
            query = query.filter_by(requirement_id=requirement_id)
        
        return query.order_by(cls.upload_date.desc()).all()

    @classmethod
    def get_documents_by_status(cls, session, verification_status, limit=None):
        """Get documents by verification status."""
        query = session.query(cls).filter_by(
            verification_status=verification_status,
            is_current_version=True
        )
        
        if limit:
            query = query.limit(limit)
        
        return query.order_by(cls.upload_date).all()

    @classmethod
    def get_document_by_id(cls, session, document_id):
        """Get document by ID."""
        return session.query(cls).filter_by(document_id=document_id).first()

    def verify_document(self, session, verified_by, verification_notes=None, approved=True):
        """Verify or reject the document."""
        try:
            if approved:
                self.verification_status = 'VERIFIED'
            else:
                self.verification_status = 'REJECTED'
                if verification_notes:
                    self.rejection_reason = verification_notes
            
            self.verified_by = verified_by
            self.verification_date = datetime.now()
            self.verification_notes = verification_notes
            self.updated_at = datetime.now()
            
            session.commit()
            
            # Update instance progress
            self.instance.update_progress_counts(session)
            
            current_app.logger.info(f"Verified document: {self.document_id} - Approved: {approved}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error verifying document: {e}")
            return False, str(e)

    def replace_document(self, session, new_file_path, new_filename, uploaded_by):
        """Replace document with a new version."""
        try:
            # Mark current document as not current
            self.is_current_version = False
            self.updated_at = datetime.now()
            
            # Create new document version
            new_document = OnboardingDocument(
                instance_id=self.instance_id,
                requirement_id=self.requirement_id,
                original_filename=new_filename,
                stored_filename=new_filename,  # This should be generated
                file_path=new_file_path,
                uploaded_by=uploaded_by,
                document_title=self.document_title,
                document_description=self.document_description,
                version=self.version + 1,
                replaces_document_id=self.document_id,
                is_sensitive=self.is_sensitive,
                access_level=self.access_level
            )
            
            session.add(new_document)
            session.commit()
            
            current_app.logger.info(f"Replaced document: {self.document_id} with {new_document.document_id}")
            return new_document, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error replacing document: {e}")
            return None, str(e)

    def mark_virus_scan_result(self, session, status, scan_date=None):
        """Update virus scan status."""
        try:
            self.virus_scan_status = status
            self.virus_scan_date = scan_date or datetime.now()
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Updated virus scan status for document {self.document_id}: {status}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error updating virus scan status: {e}")
            return False, str(e)

    def update_ocr_text(self, session, extracted_text):
        """Update OCR extracted text."""
        try:
            self.ocr_text = extracted_text
            self.ocr_processed = True
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Updated OCR text for document: {self.document_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error updating OCR text: {e}")
            return False, str(e)

    def is_expired(self):
        """Check if document is expired."""
        if not self.expiry_date:
            return False
        
        return date.today() > self.expiry_date

    def get_days_until_expiry(self):
        """Get number of days until document expires."""
        if not self.expiry_date:
            return None
        
        days_remaining = (self.expiry_date - date.today()).days
        return days_remaining

    def is_expiring_soon(self, days_threshold=30):
        """Check if document is expiring within threshold days."""
        days_until_expiry = self.get_days_until_expiry()
        if days_until_expiry is None:
            return False
        
        return 0 <= days_until_expiry <= days_threshold

    def get_download_url(self):
        """Get secure download URL for the document."""
        # This would generate a signed URL for Azure Blob Storage
        # Implementation depends on your Azure Blob Storage setup
        return f"/api/onboarding/documents/{self.document_id}/download"

    def get_file_extension(self):
        """Get file extension from filename."""
        if self.original_filename and '.' in self.original_filename:
            return self.original_filename.split('.')[-1].lower()
        return None

    def is_image(self):
        """Check if document is an image file."""
        image_extensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tiff']
        return self.get_file_extension() in image_extensions

    def is_pdf(self):
        """Check if document is a PDF file."""
        return self.get_file_extension() == 'pdf'

    def get_retention_expiry_date(self):
        """Get date when document should be deleted based on retention policy."""
        if not self.upload_date or not self.retention_period_years:
            return None
        
        from dateutil.relativedelta import relativedelta
        return self.upload_date.date() + relativedelta(years=self.retention_period_years)

    def should_be_retained(self):
        """Check if document should still be retained."""
        retention_expiry = self.get_retention_expiry_date()
        if not retention_expiry:
            return True
        
        return date.today() <= retention_expiry

    @classmethod
    def get_expiring_documents(cls, session, days_threshold=30):
        """Get documents expiring within threshold days."""
        threshold_date = date.today() + timedelta(days=days_threshold)
        
        return session.query(cls).filter(
            cls.expiry_date.isnot(None),
            cls.expiry_date <= threshold_date,
            cls.expiry_date >= date.today(),
            cls.is_current_version == True,
            cls.verification_status == 'VERIFIED'
        ).order_by(cls.expiry_date).all()

    @classmethod
    def get_documents_for_retention_cleanup(cls, session):
        """Get documents that have exceeded retention period."""
        return session.query(cls).filter(
            cls.retention_period_years.isnot(None),
            cls.upload_date < datetime.now() - timedelta(days=365 * cls.retention_period_years)
        ).all()

    def update_metadata(self, session, **kwargs):
        """Update document metadata."""
        try:
            allowed_fields = [
                'document_title', 'document_description', 'document_date',
                'expiry_date', 'document_number', 'issuing_authority'
            ]
            
            for key, value in kwargs.items():
                if key in allowed_fields and hasattr(self, key):
                    setattr(self, key, value)
            
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Updated document metadata: {self.document_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error updating document metadata: {e}")
            return False, str(e)
