from application.database import db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime
from flask import current_app
import json


class OnboardingDocumentRequirement(db.Model):
    """
    Document requirements that companies can configure for their onboarding workflows.
    Each company can define exactly what documents they need from new employees.
    """
    __tablename__ = 'onboarding_document_requirements'

    requirement_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    workflow_id = db.Column(UUID(as_uuid=True), db.ForeignKey('onboarding_workflows.workflow_id'), nullable=False)
    
    # Document details
    document_name = db.Column(db.String(255), nullable=False)
    document_code = db.Column(db.String(100), nullable=False)  # Unique code for the document type
    description = db.Column(db.Text)
    category = db.Column(db.String(100))  # IDENTITY, EDUCATION, EMPLOYMENT, FINANCIAL, LEGAL, MEDICAL
    
    # Requirements
    is_mandatory = db.Column(db.Boolean, default=True)
    is_sensitive = db.Column(db.Bo<PERSON>, default=False)  # Requires special handling
    requires_verification = db.Column(db.Boolean, default=True)
    requires_original = db.Column(db.Boolean, default=False)  # Original document needed
    
    # File specifications
    file_types_allowed = db.Column(db.String(255), default='pdf,jpg,jpeg,png')  # Comma-separated
    max_file_size_mb = db.Column(db.Integer, default=5)
    min_file_size_kb = db.Column(db.Integer, default=10)
    max_files_count = db.Column(db.Integer, default=1)  # How many files can be uploaded
    
    # Validation rules
    validation_rules = db.Column(db.Text)  # JSON with validation criteria
    expiry_check = db.Column(db.Boolean, default=False)  # Check if document has expiry
    min_validity_days = db.Column(db.Integer)  # Minimum days before expiry
    
    # Display and ordering
    display_order = db.Column(db.Integer, default=1)
    help_text = db.Column(db.Text)  # Help text for employees
    example_url = db.Column(db.String(500))  # URL to example document
    
    # Workflow integration
    due_days_from_start = db.Column(db.Integer, default=3)  # Days to submit from onboarding start
    blocks_other_tasks = db.Column(db.Boolean, default=False)  # Blocks other tasks if not submitted
    auto_remind = db.Column(db.Boolean, default=True)
    reminder_frequency_days = db.Column(db.Integer, default=1)
    
    # Status and metadata
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # Relationships
    documents = db.relationship('OnboardingDocument', backref='requirement', lazy='dynamic')

    def to_dict(self):
        """Dictionary representation of the document requirement."""
        return {
            "requirement_id": str(self.requirement_id),
            "workflow_id": str(self.workflow_id),
            "document_name": self.document_name,
            "document_code": self.document_code,
            "description": self.description,
            "category": self.category,
            "is_mandatory": self.is_mandatory,
            "is_sensitive": self.is_sensitive,
            "requires_verification": self.requires_verification,
            "requires_original": self.requires_original,
            "file_types_allowed": self.file_types_allowed.split(',') if self.file_types_allowed else [],
            "max_file_size_mb": self.max_file_size_mb,
            "min_file_size_kb": self.min_file_size_kb,
            "max_files_count": self.max_files_count,
            "validation_rules": json.loads(self.validation_rules) if self.validation_rules else {},
            "expiry_check": self.expiry_check,
            "min_validity_days": self.min_validity_days,
            "display_order": self.display_order,
            "help_text": self.help_text,
            "example_url": self.example_url,
            "due_days_from_start": self.due_days_from_start,
            "blocks_other_tasks": self.blocks_other_tasks,
            "auto_remind": self.auto_remind,
            "reminder_frequency_days": self.reminder_frequency_days,
            "is_active": self.is_active,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            "submission_count": self.documents.count()
        }

    @classmethod
    def create_requirement(cls, session, **kwargs):
        """Create a new document requirement."""
        try:
            # Handle JSON fields
            if 'validation_rules' in kwargs and isinstance(kwargs['validation_rules'], dict):
                kwargs['validation_rules'] = json.dumps(kwargs['validation_rules'])
            
            requirement = cls(**kwargs)
            session.add(requirement)
            session.flush()
            
            current_app.logger.info(f"Created document requirement: {requirement.document_name}")
            return requirement, None
            
        except Exception as e:
            current_app.logger.error(f"Error creating document requirement: {e}")
            return None, str(e)

    @classmethod
    def get_requirements_by_workflow(cls, session, workflow_id, include_inactive=False):
        """Get all document requirements for a workflow."""
        query = session.query(cls).filter_by(workflow_id=workflow_id)
        
        if not include_inactive:
            query = query.filter_by(is_active=True)
            
        return query.order_by(cls.display_order, cls.document_name).all()

    @classmethod
    def get_mandatory_requirements(cls, session, workflow_id):
        """Get all mandatory document requirements for a workflow."""
        return session.query(cls).filter_by(
            workflow_id=workflow_id,
            is_mandatory=True,
            is_active=True
        ).order_by(cls.display_order).all()

    @classmethod
    def get_requirement_by_id(cls, session, requirement_id):
        """Get requirement by ID."""
        return session.query(cls).filter_by(requirement_id=requirement_id).first()

    @classmethod
    def get_requirement_by_code(cls, session, workflow_id, document_code):
        """Get requirement by document code within a workflow."""
        return session.query(cls).filter_by(
            workflow_id=workflow_id,
            document_code=document_code,
            is_active=True
        ).first()

    def update_requirement(self, session, **kwargs):
        """Update requirement details."""
        try:
            # Handle JSON fields
            if 'validation_rules' in kwargs and isinstance(kwargs['validation_rules'], dict):
                kwargs['validation_rules'] = json.dumps(kwargs['validation_rules'])
            
            for key, value in kwargs.items():
                if hasattr(self, key):
                    setattr(self, key, value)
            
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Updated document requirement: {self.requirement_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error updating document requirement: {e}")
            return False, str(e)

    def deactivate_requirement(self, session):
        """Deactivate requirement (soft delete)."""
        try:
            self.is_active = False
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Deactivated document requirement: {self.requirement_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error deactivating document requirement: {e}")
            return False, str(e)

    def validate_file(self, file_name, file_size_bytes):
        """Validate if a file meets the requirements."""
        errors = []
        
        # Check file extension
        if file_name:
            file_ext = file_name.lower().split('.')[-1] if '.' in file_name else ''
            allowed_types = [t.strip().lower() for t in self.file_types_allowed.split(',')]
            
            if file_ext not in allowed_types:
                errors.append(f"File type '{file_ext}' not allowed. Allowed types: {', '.join(allowed_types)}")
        
        # Check file size
        if file_size_bytes:
            max_size_bytes = self.max_file_size_mb * 1024 * 1024
            min_size_bytes = self.min_file_size_kb * 1024
            
            if file_size_bytes > max_size_bytes:
                errors.append(f"File size exceeds maximum allowed size of {self.max_file_size_mb}MB")
            
            if file_size_bytes < min_size_bytes:
                errors.append(f"File size below minimum required size of {self.min_file_size_kb}KB")
        
        return len(errors) == 0, errors

    def get_submission_stats(self, session):
        """Get submission statistics for this requirement."""
        try:
            from application.Models.onboarding.onboarding_document import OnboardingDocument
            
            total_submissions = self.documents.count()
            verified_submissions = self.documents.filter_by(verification_status='VERIFIED').count()
            pending_submissions = self.documents.filter_by(verification_status='PENDING').count()
            rejected_submissions = self.documents.filter_by(verification_status='REJECTED').count()
            
            return {
                "total_submissions": total_submissions,
                "verified_submissions": verified_submissions,
                "pending_submissions": pending_submissions,
                "rejected_submissions": rejected_submissions,
                "verification_rate": round(verified_submissions / total_submissions * 100, 2) if total_submissions > 0 else 0
            }
            
        except Exception as e:
            current_app.logger.error(f"Error getting requirement submission stats: {e}")
            return {
                "total_submissions": 0,
                "verified_submissions": 0,
                "pending_submissions": 0,
                "rejected_submissions": 0,
                "verification_rate": 0
            }

    @classmethod
    def get_requirements_by_category(cls, session, workflow_id, category):
        """Get all requirements in a specific category."""
        return session.query(cls).filter_by(
            workflow_id=workflow_id,
            category=category,
            is_active=True
        ).order_by(cls.display_order).all()

    def is_blocking_requirement(self, session, instance_id):
        """Check if this requirement is blocking other tasks."""
        if not self.blocks_other_tasks:
            return False
        
        # Check if document has been submitted and verified
        submitted_doc = self.documents.filter_by(
            instance_id=instance_id,
            verification_status='VERIFIED'
        ).first()
        
        return submitted_doc is None

    def get_validation_rules_dict(self):
        """Get validation rules as dictionary."""
        try:
            return json.loads(self.validation_rules) if self.validation_rules else {}
        except:
            return {}

    def set_validation_rules(self, rules_dict):
        """Set validation rules from dictionary."""
        try:
            self.validation_rules = json.dumps(rules_dict) if rules_dict else None
        except:
            self.validation_rules = None
