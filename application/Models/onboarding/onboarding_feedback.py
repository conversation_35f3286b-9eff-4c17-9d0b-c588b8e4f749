from application.database import db
from sqlalchemy.dialects.postgresql import <PERSON>UID
import uuid
from datetime import datetime, date
from flask import current_app
import json


class OnboardingFeedback(db.Model):
    """
    Feedback collected during the onboarding process from employees, managers, HR, and other stakeholders.
    Helps improve the onboarding experience and track satisfaction.
    """
    __tablename__ = 'onboarding_feedback'

    feedback_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    instance_id = db.Column(UUID(as_uuid=True), db.ForeignKey('onboarding_instances.instance_id'), nullable=False)
    
    # Feedback details
    feedback_type = db.Column(db.String(100), nullable=False)  # EMPLOYEE, MANAGER, HR, BUDDY, EXIT_INTERVIEW
    feedback_stage = db.Column(db.String(100))  # PRE_BOARDING, FIRST_DAY, FIRST_WEEK, FIRST_MONTH, COMPLETION
    feedback_category = db.Column(db.String(100))  # PROCESS, COMMUNICATION, TRAINING, SUPPORT, OVERALL
    
    # Content
    feedback_text = db.Column(db.Text, nullable=False)
    feedback_title = db.Column(db.String(255))
    
    # Ratings (1-5 scale)
    overall_rating = db.Column(db.Integer)  # Overall experience rating
    process_rating = db.Column(db.Integer)  # Onboarding process rating
    communication_rating = db.Column(db.Integer)  # Communication effectiveness
    support_rating = db.Column(db.Integer)  # Support received rating
    training_rating = db.Column(db.Integer)  # Training quality rating
    
    # Specific feedback areas
    what_went_well = db.Column(db.Text)
    what_could_improve = db.Column(db.Text)
    suggestions = db.Column(db.Text)
    would_recommend = db.Column(db.Boolean)  # Would recommend this onboarding process
    
    # Structured feedback (JSON)
    structured_responses = db.Column(db.Text)  # JSON with specific question responses
    
    # Provider details
    provided_by = db.Column(UUID(as_uuid=True), nullable=False)
    provider_role = db.Column(db.String(100))  # Role of feedback provider
    is_anonymous = db.Column(db.Boolean, default=False)
    
    # Timing
    feedback_date = db.Column(db.DateTime, default=datetime.now)
    onboarding_day = db.Column(db.Integer)  # Which day of onboarding this feedback was given
    
    # Follow-up
    requires_follow_up = db.Column(db.Boolean, default=False)
    follow_up_assigned_to = db.Column(UUID(as_uuid=True))
    follow_up_completed = db.Column(db.Boolean, default=False)
    follow_up_date = db.Column(db.DateTime)
    follow_up_notes = db.Column(db.Text)
    
    # Sentiment analysis (if implemented)
    sentiment_score = db.Column(db.Numeric(3, 2))  # -1.0 to 1.0
    sentiment_label = db.Column(db.String(50))  # POSITIVE, NEGATIVE, NEUTRAL
    
    # Status and visibility
    status = db.Column(db.String(50), default='ACTIVE')  # ACTIVE, ARCHIVED, FLAGGED
    is_public = db.Column(db.Boolean, default=False)  # Can be shared with other employees
    visibility_level = db.Column(db.String(50), default='HR_ONLY')  # HR_ONLY, MANAGEMENT, PUBLIC
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    def to_dict(self):
        """Dictionary representation of the feedback."""
        return {
            "feedback_id": str(self.feedback_id),
            "instance_id": str(self.instance_id),
            "feedback_type": self.feedback_type,
            "feedback_stage": self.feedback_stage,
            "feedback_category": self.feedback_category,
            "feedback_text": self.feedback_text,
            "feedback_title": self.feedback_title,
            "overall_rating": self.overall_rating,
            "process_rating": self.process_rating,
            "communication_rating": self.communication_rating,
            "support_rating": self.support_rating,
            "training_rating": self.training_rating,
            "what_went_well": self.what_went_well,
            "what_could_improve": self.what_could_improve,
            "suggestions": self.suggestions,
            "would_recommend": self.would_recommend,
            "structured_responses": json.loads(self.structured_responses) if self.structured_responses else {},
            "provided_by": str(self.provided_by) if not self.is_anonymous else None,
            "provider_role": self.provider_role,
            "is_anonymous": self.is_anonymous,
            "feedback_date": self.feedback_date.strftime('%Y-%m-%d %H:%M:%S') if self.feedback_date else None,
            "onboarding_day": self.onboarding_day,
            "requires_follow_up": self.requires_follow_up,
            "follow_up_assigned_to": str(self.follow_up_assigned_to) if self.follow_up_assigned_to else None,
            "follow_up_completed": self.follow_up_completed,
            "follow_up_date": self.follow_up_date.strftime('%Y-%m-%d %H:%M:%S') if self.follow_up_date else None,
            "follow_up_notes": self.follow_up_notes,
            "sentiment_score": float(self.sentiment_score) if self.sentiment_score else None,
            "sentiment_label": self.sentiment_label,
            "status": self.status,
            "is_public": self.is_public,
            "visibility_level": self.visibility_level,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            "average_rating": self.get_average_rating()
        }

    @classmethod
    def create_feedback(cls, session, **kwargs):
        """Create new feedback entry."""
        try:
            # Handle JSON fields
            if 'structured_responses' in kwargs and isinstance(kwargs['structured_responses'], dict):
                kwargs['structured_responses'] = json.dumps(kwargs['structured_responses'])
            
            feedback = cls(**kwargs)
            session.add(feedback)
            session.flush()
            
            current_app.logger.info(f"Created feedback entry: {feedback.feedback_type}")
            return feedback, None
            
        except Exception as e:
            current_app.logger.error(f"Error creating feedback: {e}")
            return None, str(e)

    @classmethod
    def get_feedback_by_instance(cls, session, instance_id, feedback_type=None, feedback_stage=None):
        """Get feedback for an onboarding instance."""
        query = session.query(cls).filter_by(instance_id=instance_id, status='ACTIVE')
        
        if feedback_type:
            query = query.filter_by(feedback_type=feedback_type)
        
        if feedback_stage:
            query = query.filter_by(feedback_stage=feedback_stage)
        
        return query.order_by(cls.feedback_date.desc()).all()

    @classmethod
    def get_feedback_by_id(cls, session, feedback_id):
        """Get feedback by ID."""
        return session.query(cls).filter_by(feedback_id=feedback_id).first()

    @classmethod
    def get_feedback_requiring_follow_up(cls, session, assigned_to=None):
        """Get feedback that requires follow-up."""
        query = session.query(cls).filter_by(
            requires_follow_up=True,
            follow_up_completed=False,
            status='ACTIVE'
        )
        
        if assigned_to:
            query = query.filter_by(follow_up_assigned_to=assigned_to)
        
        return query.order_by(cls.feedback_date).all()

    def update_feedback(self, session, **kwargs):
        """Update feedback details."""
        try:
            # Handle JSON fields
            if 'structured_responses' in kwargs and isinstance(kwargs['structured_responses'], dict):
                kwargs['structured_responses'] = json.dumps(kwargs['structured_responses'])
            
            for key, value in kwargs.items():
                if hasattr(self, key):
                    setattr(self, key, value)
            
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Updated feedback: {self.feedback_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error updating feedback: {e}")
            return False, str(e)

    def assign_follow_up(self, session, assigned_to, notes=None):
        """Assign follow-up action for this feedback."""
        try:
            self.requires_follow_up = True
            self.follow_up_assigned_to = assigned_to
            
            if notes:
                self.follow_up_notes = notes
            
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Assigned follow-up for feedback: {self.feedback_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error assigning follow-up: {e}")
            return False, str(e)

    def complete_follow_up(self, session, completion_notes=None):
        """Mark follow-up as completed."""
        try:
            self.follow_up_completed = True
            self.follow_up_date = datetime.now()
            
            if completion_notes:
                existing_notes = self.follow_up_notes or ""
                self.follow_up_notes = f"{existing_notes}\n\nCompleted: {completion_notes}"
            
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Completed follow-up for feedback: {self.feedback_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error completing follow-up: {e}")
            return False, str(e)

    def archive_feedback(self, session):
        """Archive the feedback."""
        try:
            self.status = 'ARCHIVED'
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Archived feedback: {self.feedback_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error archiving feedback: {e}")
            return False, str(e)

    def flag_feedback(self, session, reason=None):
        """Flag feedback for review."""
        try:
            self.status = 'FLAGGED'
            
            if reason:
                existing_notes = self.follow_up_notes or ""
                self.follow_up_notes = f"{existing_notes}\n\nFlagged: {reason}"
            
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Flagged feedback: {self.feedback_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error flagging feedback: {e}")
            return False, str(e)

    def get_average_rating(self):
        """Calculate average rating across all rating fields."""
        ratings = [
            self.overall_rating,
            self.process_rating,
            self.communication_rating,
            self.support_rating,
            self.training_rating
        ]
        
        valid_ratings = [r for r in ratings if r is not None]
        
        if not valid_ratings:
            return None
        
        return round(sum(valid_ratings) / len(valid_ratings), 2)

    def get_structured_responses_dict(self):
        """Get structured responses as dictionary."""
        try:
            return json.loads(self.structured_responses) if self.structured_responses else {}
        except:
            return {}

    def set_structured_responses(self, responses_dict):
        """Set structured responses from dictionary."""
        try:
            self.structured_responses = json.dumps(responses_dict) if responses_dict else None
        except:
            self.structured_responses = None

    @classmethod
    def get_feedback_statistics(cls, session, instance_id=None, feedback_type=None, date_from=None, date_to=None):
        """Get feedback statistics."""
        try:
            query = session.query(cls).filter_by(status='ACTIVE')
            
            if instance_id:
                query = query.filter_by(instance_id=instance_id)
            
            if feedback_type:
                query = query.filter_by(feedback_type=feedback_type)
            
            if date_from:
                query = query.filter(cls.feedback_date >= date_from)
            
            if date_to:
                query = query.filter(cls.feedback_date <= date_to)
            
            feedback_entries = query.all()
            
            if not feedback_entries:
                return {
                    "total_feedback": 0,
                    "average_overall_rating": 0,
                    "average_process_rating": 0,
                    "average_communication_rating": 0,
                    "average_support_rating": 0,
                    "average_training_rating": 0,
                    "recommendation_rate": 0,
                    "follow_up_required": 0
                }
            
            total_feedback = len(feedback_entries)
            
            # Calculate averages
            overall_ratings = [f.overall_rating for f in feedback_entries if f.overall_rating]
            process_ratings = [f.process_rating for f in feedback_entries if f.process_rating]
            communication_ratings = [f.communication_rating for f in feedback_entries if f.communication_rating]
            support_ratings = [f.support_rating for f in feedback_entries if f.support_rating]
            training_ratings = [f.training_rating for f in feedback_entries if f.training_rating]
            
            recommendations = [f.would_recommend for f in feedback_entries if f.would_recommend is not None]
            follow_ups = [f for f in feedback_entries if f.requires_follow_up and not f.follow_up_completed]
            
            return {
                "total_feedback": total_feedback,
                "average_overall_rating": round(sum(overall_ratings) / len(overall_ratings), 2) if overall_ratings else 0,
                "average_process_rating": round(sum(process_ratings) / len(process_ratings), 2) if process_ratings else 0,
                "average_communication_rating": round(sum(communication_ratings) / len(communication_ratings), 2) if communication_ratings else 0,
                "average_support_rating": round(sum(support_ratings) / len(support_ratings), 2) if support_ratings else 0,
                "average_training_rating": round(sum(training_ratings) / len(training_ratings), 2) if training_ratings else 0,
                "recommendation_rate": round(sum(recommendations) / len(recommendations) * 100, 2) if recommendations else 0,
                "follow_up_required": len(follow_ups)
            }
            
        except Exception as e:
            current_app.logger.error(f"Error getting feedback statistics: {e}")
            return {
                "total_feedback": 0,
                "average_overall_rating": 0,
                "average_process_rating": 0,
                "average_communication_rating": 0,
                "average_support_rating": 0,
                "average_training_rating": 0,
                "recommendation_rate": 0,
                "follow_up_required": 0
            }

    @classmethod
    def get_feedback_by_rating_range(cls, session, min_rating, max_rating, rating_type='overall_rating'):
        """Get feedback within a specific rating range."""
        rating_column = getattr(cls, rating_type, cls.overall_rating)
        
        return session.query(cls).filter(
            rating_column >= min_rating,
            rating_column <= max_rating,
            cls.status == 'ACTIVE'
        ).order_by(cls.feedback_date.desc()).all()

    def update_sentiment(self, session, sentiment_score, sentiment_label):
        """Update sentiment analysis results."""
        try:
            self.sentiment_score = sentiment_score
            self.sentiment_label = sentiment_label
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Updated sentiment for feedback: {self.feedback_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error updating sentiment: {e}")
            return False, str(e)
