from application.database import db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime, date, timedelta
from flask import current_app
from decimal import Decimal
import json


class OnboardingInstance(db.Model):
    """
    Active onboarding process for a specific employee.
    This tracks the progress of an employee through their onboarding workflow.
    """
    __tablename__ = 'onboarding_instances'

    instance_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    employee_id = db.Column(UUID(as_uuid=True), nullable=False, index=True)
    workflow_id = db.Column(UUID(as_uuid=True), db.ForeignKey('onboarding_workflows.workflow_id'), nullable=False)
    
    # Timeline
    start_date = db.Column(db.Date, nullable=False, default=date.today)
    expected_completion_date = db.Column(db.Date)
    actual_completion_date = db.Column(db.Date)
    first_day_date = db.Column(db.Date)  # Employee's first day of work
    
    # Status tracking
    status = db.Column(db.String(50), default='NOT_STARTED')  # NOT_STARTED, IN_PROGRESS, COMPLETED, PAUSED, CANCELLED
    completion_percentage = db.Column(db.Numeric(5, 2), default=0)
    current_phase = db.Column(db.String(100))  # PRE_BOARDING, FIRST_DAY, FIRST_WEEK, FIRST_MONTH
    
    # Assignments
    assigned_hr = db.Column(UUID(as_uuid=True))  # HR person responsible
    assigned_manager = db.Column(UUID(as_uuid=True))  # Direct manager
    assigned_buddy = db.Column(UUID(as_uuid=True))  # Buddy/mentor assigned
    
    # Progress tracking
    total_tasks = db.Column(db.Integer, default=0)
    completed_tasks = db.Column(db.Integer, default=0)
    overdue_tasks = db.Column(db.Integer, default=0)
    total_documents = db.Column(db.Integer, default=0)
    submitted_documents = db.Column(db.Integer, default=0)
    verified_documents = db.Column(db.Integer, default=0)
    
    # Feedback and notes
    employee_feedback = db.Column(db.Text)
    manager_feedback = db.Column(db.Text)
    hr_notes = db.Column(db.Text)
    overall_rating = db.Column(db.Integer)  # 1-5 rating of onboarding experience
    
    # System tracking
    welcome_email_sent = db.Column(db.Boolean, default=False)
    welcome_email_sent_date = db.Column(db.DateTime)
    system_accounts_created = db.Column(db.Boolean, default=False)
    equipment_assigned = db.Column(db.Boolean, default=False)
    
    # Metadata
    created_by = db.Column(UUID(as_uuid=True))
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    completed_by = db.Column(UUID(as_uuid=True))

    # Relationships
    tasks = db.relationship('OnboardingTask', backref='instance', lazy='dynamic', cascade='all, delete-orphan')
    documents = db.relationship('OnboardingDocument', backref='instance', lazy='dynamic', cascade='all, delete-orphan')
    assets = db.relationship('OnboardingAsset', backref='instance', lazy='dynamic', cascade='all, delete-orphan')
    feedback_entries = db.relationship('OnboardingFeedback', backref='instance', lazy='dynamic', cascade='all, delete-orphan')

    def to_dict(self):
        """Dictionary representation of the onboarding instance."""
        return {
            "instance_id": str(self.instance_id),
            "employee_id": str(self.employee_id),
            "workflow_id": str(self.workflow_id),
            "start_date": self.start_date.strftime('%Y-%m-%d') if self.start_date else None,
            "expected_completion_date": self.expected_completion_date.strftime('%Y-%m-%d') if self.expected_completion_date else None,
            "actual_completion_date": self.actual_completion_date.strftime('%Y-%m-%d') if self.actual_completion_date else None,
            "first_day_date": self.first_day_date.strftime('%Y-%m-%d') if self.first_day_date else None,
            "status": self.status,
            "completion_percentage": float(self.completion_percentage) if self.completion_percentage else 0,
            "current_phase": self.current_phase,
            "assigned_hr": str(self.assigned_hr) if self.assigned_hr else None,
            "assigned_manager": str(self.assigned_manager) if self.assigned_manager else None,
            "assigned_buddy": str(self.assigned_buddy) if self.assigned_buddy else None,
            "total_tasks": self.total_tasks,
            "completed_tasks": self.completed_tasks,
            "overdue_tasks": self.overdue_tasks,
            "total_documents": self.total_documents,
            "submitted_documents": self.submitted_documents,
            "verified_documents": self.verified_documents,
            "employee_feedback": self.employee_feedback,
            "manager_feedback": self.manager_feedback,
            "hr_notes": self.hr_notes,
            "overall_rating": self.overall_rating,
            "welcome_email_sent": self.welcome_email_sent,
            "welcome_email_sent_date": self.welcome_email_sent_date.strftime('%Y-%m-%d %H:%M:%S') if self.welcome_email_sent_date else None,
            "system_accounts_created": self.system_accounts_created,
            "equipment_assigned": self.equipment_assigned,
            "created_by": str(self.created_by) if self.created_by else None,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            "completed_by": str(self.completed_by) if self.completed_by else None,
            "days_since_start": (date.today() - self.start_date).days if self.start_date else 0,
            "is_overdue": self.is_overdue(),
            "days_overdue": self.get_days_overdue()
        }

    @classmethod
    def create_instance(cls, session, **kwargs):
        """Create a new onboarding instance."""
        try:
            instance = cls(**kwargs)
            session.add(instance)
            session.flush()
            
            # Calculate expected completion date based on workflow
            if instance.workflow and not instance.expected_completion_date:
                instance.expected_completion_date = instance.start_date + timedelta(days=instance.workflow.duration_days)
            
            # Initialize task and document counts
            instance.update_progress_counts(session)
            
            current_app.logger.info(f"Created onboarding instance for employee: {instance.employee_id}")
            return instance, None
            
        except Exception as e:
            current_app.logger.error(f"Error creating onboarding instance: {e}")
            return None, str(e)

    @classmethod
    def get_instance_by_employee(cls, session, employee_id, active_only=True):
        """Get onboarding instance for an employee."""
        query = session.query(cls).filter_by(employee_id=employee_id)
        
        if active_only:
            query = query.filter(cls.status.in_(['NOT_STARTED', 'IN_PROGRESS', 'PAUSED']))
        
        return query.order_by(cls.created_at.desc()).first()

    @classmethod
    def get_instance_by_id(cls, session, instance_id):
        """Get instance by ID."""
        return session.query(cls).filter_by(instance_id=instance_id).first()

    @classmethod
    def get_instances_by_status(cls, session, company_id, status, limit=None):
        """Get instances by status for a company."""
        # This would need to join with employee table to filter by company
        query = session.query(cls).filter_by(status=status)
        
        if limit:
            query = query.limit(limit)
            
        return query.order_by(cls.created_at.desc()).all()

    def update_status(self, session, new_status, updated_by=None):
        """Update instance status."""
        try:
            old_status = self.status
            self.status = new_status
            self.updated_at = datetime.now()
            
            if new_status == 'COMPLETED' and not self.actual_completion_date:
                self.actual_completion_date = date.today()
                self.completed_by = updated_by
                self.completion_percentage = 100
            
            session.commit()
            
            current_app.logger.info(f"Updated onboarding instance {self.instance_id} status from {old_status} to {new_status}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error updating onboarding instance status: {e}")
            return False, str(e)

    def update_progress_counts(self, session):
        """Update task and document progress counts."""
        try:
            # Update task counts
            self.total_tasks = self.tasks.count()
            self.completed_tasks = self.tasks.filter_by(status='COMPLETED').count()
            self.overdue_tasks = self.tasks.filter(
                OnboardingTask.due_date < date.today(),
                OnboardingTask.status.in_(['PENDING', 'IN_PROGRESS'])
            ).count()
            
            # Update document counts
            self.total_documents = self.documents.count()
            self.submitted_documents = self.documents.filter(
                OnboardingDocument.upload_date.isnot(None)
            ).count()
            self.verified_documents = self.documents.filter_by(
                verification_status='VERIFIED'
            ).count()
            
            # Calculate completion percentage
            if self.total_tasks > 0:
                self.completion_percentage = round(
                    (self.completed_tasks / self.total_tasks) * 100, 2
                )
            
            # Update current phase based on progress
            self.update_current_phase()
            
            # Update overall status
            if self.completion_percentage == 100:
                self.status = 'COMPLETED'
                if not self.actual_completion_date:
                    self.actual_completion_date = date.today()
            elif self.completion_percentage > 0:
                self.status = 'IN_PROGRESS'
            
            self.updated_at = datetime.now()
            session.commit()
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error updating progress counts: {e}")

    def update_current_phase(self):
        """Update current phase based on days since start."""
        if not self.start_date:
            return
        
        days_since_start = (date.today() - self.start_date).days
        
        if days_since_start < 0:
            self.current_phase = 'PRE_BOARDING'
        elif days_since_start == 0:
            self.current_phase = 'FIRST_DAY'
        elif days_since_start <= 7:
            self.current_phase = 'FIRST_WEEK'
        elif days_since_start <= 30:
            self.current_phase = 'FIRST_MONTH'
        else:
            self.current_phase = 'ONGOING'

    def is_overdue(self):
        """Check if onboarding is overdue."""
        if not self.expected_completion_date or self.status == 'COMPLETED':
            return False
        
        return date.today() > self.expected_completion_date

    def get_days_overdue(self):
        """Get number of days overdue."""
        if not self.is_overdue():
            return 0
        
        return (date.today() - self.expected_completion_date).days

    def get_days_remaining(self):
        """Get number of days remaining until expected completion."""
        if not self.expected_completion_date or self.status == 'COMPLETED':
            return 0
        
        days_remaining = (self.expected_completion_date - date.today()).days
        return max(0, days_remaining)

    def add_feedback(self, session, feedback_text, feedback_type, provided_by, rating=None):
        """Add feedback to the onboarding instance."""
        try:
            from application.Models.onboarding.onboarding_feedback import OnboardingFeedback
            
            feedback = OnboardingFeedback(
                instance_id=self.instance_id,
                feedback_text=feedback_text,
                feedback_type=feedback_type,
                provided_by=provided_by,
                rating=rating
            )
            
            session.add(feedback)
            
            # Update instance-level feedback fields
            if feedback_type == 'EMPLOYEE':
                self.employee_feedback = feedback_text
                if rating:
                    self.overall_rating = rating
            elif feedback_type == 'MANAGER':
                self.manager_feedback = feedback_text
            
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Added feedback to onboarding instance: {self.instance_id}")
            return feedback, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error adding feedback: {e}")
            return None, str(e)

    def send_welcome_email(self, session):
        """Mark welcome email as sent."""
        try:
            self.welcome_email_sent = True
            self.welcome_email_sent_date = datetime.now()
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Marked welcome email as sent for instance: {self.instance_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error marking welcome email as sent: {e}")
            return False, str(e)

    def get_next_tasks(self, session, limit=5):
        """Get next tasks that can be started."""
        from application.Models.onboarding.onboarding_task import OnboardingTask
        
        return session.query(OnboardingTask).filter(
            OnboardingTask.instance_id == self.instance_id,
            OnboardingTask.status == 'PENDING'
        ).order_by(OnboardingTask.due_date, OnboardingTask.task_order).limit(limit).all()

    def get_overdue_tasks(self, session):
        """Get all overdue tasks."""
        from application.Models.onboarding.onboarding_task import OnboardingTask
        
        return session.query(OnboardingTask).filter(
            OnboardingTask.instance_id == self.instance_id,
            OnboardingTask.due_date < date.today(),
            OnboardingTask.status.in_(['PENDING', 'IN_PROGRESS'])
        ).order_by(OnboardingTask.due_date).all()

    def get_pending_documents(self, session):
        """Get all pending document submissions."""
        from application.Models.onboarding.onboarding_document import OnboardingDocument
        
        return session.query(OnboardingDocument).filter(
            OnboardingDocument.instance_id == self.instance_id,
            OnboardingDocument.verification_status.in_(['PENDING', 'REJECTED'])
        ).all()
