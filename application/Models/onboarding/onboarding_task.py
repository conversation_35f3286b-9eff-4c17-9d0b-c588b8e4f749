from application.database import db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime, date, timedelta
from flask import current_app
import json


class OnboardingTask(db.Model):
    """
    Individual tasks assigned to employees, HR, managers, or other roles during onboarding.
    These are instances of workflow task templates.
    """
    __tablename__ = 'onboarding_tasks'

    task_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    instance_id = db.Column(UUID(as_uuid=True), db.ForeignKey('onboarding_instances.instance_id'), nullable=False)
    task_template_id = db.Column(UUID(as_uuid=True), db.ForeignKey('onboarding_workflow_tasks.task_template_id'), nullable=False)
    
    # Task details (copied from template for historical consistency)
    task_name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    instructions = db.Column(db.Text)
    completion_criteria = db.Column(db.Text)
    
    # Assignment
    assigned_to_role = db.Column(db.String(100), nullable=False)
    assigned_to_user = db.Column(UUID(as_uuid=True))  # Specific user if assigned
    assigned_by = db.Column(UUID(as_uuid=True))
    assigned_date = db.Column(db.DateTime, default=datetime.now)
    
    # Timing
    due_date = db.Column(db.Date, nullable=False)
    estimated_duration_hours = db.Column(db.Integer, default=1)
    started_date = db.Column(db.DateTime)
    completed_date = db.Column(db.DateTime)
    
    # Status and progress
    status = db.Column(db.String(50), default='PENDING')  # PENDING, IN_PROGRESS, COMPLETED, SKIPPED, CANCELLED
    completion_percentage = db.Column(db.Integer, default=0)
    is_mandatory = db.Column(db.Boolean, default=True)
    is_blocking = db.Column(db.Boolean, default=False)
    task_type = db.Column(db.String(100), default='GENERAL')
    task_category = db.Column(db.String(100))
    task_order = db.Column(db.Integer, default=1)
    
    # Completion details
    completed_by = db.Column(UUID(as_uuid=True))
    completion_notes = db.Column(db.Text)
    completion_attachments = db.Column(db.Text)  # JSON array of file paths
    verification_required = db.Column(db.Boolean, default=False)
    verified_by = db.Column(UUID(as_uuid=True))
    verification_date = db.Column(db.DateTime)
    verification_notes = db.Column(db.Text)
    
    # Reminders and notifications
    reminder_sent = db.Column(db.Boolean, default=False)
    reminder_sent_date = db.Column(db.DateTime)
    escalation_sent = db.Column(db.Boolean, default=False)
    escalation_sent_date = db.Column(db.DateTime)
    
    # External system integration
    external_system = db.Column(db.String(100))
    external_reference = db.Column(db.String(255))
    external_status = db.Column(db.String(100))
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    def to_dict(self):
        """Dictionary representation of the task."""
        return {
            "task_id": str(self.task_id),
            "instance_id": str(self.instance_id),
            "task_template_id": str(self.task_template_id),
            "task_name": self.task_name,
            "description": self.description,
            "instructions": self.instructions,
            "completion_criteria": self.completion_criteria,
            "assigned_to_role": self.assigned_to_role,
            "assigned_to_user": str(self.assigned_to_user) if self.assigned_to_user else None,
            "assigned_by": str(self.assigned_by) if self.assigned_by else None,
            "assigned_date": self.assigned_date.strftime('%Y-%m-%d %H:%M:%S') if self.assigned_date else None,
            "due_date": self.due_date.strftime('%Y-%m-%d') if self.due_date else None,
            "estimated_duration_hours": self.estimated_duration_hours,
            "started_date": self.started_date.strftime('%Y-%m-%d %H:%M:%S') if self.started_date else None,
            "completed_date": self.completed_date.strftime('%Y-%m-%d %H:%M:%S') if self.completed_date else None,
            "status": self.status,
            "completion_percentage": self.completion_percentage,
            "is_mandatory": self.is_mandatory,
            "is_blocking": self.is_blocking,
            "task_type": self.task_type,
            "task_category": self.task_category,
            "task_order": self.task_order,
            "completed_by": str(self.completed_by) if self.completed_by else None,
            "completion_notes": self.completion_notes,
            "completion_attachments": json.loads(self.completion_attachments) if self.completion_attachments else [],
            "verification_required": self.verification_required,
            "verified_by": str(self.verified_by) if self.verified_by else None,
            "verification_date": self.verification_date.strftime('%Y-%m-%d %H:%M:%S') if self.verification_date else None,
            "verification_notes": self.verification_notes,
            "reminder_sent": self.reminder_sent,
            "reminder_sent_date": self.reminder_sent_date.strftime('%Y-%m-%d %H:%M:%S') if self.reminder_sent_date else None,
            "escalation_sent": self.escalation_sent,
            "escalation_sent_date": self.escalation_sent_date.strftime('%Y-%m-%d %H:%M:%S') if self.escalation_sent_date else None,
            "external_system": self.external_system,
            "external_reference": self.external_reference,
            "external_status": self.external_status,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            "is_overdue": self.is_overdue(),
            "days_overdue": self.get_days_overdue(),
            "can_be_started": self.can_be_started()
        }

    @classmethod
    def create_task(cls, session, **kwargs):
        """Create a new onboarding task."""
        try:
            # Handle JSON fields
            if 'completion_attachments' in kwargs and isinstance(kwargs['completion_attachments'], list):
                kwargs['completion_attachments'] = json.dumps(kwargs['completion_attachments'])
            
            task = cls(**kwargs)
            session.add(task)
            session.flush()
            
            current_app.logger.info(f"Created onboarding task: {task.task_name}")
            return task, None
            
        except Exception as e:
            current_app.logger.error(f"Error creating onboarding task: {e}")
            return None, str(e)

    @classmethod
    def get_tasks_by_instance(cls, session, instance_id, status=None):
        """Get all tasks for an onboarding instance."""
        query = session.query(cls).filter_by(instance_id=instance_id)
        
        if status:
            if isinstance(status, list):
                query = query.filter(cls.status.in_(status))
            else:
                query = query.filter_by(status=status)
        
        return query.order_by(cls.task_order, cls.due_date).all()

    @classmethod
    def get_tasks_by_assignee(cls, session, assigned_to_user=None, assigned_to_role=None, status=None):
        """Get tasks assigned to a specific user or role."""
        query = session.query(cls)
        
        if assigned_to_user:
            query = query.filter_by(assigned_to_user=assigned_to_user)
        elif assigned_to_role:
            query = query.filter_by(assigned_to_role=assigned_to_role)
        
        if status:
            if isinstance(status, list):
                query = query.filter(cls.status.in_(status))
            else:
                query = query.filter_by(status=status)
        
        return query.order_by(cls.due_date, cls.task_order).all()

    @classmethod
    def get_task_by_id(cls, session, task_id):
        """Get task by ID."""
        return session.query(cls).filter_by(task_id=task_id).first()

    def start_task(self, session, started_by=None):
        """Start the task."""
        try:
            if self.status != 'PENDING':
                return False, f"Task cannot be started. Current status: {self.status}"
            
            self.status = 'IN_PROGRESS'
            self.started_date = datetime.now()
            self.completion_percentage = 10  # Indicate task has been started
            
            if started_by:
                self.assigned_to_user = started_by
            
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Started task: {self.task_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error starting task: {e}")
            return False, str(e)

    def complete_task(self, session, completed_by, completion_notes=None, attachments=None):
        """Complete the task."""
        try:
            if self.status == 'COMPLETED':
                return False, "Task is already completed"
            
            self.status = 'COMPLETED'
            self.completed_date = datetime.now()
            self.completed_by = completed_by
            self.completion_percentage = 100
            
            if completion_notes:
                self.completion_notes = completion_notes
            
            if attachments:
                self.completion_attachments = json.dumps(attachments)
            
            self.updated_at = datetime.now()
            session.commit()
            
            # Update instance progress
            self.instance.update_progress_counts(session)
            
            current_app.logger.info(f"Completed task: {self.task_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error completing task: {e}")
            return False, str(e)

    def skip_task(self, session, skipped_by, reason=None):
        """Skip the task (for non-mandatory tasks)."""
        try:
            if self.is_mandatory:
                return False, "Mandatory tasks cannot be skipped"
            
            self.status = 'SKIPPED'
            self.completed_date = datetime.now()
            self.completed_by = skipped_by
            self.completion_percentage = 100
            
            if reason:
                self.completion_notes = f"Skipped: {reason}"
            
            self.updated_at = datetime.now()
            session.commit()
            
            # Update instance progress
            self.instance.update_progress_counts(session)
            
            current_app.logger.info(f"Skipped task: {self.task_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error skipping task: {e}")
            return False, str(e)

    def verify_task(self, session, verified_by, verification_notes=None, approved=True):
        """Verify task completion."""
        try:
            if not self.verification_required:
                return False, "Task does not require verification"
            
            if self.status != 'COMPLETED':
                return False, "Task must be completed before verification"
            
            self.verified_by = verified_by
            self.verification_date = datetime.now()
            self.verification_notes = verification_notes
            
            if not approved:
                # Reset task to in-progress if verification failed
                self.status = 'IN_PROGRESS'
                self.completion_percentage = 50
                self.completed_date = None
                self.completed_by = None
            
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Verified task: {self.task_id} - Approved: {approved}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error verifying task: {e}")
            return False, str(e)

    def send_reminder(self, session):
        """Mark reminder as sent."""
        try:
            self.reminder_sent = True
            self.reminder_sent_date = datetime.now()
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Sent reminder for task: {self.task_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error sending reminder: {e}")
            return False, str(e)

    def send_escalation(self, session):
        """Mark escalation as sent."""
        try:
            self.escalation_sent = True
            self.escalation_sent_date = datetime.now()
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Sent escalation for task: {self.task_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error sending escalation: {e}")
            return False, str(e)

    def is_overdue(self):
        """Check if task is overdue."""
        if self.status in ['COMPLETED', 'SKIPPED', 'CANCELLED']:
            return False
        
        return date.today() > self.due_date

    def get_days_overdue(self):
        """Get number of days overdue."""
        if not self.is_overdue():
            return 0
        
        return (date.today() - self.due_date).days

    def can_be_started(self):
        """Check if task can be started based on dependencies."""
        if self.status != 'PENDING':
            return False
        
        # Check if template has dependencies
        if hasattr(self, 'template') and self.template:
            return self.template.can_be_started(db.session, self.instance_id)
        
        return True

    def update_progress(self, session, percentage, notes=None):
        """Update task progress."""
        try:
            if percentage < 0 or percentage > 100:
                return False, "Progress percentage must be between 0 and 100"
            
            self.completion_percentage = percentage
            
            if notes:
                self.completion_notes = notes
            
            # Update status based on progress
            if percentage == 0:
                self.status = 'PENDING'
            elif percentage == 100:
                self.status = 'COMPLETED'
                self.completed_date = datetime.now()
            else:
                self.status = 'IN_PROGRESS'
                if not self.started_date:
                    self.started_date = datetime.now()
            
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Updated task progress: {self.task_id} - {percentage}%")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error updating task progress: {e}")
            return False, str(e)

    def assign_to_user(self, session, user_id, assigned_by=None):
        """Assign task to a specific user."""
        try:
            self.assigned_to_user = user_id
            self.assigned_by = assigned_by
            self.assigned_date = datetime.now()
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Assigned task {self.task_id} to user {user_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error assigning task: {e}")
            return False, str(e)
