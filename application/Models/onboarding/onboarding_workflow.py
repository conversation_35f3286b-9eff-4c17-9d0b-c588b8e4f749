from application.database import db
from sqlalchemy.dialects.postgresql import UUI<PERSON>
import uuid
from datetime import datetime, date, timedelta
from flask import current_app
from decimal import Decimal
import json


class OnboardingWorkflow(db.Model):
    """
    Onboarding workflow templates that companies can create and customize.
    Each company can have multiple workflow templates for different roles/departments.
    """
    __tablename__ = 'onboarding_workflows'

    workflow_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    company_id = db.Column(UUID(as_uuid=True), nullable=False, index=True)
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    workflow_type = db.Column(db.String(100), default='STANDARD')  # STANDARD, EXECUTIVE, INTERN, CONTRACTOR
    department = db.Column(db.String(100))  # Optional department-specific workflow
    position_level = db.Column(db.String(50))  # ENTRY, MID, SENIOR, EXECUTIVE
    
    # Workflow configuration
    duration_days = db.Column(db.Integer, default=30)  # Expected completion time
    auto_start = db.Column(db.Boolean, default=True)  # Auto-start when employee is created
    requires_manager_approval = db.Column(db.Boolean, default=False)
    requires_hr_approval = db.Column(db.Boolean, default=True)
    
    # Workflow settings
    send_welcome_email = db.Column(db.Boolean, default=True)
    create_system_accounts = db.Column(db.Boolean, default=True)
    assign_buddy = db.Column(db.Boolean, default=False)
    schedule_orientation = db.Column(db.Boolean, default=True)
    
    # Status and metadata
    is_active = db.Column(db.Boolean, default=True)
    is_default = db.Column(db.Boolean, default=False)  # Default workflow for new employees
    version = db.Column(db.String(20), default='1.0')
    created_by = db.Column(UUID(as_uuid=True))
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # Relationships
    instances = db.relationship('OnboardingInstance', backref='workflow', lazy='dynamic')
    tasks = db.relationship('OnboardingWorkflowTask', backref='workflow', lazy='dynamic', cascade='all, delete-orphan')
    document_requirements = db.relationship('OnboardingDocumentRequirement', backref='workflow', lazy='dynamic', cascade='all, delete-orphan')

    def to_dict(self):
        """Dictionary representation of the workflow."""
        return {
            "workflow_id": str(self.workflow_id),
            "company_id": str(self.company_id),
            "name": self.name,
            "description": self.description,
            "workflow_type": self.workflow_type,
            "department": self.department,
            "position_level": self.position_level,
            "duration_days": self.duration_days,
            "auto_start": self.auto_start,
            "requires_manager_approval": self.requires_manager_approval,
            "requires_hr_approval": self.requires_hr_approval,
            "send_welcome_email": self.send_welcome_email,
            "create_system_accounts": self.create_system_accounts,
            "assign_buddy": self.assign_buddy,
            "schedule_orientation": self.schedule_orientation,
            "is_active": self.is_active,
            "is_default": self.is_default,
            "version": self.version,
            "created_by": str(self.created_by) if self.created_by else None,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            "task_count": self.tasks.count(),
            "document_requirement_count": self.document_requirements.count()
        }

    @classmethod
    def create_workflow(cls, session, **kwargs):
        """Create a new onboarding workflow."""
        try:
            workflow = cls(**kwargs)
            session.add(workflow)
            session.flush()  # Get the ID without committing
            
            current_app.logger.info(f"Created onboarding workflow: {workflow.name}")
            return workflow, None
            
        except Exception as e:
            current_app.logger.error(f"Error creating onboarding workflow: {e}")
            return None, str(e)

    @classmethod
    def get_workflows_by_company(cls, session, company_id, include_inactive=False):
        """Get all workflows for a company."""
        query = session.query(cls).filter_by(company_id=company_id)
        
        if not include_inactive:
            query = query.filter_by(is_active=True)
            
        return query.order_by(cls.name).all()

    @classmethod
    def get_default_workflow(cls, session, company_id, department=None, position_level=None):
        """Get the default workflow for a company, optionally filtered by department/level."""
        query = session.query(cls).filter_by(
            company_id=company_id,
            is_active=True,
            is_default=True
        )
        
        if department:
            query = query.filter_by(department=department)
        
        if position_level:
            query = query.filter_by(position_level=position_level)
            
        return query.first()

    @classmethod
    def get_workflow_by_id(cls, session, workflow_id):
        """Get workflow by ID."""
        return session.query(cls).filter_by(workflow_id=workflow_id).first()

    def update_workflow(self, session, **kwargs):
        """Update workflow details."""
        try:
            for key, value in kwargs.items():
                if hasattr(self, key):
                    setattr(self, key, value)
            
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Updated onboarding workflow: {self.workflow_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error updating onboarding workflow: {e}")
            return False, str(e)

    def deactivate_workflow(self, session):
        """Deactivate workflow (soft delete)."""
        try:
            self.is_active = False
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Deactivated onboarding workflow: {self.workflow_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error deactivating onboarding workflow: {e}")
            return False, str(e)

    def clone_workflow(self, session, new_name, created_by=None):
        """Clone workflow with all tasks and document requirements."""
        try:
            # Create new workflow
            new_workflow = OnboardingWorkflow(
                company_id=self.company_id,
                name=new_name,
                description=f"Cloned from: {self.name}",
                workflow_type=self.workflow_type,
                department=self.department,
                position_level=self.position_level,
                duration_days=self.duration_days,
                auto_start=self.auto_start,
                requires_manager_approval=self.requires_manager_approval,
                requires_hr_approval=self.requires_hr_approval,
                send_welcome_email=self.send_welcome_email,
                create_system_accounts=self.create_system_accounts,
                assign_buddy=self.assign_buddy,
                schedule_orientation=self.schedule_orientation,
                created_by=created_by,
                version="1.0"
            )
            
            session.add(new_workflow)
            session.flush()
            
            # Clone tasks
            for task in self.tasks:
                new_task = OnboardingWorkflowTask(
                    workflow_id=new_workflow.workflow_id,
                    task_name=task.task_name,
                    description=task.description,
                    assigned_to_role=task.assigned_to_role,
                    due_days_from_start=task.due_days_from_start,
                    is_mandatory=task.is_mandatory,
                    task_type=task.task_type,
                    task_order=task.task_order,
                    instructions=task.instructions,
                    completion_criteria=task.completion_criteria
                )
                session.add(new_task)
            
            # Clone document requirements
            for doc_req in self.document_requirements:
                new_doc_req = OnboardingDocumentRequirement(
                    workflow_id=new_workflow.workflow_id,
                    document_name=doc_req.document_name,
                    document_code=doc_req.document_code,
                    description=doc_req.description,
                    is_mandatory=doc_req.is_mandatory,
                    file_types_allowed=doc_req.file_types_allowed,
                    max_file_size_mb=doc_req.max_file_size_mb,
                    validation_rules=doc_req.validation_rules,
                    display_order=doc_req.display_order
                )
                session.add(new_doc_req)
            
            session.commit()
            
            current_app.logger.info(f"Cloned workflow {self.workflow_id} to {new_workflow.workflow_id}")
            return new_workflow, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error cloning workflow: {e}")
            return None, str(e)

    def get_completion_stats(self, session):
        """Get completion statistics for this workflow."""
        try:
            total_instances = self.instances.count()
            completed_instances = self.instances.filter_by(status='COMPLETED').count()
            in_progress_instances = self.instances.filter_by(status='IN_PROGRESS').count()
            
            avg_completion_days = session.query(
                db.func.avg(
                    db.func.extract('day', OnboardingInstance.actual_completion_date - OnboardingInstance.start_date)
                )
            ).filter(
                OnboardingInstance.workflow_id == self.workflow_id,
                OnboardingInstance.status == 'COMPLETED'
            ).scalar() or 0
            
            return {
                "total_instances": total_instances,
                "completed_instances": completed_instances,
                "in_progress_instances": in_progress_instances,
                "completion_rate": round(completed_instances / total_instances * 100, 2) if total_instances > 0 else 0,
                "average_completion_days": round(float(avg_completion_days), 1)
            }
            
        except Exception as e:
            current_app.logger.error(f"Error getting workflow completion stats: {e}")
            return {
                "total_instances": 0,
                "completed_instances": 0,
                "in_progress_instances": 0,
                "completion_rate": 0,
                "average_completion_days": 0
            }
