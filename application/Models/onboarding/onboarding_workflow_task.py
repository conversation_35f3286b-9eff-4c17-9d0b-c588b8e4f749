from application.database import db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime, date, timedelta
from flask import current_app
import json


class OnboardingWorkflowTask(db.Model):
    """
    Tasks that are part of an onboarding workflow template.
    These define what needs to be done during onboarding.
    """
    __tablename__ = 'onboarding_workflow_tasks'

    task_template_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    workflow_id = db.Column(UUID(as_uuid=True), db.ForeignKey('onboarding_workflows.workflow_id'), nullable=False)
    
    # Task details
    task_name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    instructions = db.Column(db.Text)  # Detailed instructions for completing the task
    completion_criteria = db.Column(db.Text)  # What constitutes task completion
    
    # Assignment and timing
    assigned_to_role = db.Column(db.String(100), nullable=False)  # HR, MANAGER, EMPLOYEE, IT, FINANCE
    due_days_from_start = db.Column(db.Integer, default=1)  # Days from onboarding start
    estimated_duration_hours = db.Column(db.Integer, default=1)  # Estimated time to complete
    
    # Task properties
    is_mandatory = db.Column(db.Boolean, default=True)
    is_blocking = db.Column(db.Boolean, default=False)  # Blocks other tasks if not completed
    task_type = db.Column(db.String(100), default='GENERAL')  # DOCUMENT, SYSTEM, TRAINING, MEETING, EQUIPMENT
    task_category = db.Column(db.String(100))  # COMPLIANCE, SETUP, ORIENTATION, TRAINING
    
    # Ordering and dependencies
    task_order = db.Column(db.Integer, default=1)
    depends_on_tasks = db.Column(db.Text)  # JSON array of task IDs this depends on
    
    # Automation settings
    auto_assign = db.Column(db.Boolean, default=True)
    send_reminder = db.Column(db.Boolean, default=True)
    reminder_days_before = db.Column(db.Integer, default=1)
    
    # External integrations
    external_system = db.Column(db.String(100))  # SLACK, EMAIL, JIRA, etc.
    external_config = db.Column(db.Text)  # JSON configuration for external systems
    
    # Status and metadata
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # Relationships
    task_instances = db.relationship('OnboardingTask', backref='template', lazy='dynamic')

    def to_dict(self):
        """Dictionary representation of the workflow task."""
        return {
            "task_template_id": str(self.task_template_id),
            "workflow_id": str(self.workflow_id),
            "task_name": self.task_name,
            "description": self.description,
            "instructions": self.instructions,
            "completion_criteria": self.completion_criteria,
            "assigned_to_role": self.assigned_to_role,
            "due_days_from_start": self.due_days_from_start,
            "estimated_duration_hours": self.estimated_duration_hours,
            "is_mandatory": self.is_mandatory,
            "is_blocking": self.is_blocking,
            "task_type": self.task_type,
            "task_category": self.task_category,
            "task_order": self.task_order,
            "depends_on_tasks": json.loads(self.depends_on_tasks) if self.depends_on_tasks else [],
            "auto_assign": self.auto_assign,
            "send_reminder": self.send_reminder,
            "reminder_days_before": self.reminder_days_before,
            "external_system": self.external_system,
            "external_config": json.loads(self.external_config) if self.external_config else {},
            "is_active": self.is_active,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

    @classmethod
    def create_task(cls, session, **kwargs):
        """Create a new workflow task."""
        try:
            # Handle JSON fields
            if 'depends_on_tasks' in kwargs and isinstance(kwargs['depends_on_tasks'], list):
                kwargs['depends_on_tasks'] = json.dumps(kwargs['depends_on_tasks'])
            
            if 'external_config' in kwargs and isinstance(kwargs['external_config'], dict):
                kwargs['external_config'] = json.dumps(kwargs['external_config'])
            
            task = cls(**kwargs)
            session.add(task)
            session.flush()
            
            current_app.logger.info(f"Created workflow task: {task.task_name}")
            return task, None
            
        except Exception as e:
            current_app.logger.error(f"Error creating workflow task: {e}")
            return None, str(e)

    @classmethod
    def get_tasks_by_workflow(cls, session, workflow_id, include_inactive=False):
        """Get all tasks for a workflow."""
        query = session.query(cls).filter_by(workflow_id=workflow_id)
        
        if not include_inactive:
            query = query.filter_by(is_active=True)
            
        return query.order_by(cls.task_order, cls.due_days_from_start).all()

    @classmethod
    def get_task_by_id(cls, session, task_template_id):
        """Get task by ID."""
        return session.query(cls).filter_by(task_template_id=task_template_id).first()

    def update_task(self, session, **kwargs):
        """Update task details."""
        try:
            # Handle JSON fields
            if 'depends_on_tasks' in kwargs and isinstance(kwargs['depends_on_tasks'], list):
                kwargs['depends_on_tasks'] = json.dumps(kwargs['depends_on_tasks'])
            
            if 'external_config' in kwargs and isinstance(kwargs['external_config'], dict):
                kwargs['external_config'] = json.dumps(kwargs['external_config'])
            
            for key, value in kwargs.items():
                if hasattr(self, key):
                    setattr(self, key, value)
            
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Updated workflow task: {self.task_template_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error updating workflow task: {e}")
            return False, str(e)

    def deactivate_task(self, session):
        """Deactivate task (soft delete)."""
        try:
            self.is_active = False
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Deactivated workflow task: {self.task_template_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error deactivating workflow task: {e}")
            return False, str(e)

    def get_dependencies(self, session):
        """Get tasks that this task depends on."""
        if not self.depends_on_tasks:
            return []
        
        try:
            dependency_ids = json.loads(self.depends_on_tasks)
            return session.query(OnboardingWorkflowTask).filter(
                OnboardingWorkflowTask.task_template_id.in_(dependency_ids)
            ).all()
        except:
            return []

    def get_dependent_tasks(self, session):
        """Get tasks that depend on this task."""
        return session.query(OnboardingWorkflowTask).filter(
            OnboardingWorkflowTask.workflow_id == self.workflow_id,
            OnboardingWorkflowTask.depends_on_tasks.contains(str(self.task_template_id))
        ).all()

    def can_be_started(self, session, onboarding_instance_id):
        """Check if this task can be started based on dependencies."""
        if not self.depends_on_tasks:
            return True
        
        try:
            dependency_ids = json.loads(self.depends_on_tasks)
            
            # Check if all dependent tasks are completed
            from application.Models.onboarding.onboarding_task import OnboardingTask
            
            completed_dependencies = session.query(OnboardingTask).filter(
                OnboardingTask.instance_id == onboarding_instance_id,
                OnboardingTask.task_template_id.in_(dependency_ids),
                OnboardingTask.status == 'COMPLETED'
            ).count()
            
            return completed_dependencies == len(dependency_ids)
            
        except Exception as e:
            current_app.logger.error(f"Error checking task dependencies: {e}")
            return False

    def get_completion_stats(self, session):
        """Get completion statistics for this task template."""
        try:
            total_instances = self.task_instances.count()
            completed_instances = self.task_instances.filter_by(status='COMPLETED').count()
            overdue_instances = self.task_instances.filter(
                OnboardingTask.due_date < date.today(),
                OnboardingTask.status.in_(['PENDING', 'IN_PROGRESS'])
            ).count()
            
            avg_completion_days = session.query(
                db.func.avg(
                    db.func.extract('day', OnboardingTask.completed_date - OnboardingTask.assigned_date)
                )
            ).filter(
                OnboardingTask.task_template_id == self.task_template_id,
                OnboardingTask.status == 'COMPLETED'
            ).scalar() or 0
            
            return {
                "total_instances": total_instances,
                "completed_instances": completed_instances,
                "overdue_instances": overdue_instances,
                "completion_rate": round(completed_instances / total_instances * 100, 2) if total_instances > 0 else 0,
                "average_completion_days": round(float(avg_completion_days), 1)
            }
            
        except Exception as e:
            current_app.logger.error(f"Error getting task completion stats: {e}")
            return {
                "total_instances": 0,
                "completed_instances": 0,
                "overdue_instances": 0,
                "completion_rate": 0,
                "average_completion_days": 0
            }

    @classmethod
    def get_tasks_by_role(cls, session, workflow_id, role):
        """Get all tasks assigned to a specific role in a workflow."""
        return session.query(cls).filter_by(
            workflow_id=workflow_id,
            assigned_to_role=role,
            is_active=True
        ).order_by(cls.task_order, cls.due_days_from_start).all()

    @classmethod
    def get_tasks_by_type(cls, session, workflow_id, task_type):
        """Get all tasks of a specific type in a workflow."""
        return session.query(cls).filter_by(
            workflow_id=workflow_id,
            task_type=task_type,
            is_active=True
        ).order_by(cls.task_order, cls.due_days_from_start).all()

    def reorder_tasks(self, session, new_order):
        """Reorder tasks within a workflow."""
        try:
            self.task_order = new_order
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Reordered task {self.task_template_id} to position {new_order}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error reordering task: {e}")
            return False, str(e)
