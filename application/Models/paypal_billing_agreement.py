from application.database import central_db as db
import uuid
from sqlalchemy.dialects.postgresql import UUID, JSONB
from flask import current_app
from datetime import datetime, date
from decimal import Decimal

class PayPalBillingAgreement(db.Model):
    """Model representing PayPal billing agreements for recurring payments (stored in central database)."""
    __tablename__ = 'paypal_billing_agreements'

    agreement_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    company_id = db.Column(db.String(36), db.ForeignKey('companies.company_id'), nullable=False)
    paypal_agreement_id = db.Column(db.String(255), nullable=False, unique=True)  # PayPal's billing agreement ID
    payment_method_type = db.Column(db.String(20), nullable=False)  # 'PAYPAL_ACCOUNT', 'CREDIT_CARD', 'DEBIT_CARD'
    payment_method_details = db.Column(JSONB, nullable=True)  # Store card last 4 digits, PayPal email, etc.
    status = db.Column(db.String(20), default='ACTIVE', nullable=False)  # ACTIVE, CANCELLED, SUSPENDED, PENDING
    payer_info = db.Column(JSONB, nullable=True)  # Store payer information from PayPal
    agreement_details = db.Column(JSONB, nullable=True)  # Store full agreement details from PayPal
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())
    cancelled_at = db.Column(db.DateTime, nullable=True)
    cancelled_reason = db.Column(db.String(255), nullable=True)

    # Indexes for performance
    __table_args__ = (
        db.Index('idx_paypal_agreement_company', 'company_id'),
        db.Index('idx_paypal_agreement_status', 'status'),
        db.Index('idx_paypal_agreement_paypal_id', 'paypal_agreement_id'),
    )

    # Relationships
    company = db.relationship('Company', backref=db.backref('paypal_agreements', lazy='dynamic'))

    def __str__(self):
        """Return a string representation of the object."""
        return f"PayPalBillingAgreement [agreement_id={self.agreement_id}, company_id={self.company_id}, status={self.status}]"

    def to_dict(self, include_company=False, include_sensitive=False):
        """Dictionary representation of the object."""
        data = {
            "agreement_id": str(self.agreement_id),
            "company_id": self.company_id,
            "paypal_agreement_id": self.paypal_agreement_id,
            "payment_method_type": self.payment_method_type,
            "status": self.status,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            "cancelled_at": self.cancelled_at.strftime('%Y-%m-%d %H:%M:%S') if self.cancelled_at else None,
            "cancelled_reason": self.cancelled_reason,
            "is_active": self.is_active()
        }
        
        # Include payment method details (masked for security)
        if self.payment_method_details:
            if include_sensitive:
                data["payment_method_details"] = self.payment_method_details
            else:
                # Mask sensitive information
                masked_details = {}
                if self.payment_method_type in ['CREDIT_CARD', 'DEBIT_CARD']:
                    masked_details = {
                        "card_type": self.payment_method_details.get('card_type'),
                        "last_four": self.payment_method_details.get('last_four'),
                        "expiry_month": self.payment_method_details.get('expiry_month'),
                        "expiry_year": self.payment_method_details.get('expiry_year')
                    }
                elif self.payment_method_type == 'PAYPAL_ACCOUNT':
                    masked_details = {
                        "paypal_email": self.mask_email(self.payment_method_details.get('paypal_email'))
                    }
                data["payment_method_details"] = masked_details
        
        if include_company and self.company:
            data["company"] = {
                "company_name": self.company.company_name,
                "company_tin": self.company.company_tin
            }
            
        return data

    def mask_email(self, email):
        """Mask email address for security."""
        if not email or '@' not in email:
            return email
        
        local, domain = email.split('@', 1)
        if len(local) <= 2:
            masked_local = '*' * len(local)
        else:
            masked_local = local[0] + '*' * (len(local) - 2) + local[-1]
        
        return f"{masked_local}@{domain}"

    def is_active(self):
        """Check if billing agreement is active."""
        return self.status == 'ACTIVE'

    def can_be_charged(self):
        """Check if billing agreement can be used for charging."""
        return self.status in ['ACTIVE'] and self.paypal_agreement_id

    @classmethod
    def create_agreement(cls, company_id, paypal_agreement_id, payment_method_type, 
                        payment_method_details=None, payer_info=None, agreement_details=None, **kwargs):
        """Create a new PayPal billing agreement."""
        try:
            from application.Models.company import Company
            
            # Validate company exists
            company = Company.query.filter_by(company_id=company_id).first()
            if not company:
                return None, "Company not found"

            # Check if agreement already exists
            existing = cls.query.filter_by(paypal_agreement_id=paypal_agreement_id).first()
            if existing:
                return None, "PayPal agreement already exists"

            agreement_data = {
                'company_id': company_id,
                'paypal_agreement_id': paypal_agreement_id,
                'payment_method_type': payment_method_type,
                'payment_method_details': payment_method_details or {},
                'payer_info': payer_info or {},
                'agreement_details': agreement_details or {},
                'status': 'ACTIVE',
                **kwargs
            }

            agreement = cls(**agreement_data)
            db.session.add(agreement)
            db.session.commit()
            
            current_app.logger.info(f"Created PayPal billing agreement {paypal_agreement_id} for company {company_id}")
            return agreement, None
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error creating PayPal billing agreement: {e}")
            return None, str(e)

    @classmethod
    def get_by_company_id(cls, company_id):
        """Get active billing agreement for a company."""
        return cls.query.filter_by(company_id=company_id, status='ACTIVE').first()

    @classmethod
    def get_by_paypal_id(cls, paypal_agreement_id):
        """Get billing agreement by PayPal agreement ID."""
        return cls.query.filter_by(paypal_agreement_id=paypal_agreement_id).first()

    def cancel_agreement(self, reason=None):
        """Cancel the billing agreement."""
        try:
            self.status = 'CANCELLED'
            self.cancelled_at = datetime.now()
            self.cancelled_reason = reason
            
            db.session.commit()
            current_app.logger.info(f"Cancelled PayPal billing agreement {self.paypal_agreement_id}")
            return True, None
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error cancelling billing agreement: {e}")
            return False, str(e)

    def suspend_agreement(self, reason=None):
        """Suspend the billing agreement."""
        try:
            self.status = 'SUSPENDED'
            self.cancelled_reason = reason
            
            db.session.commit()
            current_app.logger.info(f"Suspended PayPal billing agreement {self.paypal_agreement_id}")
            return True, None
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error suspending billing agreement: {e}")
            return False, str(e)

    def reactivate_agreement(self):
        """Reactivate a suspended billing agreement."""
        try:
            if self.status != 'SUSPENDED':
                return False, "Can only reactivate suspended agreements"
                
            self.status = 'ACTIVE'
            self.cancelled_reason = None
            
            db.session.commit()
            current_app.logger.info(f"Reactivated PayPal billing agreement {self.paypal_agreement_id}")
            return True, None
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error reactivating billing agreement: {e}")
            return False, str(e)

    def update_payment_method_details(self, payment_method_details):
        """Update payment method details."""
        try:
            self.payment_method_details = payment_method_details
            db.session.commit()
            return True, None
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error updating payment method details: {e}")
            return False, str(e)

    @classmethod
    def get_active_agreements(cls):
        """Get all active billing agreements."""
        return cls.query.filter_by(status='ACTIVE').all()

    @classmethod
    def get_agreements_for_billing(cls):
        """Get all agreements that can be billed."""
        return cls.query.filter(cls.status.in_(['ACTIVE'])).all()
