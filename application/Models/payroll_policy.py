from application.database import central_db as db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime
from flask import current_app

class PayrollPolicy(db.Model):
    """Model representing time-versioned payroll policies (stored in central database)."""
    __tablename__ = 'payroll_policies'

    policy_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    policy_type_id = db.Column(UUID(as_uuid=True), db.Foreign<PERSON>ey('payroll_policy_types.policy_type_id'), nullable=False)
    country_id = db.Column(UUID(as_uuid=True), db.ForeignKey('countries.country_id'), nullable=False)
    employee_type_id = db.Column(UUID(as_uuid=True), db.<PERSON><PERSON>('employee_types.employee_type_id'), nullable=True)
    version_number = db.Column(db.Integer, nullable=False, default=1)
    effective_from = db.Column(db.Date, nullable=False)
    effective_to = db.Column(db.Date, nullable=True)  # null means currently active
    is_active = db.Column(db.<PERSON>an, default=True, nullable=False)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())
    created_by = db.Column(UUID(as_uuid=True), nullable=True)  # User who created this version
    change_reason = db.Column(db.Text, nullable=True)  # Reason for the policy change

    # Relationships
    policy_type = db.relationship('PayrollPolicyType', backref=db.backref('policies', lazy='dynamic'))
    country = db.relationship('Country', backref=db.backref('payroll_policies', lazy='dynamic'))
    employee_type = db.relationship('EmployeeType', backref=db.backref('payroll_policies', lazy='dynamic'))

    # Unique constraint: one active policy per type/country/employee_type at any time
    __table_args__ = (
        db.Index('idx_payroll_policy_lookup', 'policy_type_id', 'country_id', 'employee_type_id', 'effective_from', 'effective_to'),
    )

    def __str__(self):
        """Return a string representation of the object."""
        return f"PayrollPolicy [policy_id={self.policy_id}, type={self.policy_type.name if self.policy_type else None}, version={self.version_number}]"

    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "policy_id": str(self.policy_id),
            "policy_type_id": str(self.policy_type_id),
            "policy_type_name": self.policy_type.name if self.policy_type else None,
            "policy_type_code": self.policy_type.code if self.policy_type else None,
            "country_id": str(self.country_id),
            "country_name": self.country.name if self.country else None,
            "employee_type_id": str(self.employee_type_id) if self.employee_type_id else None,
            "employee_type_name": self.employee_type.name if self.employee_type else None,
            "version_number": self.version_number,
            "effective_from": self.effective_from.strftime('%Y-%m-%d') if self.effective_from else None,
            "effective_to": self.effective_to.strftime('%Y-%m-%d') if self.effective_to else None,
            "is_active": self.is_active,
            "change_reason": self.change_reason,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

    @classmethod
    def create_policy(cls, **kwargs):
        """Create a new payroll policy."""
        try:
            policy = cls(**kwargs)
            db.session.add(policy)
            db.session.commit()
            current_app.logger.info(f"Created payroll policy: {policy.policy_type.name if policy.policy_type else 'Unknown'} v{policy.version_number}")
            return policy
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error creating payroll policy: {e}")
            return None

    @classmethod
    def get_active_policy(cls, policy_type_code, country_id, employee_type_id=None, date=None):
        """Get the active policy for specific criteria on a given date."""
        if date is None:
            date = datetime.now().date()

        query = cls.query.join(cls.policy_type).filter(
            cls.policy_type.has(code=policy_type_code),
            cls.country_id == country_id,
            cls.effective_from <= date,
            cls.is_active == True
        )

        # Handle employee type filter
        if employee_type_id:
            query = query.filter(cls.employee_type_id == employee_type_id)
        else:
            query = query.filter(cls.employee_type_id.is_(None))

        # Handle effective_to (null means currently active)
        query = query.filter(
            db.or_(
                cls.effective_to.is_(None),
                cls.effective_to >= date
            )
        )

        return query.first()

    @classmethod
    def get_policy_history(cls, policy_type_code, country_id, employee_type_id=None):
        """Get the history of policies for specific criteria."""
        query = cls.query.join(cls.policy_type).filter(
            cls.policy_type.has(code=policy_type_code),
            cls.country_id == country_id
        )

        if employee_type_id:
            query = query.filter(cls.employee_type_id == employee_type_id)
        else:
            query = query.filter(cls.employee_type_id.is_(None))

        return query.order_by(cls.version_number.desc()).all()

    @classmethod
    def create_new_version(cls, existing_policy_id, new_effective_from, **kwargs):
        """Create a new version of an existing policy."""
        try:
            # Get the existing policy
            existing_policy = cls.query.filter_by(policy_id=existing_policy_id).first()
            if not existing_policy:
                return None

            # Set end date for existing policy
            existing_policy.effective_to = new_effective_from
            
            # Get the next version number
            max_version = cls.query.filter(
                cls.policy_type_id == existing_policy.policy_type_id,
                cls.country_id == existing_policy.country_id,
                cls.employee_type_id == existing_policy.employee_type_id
            ).order_by(cls.version_number.desc()).first()
            
            next_version = (max_version.version_number + 1) if max_version else 1

            # Create new policy version
            new_policy_data = {
                'policy_type_id': existing_policy.policy_type_id,
                'country_id': existing_policy.country_id,
                'employee_type_id': existing_policy.employee_type_id,
                'version_number': next_version,
                'effective_from': new_effective_from,
                'effective_to': None,
                **kwargs
            }

            new_policy = cls.create_policy(**new_policy_data)
            
            if new_policy:
                db.session.commit()
                current_app.logger.info(f"Created new policy version: v{next_version}")
                return new_policy
            else:
                db.session.rollback()
                return None

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error creating new policy version: {e}")
            return None

    @classmethod
    def get_by_id(cls, policy_id):
        """Get payroll policy by ID."""
        return cls.query.filter_by(policy_id=policy_id).first()

    @classmethod
    def get_policies_for_country(cls, country_id, date=None):
        """Get all active policies for a country on a specific date."""
        if date is None:
            date = datetime.now().date()

        return cls.query.filter(
            cls.country_id == country_id,
            cls.effective_from <= date,
            db.or_(
                cls.effective_to.is_(None),
                cls.effective_to >= date
            ),
            cls.is_active == True
        ).all()
