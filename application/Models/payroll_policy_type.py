from application.database import central_db as db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from flask import current_app

class PayrollPolicyType(db.Model):
    """Model representing types of payroll policies (stored in central database)."""
    __tablename__ = 'payroll_policy_types'

    policy_type_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    name = db.Column(db.String(255), nullable=False)  # "PAYE", "Employee Pension", "CBHI"
    code = db.Column(db.String(50), unique=True, nullable=False)  # "PAYE", "EMP_PENSION", "CBHI"
    description = db.Column(db.Text, nullable=True)
    calculation_method = db.Column(db.String(50), nullable=False)  # "PROGRESSIVE_TAX", "FLAT_PERCENTAGE", "TIERED"
    applies_to = db.Column(db.String(50), nullable=False)  # "ALL_EMPLOYEES", "EMPLOYEE_TYPE_SPECIFIC"
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    def __str__(self):
        """Return a string representation of the object."""
        return f"PayrollPolicyType [policy_type_id={self.policy_type_id}, name={self.name}, code={self.code}]"

    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "policy_type_id": str(self.policy_type_id),
            "name": self.name,
            "code": self.code,
            "description": self.description,
            "calculation_method": self.calculation_method,
            "applies_to": self.applies_to,
            "is_active": self.is_active,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

    @classmethod
    def create_policy_type(cls, **kwargs):
        """Create a new payroll policy type."""
        try:
            policy_type = cls(**kwargs)
            db.session.add(policy_type)
            db.session.commit()
            current_app.logger.info(f"Created payroll policy type: {policy_type.name} ({policy_type.code})")
            return policy_type
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error creating payroll policy type: {e}")
            return None

    @classmethod
    def get_all_active(cls):
        """Get all active payroll policy types."""
        return cls.query.filter_by(is_active=True).order_by(cls.name).all()

    @classmethod
    def get_by_code(cls, code):
        """Get payroll policy type by code."""
        return cls.query.filter_by(code=code, is_active=True).first()

    @classmethod
    def get_by_id(cls, policy_type_id):
        """Get payroll policy type by ID."""
        return cls.query.filter_by(policy_type_id=policy_type_id, is_active=True).first()

    @classmethod
    def update_policy_type(cls, policy_type_id, **kwargs):
        """Update a payroll policy type."""
        try:
            policy_type = cls.query.filter_by(policy_type_id=policy_type_id).first()
            if not policy_type:
                return None

            for key, value in kwargs.items():
                if hasattr(policy_type, key):
                    setattr(policy_type, key, value)

            db.session.commit()
            current_app.logger.info(f"Updated payroll policy type: {policy_type.name}")
            return policy_type
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error updating payroll policy type: {e}")
            return None
