from application.database import db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime, date
from flask import current_app
from decimal import Decimal
import json


class CompetencyFramework(db.Model):
    """
    Competency frameworks that define skills and behaviors expected at different levels.
    Used for performance evaluation and development planning.
    """
    __tablename__ = 'competency_frameworks'

    framework_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    company_id = db.Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # Framework details
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    framework_type = db.Column(db.String(100), default='GENERAL')  # GENERAL, LEADERSHIP, TECHNICAL, BEHAVIORAL
    
    # Scope and application
    applicable_roles = db.Column(db.Text)  # JSON array of role names/IDs
    applicable_departments = db.Column(db.Text)  # JSON array of department names
    applicable_levels = db.Column(db.Text)  # JSON array of job levels
    
    # Framework configuration
    proficiency_levels = db.Column(db.Text, nullable=False)  # JSON array of proficiency level definitions
    competency_categories = db.Column(db.Text)  # JSON array of competency categories
    
    # Usage settings
    is_default = db.Column(db.Boolean, default=False)
    is_active = db.Column(db.Boolean, default=True)
    version = db.Column(db.String(50), default='1.0')
    
    # Metadata
    created_by = db.Column(UUID(as_uuid=True))
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # Relationships
    competencies = db.relationship('Competency', backref='framework', lazy='dynamic', cascade='all, delete-orphan')

    def to_dict(self):
        """Dictionary representation of the competency framework."""
        return {
            "framework_id": str(self.framework_id),
            "company_id": str(self.company_id),
            "name": self.name,
            "description": self.description,
            "framework_type": self.framework_type,
            "applicable_roles": self.get_applicable_roles_list(),
            "applicable_departments": self.get_applicable_departments_list(),
            "applicable_levels": self.get_applicable_levels_list(),
            "proficiency_levels": self.get_proficiency_levels_list(),
            "competency_categories": self.get_competency_categories_list(),
            "is_default": self.is_default,
            "is_active": self.is_active,
            "version": self.version,
            "created_by": str(self.created_by) if self.created_by else None,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            "competency_count": self.competencies.count()
        }

    @classmethod
    def create_framework(cls, session, **kwargs):
        """Create a new competency framework."""
        try:
            # Handle JSON fields
            json_fields = ['applicable_roles', 'applicable_departments', 'applicable_levels', 
                          'proficiency_levels', 'competency_categories']
            
            for field in json_fields:
                if field in kwargs and isinstance(kwargs[field], list):
                    kwargs[field] = json.dumps(kwargs[field])
            
            framework = cls(**kwargs)
            session.add(framework)
            session.flush()
            
            # If this is set as default, unset other defaults
            if framework.is_default:
                session.query(cls).filter(
                    cls.company_id == framework.company_id,
                    cls.framework_id != framework.framework_id
                ).update({'is_default': False})
            
            session.commit()
            
            current_app.logger.info(f"Created competency framework: {framework.name}")
            return framework, None
            
        except Exception as e:
            current_app.logger.error(f"Error creating competency framework: {e}")
            return None, str(e)

    @classmethod
    def get_frameworks_by_company(cls, session, company_id, include_inactive=False):
        """Get all competency frameworks for a company."""
        query = session.query(cls).filter_by(company_id=company_id)
        
        if not include_inactive:
            query = query.filter_by(is_active=True)
        
        return query.order_by(cls.is_default.desc(), cls.name).all()

    @classmethod
    def get_default_framework(cls, session, company_id):
        """Get the default competency framework for a company."""
        return session.query(cls).filter_by(
            company_id=company_id,
            is_default=True,
            is_active=True
        ).first()

    @classmethod
    def get_framework_by_id(cls, session, framework_id):
        """Get competency framework by ID."""
        return session.query(cls).filter_by(framework_id=framework_id).first()

    def get_applicable_roles_list(self):
        """Get applicable roles as a list."""
        try:
            return json.loads(self.applicable_roles) if self.applicable_roles else []
        except:
            return []

    def get_applicable_departments_list(self):
        """Get applicable departments as a list."""
        try:
            return json.loads(self.applicable_departments) if self.applicable_departments else []
        except:
            return []

    def get_applicable_levels_list(self):
        """Get applicable levels as a list."""
        try:
            return json.loads(self.applicable_levels) if self.applicable_levels else []
        except:
            return []

    def get_proficiency_levels_list(self):
        """Get proficiency levels as a list."""
        try:
            return json.loads(self.proficiency_levels) if self.proficiency_levels else []
        except:
            return []

    def get_competency_categories_list(self):
        """Get competency categories as a list."""
        try:
            return json.loads(self.competency_categories) if self.competency_categories else []
        except:
            return []

    def add_competency(self, session, competency_data):
        """Add a competency to this framework."""
        try:
            competency = Competency(
                framework_id=self.framework_id,
                **competency_data
            )
            session.add(competency)
            session.commit()
            
            current_app.logger.info(f"Added competency to framework: {self.framework_id}")
            return competency, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error adding competency: {e}")
            return None, str(e)

    @classmethod
    def create_default_framework(cls, session, company_id, created_by=None):
        """Create a default competency framework for a new company."""
        try:
            # Default proficiency levels
            proficiency_levels = [
                {
                    "level": 1,
                    "name": "Developing",
                    "description": "Learning and developing the competency",
                    "behavioral_indicators": ["Shows awareness", "Seeks guidance", "Learning basics"]
                },
                {
                    "level": 2,
                    "name": "Competent",
                    "description": "Demonstrates competency consistently",
                    "behavioral_indicators": ["Applies knowledge", "Works independently", "Meets expectations"]
                },
                {
                    "level": 3,
                    "name": "Proficient",
                    "description": "Demonstrates advanced competency",
                    "behavioral_indicators": ["Exceeds expectations", "Guides others", "Innovates"]
                },
                {
                    "level": 4,
                    "name": "Expert",
                    "description": "Demonstrates mastery and teaches others",
                    "behavioral_indicators": ["Sets standards", "Mentors others", "Drives excellence"]
                }
            ]
            
            # Default competency categories
            competency_categories = [
                {
                    "category": "Core Competencies",
                    "description": "Fundamental skills required for all roles",
                    "weight": 30
                },
                {
                    "category": "Leadership Competencies",
                    "description": "Skills required for leadership roles",
                    "weight": 25
                },
                {
                    "category": "Technical Competencies",
                    "description": "Job-specific technical skills",
                    "weight": 25
                },
                {
                    "category": "Behavioral Competencies",
                    "description": "Behavioral and soft skills",
                    "weight": 20
                }
            ]
            
            framework_data = {
                "company_id": company_id,
                "name": "Standard Competency Framework",
                "description": "Default competency framework covering core, leadership, technical, and behavioral competencies",
                "framework_type": "GENERAL",
                "proficiency_levels": proficiency_levels,
                "competency_categories": competency_categories,
                "is_default": True,
                "created_by": created_by
            }
            
            framework, error = cls.create_framework(session, **framework_data)
            if error:
                return None, error
            
            # Add default competencies
            default_competencies = [
                # Core Competencies
                {
                    "name": "Communication",
                    "description": "Ability to communicate effectively with others",
                    "category": "Core Competencies",
                    "competency_type": "BEHAVIORAL",
                    "is_required": True,
                    "weight": 20
                },
                {
                    "name": "Problem Solving",
                    "description": "Ability to identify and solve problems effectively",
                    "category": "Core Competencies",
                    "competency_type": "BEHAVIORAL",
                    "is_required": True,
                    "weight": 20
                },
                {
                    "name": "Teamwork",
                    "description": "Ability to work effectively in a team environment",
                    "category": "Core Competencies",
                    "competency_type": "BEHAVIORAL",
                    "is_required": True,
                    "weight": 15
                },
                # Leadership Competencies
                {
                    "name": "Strategic Thinking",
                    "description": "Ability to think strategically and plan for the future",
                    "category": "Leadership Competencies",
                    "competency_type": "LEADERSHIP",
                    "is_required": False,
                    "weight": 25
                },
                {
                    "name": "People Management",
                    "description": "Ability to manage and develop people effectively",
                    "category": "Leadership Competencies",
                    "competency_type": "LEADERSHIP",
                    "is_required": False,
                    "weight": 25
                },
                # Technical Competencies
                {
                    "name": "Job Knowledge",
                    "description": "Knowledge and skills specific to the role",
                    "category": "Technical Competencies",
                    "competency_type": "TECHNICAL",
                    "is_required": True,
                    "weight": 30
                },
                # Behavioral Competencies
                {
                    "name": "Adaptability",
                    "description": "Ability to adapt to change and new situations",
                    "category": "Behavioral Competencies",
                    "competency_type": "BEHAVIORAL",
                    "is_required": True,
                    "weight": 15
                },
                {
                    "name": "Initiative",
                    "description": "Ability to take initiative and be proactive",
                    "category": "Behavioral Competencies",
                    "competency_type": "BEHAVIORAL",
                    "is_required": True,
                    "weight": 15
                }
            ]
            
            for comp_data in default_competencies:
                framework.add_competency(session, comp_data)
            
            current_app.logger.info(f"Created default competency framework for company: {company_id}")
            return framework, None
            
        except Exception as e:
            current_app.logger.error(f"Error creating default competency framework: {e}")
            return None, str(e)


class Competency(db.Model):
    """
    Individual competencies within a competency framework.
    Defines specific skills, behaviors, or knowledge areas.
    """
    __tablename__ = 'competencies'

    competency_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    framework_id = db.Column(UUID(as_uuid=True), db.ForeignKey('competency_frameworks.framework_id'), nullable=False)
    
    # Competency details
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    category = db.Column(db.String(255))  # Maps to framework categories
    competency_type = db.Column(db.String(100), default='BEHAVIORAL')  # TECHNICAL, BEHAVIORAL, LEADERSHIP
    
    # Importance and weighting
    is_required = db.Column(db.Boolean, default=True)
    weight = db.Column(db.Numeric(5, 2), default=100)  # Weight in overall assessment
    
    # Level definitions
    level_definitions = db.Column(db.Text)  # JSON with level-specific behavioral indicators
    
    # Usage settings
    is_active = db.Column(db.Boolean, default=True)
    display_order = db.Column(db.Integer, default=0)
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    def to_dict(self):
        """Dictionary representation of the competency."""
        return {
            "competency_id": str(self.competency_id),
            "framework_id": str(self.framework_id),
            "name": self.name,
            "description": self.description,
            "category": self.category,
            "competency_type": self.competency_type,
            "is_required": self.is_required,
            "weight": float(self.weight) if self.weight else 100,
            "level_definitions": self.get_level_definitions_dict(),
            "is_active": self.is_active,
            "display_order": self.display_order,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }

    def get_level_definitions_dict(self):
        """Get level definitions as dictionary."""
        try:
            return json.loads(self.level_definitions) if self.level_definitions else {}
        except:
            return {}

    def set_level_definitions(self, definitions_dict):
        """Set level definitions from dictionary."""
        try:
            self.level_definitions = json.dumps(definitions_dict) if definitions_dict else None
        except:
            self.level_definitions = None

    @classmethod
    def get_competencies_by_framework(cls, session, framework_id, category=None, include_inactive=False):
        """Get all competencies for a framework."""
        query = session.query(cls).filter_by(framework_id=framework_id)
        
        if category:
            query = query.filter_by(category=category)
        
        if not include_inactive:
            query = query.filter_by(is_active=True)
        
        return query.order_by(cls.display_order, cls.name).all()

    @classmethod
    def get_competency_by_id(cls, session, competency_id):
        """Get competency by ID."""
        return session.query(cls).filter_by(competency_id=competency_id).first()
