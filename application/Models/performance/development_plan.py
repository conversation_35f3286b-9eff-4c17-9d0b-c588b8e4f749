from application.database import db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime, date, timedelta
from flask import current_app
from decimal import Decimal
import json


class DevelopmentPlan(db.Model):
    """
    Individual development plans for employees based on performance reviews and career goals.
    Tracks skill gaps, development activities, and progress toward career objectives.
    """
    __tablename__ = 'development_plans'

    plan_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    employee_id = db.Column(UUID(as_uuid=True), nullable=False, index=True)
    cycle_id = db.Column(UUID(as_uuid=True), db.ForeignKey('performance_review_cycles.cycle_id'))
    
    # Plan details
    plan_name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    plan_type = db.Column(db.String(100), default='ANNUAL')  # ANNUAL, QUARTERLY, PROJECT_BASED, CAREER_FOCUSED
    
    # Timeline
    start_date = db.Column(db.Date, nullable=False, default=date.today)
    target_completion_date = db.Column(db.Date, nullable=False)
    actual_completion_date = db.Column(db.Date)
    
    # Development focus areas
    primary_focus_area = db.Column(db.String(255))  # Main area of development
    secondary_focus_areas = db.Column(db.Text)  # JSON array of additional focus areas
    
    # Career and role information
    current_role = db.Column(db.String(255))
    target_role = db.Column(db.String(255))  # Role employee is developing toward
    career_path = db.Column(db.String(255))  # Defined career path
    promotion_timeline = db.Column(db.String(100))  # 6_MONTHS, 1_YEAR, 2_YEARS, LONG_TERM
    
    # Skill gaps and competencies
    skill_gaps_identified = db.Column(db.Text)  # JSON array of skill gaps
    competency_targets = db.Column(db.Text)  # JSON with competency_id: target_level
    
    # Development objectives
    development_objectives = db.Column(db.Text)  # JSON array of SMART development objectives
    success_criteria = db.Column(db.Text)  # How success will be measured
    
    # Support and resources
    manager_support_needed = db.Column(db.Text)
    mentor_assigned = db.Column(UUID(as_uuid=True))  # Mentor employee ID
    budget_allocated = db.Column(db.Numeric(10, 2))  # Budget for development activities
    time_allocation = db.Column(db.String(100))  # Time allocated for development (e.g., "10% of work time")
    
    # Progress tracking
    overall_progress_percentage = db.Column(db.Numeric(5, 2), default=0)
    last_review_date = db.Column(db.Date)
    next_review_date = db.Column(db.Date)
    review_frequency = db.Column(db.String(50), default='MONTHLY')  # WEEKLY, MONTHLY, QUARTERLY
    
    # Status and approval
    status = db.Column(db.String(50), default='DRAFT')  # DRAFT, PENDING_APPROVAL, APPROVED, IN_PROGRESS, COMPLETED, CANCELLED
    approved_by = db.Column(UUID(as_uuid=True))
    approval_date = db.Column(db.DateTime)
    approval_comments = db.Column(db.Text)
    
    # Outcomes and results
    completion_summary = db.Column(db.Text)  # Summary of what was achieved
    skills_developed = db.Column(db.Text)  # JSON array of skills actually developed
    certifications_earned = db.Column(db.Text)  # JSON array of certifications earned
    promotion_achieved = db.Column(db.Boolean, default=False)
    role_change_date = db.Column(db.Date)
    
    # Metadata
    created_by = db.Column(UUID(as_uuid=True))
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # Relationships
    development_actions = db.relationship('DevelopmentAction', backref='plan', lazy='dynamic', cascade='all, delete-orphan')

    def to_dict(self):
        """Dictionary representation of the development plan."""
        return {
            "plan_id": str(self.plan_id),
            "employee_id": str(self.employee_id),
            "cycle_id": str(self.cycle_id) if self.cycle_id else None,
            "plan_name": self.plan_name,
            "description": self.description,
            "plan_type": self.plan_type,
            "start_date": self.start_date.strftime('%Y-%m-%d') if self.start_date else None,
            "target_completion_date": self.target_completion_date.strftime('%Y-%m-%d') if self.target_completion_date else None,
            "actual_completion_date": self.actual_completion_date.strftime('%Y-%m-%d') if self.actual_completion_date else None,
            "primary_focus_area": self.primary_focus_area,
            "secondary_focus_areas": self.get_secondary_focus_areas_list(),
            "current_role": self.current_role,
            "target_role": self.target_role,
            "career_path": self.career_path,
            "promotion_timeline": self.promotion_timeline,
            "skill_gaps_identified": self.get_skill_gaps_list(),
            "competency_targets": self.get_competency_targets_dict(),
            "development_objectives": self.get_development_objectives_list(),
            "success_criteria": self.success_criteria,
            "manager_support_needed": self.manager_support_needed,
            "mentor_assigned": str(self.mentor_assigned) if self.mentor_assigned else None,
            "budget_allocated": float(self.budget_allocated) if self.budget_allocated else None,
            "time_allocation": self.time_allocation,
            "overall_progress_percentage": float(self.overall_progress_percentage) if self.overall_progress_percentage else 0,
            "last_review_date": self.last_review_date.strftime('%Y-%m-%d') if self.last_review_date else None,
            "next_review_date": self.next_review_date.strftime('%Y-%m-%d') if self.next_review_date else None,
            "review_frequency": self.review_frequency,
            "status": self.status,
            "approved_by": str(self.approved_by) if self.approved_by else None,
            "approval_date": self.approval_date.strftime('%Y-%m-%d %H:%M:%S') if self.approval_date else None,
            "approval_comments": self.approval_comments,
            "completion_summary": self.completion_summary,
            "skills_developed": self.get_skills_developed_list(),
            "certifications_earned": self.get_certifications_earned_list(),
            "promotion_achieved": self.promotion_achieved,
            "role_change_date": self.role_change_date.strftime('%Y-%m-%d') if self.role_change_date else None,
            "created_by": str(self.created_by) if self.created_by else None,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            "days_remaining": self.get_days_remaining(),
            "is_overdue": self.is_overdue(),
            "action_count": self.development_actions.count(),
            "completed_actions": self.get_completed_actions_count()
        }

    @classmethod
    def create_plan(cls, session, **kwargs):
        """Create a new development plan."""
        try:
            # Handle JSON fields
            json_fields = ['secondary_focus_areas', 'skill_gaps_identified', 'competency_targets', 
                          'development_objectives', 'skills_developed', 'certifications_earned']
            
            for field in json_fields:
                if field in kwargs and isinstance(kwargs[field], (list, dict)):
                    kwargs[field] = json.dumps(kwargs[field])
            
            plan = cls(**kwargs)
            session.add(plan)
            session.flush()
            
            current_app.logger.info(f"Created development plan: {plan.plan_name}")
            return plan, None
            
        except Exception as e:
            current_app.logger.error(f"Error creating development plan: {e}")
            return None, str(e)

    @classmethod
    def get_plans_by_employee(cls, session, employee_id, status=None):
        """Get all development plans for an employee."""
        query = session.query(cls).filter_by(employee_id=employee_id)
        
        if status:
            if isinstance(status, list):
                query = query.filter(cls.status.in_(status))
            else:
                query = query.filter_by(status=status)
        
        return query.order_by(cls.start_date.desc()).all()

    @classmethod
    def get_plan_by_id(cls, session, plan_id):
        """Get development plan by ID."""
        return session.query(cls).filter_by(plan_id=plan_id).first()

    def approve_plan(self, session, approved_by, approval_comments=None):
        """Approve the development plan."""
        try:
            self.status = 'APPROVED'
            self.approved_by = approved_by
            self.approval_date = datetime.now()
            self.approval_comments = approval_comments
            self.updated_at = datetime.now()
            
            session.commit()
            
            current_app.logger.info(f"Approved development plan: {self.plan_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error approving development plan: {e}")
            return False, str(e)

    def start_plan(self, session):
        """Start the development plan."""
        try:
            if self.status not in ['APPROVED', 'DRAFT']:
                return False, f"Cannot start plan with status: {self.status}"
            
            self.status = 'IN_PROGRESS'
            self.updated_at = datetime.now()
            
            # Set next review date based on frequency
            if self.review_frequency == 'WEEKLY':
                self.next_review_date = date.today() + timedelta(weeks=1)
            elif self.review_frequency == 'MONTHLY':
                self.next_review_date = date.today() + timedelta(days=30)
            elif self.review_frequency == 'QUARTERLY':
                self.next_review_date = date.today() + timedelta(days=90)
            
            session.commit()
            
            current_app.logger.info(f"Started development plan: {self.plan_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error starting development plan: {e}")
            return False, str(e)

    def complete_plan(self, session, completion_summary=None, completed_by=None):
        """Complete the development plan."""
        try:
            self.status = 'COMPLETED'
            self.actual_completion_date = date.today()
            self.overall_progress_percentage = 100
            self.completion_summary = completion_summary
            self.updated_at = datetime.now()
            
            session.commit()
            
            current_app.logger.info(f"Completed development plan: {self.plan_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error completing development plan: {e}")
            return False, str(e)

    def update_progress(self, session):
        """Update overall progress based on development actions."""
        try:
            actions = self.development_actions.all()
            
            if not actions:
                self.overall_progress_percentage = 0
            else:
                total_weight = sum(float(action.weight or 100) for action in actions)
                completed_weight = sum(float(action.weight or 100) for action in actions if action.status == 'COMPLETED')
                
                if total_weight > 0:
                    self.overall_progress_percentage = Decimal(str(round(completed_weight / total_weight * 100, 2)))
                else:
                    self.overall_progress_percentage = 0
            
            self.updated_at = datetime.now()
            session.commit()
            
        except Exception as e:
            current_app.logger.error(f"Error updating plan progress: {e}")

    def add_development_action(self, session, action_data):
        """Add a development action to this plan."""
        try:
            action = DevelopmentAction(
                plan_id=self.plan_id,
                **action_data
            )
            session.add(action)
            session.commit()
            
            # Update overall progress
            self.update_progress(session)
            
            current_app.logger.info(f"Added development action to plan: {self.plan_id}")
            return action, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error adding development action: {e}")
            return None, str(e)

    def get_days_remaining(self):
        """Get number of days remaining to complete the plan."""
        if not self.target_completion_date or self.status == 'COMPLETED':
            return 0
        
        days_remaining = (self.target_completion_date - date.today()).days
        return max(0, days_remaining)

    def is_overdue(self):
        """Check if plan is overdue."""
        if self.status == 'COMPLETED' or not self.target_completion_date:
            return False
        
        return date.today() > self.target_completion_date

    def get_completed_actions_count(self):
        """Get count of completed development actions."""
        return self.development_actions.filter_by(status='COMPLETED').count()

    # JSON field helper methods
    def get_secondary_focus_areas_list(self):
        try:
            return json.loads(self.secondary_focus_areas) if self.secondary_focus_areas else []
        except:
            return []

    def get_skill_gaps_list(self):
        try:
            return json.loads(self.skill_gaps_identified) if self.skill_gaps_identified else []
        except:
            return []

    def get_competency_targets_dict(self):
        try:
            return json.loads(self.competency_targets) if self.competency_targets else {}
        except:
            return {}

    def get_development_objectives_list(self):
        try:
            return json.loads(self.development_objectives) if self.development_objectives else []
        except:
            return []

    def get_skills_developed_list(self):
        try:
            return json.loads(self.skills_developed) if self.skills_developed else []
        except:
            return []

    def get_certifications_earned_list(self):
        try:
            return json.loads(self.certifications_earned) if self.certifications_earned else []
        except:
            return []


class DevelopmentAction(db.Model):
    """
    Specific development actions within a development plan.
    Includes training, mentoring, projects, and other development activities.
    """
    __tablename__ = 'development_actions'

    action_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    plan_id = db.Column(UUID(as_uuid=True), db.ForeignKey('development_plans.plan_id'), nullable=False)
    
    # Action details
    action_title = db.Column(db.String(255), nullable=False)
    action_description = db.Column(db.Text)
    action_type = db.Column(db.String(100), nullable=False)  # TRAINING, MENTORING, PROJECT, READING, CERTIFICATION, CONFERENCE, JOB_ROTATION
    action_category = db.Column(db.String(100))  # FORMAL_LEARNING, EXPERIENTIAL, SOCIAL_LEARNING
    
    # Timeline
    start_date = db.Column(db.Date)
    target_completion_date = db.Column(db.Date, nullable=False)
    actual_completion_date = db.Column(db.Date)
    
    # Resources and requirements
    provider = db.Column(db.String(255))  # Training provider, mentor name, etc.
    cost = db.Column(db.Numeric(10, 2))
    time_required = db.Column(db.String(100))  # e.g., "40 hours", "2 days"
    prerequisites = db.Column(db.Text)
    
    # Progress and completion
    status = db.Column(db.String(50), default='PLANNED')  # PLANNED, IN_PROGRESS, COMPLETED, CANCELLED, DEFERRED
    progress_percentage = db.Column(db.Numeric(5, 2), default=0)
    completion_evidence = db.Column(db.Text)  # Evidence of completion
    
    # Weighting and priority
    weight = db.Column(db.Numeric(5, 2), default=100)  # Weight in overall development plan
    priority = db.Column(db.String(50), default='MEDIUM')  # HIGH, MEDIUM, LOW
    
    # Outcomes and impact
    skills_targeted = db.Column(db.Text)  # JSON array of skills this action targets
    competencies_addressed = db.Column(db.Text)  # JSON array of competencies
    success_criteria = db.Column(db.Text)
    actual_outcomes = db.Column(db.Text)
    
    # Approval and support
    requires_manager_approval = db.Column(db.Boolean, default=False)
    manager_approved = db.Column(db.Boolean, default=False)
    approval_date = db.Column(db.DateTime)
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    def to_dict(self):
        """Dictionary representation of the development action."""
        return {
            "action_id": str(self.action_id),
            "plan_id": str(self.plan_id),
            "action_title": self.action_title,
            "action_description": self.action_description,
            "action_type": self.action_type,
            "action_category": self.action_category,
            "start_date": self.start_date.strftime('%Y-%m-%d') if self.start_date else None,
            "target_completion_date": self.target_completion_date.strftime('%Y-%m-%d') if self.target_completion_date else None,
            "actual_completion_date": self.actual_completion_date.strftime('%Y-%m-%d') if self.actual_completion_date else None,
            "provider": self.provider,
            "cost": float(self.cost) if self.cost else None,
            "time_required": self.time_required,
            "prerequisites": self.prerequisites,
            "status": self.status,
            "progress_percentage": float(self.progress_percentage) if self.progress_percentage else 0,
            "completion_evidence": self.completion_evidence,
            "weight": float(self.weight) if self.weight else 100,
            "priority": self.priority,
            "skills_targeted": self.get_skills_targeted_list(),
            "competencies_addressed": self.get_competencies_addressed_list(),
            "success_criteria": self.success_criteria,
            "actual_outcomes": self.actual_outcomes,
            "requires_manager_approval": self.requires_manager_approval,
            "manager_approved": self.manager_approved,
            "approval_date": self.approval_date.strftime('%Y-%m-%d %H:%M:%S') if self.approval_date else None,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            "days_remaining": self.get_days_remaining(),
            "is_overdue": self.is_overdue()
        }

    def complete_action(self, session, completion_evidence=None, actual_outcomes=None):
        """Complete the development action."""
        try:
            self.status = 'COMPLETED'
            self.actual_completion_date = date.today()
            self.progress_percentage = 100
            
            if completion_evidence:
                self.completion_evidence = completion_evidence
            
            if actual_outcomes:
                self.actual_outcomes = actual_outcomes
            
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Completed development action: {self.action_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error completing development action: {e}")
            return False, str(e)

    def get_days_remaining(self):
        """Get number of days remaining to complete the action."""
        if not self.target_completion_date or self.status == 'COMPLETED':
            return 0
        
        days_remaining = (self.target_completion_date - date.today()).days
        return max(0, days_remaining)

    def is_overdue(self):
        """Check if action is overdue."""
        if self.status == 'COMPLETED' or not self.target_completion_date:
            return False
        
        return date.today() > self.target_completion_date

    def get_skills_targeted_list(self):
        try:
            return json.loads(self.skills_targeted) if self.skills_targeted else []
        except:
            return []

    def get_competencies_addressed_list(self):
        try:
            return json.loads(self.competencies_addressed) if self.competencies_addressed else []
        except:
            return []
