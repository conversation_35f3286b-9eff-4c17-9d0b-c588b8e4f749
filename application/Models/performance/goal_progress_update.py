from application.database import db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime, date
from flask import current_app
from decimal import Decimal


class GoalProgressUpdate(db.Model):
    """
    Progress updates for performance goals. Tracks historical progress changes
    and provides audit trail for goal achievement.
    """
    __tablename__ = 'goal_progress_updates'

    update_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    goal_id = db.Column(UUID(as_uuid=True), db.Foreign<PERSON>ey('performance_goals.goal_id'), nullable=False)
    
    # Update details
    update_date = db.Column(db.Date, nullable=False, default=date.today)
    update_type = db.Column(db.String(100), default='PROGRESS')  # PROGRESS, MILESTONE, ADJUSTMENT, COMPLETION
    
    # Progress values
    previous_value = db.Column(db.Numeric(15, 2))
    new_value = db.Column(db.Numeric(15, 2))
    value_change = db.Column(db.Numeric(15, 2))
    completion_percentage = db.Column(db.Numeric(5, 2))
    percentage_change = db.Column(db.Numeric(5, 2))
    
    # Update context
    notes = db.Column(db.Text)
    milestone_achieved = db.Column(db.String(255))  # If a milestone was achieved
    challenges_faced = db.Column(db.Text)
    support_needed = db.Column(db.Text)
    next_steps = db.Column(db.Text)
    
    # Evidence and attachments
    evidence_description = db.Column(db.Text)
    attachments = db.Column(db.Text)  # JSON array of file paths
    
    # Review and validation
    requires_manager_review = db.Column(db.Boolean, default=False)
    manager_reviewed = db.Column(db.Boolean, default=False)
    manager_comments = db.Column(db.Text)
    reviewed_by = db.Column(UUID(as_uuid=True))
    review_date = db.Column(db.DateTime)
    
    # Metadata
    updated_by = db.Column(UUID(as_uuid=True), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.now)
    
    def to_dict(self):
        """Dictionary representation of the progress update."""
        return {
            "update_id": str(self.update_id),
            "goal_id": str(self.goal_id),
            "update_date": self.update_date.strftime('%Y-%m-%d') if self.update_date else None,
            "update_type": self.update_type,
            "previous_value": float(self.previous_value) if self.previous_value else None,
            "new_value": float(self.new_value) if self.new_value else None,
            "value_change": float(self.value_change) if self.value_change else None,
            "completion_percentage": float(self.completion_percentage) if self.completion_percentage else None,
            "percentage_change": float(self.percentage_change) if self.percentage_change else None,
            "notes": self.notes,
            "milestone_achieved": self.milestone_achieved,
            "challenges_faced": self.challenges_faced,
            "support_needed": self.support_needed,
            "next_steps": self.next_steps,
            "evidence_description": self.evidence_description,
            "attachments": self.get_attachments_list(),
            "requires_manager_review": self.requires_manager_review,
            "manager_reviewed": self.manager_reviewed,
            "manager_comments": self.manager_comments,
            "reviewed_by": str(self.reviewed_by) if self.reviewed_by else None,
            "review_date": self.review_date.strftime('%Y-%m-%d %H:%M:%S') if self.review_date else None,
            "updated_by": str(self.updated_by),
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None
        }

    @classmethod
    def create_update(cls, session, **kwargs):
        """Create a new progress update."""
        try:
            # Calculate changes if previous and new values provided
            if 'previous_value' in kwargs and 'new_value' in kwargs:
                if kwargs['previous_value'] is not None and kwargs['new_value'] is not None:
                    kwargs['value_change'] = Decimal(str(kwargs['new_value'])) - Decimal(str(kwargs['previous_value']))
            
            # Handle JSON fields
            if 'attachments' in kwargs and isinstance(kwargs['attachments'], list):
                import json
                kwargs['attachments'] = json.dumps(kwargs['attachments'])
            
            update = cls(**kwargs)
            session.add(update)
            session.flush()
            
            current_app.logger.info(f"Created goal progress update: {update.update_id}")
            return update, None
            
        except Exception as e:
            current_app.logger.error(f"Error creating progress update: {e}")
            return None, str(e)

    @classmethod
    def get_updates_by_goal(cls, session, goal_id, limit=None):
        """Get all progress updates for a goal."""
        query = session.query(cls).filter_by(goal_id=goal_id)
        
        if limit:
            query = query.limit(limit)
        
        return query.order_by(cls.update_date.desc()).all()

    @classmethod
    def get_recent_updates(cls, session, employee_id=None, days=30, limit=10):
        """Get recent progress updates."""
        from application.Models.performance.performance_goal import PerformanceGoal
        
        cutoff_date = date.today() - timedelta(days=days)
        
        query = session.query(cls).filter(cls.update_date >= cutoff_date)
        
        if employee_id:
            query = query.join(PerformanceGoal).filter(PerformanceGoal.employee_id == employee_id)
        
        if limit:
            query = query.limit(limit)
        
        return query.order_by(cls.update_date.desc()).all()

    def manager_review(self, session, reviewed_by, manager_comments=None, approved=True):
        """Manager review of the progress update."""
        try:
            self.manager_reviewed = True
            self.reviewed_by = reviewed_by
            self.review_date = datetime.now()
            self.manager_comments = manager_comments
            
            session.commit()
            
            current_app.logger.info(f"Manager reviewed progress update: {self.update_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error in manager review: {e}")
            return False, str(e)

    def get_attachments_list(self):
        """Get attachments as a list."""
        try:
            import json
            return json.loads(self.attachments) if self.attachments else []
        except:
            return []

    def set_attachments(self, attachments_list):
        """Set attachments from a list."""
        try:
            import json
            self.attachments = json.dumps(attachments_list) if attachments_list else None
        except:
            self.attachments = None

    @classmethod
    def get_updates_requiring_review(cls, session, manager_id=None):
        """Get progress updates that require manager review."""
        from application.Models.performance.performance_goal import PerformanceGoal
        from application.Models.employees import Employee
        
        query = session.query(cls).filter(
            cls.requires_manager_review == True,
            cls.manager_reviewed == False
        )
        
        if manager_id:
            query = query.join(PerformanceGoal).join(Employee).filter(
                Employee.manager_id == manager_id
            )
        
        return query.order_by(cls.update_date).all()

    @classmethod
    def get_progress_trend(cls, session, goal_id, days=90):
        """Get progress trend for a goal over specified days."""
        try:
            cutoff_date = date.today() - timedelta(days=days)
            
            updates = session.query(cls).filter(
                cls.goal_id == goal_id,
                cls.update_date >= cutoff_date
            ).order_by(cls.update_date).all()
            
            if len(updates) < 2:
                return {
                    "trend": "INSUFFICIENT_DATA",
                    "updates_count": len(updates),
                    "trend_percentage": 0
                }
            
            # Calculate overall trend
            first_update = updates[0]
            last_update = updates[-1]
            
            if first_update.completion_percentage and last_update.completion_percentage:
                trend_percentage = float(last_update.completion_percentage) - float(first_update.completion_percentage)
                
                if trend_percentage > 10:
                    trend = "STRONG_POSITIVE"
                elif trend_percentage > 5:
                    trend = "POSITIVE"
                elif trend_percentage > -5:
                    trend = "STABLE"
                elif trend_percentage > -10:
                    trend = "NEGATIVE"
                else:
                    trend = "STRONG_NEGATIVE"
            else:
                trend = "UNKNOWN"
                trend_percentage = 0
            
            return {
                "trend": trend,
                "updates_count": len(updates),
                "trend_percentage": trend_percentage,
                "first_update_date": first_update.update_date.strftime('%Y-%m-%d'),
                "last_update_date": last_update.update_date.strftime('%Y-%m-%d')
            }
            
        except Exception as e:
            current_app.logger.error(f"Error calculating progress trend: {e}")
            return {
                "trend": "ERROR",
                "updates_count": 0,
                "trend_percentage": 0
            }

    @classmethod
    def get_milestone_achievements(cls, session, goal_id):
        """Get all milestone achievements for a goal."""
        return session.query(cls).filter(
            cls.goal_id == goal_id,
            cls.update_type == 'MILESTONE',
            cls.milestone_achieved.isnot(None)
        ).order_by(cls.update_date).all()

    @classmethod
    def get_update_statistics(cls, session, goal_id):
        """Get update statistics for a goal."""
        try:
            total_updates = session.query(cls).filter_by(goal_id=goal_id).count()
            
            milestone_updates = session.query(cls).filter(
                cls.goal_id == goal_id,
                cls.update_type == 'MILESTONE'
            ).count()
            
            updates_needing_review = session.query(cls).filter(
                cls.goal_id == goal_id,
                cls.requires_manager_review == True,
                cls.manager_reviewed == False
            ).count()
            
            recent_updates = session.query(cls).filter(
                cls.goal_id == goal_id,
                cls.update_date >= date.today() - timedelta(days=30)
            ).count()
            
            return {
                "total_updates": total_updates,
                "milestone_updates": milestone_updates,
                "updates_needing_review": updates_needing_review,
                "recent_updates": recent_updates
            }
            
        except Exception as e:
            current_app.logger.error(f"Error getting update statistics: {e}")
            return {
                "total_updates": 0,
                "milestone_updates": 0,
                "updates_needing_review": 0,
                "recent_updates": 0
            }

    def calculate_velocity(self, session):
        """Calculate progress velocity (progress per day) based on recent updates."""
        try:
            # Get updates from the last 30 days for this goal
            recent_updates = session.query(GoalProgressUpdate).filter(
                GoalProgressUpdate.goal_id == self.goal_id,
                GoalProgressUpdate.update_date >= date.today() - timedelta(days=30)
            ).order_by(GoalProgressUpdate.update_date).all()
            
            if len(recent_updates) < 2:
                return 0
            
            # Calculate velocity based on completion percentage change over time
            first_update = recent_updates[0]
            last_update = recent_updates[-1]
            
            if first_update.completion_percentage and last_update.completion_percentage:
                percentage_change = float(last_update.completion_percentage) - float(first_update.completion_percentage)
                days_elapsed = (last_update.update_date - first_update.update_date).days
                
                if days_elapsed > 0:
                    velocity = percentage_change / days_elapsed
                    return round(velocity, 2)
            
            return 0
            
        except Exception as e:
            current_app.logger.error(f"Error calculating velocity: {e}")
            return 0

    @classmethod
    def get_team_progress_summary(cls, session, manager_id, days=30):
        """Get progress summary for all goals under a manager."""
        try:
            from application.Models.performance.performance_goal import PerformanceGoal
            from application.Models.employees import Employee
            
            cutoff_date = date.today() - timedelta(days=days)
            
            # Get all updates for goals of employees under this manager
            updates = session.query(cls).join(PerformanceGoal).join(Employee).filter(
                Employee.manager_id == manager_id,
                cls.update_date >= cutoff_date
            ).all()
            
            total_updates = len(updates)
            milestone_achievements = len([u for u in updates if u.update_type == 'MILESTONE'])
            updates_needing_review = len([u for u in updates if u.requires_manager_review and not u.manager_reviewed])
            
            # Calculate average progress
            progress_values = [float(u.completion_percentage) for u in updates if u.completion_percentage]
            avg_progress = sum(progress_values) / len(progress_values) if progress_values else 0
            
            return {
                "total_updates": total_updates,
                "milestone_achievements": milestone_achievements,
                "updates_needing_review": updates_needing_review,
                "average_progress": round(avg_progress, 2),
                "period_days": days
            }
            
        except Exception as e:
            current_app.logger.error(f"Error getting team progress summary: {e}")
            return {
                "total_updates": 0,
                "milestone_achievements": 0,
                "updates_needing_review": 0,
                "average_progress": 0,
                "period_days": days
            }
