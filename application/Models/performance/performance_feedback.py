from application.database import db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime, date, timedelta
from flask import current_app
from decimal import Decimal
import json


class PerformanceFeedback(db.Model):
    """
    360-degree feedback and continuous feedback for performance management.
    Supports feedback from multiple sources and real-time feedback collection.
    """
    __tablename__ = 'performance_feedback'

    feedback_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    employee_id = db.Column(UUID(as_uuid=True), nullable=False, index=True)  # Person receiving feedback
    feedback_provider_id = db.Column(UUID(as_uuid=True), nullable=False, index=True)  # Person giving feedback
    cycle_id = db.Column(UUID(as_uuid=True), db.ForeignKey('performance_review_cycles.cycle_id'))  # Optional cycle association
    
    # Feedback context
    feedback_type = db.Column(db.String(100), nullable=False)  # CONTINUOUS, FORMAL_REVIEW, PROJECT_BASED, PEER_NOMINATION
    feedback_source = db.Column(db.String(100), nullable=False)  # MANAGER, PEER, SUBORDINATE, CUSTOMER, SELF, SKIP_LEVEL
    relationship_to_employee = db.Column(db.String(100))  # DIRECT_MANAGER, COLLEAGUE, TEAM_MEMBER, CLIENT
    
    # Feedback request details
    requested_by = db.Column(UUID(as_uuid=True))  # Who requested this feedback
    request_date = db.Column(db.DateTime)
    due_date = db.Column(db.Date)
    feedback_period_start = db.Column(db.Date)  # Period this feedback covers
    feedback_period_end = db.Column(db.Date)
    
    # Feedback content
    overall_rating = db.Column(db.Numeric(3, 2))  # Overall performance rating
    strengths = db.Column(db.Text)
    areas_for_improvement = db.Column(db.Text)
    specific_examples = db.Column(db.Text)
    recommendations = db.Column(db.Text)
    
    # Structured feedback areas
    communication_rating = db.Column(db.Numeric(3, 2))
    communication_comments = db.Column(db.Text)
    
    teamwork_rating = db.Column(db.Numeric(3, 2))
    teamwork_comments = db.Column(db.Text)
    
    leadership_rating = db.Column(db.Numeric(3, 2))
    leadership_comments = db.Column(db.Text)
    
    technical_skills_rating = db.Column(db.Numeric(3, 2))
    technical_skills_comments = db.Column(db.Text)
    
    problem_solving_rating = db.Column(db.Numeric(3, 2))
    problem_solving_comments = db.Column(db.Text)
    
    initiative_rating = db.Column(db.Numeric(3, 2))
    initiative_comments = db.Column(db.Text)
    
    # Competency-based feedback
    competency_feedback = db.Column(db.Text)  # JSON with competency_id: {rating, comments}
    
    # Development and growth feedback
    development_suggestions = db.Column(db.Text)
    career_advice = db.Column(db.Text)
    training_recommendations = db.Column(db.Text)
    mentoring_suggestions = db.Column(db.Text)
    
    # Future potential and readiness
    promotion_readiness = db.Column(db.String(50))  # READY, DEVELOPING, NOT_READY
    potential_rating = db.Column(db.String(50))  # HIGH, MEDIUM, LOW
    retention_risk = db.Column(db.String(50))  # LOW, MEDIUM, HIGH
    
    # Specific questions and responses
    custom_questions_responses = db.Column(db.Text)  # JSON with question: response pairs
    
    # Feedback quality and validation
    feedback_quality = db.Column(db.String(50))  # EXCELLENT, GOOD, ADEQUATE, POOR
    is_anonymous = db.Column(db.Boolean, default=False)
    is_confidential = db.Column(db.Boolean, default=True)
    
    # Status and workflow
    status = db.Column(db.String(50), default='REQUESTED')  # REQUESTED, IN_PROGRESS, SUBMITTED, REVIEWED, SHARED
    submission_date = db.Column(db.DateTime)
    review_date = db.Column(db.DateTime)
    shared_with_employee = db.Column(db.Boolean, default=False)
    share_date = db.Column(db.DateTime)
    
    # Employee response
    employee_response = db.Column(db.Text)
    employee_response_date = db.Column(db.DateTime)
    employee_acknowledgment = db.Column(db.Boolean, default=False)
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    def to_dict(self):
        """Dictionary representation of the performance feedback."""
        return {
            "feedback_id": str(self.feedback_id),
            "employee_id": str(self.employee_id),
            "feedback_provider_id": str(self.feedback_provider_id),
            "cycle_id": str(self.cycle_id) if self.cycle_id else None,
            "feedback_type": self.feedback_type,
            "feedback_source": self.feedback_source,
            "relationship_to_employee": self.relationship_to_employee,
            "requested_by": str(self.requested_by) if self.requested_by else None,
            "request_date": self.request_date.strftime('%Y-%m-%d %H:%M:%S') if self.request_date else None,
            "due_date": self.due_date.strftime('%Y-%m-%d') if self.due_date else None,
            "feedback_period_start": self.feedback_period_start.strftime('%Y-%m-%d') if self.feedback_period_start else None,
            "feedback_period_end": self.feedback_period_end.strftime('%Y-%m-%d') if self.feedback_period_end else None,
            "overall_rating": float(self.overall_rating) if self.overall_rating else None,
            "strengths": self.strengths,
            "areas_for_improvement": self.areas_for_improvement,
            "specific_examples": self.specific_examples,
            "recommendations": self.recommendations,
            "structured_ratings": {
                "communication": {
                    "rating": float(self.communication_rating) if self.communication_rating else None,
                    "comments": self.communication_comments
                },
                "teamwork": {
                    "rating": float(self.teamwork_rating) if self.teamwork_rating else None,
                    "comments": self.teamwork_comments
                },
                "leadership": {
                    "rating": float(self.leadership_rating) if self.leadership_rating else None,
                    "comments": self.leadership_comments
                },
                "technical_skills": {
                    "rating": float(self.technical_skills_rating) if self.technical_skills_rating else None,
                    "comments": self.technical_skills_comments
                },
                "problem_solving": {
                    "rating": float(self.problem_solving_rating) if self.problem_solving_rating else None,
                    "comments": self.problem_solving_comments
                },
                "initiative": {
                    "rating": float(self.initiative_rating) if self.initiative_rating else None,
                    "comments": self.initiative_comments
                }
            },
            "competency_feedback": self.get_competency_feedback_dict(),
            "development": {
                "development_suggestions": self.development_suggestions,
                "career_advice": self.career_advice,
                "training_recommendations": self.training_recommendations,
                "mentoring_suggestions": self.mentoring_suggestions
            },
            "future_potential": {
                "promotion_readiness": self.promotion_readiness,
                "potential_rating": self.potential_rating,
                "retention_risk": self.retention_risk
            },
            "custom_questions_responses": self.get_custom_questions_responses_dict(),
            "feedback_quality": self.feedback_quality,
            "is_anonymous": self.is_anonymous,
            "is_confidential": self.is_confidential,
            "status": self.status,
            "submission_date": self.submission_date.strftime('%Y-%m-%d %H:%M:%S') if self.submission_date else None,
            "review_date": self.review_date.strftime('%Y-%m-%d %H:%M:%S') if self.review_date else None,
            "shared_with_employee": self.shared_with_employee,
            "share_date": self.share_date.strftime('%Y-%m-%d %H:%M:%S') if self.share_date else None,
            "employee_response": self.employee_response,
            "employee_response_date": self.employee_response_date.strftime('%Y-%m-%d %H:%M:%S') if self.employee_response_date else None,
            "employee_acknowledgment": self.employee_acknowledgment,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            "is_overdue": self.is_overdue(),
            "days_overdue": self.get_days_overdue(),
            "average_rating": self.calculate_average_rating()
        }

    @classmethod
    def create_feedback(cls, session, **kwargs):
        """Create a new performance feedback entry."""
        try:
            # Handle JSON fields
            if 'competency_feedback' in kwargs and isinstance(kwargs['competency_feedback'], dict):
                kwargs['competency_feedback'] = json.dumps(kwargs['competency_feedback'])
            
            if 'custom_questions_responses' in kwargs and isinstance(kwargs['custom_questions_responses'], dict):
                kwargs['custom_questions_responses'] = json.dumps(kwargs['custom_questions_responses'])
            
            feedback = cls(**kwargs)
            session.add(feedback)
            session.flush()
            
            current_app.logger.info(f"Created performance feedback: {feedback.feedback_id}")
            return feedback, None
            
        except Exception as e:
            current_app.logger.error(f"Error creating performance feedback: {e}")
            return None, str(e)

    @classmethod
    def get_feedback_for_employee(cls, session, employee_id, feedback_type=None, cycle_id=None):
        """Get all feedback for an employee."""
        query = session.query(cls).filter_by(employee_id=employee_id)
        
        if feedback_type:
            query = query.filter_by(feedback_type=feedback_type)
        
        if cycle_id:
            query = query.filter_by(cycle_id=cycle_id)
        
        return query.order_by(cls.submission_date.desc()).all()

    @classmethod
    def get_feedback_by_provider(cls, session, feedback_provider_id, status=None):
        """Get all feedback provided by a specific person."""
        query = session.query(cls).filter_by(feedback_provider_id=feedback_provider_id)
        
        if status:
            if isinstance(status, list):
                query = query.filter(cls.status.in_(status))
            else:
                query = query.filter_by(status=status)
        
        return query.order_by(cls.due_date).all()

    @classmethod
    def get_feedback_by_id(cls, session, feedback_id):
        """Get feedback by ID."""
        return session.query(cls).filter_by(feedback_id=feedback_id).first()

    def submit_feedback(self, session):
        """Submit the feedback."""
        try:
            if self.status not in ['REQUESTED', 'IN_PROGRESS']:
                return False, f"Cannot submit feedback with status: {self.status}"
            
            self.status = 'SUBMITTED'
            self.submission_date = datetime.now()
            self.updated_at = datetime.now()
            
            session.commit()
            
            current_app.logger.info(f"Submitted performance feedback: {self.feedback_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error submitting feedback: {e}")
            return False, str(e)

    def review_feedback(self, session, reviewed_by=None):
        """Mark feedback as reviewed."""
        try:
            self.status = 'REVIEWED'
            self.review_date = datetime.now()
            self.updated_at = datetime.now()
            
            session.commit()
            
            current_app.logger.info(f"Reviewed performance feedback: {self.feedback_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error reviewing feedback: {e}")
            return False, str(e)

    def share_with_employee(self, session):
        """Share feedback with the employee."""
        try:
            self.shared_with_employee = True
            self.share_date = datetime.now()
            self.status = 'SHARED'
            self.updated_at = datetime.now()
            
            session.commit()
            
            current_app.logger.info(f"Shared feedback with employee: {self.feedback_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error sharing feedback: {e}")
            return False, str(e)

    def employee_respond(self, session, response_text):
        """Employee responds to the feedback."""
        try:
            self.employee_response = response_text
            self.employee_response_date = datetime.now()
            self.employee_acknowledgment = True
            self.updated_at = datetime.now()
            
            session.commit()
            
            current_app.logger.info(f"Employee responded to feedback: {self.feedback_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error in employee response: {e}")
            return False, str(e)

    def is_overdue(self):
        """Check if feedback is overdue."""
        if self.status in ['SUBMITTED', 'REVIEWED', 'SHARED'] or not self.due_date:
            return False
        
        return date.today() > self.due_date

    def get_days_overdue(self):
        """Get number of days overdue."""
        if not self.is_overdue():
            return 0
        
        return (date.today() - self.due_date).days

    def calculate_average_rating(self):
        """Calculate average rating across all performance areas."""
        ratings = [
            self.communication_rating,
            self.teamwork_rating,
            self.leadership_rating,
            self.technical_skills_rating,
            self.problem_solving_rating,
            self.initiative_rating
        ]
        
        valid_ratings = [float(r) for r in ratings if r is not None]
        
        if not valid_ratings:
            return None
        
        return round(sum(valid_ratings) / len(valid_ratings), 2)

    def get_competency_feedback_dict(self):
        """Get competency feedback as dictionary."""
        try:
            return json.loads(self.competency_feedback) if self.competency_feedback else {}
        except:
            return {}

    def set_competency_feedback(self, feedback_dict):
        """Set competency feedback from dictionary."""
        try:
            self.competency_feedback = json.dumps(feedback_dict) if feedback_dict else None
        except:
            self.competency_feedback = None

    def get_custom_questions_responses_dict(self):
        """Get custom questions responses as dictionary."""
        try:
            return json.loads(self.custom_questions_responses) if self.custom_questions_responses else {}
        except:
            return {}

    def set_custom_questions_responses(self, responses_dict):
        """Set custom questions responses from dictionary."""
        try:
            self.custom_questions_responses = json.dumps(responses_dict) if responses_dict else None
        except:
            self.custom_questions_responses = None

    @classmethod
    def get_overdue_feedback(cls, session, feedback_provider_id=None, days_overdue=0):
        """Get overdue feedback requests."""
        overdue_date = date.today() - timedelta(days=days_overdue)
        
        query = session.query(cls).filter(
            cls.due_date < overdue_date,
            cls.status.in_(['REQUESTED', 'IN_PROGRESS'])
        )
        
        if feedback_provider_id:
            query = query.filter_by(feedback_provider_id=feedback_provider_id)
        
        return query.order_by(cls.due_date).all()

    @classmethod
    def get_pending_feedback_requests(cls, session, feedback_provider_id):
        """Get pending feedback requests for a provider."""
        return session.query(cls).filter(
            cls.feedback_provider_id == feedback_provider_id,
            cls.status.in_(['REQUESTED', 'IN_PROGRESS'])
        ).order_by(cls.due_date).all()

    @classmethod
    def request_feedback(cls, session, employee_id, feedback_provider_id, requested_by, 
                        feedback_type='CONTINUOUS', feedback_source='PEER', due_date=None, **kwargs):
        """Request feedback from a specific provider."""
        try:
            if not due_date:
                due_date = date.today() + timedelta(days=7)  # Default 7 days
            
            feedback_data = {
                'employee_id': employee_id,
                'feedback_provider_id': feedback_provider_id,
                'requested_by': requested_by,
                'feedback_type': feedback_type,
                'feedback_source': feedback_source,
                'due_date': due_date,
                'request_date': datetime.now(),
                'status': 'REQUESTED',
                **kwargs
            }
            
            feedback, error = cls.create_feedback(session, **feedback_data)
            if error:
                return None, error
            
            current_app.logger.info(f"Requested feedback from {feedback_provider_id} for {employee_id}")
            return feedback, None
            
        except Exception as e:
            current_app.logger.error(f"Error requesting feedback: {e}")
            return None, str(e)

    @classmethod
    def get_feedback_statistics(cls, session, employee_id=None, cycle_id=None):
        """Get feedback statistics."""
        try:
            query = session.query(cls)
            
            if employee_id:
                query = query.filter_by(employee_id=employee_id)
            
            if cycle_id:
                query = query.filter_by(cycle_id=cycle_id)
            
            feedback_entries = query.all()
            
            total_feedback = len(feedback_entries)
            submitted_feedback = len([f for f in feedback_entries if f.status in ['SUBMITTED', 'REVIEWED', 'SHARED']])
            pending_feedback = len([f for f in feedback_entries if f.status in ['REQUESTED', 'IN_PROGRESS']])
            overdue_feedback = len([f for f in feedback_entries if f.is_overdue()])
            
            # Calculate average ratings
            overall_ratings = [float(f.overall_rating) for f in feedback_entries if f.overall_rating]
            avg_overall_rating = sum(overall_ratings) / len(overall_ratings) if overall_ratings else 0
            
            # Feedback by source
            source_breakdown = {}
            for feedback in feedback_entries:
                source = feedback.feedback_source
                if source not in source_breakdown:
                    source_breakdown[source] = 0
                source_breakdown[source] += 1
            
            return {
                "total_feedback": total_feedback,
                "submitted_feedback": submitted_feedback,
                "pending_feedback": pending_feedback,
                "overdue_feedback": overdue_feedback,
                "completion_rate": round(submitted_feedback / total_feedback * 100, 2) if total_feedback > 0 else 0,
                "average_overall_rating": round(avg_overall_rating, 2),
                "source_breakdown": source_breakdown
            }
            
        except Exception as e:
            current_app.logger.error(f"Error getting feedback statistics: {e}")
            return {
                "total_feedback": 0,
                "submitted_feedback": 0,
                "pending_feedback": 0,
                "overdue_feedback": 0,
                "completion_rate": 0,
                "average_overall_rating": 0,
                "source_breakdown": {}
            }

    @classmethod
    def get_360_feedback_summary(cls, session, employee_id, cycle_id=None):
        """Get 360-degree feedback summary for an employee."""
        try:
            query = session.query(cls).filter(
                cls.employee_id == employee_id,
                cls.status.in_(['SUBMITTED', 'REVIEWED', 'SHARED'])
            )
            
            if cycle_id:
                query = query.filter_by(cycle_id=cycle_id)
            
            feedback_entries = query.all()
            
            if not feedback_entries:
                return {
                    "total_feedback_count": 0,
                    "source_summary": {},
                    "average_ratings": {},
                    "common_themes": {}
                }
            
            # Group by source
            source_summary = {}
            for feedback in feedback_entries:
                source = feedback.feedback_source
                if source not in source_summary:
                    source_summary[source] = {
                        "count": 0,
                        "ratings": [],
                        "strengths": [],
                        "improvements": []
                    }
                
                source_summary[source]["count"] += 1
                
                if feedback.overall_rating:
                    source_summary[source]["ratings"].append(float(feedback.overall_rating))
                
                if feedback.strengths:
                    source_summary[source]["strengths"].append(feedback.strengths)
                
                if feedback.areas_for_improvement:
                    source_summary[source]["improvements"].append(feedback.areas_for_improvement)
            
            # Calculate average ratings by source
            for source in source_summary:
                ratings = source_summary[source]["ratings"]
                source_summary[source]["average_rating"] = round(sum(ratings) / len(ratings), 2) if ratings else 0
            
            # Overall averages
            all_ratings = [float(f.overall_rating) for f in feedback_entries if f.overall_rating]
            overall_average = round(sum(all_ratings) / len(all_ratings), 2) if all_ratings else 0
            
            return {
                "total_feedback_count": len(feedback_entries),
                "source_summary": source_summary,
                "overall_average_rating": overall_average,
                "feedback_period": {
                    "start": min(f.feedback_period_start for f in feedback_entries if f.feedback_period_start).strftime('%Y-%m-%d') if any(f.feedback_period_start for f in feedback_entries) else None,
                    "end": max(f.feedback_period_end for f in feedback_entries if f.feedback_period_end).strftime('%Y-%m-%d') if any(f.feedback_period_end for f in feedback_entries) else None
                }
            }
            
        except Exception as e:
            current_app.logger.error(f"Error getting 360 feedback summary: {e}")
            return {
                "total_feedback_count": 0,
                "source_summary": {},
                "overall_average_rating": 0,
                "feedback_period": {"start": None, "end": None}
            }
