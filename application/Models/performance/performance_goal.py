from application.database import db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime, date, timedelta
from flask import current_app
from decimal import Decimal
import json


class PerformanceGoal(db.Model):
    """
    Performance goals set by employees, managers, or derived from company objectives.
    Supports SMART goals with progress tracking and alignment to higher-level objectives.
    """
    __tablename__ = 'performance_goals'

    goal_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    employee_id = db.Column(UUID(as_uuid=True), nullable=False, index=True)
    cycle_id = db.Column(UUID(as_uuid=True), db.ForeignKey('performance_review_cycles.cycle_id'), nullable=False)
    
    # Goal details
    title = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    goal_category = db.Column(db.String(100))  # PERFORMANCE, DEVELOPMENT, BEHAVIORAL, PROJECT
    goal_type = db.Column(db.String(100), default='INDIVIDUAL')  # INDIVIDUAL, TEAM, DEPARTMENT, COMPANY
    
    # SMART goal components
    specific_description = db.Column(db.Text)  # Specific - What exactly will be accomplished
    measurable_criteria = db.Column(db.Text)  # Measurable - How will progress be measured
    achievable_rationale = db.Column(db.Text)  # Achievable - Why this goal is realistic
    relevant_justification = db.Column(db.Text)  # Relevant - How this aligns with role/company
    time_bound_deadline = db.Column(db.Date)  # Time-bound - When will this be completed
    
    # Progress tracking
    target_value = db.Column(db.Numeric(15, 2))  # Numeric target (e.g., sales amount, % improvement)
    current_value = db.Column(db.Numeric(15, 2), default=0)  # Current progress value
    unit_of_measure = db.Column(db.String(100))  # Unit (e.g., USD, %, hours, count)
    completion_percentage = db.Column(db.Numeric(5, 2), default=0)  # 0-100%
    
    # Weighting and priority
    weight = db.Column(db.Numeric(5, 2), default=100)  # Weight in overall performance (%)
    priority = db.Column(db.String(50), default='MEDIUM')  # HIGH, MEDIUM, LOW
    difficulty_level = db.Column(db.String(50), default='MODERATE')  # EASY, MODERATE, CHALLENGING, STRETCH
    
    # Timeline
    start_date = db.Column(db.Date, default=date.today)
    target_completion_date = db.Column(db.Date, nullable=False)
    actual_completion_date = db.Column(db.Date)
    
    # Milestones and checkpoints
    milestones = db.Column(db.Text)  # JSON array of milestone objects
    next_review_date = db.Column(db.Date)
    review_frequency = db.Column(db.String(50), default='MONTHLY')  # WEEKLY, MONTHLY, QUARTERLY
    
    # Alignment and dependencies
    parent_goal_id = db.Column(UUID(as_uuid=True))  # Links to higher-level goal
    aligned_company_objective = db.Column(db.String(255))  # Company objective this supports
    department_alignment = db.Column(db.String(255))  # Department goal this supports
    
    # Support and resources
    required_resources = db.Column(db.Text)  # Resources needed to achieve goal
    support_needed = db.Column(db.Text)  # Support needed from others
    potential_obstacles = db.Column(db.Text)  # Anticipated challenges
    success_factors = db.Column(db.Text)  # Key factors for success
    
    # Status and approval
    status = db.Column(db.String(50), default='DRAFT')  # DRAFT, PENDING_APPROVAL, APPROVED, IN_PROGRESS, COMPLETED, CANCELLED, DEFERRED
    approval_status = db.Column(db.String(50), default='PENDING')  # PENDING, APPROVED, REJECTED, NEEDS_REVISION
    approved_by = db.Column(UUID(as_uuid=True))
    approval_date = db.Column(db.DateTime)
    approval_comments = db.Column(db.Text)
    
    # Performance impact
    performance_impact = db.Column(db.String(50))  # CRITICAL, HIGH, MEDIUM, LOW
    business_impact = db.Column(db.Text)  # How this goal impacts business results
    skill_development_areas = db.Column(db.Text)  # Skills this goal will develop
    
    # Tracking and updates
    last_update_date = db.Column(db.Date)
    update_frequency = db.Column(db.String(50), default='WEEKLY')  # DAILY, WEEKLY, MONTHLY
    progress_notes = db.Column(db.Text)  # Latest progress update
    
    # Metadata
    created_by = db.Column(UUID(as_uuid=True))
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    completed_by = db.Column(UUID(as_uuid=True))

    # Relationships
    progress_updates = db.relationship('GoalProgressUpdate', backref='goal', lazy='dynamic', cascade='all, delete-orphan')

    def to_dict(self):
        """Dictionary representation of the goal."""
        return {
            "goal_id": str(self.goal_id),
            "employee_id": str(self.employee_id),
            "cycle_id": str(self.cycle_id),
            "title": self.title,
            "description": self.description,
            "goal_category": self.goal_category,
            "goal_type": self.goal_type,
            "specific_description": self.specific_description,
            "measurable_criteria": self.measurable_criteria,
            "achievable_rationale": self.achievable_rationale,
            "relevant_justification": self.relevant_justification,
            "time_bound_deadline": self.time_bound_deadline.strftime('%Y-%m-%d') if self.time_bound_deadline else None,
            "target_value": float(self.target_value) if self.target_value else None,
            "current_value": float(self.current_value) if self.current_value else 0,
            "unit_of_measure": self.unit_of_measure,
            "completion_percentage": float(self.completion_percentage) if self.completion_percentage else 0,
            "weight": float(self.weight) if self.weight else 100,
            "priority": self.priority,
            "difficulty_level": self.difficulty_level,
            "start_date": self.start_date.strftime('%Y-%m-%d') if self.start_date else None,
            "target_completion_date": self.target_completion_date.strftime('%Y-%m-%d') if self.target_completion_date else None,
            "actual_completion_date": self.actual_completion_date.strftime('%Y-%m-%d') if self.actual_completion_date else None,
            "milestones": json.loads(self.milestones) if self.milestones else [],
            "next_review_date": self.next_review_date.strftime('%Y-%m-%d') if self.next_review_date else None,
            "review_frequency": self.review_frequency,
            "parent_goal_id": str(self.parent_goal_id) if self.parent_goal_id else None,
            "aligned_company_objective": self.aligned_company_objective,
            "department_alignment": self.department_alignment,
            "required_resources": self.required_resources,
            "support_needed": self.support_needed,
            "potential_obstacles": self.potential_obstacles,
            "success_factors": self.success_factors,
            "status": self.status,
            "approval_status": self.approval_status,
            "approved_by": str(self.approved_by) if self.approved_by else None,
            "approval_date": self.approval_date.strftime('%Y-%m-%d %H:%M:%S') if self.approval_date else None,
            "approval_comments": self.approval_comments,
            "performance_impact": self.performance_impact,
            "business_impact": self.business_impact,
            "skill_development_areas": self.skill_development_areas,
            "last_update_date": self.last_update_date.strftime('%Y-%m-%d') if self.last_update_date else None,
            "update_frequency": self.update_frequency,
            "progress_notes": self.progress_notes,
            "created_by": str(self.created_by) if self.created_by else None,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            "completed_by": str(self.completed_by) if self.completed_by else None,
            "days_remaining": self.get_days_remaining(),
            "is_overdue": self.is_overdue(),
            "progress_trend": self.get_progress_trend(),
            "is_on_track": self.is_on_track()
        }

    @classmethod
    def create_goal(cls, session, **kwargs):
        """Create a new performance goal."""
        try:
            # Handle JSON fields
            if 'milestones' in kwargs and isinstance(kwargs['milestones'], list):
                kwargs['milestones'] = json.dumps(kwargs['milestones'])
            
            goal = cls(**kwargs)
            session.add(goal)
            session.flush()
            
            current_app.logger.info(f"Created performance goal: {goal.title}")
            return goal, None
            
        except Exception as e:
            current_app.logger.error(f"Error creating performance goal: {e}")
            return None, str(e)

    @classmethod
    def get_goals_by_employee(cls, session, employee_id, cycle_id=None, status=None):
        """Get all goals for an employee."""
        query = session.query(cls).filter_by(employee_id=employee_id)
        
        if cycle_id:
            query = query.filter_by(cycle_id=cycle_id)
        
        if status:
            if isinstance(status, list):
                query = query.filter(cls.status.in_(status))
            else:
                query = query.filter_by(status=status)
        
        return query.order_by(cls.priority.desc(), cls.target_completion_date).all()

    @classmethod
    def get_goals_by_cycle(cls, session, cycle_id, status=None):
        """Get all goals for a review cycle."""
        query = session.query(cls).filter_by(cycle_id=cycle_id)
        
        if status:
            query = query.filter_by(status=status)
        
        return query.order_by(cls.target_completion_date).all()

    @classmethod
    def get_goal_by_id(cls, session, goal_id):
        """Get goal by ID."""
        return session.query(cls).filter_by(goal_id=goal_id).first()

    def update_progress(self, session, current_value=None, completion_percentage=None, progress_notes=None, updated_by=None):
        """Update goal progress."""
        try:
            if current_value is not None:
                self.current_value = Decimal(str(current_value))
                
                # Auto-calculate completion percentage if target value exists
                if self.target_value and self.target_value > 0:
                    calculated_percentage = (self.current_value / self.target_value) * 100
                    self.completion_percentage = min(Decimal('100'), max(Decimal('0'), calculated_percentage))
            
            if completion_percentage is not None:
                self.completion_percentage = Decimal(str(completion_percentage))
                
                # Auto-calculate current value if target value exists
                if self.target_value and completion_percentage is not None:
                    self.current_value = (self.target_value * Decimal(str(completion_percentage))) / 100
            
            if progress_notes:
                self.progress_notes = progress_notes
            
            self.last_update_date = date.today()
            self.updated_at = datetime.now()
            
            # Create progress update record
            from application.Models.performance.goal_progress_update import GoalProgressUpdate
            
            progress_update = GoalProgressUpdate(
                goal_id=self.goal_id,
                update_date=date.today(),
                previous_value=self.current_value,
                new_value=current_value if current_value is not None else self.current_value,
                completion_percentage=self.completion_percentage,
                notes=progress_notes,
                updated_by=updated_by
            )
            session.add(progress_update)
            
            # Update status based on completion
            if self.completion_percentage >= 100:
                self.status = 'COMPLETED'
                self.actual_completion_date = date.today()
                self.completed_by = updated_by
            elif self.completion_percentage > 0:
                self.status = 'IN_PROGRESS'
            
            session.commit()
            
            current_app.logger.info(f"Updated goal progress: {self.goal_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error updating goal progress: {e}")
            return False, str(e)

    def approve_goal(self, session, approved_by, approval_comments=None):
        """Approve the goal."""
        try:
            self.approval_status = 'APPROVED'
            self.approved_by = approved_by
            self.approval_date = datetime.now()
            self.approval_comments = approval_comments
            
            if self.status == 'DRAFT':
                self.status = 'APPROVED'
            
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Approved goal: {self.goal_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error approving goal: {e}")
            return False, str(e)

    def reject_goal(self, session, rejected_by, rejection_comments):
        """Reject the goal."""
        try:
            self.approval_status = 'REJECTED'
            self.approved_by = rejected_by
            self.approval_date = datetime.now()
            self.approval_comments = rejection_comments
            self.status = 'DRAFT'  # Send back to draft for revision
            
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Rejected goal: {self.goal_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error rejecting goal: {e}")
            return False, str(e)

    def complete_goal(self, session, completed_by, completion_notes=None):
        """Mark goal as completed."""
        try:
            self.status = 'COMPLETED'
            self.completion_percentage = 100
            self.actual_completion_date = date.today()
            self.completed_by = completed_by
            
            if completion_notes:
                self.progress_notes = completion_notes
            
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Completed goal: {self.goal_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error completing goal: {e}")
            return False, str(e)

    def get_days_remaining(self):
        """Get number of days remaining to complete the goal."""
        if not self.target_completion_date or self.status == 'COMPLETED':
            return 0
        
        days_remaining = (self.target_completion_date - date.today()).days
        return max(0, days_remaining)

    def is_overdue(self):
        """Check if goal is overdue."""
        if self.status == 'COMPLETED' or not self.target_completion_date:
            return False
        
        return date.today() > self.target_completion_date

    def is_on_track(self):
        """Check if goal progress is on track based on time elapsed."""
        if not self.start_date or not self.target_completion_date:
            return True
        
        total_days = (self.target_completion_date - self.start_date).days
        elapsed_days = (date.today() - self.start_date).days
        
        if total_days <= 0:
            return True
        
        expected_progress = (elapsed_days / total_days) * 100
        actual_progress = float(self.completion_percentage or 0)
        
        # Consider on track if within 10% of expected progress
        return actual_progress >= (expected_progress - 10)

    def get_progress_trend(self):
        """Get progress trend based on recent updates."""
        try:
            recent_updates = self.progress_updates.filter(
                GoalProgressUpdate.update_date >= date.today() - timedelta(days=30)
            ).order_by(GoalProgressUpdate.update_date.desc()).limit(5).all()
            
            if len(recent_updates) < 2:
                return 'STABLE'
            
            # Calculate trend based on completion percentage changes
            recent_changes = []
            for i in range(len(recent_updates) - 1):
                change = float(recent_updates[i].completion_percentage) - float(recent_updates[i + 1].completion_percentage)
                recent_changes.append(change)
            
            avg_change = sum(recent_changes) / len(recent_changes)
            
            if avg_change > 5:
                return 'IMPROVING'
            elif avg_change < -2:
                return 'DECLINING'
            else:
                return 'STABLE'
                
        except Exception as e:
            current_app.logger.error(f"Error calculating progress trend: {e}")
            return 'UNKNOWN'

    def add_milestone(self, session, milestone_title, milestone_date, milestone_description=None):
        """Add a milestone to the goal."""
        try:
            milestones = json.loads(self.milestones) if self.milestones else []
            
            new_milestone = {
                "id": str(uuid.uuid4()),
                "title": milestone_title,
                "description": milestone_description,
                "target_date": milestone_date.strftime('%Y-%m-%d') if isinstance(milestone_date, date) else milestone_date,
                "status": "PENDING",
                "completion_date": None,
                "created_at": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            milestones.append(new_milestone)
            self.milestones = json.dumps(milestones)
            self.updated_at = datetime.now()
            
            session.commit()
            
            current_app.logger.info(f"Added milestone to goal: {self.goal_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error adding milestone: {e}")
            return False, str(e)

    def complete_milestone(self, session, milestone_id):
        """Mark a milestone as completed."""
        try:
            milestones = json.loads(self.milestones) if self.milestones else []
            
            for milestone in milestones:
                if milestone.get('id') == milestone_id:
                    milestone['status'] = 'COMPLETED'
                    milestone['completion_date'] = date.today().strftime('%Y-%m-%d')
                    break
            
            self.milestones = json.dumps(milestones)
            self.updated_at = datetime.now()
            
            session.commit()
            
            current_app.logger.info(f"Completed milestone {milestone_id} for goal: {self.goal_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error completing milestone: {e}")
            return False, str(e)

    def get_milestones_list(self):
        """Get milestones as a list."""
        try:
            return json.loads(self.milestones) if self.milestones else []
        except:
            return []

    def set_milestones(self, milestones_list):
        """Set milestones from a list."""
        try:
            self.milestones = json.dumps(milestones_list) if milestones_list else None
        except:
            self.milestones = None

    @classmethod
    def get_overdue_goals(cls, session, employee_id=None, days_overdue=0):
        """Get overdue goals."""
        overdue_date = date.today() - timedelta(days=days_overdue)
        
        query = session.query(cls).filter(
            cls.target_completion_date < overdue_date,
            cls.status.in_(['APPROVED', 'IN_PROGRESS'])
        )
        
        if employee_id:
            query = query.filter_by(employee_id=employee_id)
        
        return query.order_by(cls.target_completion_date).all()

    @classmethod
    def get_goals_needing_review(cls, session, employee_id=None):
        """Get goals that need review based on review frequency."""
        query = session.query(cls).filter(
            cls.status.in_(['APPROVED', 'IN_PROGRESS']),
            cls.next_review_date <= date.today()
        )
        
        if employee_id:
            query = query.filter_by(employee_id=employee_id)
        
        return query.order_by(cls.next_review_date).all()
