from application.database import db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime, date
from flask import current_app
from decimal import Decimal
import json


class PerformanceRatingScale(db.Model):
    """
    Rating scales used for performance evaluations.
    Companies can define custom rating scales with different point systems and descriptions.
    """
    __tablename__ = 'performance_rating_scales'

    scale_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    company_id = db.Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # Scale details
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    scale_type = db.Column(db.String(100), default='NUMERIC')  # NUMERIC, DESCRIPTIVE, MIXED
    
    # Scale configuration
    min_value = db.Column(db.Numeric(3, 2), default=1)
    max_value = db.Column(db.Numeric(3, 2), default=5)
    increment = db.Column(db.Numeric(3, 2), default=1)  # 0.5 for half-point scales
    
    # Scale labels and descriptions
    scale_points = db.Column(db.Text, nullable=False)  # JSON array of scale point definitions
    
    # Usage settings
    is_default = db.Column(db.Boolean, default=False)
    is_active = db.Column(db.Boolean, default=True)
    allow_na = db.Column(db.Boolean, default=False)  # Allow "Not Applicable" ratings
    require_comments = db.Column(db.Boolean, default=False)  # Require comments for all ratings
    require_comments_for_extremes = db.Column(db.Boolean, default=True)  # Require comments for highest/lowest ratings
    
    # Distribution guidelines
    forced_distribution = db.Column(db.Boolean, default=False)
    distribution_guidelines = db.Column(db.Text)  # JSON with percentage guidelines per rating
    
    # Metadata
    created_by = db.Column(UUID(as_uuid=True))
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    def to_dict(self):
        """Dictionary representation of the rating scale."""
        return {
            "scale_id": str(self.scale_id),
            "company_id": str(self.company_id),
            "name": self.name,
            "description": self.description,
            "scale_type": self.scale_type,
            "min_value": float(self.min_value) if self.min_value else None,
            "max_value": float(self.max_value) if self.max_value else None,
            "increment": float(self.increment) if self.increment else None,
            "scale_points": self.get_scale_points_list(),
            "is_default": self.is_default,
            "is_active": self.is_active,
            "allow_na": self.allow_na,
            "require_comments": self.require_comments,
            "require_comments_for_extremes": self.require_comments_for_extremes,
            "forced_distribution": self.forced_distribution,
            "distribution_guidelines": self.get_distribution_guidelines_dict(),
            "created_by": str(self.created_by) if self.created_by else None,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            "point_count": len(self.get_scale_points_list())
        }

    @classmethod
    def create_scale(cls, session, **kwargs):
        """Create a new rating scale."""
        try:
            # Handle JSON fields
            if 'scale_points' in kwargs and isinstance(kwargs['scale_points'], list):
                kwargs['scale_points'] = json.dumps(kwargs['scale_points'])
            
            if 'distribution_guidelines' in kwargs and isinstance(kwargs['distribution_guidelines'], dict):
                kwargs['distribution_guidelines'] = json.dumps(kwargs['distribution_guidelines'])
            
            scale = cls(**kwargs)
            session.add(scale)
            session.flush()
            
            # If this is set as default, unset other defaults
            if scale.is_default:
                session.query(cls).filter(
                    cls.company_id == scale.company_id,
                    cls.scale_id != scale.scale_id
                ).update({'is_default': False})
            
            session.commit()
            
            current_app.logger.info(f"Created performance rating scale: {scale.name}")
            return scale, None
            
        except Exception as e:
            current_app.logger.error(f"Error creating rating scale: {e}")
            return None, str(e)

    @classmethod
    def get_scales_by_company(cls, session, company_id, include_inactive=False):
        """Get all rating scales for a company."""
        query = session.query(cls).filter_by(company_id=company_id)
        
        if not include_inactive:
            query = query.filter_by(is_active=True)
        
        return query.order_by(cls.is_default.desc(), cls.name).all()

    @classmethod
    def get_default_scale(cls, session, company_id):
        """Get the default rating scale for a company."""
        return session.query(cls).filter_by(
            company_id=company_id,
            is_default=True,
            is_active=True
        ).first()

    @classmethod
    def get_scale_by_id(cls, session, scale_id):
        """Get rating scale by ID."""
        return session.query(cls).filter_by(scale_id=scale_id).first()

    def update_scale(self, session, **kwargs):
        """Update rating scale details."""
        try:
            # Handle JSON fields
            if 'scale_points' in kwargs and isinstance(kwargs['scale_points'], list):
                kwargs['scale_points'] = json.dumps(kwargs['scale_points'])
            
            if 'distribution_guidelines' in kwargs and isinstance(kwargs['distribution_guidelines'], dict):
                kwargs['distribution_guidelines'] = json.dumps(kwargs['distribution_guidelines'])
            
            for key, value in kwargs.items():
                if hasattr(self, key):
                    setattr(self, key, value)
            
            # If this is set as default, unset other defaults
            if kwargs.get('is_default'):
                session.query(PerformanceRatingScale).filter(
                    PerformanceRatingScale.company_id == self.company_id,
                    PerformanceRatingScale.scale_id != self.scale_id
                ).update({'is_default': False})
            
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Updated rating scale: {self.scale_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error updating rating scale: {e}")
            return False, str(e)

    def deactivate_scale(self, session):
        """Deactivate the rating scale."""
        try:
            if self.is_default:
                return False, "Cannot deactivate the default rating scale"
            
            self.is_active = False
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Deactivated rating scale: {self.scale_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error deactivating rating scale: {e}")
            return False, str(e)

    def get_scale_points_list(self):
        """Get scale points as a list."""
        try:
            return json.loads(self.scale_points) if self.scale_points else []
        except:
            return []

    def set_scale_points(self, points_list):
        """Set scale points from a list."""
        try:
            self.scale_points = json.dumps(points_list) if points_list else None
        except:
            self.scale_points = None

    def get_distribution_guidelines_dict(self):
        """Get distribution guidelines as dictionary."""
        try:
            return json.loads(self.distribution_guidelines) if self.distribution_guidelines else {}
        except:
            return {}

    def set_distribution_guidelines(self, guidelines_dict):
        """Set distribution guidelines from dictionary."""
        try:
            self.distribution_guidelines = json.dumps(guidelines_dict) if guidelines_dict else None
        except:
            self.distribution_guidelines = None

    def validate_rating(self, rating_value):
        """Validate if a rating value is valid for this scale."""
        try:
            if rating_value is None:
                return self.allow_na, "Rating cannot be null" if not self.allow_na else None
            
            rating_decimal = Decimal(str(rating_value))
            
            if rating_decimal < self.min_value or rating_decimal > self.max_value:
                return False, f"Rating must be between {self.min_value} and {self.max_value}"
            
            # Check if rating aligns with increment
            if self.increment:
                steps_from_min = (rating_decimal - self.min_value) / self.increment
                if not steps_from_min.is_integer():
                    return False, f"Rating must be in increments of {self.increment}"
            
            return True, None
            
        except Exception as e:
            return False, f"Invalid rating value: {e}"

    def get_rating_description(self, rating_value):
        """Get description for a specific rating value."""
        try:
            scale_points = self.get_scale_points_list()
            
            for point in scale_points:
                if point.get('value') == float(rating_value):
                    return point.get('description', point.get('label', ''))
            
            return f"Rating {rating_value}"
            
        except Exception as e:
            current_app.logger.error(f"Error getting rating description: {e}")
            return f"Rating {rating_value}"

    def is_extreme_rating(self, rating_value):
        """Check if rating is at the extremes (highest or lowest)."""
        try:
            rating_decimal = Decimal(str(rating_value))
            return rating_decimal == self.min_value or rating_decimal == self.max_value
        except:
            return False

    def get_valid_rating_values(self):
        """Get list of all valid rating values for this scale."""
        try:
            values = []
            current_value = self.min_value
            
            while current_value <= self.max_value:
                values.append(float(current_value))
                current_value += self.increment
            
            return values
            
        except Exception as e:
            current_app.logger.error(f"Error getting valid rating values: {e}")
            return []

    @classmethod
    def create_default_scales(cls, session, company_id, created_by=None):
        """Create default rating scales for a new company."""
        try:
            # 5-Point Numeric Scale
            five_point_scale = {
                "company_id": company_id,
                "name": "5-Point Scale",
                "description": "Standard 5-point performance rating scale",
                "scale_type": "NUMERIC",
                "min_value": 1,
                "max_value": 5,
                "increment": 1,
                "scale_points": [
                    {"value": 1, "label": "Below Expectations", "description": "Performance consistently falls short of expectations"},
                    {"value": 2, "label": "Partially Meets Expectations", "description": "Performance occasionally meets expectations"},
                    {"value": 3, "label": "Meets Expectations", "description": "Performance consistently meets expectations"},
                    {"value": 4, "label": "Exceeds Expectations", "description": "Performance frequently exceeds expectations"},
                    {"value": 5, "label": "Outstanding", "description": "Performance consistently exceeds expectations and sets example for others"}
                ],
                "is_default": True,
                "require_comments_for_extremes": True,
                "created_by": created_by
            }
            
            scale1, error1 = cls.create_scale(session, **five_point_scale)
            if error1:
                return False, error1
            
            # 4-Point Scale (No "Average" option)
            four_point_scale = {
                "company_id": company_id,
                "name": "4-Point Scale",
                "description": "4-point scale without neutral option",
                "scale_type": "NUMERIC",
                "min_value": 1,
                "max_value": 4,
                "increment": 1,
                "scale_points": [
                    {"value": 1, "label": "Needs Improvement", "description": "Performance requires significant improvement"},
                    {"value": 2, "label": "Developing", "description": "Performance is developing but not yet at expected level"},
                    {"value": 3, "label": "Proficient", "description": "Performance meets expectations consistently"},
                    {"value": 4, "label": "Advanced", "description": "Performance exceeds expectations and demonstrates mastery"}
                ],
                "is_default": False,
                "require_comments_for_extremes": True,
                "created_by": created_by
            }
            
            scale2, error2 = cls.create_scale(session, **four_point_scale)
            if error2:
                return False, error2
            
            # 10-Point Scale for detailed evaluation
            ten_point_scale = {
                "company_id": company_id,
                "name": "10-Point Scale",
                "description": "Detailed 10-point performance rating scale",
                "scale_type": "NUMERIC",
                "min_value": 1,
                "max_value": 10,
                "increment": 1,
                "scale_points": [
                    {"value": 1, "label": "Unacceptable", "description": "Performance is unacceptable"},
                    {"value": 2, "label": "Poor", "description": "Performance is poor"},
                    {"value": 3, "label": "Below Average", "description": "Performance is below average"},
                    {"value": 4, "label": "Fair", "description": "Performance is fair"},
                    {"value": 5, "label": "Average", "description": "Performance is average"},
                    {"value": 6, "label": "Above Average", "description": "Performance is above average"},
                    {"value": 7, "label": "Good", "description": "Performance is good"},
                    {"value": 8, "label": "Very Good", "description": "Performance is very good"},
                    {"value": 9, "label": "Excellent", "description": "Performance is excellent"},
                    {"value": 10, "label": "Outstanding", "description": "Performance is outstanding"}
                ],
                "is_default": False,
                "require_comments_for_extremes": True,
                "created_by": created_by
            }
            
            scale3, error3 = cls.create_scale(session, **ten_point_scale)
            if error3:
                return False, error3
            
            current_app.logger.info(f"Created default rating scales for company: {company_id}")
            return True, None
            
        except Exception as e:
            current_app.logger.error(f"Error creating default rating scales: {e}")
            return False, str(e)

    def get_usage_statistics(self, session):
        """Get usage statistics for this rating scale."""
        try:
            from application.Models.performance.performance_review import PerformanceReview
            
            # Count reviews using this scale (would need to add scale_id to review table)
            # For now, return basic stats
            
            return {
                "total_reviews": 0,  # Would count actual usage
                "average_rating": 0,  # Would calculate from actual reviews
                "rating_distribution": {},  # Would show distribution of ratings
                "last_used": None  # Would show last usage date
            }
            
        except Exception as e:
            current_app.logger.error(f"Error getting scale usage statistics: {e}")
            return {
                "total_reviews": 0,
                "average_rating": 0,
                "rating_distribution": {},
                "last_used": None
            }

    def clone_scale(self, session, new_name, created_by=None):
        """Clone this rating scale with a new name."""
        try:
            new_scale = PerformanceRatingScale(
                company_id=self.company_id,
                name=new_name,
                description=f"Cloned from: {self.name}",
                scale_type=self.scale_type,
                min_value=self.min_value,
                max_value=self.max_value,
                increment=self.increment,
                scale_points=self.scale_points,
                allow_na=self.allow_na,
                require_comments=self.require_comments,
                require_comments_for_extremes=self.require_comments_for_extremes,
                forced_distribution=self.forced_distribution,
                distribution_guidelines=self.distribution_guidelines,
                is_default=False,  # Cloned scales are never default
                created_by=created_by
            )
            
            session.add(new_scale)
            session.commit()
            
            current_app.logger.info(f"Cloned rating scale {self.scale_id} to {new_scale.scale_id}")
            return new_scale, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error cloning rating scale: {e}")
            return None, str(e)
