from application.database import db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime, date, timedelta
from flask import current_app
from decimal import Decimal
import json


class PerformanceReview(db.Model):
    """
    Individual performance reviews conducted as part of review cycles.
    Supports self-reviews, manager reviews, peer feedback, and 360-degree reviews.
    """
    __tablename__ = 'performance_reviews'

    review_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    cycle_id = db.Column(UUID(as_uuid=True), db.ForeignKey('performance_review_cycles.cycle_id'), nullable=False)
    employee_id = db.Column(UUID(as_uuid=True), nullable=False, index=True)
    reviewer_id = db.Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # Review type and context
    review_type = db.Column(db.String(100), nullable=False)  # SELF, MANAGER, PEER, SUBORDINATE, SKIP_LEVEL, CUSTOMER
    review_relationship = db.Column(db.String(100))  # DIRECT_REPORT, COLLEAGUE, CROSS_FUNCTIONAL, EXTERNAL
    
    # Timeline
    assigned_date = db.Column(db.DateTime, default=datetime.now)
    due_date = db.Column(db.Date)
    started_date = db.Column(db.DateTime)
    submitted_date = db.Column(db.DateTime)
    completed_date = db.Column(db.DateTime)
    
    # Status tracking
    status = db.Column(db.String(50), default='PENDING')  # PENDING, IN_PROGRESS, SUBMITTED, COMPLETED, OVERDUE, CANCELLED
    completion_percentage = db.Column(db.Numeric(5, 2), default=0)
    
    # Overall ratings
    overall_rating = db.Column(db.Numeric(3, 2))  # Overall performance rating
    overall_rating_justification = db.Column(db.Text)  # Explanation for overall rating
    
    # Performance areas
    job_knowledge_rating = db.Column(db.Numeric(3, 2))
    job_knowledge_comments = db.Column(db.Text)
    
    quality_of_work_rating = db.Column(db.Numeric(3, 2))
    quality_of_work_comments = db.Column(db.Text)
    
    productivity_rating = db.Column(db.Numeric(3, 2))
    productivity_comments = db.Column(db.Text)
    
    communication_rating = db.Column(db.Numeric(3, 2))
    communication_comments = db.Column(db.Text)
    
    teamwork_rating = db.Column(db.Numeric(3, 2))
    teamwork_comments = db.Column(db.Text)
    
    leadership_rating = db.Column(db.Numeric(3, 2))
    leadership_comments = db.Column(db.Text)
    
    initiative_rating = db.Column(db.Numeric(3, 2))
    initiative_comments = db.Column(db.Text)
    
    problem_solving_rating = db.Column(db.Numeric(3, 2))
    problem_solving_comments = db.Column(db.Text)
    
    # Goal achievement
    goals_achievement_rating = db.Column(db.Numeric(3, 2))
    goals_achievement_comments = db.Column(db.Text)
    
    # Competency ratings (JSON structure for flexibility)
    competency_ratings = db.Column(db.Text)  # JSON with competency_id: {rating, comments}
    
    # Qualitative feedback
    strengths = db.Column(db.Text)
    areas_for_improvement = db.Column(db.Text)
    achievements = db.Column(db.Text)
    challenges_faced = db.Column(db.Text)
    
    # Development and growth
    development_needs = db.Column(db.Text)
    career_aspirations = db.Column(db.Text)
    training_recommendations = db.Column(db.Text)
    mentoring_needs = db.Column(db.Text)
    
    # Future planning
    short_term_goals = db.Column(db.Text)  # Next 3-6 months
    long_term_goals = db.Column(db.Text)  # Next 1-2 years
    support_needed = db.Column(db.Text)
    
    # Manager-specific fields
    promotion_readiness = db.Column(db.String(50))  # READY, DEVELOPING, NOT_READY
    retention_risk = db.Column(db.String(50))  # LOW, MEDIUM, HIGH
    succession_potential = db.Column(db.String(50))  # HIGH, MEDIUM, LOW
    
    # Performance improvement
    requires_pip = db.Column(db.Boolean, default=False)  # Performance Improvement Plan needed
    pip_reason = db.Column(db.Text)
    
    # Calibration and validation
    calibrated = db.Column(db.Boolean, default=False)
    calibration_notes = db.Column(db.Text)
    calibrated_by = db.Column(UUID(as_uuid=True))
    calibration_date = db.Column(db.DateTime)
    
    # Approval workflow
    requires_approval = db.Column(db.Boolean, default=False)
    approved = db.Column(db.Boolean, default=False)
    approved_by = db.Column(UUID(as_uuid=True))
    approval_date = db.Column(db.DateTime)
    approval_comments = db.Column(db.Text)
    
    # Employee acknowledgment
    employee_acknowledged = db.Column(db.Boolean, default=False)
    acknowledgment_date = db.Column(db.DateTime)
    employee_comments = db.Column(db.Text)  # Employee response to review
    employee_agreement = db.Column(db.String(50))  # AGREE, PARTIALLY_AGREE, DISAGREE
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    # Additional data
    custom_fields = db.Column(db.Text)  # JSON for company-specific fields

    def to_dict(self):
        """Dictionary representation of the performance review."""
        return {
            "review_id": str(self.review_id),
            "cycle_id": str(self.cycle_id),
            "employee_id": str(self.employee_id),
            "reviewer_id": str(self.reviewer_id),
            "review_type": self.review_type,
            "review_relationship": self.review_relationship,
            "assigned_date": self.assigned_date.strftime('%Y-%m-%d %H:%M:%S') if self.assigned_date else None,
            "due_date": self.due_date.strftime('%Y-%m-%d') if self.due_date else None,
            "started_date": self.started_date.strftime('%Y-%m-%d %H:%M:%S') if self.started_date else None,
            "submitted_date": self.submitted_date.strftime('%Y-%m-%d %H:%M:%S') if self.submitted_date else None,
            "completed_date": self.completed_date.strftime('%Y-%m-%d %H:%M:%S') if self.completed_date else None,
            "status": self.status,
            "completion_percentage": float(self.completion_percentage) if self.completion_percentage else 0,
            "overall_rating": float(self.overall_rating) if self.overall_rating else None,
            "overall_rating_justification": self.overall_rating_justification,
            "performance_ratings": {
                "job_knowledge": {
                    "rating": float(self.job_knowledge_rating) if self.job_knowledge_rating else None,
                    "comments": self.job_knowledge_comments
                },
                "quality_of_work": {
                    "rating": float(self.quality_of_work_rating) if self.quality_of_work_rating else None,
                    "comments": self.quality_of_work_comments
                },
                "productivity": {
                    "rating": float(self.productivity_rating) if self.productivity_rating else None,
                    "comments": self.productivity_comments
                },
                "communication": {
                    "rating": float(self.communication_rating) if self.communication_rating else None,
                    "comments": self.communication_comments
                },
                "teamwork": {
                    "rating": float(self.teamwork_rating) if self.teamwork_rating else None,
                    "comments": self.teamwork_comments
                },
                "leadership": {
                    "rating": float(self.leadership_rating) if self.leadership_rating else None,
                    "comments": self.leadership_comments
                },
                "initiative": {
                    "rating": float(self.initiative_rating) if self.initiative_rating else None,
                    "comments": self.initiative_comments
                },
                "problem_solving": {
                    "rating": float(self.problem_solving_rating) if self.problem_solving_rating else None,
                    "comments": self.problem_solving_comments
                }
            },
            "goals_achievement": {
                "rating": float(self.goals_achievement_rating) if self.goals_achievement_rating else None,
                "comments": self.goals_achievement_comments
            },
            "competency_ratings": self.get_competency_ratings_dict(),
            "qualitative_feedback": {
                "strengths": self.strengths,
                "areas_for_improvement": self.areas_for_improvement,
                "achievements": self.achievements,
                "challenges_faced": self.challenges_faced
            },
            "development": {
                "development_needs": self.development_needs,
                "career_aspirations": self.career_aspirations,
                "training_recommendations": self.training_recommendations,
                "mentoring_needs": self.mentoring_needs
            },
            "future_planning": {
                "short_term_goals": self.short_term_goals,
                "long_term_goals": self.long_term_goals,
                "support_needed": self.support_needed
            },
            "manager_assessment": {
                "promotion_readiness": self.promotion_readiness,
                "retention_risk": self.retention_risk,
                "succession_potential": self.succession_potential,
                "requires_pip": self.requires_pip,
                "pip_reason": self.pip_reason
            },
            "calibration": {
                "calibrated": self.calibrated,
                "calibration_notes": self.calibration_notes,
                "calibrated_by": str(self.calibrated_by) if self.calibrated_by else None,
                "calibration_date": self.calibration_date.strftime('%Y-%m-%d %H:%M:%S') if self.calibration_date else None
            },
            "approval": {
                "requires_approval": self.requires_approval,
                "approved": self.approved,
                "approved_by": str(self.approved_by) if self.approved_by else None,
                "approval_date": self.approval_date.strftime('%Y-%m-%d %H:%M:%S') if self.approval_date else None,
                "approval_comments": self.approval_comments
            },
            "employee_acknowledgment": {
                "acknowledged": self.employee_acknowledged,
                "acknowledgment_date": self.acknowledgment_date.strftime('%Y-%m-%d %H:%M:%S') if self.acknowledgment_date else None,
                "employee_comments": self.employee_comments,
                "employee_agreement": self.employee_agreement
            },
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            "custom_fields": self.get_custom_fields_dict(),
            "is_overdue": self.is_overdue(),
            "days_overdue": self.get_days_overdue(),
            "average_rating": self.calculate_average_rating()
        }

    @classmethod
    def create_review(cls, session, **kwargs):
        """Create a new performance review."""
        try:
            # Handle JSON fields
            if 'competency_ratings' in kwargs and isinstance(kwargs['competency_ratings'], dict):
                kwargs['competency_ratings'] = json.dumps(kwargs['competency_ratings'])
            
            if 'custom_fields' in kwargs and isinstance(kwargs['custom_fields'], dict):
                kwargs['custom_fields'] = json.dumps(kwargs['custom_fields'])
            
            review = cls(**kwargs)
            session.add(review)
            session.flush()
            
            current_app.logger.info(f"Created performance review: {review.review_id}")
            return review, None
            
        except Exception as e:
            current_app.logger.error(f"Error creating performance review: {e}")
            return None, str(e)

    @classmethod
    def get_reviews_by_employee(cls, session, employee_id, cycle_id=None, review_type=None):
        """Get all reviews for an employee."""
        query = session.query(cls).filter_by(employee_id=employee_id)
        
        if cycle_id:
            query = query.filter_by(cycle_id=cycle_id)
        
        if review_type:
            query = query.filter_by(review_type=review_type)
        
        return query.order_by(cls.assigned_date.desc()).all()

    @classmethod
    def get_reviews_by_reviewer(cls, session, reviewer_id, status=None):
        """Get all reviews assigned to a reviewer."""
        query = session.query(cls).filter_by(reviewer_id=reviewer_id)
        
        if status:
            if isinstance(status, list):
                query = query.filter(cls.status.in_(status))
            else:
                query = query.filter_by(status=status)
        
        return query.order_by(cls.due_date).all()

    @classmethod
    def get_review_by_id(cls, session, review_id):
        """Get review by ID."""
        return session.query(cls).filter_by(review_id=review_id).first()

    def start_review(self, session):
        """Start the review process."""
        try:
            if self.status != 'PENDING':
                return False, f"Cannot start review with status: {self.status}"
            
            self.status = 'IN_PROGRESS'
            self.started_date = datetime.now()
            self.completion_percentage = 10  # Indicate review has been started
            self.updated_at = datetime.now()
            
            session.commit()
            
            current_app.logger.info(f"Started performance review: {self.review_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error starting review: {e}")
            return False, str(e)

    def submit_review(self, session):
        """Submit the review for approval."""
        try:
            if self.status not in ['IN_PROGRESS', 'PENDING']:
                return False, f"Cannot submit review with status: {self.status}"
            
            # Validate required fields based on review type
            if not self.overall_rating:
                return False, "Overall rating is required"
            
            self.status = 'SUBMITTED'
            self.submitted_date = datetime.now()
            self.completion_percentage = 90
            self.updated_at = datetime.now()
            
            session.commit()
            
            current_app.logger.info(f"Submitted performance review: {self.review_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error submitting review: {e}")
            return False, str(e)

    def complete_review(self, session, completed_by=None):
        """Complete the review process."""
        try:
            self.status = 'COMPLETED'
            self.completed_date = datetime.now()
            self.completion_percentage = 100
            self.updated_at = datetime.now()
            
            session.commit()
            
            current_app.logger.info(f"Completed performance review: {self.review_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error completing review: {e}")
            return False, str(e)

    def approve_review(self, session, approved_by, approval_comments=None):
        """Approve the review."""
        try:
            self.approved = True
            self.approved_by = approved_by
            self.approval_date = datetime.now()
            self.approval_comments = approval_comments
            
            if self.status == 'SUBMITTED':
                self.status = 'COMPLETED'
                self.completed_date = datetime.now()
                self.completion_percentage = 100
            
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Approved performance review: {self.review_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error approving review: {e}")
            return False, str(e)

    def employee_acknowledge(self, session, employee_comments=None, agreement=None):
        """Employee acknowledges the review."""
        try:
            self.employee_acknowledged = True
            self.acknowledgment_date = datetime.now()
            self.employee_comments = employee_comments
            self.employee_agreement = agreement
            self.updated_at = datetime.now()
            
            session.commit()
            
            current_app.logger.info(f"Employee acknowledged review: {self.review_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error in employee acknowledgment: {e}")
            return False, str(e)

    def calibrate_review(self, session, calibrated_by, calibration_notes=None):
        """Calibrate the review ratings."""
        try:
            self.calibrated = True
            self.calibrated_by = calibrated_by
            self.calibration_date = datetime.now()
            self.calibration_notes = calibration_notes
            self.updated_at = datetime.now()
            
            session.commit()
            
            current_app.logger.info(f"Calibrated performance review: {self.review_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error calibrating review: {e}")
            return False, str(e)

    def is_overdue(self):
        """Check if review is overdue."""
        if self.status in ['COMPLETED', 'CANCELLED'] or not self.due_date:
            return False
        
        return date.today() > self.due_date

    def get_days_overdue(self):
        """Get number of days overdue."""
        if not self.is_overdue():
            return 0
        
        return (date.today() - self.due_date).days

    def calculate_average_rating(self):
        """Calculate average rating across all performance areas."""
        ratings = [
            self.job_knowledge_rating,
            self.quality_of_work_rating,
            self.productivity_rating,
            self.communication_rating,
            self.teamwork_rating,
            self.leadership_rating,
            self.initiative_rating,
            self.problem_solving_rating,
            self.goals_achievement_rating
        ]
        
        valid_ratings = [float(r) for r in ratings if r is not None]
        
        if not valid_ratings:
            return None
        
        return round(sum(valid_ratings) / len(valid_ratings), 2)

    def get_competency_ratings_dict(self):
        """Get competency ratings as dictionary."""
        try:
            return json.loads(self.competency_ratings) if self.competency_ratings else {}
        except:
            return {}

    def set_competency_ratings(self, ratings_dict):
        """Set competency ratings from dictionary."""
        try:
            self.competency_ratings = json.dumps(ratings_dict) if ratings_dict else None
        except:
            self.competency_ratings = None

    def get_custom_fields_dict(self):
        """Get custom fields as dictionary."""
        try:
            return json.loads(self.custom_fields) if self.custom_fields else {}
        except:
            return {}

    def set_custom_fields(self, fields_dict):
        """Set custom fields from dictionary."""
        try:
            self.custom_fields = json.dumps(fields_dict) if fields_dict else None
        except:
            self.custom_fields = None

    @classmethod
    def get_overdue_reviews(cls, session, reviewer_id=None, days_overdue=0):
        """Get overdue reviews."""
        overdue_date = date.today() - timedelta(days=days_overdue)
        
        query = session.query(cls).filter(
            cls.due_date < overdue_date,
            cls.status.in_(['PENDING', 'IN_PROGRESS'])
        )
        
        if reviewer_id:
            query = query.filter_by(reviewer_id=reviewer_id)
        
        return query.order_by(cls.due_date).all()

    @classmethod
    def get_pending_approvals(cls, session, approver_id=None):
        """Get reviews pending approval."""
        query = session.query(cls).filter(
            cls.status == 'SUBMITTED',
            cls.requires_approval == True,
            cls.approved == False
        )
        
        if approver_id:
            # Would need to join with employee/manager relationship
            pass
        
        return query.order_by(cls.submitted_date).all()

    def update_completion_percentage(self, session):
        """Update completion percentage based on filled fields."""
        try:
            total_fields = 0
            completed_fields = 0
            
            # Core rating fields
            rating_fields = [
                self.overall_rating,
                self.job_knowledge_rating,
                self.quality_of_work_rating,
                self.productivity_rating,
                self.communication_rating,
                self.teamwork_rating,
                self.goals_achievement_rating
            ]
            
            for field in rating_fields:
                total_fields += 1
                if field is not None:
                    completed_fields += 1
            
            # Core text fields
            text_fields = [
                self.strengths,
                self.areas_for_improvement,
                self.development_needs
            ]
            
            for field in text_fields:
                total_fields += 1
                if field and field.strip():
                    completed_fields += 1
            
            if total_fields > 0:
                self.completion_percentage = Decimal(str(round(completed_fields / total_fields * 100, 2)))
            else:
                self.completion_percentage = 0
            
            self.updated_at = datetime.now()
            session.commit()
            
        except Exception as e:
            current_app.logger.error(f"Error updating completion percentage: {e}")

    @classmethod
    def get_review_statistics(cls, session, cycle_id=None, employee_id=None, reviewer_id=None):
        """Get review statistics."""
        try:
            query = session.query(cls)
            
            if cycle_id:
                query = query.filter_by(cycle_id=cycle_id)
            
            if employee_id:
                query = query.filter_by(employee_id=employee_id)
            
            if reviewer_id:
                query = query.filter_by(reviewer_id=reviewer_id)
            
            reviews = query.all()
            
            total_reviews = len(reviews)
            completed_reviews = len([r for r in reviews if r.status == 'COMPLETED'])
            pending_reviews = len([r for r in reviews if r.status == 'PENDING'])
            overdue_reviews = len([r for r in reviews if r.is_overdue()])
            
            # Calculate average ratings
            overall_ratings = [float(r.overall_rating) for r in reviews if r.overall_rating]
            avg_overall_rating = sum(overall_ratings) / len(overall_ratings) if overall_ratings else 0
            
            return {
                "total_reviews": total_reviews,
                "completed_reviews": completed_reviews,
                "pending_reviews": pending_reviews,
                "overdue_reviews": overdue_reviews,
                "completion_rate": round(completed_reviews / total_reviews * 100, 2) if total_reviews > 0 else 0,
                "average_overall_rating": round(avg_overall_rating, 2)
            }
            
        except Exception as e:
            current_app.logger.error(f"Error getting review statistics: {e}")
            return {
                "total_reviews": 0,
                "completed_reviews": 0,
                "pending_reviews": 0,
                "overdue_reviews": 0,
                "completion_rate": 0,
                "average_overall_rating": 0
            }
