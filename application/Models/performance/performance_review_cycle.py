from application.database import db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime, date, timedelta
from flask import current_app
from decimal import Decimal
import json


class PerformanceReviewCycle(db.Model):
    """
    Performance review cycles that define when and how performance reviews are conducted.
    Companies can have multiple cycles (annual, quarterly, monthly) running simultaneously.
    """
    __tablename__ = 'performance_review_cycles'

    cycle_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    company_id = db.Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # Cycle details
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    cycle_type = db.Column(db.String(100), default='ANNUAL')  # ANNUAL, QUARTERLY, MONTHLY, PROBATIONARY, PROJECT_BASED
    
    # Timeline
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    review_period_start = db.Column(db.Date)  # Period being reviewed (can be different from cycle dates)
    review_period_end = db.Column(db.Date)
    
    # Deadlines
    goal_setting_deadline = db.Column(db.Date)
    self_review_deadline = db.Column(db.Date)
    manager_review_deadline = db.Column(db.Date)
    peer_feedback_deadline = db.Column(db.Date)
    final_review_deadline = db.Column(db.Date)
    
    # Configuration
    requires_goals = db.Column(db.Boolean, default=True)
    requires_self_review = db.Column(db.Boolean, default=True)
    requires_manager_review = db.Column(db.Boolean, default=True)
    requires_peer_feedback = db.Column(db.Boolean, default=False)
    requires_subordinate_feedback = db.Column(db.Boolean, default=False)
    
    # Participants
    include_all_employees = db.Column(db.Boolean, default=True)
    included_departments = db.Column(db.Text)  # JSON array of department names
    excluded_employees = db.Column(db.Text)  # JSON array of employee IDs
    minimum_tenure_days = db.Column(db.Integer, default=90)  # Minimum days employed to participate
    
    # Rating configuration
    rating_scale_id = db.Column(UUID(as_uuid=True))  # Reference to rating scale
    competency_framework_id = db.Column(UUID(as_uuid=True))  # Reference to competency framework
    
    # Workflow settings
    auto_assign_reviewers = db.Column(db.Boolean, default=True)
    send_reminders = db.Column(db.Boolean, default=True)
    reminder_frequency_days = db.Column(db.Integer, default=7)
    allow_late_submissions = db.Column(db.Boolean, default=True)
    
    # Calibration settings
    requires_calibration = db.Column(db.Boolean, default=False)
    calibration_deadline = db.Column(db.Date)
    forced_ranking = db.Column(db.Boolean, default=False)
    distribution_guidelines = db.Column(db.Text)  # JSON with rating distribution guidelines
    
    # Status and metadata
    status = db.Column(db.String(50), default='DRAFT')  # DRAFT, ACTIVE, IN_PROGRESS, COMPLETED, CANCELLED
    completion_percentage = db.Column(db.Numeric(5, 2), default=0)
    
    created_by = db.Column(UUID(as_uuid=True))
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    activated_at = db.Column(db.DateTime)
    completed_at = db.Column(db.DateTime)

    # Relationships
    reviews = db.relationship('PerformanceReview', backref='cycle', lazy='dynamic')
    goals = db.relationship('PerformanceGoal', backref='cycle', lazy='dynamic')

    def to_dict(self):
        """Dictionary representation of the review cycle."""
        return {
            "cycle_id": str(self.cycle_id),
            "company_id": str(self.company_id),
            "name": self.name,
            "description": self.description,
            "cycle_type": self.cycle_type,
            "start_date": self.start_date.strftime('%Y-%m-%d') if self.start_date else None,
            "end_date": self.end_date.strftime('%Y-%m-%d') if self.end_date else None,
            "review_period_start": self.review_period_start.strftime('%Y-%m-%d') if self.review_period_start else None,
            "review_period_end": self.review_period_end.strftime('%Y-%m-%d') if self.review_period_end else None,
            "goal_setting_deadline": self.goal_setting_deadline.strftime('%Y-%m-%d') if self.goal_setting_deadline else None,
            "self_review_deadline": self.self_review_deadline.strftime('%Y-%m-%d') if self.self_review_deadline else None,
            "manager_review_deadline": self.manager_review_deadline.strftime('%Y-%m-%d') if self.manager_review_deadline else None,
            "peer_feedback_deadline": self.peer_feedback_deadline.strftime('%Y-%m-%d') if self.peer_feedback_deadline else None,
            "final_review_deadline": self.final_review_deadline.strftime('%Y-%m-%d') if self.final_review_deadline else None,
            "requires_goals": self.requires_goals,
            "requires_self_review": self.requires_self_review,
            "requires_manager_review": self.requires_manager_review,
            "requires_peer_feedback": self.requires_peer_feedback,
            "requires_subordinate_feedback": self.requires_subordinate_feedback,
            "include_all_employees": self.include_all_employees,
            "included_departments": json.loads(self.included_departments) if self.included_departments else [],
            "excluded_employees": json.loads(self.excluded_employees) if self.excluded_employees else [],
            "minimum_tenure_days": self.minimum_tenure_days,
            "rating_scale_id": str(self.rating_scale_id) if self.rating_scale_id else None,
            "competency_framework_id": str(self.competency_framework_id) if self.competency_framework_id else None,
            "auto_assign_reviewers": self.auto_assign_reviewers,
            "send_reminders": self.send_reminders,
            "reminder_frequency_days": self.reminder_frequency_days,
            "allow_late_submissions": self.allow_late_submissions,
            "requires_calibration": self.requires_calibration,
            "calibration_deadline": self.calibration_deadline.strftime('%Y-%m-%d') if self.calibration_deadline else None,
            "forced_ranking": self.forced_ranking,
            "distribution_guidelines": json.loads(self.distribution_guidelines) if self.distribution_guidelines else {},
            "status": self.status,
            "completion_percentage": float(self.completion_percentage) if self.completion_percentage else 0,
            "created_by": str(self.created_by) if self.created_by else None,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            "activated_at": self.activated_at.strftime('%Y-%m-%d %H:%M:%S') if self.activated_at else None,
            "completed_at": self.completed_at.strftime('%Y-%m-%d %H:%M:%S') if self.completed_at else None,
            "days_remaining": self.get_days_remaining(),
            "is_active": self.is_active(),
            "participant_count": self.get_participant_count()
        }

    @classmethod
    def create_cycle(cls, session, **kwargs):
        """Create a new performance review cycle."""
        try:
            # Handle JSON fields
            if 'included_departments' in kwargs and isinstance(kwargs['included_departments'], list):
                kwargs['included_departments'] = json.dumps(kwargs['included_departments'])
            
            if 'excluded_employees' in kwargs and isinstance(kwargs['excluded_employees'], list):
                kwargs['excluded_employees'] = json.dumps(kwargs['excluded_employees'])
            
            if 'distribution_guidelines' in kwargs and isinstance(kwargs['distribution_guidelines'], dict):
                kwargs['distribution_guidelines'] = json.dumps(kwargs['distribution_guidelines'])
            
            cycle = cls(**kwargs)
            session.add(cycle)
            session.flush()
            
            current_app.logger.info(f"Created performance review cycle: {cycle.name}")
            return cycle, None
            
        except Exception as e:
            current_app.logger.error(f"Error creating performance review cycle: {e}")
            return None, str(e)

    @classmethod
    def get_cycles_by_company(cls, session, company_id, status=None, include_inactive=False):
        """Get all cycles for a company."""
        query = session.query(cls).filter_by(company_id=company_id)
        
        if status:
            if isinstance(status, list):
                query = query.filter(cls.status.in_(status))
            else:
                query = query.filter_by(status=status)
        
        if not include_inactive:
            query = query.filter(cls.status != 'CANCELLED')
        
        return query.order_by(cls.start_date.desc()).all()

    @classmethod
    def get_active_cycles(cls, session, company_id):
        """Get currently active cycles for a company."""
        today = date.today()
        return session.query(cls).filter(
            cls.company_id == company_id,
            cls.status == 'ACTIVE',
            cls.start_date <= today,
            cls.end_date >= today
        ).all()

    @classmethod
    def get_cycle_by_id(cls, session, cycle_id):
        """Get cycle by ID."""
        return session.query(cls).filter_by(cycle_id=cycle_id).first()

    def activate_cycle(self, session, activated_by=None):
        """Activate the review cycle and create participant assignments."""
        try:
            if self.status != 'DRAFT':
                return False, f"Cannot activate cycle with status: {self.status}"
            
            self.status = 'ACTIVE'
            self.activated_at = datetime.now()
            self.updated_at = datetime.now()
            
            # Auto-assign participants if configured
            if self.include_all_employees:
                self.assign_participants(session)
            
            session.commit()
            
            current_app.logger.info(f"Activated performance review cycle: {self.cycle_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error activating cycle: {e}")
            return False, str(e)

    def assign_participants(self, session):
        """Assign eligible employees to the review cycle."""
        try:
            from application.Models.employees import Employee
            
            # Get eligible employees
            query = session.query(Employee).filter_by(company_id=self.company_id)
            
            # Apply tenure filter
            if self.minimum_tenure_days:
                cutoff_date = date.today() - timedelta(days=self.minimum_tenure_days)
                query = query.filter(Employee.hire_date <= cutoff_date)
            
            # Apply department filter
            if self.included_departments:
                departments = json.loads(self.included_departments)
                query = query.filter(Employee.department_name.in_(departments))
            
            # Exclude specific employees
            if self.excluded_employees:
                excluded = json.loads(self.excluded_employees)
                query = query.filter(~Employee.employee_id.in_(excluded))
            
            eligible_employees = query.all()
            
            # Create review assignments
            from application.Models.performance.performance_review import PerformanceReview
            
            for employee in eligible_employees:
                # Create self-review if required
                if self.requires_self_review:
                    self_review = PerformanceReview(
                        cycle_id=self.cycle_id,
                        employee_id=employee.employee_id,
                        reviewer_id=employee.employee_id,
                        review_type='SELF',
                        status='PENDING',
                        due_date=self.self_review_deadline
                    )
                    session.add(self_review)
                
                # Create manager review if required
                if self.requires_manager_review and employee.manager_id:
                    manager_review = PerformanceReview(
                        cycle_id=self.cycle_id,
                        employee_id=employee.employee_id,
                        reviewer_id=employee.manager_id,
                        review_type='MANAGER',
                        status='PENDING',
                        due_date=self.manager_review_deadline
                    )
                    session.add(manager_review)
            
            session.commit()
            current_app.logger.info(f"Assigned {len(eligible_employees)} participants to cycle: {self.cycle_id}")
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error assigning participants: {e}")
            raise

    def complete_cycle(self, session, completed_by=None):
        """Complete the review cycle."""
        try:
            self.status = 'COMPLETED'
            self.completed_at = datetime.now()
            self.completion_percentage = 100
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Completed performance review cycle: {self.cycle_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error completing cycle: {e}")
            return False, str(e)

    def get_days_remaining(self):
        """Get number of days remaining in the cycle."""
        if not self.end_date or self.status in ['COMPLETED', 'CANCELLED']:
            return 0
        
        days_remaining = (self.end_date - date.today()).days
        return max(0, days_remaining)

    def is_active(self):
        """Check if cycle is currently active."""
        if self.status != 'ACTIVE':
            return False
        
        today = date.today()
        return self.start_date <= today <= self.end_date

    def get_participant_count(self):
        """Get number of participants in this cycle."""
        return self.reviews.filter_by(review_type='SELF').count()

    def get_completion_stats(self, session):
        """Get completion statistics for this cycle."""
        try:
            total_reviews = self.reviews.count()
            completed_reviews = self.reviews.filter_by(status='COMPLETED').count()
            pending_reviews = self.reviews.filter_by(status='PENDING').count()
            overdue_reviews = self.reviews.filter(
                PerformanceReview.due_date < date.today(),
                PerformanceReview.status == 'PENDING'
            ).count()
            
            return {
                "total_reviews": total_reviews,
                "completed_reviews": completed_reviews,
                "pending_reviews": pending_reviews,
                "overdue_reviews": overdue_reviews,
                "completion_rate": round(completed_reviews / total_reviews * 100, 2) if total_reviews > 0 else 0
            }
            
        except Exception as e:
            current_app.logger.error(f"Error getting cycle completion stats: {e}")
            return {
                "total_reviews": 0,
                "completed_reviews": 0,
                "pending_reviews": 0,
                "overdue_reviews": 0,
                "completion_rate": 0
            }

    def update_completion_percentage(self, session):
        """Update cycle completion percentage based on review progress."""
        try:
            stats = self.get_completion_stats(session)
            self.completion_percentage = Decimal(str(stats['completion_rate']))
            self.updated_at = datetime.now()
            session.commit()
            
        except Exception as e:
            current_app.logger.error(f"Error updating completion percentage: {e}")

    def send_reminders(self, session):
        """Send reminder notifications for pending reviews."""
        try:
            if not self.send_reminders:
                return
            
            overdue_reviews = self.reviews.filter(
                PerformanceReview.due_date < date.today(),
                PerformanceReview.status == 'PENDING'
            ).all()
            
            # Send overdue reminders
            for review in overdue_reviews:
                # Implementation would send email/notification
                current_app.logger.info(f"Sending overdue reminder for review: {review.review_id}")
            
            # Send upcoming deadline reminders
            upcoming_deadline = date.today() + timedelta(days=self.reminder_frequency_days)
            upcoming_reviews = self.reviews.filter(
                PerformanceReview.due_date <= upcoming_deadline,
                PerformanceReview.due_date >= date.today(),
                PerformanceReview.status == 'PENDING'
            ).all()
            
            for review in upcoming_reviews:
                # Implementation would send email/notification
                current_app.logger.info(f"Sending deadline reminder for review: {review.review_id}")
                
        except Exception as e:
            current_app.logger.error(f"Error sending reminders: {e}")

    def get_distribution_guidelines_dict(self):
        """Get distribution guidelines as dictionary."""
        try:
            return json.loads(self.distribution_guidelines) if self.distribution_guidelines else {}
        except:
            return {}

    def set_distribution_guidelines(self, guidelines_dict):
        """Set distribution guidelines from dictionary."""
        try:
            self.distribution_guidelines = json.dumps(guidelines_dict) if guidelines_dict else None
        except:
            self.distribution_guidelines = None
