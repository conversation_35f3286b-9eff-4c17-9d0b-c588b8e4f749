from application.database import db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime, date, timedelta
from flask import current_app
from decimal import Decimal
import json


class Candidate(db.Model):
    """
    Candidate profiles and information for recruitment tracking.
    Supports talent pipeline management and candidate relationship management.
    """
    __tablename__ = 'candidates'

    candidate_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    company_id = db.Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # Personal information
    first_name = db.Column(db.String(255), nullable=False)
    last_name = db.Column(db.String(255), nullable=False)
    email = db.Column(db.String(255), nullable=False, index=True)
    phone = db.Column(db.String(50))
    
    # Contact details
    address_line1 = db.Column(db.String(255))
    address_line2 = db.Column(db.String(255))
    city = db.Column(db.String(100))
    state_province = db.Column(db.String(100))
    postal_code = db.Column(db.String(20))
    country = db.Column(db.String(100))
    
    # Professional information
    current_job_title = db.Column(db.String(255))
    current_company = db.Column(db.String(255))
    current_salary = db.Column(db.Numeric(12, 2))
    expected_salary = db.Column(db.Numeric(12, 2))
    currency = db.Column(db.String(10), default='USD')
    
    # Experience and education
    years_of_experience = db.Column(db.Integer)
    highest_education = db.Column(db.String(100))  # HIGH_SCHOOL, BACHELOR, MASTER, PHD, DIPLOMA, CERTIFICATE
    field_of_study = db.Column(db.String(255))
    university = db.Column(db.String(255))
    graduation_year = db.Column(db.Integer)
    
    # Skills and competencies
    skills = db.Column(db.Text)  # JSON array of skills
    certifications = db.Column(db.Text)  # JSON array of certifications
    languages = db.Column(db.Text)  # JSON array with proficiency levels
    
    # Availability and preferences
    availability_date = db.Column(db.Date)
    notice_period = db.Column(db.String(100))  # IMMEDIATE, 1_WEEK, 2_WEEKS, 1_MONTH, 2_MONTHS, 3_MONTHS
    work_authorization = db.Column(db.String(100))  # CITIZEN, PERMANENT_RESIDENT, WORK_VISA, REQUIRES_SPONSORSHIP
    willing_to_relocate = db.Column(db.Boolean, default=False)
    preferred_locations = db.Column(db.Text)  # JSON array of preferred locations
    remote_work_preference = db.Column(db.String(100))  # OFFICE_ONLY, REMOTE_ONLY, HYBRID, FLEXIBLE
    
    # Social and professional profiles
    linkedin_url = db.Column(db.String(500))
    portfolio_url = db.Column(db.String(500))
    github_url = db.Column(db.String(500))
    other_profiles = db.Column(db.Text)  # JSON with other social/professional profiles
    
    # Candidate status and pipeline
    status = db.Column(db.String(50), default='NEW')  # NEW, ACTIVE, PASSIVE, HIRED, REJECTED, WITHDRAWN, BLACKLISTED
    source = db.Column(db.String(100))  # LINKEDIN, REFERRAL, JOB_BOARD, COMPANY_WEBSITE, RECRUITER, SOCIAL_MEDIA
    source_details = db.Column(db.String(255))  # Specific source information
    
    # Talent pipeline
    talent_pool = db.Column(db.String(255))  # Talent pool categorization
    pipeline_stage = db.Column(db.String(100))  # SOURCED, CONTACTED, INTERESTED, QUALIFIED, PIPELINE
    last_contact_date = db.Column(db.Date)
    next_contact_date = db.Column(db.Date)
    
    # Engagement and communication
    communication_preference = db.Column(db.String(100), default='EMAIL')  # EMAIL, PHONE, SMS, LINKEDIN
    best_time_to_contact = db.Column(db.String(100))
    timezone = db.Column(db.String(100))
    
    # Privacy and consent
    gdpr_consent = db.Column(db.Boolean, default=False)
    marketing_consent = db.Column(db.Boolean, default=False)
    data_retention_date = db.Column(db.Date)
    
    # Notes and tags
    notes = db.Column(db.Text)
    tags = db.Column(db.Text)  # JSON array of tags for categorization
    
    # Referral information
    referred_by = db.Column(UUID(as_uuid=True))  # Employee who referred this candidate
    referral_bonus_eligible = db.Column(db.Boolean, default=False)
    referral_bonus_paid = db.Column(db.Boolean, default=False)
    
    # Metadata
    created_by = db.Column(UUID(as_uuid=True))
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    # Relationships
    applications = db.relationship('JobApplication', backref='candidate', lazy='dynamic')
    documents = db.relationship('CandidateDocument', backref='candidate', lazy='dynamic', cascade='all, delete-orphan')
    assessments = db.relationship('CandidateAssessment', backref='candidate', lazy='dynamic')
    interviews = db.relationship('Interview', backref='candidate', lazy='dynamic')

    def to_dict(self):
        """Dictionary representation of the candidate."""
        return {
            "candidate_id": str(self.candidate_id),
            "company_id": str(self.company_id),
            "first_name": self.first_name,
            "last_name": self.last_name,
            "full_name": f"{self.first_name} {self.last_name}",
            "email": self.email,
            "phone": self.phone,
            "address": {
                "line1": self.address_line1,
                "line2": self.address_line2,
                "city": self.city,
                "state_province": self.state_province,
                "postal_code": self.postal_code,
                "country": self.country
            },
            "professional_info": {
                "current_job_title": self.current_job_title,
                "current_company": self.current_company,
                "current_salary": float(self.current_salary) if self.current_salary else None,
                "expected_salary": float(self.expected_salary) if self.expected_salary else None,
                "currency": self.currency,
                "years_of_experience": self.years_of_experience
            },
            "education": {
                "highest_education": self.highest_education,
                "field_of_study": self.field_of_study,
                "university": self.university,
                "graduation_year": self.graduation_year
            },
            "skills": self.get_skills_list(),
            "certifications": self.get_certifications_list(),
            "languages": self.get_languages_list(),
            "availability": {
                "availability_date": self.availability_date.strftime('%Y-%m-%d') if self.availability_date else None,
                "notice_period": self.notice_period,
                "work_authorization": self.work_authorization,
                "willing_to_relocate": self.willing_to_relocate,
                "preferred_locations": self.get_preferred_locations_list(),
                "remote_work_preference": self.remote_work_preference
            },
            "profiles": {
                "linkedin_url": self.linkedin_url,
                "portfolio_url": self.portfolio_url,
                "github_url": self.github_url,
                "other_profiles": self.get_other_profiles_dict()
            },
            "status": self.status,
            "source": self.source,
            "source_details": self.source_details,
            "talent_pool": self.talent_pool,
            "pipeline_stage": self.pipeline_stage,
            "last_contact_date": self.last_contact_date.strftime('%Y-%m-%d') if self.last_contact_date else None,
            "next_contact_date": self.next_contact_date.strftime('%Y-%m-%d') if self.next_contact_date else None,
            "communication": {
                "preference": self.communication_preference,
                "best_time_to_contact": self.best_time_to_contact,
                "timezone": self.timezone
            },
            "privacy": {
                "gdpr_consent": self.gdpr_consent,
                "marketing_consent": self.marketing_consent,
                "data_retention_date": self.data_retention_date.strftime('%Y-%m-%d') if self.data_retention_date else None
            },
            "notes": self.notes,
            "tags": self.get_tags_list(),
            "referral": {
                "referred_by": str(self.referred_by) if self.referred_by else None,
                "bonus_eligible": self.referral_bonus_eligible,
                "bonus_paid": self.referral_bonus_paid
            },
            "created_by": str(self.created_by) if self.created_by else None,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            "application_count": self.applications.count(),
            "latest_application": self.get_latest_application_summary(),
            "document_count": self.documents.count(),
            "assessment_count": self.assessments.count(),
            "interview_count": self.interviews.count()
        }

    @classmethod
    def create_candidate(cls, session, **kwargs):
        """Create a new candidate."""
        try:
            # Handle JSON fields
            json_fields = ['skills', 'certifications', 'languages', 'preferred_locations', 'other_profiles', 'tags']
            for field in json_fields:
                if field in kwargs and isinstance(kwargs[field], (list, dict)):
                    kwargs[field] = json.dumps(kwargs[field])
            
            # Check for duplicate email
            existing_candidate = session.query(cls).filter_by(
                company_id=kwargs['company_id'],
                email=kwargs['email']
            ).first()
            
            if existing_candidate:
                return None, f"Candidate with email {kwargs['email']} already exists"
            
            candidate = cls(**kwargs)
            session.add(candidate)
            session.flush()
            
            current_app.logger.info(f"Created candidate: {candidate.email}")
            return candidate, None
            
        except Exception as e:
            current_app.logger.error(f"Error creating candidate: {e}")
            return None, str(e)

    @classmethod
    def get_candidates_by_company(cls, session, company_id, status=None, talent_pool=None, source=None):
        """Get all candidates for a company with optional filters."""
        query = session.query(cls).filter_by(company_id=company_id)
        
        if status:
            if isinstance(status, list):
                query = query.filter(cls.status.in_(status))
            else:
                query = query.filter_by(status=status)
        
        if talent_pool:
            query = query.filter_by(talent_pool=talent_pool)
        
        if source:
            query = query.filter_by(source=source)
        
        return query.order_by(cls.created_at.desc()).all()

    @classmethod
    def get_candidate_by_id(cls, session, candidate_id):
        """Get candidate by ID."""
        return session.query(cls).filter_by(candidate_id=candidate_id).first()

    @classmethod
    def get_candidate_by_email(cls, session, company_id, email):
        """Get candidate by email."""
        return session.query(cls).filter_by(company_id=company_id, email=email).first()

    @classmethod
    def search_candidates(cls, session, company_id, search_term=None, skills=None, location=None, experience_min=None, experience_max=None):
        """Search candidates with various filters."""
        query = session.query(cls).filter_by(company_id=company_id)
        
        if search_term:
            search_filter = db.or_(
                cls.first_name.ilike(f'%{search_term}%'),
                cls.last_name.ilike(f'%{search_term}%'),
                cls.email.ilike(f'%{search_term}%'),
                cls.current_job_title.ilike(f'%{search_term}%'),
                cls.current_company.ilike(f'%{search_term}%'),
                cls.skills.ilike(f'%{search_term}%')
            )
            query = query.filter(search_filter)
        
        if skills:
            for skill in skills:
                query = query.filter(cls.skills.ilike(f'%{skill}%'))
        
        if location:
            location_filter = db.or_(
                cls.city.ilike(f'%{location}%'),
                cls.state_province.ilike(f'%{location}%'),
                cls.country.ilike(f'%{location}%'),
                cls.preferred_locations.ilike(f'%{location}%')
            )
            query = query.filter(location_filter)
        
        if experience_min is not None:
            query = query.filter(cls.years_of_experience >= experience_min)
        
        if experience_max is not None:
            query = query.filter(cls.years_of_experience <= experience_max)
        
        return query.order_by(cls.updated_at.desc()).all()

    def update_contact_date(self, session, next_contact_date=None):
        """Update last contact date and set next contact date."""
        try:
            self.last_contact_date = date.today()
            
            if next_contact_date:
                self.next_contact_date = next_contact_date
            
            self.updated_at = datetime.now()
            session.commit()
            
        except Exception as e:
            current_app.logger.error(f"Error updating contact date: {e}")

    def move_to_pipeline_stage(self, session, stage):
        """Move candidate to a different pipeline stage."""
        try:
            self.pipeline_stage = stage
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Moved candidate {self.candidate_id} to stage: {stage}")
            
        except Exception as e:
            current_app.logger.error(f"Error moving candidate to pipeline stage: {e}")

    def add_tag(self, session, tag):
        """Add a tag to the candidate."""
        try:
            tags = self.get_tags_list()
            if tag not in tags:
                tags.append(tag)
                self.set_tags(tags)
                self.updated_at = datetime.now()
                session.commit()
            
        except Exception as e:
            current_app.logger.error(f"Error adding tag: {e}")

    def remove_tag(self, session, tag):
        """Remove a tag from the candidate."""
        try:
            tags = self.get_tags_list()
            if tag in tags:
                tags.remove(tag)
                self.set_tags(tags)
                self.updated_at = datetime.now()
                session.commit()
            
        except Exception as e:
            current_app.logger.error(f"Error removing tag: {e}")

    def get_latest_application_summary(self):
        """Get summary of latest application."""
        latest_app = self.applications.order_by(db.desc('created_at')).first()
        
        if latest_app:
            return {
                "application_id": str(latest_app.application_id),
                "job_title": latest_app.job_posting.job_title if latest_app.job_posting else None,
                "status": latest_app.status,
                "applied_date": latest_app.created_at.strftime('%Y-%m-%d') if latest_app.created_at else None
            }
        
        return None

    # JSON field helper methods
    def get_skills_list(self):
        try:
            return json.loads(self.skills) if self.skills else []
        except:
            return []

    def set_skills(self, skills_list):
        try:
            self.skills = json.dumps(skills_list) if skills_list else None
        except:
            self.skills = None

    def get_certifications_list(self):
        try:
            return json.loads(self.certifications) if self.certifications else []
        except:
            return []

    def set_certifications(self, certifications_list):
        try:
            self.certifications = json.dumps(certifications_list) if certifications_list else None
        except:
            self.certifications = None

    def get_languages_list(self):
        try:
            return json.loads(self.languages) if self.languages else []
        except:
            return []

    def set_languages(self, languages_list):
        try:
            self.languages = json.dumps(languages_list) if languages_list else None
        except:
            self.languages = None

    def get_preferred_locations_list(self):
        try:
            return json.loads(self.preferred_locations) if self.preferred_locations else []
        except:
            return []

    def set_preferred_locations(self, locations_list):
        try:
            self.preferred_locations = json.dumps(locations_list) if locations_list else None
        except:
            self.preferred_locations = None

    def get_other_profiles_dict(self):
        try:
            return json.loads(self.other_profiles) if self.other_profiles else {}
        except:
            return {}

    def set_other_profiles(self, profiles_dict):
        try:
            self.other_profiles = json.dumps(profiles_dict) if profiles_dict else None
        except:
            self.other_profiles = None

    def get_tags_list(self):
        try:
            return json.loads(self.tags) if self.tags else []
        except:
            return []

    def set_tags(self, tags_list):
        try:
            self.tags = json.dumps(tags_list) if tags_list else None
        except:
            self.tags = None

    @classmethod
    def get_candidates_needing_contact(cls, session, company_id, days_overdue=0):
        """Get candidates that need to be contacted."""
        overdue_date = date.today() - timedelta(days=days_overdue)
        
        return session.query(cls).filter(
            cls.company_id == company_id,
            cls.status.in_(['ACTIVE', 'PASSIVE']),
            cls.next_contact_date <= overdue_date
        ).order_by(cls.next_contact_date).all()

    @classmethod
    def get_talent_pipeline_summary(cls, session, company_id):
        """Get talent pipeline summary statistics."""
        try:
            pipeline_stats = session.query(
                cls.pipeline_stage,
                db.func.count(cls.candidate_id).label('count')
            ).filter_by(
                company_id=company_id,
                status='ACTIVE'
            ).group_by(cls.pipeline_stage).all()
            
            source_stats = session.query(
                cls.source,
                db.func.count(cls.candidate_id).label('count')
            ).filter_by(
                company_id=company_id
            ).group_by(cls.source).all()
            
            return {
                "pipeline_breakdown": {stage: count for stage, count in pipeline_stats},
                "source_breakdown": {source: count for source, count in source_stats},
                "total_candidates": session.query(cls).filter_by(company_id=company_id).count(),
                "active_candidates": session.query(cls).filter_by(company_id=company_id, status='ACTIVE').count()
            }
            
        except Exception as e:
            current_app.logger.error(f"Error getting talent pipeline summary: {e}")
            return {
                "pipeline_breakdown": {},
                "source_breakdown": {},
                "total_candidates": 0,
                "active_candidates": 0
            }
