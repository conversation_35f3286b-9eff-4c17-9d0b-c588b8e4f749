from application.database import db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime, date, timedelta
from flask import current_app
from decimal import Decimal
import json


class CandidateAssessment(db.Model):
    """
    Skills assessments and tests for candidates.
    Supports various assessment types including technical tests, personality assessments, etc.
    """
    __tablename__ = 'candidate_assessments'

    assessment_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    candidate_id = db.Column(UUID(as_uuid=True), db.ForeignKey('candidates.candidate_id'), nullable=False)
    application_id = db.Column(UUID(as_uuid=True), db.ForeignKey('job_applications.application_id'))
    
    # Assessment details
    assessment_name = db.Column(db.String(255), nullable=False)
    assessment_type = db.Column(db.String(100), nullable=False)  # TECHNICAL, COGNITIVE, PERSONALITY, SKILLS, CODING, BEHAVIORAL
    assessment_category = db.Column(db.String(100))  # PROGRAMMING, COMMUNICATION, LEADERSHIP, ANALYTICAL, etc.
    
    # Assessment provider and platform
    provider = db.Column(db.String(255))  # Internal, HackerRank, Codility, etc.
    platform_url = db.Column(db.String(500))
    external_assessment_id = db.Column(db.String(255))
    
    # Assessment configuration
    time_limit_minutes = db.Column(db.Integer)
    max_attempts = db.Column(db.Integer, default=1)
    passing_score = db.Column(db.Numeric(5, 2))
    total_possible_score = db.Column(db.Numeric(5, 2))
    
    # Assessment content
    instructions = db.Column(db.Text)
    questions = db.Column(db.Text)  # JSON array of questions
    skills_tested = db.Column(db.Text)  # JSON array of skills being tested
    
    # Scheduling and access
    assigned_date = db.Column(db.DateTime, default=datetime.now)
    due_date = db.Column(db.DateTime)
    access_link = db.Column(db.String(500))
    access_code = db.Column(db.String(100))
    
    # Completion tracking
    status = db.Column(db.String(50), default='ASSIGNED')  # ASSIGNED, IN_PROGRESS, COMPLETED, EXPIRED, CANCELLED
    started_at = db.Column(db.DateTime)
    completed_at = db.Column(db.DateTime)
    time_spent_minutes = db.Column(db.Integer)
    
    # Results and scoring
    score = db.Column(db.Numeric(5, 2))
    percentage_score = db.Column(db.Numeric(5, 2))
    passed = db.Column(db.Boolean)
    
    # Detailed results
    section_scores = db.Column(db.Text)  # JSON with scores by section
    question_responses = db.Column(db.Text)  # JSON with individual question responses
    skill_scores = db.Column(db.Text)  # JSON with scores by skill
    
    # Analysis and feedback
    strengths = db.Column(db.Text)
    weaknesses = db.Column(db.Text)
    recommendations = db.Column(db.Text)
    detailed_report = db.Column(db.Text)
    report_url = db.Column(db.String(500))
    
    # Proctoring and security
    proctored = db.Column(db.Boolean, default=False)
    proctoring_notes = db.Column(db.Text)
    security_flags = db.Column(db.Text)  # JSON array of security concerns
    
    # Attempts tracking
    attempt_number = db.Column(db.Integer, default=1)
    previous_attempts = db.Column(db.Text)  # JSON array of previous attempt results
    
    # Metadata
    created_by = db.Column(UUID(as_uuid=True), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    def to_dict(self):
        """Dictionary representation of the candidate assessment."""
        return {
            "assessment_id": str(self.assessment_id),
            "candidate_id": str(self.candidate_id),
            "application_id": str(self.application_id) if self.application_id else None,
            "assessment_details": {
                "name": self.assessment_name,
                "type": self.assessment_type,
                "category": self.assessment_category,
                "provider": self.provider,
                "platform_url": self.platform_url,
                "external_assessment_id": self.external_assessment_id
            },
            "configuration": {
                "time_limit_minutes": self.time_limit_minutes,
                "max_attempts": self.max_attempts,
                "passing_score": float(self.passing_score) if self.passing_score else None,
                "total_possible_score": float(self.total_possible_score) if self.total_possible_score else None
            },
            "content": {
                "instructions": self.instructions,
                "questions": self.get_questions_list(),
                "skills_tested": self.get_skills_tested_list()
            },
            "scheduling": {
                "assigned_date": self.assigned_date.strftime('%Y-%m-%d %H:%M:%S') if self.assigned_date else None,
                "due_date": self.due_date.strftime('%Y-%m-%d %H:%M:%S') if self.due_date else None,
                "access_link": self.access_link,
                "access_code": self.access_code
            },
            "completion": {
                "status": self.status,
                "started_at": self.started_at.strftime('%Y-%m-%d %H:%M:%S') if self.started_at else None,
                "completed_at": self.completed_at.strftime('%Y-%m-%d %H:%M:%S') if self.completed_at else None,
                "time_spent_minutes": self.time_spent_minutes
            },
            "results": {
                "score": float(self.score) if self.score else None,
                "percentage_score": float(self.percentage_score) if self.percentage_score else None,
                "passed": self.passed,
                "section_scores": self.get_section_scores_dict(),
                "skill_scores": self.get_skill_scores_dict()
            },
            "analysis": {
                "strengths": self.strengths,
                "weaknesses": self.weaknesses,
                "recommendations": self.recommendations,
                "detailed_report": self.detailed_report,
                "report_url": self.report_url
            },
            "proctoring": {
                "proctored": self.proctored,
                "proctoring_notes": self.proctoring_notes,
                "security_flags": self.get_security_flags_list()
            },
            "attempts": {
                "attempt_number": self.attempt_number,
                "previous_attempts": self.get_previous_attempts_list()
            },
            "created_by": str(self.created_by),
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            "is_overdue": self.is_overdue(),
            "days_remaining": self.get_days_remaining(),
            "completion_rate": self.calculate_completion_rate()
        }

    @classmethod
    def create_assessment(cls, session, **kwargs):
        """Create a new candidate assessment."""
        try:
            # Handle JSON fields
            json_fields = ['questions', 'skills_tested', 'section_scores', 'question_responses', 
                          'skill_scores', 'security_flags', 'previous_attempts']
            for field in json_fields:
                if field in kwargs and isinstance(kwargs[field], (list, dict)):
                    kwargs[field] = json.dumps(kwargs[field])
            
            assessment = cls(**kwargs)
            session.add(assessment)
            session.flush()
            
            current_app.logger.info(f"Created candidate assessment: {assessment.assessment_id}")
            return assessment, None
            
        except Exception as e:
            current_app.logger.error(f"Error creating candidate assessment: {e}")
            return None, str(e)

    @classmethod
    def get_assessments_by_candidate(cls, session, candidate_id, status=None):
        """Get all assessments for a candidate."""
        query = session.query(cls).filter_by(candidate_id=candidate_id)
        
        if status:
            if isinstance(status, list):
                query = query.filter(cls.status.in_(status))
            else:
                query = query.filter_by(status=status)
        
        return query.order_by(cls.assigned_date.desc()).all()

    @classmethod
    def get_assessments_by_application(cls, session, application_id):
        """Get all assessments for an application."""
        return session.query(cls).filter_by(application_id=application_id).order_by(cls.assigned_date).all()

    @classmethod
    def get_assessment_by_id(cls, session, assessment_id):
        """Get assessment by ID."""
        return session.query(cls).filter_by(assessment_id=assessment_id).first()

    def start_assessment(self, session):
        """Mark assessment as started."""
        try:
            if self.status != 'ASSIGNED':
                return False, f"Cannot start assessment with status: {self.status}"
            
            self.status = 'IN_PROGRESS'
            self.started_at = datetime.now()
            self.updated_at = datetime.now()
            
            session.commit()
            
            current_app.logger.info(f"Started assessment: {self.assessment_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error starting assessment: {e}")
            return False, str(e)

    def complete_assessment(self, session, results_data):
        """Complete the assessment with results."""
        try:
            if self.status != 'IN_PROGRESS':
                return False, f"Cannot complete assessment with status: {self.status}"
            
            self.status = 'COMPLETED'
            self.completed_at = datetime.now()
            
            # Calculate time spent
            if self.started_at:
                time_diff = self.completed_at - self.started_at
                self.time_spent_minutes = int(time_diff.total_seconds() / 60)
            
            # Update results
            if 'score' in results_data:
                self.score = results_data['score']
            
            if 'percentage_score' in results_data:
                self.percentage_score = results_data['percentage_score']
            elif self.score and self.total_possible_score:
                self.percentage_score = (self.score / self.total_possible_score) * 100
            
            # Determine if passed
            if self.passing_score and self.score:
                self.passed = self.score >= self.passing_score
            elif self.passing_score and self.percentage_score:
                self.passed = self.percentage_score >= self.passing_score
            
            # Update detailed results
            result_fields = ['section_scores', 'question_responses', 'skill_scores', 
                           'strengths', 'weaknesses', 'recommendations', 'detailed_report']
            
            for field in result_fields:
                if field in results_data:
                    if field in ['section_scores', 'question_responses', 'skill_scores']:
                        setattr(self, field, json.dumps(results_data[field]))
                    else:
                        setattr(self, field, results_data[field])
            
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Completed assessment: {self.assessment_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error completing assessment: {e}")
            return False, str(e)

    def cancel_assessment(self, session, reason=None):
        """Cancel the assessment."""
        try:
            self.status = 'CANCELLED'
            if reason:
                self.detailed_report = f"Assessment cancelled: {reason}"
            
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Cancelled assessment: {self.assessment_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error cancelling assessment: {e}")
            return False, str(e)

    def extend_deadline(self, session, new_due_date, reason=None):
        """Extend the assessment deadline."""
        try:
            old_due_date = self.due_date
            self.due_date = new_due_date
            
            if reason:
                current_notes = self.proctoring_notes or ""
                self.proctoring_notes = f"{current_notes}\nDeadline extended from {old_due_date} to {new_due_date}: {reason}"
            
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Extended deadline for assessment: {self.assessment_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error extending deadline: {e}")
            return False, str(e)

    def add_security_flag(self, session, flag_type, description):
        """Add a security flag to the assessment."""
        try:
            flags = self.get_security_flags_list()
            new_flag = {
                "type": flag_type,
                "description": description,
                "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            flags.append(new_flag)
            
            self.set_security_flags(flags)
            self.updated_at = datetime.now()
            session.commit()
            
        except Exception as e:
            current_app.logger.error(f"Error adding security flag: {e}")

    def is_overdue(self):
        """Check if assessment is overdue."""
        if not self.due_date or self.status in ['COMPLETED', 'CANCELLED']:
            return False
        
        return datetime.now() > self.due_date

    def get_days_remaining(self):
        """Get days remaining until due date."""
        if not self.due_date or self.status in ['COMPLETED', 'CANCELLED']:
            return None
        
        days = (self.due_date.date() - date.today()).days
        return max(0, days)

    def calculate_completion_rate(self):
        """Calculate completion rate based on questions answered."""
        if self.status != 'IN_PROGRESS':
            return None
        
        responses = self.get_question_responses_dict()
        questions = self.get_questions_list()
        
        if not questions:
            return None
        
        answered = len([q for q in questions if str(q.get('id', '')) in responses])
        return round((answered / len(questions)) * 100, 2)

    # JSON field helper methods
    def get_questions_list(self):
        try:
            return json.loads(self.questions) if self.questions else []
        except:
            return []

    def set_questions(self, questions_list):
        try:
            self.questions = json.dumps(questions_list) if questions_list else None
        except:
            self.questions = None

    def get_skills_tested_list(self):
        try:
            return json.loads(self.skills_tested) if self.skills_tested else []
        except:
            return []

    def set_skills_tested(self, skills_list):
        try:
            self.skills_tested = json.dumps(skills_list) if skills_list else None
        except:
            self.skills_tested = None

    def get_section_scores_dict(self):
        try:
            return json.loads(self.section_scores) if self.section_scores else {}
        except:
            return {}

    def set_section_scores(self, scores_dict):
        try:
            self.section_scores = json.dumps(scores_dict) if scores_dict else None
        except:
            self.section_scores = None

    def get_question_responses_dict(self):
        try:
            return json.loads(self.question_responses) if self.question_responses else {}
        except:
            return {}

    def set_question_responses(self, responses_dict):
        try:
            self.question_responses = json.dumps(responses_dict) if responses_dict else None
        except:
            self.question_responses = None

    def get_skill_scores_dict(self):
        try:
            return json.loads(self.skill_scores) if self.skill_scores else {}
        except:
            return {}

    def set_skill_scores(self, scores_dict):
        try:
            self.skill_scores = json.dumps(scores_dict) if scores_dict else None
        except:
            self.skill_scores = None

    def get_security_flags_list(self):
        try:
            return json.loads(self.security_flags) if self.security_flags else []
        except:
            return []

    def set_security_flags(self, flags_list):
        try:
            self.security_flags = json.dumps(flags_list) if flags_list else None
        except:
            self.security_flags = None

    def get_previous_attempts_list(self):
        try:
            return json.loads(self.previous_attempts) if self.previous_attempts else []
        except:
            return []

    def set_previous_attempts(self, attempts_list):
        try:
            self.previous_attempts = json.dumps(attempts_list) if attempts_list else None
        except:
            self.previous_attempts = None

    @classmethod
    def get_overdue_assessments(cls, session, company_id=None, days_overdue=0):
        """Get overdue assessments."""
        overdue_date = datetime.now() - timedelta(days=days_overdue)
        
        query = session.query(cls).filter(
            cls.due_date <= overdue_date,
            cls.status.in_(['ASSIGNED', 'IN_PROGRESS'])
        )
        
        if company_id:
            query = query.join(cls.application).join('job_posting').filter_by(company_id=company_id)
        
        return query.order_by(cls.due_date).all()

    @classmethod
    def get_assessment_statistics(cls, session, company_id=None, assessment_type=None, date_from=None, date_to=None):
        """Get assessment statistics."""
        try:
            query = session.query(cls)
            
            if company_id:
                query = query.join(cls.application).join('job_posting').filter_by(company_id=company_id)
            
            if assessment_type:
                query = query.filter_by(assessment_type=assessment_type)
            
            if date_from:
                query = query.filter(cls.assigned_date >= date_from)
            
            if date_to:
                query = query.filter(cls.assigned_date <= date_to)
            
            assessments = query.all()
            
            total_assessments = len(assessments)
            
            # Status breakdown
            status_breakdown = {}
            for assessment in assessments:
                status = assessment.status
                status_breakdown[status] = status_breakdown.get(status, 0) + 1
            
            # Type breakdown
            type_breakdown = {}
            for assessment in assessments:
                assessment_type = assessment.assessment_type
                type_breakdown[assessment_type] = type_breakdown.get(assessment_type, 0) + 1
            
            # Completion rate
            completed = status_breakdown.get('COMPLETED', 0)
            completion_rate = round(completed / total_assessments * 100, 2) if total_assessments > 0 else 0
            
            # Pass rate
            passed_assessments = [a for a in assessments if a.passed is True]
            pass_rate = round(len(passed_assessments) / completed * 100, 2) if completed > 0 else 0
            
            # Average score
            scored_assessments = [a for a in assessments if a.percentage_score is not None]
            avg_score = 0
            if scored_assessments:
                total_score = sum(float(a.percentage_score) for a in scored_assessments)
                avg_score = round(total_score / len(scored_assessments), 2)
            
            return {
                "total_assessments": total_assessments,
                "status_breakdown": status_breakdown,
                "type_breakdown": type_breakdown,
                "completion_rate": completion_rate,
                "pass_rate": pass_rate,
                "average_score": avg_score,
                "completed_assessments": completed
            }
            
        except Exception as e:
            current_app.logger.error(f"Error getting assessment statistics: {e}")
            return {
                "total_assessments": 0,
                "status_breakdown": {},
                "type_breakdown": {},
                "completion_rate": 0,
                "pass_rate": 0,
                "average_score": 0,
                "completed_assessments": 0
            }
