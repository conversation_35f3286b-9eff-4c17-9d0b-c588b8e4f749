from application.database import db
from sqlalchemy.dialects.postgresql import <PERSON>UI<PERSON>
import uuid
from datetime import datetime, date, timed<PERSON>ta
from flask import current_app
from decimal import Decimal
import json
import os


class CandidateDocument(db.Model):
    """
    Documents associated with candidates (resumes, cover letters, portfolios, etc.).
    Supports document parsing, version control, and secure storage.
    """
    __tablename__ = 'candidate_documents'

    document_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    candidate_id = db.Column(UUID(as_uuid=True), db.ForeignKey('candidates.candidate_id'), nullable=False)
    application_id = db.Column(UUID(as_uuid=True), db.<PERSON>ey('job_applications.application_id'))
    
    # Document details
    document_name = db.Column(db.String(255), nullable=False)
    document_type = db.Column(db.String(100), nullable=False)  # RESUME, COVER_LETTER, PORTFOLIO, CERTIFICATE, TRANSCRIPT, ID_DOCUMENT
    document_category = db.Column(db.String(100))  # REQUIRED, OPTIONAL, SUPPORTING
    
    # File information
    original_filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(500), nullable=False)
    file_size = db.Column(db.BigInteger)  # Size in bytes
    file_type = db.Column(db.String(100))  # MIME type
    file_extension = db.Column(db.String(10))
    
    # Storage details
    storage_provider = db.Column(db.String(100), default='LOCAL')  # LOCAL, S3, AZURE_BLOB, GOOGLE_CLOUD
    storage_bucket = db.Column(db.String(255))
    storage_key = db.Column(db.String(500))
    storage_url = db.Column(db.String(500))
    
    # Document parsing and analysis
    parsed_content = db.Column(db.Text)  # Extracted text content
    parsed_data = db.Column(db.Text)  # JSON with structured data (skills, experience, etc.)
    parsing_status = db.Column(db.String(50), default='PENDING')  # PENDING, COMPLETED, FAILED
    parsing_error = db.Column(db.Text)
    
    # Document verification
    is_verified = db.Column(db.Boolean, default=False)
    verification_status = db.Column(db.String(50))  # PENDING, VERIFIED, REJECTED, EXPIRED
    verification_notes = db.Column(db.Text)
    verified_by = db.Column(UUID(as_uuid=True))
    verified_date = db.Column(db.DateTime)
    
    # Version control
    version = db.Column(db.Integer, default=1)
    is_latest_version = db.Column(db.Boolean, default=True)
    previous_version_id = db.Column(UUID(as_uuid=True))
    
    # Access control and security
    is_confidential = db.Column(db.Boolean, default=False)
    access_level = db.Column(db.String(50), default='STANDARD')  # PUBLIC, STANDARD, CONFIDENTIAL, RESTRICTED
    password_protected = db.Column(db.Boolean, default=False)
    
    # Document status
    status = db.Column(db.String(50), default='ACTIVE')  # ACTIVE, ARCHIVED, DELETED, EXPIRED
    expiry_date = db.Column(db.Date)
    
    # AI/ML analysis
    ai_analysis = db.Column(db.Text)  # JSON with AI-generated insights
    skill_extraction = db.Column(db.Text)  # JSON with extracted skills
    experience_analysis = db.Column(db.Text)  # JSON with experience breakdown
    quality_score = db.Column(db.Numeric(5, 2))  # Document quality score (1-5)
    
    # Compliance and legal
    gdpr_compliant = db.Column(db.Boolean, default=True)
    retention_period_days = db.Column(db.Integer)
    auto_delete_date = db.Column(db.Date)
    
    # Metadata
    uploaded_by = db.Column(UUID(as_uuid=True))
    uploaded_at = db.Column(db.DateTime, default=datetime.now)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    def to_dict(self):
        """Dictionary representation of the candidate document."""
        return {
            "document_id": str(self.document_id),
            "candidate_id": str(self.candidate_id),
            "application_id": str(self.application_id) if self.application_id else None,
            "document_details": {
                "name": self.document_name,
                "type": self.document_type,
                "category": self.document_category,
                "original_filename": self.original_filename
            },
            "file_info": {
                "file_path": self.file_path,
                "file_size": self.file_size,
                "file_size_mb": round(self.file_size / (1024 * 1024), 2) if self.file_size else None,
                "file_type": self.file_type,
                "file_extension": self.file_extension
            },
            "storage": {
                "provider": self.storage_provider,
                "bucket": self.storage_bucket,
                "key": self.storage_key,
                "url": self.storage_url
            },
            "parsing": {
                "status": self.parsing_status,
                "error": self.parsing_error,
                "parsed_data": self.get_parsed_data_dict(),
                "content_preview": self.get_content_preview()
            },
            "verification": {
                "is_verified": self.is_verified,
                "status": self.verification_status,
                "notes": self.verification_notes,
                "verified_by": str(self.verified_by) if self.verified_by else None,
                "verified_date": self.verified_date.strftime('%Y-%m-%d %H:%M:%S') if self.verified_date else None
            },
            "version_control": {
                "version": self.version,
                "is_latest_version": self.is_latest_version,
                "previous_version_id": str(self.previous_version_id) if self.previous_version_id else None
            },
            "security": {
                "is_confidential": self.is_confidential,
                "access_level": self.access_level,
                "password_protected": self.password_protected
            },
            "status": self.status,
            "expiry_date": self.expiry_date.strftime('%Y-%m-%d') if self.expiry_date else None,
            "ai_analysis": {
                "analysis": self.get_ai_analysis_dict(),
                "skills": self.get_skill_extraction_list(),
                "experience": self.get_experience_analysis_dict(),
                "quality_score": float(self.quality_score) if self.quality_score else None
            },
            "compliance": {
                "gdpr_compliant": self.gdpr_compliant,
                "retention_period_days": self.retention_period_days,
                "auto_delete_date": self.auto_delete_date.strftime('%Y-%m-%d') if self.auto_delete_date else None
            },
            "uploaded_by": str(self.uploaded_by) if self.uploaded_by else None,
            "uploaded_at": self.uploaded_at.strftime('%Y-%m-%d %H:%M:%S') if self.uploaded_at else None,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            "is_expired": self.is_expired(),
            "days_until_expiry": self.get_days_until_expiry(),
            "download_url": self.get_download_url()
        }

    @classmethod
    def create_document(cls, session, **kwargs):
        """Create a new candidate document."""
        try:
            # Set file extension from filename
            if 'original_filename' in kwargs and not kwargs.get('file_extension'):
                kwargs['file_extension'] = os.path.splitext(kwargs['original_filename'])[1].lower()
            
            # Set auto-delete date based on retention period
            if kwargs.get('retention_period_days'):
                kwargs['auto_delete_date'] = date.today() + timedelta(days=kwargs['retention_period_days'])
            
            # Handle JSON fields
            json_fields = ['parsed_data', 'ai_analysis', 'skill_extraction', 'experience_analysis']
            for field in json_fields:
                if field in kwargs and isinstance(kwargs[field], (list, dict)):
                    kwargs[field] = json.dumps(kwargs[field])
            
            # Mark previous versions as not latest if this is a new version
            if kwargs.get('previous_version_id'):
                previous_doc = session.query(cls).filter_by(document_id=kwargs['previous_version_id']).first()
                if previous_doc:
                    previous_doc.is_latest_version = False
                    kwargs['version'] = previous_doc.version + 1
            
            document = cls(**kwargs)
            session.add(document)
            session.flush()
            
            current_app.logger.info(f"Created candidate document: {document.document_id}")
            return document, None
            
        except Exception as e:
            current_app.logger.error(f"Error creating candidate document: {e}")
            return None, str(e)

    @classmethod
    def get_documents_by_candidate(cls, session, candidate_id, document_type=None, latest_only=True):
        """Get all documents for a candidate."""
        query = session.query(cls).filter_by(candidate_id=candidate_id, status='ACTIVE')
        
        if document_type:
            query = query.filter_by(document_type=document_type)
        
        if latest_only:
            query = query.filter_by(is_latest_version=True)
        
        return query.order_by(cls.uploaded_at.desc()).all()

    @classmethod
    def get_documents_by_application(cls, session, application_id):
        """Get all documents for an application."""
        return session.query(cls).filter_by(
            application_id=application_id, 
            status='ACTIVE'
        ).order_by(cls.uploaded_at.desc()).all()

    @classmethod
    def get_document_by_id(cls, session, document_id):
        """Get document by ID."""
        return session.query(cls).filter_by(document_id=document_id).first()

    def parse_document(self, session, parsed_content=None, parsed_data=None):
        """Update document with parsed content and data."""
        try:
            self.parsing_status = 'COMPLETED'
            
            if parsed_content:
                self.parsed_content = parsed_content
            
            if parsed_data:
                if isinstance(parsed_data, dict):
                    self.parsed_data = json.dumps(parsed_data)
                else:
                    self.parsed_data = parsed_data
            
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Parsed document: {self.document_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            self.parsing_status = 'FAILED'
            self.parsing_error = str(e)
            session.commit()
            current_app.logger.error(f"Error parsing document: {e}")
            return False, str(e)

    def verify_document(self, session, verified_by, verification_status, notes=None):
        """Verify the document."""
        try:
            self.verification_status = verification_status
            self.is_verified = (verification_status == 'VERIFIED')
            self.verified_by = verified_by
            self.verified_date = datetime.now()
            
            if notes:
                self.verification_notes = notes
            
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Verified document: {self.document_id} - {verification_status}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error verifying document: {e}")
            return False, str(e)

    def archive_document(self, session):
        """Archive the document."""
        try:
            self.status = 'ARCHIVED'
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Archived document: {self.document_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error archiving document: {e}")
            return False, str(e)

    def delete_document(self, session, soft_delete=True):
        """Delete the document (soft or hard delete)."""
        try:
            if soft_delete:
                self.status = 'DELETED'
                self.updated_at = datetime.now()
                session.commit()
            else:
                # TODO: Delete actual file from storage
                session.delete(self)
                session.commit()
            
            current_app.logger.info(f"Deleted document: {self.document_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error deleting document: {e}")
            return False, str(e)

    def update_ai_analysis(self, session, analysis_data):
        """Update AI analysis results."""
        try:
            if 'ai_analysis' in analysis_data:
                self.ai_analysis = json.dumps(analysis_data['ai_analysis'])
            
            if 'skill_extraction' in analysis_data:
                self.skill_extraction = json.dumps(analysis_data['skill_extraction'])
            
            if 'experience_analysis' in analysis_data:
                self.experience_analysis = json.dumps(analysis_data['experience_analysis'])
            
            if 'quality_score' in analysis_data:
                self.quality_score = analysis_data['quality_score']
            
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Updated AI analysis for document: {self.document_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error updating AI analysis: {e}")
            return False, str(e)

    def is_expired(self):
        """Check if document is expired."""
        if not self.expiry_date:
            return False
        
        return date.today() > self.expiry_date

    def get_days_until_expiry(self):
        """Get days until document expires."""
        if not self.expiry_date:
            return None
        
        days = (self.expiry_date - date.today()).days
        return max(0, days)

    def get_download_url(self):
        """Get secure download URL for the document."""
        # This would typically generate a signed URL for cloud storage
        # For now, return a placeholder
        return f"/api/recruitment/documents/{self.document_id}/download"

    def get_content_preview(self, max_length=200):
        """Get a preview of the document content."""
        if not self.parsed_content:
            return None
        
        if len(self.parsed_content) <= max_length:
            return self.parsed_content
        
        return self.parsed_content[:max_length] + "..."

    # JSON field helper methods
    def get_parsed_data_dict(self):
        try:
            return json.loads(self.parsed_data) if self.parsed_data else {}
        except:
            return {}

    def set_parsed_data(self, data_dict):
        try:
            self.parsed_data = json.dumps(data_dict) if data_dict else None
        except:
            self.parsed_data = None

    def get_ai_analysis_dict(self):
        try:
            return json.loads(self.ai_analysis) if self.ai_analysis else {}
        except:
            return {}

    def set_ai_analysis(self, analysis_dict):
        try:
            self.ai_analysis = json.dumps(analysis_dict) if analysis_dict else None
        except:
            self.ai_analysis = None

    def get_skill_extraction_list(self):
        try:
            return json.loads(self.skill_extraction) if self.skill_extraction else []
        except:
            return []

    def set_skill_extraction(self, skills_list):
        try:
            self.skill_extraction = json.dumps(skills_list) if skills_list else None
        except:
            self.skill_extraction = None

    def get_experience_analysis_dict(self):
        try:
            return json.loads(self.experience_analysis) if self.experience_analysis else {}
        except:
            return {}

    def set_experience_analysis(self, analysis_dict):
        try:
            self.experience_analysis = json.dumps(analysis_dict) if analysis_dict else None
        except:
            self.experience_analysis = None

    @classmethod
    def get_documents_needing_parsing(cls, session, limit=50):
        """Get documents that need parsing."""
        return session.query(cls).filter_by(
            parsing_status='PENDING',
            status='ACTIVE'
        ).limit(limit).all()

    @classmethod
    def get_documents_needing_verification(cls, session, company_id=None):
        """Get documents that need verification."""
        query = session.query(cls).filter(
            cls.verification_status.in_(['PENDING', None]),
            cls.status == 'ACTIVE'
        )
        
        if company_id:
            query = query.join(cls.candidate).filter_by(company_id=company_id)
        
        return query.order_by(cls.uploaded_at).all()

    @classmethod
    def get_expiring_documents(cls, session, days_ahead=30):
        """Get documents expiring soon."""
        expiry_date = date.today() + timedelta(days=days_ahead)
        
        return session.query(cls).filter(
            cls.expiry_date <= expiry_date,
            cls.status == 'ACTIVE'
        ).order_by(cls.expiry_date).all()

    @classmethod
    def cleanup_expired_documents(cls, session):
        """Clean up expired documents based on auto-delete date."""
        try:
            expired_docs = session.query(cls).filter(
                cls.auto_delete_date <= date.today(),
                cls.status == 'ACTIVE'
            ).all()
            
            deleted_count = 0
            for doc in expired_docs:
                doc.delete_document(session, soft_delete=True)
                deleted_count += 1
            
            current_app.logger.info(f"Cleaned up {deleted_count} expired documents")
            return deleted_count
            
        except Exception as e:
            current_app.logger.error(f"Error cleaning up expired documents: {e}")
            return 0

    @classmethod
    def get_document_statistics(cls, session, candidate_id=None, company_id=None, date_from=None, date_to=None):
        """Get document statistics."""
        try:
            query = session.query(cls).filter_by(status='ACTIVE')
            
            if candidate_id:
                query = query.filter_by(candidate_id=candidate_id)
            
            if company_id:
                query = query.join(cls.candidate).filter_by(company_id=company_id)
            
            if date_from:
                query = query.filter(cls.uploaded_at >= date_from)
            
            if date_to:
                query = query.filter(cls.uploaded_at <= date_to)
            
            documents = query.all()
            
            total_documents = len(documents)
            
            # Type breakdown
            type_breakdown = {}
            for doc in documents:
                doc_type = doc.document_type
                type_breakdown[doc_type] = type_breakdown.get(doc_type, 0) + 1
            
            # Parsing status breakdown
            parsing_breakdown = {}
            for doc in documents:
                status = doc.parsing_status
                parsing_breakdown[status] = parsing_breakdown.get(status, 0) + 1
            
            # Verification breakdown
            verification_breakdown = {}
            for doc in documents:
                status = doc.verification_status or 'PENDING'
                verification_breakdown[status] = verification_breakdown.get(status, 0) + 1
            
            # Total file size
            total_size = sum(doc.file_size or 0 for doc in documents)
            total_size_mb = round(total_size / (1024 * 1024), 2)
            
            return {
                "total_documents": total_documents,
                "type_breakdown": type_breakdown,
                "parsing_breakdown": parsing_breakdown,
                "verification_breakdown": verification_breakdown,
                "total_size_mb": total_size_mb,
                "verified_documents": verification_breakdown.get('VERIFIED', 0),
                "parsed_documents": parsing_breakdown.get('COMPLETED', 0)
            }
            
        except Exception as e:
            current_app.logger.error(f"Error getting document statistics: {e}")
            return {
                "total_documents": 0,
                "type_breakdown": {},
                "parsing_breakdown": {},
                "verification_breakdown": {},
                "total_size_mb": 0,
                "verified_documents": 0,
                "parsed_documents": 0
            }
