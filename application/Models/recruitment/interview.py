from application.database import db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime, date, timedelta
from flask import current_app
from decimal import Decimal
import json


class Interview(db.Model):
    """
    Interview scheduling and management for job applications.
    Supports multiple interview types and video conferencing integration.
    """
    __tablename__ = 'interviews'

    interview_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    application_id = db.Column(UUID(as_uuid=True), db.<PERSON><PERSON>('job_applications.application_id'), nullable=False)
    candidate_id = db.Column(UUID(as_uuid=True), db.<PERSON>Key('candidates.candidate_id'), nullable=False)
    
    # Interview details
    interview_type = db.Column(db.String(100), nullable=False)  # PHONE, VIDEO, IN_PERSON, PANEL, TECHNICAL, BEHAVIORAL
    interview_round = db.Column(db.Integer, default=1)
    interview_title = db.Column(db.String(255))
    interview_description = db.Column(db.Text)
    
    # Scheduling
    scheduled_date = db.Column(db.DateTime, nullable=False)
    duration_minutes = db.Column(db.Integer, default=60)
    timezone = db.Column(db.String(100))
    
    # Location and meeting details
    location = db.Column(db.String(255))  # Physical address or "Video Conference"
    meeting_platform = db.Column(db.String(100))  # ZOOM, GOOGLE_MEET, TEAMS, CALENDLY, IN_PERSON
    meeting_url = db.Column(db.String(500))
    meeting_id = db.Column(db.String(255))
    meeting_password = db.Column(db.String(100))
    dial_in_number = db.Column(db.String(50))
    
    # External calendar integration
    calendar_event_id = db.Column(db.String(255))  # Google Calendar, Outlook event ID
    calendly_event_id = db.Column(db.String(255))  # Calendly event ID
    
    # Interview panel
    primary_interviewer = db.Column(UUID(as_uuid=True), nullable=False)
    interview_panel = db.Column(db.Text)  # JSON array of interviewer IDs
    
    # Interview structure
    interview_template_id = db.Column(UUID(as_uuid=True))
    questions = db.Column(db.Text)  # JSON array of interview questions
    evaluation_criteria = db.Column(db.Text)  # JSON array of evaluation criteria
    
    # Status and outcomes
    status = db.Column(db.String(50), default='SCHEDULED')  # SCHEDULED, CONFIRMED, IN_PROGRESS, COMPLETED, CANCELLED, NO_SHOW
    attendance_status = db.Column(db.String(50))  # ATTENDED, NO_SHOW, CANCELLED_BY_CANDIDATE, CANCELLED_BY_COMPANY
    
    # Interview feedback and scoring
    overall_rating = db.Column(db.Numeric(5, 2))
    technical_rating = db.Column(db.Numeric(5, 2))
    communication_rating = db.Column(db.Numeric(5, 2))
    cultural_fit_rating = db.Column(db.Numeric(5, 2))
    problem_solving_rating = db.Column(db.Numeric(5, 2))
    
    # Detailed feedback
    strengths = db.Column(db.Text)
    weaknesses = db.Column(db.Text)
    detailed_feedback = db.Column(db.Text)
    recommendation = db.Column(db.String(100))  # STRONG_HIRE, HIRE, NO_HIRE, STRONG_NO_HIRE
    
    # Follow-up and next steps
    next_steps = db.Column(db.Text)
    follow_up_required = db.Column(db.Boolean, default=False)
    follow_up_date = db.Column(db.Date)
    
    # Recording and documentation
    recording_url = db.Column(db.String(500))
    recording_available = db.Column(db.Boolean, default=False)
    notes_document_url = db.Column(db.String(500))
    
    # Reminders and notifications
    reminder_sent_candidate = db.Column(db.Boolean, default=False)
    reminder_sent_interviewer = db.Column(db.Boolean, default=False)
    confirmation_received = db.Column(db.Boolean, default=False)
    
    # Metadata
    created_by = db.Column(UUID(as_uuid=True), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    # Relationships
    feedback_entries = db.relationship('InterviewFeedback', backref='interview', lazy='dynamic', cascade='all, delete-orphan')

    def to_dict(self):
        """Dictionary representation of the interview."""
        return {
            "interview_id": str(self.interview_id),
            "application_id": str(self.application_id),
            "candidate_id": str(self.candidate_id),
            "interview_type": self.interview_type,
            "interview_round": self.interview_round,
            "interview_title": self.interview_title,
            "interview_description": self.interview_description,
            "scheduling": {
                "scheduled_date": self.scheduled_date.strftime('%Y-%m-%d %H:%M:%S') if self.scheduled_date else None,
                "duration_minutes": self.duration_minutes,
                "timezone": self.timezone
            },
            "meeting_details": {
                "location": self.location,
                "meeting_platform": self.meeting_platform,
                "meeting_url": self.meeting_url,
                "meeting_id": self.meeting_id,
                "meeting_password": self.meeting_password,
                "dial_in_number": self.dial_in_number
            },
            "calendar_integration": {
                "calendar_event_id": self.calendar_event_id,
                "calendly_event_id": self.calendly_event_id
            },
            "panel": {
                "primary_interviewer": str(self.primary_interviewer),
                "interview_panel": self.get_interview_panel_list()
            },
            "structure": {
                "interview_template_id": str(self.interview_template_id) if self.interview_template_id else None,
                "questions": self.get_questions_list(),
                "evaluation_criteria": self.get_evaluation_criteria_list()
            },
            "status": self.status,
            "attendance_status": self.attendance_status,
            "ratings": {
                "overall_rating": float(self.overall_rating) if self.overall_rating else None,
                "technical_rating": float(self.technical_rating) if self.technical_rating else None,
                "communication_rating": float(self.communication_rating) if self.communication_rating else None,
                "cultural_fit_rating": float(self.cultural_fit_rating) if self.cultural_fit_rating else None,
                "problem_solving_rating": float(self.problem_solving_rating) if self.problem_solving_rating else None
            },
            "feedback": {
                "strengths": self.strengths,
                "weaknesses": self.weaknesses,
                "detailed_feedback": self.detailed_feedback,
                "recommendation": self.recommendation
            },
            "follow_up": {
                "next_steps": self.next_steps,
                "follow_up_required": self.follow_up_required,
                "follow_up_date": self.follow_up_date.strftime('%Y-%m-%d') if self.follow_up_date else None
            },
            "documentation": {
                "recording_url": self.recording_url,
                "recording_available": self.recording_available,
                "notes_document_url": self.notes_document_url
            },
            "notifications": {
                "reminder_sent_candidate": self.reminder_sent_candidate,
                "reminder_sent_interviewer": self.reminder_sent_interviewer,
                "confirmation_received": self.confirmation_received
            },
            "created_by": str(self.created_by),
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            "is_upcoming": self.is_upcoming(),
            "is_overdue": self.is_overdue(),
            "time_until_interview": self.get_time_until_interview(),
            "panel_feedback": [feedback.to_dict() for feedback in self.feedback_entries.all()]
        }

    @classmethod
    def create_interview(cls, session, **kwargs):
        """Create a new interview."""
        try:
            # Handle JSON fields
            json_fields = ['interview_panel', 'questions', 'evaluation_criteria']
            for field in json_fields:
                if field in kwargs and isinstance(kwargs[field], list):
                    kwargs[field] = json.dumps(kwargs[field])
            
            interview = cls(**kwargs)
            session.add(interview)
            session.flush()
            
            current_app.logger.info(f"Created interview: {interview.interview_id}")
            return interview, None
            
        except Exception as e:
            current_app.logger.error(f"Error creating interview: {e}")
            return None, str(e)

    @classmethod
    def get_interviews_by_application(cls, session, application_id):
        """Get all interviews for an application."""
        return session.query(cls).filter_by(application_id=application_id).order_by(cls.scheduled_date).all()

    @classmethod
    def get_interviews_by_candidate(cls, session, candidate_id):
        """Get all interviews for a candidate."""
        return session.query(cls).filter_by(candidate_id=candidate_id).order_by(cls.scheduled_date.desc()).all()

    @classmethod
    def get_interviews_by_interviewer(cls, session, interviewer_id, date_from=None, date_to=None):
        """Get interviews for a specific interviewer."""
        query = session.query(cls).filter(
            db.or_(
                cls.primary_interviewer == interviewer_id,
                cls.interview_panel.like(f'%"{interviewer_id}"%')
            )
        )
        
        if date_from:
            query = query.filter(cls.scheduled_date >= date_from)
        
        if date_to:
            query = query.filter(cls.scheduled_date <= date_to)
        
        return query.order_by(cls.scheduled_date).all()

    @classmethod
    def get_interview_by_id(cls, session, interview_id):
        """Get interview by ID."""
        return session.query(cls).filter_by(interview_id=interview_id).first()

    def reschedule(self, session, new_date, reason=None):
        """Reschedule the interview."""
        try:
            old_date = self.scheduled_date
            self.scheduled_date = new_date
            self.status = 'SCHEDULED'
            self.reminder_sent_candidate = False
            self.reminder_sent_interviewer = False
            self.confirmation_received = False
            self.updated_at = datetime.now()
            
            session.commit()
            
            current_app.logger.info(f"Rescheduled interview {self.interview_id} from {old_date} to {new_date}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error rescheduling interview: {e}")
            return False, str(e)

    def cancel(self, session, reason, cancelled_by):
        """Cancel the interview."""
        try:
            self.status = 'CANCELLED'
            
            if cancelled_by == 'CANDIDATE':
                self.attendance_status = 'CANCELLED_BY_CANDIDATE'
            else:
                self.attendance_status = 'CANCELLED_BY_COMPANY'
            
            self.detailed_feedback = f"Interview cancelled: {reason}"
            self.updated_at = datetime.now()
            
            session.commit()
            
            current_app.logger.info(f"Cancelled interview: {self.interview_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error cancelling interview: {e}")
            return False, str(e)

    def mark_completed(self, session):
        """Mark interview as completed."""
        try:
            self.status = 'COMPLETED'
            self.attendance_status = 'ATTENDED'
            self.updated_at = datetime.now()
            
            session.commit()
            
            current_app.logger.info(f"Marked interview as completed: {self.interview_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error marking interview as completed: {e}")
            return False, str(e)

    def mark_no_show(self, session):
        """Mark candidate as no-show."""
        try:
            self.status = 'COMPLETED'
            self.attendance_status = 'NO_SHOW'
            self.recommendation = 'STRONG_NO_HIRE'
            self.detailed_feedback = "Candidate did not attend scheduled interview"
            self.updated_at = datetime.now()
            
            session.commit()
            
            current_app.logger.info(f"Marked interview as no-show: {self.interview_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error marking interview as no-show: {e}")
            return False, str(e)

    def submit_feedback(self, session, feedback_data, submitted_by):
        """Submit interview feedback."""
        try:
            # Update interview ratings
            rating_fields = ['overall_rating', 'technical_rating', 'communication_rating', 
                           'cultural_fit_rating', 'problem_solving_rating']
            
            for field in rating_fields:
                if field in feedback_data:
                    setattr(self, field, feedback_data[field])
            
            # Update feedback text
            text_fields = ['strengths', 'weaknesses', 'detailed_feedback', 'recommendation', 'next_steps']
            
            for field in text_fields:
                if field in feedback_data:
                    setattr(self, field, feedback_data[field])
            
            # Set follow-up if required
            if feedback_data.get('follow_up_required'):
                self.follow_up_required = True
                self.follow_up_date = feedback_data.get('follow_up_date')
            
            self.updated_at = datetime.now()
            
            # Create individual feedback entry for panel member
            if submitted_by != self.primary_interviewer or len(self.get_interview_panel_list()) > 0:
                panel_feedback = InterviewFeedback(
                    interview_id=self.interview_id,
                    interviewer_id=submitted_by,
                    **{k: v for k, v in feedback_data.items() if k in rating_fields + text_fields}
                )
                session.add(panel_feedback)
            
            session.commit()
            
            current_app.logger.info(f"Submitted feedback for interview: {self.interview_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error submitting interview feedback: {e}")
            return False, str(e)

    def confirm_attendance(self, session):
        """Confirm candidate attendance."""
        try:
            self.confirmation_received = True
            self.updated_at = datetime.now()
            session.commit()
            
        except Exception as e:
            current_app.logger.error(f"Error confirming attendance: {e}")

    def is_upcoming(self):
        """Check if interview is upcoming."""
        return self.scheduled_date > datetime.now() and self.status == 'SCHEDULED'

    def is_overdue(self):
        """Check if interview is overdue for feedback."""
        if self.status != 'COMPLETED':
            return False
        
        # Consider overdue if completed more than 24 hours ago without feedback
        if not self.overall_rating and not self.detailed_feedback:
            return (datetime.now() - self.scheduled_date).total_seconds() > 86400  # 24 hours
        
        return False

    def get_time_until_interview(self):
        """Get time until interview in human-readable format."""
        if not self.is_upcoming():
            return None
        
        time_diff = self.scheduled_date - datetime.now()
        
        if time_diff.days > 0:
            return f"{time_diff.days} days"
        elif time_diff.seconds > 3600:
            hours = time_diff.seconds // 3600
            return f"{hours} hours"
        else:
            minutes = time_diff.seconds // 60
            return f"{minutes} minutes"

    # JSON field helper methods
    def get_interview_panel_list(self):
        try:
            return json.loads(self.interview_panel) if self.interview_panel else []
        except:
            return []

    def set_interview_panel(self, panel_list):
        try:
            self.interview_panel = json.dumps(panel_list) if panel_list else None
        except:
            self.interview_panel = None

    def get_questions_list(self):
        try:
            return json.loads(self.questions) if self.questions else []
        except:
            return []

    def set_questions(self, questions_list):
        try:
            self.questions = json.dumps(questions_list) if questions_list else None
        except:
            self.questions = None

    def get_evaluation_criteria_list(self):
        try:
            return json.loads(self.evaluation_criteria) if self.evaluation_criteria else []
        except:
            return []

    def set_evaluation_criteria(self, criteria_list):
        try:
            self.evaluation_criteria = json.dumps(criteria_list) if criteria_list else None
        except:
            self.evaluation_criteria = None

    @classmethod
    def get_upcoming_interviews(cls, session, interviewer_id=None, days_ahead=7):
        """Get upcoming interviews."""
        end_date = datetime.now() + timedelta(days=days_ahead)
        
        query = session.query(cls).filter(
            cls.scheduled_date.between(datetime.now(), end_date),
            cls.status == 'SCHEDULED'
        )
        
        if interviewer_id:
            query = query.filter(
                db.or_(
                    cls.primary_interviewer == interviewer_id,
                    cls.interview_panel.like(f'%"{interviewer_id}"%')
                )
            )
        
        return query.order_by(cls.scheduled_date).all()

    @classmethod
    def get_interview_statistics(cls, session, company_id=None, date_from=None, date_to=None):
        """Get interview statistics."""
        try:
            query = session.query(cls)
            
            if company_id:
                query = query.join(cls.application).join('job_posting').filter_by(company_id=company_id)
            
            if date_from:
                query = query.filter(cls.scheduled_date >= date_from)
            
            if date_to:
                query = query.filter(cls.scheduled_date <= date_to)
            
            interviews = query.all()
            
            total_interviews = len(interviews)
            
            # Status breakdown
            status_breakdown = {}
            for interview in interviews:
                status = interview.status
                status_breakdown[status] = status_breakdown.get(status, 0) + 1
            
            # Type breakdown
            type_breakdown = {}
            for interview in interviews:
                interview_type = interview.interview_type
                type_breakdown[interview_type] = type_breakdown.get(interview_type, 0) + 1
            
            # No-show rate
            no_shows = len([i for i in interviews if i.attendance_status == 'NO_SHOW'])
            no_show_rate = round(no_shows / total_interviews * 100, 2) if total_interviews > 0 else 0
            
            # Average ratings
            completed_interviews = [i for i in interviews if i.status == 'COMPLETED' and i.overall_rating]
            avg_rating = 0
            if completed_interviews:
                total_rating = sum(float(i.overall_rating) for i in completed_interviews)
                avg_rating = round(total_rating / len(completed_interviews), 2)
            
            return {
                "total_interviews": total_interviews,
                "status_breakdown": status_breakdown,
                "type_breakdown": type_breakdown,
                "no_show_rate": no_show_rate,
                "average_rating": avg_rating,
                "completed_interviews": len(completed_interviews)
            }
            
        except Exception as e:
            current_app.logger.error(f"Error getting interview statistics: {e}")
            return {
                "total_interviews": 0,
                "status_breakdown": {},
                "type_breakdown": {},
                "no_show_rate": 0,
                "average_rating": 0,
                "completed_interviews": 0
            }


class InterviewFeedback(db.Model):
    """
    Individual feedback from panel members for interviews.
    """
    __tablename__ = 'interview_feedback'

    feedback_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    interview_id = db.Column(UUID(as_uuid=True), db.ForeignKey('interviews.interview_id'), nullable=False)
    interviewer_id = db.Column(UUID(as_uuid=True), nullable=False)
    
    # Ratings
    overall_rating = db.Column(db.Numeric(5, 2))
    technical_rating = db.Column(db.Numeric(5, 2))
    communication_rating = db.Column(db.Numeric(5, 2))
    cultural_fit_rating = db.Column(db.Numeric(5, 2))
    problem_solving_rating = db.Column(db.Numeric(5, 2))
    
    # Feedback
    strengths = db.Column(db.Text)
    weaknesses = db.Column(db.Text)
    detailed_feedback = db.Column(db.Text)
    recommendation = db.Column(db.String(100))
    
    # Metadata
    submitted_at = db.Column(db.DateTime, default=datetime.now)

    def to_dict(self):
        """Dictionary representation of the interview feedback."""
        return {
            "feedback_id": str(self.feedback_id),
            "interview_id": str(self.interview_id),
            "interviewer_id": str(self.interviewer_id),
            "ratings": {
                "overall_rating": float(self.overall_rating) if self.overall_rating else None,
                "technical_rating": float(self.technical_rating) if self.technical_rating else None,
                "communication_rating": float(self.communication_rating) if self.communication_rating else None,
                "cultural_fit_rating": float(self.cultural_fit_rating) if self.cultural_fit_rating else None,
                "problem_solving_rating": float(self.problem_solving_rating) if self.problem_solving_rating else None
            },
            "feedback": {
                "strengths": self.strengths,
                "weaknesses": self.weaknesses,
                "detailed_feedback": self.detailed_feedback,
                "recommendation": self.recommendation
            },
            "submitted_at": self.submitted_at.strftime('%Y-%m-%d %H:%M:%S') if self.submitted_at else None
        }
