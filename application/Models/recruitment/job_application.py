from application.database import db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime, date, timedelta
from flask import current_app
from decimal import Decimal
import json


class JobApplication(db.Model):
    """
    Job applications submitted by candidates for specific job postings.
    Tracks application progress through the hiring pipeline.
    """
    __tablename__ = 'job_applications'

    application_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    posting_id = db.Column(UUID(as_uuid=True), db.ForeignKey('job_postings.posting_id'), nullable=False)
    candidate_id = db.Column(UUID(as_uuid=True), db.ForeignKey('candidates.candidate_id'), nullable=False)
    
    # Application details
    application_source = db.Column(db.String(100))  # COMPANY_WEBSITE, LINKEDIN, REFERRAL, JOB_BOARD
    referral_source = db.Column(db.String(255))  # Specific referral details
    cover_letter = db.Column(db.Text)
    
    # Application responses
    screening_responses = db.Column(db.Text)  # JSON with screening question responses
    custom_responses = db.Column(db.Text)  # JSON with custom application form responses
    
    # Status and progression
    status = db.Column(db.String(50), default='SUBMITTED')  # SUBMITTED, SCREENING, INTERVIEWING, OFFER, HIRED, REJECTED, WITHDRAWN
    current_stage = db.Column(db.String(100))  # Current stage in hiring process
    stage_order = db.Column(db.Integer, default=1)  # Order of current stage
    
    # Scoring and evaluation
    overall_score = db.Column(db.Numeric(5, 2))  # Overall candidate score
    screening_score = db.Column(db.Numeric(5, 2))  # Screening/qualification score
    interview_score = db.Column(db.Numeric(5, 2))  # Average interview score
    assessment_score = db.Column(db.Numeric(5, 2))  # Skills assessment score
    
    # Timeline tracking
    submitted_date = db.Column(db.DateTime, default=datetime.now)
    screening_completed_date = db.Column(db.DateTime)
    first_interview_date = db.Column(db.DateTime)
    final_interview_date = db.Column(db.DateTime)
    offer_date = db.Column(db.DateTime)
    decision_date = db.Column(db.DateTime)
    
    # Hiring decision
    hiring_decision = db.Column(db.String(50))  # HIRE, REJECT, HOLD, CONSIDER_FUTURE
    decision_reason = db.Column(db.Text)
    decision_made_by = db.Column(UUID(as_uuid=True))
    
    # Offer details
    offer_extended = db.Column(db.Boolean, default=False)
    offer_accepted = db.Column(db.Boolean, default=False)
    offer_declined_reason = db.Column(db.Text)
    
    # Communication tracking
    last_communication_date = db.Column(db.DateTime)
    next_action_date = db.Column(db.Date)
    next_action_type = db.Column(db.String(100))  # CALL, EMAIL, INTERVIEW, DECISION
    
    # Feedback and notes
    recruiter_notes = db.Column(db.Text)
    hiring_manager_notes = db.Column(db.Text)
    rejection_feedback = db.Column(db.Text)
    
    # Diversity and compliance
    diversity_data = db.Column(db.Text)  # JSON with optional diversity information
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    # Relationships
    stage_history = db.relationship('ApplicationStageHistory', backref='application', lazy='dynamic', cascade='all, delete-orphan')
    interviews = db.relationship('Interview', backref='application', lazy='dynamic')
    assessments = db.relationship('CandidateAssessment', backref='application', lazy='dynamic')

    def to_dict(self):
        """Dictionary representation of the job application."""
        return {
            "application_id": str(self.application_id),
            "posting_id": str(self.posting_id),
            "candidate_id": str(self.candidate_id),
            "application_source": self.application_source,
            "referral_source": self.referral_source,
            "cover_letter": self.cover_letter,
            "screening_responses": self.get_screening_responses_dict(),
            "custom_responses": self.get_custom_responses_dict(),
            "status": self.status,
            "current_stage": self.current_stage,
            "stage_order": self.stage_order,
            "scores": {
                "overall_score": float(self.overall_score) if self.overall_score else None,
                "screening_score": float(self.screening_score) if self.screening_score else None,
                "interview_score": float(self.interview_score) if self.interview_score else None,
                "assessment_score": float(self.assessment_score) if self.assessment_score else None
            },
            "timeline": {
                "submitted_date": self.submitted_date.strftime('%Y-%m-%d %H:%M:%S') if self.submitted_date else None,
                "screening_completed_date": self.screening_completed_date.strftime('%Y-%m-%d %H:%M:%S') if self.screening_completed_date else None,
                "first_interview_date": self.first_interview_date.strftime('%Y-%m-%d %H:%M:%S') if self.first_interview_date else None,
                "final_interview_date": self.final_interview_date.strftime('%Y-%m-%d %H:%M:%S') if self.final_interview_date else None,
                "offer_date": self.offer_date.strftime('%Y-%m-%d %H:%M:%S') if self.offer_date else None,
                "decision_date": self.decision_date.strftime('%Y-%m-%d %H:%M:%S') if self.decision_date else None
            },
            "hiring_decision": self.hiring_decision,
            "decision_reason": self.decision_reason,
            "decision_made_by": str(self.decision_made_by) if self.decision_made_by else None,
            "offer_details": {
                "offer_extended": self.offer_extended,
                "offer_accepted": self.offer_accepted,
                "offer_declined_reason": self.offer_declined_reason
            },
            "communication": {
                "last_communication_date": self.last_communication_date.strftime('%Y-%m-%d %H:%M:%S') if self.last_communication_date else None,
                "next_action_date": self.next_action_date.strftime('%Y-%m-%d') if self.next_action_date else None,
                "next_action_type": self.next_action_type
            },
            "notes": {
                "recruiter_notes": self.recruiter_notes,
                "hiring_manager_notes": self.hiring_manager_notes,
                "rejection_feedback": self.rejection_feedback
            },
            "diversity_data": self.get_diversity_data_dict(),
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            "days_in_process": self.get_days_in_process(),
            "stage_history": [stage.to_dict() for stage in self.stage_history.order_by('created_at').all()],
            "interview_count": self.interviews.count(),
            "assessment_count": self.assessments.count()
        }

    @classmethod
    def create_application(cls, session, **kwargs):
        """Create a new job application."""
        try:
            # Handle JSON fields
            json_fields = ['screening_responses', 'custom_responses', 'diversity_data']
            for field in json_fields:
                if field in kwargs and isinstance(kwargs[field], dict):
                    kwargs[field] = json.dumps(kwargs[field])
            
            # Check for duplicate application
            existing_app = session.query(cls).filter_by(
                posting_id=kwargs['posting_id'],
                candidate_id=kwargs['candidate_id']
            ).first()
            
            if existing_app:
                return None, "Candidate has already applied for this position"
            
            application = cls(**kwargs)
            session.add(application)
            session.flush()
            
            # Create initial stage history
            initial_stage = ApplicationStageHistory(
                application_id=application.application_id,
                stage_name='SUBMITTED',
                stage_order=1,
                entered_date=datetime.now(),
                status='CURRENT'
            )
            session.add(initial_stage)
            
            # Update job posting application count
            if application.job_posting:
                application.job_posting.increment_applications(session)
            
            session.commit()
            
            current_app.logger.info(f"Created job application: {application.application_id}")
            return application, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error creating job application: {e}")
            return None, str(e)

    @classmethod
    def get_applications_by_posting(cls, session, posting_id, status=None):
        """Get all applications for a job posting."""
        query = session.query(cls).filter_by(posting_id=posting_id)
        
        if status:
            if isinstance(status, list):
                query = query.filter(cls.status.in_(status))
            else:
                query = query.filter_by(status=status)
        
        return query.order_by(cls.submitted_date.desc()).all()

    @classmethod
    def get_applications_by_candidate(cls, session, candidate_id, status=None):
        """Get all applications for a candidate."""
        query = session.query(cls).filter_by(candidate_id=candidate_id)
        
        if status:
            if isinstance(status, list):
                query = query.filter(cls.status.in_(status))
            else:
                query = query.filter_by(status=status)
        
        return query.order_by(cls.submitted_date.desc()).all()

    @classmethod
    def get_application_by_id(cls, session, application_id):
        """Get application by ID."""
        return session.query(cls).filter_by(application_id=application_id).first()

    def move_to_stage(self, session, stage_name, stage_order=None, notes=None):
        """Move application to a new stage."""
        try:
            # Mark current stage as completed
            current_stage_history = self.stage_history.filter_by(status='CURRENT').first()
            if current_stage_history:
                current_stage_history.status = 'COMPLETED'
                current_stage_history.completed_date = datetime.now()
                current_stage_history.notes = notes
            
            # Create new stage history
            new_stage_order = stage_order or (self.stage_order + 1)
            new_stage = ApplicationStageHistory(
                application_id=self.application_id,
                stage_name=stage_name,
                stage_order=new_stage_order,
                entered_date=datetime.now(),
                status='CURRENT'
            )
            session.add(new_stage)
            
            # Update application
            self.current_stage = stage_name
            self.stage_order = new_stage_order
            self.updated_at = datetime.now()
            
            # Update status based on stage
            self.update_status_from_stage(stage_name)
            
            session.commit()
            
            current_app.logger.info(f"Moved application {self.application_id} to stage: {stage_name}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error moving application to stage: {e}")
            return False, str(e)

    def update_status_from_stage(self, stage_name):
        """Update application status based on stage."""
        stage_status_mapping = {
            'SUBMITTED': 'SUBMITTED',
            'SCREENING': 'SCREENING',
            'PHONE_SCREEN': 'SCREENING',
            'TECHNICAL_ASSESSMENT': 'SCREENING',
            'FIRST_INTERVIEW': 'INTERVIEWING',
            'SECOND_INTERVIEW': 'INTERVIEWING',
            'FINAL_INTERVIEW': 'INTERVIEWING',
            'REFERENCE_CHECK': 'INTERVIEWING',
            'BACKGROUND_CHECK': 'INTERVIEWING',
            'OFFER_PREPARATION': 'OFFER',
            'OFFER_EXTENDED': 'OFFER',
            'OFFER_NEGOTIATION': 'OFFER',
            'HIRED': 'HIRED',
            'REJECTED': 'REJECTED',
            'WITHDRAWN': 'WITHDRAWN'
        }
        
        self.status = stage_status_mapping.get(stage_name, self.status)

    def reject_application(self, session, reason, rejected_by, feedback=None):
        """Reject the application."""
        try:
            self.status = 'REJECTED'
            self.hiring_decision = 'REJECT'
            self.decision_reason = reason
            self.decision_made_by = rejected_by
            self.decision_date = datetime.now()
            self.rejection_feedback = feedback
            self.updated_at = datetime.now()
            
            # Move to rejected stage
            self.move_to_stage(session, 'REJECTED', notes=reason)
            
            current_app.logger.info(f"Rejected application: {self.application_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error rejecting application: {e}")
            return False, str(e)

    def extend_offer(self, session, offer_details, extended_by):
        """Extend job offer to candidate."""
        try:
            self.status = 'OFFER'
            self.offer_extended = True
            self.offer_date = datetime.now()
            self.updated_at = datetime.now()
            
            # Move to offer stage
            self.move_to_stage(session, 'OFFER_EXTENDED', notes="Job offer extended")
            
            current_app.logger.info(f"Extended offer for application: {self.application_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error extending offer: {e}")
            return False, str(e)

    def accept_offer(self, session):
        """Mark offer as accepted."""
        try:
            self.offer_accepted = True
            self.status = 'HIRED'
            self.hiring_decision = 'HIRE'
            self.decision_date = datetime.now()
            self.updated_at = datetime.now()
            
            # Move to hired stage
            self.move_to_stage(session, 'HIRED', notes="Offer accepted")
            
            current_app.logger.info(f"Offer accepted for application: {self.application_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error accepting offer: {e}")
            return False, str(e)

    def decline_offer(self, session, reason):
        """Mark offer as declined."""
        try:
            self.offer_accepted = False
            self.offer_declined_reason = reason
            self.status = 'REJECTED'
            self.hiring_decision = 'REJECT'
            self.decision_date = datetime.now()
            self.updated_at = datetime.now()
            
            # Move to rejected stage
            self.move_to_stage(session, 'REJECTED', notes=f"Offer declined: {reason}")
            
            current_app.logger.info(f"Offer declined for application: {self.application_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error declining offer: {e}")
            return False, str(e)

    def withdraw_application(self, session, reason=None):
        """Withdraw the application."""
        try:
            self.status = 'WITHDRAWN'
            self.decision_date = datetime.now()
            self.updated_at = datetime.now()
            
            # Move to withdrawn stage
            self.move_to_stage(session, 'WITHDRAWN', notes=reason or "Application withdrawn")
            
            current_app.logger.info(f"Withdrew application: {self.application_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error withdrawing application: {e}")
            return False, str(e)

    def update_scores(self, session, **scores):
        """Update application scores."""
        try:
            for score_type, value in scores.items():
                if hasattr(self, score_type):
                    setattr(self, score_type, value)
            
            # Calculate overall score if individual scores are available
            if self.screening_score or self.interview_score or self.assessment_score:
                scores_list = [
                    float(self.screening_score) if self.screening_score else 0,
                    float(self.interview_score) if self.interview_score else 0,
                    float(self.assessment_score) if self.assessment_score else 0
                ]
                valid_scores = [s for s in scores_list if s > 0]
                
                if valid_scores:
                    self.overall_score = Decimal(str(round(sum(valid_scores) / len(valid_scores), 2)))
            
            self.updated_at = datetime.now()
            session.commit()
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error updating scores: {e}")

    def get_days_in_process(self):
        """Get number of days application has been in process."""
        if self.status in ['HIRED', 'REJECTED', 'WITHDRAWN'] and self.decision_date:
            end_date = self.decision_date.date()
        else:
            end_date = date.today()
        
        return (end_date - self.submitted_date.date()).days

    # JSON field helper methods
    def get_screening_responses_dict(self):
        try:
            return json.loads(self.screening_responses) if self.screening_responses else {}
        except:
            return {}

    def set_screening_responses(self, responses_dict):
        try:
            self.screening_responses = json.dumps(responses_dict) if responses_dict else None
        except:
            self.screening_responses = None

    def get_custom_responses_dict(self):
        try:
            return json.loads(self.custom_responses) if self.custom_responses else {}
        except:
            return {}

    def set_custom_responses(self, responses_dict):
        try:
            self.custom_responses = json.dumps(responses_dict) if responses_dict else None
        except:
            self.custom_responses = None

    def get_diversity_data_dict(self):
        try:
            return json.loads(self.diversity_data) if self.diversity_data else {}
        except:
            return {}

    def set_diversity_data(self, data_dict):
        try:
            self.diversity_data = json.dumps(data_dict) if data_dict else None
        except:
            self.diversity_data = None

    @classmethod
    def get_application_statistics(cls, session, posting_id=None, company_id=None, date_from=None, date_to=None):
        """Get application statistics."""
        try:
            query = session.query(cls)
            
            if posting_id:
                query = query.filter_by(posting_id=posting_id)
            
            if company_id:
                query = query.join(cls.job_posting).filter_by(company_id=company_id)
            
            if date_from:
                query = query.filter(cls.submitted_date >= date_from)
            
            if date_to:
                query = query.filter(cls.submitted_date <= date_to)
            
            applications = query.all()
            
            total_applications = len(applications)
            
            # Status breakdown
            status_breakdown = {}
            for app in applications:
                status = app.status
                status_breakdown[status] = status_breakdown.get(status, 0) + 1
            
            # Source breakdown
            source_breakdown = {}
            for app in applications:
                source = app.application_source or 'UNKNOWN'
                source_breakdown[source] = source_breakdown.get(source, 0) + 1
            
            # Average time in process
            completed_apps = [app for app in applications if app.status in ['HIRED', 'REJECTED', 'WITHDRAWN']]
            avg_time_to_decision = 0
            if completed_apps:
                total_days = sum(app.get_days_in_process() for app in completed_apps)
                avg_time_to_decision = round(total_days / len(completed_apps), 1)
            
            # Conversion rates
            hired_count = status_breakdown.get('HIRED', 0)
            hire_rate = round(hired_count / total_applications * 100, 2) if total_applications > 0 else 0
            
            return {
                "total_applications": total_applications,
                "status_breakdown": status_breakdown,
                "source_breakdown": source_breakdown,
                "average_time_to_decision_days": avg_time_to_decision,
                "hire_rate_percentage": hire_rate,
                "hired_count": hired_count
            }
            
        except Exception as e:
            current_app.logger.error(f"Error getting application statistics: {e}")
            return {
                "total_applications": 0,
                "status_breakdown": {},
                "source_breakdown": {},
                "average_time_to_decision_days": 0,
                "hire_rate_percentage": 0,
                "hired_count": 0
            }


class ApplicationStageHistory(db.Model):
    """
    History of application progression through hiring stages.
    """
    __tablename__ = 'application_stage_history'

    stage_history_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    application_id = db.Column(UUID(as_uuid=True), db.ForeignKey('job_applications.application_id'), nullable=False)
    
    # Stage details
    stage_name = db.Column(db.String(100), nullable=False)
    stage_order = db.Column(db.Integer, nullable=False)
    
    # Timeline
    entered_date = db.Column(db.DateTime, nullable=False)
    completed_date = db.Column(db.DateTime)
    
    # Status and notes
    status = db.Column(db.String(50), default='CURRENT')  # CURRENT, COMPLETED, SKIPPED
    notes = db.Column(db.Text)
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.now)

    def to_dict(self):
        """Dictionary representation of the stage history."""
        return {
            "stage_history_id": str(self.stage_history_id),
            "application_id": str(self.application_id),
            "stage_name": self.stage_name,
            "stage_order": self.stage_order,
            "entered_date": self.entered_date.strftime('%Y-%m-%d %H:%M:%S') if self.entered_date else None,
            "completed_date": self.completed_date.strftime('%Y-%m-%d %H:%M:%S') if self.completed_date else None,
            "status": self.status,
            "notes": self.notes,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "days_in_stage": self.get_days_in_stage()
        }

    def get_days_in_stage(self):
        """Get number of days spent in this stage."""
        end_date = self.completed_date or datetime.now()
        return (end_date - self.entered_date).days
