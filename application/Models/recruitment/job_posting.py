from application.database import db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime, date, timedelta
from flask import current_app
from decimal import Decimal
import json


class JobPosting(db.Model):
    """
    Published job postings that candidates can apply to.
    Supports multi-channel distribution and tracking.
    """
    __tablename__ = 'job_postings'

    posting_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    company_id = db.Column(UUID(as_uuid=True), nullable=False, index=True)
    requisition_id = db.Column(UUID(as_uuid=True), db.ForeignKey('job_requisitions.requisition_id'))
    
    # Basic posting information
    job_title = db.Column(db.String(255), nullable=False)
    job_code = db.Column(db.String(100))
    department = db.Column(db.String(255))
    location = db.Column(db.String(255))
    
    # Job details
    employment_type = db.Column(db.String(100))  # FULL_TIME, PART_TIME, CONTRACT, INTERN
    work_arrangement = db.Column(db.String(100))  # OFFICE, REMOTE, HYBRID
    experience_level = db.Column(db.String(100))  # ENTRY, MID, SENIOR, EXECUTIVE
    
    # Job description
    job_summary = db.Column(db.Text, nullable=False)
    responsibilities = db.Column(db.Text)
    required_qualifications = db.Column(db.Text)
    preferred_qualifications = db.Column(db.Text)
    skills_required = db.Column(db.Text)  # JSON array of skills
    
    # Compensation and benefits
    salary_disclosed = db.Column(db.Boolean, default=False)
    salary_range_min = db.Column(db.Numeric(12, 2))
    salary_range_max = db.Column(db.Numeric(12, 2))
    currency = db.Column(db.String(10), default='USD')
    benefits_summary = db.Column(db.Text)
    
    # Application details
    application_method = db.Column(db.String(100), default='INTERNAL')  # INTERNAL, EXTERNAL_LINK, EMAIL
    external_application_url = db.Column(db.String(500))
    application_email = db.Column(db.String(255))
    application_instructions = db.Column(db.Text)
    
    # Posting timeline
    posting_date = db.Column(db.Date, default=date.today)
    application_deadline = db.Column(db.Date)
    target_start_date = db.Column(db.Date)
    
    # Status and visibility
    status = db.Column(db.String(50), default='DRAFT')  # DRAFT, ACTIVE, PAUSED, CLOSED, EXPIRED
    is_internal_only = db.Column(db.Boolean, default=False)
    is_featured = db.Column(db.Boolean, default=False)
    
    # SEO and branding
    seo_title = db.Column(db.String(255))
    seo_description = db.Column(db.Text)
    seo_keywords = db.Column(db.Text)  # JSON array of keywords
    company_description = db.Column(db.Text)
    
    # Application tracking
    total_applications = db.Column(db.Integer, default=0)
    total_views = db.Column(db.Integer, default=0)
    
    # Metadata
    created_by = db.Column(UUID(as_uuid=True), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    # Relationships
    applications = db.relationship('JobApplication', backref='job_posting', lazy='dynamic')
    posting_channels = db.relationship('PostingChannel', backref='job_posting', lazy='dynamic', cascade='all, delete-orphan')

    def to_dict(self):
        """Dictionary representation of the job posting."""
        return {
            "posting_id": str(self.posting_id),
            "company_id": str(self.company_id),
            "requisition_id": str(self.requisition_id) if self.requisition_id else None,
            "job_title": self.job_title,
            "job_code": self.job_code,
            "department": self.department,
            "location": self.location,
            "employment_type": self.employment_type,
            "work_arrangement": self.work_arrangement,
            "experience_level": self.experience_level,
            "job_summary": self.job_summary,
            "responsibilities": self.responsibilities,
            "required_qualifications": self.required_qualifications,
            "preferred_qualifications": self.preferred_qualifications,
            "skills_required": self.get_skills_required_list(),
            "salary_disclosed": self.salary_disclosed,
            "salary_range_min": float(self.salary_range_min) if self.salary_range_min else None,
            "salary_range_max": float(self.salary_range_max) if self.salary_range_max else None,
            "currency": self.currency,
            "benefits_summary": self.benefits_summary,
            "application_method": self.application_method,
            "external_application_url": self.external_application_url,
            "application_email": self.application_email,
            "application_instructions": self.application_instructions,
            "posting_date": self.posting_date.strftime('%Y-%m-%d') if self.posting_date else None,
            "application_deadline": self.application_deadline.strftime('%Y-%m-%d') if self.application_deadline else None,
            "target_start_date": self.target_start_date.strftime('%Y-%m-%d') if self.target_start_date else None,
            "status": self.status,
            "is_internal_only": self.is_internal_only,
            "is_featured": self.is_featured,
            "seo_title": self.seo_title,
            "seo_description": self.seo_description,
            "seo_keywords": self.get_seo_keywords_list(),
            "company_description": self.company_description,
            "total_applications": self.total_applications,
            "total_views": self.total_views,
            "created_by": str(self.created_by),
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            "posting_channels": [channel.to_dict() for channel in self.posting_channels.all()],
            "is_expired": self.is_expired(),
            "days_remaining": self.get_days_remaining(),
            "application_rate": self.calculate_application_rate()
        }

    @classmethod
    def create_posting(cls, session, **kwargs):
        """Create a new job posting."""
        try:
            # Handle JSON fields
            json_fields = ['skills_required', 'seo_keywords']
            for field in json_fields:
                if field in kwargs and isinstance(kwargs[field], list):
                    kwargs[field] = json.dumps(kwargs[field])
            
            # Generate job code if not provided
            if 'job_code' not in kwargs or not kwargs['job_code']:
                kwargs['job_code'] = cls.generate_job_code(session, kwargs['company_id'])
            
            posting = cls(**kwargs)
            session.add(posting)
            session.flush()
            
            current_app.logger.info(f"Created job posting: {posting.job_title}")
            return posting, None
            
        except Exception as e:
            current_app.logger.error(f"Error creating job posting: {e}")
            return None, str(e)

    @classmethod
    def generate_job_code(cls, session, company_id):
        """Generate unique job code."""
        year = datetime.now().year
        
        # Count existing postings for this year and company
        count = session.query(cls).filter(
            cls.company_id == company_id,
            db.extract('year', cls.created_at) == year
        ).count()
        
        return f"JOB-{year}-{count + 1:04d}"

    @classmethod
    def get_postings_by_company(cls, session, company_id, status=None, is_internal_only=None):
        """Get all job postings for a company with optional filters."""
        query = session.query(cls).filter_by(company_id=company_id)
        
        if status:
            if isinstance(status, list):
                query = query.filter(cls.status.in_(status))
            else:
                query = query.filter_by(status=status)
        
        if is_internal_only is not None:
            query = query.filter_by(is_internal_only=is_internal_only)
        
        return query.order_by(cls.created_at.desc()).all()

    @classmethod
    def get_active_postings(cls, session, company_id=None, location=None, department=None):
        """Get active job postings with optional filters."""
        query = session.query(cls).filter_by(status='ACTIVE')
        
        if company_id:
            query = query.filter_by(company_id=company_id)
        
        if location:
            query = query.filter(cls.location.ilike(f'%{location}%'))
        
        if department:
            query = query.filter_by(department=department)
        
        return query.order_by(cls.posting_date.desc()).all()

    @classmethod
    def get_posting_by_id(cls, session, posting_id):
        """Get job posting by ID."""
        return session.query(cls).filter_by(posting_id=posting_id).first()

    def publish(self, session, channels=None):
        """Publish the job posting to specified channels."""
        try:
            if self.status != 'DRAFT':
                return False, f"Cannot publish posting with status: {self.status}"
            
            self.status = 'ACTIVE'
            self.posting_date = date.today()
            self.updated_at = datetime.now()
            
            # Create posting channels if specified
            if channels:
                for channel_data in channels:
                    channel = PostingChannel(
                        posting_id=self.posting_id,
                        **channel_data
                    )
                    session.add(channel)
            
            session.commit()
            
            current_app.logger.info(f"Published job posting: {self.posting_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error publishing job posting: {e}")
            return False, str(e)

    def pause(self, session):
        """Pause the job posting."""
        try:
            if self.status != 'ACTIVE':
                return False, f"Cannot pause posting with status: {self.status}"
            
            self.status = 'PAUSED'
            self.updated_at = datetime.now()
            
            session.commit()
            
            current_app.logger.info(f"Paused job posting: {self.posting_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error pausing job posting: {e}")
            return False, str(e)

    def close(self, session, reason=None):
        """Close the job posting."""
        try:
            self.status = 'CLOSED'
            self.updated_at = datetime.now()
            
            session.commit()
            
            current_app.logger.info(f"Closed job posting: {self.posting_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error closing job posting: {e}")
            return False, str(e)

    def increment_views(self, session):
        """Increment view count."""
        try:
            self.total_views += 1
            session.commit()
        except Exception as e:
            current_app.logger.error(f"Error incrementing views: {e}")

    def increment_applications(self, session):
        """Increment application count."""
        try:
            self.total_applications += 1
            session.commit()
        except Exception as e:
            current_app.logger.error(f"Error incrementing applications: {e}")

    def is_expired(self):
        """Check if posting is expired."""
        if not self.application_deadline:
            return False
        
        return date.today() > self.application_deadline

    def get_days_remaining(self):
        """Get days remaining until application deadline."""
        if not self.application_deadline:
            return None
        
        days = (self.application_deadline - date.today()).days
        return max(0, days)

    def calculate_application_rate(self):
        """Calculate application rate (applications per view)."""
        if self.total_views == 0:
            return 0
        
        return round((self.total_applications / self.total_views) * 100, 2)

    # JSON field helper methods
    def get_skills_required_list(self):
        try:
            return json.loads(self.skills_required) if self.skills_required else []
        except:
            return []

    def set_skills_required(self, skills_list):
        try:
            self.skills_required = json.dumps(skills_list) if skills_list else None
        except:
            self.skills_required = None

    def get_seo_keywords_list(self):
        try:
            return json.loads(self.seo_keywords) if self.seo_keywords else []
        except:
            return []

    def set_seo_keywords(self, keywords_list):
        try:
            self.seo_keywords = json.dumps(keywords_list) if keywords_list else None
        except:
            self.seo_keywords = None

    @classmethod
    def search_postings(cls, session, search_term, location=None, employment_type=None, company_id=None):
        """Search job postings by keywords."""
        query = session.query(cls).filter(cls.status == 'ACTIVE')
        
        if search_term:
            search_filter = db.or_(
                cls.job_title.ilike(f'%{search_term}%'),
                cls.job_summary.ilike(f'%{search_term}%'),
                cls.skills_required.ilike(f'%{search_term}%')
            )
            query = query.filter(search_filter)
        
        if location:
            query = query.filter(cls.location.ilike(f'%{location}%'))
        
        if employment_type:
            query = query.filter_by(employment_type=employment_type)
        
        if company_id:
            query = query.filter_by(company_id=company_id)
        
        return query.order_by(cls.posting_date.desc()).all()


class PostingChannel(db.Model):
    """
    Distribution channels for job postings (LinkedIn, job boards, etc.).
    """
    __tablename__ = 'posting_channels'

    channel_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    posting_id = db.Column(UUID(as_uuid=True), db.ForeignKey('job_postings.posting_id'), nullable=False)
    
    # Channel details
    channel_name = db.Column(db.String(255), nullable=False)  # LINKEDIN, INDEED, COMPANY_WEBSITE, etc.
    channel_type = db.Column(db.String(100))  # JOB_BOARD, SOCIAL_MEDIA, COMPANY_SITE, REFERRAL
    
    # Posting details
    external_posting_id = db.Column(db.String(255))  # ID from external system
    posting_url = db.Column(db.String(500))
    
    # Status and metrics
    status = db.Column(db.String(50), default='PENDING')  # PENDING, POSTED, FAILED, REMOVED
    posted_date = db.Column(db.DateTime)
    removed_date = db.Column(db.DateTime)
    
    # Performance metrics
    views = db.Column(db.Integer, default=0)
    applications = db.Column(db.Integer, default=0)
    cost = db.Column(db.Numeric(10, 2), default=0)
    
    # Integration details
    api_response = db.Column(db.Text)  # JSON response from posting API
    error_message = db.Column(db.Text)
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    def to_dict(self):
        """Dictionary representation of the posting channel."""
        return {
            "channel_id": str(self.channel_id),
            "posting_id": str(self.posting_id),
            "channel_name": self.channel_name,
            "channel_type": self.channel_type,
            "external_posting_id": self.external_posting_id,
            "posting_url": self.posting_url,
            "status": self.status,
            "posted_date": self.posted_date.strftime('%Y-%m-%d %H:%M:%S') if self.posted_date else None,
            "removed_date": self.removed_date.strftime('%Y-%m-%d %H:%M:%S') if self.removed_date else None,
            "views": self.views,
            "applications": self.applications,
            "cost": float(self.cost) if self.cost else 0,
            "error_message": self.error_message,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            "conversion_rate": self.calculate_conversion_rate()
        }

    def mark_as_posted(self, session, external_id=None, posting_url=None):
        """Mark channel as successfully posted."""
        try:
            self.status = 'POSTED'
            self.posted_date = datetime.now()
            
            if external_id:
                self.external_posting_id = external_id
            
            if posting_url:
                self.posting_url = posting_url
            
            self.updated_at = datetime.now()
            session.commit()
            
        except Exception as e:
            current_app.logger.error(f"Error marking channel as posted: {e}")

    def mark_as_failed(self, session, error_message):
        """Mark channel posting as failed."""
        try:
            self.status = 'FAILED'
            self.error_message = error_message
            self.updated_at = datetime.now()
            session.commit()
            
        except Exception as e:
            current_app.logger.error(f"Error marking channel as failed: {e}")

    def calculate_conversion_rate(self):
        """Calculate conversion rate (applications per view)."""
        if self.views == 0:
            return 0
        
        return round((self.applications / self.views) * 100, 2)
