from application.database import db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime, date, timedelta
from flask import current_app
from decimal import Decimal
import json


class JobRequisition(db.Model):
    """
    Job requisitions for requesting new positions or replacements.
    Includes approval workflow and budget management.
    """
    __tablename__ = 'job_requisitions'

    requisition_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    company_id = db.Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # Basic requisition information
    requisition_number = db.Column(db.String(100), unique=True, nullable=False)
    job_title = db.Column(db.String(255), nullable=False)
    department = db.Column(db.String(255), nullable=False)
    reporting_manager = db.Column(UUID(as_uuid=True), nullable=False)
    
    # Position details
    position_type = db.Column(db.String(100), default='FULL_TIME')  # FULL_TIME, PART_TIME, CONTRACT, INTERN
    employment_type = db.Column(db.String(100), default='PERMANENT')  # PERMANENT, TEMPORARY, SEASONAL
    location = db.Column(db.String(255))
    remote_work_option = db.Column(db.String(100), default='OFFICE')  # OFFICE, REMOTE, HYBRID
    
    # Headcount and timing
    number_of_positions = db.Column(db.Integer, default=1)
    replacement_for = db.Column(UUID(as_uuid=True))  # If replacing someone
    urgency_level = db.Column(db.String(50), default='NORMAL')  # URGENT, HIGH, NORMAL, LOW
    target_start_date = db.Column(db.Date)
    
    # Budget and compensation
    budget_approved = db.Column(db.Boolean, default=False)
    salary_range_min = db.Column(db.Numeric(12, 2))
    salary_range_max = db.Column(db.Numeric(12, 2))
    currency = db.Column(db.String(10), default='USD')
    benefits_package = db.Column(db.Text)  # JSON with benefits details
    
    # Business justification
    business_justification = db.Column(db.Text, nullable=False)
    role_responsibilities = db.Column(db.Text)
    required_qualifications = db.Column(db.Text)
    preferred_qualifications = db.Column(db.Text)
    success_criteria = db.Column(db.Text)
    
    # Approval workflow
    status = db.Column(db.String(50), default='DRAFT')  # DRAFT, PENDING_APPROVAL, APPROVED, REJECTED, ON_HOLD, CANCELLED
    current_approver = db.Column(UUID(as_uuid=True))
    approval_level = db.Column(db.Integer, default=0)
    
    # Recruitment details
    recruitment_type = db.Column(db.String(100), default='EXTERNAL')  # EXTERNAL, INTERNAL, BOTH
    sourcing_strategy = db.Column(db.Text)  # JSON with sourcing channels
    assessment_requirements = db.Column(db.Text)  # JSON with assessment details
    interview_process = db.Column(db.Text)  # JSON with interview stages
    
    # Timeline and deadlines
    approval_deadline = db.Column(db.Date)
    posting_start_date = db.Column(db.Date)
    application_deadline = db.Column(db.Date)
    target_hire_date = db.Column(db.Date)
    
    # Metadata
    requested_by = db.Column(UUID(as_uuid=True), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    
    # Relationships
    approvals = db.relationship('RequisitionApproval', backref='requisition', lazy='dynamic', cascade='all, delete-orphan')
    job_postings = db.relationship('JobPosting', backref='requisition', lazy='dynamic')

    def to_dict(self):
        """Dictionary representation of the job requisition."""
        return {
            "requisition_id": str(self.requisition_id),
            "company_id": str(self.company_id),
            "requisition_number": self.requisition_number,
            "job_title": self.job_title,
            "department": self.department,
            "reporting_manager": str(self.reporting_manager),
            "position_type": self.position_type,
            "employment_type": self.employment_type,
            "location": self.location,
            "remote_work_option": self.remote_work_option,
            "number_of_positions": self.number_of_positions,
            "replacement_for": str(self.replacement_for) if self.replacement_for else None,
            "urgency_level": self.urgency_level,
            "target_start_date": self.target_start_date.strftime('%Y-%m-%d') if self.target_start_date else None,
            "budget_approved": self.budget_approved,
            "salary_range_min": float(self.salary_range_min) if self.salary_range_min else None,
            "salary_range_max": float(self.salary_range_max) if self.salary_range_max else None,
            "currency": self.currency,
            "benefits_package": self.get_benefits_package_dict(),
            "business_justification": self.business_justification,
            "role_responsibilities": self.role_responsibilities,
            "required_qualifications": self.required_qualifications,
            "preferred_qualifications": self.preferred_qualifications,
            "success_criteria": self.success_criteria,
            "status": self.status,
            "current_approver": str(self.current_approver) if self.current_approver else None,
            "approval_level": self.approval_level,
            "recruitment_type": self.recruitment_type,
            "sourcing_strategy": self.get_sourcing_strategy_dict(),
            "assessment_requirements": self.get_assessment_requirements_dict(),
            "interview_process": self.get_interview_process_dict(),
            "approval_deadline": self.approval_deadline.strftime('%Y-%m-%d') if self.approval_deadline else None,
            "posting_start_date": self.posting_start_date.strftime('%Y-%m-%d') if self.posting_start_date else None,
            "application_deadline": self.application_deadline.strftime('%Y-%m-%d') if self.application_deadline else None,
            "target_hire_date": self.target_hire_date.strftime('%Y-%m-%d') if self.target_hire_date else None,
            "requested_by": str(self.requested_by),
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            "approval_history": [approval.to_dict() for approval in self.approvals.all()],
            "is_overdue": self.is_overdue(),
            "days_to_deadline": self.get_days_to_deadline()
        }

    @classmethod
    def create_requisition(cls, session, **kwargs):
        """Create a new job requisition."""
        try:
            # Generate requisition number
            if 'requisition_number' not in kwargs:
                kwargs['requisition_number'] = cls.generate_requisition_number(session, kwargs['company_id'])
            
            # Handle JSON fields
            json_fields = ['benefits_package', 'sourcing_strategy', 'assessment_requirements', 'interview_process']
            for field in json_fields:
                if field in kwargs and isinstance(kwargs[field], (dict, list)):
                    kwargs[field] = json.dumps(kwargs[field])
            
            requisition = cls(**kwargs)
            session.add(requisition)
            session.flush()
            
            current_app.logger.info(f"Created job requisition: {requisition.requisition_number}")
            return requisition, None
            
        except Exception as e:
            current_app.logger.error(f"Error creating job requisition: {e}")
            return None, str(e)

    @classmethod
    def generate_requisition_number(cls, session, company_id):
        """Generate unique requisition number."""
        year = datetime.now().year
        
        # Count existing requisitions for this year and company
        count = session.query(cls).filter(
            cls.company_id == company_id,
            db.extract('year', cls.created_at) == year
        ).count()
        
        return f"REQ-{year}-{count + 1:04d}"

    @classmethod
    def get_requisitions_by_company(cls, session, company_id, status=None, department=None):
        """Get all requisitions for a company with optional filters."""
        query = session.query(cls).filter_by(company_id=company_id)
        
        if status:
            if isinstance(status, list):
                query = query.filter(cls.status.in_(status))
            else:
                query = query.filter_by(status=status)
        
        if department:
            query = query.filter_by(department=department)
        
        return query.order_by(cls.created_at.desc()).all()

    @classmethod
    def get_requisition_by_id(cls, session, requisition_id):
        """Get requisition by ID."""
        return session.query(cls).filter_by(requisition_id=requisition_id).first()

    def submit_for_approval(self, session):
        """Submit requisition for approval."""
        try:
            if self.status != 'DRAFT':
                return False, f"Cannot submit requisition with status: {self.status}"
            
            self.status = 'PENDING_APPROVAL'
            self.approval_level = 1
            
            # Create first approval record
            approval = RequisitionApproval(
                requisition_id=self.requisition_id,
                approval_level=1,
                approver_id=self.reporting_manager,  # First approval is usually reporting manager
                status='PENDING'
            )
            session.add(approval)
            
            self.current_approver = self.reporting_manager
            self.updated_at = datetime.now()
            
            session.commit()
            
            current_app.logger.info(f"Submitted requisition for approval: {self.requisition_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error submitting requisition for approval: {e}")
            return False, str(e)

    def approve(self, session, approver_id, comments=None):
        """Approve the requisition at current level."""
        try:
            # Find current approval record
            current_approval = self.approvals.filter_by(
                approval_level=self.approval_level,
                status='PENDING'
            ).first()
            
            if not current_approval:
                return False, "No pending approval found"
            
            if current_approval.approver_id != approver_id:
                return False, "You are not authorized to approve this requisition"
            
            # Update approval record
            current_approval.status = 'APPROVED'
            current_approval.approved_date = datetime.now()
            current_approval.comments = comments
            
            # Check if more approvals needed
            if self.approval_level < self.get_required_approval_levels():
                # Move to next approval level
                self.approval_level += 1
                next_approver = self.get_next_approver()
                
                if next_approver:
                    next_approval = RequisitionApproval(
                        requisition_id=self.requisition_id,
                        approval_level=self.approval_level,
                        approver_id=next_approver,
                        status='PENDING'
                    )
                    session.add(next_approval)
                    self.current_approver = next_approver
                else:
                    # No more approvers, fully approved
                    self.status = 'APPROVED'
                    self.current_approver = None
            else:
                # Final approval
                self.status = 'APPROVED'
                self.current_approver = None
            
            self.updated_at = datetime.now()
            session.commit()
            
            current_app.logger.info(f"Approved requisition: {self.requisition_id} at level {current_approval.approval_level}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error approving requisition: {e}")
            return False, str(e)

    def reject(self, session, approver_id, rejection_reason):
        """Reject the requisition."""
        try:
            current_approval = self.approvals.filter_by(
                approval_level=self.approval_level,
                status='PENDING'
            ).first()
            
            if not current_approval:
                return False, "No pending approval found"
            
            if current_approval.approver_id != approver_id:
                return False, "You are not authorized to reject this requisition"
            
            # Update approval record
            current_approval.status = 'REJECTED'
            current_approval.approved_date = datetime.now()
            current_approval.comments = rejection_reason
            
            # Update requisition status
            self.status = 'REJECTED'
            self.current_approver = None
            self.updated_at = datetime.now()
            
            session.commit()
            
            current_app.logger.info(f"Rejected requisition: {self.requisition_id}")
            return True, None
            
        except Exception as e:
            session.rollback()
            current_app.logger.error(f"Error rejecting requisition: {e}")
            return False, str(e)

    def get_required_approval_levels(self):
        """Get number of required approval levels based on salary range."""
        if not self.salary_range_max:
            return 1
        
        salary_max = float(self.salary_range_max)
        
        # Define approval levels based on salary
        if salary_max >= 100000:
            return 3  # Manager → HR → Executive
        elif salary_max >= 50000:
            return 2  # Manager → HR
        else:
            return 1  # Manager only

    def get_next_approver(self):
        """Get next approver based on approval level."""
        # This would typically integrate with your org chart
        # For now, return None to indicate no more approvers
        return None

    def is_overdue(self):
        """Check if requisition is overdue for approval."""
        if self.status != 'PENDING_APPROVAL' or not self.approval_deadline:
            return False
        
        return date.today() > self.approval_deadline

    def get_days_to_deadline(self):
        """Get days remaining to approval deadline."""
        if not self.approval_deadline:
            return None
        
        days = (self.approval_deadline - date.today()).days
        return max(0, days)

    # JSON field helper methods
    def get_benefits_package_dict(self):
        try:
            return json.loads(self.benefits_package) if self.benefits_package else {}
        except:
            return {}

    def get_sourcing_strategy_dict(self):
        try:
            return json.loads(self.sourcing_strategy) if self.sourcing_strategy else {}
        except:
            return {}

    def get_assessment_requirements_dict(self):
        try:
            return json.loads(self.assessment_requirements) if self.assessment_requirements else {}
        except:
            return {}

    def get_interview_process_dict(self):
        try:
            return json.loads(self.interview_process) if self.interview_process else {}
        except:
            return {}


class RequisitionApproval(db.Model):
    """
    Approval workflow tracking for job requisitions.
    """
    __tablename__ = 'requisition_approvals'

    approval_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    requisition_id = db.Column(UUID(as_uuid=True), db.ForeignKey('job_requisitions.requisition_id'), nullable=False)
    
    # Approval details
    approval_level = db.Column(db.Integer, nullable=False)
    approver_id = db.Column(UUID(as_uuid=True), nullable=False)
    status = db.Column(db.String(50), default='PENDING')  # PENDING, APPROVED, REJECTED
    
    # Approval outcome
    approved_date = db.Column(db.DateTime)
    comments = db.Column(db.Text)
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.now)

    def to_dict(self):
        """Dictionary representation of the approval."""
        return {
            "approval_id": str(self.approval_id),
            "requisition_id": str(self.requisition_id),
            "approval_level": self.approval_level,
            "approver_id": str(self.approver_id),
            "status": self.status,
            "approved_date": self.approved_date.strftime('%Y-%m-%d %H:%M:%S') if self.approved_date else None,
            "comments": self.comments,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None
        }
