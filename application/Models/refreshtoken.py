from application.database import central_db as db
import datetime
import jwt
import datetime
import uuid
from flask import current_app
from dotenv import load_dotenv
import os

# Load environment variables from .env file
load_dotenv()

SECRET_KEY = os.getenv('SECRET_KEY')

class RefreshToken(db.Model):
    __tablename__ = 'refresh_tokens'

    id = db.Column(db.Integer, primary_key=True)
    token = db.Column(db.String(255), nullable=False, unique=True)
    user_id = db.Column(db.String(36), db.ForeignKey('users.user_id'), nullable=False)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    expires_at = db.Column(db.DateTime, nullable=False)
    revoked = db.Column(db.<PERSON>, default=False)


    def to_dict(self):
        """Convert the RefreshToken object to a dictionary."""
        return {
            'id': self.id,
            'token': self.token,
            'user_id': self.user_id,
            'created_at': self.created_at,
            'expires_at': self.expires_at,
            'revoked': self.revoked,
            'user': self.user.to_dict() if self.user else None
        }

    @classmethod
    def generate_access_token(cls, user):
        """Generate a new access token for the user."""
        payload = {
            "user_id": user.user_id,
            "username": user.username,
            "role": user.role,
            "exp": datetime.datetime.utcnow() + datetime.timedelta(minutes=30)
        }
        return jwt.encode(payload, SECRET_KEY, algorithm="HS256")

    @classmethod
    def generate_refresh_token(cls, user):
        """Generate a new refresh token for the user."""
        token = str(uuid.uuid4())  # Random string
        expires_at = datetime.datetime.utcnow() + datetime.timedelta(days=7)

        new_token = cls(
            token=token,
            user_id=user.user_id,
            expires_at=expires_at
        )
        db.session.add(new_token)
        db.session.commit()
        return token

    @classmethod
    def revoke_token(cls, token):
        """Revoke a refresh token."""
        refresh_token = cls.query.filter_by(token=token).first()
        if refresh_token:
            refresh_token.revoked = True
            db.session.commit()
            return True
        return False

    @classmethod
    def decode_token(cls, token):
        """Decode a JWT token and return the payload.

        Args:
            token (str): The JWT token to decode.

        Returns:
            dict: The decoded payload if the token is valid, None otherwise.
        """
        # Decode the token using the secret key
        try:
            return jwt.decode(token, SECRET_KEY, algorithms=["HS256"])
        except jwt.ExpiredSignatureError:
            return None
        except jwt.InvalidTokenError:
            return None

    @classmethod
    def generate_custom_token(cls, payload):
        """Generate a custom token with the given payload.

        Args:
            payload (dict): The payload to encode in the token.

        Returns:
            str: The encoded JWT token.
        """
        # Add expiration time if not provided
        if 'exp' not in payload:
            payload['exp'] = datetime.datetime.utcnow() + datetime.timedelta(minutes=30)

        return jwt.encode(payload, SECRET_KEY, algorithm="HS256")

    @classmethod
    def generate_custom_refresh_token(cls, payload):
        """Generate a custom refresh token with the given payload.

        Args:
            payload (dict): The payload containing user information.

        Returns:
            str: The refresh token string.
        """
        token = str(uuid.uuid4())  # Random string
        expires_at = datetime.datetime.utcnow() + datetime.timedelta(days=7)

        # For company users, we don't store the refresh token in the central database
        # Instead, we encode the necessary information in the token itself
        token_payload = {
            'refresh_token': token,
            'user_id': payload.get('user_id'),
            'company_id': payload.get('company_id'),
            'company_db': payload.get('company_db'),
            'exp': expires_at
        }

        return jwt.encode(token_payload, SECRET_KEY, algorithm="HS256")


