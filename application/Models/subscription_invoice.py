from application.database import central_db as db
import uuid
from sqlalchemy.dialects.postgresql import UUID
from flask import current_app
from datetime import datetime, date, timedelta
from decimal import Decimal

class SubscriptionInvoice(db.Model):
    """Model representing subscription invoices (stored in central database)."""
    __tablename__ = 'subscription_invoices'

    invoice_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    subscription_id = db.Column(UUID(as_uuid=True), db.ForeignKey('company_subscriptions.subscription_id'), nullable=False)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    billing_period_start = db.Column(db.Date, nullable=False)
    billing_period_end = db.Column(db.Date, nullable=False)
    employee_count_billed = db.Column(db.Integer, nullable=False)
    subtotal = db.Column(db.Numeric(12, 2), nullable=False)
    tax_rate = db.Column(db.Numeric(5, 4), default=0, nullable=False)  # e.g., 0.18 for 18%
    tax_amount = db.Column(db.Numeric(12, 2), default=0, nullable=False)
    total_amount = db.Column(db.Numeric(12, 2), nullable=False)
    status = db.Column(db.String(20), default='DRAFT', nullable=False)  # DRAFT, PENDING, PAID, OVERDUE, CANCELLED
    due_date = db.Column(db.Date, nullable=False)
    paid_date = db.Column(db.Date, nullable=True)
    payment_method = db.Column(db.String(50), nullable=True)
    payment_reference = db.Column(db.String(255), nullable=True)
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    # Relationships
    payments = db.relationship('SubscriptionPayment', backref='invoice', lazy='dynamic')

    # Indexes
    __table_args__ = (
        db.Index('idx_invoice_subscription', 'subscription_id'),
        db.Index('idx_invoice_status', 'status'),
        db.Index('idx_invoice_due_date', 'due_date'),
        db.Index('idx_invoice_period', 'billing_period_start', 'billing_period_end'),
    )

    def __str__(self):
        """Return a string representation of the object."""
        return f"SubscriptionInvoice [invoice_id={self.invoice_id}, invoice_number={self.invoice_number}, total_amount={self.total_amount}]"

    def to_dict(self, include_subscription=False, include_payments=False):
        """Dictionary representation of the object."""
        data = {
            "invoice_id": str(self.invoice_id),
            "subscription_id": str(self.subscription_id),
            "invoice_number": self.invoice_number,
            "billing_period_start": self.billing_period_start.strftime('%Y-%m-%d') if self.billing_period_start else None,
            "billing_period_end": self.billing_period_end.strftime('%Y-%m-%d') if self.billing_period_end else None,
            "employee_count_billed": self.employee_count_billed,
            "subtotal": float(self.subtotal),
            "tax_rate": float(self.tax_rate),
            "tax_amount": float(self.tax_amount),
            "total_amount": float(self.total_amount),
            "status": self.status,
            "due_date": self.due_date.strftime('%Y-%m-%d') if self.due_date else None,
            "paid_date": self.paid_date.strftime('%Y-%m-%d') if self.paid_date else None,
            "payment_method": self.payment_method,
            "payment_reference": self.payment_reference,
            "notes": self.notes,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None,
            "is_overdue": self.is_overdue(),
            "days_overdue": self.days_overdue(),
            "amount_paid": float(self.get_amount_paid()),
            "amount_outstanding": float(self.get_amount_outstanding())
        }
        
        if include_subscription and self.subscription:
            data["subscription"] = self.subscription.to_dict(include_company=True, include_plan=True)
            
        if include_payments:
            data["payments"] = [payment.to_dict() for payment in self.payments.all()]
            
        return data

    def is_overdue(self):
        """Check if invoice is overdue."""
        return self.status in ['PENDING', 'DRAFT'] and self.due_date < date.today()

    def days_overdue(self):
        """Get number of days overdue."""
        if self.is_overdue():
            return (date.today() - self.due_date).days
        return 0

    def get_amount_paid(self):
        """Get total amount paid for this invoice."""
        try:
            paid_amount = self.payments.filter_by(status='COMPLETED').with_entities(
                db.func.sum(self.payments.model.amount)
            ).scalar()
            return paid_amount or Decimal('0.00')
        except Exception as e:
            current_app.logger.error(f"Error calculating paid amount: {e}")
            return Decimal('0.00')

    def get_amount_outstanding(self):
        """Get outstanding amount for this invoice."""
        return self.total_amount - self.get_amount_paid()

    def is_fully_paid(self):
        """Check if invoice is fully paid."""
        return self.get_amount_outstanding() <= Decimal('0.00')

    @classmethod
    def generate_invoice_number(cls):
        """Generate a unique invoice number."""
        try:
            # Format: INV-YYYY-NNNNNN
            year = datetime.now().year
            
            # Get the last invoice number for this year
            last_invoice = cls.query.filter(
                cls.invoice_number.like(f'INV-{year}-%')
            ).order_by(cls.invoice_number.desc()).first()
            
            if last_invoice:
                # Extract the sequence number and increment
                last_number = int(last_invoice.invoice_number.split('-')[-1])
                next_number = last_number + 1
            else:
                next_number = 1
            
            return f"INV-{year}-{next_number:06d}"
        except Exception as e:
            current_app.logger.error(f"Error generating invoice number: {e}")
            # Fallback to timestamp-based number
            timestamp = int(datetime.now().timestamp())
            return f"INV-{timestamp}"

    @classmethod
    def create_invoice(cls, subscription_id, billing_period_start, billing_period_end, 
                      employee_count, subtotal, tax_rate=0, due_days=30, **kwargs):
        """Create a new subscription invoice."""
        try:
            # Calculate tax and total
            tax_amount = subtotal * Decimal(str(tax_rate))
            total_amount = subtotal + tax_amount
            
            # Generate invoice number
            invoice_number = cls.generate_invoice_number()
            
            # Calculate due date
            due_date = date.today() + timedelta(days=due_days)
            
            invoice_data = {
                'subscription_id': subscription_id,
                'invoice_number': invoice_number,
                'billing_period_start': billing_period_start,
                'billing_period_end': billing_period_end,
                'employee_count_billed': employee_count,
                'subtotal': subtotal,
                'tax_rate': Decimal(str(tax_rate)),
                'tax_amount': tax_amount,
                'total_amount': total_amount,
                'due_date': due_date,
                'status': 'PENDING',
                **kwargs
            }

            invoice = cls(**invoice_data)
            db.session.add(invoice)
            db.session.commit()
            
            current_app.logger.info(f"Created invoice {invoice_number} for subscription {subscription_id}")
            return invoice, None
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error creating invoice: {e}")
            return None, str(e)

    @classmethod
    def get_invoice_by_id(cls, invoice_id):
        """Get invoice by ID."""
        try:
            if isinstance(invoice_id, str):
                invoice_id = uuid.UUID(invoice_id)
            return cls.query.filter_by(invoice_id=invoice_id).first()
        except Exception as e:
            current_app.logger.error(f"Error getting invoice by ID: {e}")
            return None

    @classmethod
    def get_invoice_by_number(cls, invoice_number):
        """Get invoice by invoice number."""
        try:
            return cls.query.filter_by(invoice_number=invoice_number).first()
        except Exception as e:
            current_app.logger.error(f"Error getting invoice by number: {e}")
            return None

    @classmethod
    def get_invoices_by_subscription(cls, subscription_id, limit=None):
        """Get invoices for a subscription."""
        try:
            if isinstance(subscription_id, str):
                subscription_id = uuid.UUID(subscription_id)
            
            query = cls.query.filter_by(subscription_id=subscription_id).order_by(cls.created_at.desc())
            if limit:
                query = query.limit(limit)
            return query.all()
        except Exception as e:
            current_app.logger.error(f"Error getting invoices by subscription: {e}")
            return []

    @classmethod
    def get_overdue_invoices(cls):
        """Get all overdue invoices."""
        try:
            return cls.query.filter(
                cls.status.in_(['PENDING', 'DRAFT']),
                cls.due_date < date.today()
            ).all()
        except Exception as e:
            current_app.logger.error(f"Error getting overdue invoices: {e}")
            return []

    def mark_as_paid(self, payment_date=None, payment_method=None, payment_reference=None):
        """Mark invoice as paid."""
        try:
            self.status = 'PAID'
            self.paid_date = payment_date or date.today()
            if payment_method:
                self.payment_method = payment_method
            if payment_reference:
                self.payment_reference = payment_reference

            db.session.commit()
            current_app.logger.info(f"Marked invoice {self.invoice_number} as paid")
            return True, None
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error marking invoice as paid: {e}")
            return False, str(e)

    def cancel_invoice(self, reason=None):
        """Cancel invoice."""
        try:
            if self.status == 'PAID':
                return False, "Cannot cancel a paid invoice"

            self.status = 'CANCELLED'
            if reason:
                self.notes = f"{self.notes or ''}\nCancelled: {reason}".strip()

            db.session.commit()
            current_app.logger.info(f"Cancelled invoice {self.invoice_number}")
            return True, None
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error cancelling invoice: {e}")
            return False, str(e)

    def update_status_based_on_payments(self):
        """Update invoice status based on payment status."""
        try:
            if self.is_fully_paid():
                self.status = 'PAID'
                if not self.paid_date:
                    self.paid_date = date.today()
            elif self.is_overdue():
                self.status = 'OVERDUE'
            elif self.get_amount_paid() > Decimal('0.00'):
                self.status = 'PARTIALLY_PAID'

            db.session.commit()
            return True, None
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error updating invoice status: {e}")
            return False, str(e)

    @classmethod
    def get_invoices_by_status(cls, status):
        """Get invoices by status."""
        try:
            return cls.query.filter_by(status=status).order_by(cls.created_at.desc()).all()
        except Exception as e:
            current_app.logger.error(f"Error getting invoices by status: {e}")
            return []

    @classmethod
    def get_invoices_by_date_range(cls, start_date, end_date):
        """Get invoices within a date range."""
        try:
            return cls.query.filter(
                cls.created_at >= start_date,
                cls.created_at <= end_date
            ).order_by(cls.created_at.desc()).all()
        except Exception as e:
            current_app.logger.error(f"Error getting invoices by date range: {e}")
            return []

    @classmethod
    def get_revenue_summary(cls, start_date=None, end_date=None):
        """Get revenue summary for a date range."""
        try:
            query = cls.query.filter_by(status='PAID')

            if start_date:
                query = query.filter(cls.paid_date >= start_date)
            if end_date:
                query = query.filter(cls.paid_date <= end_date)

            invoices = query.all()

            total_revenue = sum(invoice.total_amount for invoice in invoices)
            total_tax = sum(invoice.tax_amount for invoice in invoices)
            invoice_count = len(invoices)

            return {
                'total_revenue': float(total_revenue),
                'total_tax': float(total_tax),
                'net_revenue': float(total_revenue - total_tax),
                'invoice_count': invoice_count,
                'average_invoice_value': float(total_revenue / invoice_count) if invoice_count > 0 else 0
            }
        except Exception as e:
            current_app.logger.error(f"Error getting revenue summary: {e}")
            return {
                'total_revenue': 0,
                'total_tax': 0,
                'net_revenue': 0,
                'invoice_count': 0,
                'average_invoice_value': 0
            }
