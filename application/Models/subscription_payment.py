from application.database import central_db as db
import uuid
from sqlalchemy.dialects.postgresql import UUID
from flask import current_app
from datetime import datetime, date
from decimal import Decimal

class SubscriptionPayment(db.Model):
    """Model representing subscription payments (stored in central database)."""
    __tablename__ = 'subscription_payments'

    payment_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    invoice_id = db.Column(UUID(as_uuid=True), db.ForeignKey('subscription_invoices.invoice_id'), nullable=False)
    amount = db.Column(db.Numeric(12, 2), nullable=False)
    payment_method = db.Column(db.String(50), nullable=False)  # MANUAL, STRIPE, PAYPAL, BANK_TRANSFER, etc.
    payment_reference = db.Column(db.String(255), nullable=True)
    payment_date = db.Column(db.DateTime, nullable=False)
    status = db.Column(db.String(20), default='PENDING', nullable=False)  # PENDING, COMPLETED, FAILED, REFUNDED
    transaction_id = db.Column(db.String(255), nullable=True)  # External payment processor transaction ID
    processor_response = db.Column(db.Text, nullable=True)  # Raw response from payment processor
    notes = db.Column(db.Text, nullable=True)
    recorded_by = db.Column(db.String(36), nullable=True)  # User ID who recorded the payment
    created_at = db.Column(db.DateTime, server_default=db.func.now())
    updated_at = db.Column(db.DateTime, server_default=db.func.now(), onupdate=db.func.now())

    # Indexes
    __table_args__ = (
        db.Index('idx_payment_invoice', 'invoice_id'),
        db.Index('idx_payment_status', 'status'),
        db.Index('idx_payment_date', 'payment_date'),
        db.Index('idx_payment_method', 'payment_method'),
    )

    def __str__(self):
        """Return a string representation of the object."""
        return f"SubscriptionPayment [payment_id={self.payment_id}, amount={self.amount}, status={self.status}]"

    def to_dict(self, include_invoice=False):
        """Dictionary representation of the object."""
        data = {
            "payment_id": str(self.payment_id),
            "invoice_id": str(self.invoice_id),
            "amount": float(self.amount),
            "payment_method": self.payment_method,
            "payment_reference": self.payment_reference,
            "payment_date": self.payment_date.strftime('%Y-%m-%d %H:%M:%S') if self.payment_date else None,
            "status": self.status,
            "transaction_id": self.transaction_id,
            "notes": self.notes,
            "recorded_by": self.recorded_by,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None,
            "updated_at": self.updated_at.strftime('%Y-%m-%d %H:%M:%S') if self.updated_at else None
        }
        
        if include_invoice and self.invoice:
            data["invoice"] = self.invoice.to_dict()
            
        return data

    @classmethod
    def create_payment(cls, invoice_id, amount, payment_method, payment_reference=None, 
                      payment_date=None, recorded_by=None, **kwargs):
        """Create a new subscription payment."""
        try:
            from application.Models.subscription_invoice import SubscriptionInvoice
            
            # Validate invoice exists
            invoice = SubscriptionInvoice.get_invoice_by_id(invoice_id)
            if not invoice:
                return None, "Invoice not found"

            # Validate amount
            if amount <= 0:
                return None, "Payment amount must be greater than zero"

            # Check if payment amount exceeds outstanding amount
            outstanding = invoice.get_amount_outstanding()
            if amount > outstanding:
                return None, f"Payment amount ({amount}) exceeds outstanding amount ({outstanding})"

            payment_data = {
                'invoice_id': invoice_id,
                'amount': Decimal(str(amount)),
                'payment_method': payment_method,
                'payment_reference': payment_reference,
                'payment_date': payment_date or datetime.now(),
                'recorded_by': recorded_by,
                'status': 'COMPLETED',  # Default to completed for manual payments
                **kwargs
            }

            payment = cls(**payment_data)
            db.session.add(payment)
            
            # Update invoice status based on payments
            invoice.update_status_based_on_payments()
            
            db.session.commit()
            
            current_app.logger.info(f"Created payment of {amount} for invoice {invoice.invoice_number}")
            return payment, None
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error creating payment: {e}")
            return None, str(e)

    @classmethod
    def get_payment_by_id(cls, payment_id):
        """Get payment by ID."""
        try:
            if isinstance(payment_id, str):
                payment_id = uuid.UUID(payment_id)
            return cls.query.filter_by(payment_id=payment_id).first()
        except Exception as e:
            current_app.logger.error(f"Error getting payment by ID: {e}")
            return None

    @classmethod
    def get_payments_by_invoice(cls, invoice_id):
        """Get all payments for an invoice."""
        try:
            if isinstance(invoice_id, str):
                invoice_id = uuid.UUID(invoice_id)
            return cls.query.filter_by(invoice_id=invoice_id).order_by(cls.payment_date.desc()).all()
        except Exception as e:
            current_app.logger.error(f"Error getting payments by invoice: {e}")
            return []

    @classmethod
    def get_payments_by_method(cls, payment_method):
        """Get payments by payment method."""
        try:
            return cls.query.filter_by(payment_method=payment_method).order_by(cls.payment_date.desc()).all()
        except Exception as e:
            current_app.logger.error(f"Error getting payments by method: {e}")
            return []

    @classmethod
    def get_payments_by_status(cls, status):
        """Get payments by status."""
        try:
            return cls.query.filter_by(status=status).order_by(cls.payment_date.desc()).all()
        except Exception as e:
            current_app.logger.error(f"Error getting payments by status: {e}")
            return []

    @classmethod
    def get_payments_by_date_range(cls, start_date, end_date):
        """Get payments within a date range."""
        try:
            return cls.query.filter(
                cls.payment_date >= start_date,
                cls.payment_date <= end_date
            ).order_by(cls.payment_date.desc()).all()
        except Exception as e:
            current_app.logger.error(f"Error getting payments by date range: {e}")
            return []

    def update_status(self, new_status, notes=None):
        """Update payment status."""
        try:
            old_status = self.status
            self.status = new_status
            
            if notes:
                self.notes = f"{self.notes or ''}\nStatus changed from {old_status} to {new_status}: {notes}".strip()
            
            # Update invoice status if payment status changed
            if self.invoice:
                self.invoice.update_status_based_on_payments()
            
            db.session.commit()
            current_app.logger.info(f"Updated payment {self.payment_id} status from {old_status} to {new_status}")
            return True, None
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error updating payment status: {e}")
            return False, str(e)

    def refund_payment(self, refund_amount=None, reason=None):
        """Refund a payment."""
        try:
            if self.status != 'COMPLETED':
                return False, "Can only refund completed payments"

            refund_amount = refund_amount or self.amount
            if refund_amount > self.amount:
                return False, "Refund amount cannot exceed payment amount"

            # Create a refund payment record
            refund_data = {
                'invoice_id': self.invoice_id,
                'amount': -refund_amount,  # Negative amount for refund
                'payment_method': f"REFUND_{self.payment_method}",
                'payment_reference': f"REFUND_{self.payment_reference or self.payment_id}",
                'payment_date': datetime.now(),
                'status': 'COMPLETED',
                'notes': f"Refund for payment {self.payment_id}. Reason: {reason or 'No reason provided'}"
            }

            refund_payment = SubscriptionPayment(**refund_data)
            db.session.add(refund_payment)

            # Update original payment status if fully refunded
            if refund_amount == self.amount:
                self.status = 'REFUNDED'
                if reason:
                    self.notes = f"{self.notes or ''}\nRefunded: {reason}".strip()

            # Update invoice status
            if self.invoice:
                self.invoice.update_status_based_on_payments()

            db.session.commit()
            current_app.logger.info(f"Refunded {refund_amount} for payment {self.payment_id}")
            return True, None
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error refunding payment: {e}")
            return False, str(e)

    @classmethod
    def get_payment_summary(cls, start_date=None, end_date=None):
        """Get payment summary for a date range."""
        try:
            query = cls.query.filter_by(status='COMPLETED')
            
            if start_date:
                query = query.filter(cls.payment_date >= start_date)
            if end_date:
                query = query.filter(cls.payment_date <= end_date)
            
            payments = query.all()
            
            total_amount = sum(payment.amount for payment in payments if payment.amount > 0)
            total_refunds = sum(abs(payment.amount) for payment in payments if payment.amount < 0)
            net_amount = total_amount - total_refunds
            payment_count = len([p for p in payments if p.amount > 0])
            refund_count = len([p for p in payments if p.amount < 0])
            
            # Group by payment method
            by_method = {}
            for payment in payments:
                method = payment.payment_method
                if method not in by_method:
                    by_method[method] = {'count': 0, 'amount': 0}
                by_method[method]['count'] += 1
                by_method[method]['amount'] += float(payment.amount)
            
            return {
                'total_amount': float(total_amount),
                'total_refunds': float(total_refunds),
                'net_amount': float(net_amount),
                'payment_count': payment_count,
                'refund_count': refund_count,
                'average_payment': float(total_amount / payment_count) if payment_count > 0 else 0,
                'by_method': by_method
            }
        except Exception as e:
            current_app.logger.error(f"Error getting payment summary: {e}")
            return {
                'total_amount': 0,
                'total_refunds': 0,
                'net_amount': 0,
                'payment_count': 0,
                'refund_count': 0,
                'average_payment': 0,
                'by_method': {}
            }
