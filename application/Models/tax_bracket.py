from application.database import central_db as db
from sqlalchemy.dialects.postgresql import UUID
import uuid
from flask import current_app

class TaxBracket(db.Model):
    """Model representing tax brackets for progressive tax calculations (stored in central database)."""
    __tablename__ = 'tax_brackets'

    bracket_id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    policy_id = db.Column(UUID(as_uuid=True), db.<PERSON>ey('payroll_policies.policy_id'), nullable=False)
    bracket_order = db.Column(db.Integer, nullable=False)  # Order of application (1, 2, 3...)
    min_amount = db.Column(db.Numeric(15, 2), nullable=False)  # Minimum amount for this bracket
    max_amount = db.Column(db.Numeric(15, 2), nullable=True)   # Maximum amount (null = unlimited)
    tax_rate = db.Column(db.Numeric(5, 4), nullable=False)     # Tax rate as decimal (0.10 for 10%)
    effective_from = db.Column(db.Date, nullable=False)        # Inherited from policy
    effective_to = db.Column(db.Date, nullable=True)           # Inherited from policy
    created_at = db.Column(db.DateTime, server_default=db.func.now())

    # Relationship
    policy = db.relationship('PayrollPolicy', backref=db.backref('tax_brackets', lazy='dynamic', order_by='TaxBracket.bracket_order'))

    # Unique constraint: bracket order must be unique per policy
    __table_args__ = (
        db.UniqueConstraint('policy_id', 'bracket_order', name='uq_tax_bracket_policy_order'),
        db.Index('idx_tax_bracket_policy', 'policy_id', 'bracket_order'),
    )

    def __str__(self):
        """Return a string representation of the object."""
        max_display = f"{self.max_amount}" if self.max_amount else "unlimited"
        return f"TaxBracket [bracket_id={self.bracket_id}, range={self.min_amount}-{max_display}, rate={self.tax_rate}]"

    def to_dict(self):
        """Dictionary representation of the object."""
        return {
            "bracket_id": str(self.bracket_id),
            "policy_id": str(self.policy_id),
            "bracket_order": self.bracket_order,
            "min_amount": float(self.min_amount),
            "max_amount": float(self.max_amount) if self.max_amount else None,
            "tax_rate": float(self.tax_rate),
            "tax_rate_percentage": float(self.tax_rate * 100),  # For display purposes
            "effective_from": self.effective_from.strftime('%Y-%m-%d') if self.effective_from else None,
            "effective_to": self.effective_to.strftime('%Y-%m-%d') if self.effective_to else None,
            "created_at": self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None
        }

    @classmethod
    def create_bracket(cls, **kwargs):
        """Create a new tax bracket."""
        try:
            bracket = cls(**kwargs)
            db.session.add(bracket)
            db.session.commit()
            current_app.logger.info(f"Created tax bracket: {bracket.min_amount}-{bracket.max_amount or 'unlimited'} at {bracket.tax_rate}")
            return bracket
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error creating tax bracket: {e}")
            return None

    @classmethod
    def get_by_id(cls, bracket_id):
        """Get tax bracket by ID."""
        return cls.query.filter_by(bracket_id=bracket_id).first()

    @classmethod
    def get_by_policy(cls, policy_id):
        """Get all tax brackets for a specific policy, ordered by bracket_order."""
        return cls.query.filter_by(policy_id=policy_id).order_by(cls.bracket_order).all()

    @classmethod
    def get_brackets_for_policy(cls, policy_id):
        """Get all tax brackets for a specific policy, ordered by bracket_order."""
        return cls.query.filter_by(policy_id=policy_id).order_by(cls.bracket_order).all()

    @classmethod
    def create_brackets_for_policy(cls, policy_id, brackets_data):
        """Create multiple tax brackets for a policy."""
        try:
            created_brackets = []
            
            for bracket_data in brackets_data:
                bracket_data['policy_id'] = policy_id
                bracket = cls.create_bracket(**bracket_data)
                if bracket:
                    created_brackets.append(bracket)
                else:
                    # If any bracket fails, rollback all
                    db.session.rollback()
                    return None

            db.session.commit()
            current_app.logger.info(f"Created {len(created_brackets)} tax brackets for policy {policy_id}")
            return created_brackets

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error creating tax brackets for policy: {e}")
            return None

    @classmethod
    def update_bracket(cls, bracket_id, **kwargs):
        """Update a tax bracket."""
        try:
            bracket = cls.query.filter_by(bracket_id=bracket_id).first()
            if not bracket:
                return None

            for key, value in kwargs.items():
                if hasattr(bracket, key):
                    setattr(bracket, key, value)

            db.session.commit()
            current_app.logger.info(f"Updated tax bracket: {bracket.bracket_id}")
            return bracket
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error updating tax bracket: {e}")
            return None

    @classmethod
    def delete_brackets_for_policy(cls, policy_id):
        """Delete all tax brackets for a policy."""
        try:
            brackets = cls.query.filter_by(policy_id=policy_id).all()
            for bracket in brackets:
                db.session.delete(bracket)
            
            db.session.commit()
            current_app.logger.info(f"Deleted {len(brackets)} tax brackets for policy {policy_id}")
            return True
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error deleting tax brackets: {e}")
            return False

    def calculate_tax_for_amount(self, amount):
        """Calculate tax for a specific amount using this bracket."""
        from decimal import Decimal

        amount = Decimal(str(amount))

        if amount < self.min_amount:
            return Decimal('0')

        # Determine the taxable amount in this bracket
        if self.max_amount:
            taxable_amount = min(amount, self.max_amount) - self.min_amount
        else:
            taxable_amount = amount - self.min_amount

        return taxable_amount * self.tax_rate

    @classmethod
    def calculate_progressive_tax(cls, policy_id, gross_amount):
        """Calculate progressive tax using all brackets for a policy."""
        from decimal import Decimal

        brackets = cls.get_brackets_for_policy(policy_id)
        total_tax = Decimal('0')
        gross_amount = Decimal(str(gross_amount))

        for bracket in brackets:
            if gross_amount > bracket.min_amount:
                bracket_tax = bracket.calculate_tax_for_amount(gross_amount)
                total_tax += Decimal(str(bracket_tax))

                current_app.logger.debug(f"Bracket {bracket.bracket_order}: {bracket_tax} tax on amount {gross_amount}")

        return total_tax
