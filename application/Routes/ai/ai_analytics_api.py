from flask import Blueprint, request, jsonify, current_app as app
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.Models.ai.ai_insight_request import AIInsightRequest
from application.Models.ai.ai_insight_response import AIInsightResponse
from application.Services.ai.ai_cache_service import AICacheService
from application.Models.company import Company
from datetime import datetime, timedelta
import json

# Create the blueprint
ai_analytics_api = Blueprint('ai_analytics_api', __name__)

@ai_analytics_api.route('/api/ai/analytics/usage-stats', methods=['GET'])
@token_required
@roles_required(['admin', 'hr_manager'])
def get_ai_usage_stats():
    """
    Get AI usage statistics for a company.
    
    Query Parameters:
    - company_id: required - company ID
    - start_date: optional - start date for stats (YYYY-MM-DD)
    - end_date: optional - end date for stats (YYYY-MM-DD)
    - period: optional - predefined period (7d, 30d, 90d, 1y)
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"message": "Company ID is required"}), 400
        
        # Parse date parameters
        start_date = None
        end_date = None
        
        # Handle predefined periods
        period = request.args.get('period')
        if period:
            end_date = datetime.now()
            if period == '7d':
                start_date = end_date - timedelta(days=7)
            elif period == '30d':
                start_date = end_date - timedelta(days=30)
            elif period == '90d':
                start_date = end_date - timedelta(days=90)
            elif period == '1y':
                start_date = end_date - timedelta(days=365)
        else:
            # Parse custom dates
            start_date_str = request.args.get('start_date')
            end_date_str = request.args.get('end_date')
            
            if start_date_str:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
            if end_date_str:
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
        
        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404
        
        # Connect to the company database and get usage stats
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            usage_stats = AIInsightRequest.get_usage_stats(
                session, 
                company_id=company_id,
                start_date=start_date,
                end_date=end_date
            )
            
            # Get additional analytics
            query = session.query(AIInsightRequest).filter(
                AIInsightRequest.company_id == company_id
            )
            
            if start_date:
                query = query.filter(AIInsightRequest.created_at >= start_date)
            if end_date:
                query = query.filter(AIInsightRequest.created_at <= end_date)
            
            requests = query.all()
            
            # Module breakdown
            module_stats = {}
            insight_type_stats = {}
            daily_usage = {}
            
            for req in requests:
                # Module stats
                module = req.module
                if module not in module_stats:
                    module_stats[module] = {'total': 0, 'successful': 0, 'failed': 0}
                module_stats[module]['total'] += 1
                if req.status == 'completed':
                    module_stats[module]['successful'] += 1
                elif req.status == 'failed':
                    module_stats[module]['failed'] += 1
                
                # Insight type stats
                insight_type = req.insight_type
                if insight_type not in insight_type_stats:
                    insight_type_stats[insight_type] = 0
                insight_type_stats[insight_type] += 1
                
                # Daily usage
                day = req.created_at.strftime('%Y-%m-%d')
                if day not in daily_usage:
                    daily_usage[day] = 0
                daily_usage[day] += 1
            
            # Get cache stats
            cache_stats = AICacheService.get_cache_stats(session, company_id)
            
            return jsonify({
                "status": "success",
                "usage_stats": usage_stats,
                "module_breakdown": module_stats,
                "insight_type_breakdown": insight_type_stats,
                "daily_usage": daily_usage,
                "cache_stats": cache_stats,
                "period": {
                    "start_date": start_date.strftime('%Y-%m-%d') if start_date else None,
                    "end_date": end_date.strftime('%Y-%m-%d') if end_date else None
                }
            }), 200
                
    except Exception as e:
        app.logger.error(f"Error in get_ai_usage_stats: {e}")
        return jsonify({
            "status": "error",
            "message": f"Error getting AI usage stats: {str(e)}"
        }), 500

@ai_analytics_api.route('/api/ai/analytics/cost-analysis', methods=['GET'])
@token_required
@roles_required(['admin', 'hr_manager'])
def get_ai_cost_analysis():
    """
    Get AI cost analysis for a company.
    
    Query Parameters:
    - company_id: required - company ID
    - start_date: optional - start date for analysis (YYYY-MM-DD)
    - end_date: optional - end date for analysis (YYYY-MM-DD)
    - period: optional - predefined period (7d, 30d, 90d, 1y)
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"message": "Company ID is required"}), 400
        
        # Parse date parameters (same logic as usage stats)
        start_date = None
        end_date = None
        
        period = request.args.get('period')
        if period:
            end_date = datetime.now()
            if period == '7d':
                start_date = end_date - timedelta(days=7)
            elif period == '30d':
                start_date = end_date - timedelta(days=30)
            elif period == '90d':
                start_date = end_date - timedelta(days=90)
            elif period == '1y':
                start_date = end_date - timedelta(days=365)
        else:
            start_date_str = request.args.get('start_date')
            end_date_str = request.args.get('end_date')
            
            if start_date_str:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
            if end_date_str:
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
        
        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404
        
        # Connect to the company database and get cost analysis
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            query = session.query(AIInsightRequest).filter(
                AIInsightRequest.company_id == company_id,
                AIInsightRequest.status == 'completed'
            )
            
            if start_date:
                query = query.filter(AIInsightRequest.created_at >= start_date)
            if end_date:
                query = query.filter(AIInsightRequest.created_at <= end_date)
            
            requests = query.all()
            
            # Calculate cost breakdown
            total_cost = 0
            total_tokens = 0
            module_costs = {}
            daily_costs = {}
            provider_costs = {}
            
            for req in requests:
                cost = float(req.estimated_cost) if req.estimated_cost else 0
                tokens = req.tokens_used or 0
                
                total_cost += cost
                total_tokens += tokens
                
                # Module costs
                module = req.module
                if module not in module_costs:
                    module_costs[module] = {'cost': 0, 'tokens': 0, 'requests': 0}
                module_costs[module]['cost'] += cost
                module_costs[module]['tokens'] += tokens
                module_costs[module]['requests'] += 1
                
                # Daily costs
                day = req.created_at.strftime('%Y-%m-%d')
                if day not in daily_costs:
                    daily_costs[day] = {'cost': 0, 'tokens': 0, 'requests': 0}
                daily_costs[day]['cost'] += cost
                daily_costs[day]['tokens'] += tokens
                daily_costs[day]['requests'] += 1
                
                # Provider costs
                provider_id = str(req.provider_id)
                if provider_id not in provider_costs:
                    provider_costs[provider_id] = {'cost': 0, 'tokens': 0, 'requests': 0}
                provider_costs[provider_id]['cost'] += cost
                provider_costs[provider_id]['tokens'] += tokens
                provider_costs[provider_id]['requests'] += 1
            
            # Calculate averages
            total_requests = len(requests)
            avg_cost_per_request = total_cost / max(total_requests, 1)
            avg_tokens_per_request = total_tokens / max(total_requests, 1)
            
            # Projected monthly cost (if we have enough data)
            projected_monthly_cost = 0
            if start_date and end_date:
                days_in_period = (end_date - start_date).days
                if days_in_period > 0:
                    daily_avg_cost = total_cost / days_in_period
                    projected_monthly_cost = daily_avg_cost * 30
            
            return jsonify({
                "status": "success",
                "cost_analysis": {
                    "total_cost": round(total_cost, 4),
                    "total_tokens": total_tokens,
                    "total_requests": total_requests,
                    "average_cost_per_request": round(avg_cost_per_request, 4),
                    "average_tokens_per_request": round(avg_tokens_per_request, 2),
                    "projected_monthly_cost": round(projected_monthly_cost, 2)
                },
                "module_breakdown": module_costs,
                "daily_breakdown": daily_costs,
                "provider_breakdown": provider_costs,
                "period": {
                    "start_date": start_date.strftime('%Y-%m-%d') if start_date else None,
                    "end_date": end_date.strftime('%Y-%m-%d') if end_date else None
                }
            }), 200
                
    except Exception as e:
        app.logger.error(f"Error in get_ai_cost_analysis: {e}")
        return jsonify({
            "status": "error",
            "message": f"Error getting AI cost analysis: {str(e)}"
        }), 500

@ai_analytics_api.route('/api/ai/analytics/insight-performance', methods=['GET'])
@token_required
@roles_required(['admin', 'hr_manager'])
def get_insight_performance():
    """
    Get AI insight performance analytics.
    
    Query Parameters:
    - company_id: required - company ID
    - category: optional - filter by insight category
    - limit: optional - limit number of insights analyzed (default: 100)
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"message": "Company ID is required"}), 400
        
        category = request.args.get('category')
        limit = int(request.args.get('limit', 100))
        
        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404
        
        # Connect to the company database and get insight performance
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Get insights with their requests
            query = session.query(AIInsightResponse).join(AIInsightRequest).filter(
                AIInsightRequest.company_id == company_id,
                AIInsightResponse.is_active == True
            )
            
            if category:
                query = query.filter(AIInsightResponse.category == category)
            
            insights = query.order_by(AIInsightResponse.created_at.desc()).limit(limit).all()
            
            # Calculate performance metrics
            total_insights = len(insights)
            total_views = sum([insight.view_count for insight in insights])
            total_actions = sum([insight.action_count for insight in insights])
            total_ratings = sum([insight.rating_count for insight in insights])
            
            # Average metrics
            avg_views_per_insight = total_views / max(total_insights, 1)
            avg_actions_per_insight = total_actions / max(total_insights, 1)
            avg_rating = sum([float(insight.user_rating) for insight in insights if insight.user_rating]) / max(len([i for i in insights if i.user_rating]), 1)
            
            # Category breakdown
            category_performance = {}
            insight_type_performance = {}
            priority_performance = {}
            
            for insight in insights:
                # Category performance
                cat = insight.category
                if cat not in category_performance:
                    category_performance[cat] = {
                        'count': 0, 'views': 0, 'actions': 0, 'ratings': 0, 'avg_rating': 0
                    }
                category_performance[cat]['count'] += 1
                category_performance[cat]['views'] += insight.view_count
                category_performance[cat]['actions'] += insight.action_count
                category_performance[cat]['ratings'] += insight.rating_count
                
                # Insight type performance
                insight_type = insight.insight_type
                if insight_type not in insight_type_performance:
                    insight_type_performance[insight_type] = {
                        'count': 0, 'views': 0, 'actions': 0, 'avg_rating': 0
                    }
                insight_type_performance[insight_type]['count'] += 1
                insight_type_performance[insight_type]['views'] += insight.view_count
                insight_type_performance[insight_type]['actions'] += insight.action_count
                
                # Priority performance
                priority = insight.priority
                if priority not in priority_performance:
                    priority_performance[priority] = {
                        'count': 0, 'views': 0, 'actions': 0
                    }
                priority_performance[priority]['count'] += 1
                priority_performance[priority]['views'] += insight.view_count
                priority_performance[priority]['actions'] += insight.action_count
            
            # Calculate averages for categories
            for cat_data in category_performance.values():
                if cat_data['ratings'] > 0:
                    cat_insights = [i for i in insights if i.category == cat and i.user_rating]
                    cat_data['avg_rating'] = sum([float(i.user_rating) for i in cat_insights]) / len(cat_insights)
            
            # Top performing insights
            top_viewed = sorted(insights, key=lambda x: x.view_count, reverse=True)[:5]
            top_rated = sorted([i for i in insights if i.user_rating], key=lambda x: float(x.user_rating), reverse=True)[:5]
            most_actionable = sorted(insights, key=lambda x: x.action_count, reverse=True)[:5]
            
            return jsonify({
                "status": "success",
                "performance_summary": {
                    "total_insights": total_insights,
                    "total_views": total_views,
                    "total_actions": total_actions,
                    "total_ratings": total_ratings,
                    "average_views_per_insight": round(avg_views_per_insight, 2),
                    "average_actions_per_insight": round(avg_actions_per_insight, 2),
                    "average_rating": round(avg_rating, 2),
                    "engagement_rate": round((total_actions / max(total_views, 1)) * 100, 2)
                },
                "category_performance": category_performance,
                "insight_type_performance": insight_type_performance,
                "priority_performance": priority_performance,
                "top_performers": {
                    "most_viewed": [insight.to_dict(include_content=False) for insight in top_viewed],
                    "highest_rated": [insight.to_dict(include_content=False) for insight in top_rated],
                    "most_actionable": [insight.to_dict(include_content=False) for insight in most_actionable]
                }
            }), 200
                
    except Exception as e:
        app.logger.error(f"Error in get_insight_performance: {e}")
        return jsonify({
            "status": "error",
            "message": f"Error getting insight performance: {str(e)}"
        }), 500

@ai_analytics_api.route('/api/ai/analytics/cache-management', methods=['POST'])
@token_required
@roles_required(['admin'])
def manage_ai_cache():
    """
    Manage AI cache (cleanup, invalidation).
    
    Request Body:
    {
        "company_id": "required - company ID",
        "action": "required - cleanup_expired, invalidate_all, invalidate_pattern",
        "pattern": "optional - pattern for invalidation",
        "insight_type": "optional - insight type for targeted invalidation"
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"message": "Request body is required"}), 400
        
        company_id = data.get('company_id')
        action = data.get('action')
        
        if not company_id:
            return jsonify({"message": "Company ID is required"}), 400
        if not action:
            return jsonify({"message": "Action is required"}), 400
        
        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404
        
        # Connect to the company database and perform cache management
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            result_count = 0
            
            if action == 'cleanup_expired':
                result_count = AICacheService.cleanup_expired_cache(session)
                message = f"Cleaned up {result_count} expired cache entries"
                
            elif action == 'invalidate_all':
                result_count = AICacheService.invalidate_cache(session, company_id=company_id)
                message = f"Invalidated {result_count} cache entries"
                
            elif action == 'invalidate_pattern':
                pattern = data.get('pattern')
                insight_type = data.get('insight_type')
                result_count = AICacheService.invalidate_cache(
                    session, 
                    pattern=pattern, 
                    company_id=company_id,
                    insight_type=insight_type
                )
                message = f"Invalidated {result_count} cache entries matching criteria"
                
            else:
                return jsonify({
                    "status": "error",
                    "message": f"Unknown action: {action}"
                }), 400
            
            return jsonify({
                "status": "success",
                "message": message,
                "affected_count": result_count
            }), 200
                
    except Exception as e:
        app.logger.error(f"Error in manage_ai_cache: {e}")
        return jsonify({
            "status": "error",
            "message": f"Error managing AI cache: {str(e)}"
        }), 500
