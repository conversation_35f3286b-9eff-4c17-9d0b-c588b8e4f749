from flask import Blueprint, request, jsonify, current_app as app
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.Models.ai.ai_provider import AIProvider
from application.Models.ai.ai_insight_template import AIInsightTemplate
from application.Models.ai.ai_company_config import AICompanyConfig
from application.Services.ai.ai_service_factory import AIServiceFactory
from application.Models.company import Company
import json

# Create the blueprint
ai_config_api = Blueprint('ai_config_api', __name__)

@ai_config_api.route('/api/ai/providers', methods=['GET'])
@token_required
@roles_required(['admin', 'hr_manager'])
def get_ai_providers():
    """
    Get all available AI providers.
    
    Query Parameters:
    - company_id: required - company ID
    - active_only: optional - filter active providers only (default: true)
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"message": "Company ID is required"}), 400
        
        active_only = request.args.get('active_only', 'true').lower() == 'true'
        
        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404
        
        # Connect to the company database and get providers
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            query = session.query(AIProvider)
            
            if active_only:
                query = query.filter(AIProvider.is_active == True)
            
            providers = query.order_by(AIProvider.is_default.desc(), AIProvider.name).all()
            
            return jsonify({
                "status": "success",
                "providers": [provider.to_dict() for provider in providers],
                "count": len(providers)
            }), 200
                
    except Exception as e:
        app.logger.error(f"Error in get_ai_providers: {e}")
        return jsonify({
            "status": "error",
            "message": f"Error getting AI providers: {str(e)}"
        }), 500

@ai_config_api.route('/api/ai/providers', methods=['POST'])
@token_required
@roles_required(['admin'])
def create_ai_provider():
    """
    Create a new AI provider configuration.
    
    Request Body:
    {
        "company_id": "required - company ID",
        "name": "required - provider name",
        "display_name": "required - display name",
        "provider_type": "required - provider type",
        "model_name": "required - model name",
        "api_endpoint": "optional - API endpoint",
        "max_tokens": "optional - max tokens",
        "requests_per_minute": "optional - rate limit per minute",
        "requests_per_day": "optional - rate limit per day",
        "cost_per_1k_tokens": "optional - cost per 1k tokens",
        "is_default": "optional - set as default provider"
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"message": "Request body is required"}), 400
        
        company_id = data.get('company_id')
        if not company_id:
            return jsonify({"message": "Company ID is required"}), 400
        
        # Required fields
        required_fields = ['name', 'display_name', 'provider_type', 'model_name']
        for field in required_fields:
            if not data.get(field):
                return jsonify({"message": f"{field} is required"}), 400
        
        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404
        
        # Connect to the company database and create provider
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Check if provider with same name already exists
            existing_provider = session.query(AIProvider).filter_by(name=data['name']).first()
            if existing_provider:
                return jsonify({"message": f"Provider with name '{data['name']}' already exists"}), 400
            
            # Create new provider
            provider = AIProvider.create_provider(session, **data)
            
            if provider:
                return jsonify({
                    "status": "success",
                    "message": "AI provider created successfully",
                    "provider": provider.to_dict()
                }), 201
            else:
                return jsonify({
                    "status": "error",
                    "message": "Failed to create AI provider"
                }), 400
                
    except Exception as e:
        app.logger.error(f"Error in create_ai_provider: {e}")
        return jsonify({
            "status": "error",
            "message": f"Error creating AI provider: {str(e)}"
        }), 500

@ai_config_api.route('/api/ai/providers/<provider_id>', methods=['PUT'])
@token_required
@roles_required(['admin'])
def update_ai_provider(provider_id):
    """
    Update an AI provider configuration.
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"message": "Request body is required"}), 400
        
        company_id = data.get('company_id')
        if not company_id:
            return jsonify({"message": "Company ID is required"}), 400
        
        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404
        
        # Connect to the company database and update provider
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            provider = AIProvider.update_provider(session, provider_id, **data)
            
            if provider:
                return jsonify({
                    "status": "success",
                    "message": "AI provider updated successfully",
                    "provider": provider.to_dict()
                }), 200
            else:
                return jsonify({
                    "status": "error",
                    "message": "AI provider not found"
                }), 404
                
    except Exception as e:
        app.logger.error(f"Error in update_ai_provider: {e}")
        return jsonify({
            "status": "error",
            "message": f"Error updating AI provider: {str(e)}"
        }), 500

@ai_config_api.route('/api/ai/providers/<provider_id>/test', methods=['POST'])
@token_required
@roles_required(['admin'])
def test_ai_provider(provider_id):
    """
    Test an AI provider with a simple request.
    """
    try:
        data = request.get_json() or {}
        company_id = data.get('company_id')
        
        if not company_id:
            return jsonify({"message": "Company ID is required"}), 400
        
        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404
        
        # Connect to the company database and test provider
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            provider = session.query(AIProvider).filter_by(provider_id=provider_id).first()
            
            if not provider:
                return jsonify({
                    "status": "error",
                    "message": "AI provider not found"
                }), 404
            
            # Test the provider
            test_result = AIServiceFactory.test_provider(provider.name, provider.to_dict())
            
            return jsonify({
                "status": "success" if test_result.get('success') else "error",
                "test_result": test_result
            }), 200 if test_result.get('success') else 400
                
    except Exception as e:
        app.logger.error(f"Error in test_ai_provider: {e}")
        return jsonify({
            "status": "error",
            "message": f"Error testing AI provider: {str(e)}"
        }), 500

@ai_config_api.route('/api/ai/company-config', methods=['GET'])
@token_required
@roles_required(['admin', 'hr_manager'])
def get_company_ai_config():
    """
    Get AI configuration for a company.
    
    Query Parameters:
    - company_id: required - company ID
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"message": "Company ID is required"}), 400
        
        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404
        
        # Connect to the company database and get config
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            config = AICompanyConfig.get_config_by_company(session, company_id)
            
            if config:
                return jsonify({
                    "status": "success",
                    "config": config.to_dict()
                }), 200
            else:
                return jsonify({
                    "status": "error",
                    "message": "AI configuration not found"
                }), 404
                
    except Exception as e:
        app.logger.error(f"Error in get_company_ai_config: {e}")
        return jsonify({
            "status": "error",
            "message": f"Error getting AI configuration: {str(e)}"
        }), 500

@ai_config_api.route('/api/ai/company-config', methods=['PUT'])
@token_required
@roles_required(['admin', 'hr_manager'])
def update_company_ai_config():
    """
    Update AI configuration for a company.
    
    Request Body:
    {
        "company_id": "required - company ID",
        "company_size": "optional - company size",
        "industry": "optional - industry",
        "insight_frequency": "optional - insight frequency",
        "preferred_tone": "optional - preferred tone",
        "enable_ai_notifications": "optional - enable notifications",
        ... (other config fields)
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"message": "Request body is required"}), 400
        
        company_id = data.get('company_id')
        if not company_id:
            return jsonify({"message": "Company ID is required"}), 400
        
        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404
        
        # Remove company_id from update data
        update_data = {k: v for k, v in data.items() if k != 'company_id'}
        
        # Connect to the company database and update config
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            config = AICompanyConfig.update_config(session, company_id, **update_data)
            
            if config:
                return jsonify({
                    "status": "success",
                    "message": "AI configuration updated successfully",
                    "config": config.to_dict()
                }), 200
            else:
                return jsonify({
                    "status": "error",
                    "message": "Failed to update AI configuration"
                }), 400
                
    except Exception as e:
        app.logger.error(f"Error in update_company_ai_config: {e}")
        return jsonify({
            "status": "error",
            "message": f"Error updating AI configuration: {str(e)}"
        }), 500

@ai_config_api.route('/api/ai/templates', methods=['GET'])
@token_required
@roles_required(['admin', 'hr_manager'])
def get_ai_templates():
    """
    Get AI insight templates.
    
    Query Parameters:
    - company_id: required - company ID
    - category: optional - filter by category
    - active_only: optional - filter active templates only (default: true)
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"message": "Company ID is required"}), 400
        
        category = request.args.get('category')
        active_only = request.args.get('active_only', 'true').lower() == 'true'
        
        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404
        
        # Connect to the company database and get templates
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            if category:
                templates = AIInsightTemplate.get_templates_by_category(session, category, active_only)
            else:
                query = session.query(AIInsightTemplate)
                if active_only:
                    query = query.filter(AIInsightTemplate.is_active == True)
                templates = query.order_by(AIInsightTemplate.category, AIInsightTemplate.name).all()
            
            return jsonify({
                "status": "success",
                "templates": [template.to_dict() for template in templates],
                "count": len(templates)
            }), 200
                
    except Exception as e:
        app.logger.error(f"Error in get_ai_templates: {e}")
        return jsonify({
            "status": "error",
            "message": f"Error getting AI templates: {str(e)}"
        }), 500

@ai_config_api.route('/api/ai/templates', methods=['POST'])
@token_required
@roles_required(['admin'])
def create_ai_template():
    """
    Create a new AI insight template.
    
    Request Body:
    {
        "company_id": "required - company ID",
        "name": "required - template name",
        "category": "required - template category",
        "insight_type": "required - insight type",
        "prompt_template": "required - prompt template with variables",
        "system_prompt": "optional - system prompt",
        "variables": "optional - array of variable definitions",
        "target_audience": "optional - target audience",
        "output_format": "optional - output format"
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"message": "Request body is required"}), 400
        
        company_id = data.get('company_id')
        if not company_id:
            return jsonify({"message": "Company ID is required"}), 400
        
        # Required fields
        required_fields = ['name', 'category', 'insight_type', 'prompt_template']
        for field in required_fields:
            if not data.get(field):
                return jsonify({"message": f"{field} is required"}), 400
        
        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404
        
        # Remove company_id from template data
        template_data = {k: v for k, v in data.items() if k != 'company_id'}
        template_data['is_system_template'] = False  # Custom template
        
        # Connect to the company database and create template
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            template = AIInsightTemplate.create_template(session, **template_data)
            
            if template:
                return jsonify({
                    "status": "success",
                    "message": "AI template created successfully",
                    "template": template.to_dict()
                }), 201
            else:
                return jsonify({
                    "status": "error",
                    "message": "Failed to create AI template"
                }), 400
                
    except Exception as e:
        app.logger.error(f"Error in create_ai_template: {e}")
        return jsonify({
            "status": "error",
            "message": f"Error creating AI template: {str(e)}"
        }), 500
