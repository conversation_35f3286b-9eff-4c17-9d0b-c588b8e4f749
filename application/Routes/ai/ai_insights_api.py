from flask import Blueprint, request, jsonify, current_app as app
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.Services.ai.ai_insights_orchestrator import AIInsightsOrchestrator
from application.Models.company import Company
import json

# Create the blueprint
ai_insights_api = Blueprint('ai_insights_api', __name__)

# DEPRECATED: This endpoint is deprecated in favor of using include_ai_insights=true
# parameter with the attendance statistics endpoint for consistency
# @ai_insights_api.route('/api/ai/insights/attendance', methods=['GET'])
# @token_required
# @roles_required(['admin', 'hr_manager', 'manager'])
def generate_attendance_insights_deprecated():
    """
    Generate AI-powered attendance insights for various time periods.

    Query Parameters:
    - company_id: required - company ID
    - insight_type: optional - daily_summary, weekly_summary, monthly_summary, annual_summary, custom_summary, trend_analysis, risk_assessment, recommendations (defaults to daily_summary)
    - period: optional - daily, weekly, monthly, annual, custom (auto-detected from insight_type if not provided)
    - date: optional - reference date for period calculation (YYYY-MM-DD format, defaults to today)
    - start_date: optional - start date for custom period (YYYY-MM-DD format, required if period=custom)
    - end_date: optional - end date for custom period (YYYY-MM-DD format, required if period=custom)
    - force_regenerate: optional - force regeneration even if cached (true/false, defaults to false)
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"message": "Company ID is required"}), 400

        insight_type = request.args.get('insight_type', 'daily_summary')
        period = request.args.get('period')
        date_str = request.args.get('date')
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')
        force_regenerate = request.args.get('force_regenerate', 'false').lower() == 'true'
        user_id = getattr(request, 'user_id', None)

        # Auto-detect period from insight_type if not provided
        if not period:
            if 'weekly' in insight_type:
                period = 'weekly'
            elif 'monthly' in insight_type:
                period = 'monthly'
            elif 'annual' in insight_type:
                period = 'annual'
            elif 'custom' in insight_type:
                period = 'custom'
            else:
                period = 'daily'

        # Validate period-specific requirements
        if period == 'custom' and (not start_date_str or not end_date_str):
            return jsonify({"message": "start_date and end_date are required for custom period"}), 400
        
        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404
        
        # Connect to the company database and generate insights
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Fetch attendance data from analytics (since we don't have request body data)
            attendance_data = _fetch_attendance_analytics_data(
                company_id, period, date_str, start_date_str, end_date_str
            )
            if not attendance_data:
                return jsonify({"message": "Failed to fetch attendance data for analysis"}), 500

            orchestrator = AIInsightsOrchestrator(session, company_id)

            result = orchestrator.generate_attendance_insights(
                data=attendance_data,
                insight_type=insight_type,
                user_id=user_id,
                force_regenerate=force_regenerate
            )
            
            if result.get('success'):
                return jsonify({
                    "status": "success",
                    "insight": result['insight'],
                    "cached": result.get('cached', False),
                    "processing_time": result.get('processing_time'),
                    "tokens_used": result.get('tokens_used'),
                    "cost": result.get('cost')
                }), 200
            else:
                return jsonify({
                    "status": "error",
                    "message": result.get('error', 'Failed to generate insights')
                }), 400
                
    except Exception as e:
        app.logger.error(f"Error in generate_attendance_insights: {e}")
        return jsonify({
            "status": "error",
            "message": f"Error generating attendance insights: {str(e)}"
        }), 500

@ai_insights_api.route('/api/ai/insights/announcements', methods=['POST'])
@token_required
@roles_required(['admin', 'hr_manager', 'manager'])
def generate_smart_announcements():
    """
    Generate AI-powered smart announcements.
    
    Request Body:
    {
        "company_id": "required - company ID",
        "announcement_type": "optional - weekly_summary, policy_reminder, celebration",
        "data": "required - data for announcement generation"
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"message": "Request body is required"}), 400
        
        company_id = data.get('company_id')
        if not company_id:
            return jsonify({"message": "Company ID is required"}), 400
        
        announcement_type = data.get('announcement_type', 'weekly_summary')
        announcement_data = data.get('data', {})
        user_id = getattr(request, 'user_id', None)
        
        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404
        
        # Connect to the company database and generate announcement
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            orchestrator = AIInsightsOrchestrator(session, company_id)
            
            result = orchestrator.generate_smart_announcements(
                data=announcement_data,
                announcement_type=announcement_type,
                user_id=user_id
            )
            
            if result.get('success'):
                return jsonify({
                    "status": "success",
                    "announcement": result['insight'],
                    "processing_time": result.get('processing_time'),
                    "tokens_used": result.get('tokens_used'),
                    "cost": result.get('cost')
                }), 200
            else:
                return jsonify({
                    "status": "error",
                    "message": result.get('error', 'Failed to generate announcement')
                }), 400
                
    except Exception as e:
        app.logger.error(f"Error in generate_smart_announcements: {e}")
        return jsonify({
            "status": "error",
            "message": f"Error generating smart announcements: {str(e)}"
        }), 500

@ai_insights_api.route('/api/ai/insights/executive-summary', methods=['POST'])
@token_required
@roles_required(['admin', 'hr_manager'])
def generate_executive_summary():
    """
    Generate AI-powered executive summary.
    
    Request Body:
    {
        "company_id": "required - company ID",
        "executive_type": "optional - monthly_summary, strategic_insights",
        "data": "required - executive data and metrics"
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"message": "Request body is required"}), 400
        
        company_id = data.get('company_id')
        if not company_id:
            return jsonify({"message": "Company ID is required"}), 400
        
        executive_type = data.get('executive_type', 'monthly_summary')
        executive_data = data.get('data', {})
        user_id = getattr(request, 'user_id', None)
        
        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404
        
        # Connect to the company database and generate executive summary
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            orchestrator = AIInsightsOrchestrator(session, company_id)
            
            result = orchestrator.generate_executive_summary(
                data=executive_data,
                executive_type=executive_type,
                user_id=user_id
            )
            
            if result.get('success'):
                return jsonify({
                    "status": "success",
                    "executive_summary": result['insight'],
                    "processing_time": result.get('processing_time'),
                    "tokens_used": result.get('tokens_used'),
                    "cost": result.get('cost')
                }), 200
            else:
                return jsonify({
                    "status": "error",
                    "message": result.get('error', 'Failed to generate executive summary')
                }), 400
                
    except Exception as e:
        app.logger.error(f"Error in generate_executive_summary: {e}")
        return jsonify({
            "status": "error",
            "message": f"Error generating executive summary: {str(e)}"
        }), 500

@ai_insights_api.route('/api/ai/insights/custom', methods=['POST'])
@token_required
@roles_required(['admin', 'hr_manager'])
def generate_custom_insight():
    """
    Generate custom AI insight with user-provided prompt.
    
    Request Body:
    {
        "company_id": "required - company ID",
        "prompt": "required - custom prompt for AI",
        "insight_type": "required - type identifier for the insight",
        "category": "optional - category of the insight (default: custom)"
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"message": "Request body is required"}), 400
        
        company_id = data.get('company_id')
        prompt = data.get('prompt')
        insight_type = data.get('insight_type')
        
        if not company_id:
            return jsonify({"message": "Company ID is required"}), 400
        if not prompt:
            return jsonify({"message": "Prompt is required"}), 400
        if not insight_type:
            return jsonify({"message": "Insight type is required"}), 400
        
        category = data.get('category', 'custom')
        user_id = getattr(request, 'user_id', None)
        
        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404
        
        # Connect to the company database and generate custom insight
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            orchestrator = AIInsightsOrchestrator(session, company_id)
            
            result = orchestrator.generate_custom_insight(
                prompt=prompt,
                insight_type=insight_type,
                category=category,
                user_id=user_id
            )
            
            if result.get('success'):
                return jsonify({
                    "status": "success",
                    "insight": result['insight'],
                    "processing_time": result.get('processing_time'),
                    "tokens_used": result.get('tokens_used'),
                    "cost": result.get('cost')
                }), 200
            else:
                return jsonify({
                    "status": "error",
                    "message": result.get('error', 'Failed to generate custom insight')
                }), 400
                
    except Exception as e:
        app.logger.error(f"Error in generate_custom_insight: {e}")
        return jsonify({
            "status": "error",
            "message": f"Error generating custom insight: {str(e)}"
        }), 500

@ai_insights_api.route('/api/ai/insights/recent', methods=['GET'])
@token_required
@roles_required(['admin', 'hr_manager', 'manager', 'employee'])
def get_recent_insights():
    """
    Get recent AI insights for a company.
    
    Query Parameters:
    - company_id: required - company ID
    - category: optional - filter by category
    - limit: optional - maximum number of insights (default: 10)
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"message": "Company ID is required"}), 400
        
        category = request.args.get('category')
        limit = int(request.args.get('limit', 10))
        
        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404
        
        # Connect to the company database and get recent insights
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            orchestrator = AIInsightsOrchestrator(session, company_id)
            
            insights = orchestrator.get_recent_insights(
                category=category,
                limit=limit
            )
            
            return jsonify({
                "status": "success",
                "insights": insights,
                "count": len(insights)
            }), 200
                
    except Exception as e:
        app.logger.error(f"Error in get_recent_insights: {e}")
        return jsonify({
            "status": "error",
            "message": f"Error getting recent insights: {str(e)}"
        }), 500

@ai_insights_api.route('/api/ai/insights/<insight_id>', methods=['GET'])
@token_required
@roles_required(['admin', 'hr_manager', 'manager', 'employee'])
def get_insight_by_id(insight_id):
    """
    Get a specific AI insight by ID.
    
    Query Parameters:
    - company_id: required - company ID
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"message": "Company ID is required"}), 400
        
        user_id = getattr(request, 'user_id', None)
        
        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404
        
        # Connect to the company database and get insight
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            orchestrator = AIInsightsOrchestrator(session, company_id)
            
            insight = orchestrator.get_insight_by_id(insight_id, user_id)
            
            if insight:
                return jsonify({
                    "status": "success",
                    "insight": insight
                }), 200
            else:
                return jsonify({
                    "status": "error",
                    "message": "Insight not found"
                }), 404
                
    except Exception as e:
        app.logger.error(f"Error in get_insight_by_id: {e}")
        return jsonify({
            "status": "error",
            "message": f"Error getting insight: {str(e)}"
        }), 500


def _fetch_attendance_analytics_data(company_id, period, date_str=None, start_date_str=None, end_date_str=None):
    """
    Fetch attendance analytics data by calling the existing analytics logic directly.
    This is more efficient than making HTTP calls.

    Args:
        company_id: Company ID
        period: Period type (daily, weekly, monthly, annual, custom)
        date_str: Reference date string
        start_date_str: Start date for custom period
        end_date_str: End date for custom period

    Returns:
        dict: Enhanced attendance data for AI analysis
    """
    try:
        # Import the analytics function directly
        from application.Routes.attendance1.attendance_api import _get_attendance_analytics_data

        # Call the existing analytics function directly
        analytics_result = _get_attendance_analytics_data(
            company_id=company_id,
            period=period,
            date_str=date_str,
            start_date_str=start_date_str,
            end_date_str=end_date_str
        )

        if not analytics_result:
            return None

        # Transform the analytics result into the format expected by AI
        enhanced_data = {
            'company_name': analytics_result.get('company_name', 'Company'),
            'period_info': analytics_result.get('period_info', {}),
            'company_info': analytics_result.get('company_info', {}),
            'attendance_data': analytics_result.get('attendance_data', {}),
            'target_audience': 'management',
            'tone': 'professional',
            'detail_level': 'comprehensive'
        }

        return enhanced_data

    except Exception as e:
        app.logger.error(f"Error fetching attendance analytics data: {e}")
        # Fallback to direct database query if the function doesn't exist yet
        return _fetch_attendance_analytics_data_direct(company_id, period, date_str, start_date_str, end_date_str)


def _fetch_attendance_analytics_data_direct(company_id, period, date_str=None, start_date_str=None, end_date_str=None):
    """
    Direct database query fallback for attendance analytics data.
    This reuses the core logic from the attendance statistics endpoint.
    """
    try:
        from application.Helpers.date_helper import DateHelper
        from application.Helpers.helper_methods import HelperMethods
        from application.Models.employees.employee import Employee
        from application.Models.employees.department import Department
        from application.Models.employees.attendance import Attendance
        from application.Models.company.company import Company
        from datetime import datetime
        from app import db_connection

        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return None

        # Parse reference date (default to today if not provided)
        reference_date = HelperMethods.parse_date(date_str) if date_str else datetime.now().date()

        # Determine date range based on period (reusing existing logic)
        if period == 'weekly':
            start_date, end_date = DateHelper.get_week_date_range(reference_date)
            period_description = f"Week of {start_date.strftime('%Y-%m-%d')}"
        elif period == 'monthly':
            start_date, end_date = DateHelper.get_month_date_range(reference_date)
            period_description = start_date.strftime('%B %Y')
        elif period == 'annual':
            start_date, end_date = DateHelper.get_year_date_range(reference_date)
            period_description = start_date.strftime('%Y')
        elif period == 'custom':
            start_date = HelperMethods.parse_date(start_date_str)
            end_date = HelperMethods.parse_date(end_date_str)
            if not start_date or not end_date:
                return None
            period_description = f"{start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}"
        else:  # daily
            start_date = end_date = reference_date
            period_description = reference_date.strftime('%Y-%m-%d')

        # Connect to the company database and fetch analytics data
        with db_connection.get_session(database_name) as session:
            # Call the core analytics calculation functions directly
            analytics_data = _calculate_attendance_statistics(
                session, company_id, start_date, end_date, period
            )

            if not analytics_data:
                return None

            # Build enhanced data structure for AI analysis
            enhanced_data = {
                'company_name': analytics_data.get('company_name', 'Company'),
                'period_info': {
                    'type': period,
                    'start_date': start_date.strftime('%Y-%m-%d'),
                    'end_date': end_date.strftime('%Y-%m-%d'),
                    'description': period_description,
                    'total_days': analytics_data.get('total_days', 0)
                },
                'company_info': analytics_data.get('company_info', {}),
                'attendance_data': analytics_data.get('attendance_data', {}),
                'target_audience': 'management',
                'tone': 'professional',
                'detail_level': 'comprehensive'
            }

            return enhanced_data

    except Exception as e:
        app.logger.error(f"Error in direct attendance analytics data fetch: {e}")
        return None


def _calculate_attendance_statistics(session, company_id, start_date, end_date, period):
    """
    Core attendance statistics calculation function.
    This extracts and reuses the logic from the attendance statistics endpoint.
    """
    try:
        from application.Models.employees.employee import Employee
        from application.Models.employees.department import Department
        from application.Models.employees.attendance import Attendance
        from application.Models.company.company import Company
        from application.Helpers.date_helper import DateHelper

        # Get company information
        company = session.query(Company).filter(Company.company_id == company_id).first()
        if not company:
            return None

        company_dict = company.to_dict()

        # Get all active employees
        all_employees = session.query(Employee).filter(Employee.status == 'active').count()

        # Get employee positions
        employee_positions = [emp.position for emp in session.query(Employee.position).filter(
            Employee.status == 'active', Employee.position.isnot(None)
        ).distinct().all()]

        # Get dates in range
        dates_in_range = DateHelper.get_dates_in_range(start_date, end_date)

        # Get attendance records for the date range (active employees only)
        from application.Models.employees.employee import Employee
        attendance_query = session.query(Attendance).join(
            Employee, Attendance.employee_id == Employee.employee_id
        ).filter(
            Attendance.date >= start_date,
            Attendance.date <= end_date,
            Employee.status == 'active'
        )

        # Count by status for the entire period
        present_count = attendance_query.filter(Attendance.status == 'present').count()
        on_leave_count = attendance_query.filter(Attendance.status == 'on leave').count()
        late_count = attendance_query.filter(Attendance.status == 'late').count()

        # Calculate total possible attendance based on employee shifts
        from application.Routes.attendance1.attendance_api import _calculate_total_possible_attendance
        all_employees_list = session.query(Employee).filter(Employee.status == 'active').all()
        total_possible_attendance = _calculate_total_possible_attendance(session, all_employees_list, start_date, end_date)

        # Calculate absent count
        absent_count = total_possible_attendance - present_count - on_leave_count - late_count

        # Calculate attendance percentage (include late as present)
        attendance_percentage = 0
        if total_possible_attendance > 0:
            total_attended = present_count + late_count
            attendance_percentage = round((total_attended / total_possible_attendance) * 100, 2)

        # Get department statistics (reusing existing logic)
        departments = session.query(Department).all()
        department_stats = []

        for dept in departments:
            dept_employees_count = session.query(Employee).filter(
                Employee.department_id == dept.department_id,
                Employee.status == 'active'
            ).count()

            if dept_employees_count > 0:
                # Calculate department attendance (simplified version)
                dept_employee_ids = [emp.employee_id for emp in session.query(Employee.employee_id).filter(
                    Employee.department_id == dept.department_id,
                    Employee.status == 'active'
                ).all()]

                dept_present = 0
                dept_on_leave = 0
                dept_late = 0

                for current_date in dates_in_range:
                    day_records = session.query(Attendance).join(
                        Employee, Attendance.employee_id == Employee.employee_id
                    ).filter(
                        Attendance.employee_id.in_(dept_employee_ids),
                        Attendance.date == current_date,
                        Employee.status == 'active'
                    ).all()

                    day_attendance_map = {record.employee_id: record.status for record in day_records}
                    dept_present += sum(1 for status in day_attendance_map.values() if status == 'present')
                    dept_on_leave += sum(1 for status in day_attendance_map.values() if status == 'on leave')
                    dept_late += sum(1 for status in day_attendance_map.values() if status == 'late')

                # Calculate shift-aware total possible attendance for department
                dept_employees_list = session.query(Employee).filter(
                    Employee.department_id == dept.department_id,
                    Employee.status == 'active'
                ).all()
                dept_total_possible = _calculate_total_possible_attendance(session, dept_employees_list, start_date, end_date)
                dept_absent = dept_total_possible - dept_present - dept_on_leave - dept_late
                # Include late as attended for department average
                dept_attended = dept_present + dept_late
                dept_avg_attendance = round(dept_attended / dept_total_possible * 100, 2) if dept_total_possible > 0 else 0

                department_stats.append({
                    "department_id": str(dept.department_id),
                    "department_name": dept.name,
                    "employee_count": dept_employees_count,
                    "present_count": dept_present,
                    "absent_count": dept_absent,
                    "on_leave_count": dept_on_leave,
                    "late_count": dept_late,
                    "avg_attendance_percentage": dept_avg_attendance
                })

        # Return structured analytics data
        return {
            'company_name': company_dict.get('name', 'Company'),
            'total_days': len(dates_in_range),
            'company_info': {
                'country': company_dict.get('country', {}),
                'employee_positions': employee_positions,
                'total_workforce': all_employees,
                'departments': [dept.name for dept in departments]
            },
            'attendance_data': {
                'summary': {
                    'total_employees': all_employees,
                    'present_count': present_count,
                    'absent_count': absent_count,
                    'on_leave_count': on_leave_count,
                    'late_count': late_count,
                    'attendance_percentage': attendance_percentage,
                    'total_possible_attendance': total_possible_attendance
                },
                'department_statistics': department_stats
            }
        }

    except Exception as e:
        app.logger.error(f"Error calculating attendance statistics: {e}")
        return None
