from flask import Blueprint, request, jsonify
from application.Models.apiclient.api_client import APIClient

api_auth_bp = Blueprint("api_auth", __name__)

@api_auth_bp.route("/api/auth/token", methods=["POST"])
def get_api_token():
    """Get an API token for a client"""
    data = request.get_json(silent=True) or {}
    client_id = data.get("client_id")
    client_secret = data.get("client_secret")

    if not client_id or not client_secret:
        # Missing required fields
        return jsonify({
            "error": "Missing client_id or client_secret"
        }), 400

    try:
        token = APIClient.authenticate(client_id, client_secret)
        # If we get here, credentials are valid
        return jsonify({
            "access_token": token,
            "token_type": "Bearer",
            "expires_in": 3600
        }), 200

    except ValueError as e:
        # Credentials are invalid
        return jsonify({
            "error": "Invalid credentials",
            "message": str(e)
        }), 401

    except Exception as e:
        # Any unexpected error
        return jsonify({
            "error": "Internal Server Error",
            "message": str(e)
        }), 500