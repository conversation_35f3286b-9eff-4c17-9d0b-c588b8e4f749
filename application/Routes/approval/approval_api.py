from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from application.Models.approval import (
    ApprovableEntity,
    ApprovalLevel,
    ApprovalFlow,
    ApprovalWorkflow,
    ApprovalRecord,
    ApprovalAssignment
)
from application.Models.company import Company
from application.Models.Msg import Msg
import jsons
from datetime import datetime
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.Helpers.helper_methods import HelperMethods

approval_api = Blueprint('approval_api', __name__)

# ==================== Approvable Entities API ====================

@approval_api.route('/api/approval/entities', methods=['GET'])
@token_required
def get_approvable_entities():
    """
    Get all approvable entities.

    Query Parameters:
    - company_id: Required. The ID of the company.
    - active_only: Optional. Whether to return only active entities. Default: true.
    """
    company_id = request.args.get('company_id')
    active_only = request.args.get('active_only', 'true').lower() == 'true'

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        entities = ApprovableEntity.get_all_entities(session, active_only)
        entities_data = [entity.to_dict() for entity in entities]

        return jsons.dump(Msg.success().add("entities", entities_data))

@approval_api.route('/api/approval/entities/<entity_id>', methods=['GET'])
@token_required
def get_approvable_entity(entity_id):
    """
    Get a specific approvable entity by ID.

    Path Parameters:
    - entity_id: Required. The ID of the approvable entity to retrieve.

    Query Parameters:
    - company_id: Required. The ID of the company.
    """
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        entity = ApprovableEntity.get_entity_by_id(session, entity_id)

        if not entity:
            return jsonify({"message": f"Approvable entity with ID {entity_id} not found"}), 404

        return jsons.dump(Msg.success().add("entity", entity.to_dict()))

@approval_api.route('/api/approval/entities', methods=['POST'])
@token_required
@roles_required('admin')
def create_approvable_entity():
    """
    Create a new approvable entity.

    Request Body:
    - company_id: Required. The ID of the company.
    - name: Required. The name of the approvable entity.
    - code: Required. A unique code for the approvable entity.
    - description: Optional. A description of the approvable entity.
    - is_active: Optional. Whether the approvable entity is active. Default: true.
    """
    data = request.get_json()
    company_id = data.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Validate required fields
    required_fields = ['name', 'code']
    for field in required_fields:
        if not data.get(field):
            return jsonify({"message": f"{field.replace('_', ' ').title()} is required"}), 400

    # Prepare entity data
    entity_data = {
        'name': data.get('name'),
        'code': data.get('code'),
        'description': data.get('description'),
        'is_active': data.get('is_active', True)
    }

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if an entity with the same code already exists
        existing_entity = ApprovableEntity.get_entity_by_code(session, entity_data['code'])
        if existing_entity:
            return jsonify({
                "message": f"An approvable entity with the code '{entity_data['code']}' already exists"
            }), 409  # 409 Conflict

        # Create the entity
        entity = ApprovableEntity.create_entity(session, **entity_data)

        if not entity:
            return jsonify({"message": "Failed to create approvable entity"}), 500

        return jsons.dump(Msg.success().add("entity", entity.to_dict())), 201

@approval_api.route('/api/approval/entities/<entity_id>', methods=['PATCH'])
@token_required
@roles_required('admin')
def update_approvable_entity(entity_id):
    """
    Update an existing approvable entity.

    Path Parameters:
    - entity_id: Required. The ID of the approvable entity to update.

    Request Body:
    - company_id: Required. The ID of the company.
    - name: Optional. The name of the approvable entity.
    - description: Optional. A description of the approvable entity.
    - is_active: Optional. Whether the approvable entity is active.
    """
    data = request.get_json()
    company_id = data.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Prepare entity data (only include fields that are provided)
    entity_data = {}
    for field in ['name', 'description', 'is_active']:
        if field in data:
            entity_data[field] = data.get(field)

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if the entity exists
        entity = ApprovableEntity.get_entity_by_id(session, entity_id)
        if not entity:
            return jsonify({"message": f"Approvable entity with ID {entity_id} not found"}), 404

        # Update the entity
        updated_entity = ApprovableEntity.update_entity(session, entity_id, **entity_data)

        if not updated_entity:
            return jsonify({"message": "Failed to update approvable entity"}), 500

        return jsons.dump(Msg.success().add("entity", updated_entity.to_dict()))

@approval_api.route('/api/approval/entities/<entity_id>', methods=['DELETE'])
@token_required
@roles_required('admin')
def delete_approvable_entity(entity_id):
    """
    Delete an approvable entity.

    Path Parameters:
    - entity_id: Required. The ID of the approvable entity to delete.

    Query Parameters:
    - company_id: Required. The ID of the company.
    """
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if the entity exists
        entity = ApprovableEntity.get_entity_by_id(session, entity_id)
        if not entity:
            return jsonify({"message": f"Approvable entity with ID {entity_id} not found"}), 404

        # Delete the entity
        success = ApprovableEntity.delete_entity(session, entity_id)

        if not success:
            return jsonify({"message": "Failed to delete approvable entity"}), 500

        return jsonify({"message": "Approvable entity deleted successfully"}), 200

# ==================== Approval Levels API ====================

@approval_api.route('/api/approval/levels', methods=['GET'])
@token_required
def get_approval_levels():
    """
    Get all approval levels.

    Query Parameters:
    - company_id: Required. The ID of the company.
    - active_only: Optional. Whether to return only active levels. Default: true.
    """
    company_id = request.args.get('company_id')
    active_only = request.args.get('active_only', 'true').lower() == 'true'

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        levels = ApprovalLevel.get_all_levels(session, active_only)
        levels_data = [level.to_dict() for level in levels]

        return jsons.dump(Msg.success().add("levels", levels_data))

@approval_api.route('/api/approval/levels/<level_id>', methods=['GET'])
@token_required
def get_approval_level(level_id):
    """
    Get a specific approval level by ID.

    Path Parameters:
    - level_id: Required. The ID of the approval level to retrieve.

    Query Parameters:
    - company_id: Required. The ID of the company.
    """
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        level = ApprovalLevel.get_level_by_id(session, level_id)

        if not level:
            return jsonify({"message": f"Approval level with ID {level_id} not found"}), 404

        return jsons.dump(Msg.success().add("level", level.to_dict()))

@approval_api.route('/api/approval/levels', methods=['POST'])
@token_required
@roles_required('admin')
def create_approval_level():
    """
    Create a new approval level.

    Request Body:
    - company_id: Required. The ID of the company.
    - name: Required. The name of the approval level.
    - description: Optional. A description of the approval level.
    - is_active: Optional. Whether the approval level is active. Default: true.
    """
    data = request.get_json()
    company_id = data.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Validate required fields
    required_fields = ['name']
    for field in required_fields:
        if not data.get(field):
            return jsonify({"message": f"{field.replace('_', ' ').title()} is required"}), 400

    # Prepare level data
    level_data = {
        'name': data.get('name'),
        'description': data.get('description'),
        'is_active': data.get('is_active', True)
    }

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Create the level
        level = ApprovalLevel.create_level(session, **level_data)

        if not level:
            return jsonify({"message": "Failed to create approval level"}), 500

        return jsons.dump(Msg.success().add("level", level.to_dict())), 201

@approval_api.route('/api/approval/levels/<level_id>', methods=['PATCH'])
@token_required
@roles_required('admin')
def update_approval_level(level_id):
    """
    Update an existing approval level.

    Path Parameters:
    - level_id: Required. The ID of the approval level to update.

    Request Body:
    - company_id: Required. The ID of the company.
    - name: Optional. The name of the approval level.
    - description: Optional. A description of the approval level.
    - is_active: Optional. Whether the approval level is active.
    """
    data = request.get_json()
    company_id = data.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Prepare level data (only include fields that are provided)
    level_data = {}
    for field in ['name', 'description', 'is_active']:
        if field in data:
            level_data[field] = data.get(field)

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if the level exists
        level = ApprovalLevel.get_level_by_id(session, level_id)
        if not level:
            return jsonify({"message": f"Approval level with ID {level_id} not found"}), 404

        # Update the level
        updated_level = ApprovalLevel.update_level(session, level_id, **level_data)

        if not updated_level:
            return jsonify({"message": "Failed to update approval level"}), 500

        return jsons.dump(Msg.success().add("level", updated_level.to_dict()))

@approval_api.route('/api/approval/levels/<level_id>', methods=['DELETE'])
@token_required
@roles_required('admin')
def delete_approval_level(level_id):
    """
    Delete an approval level.

    Path Parameters:
    - level_id: Required. The ID of the approval level to delete.

    Query Parameters:
    - company_id: Required. The ID of the company.
    """
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if the level exists
        level = ApprovalLevel.get_level_by_id(session, level_id)
        if not level:
            return jsonify({"message": f"Approval level with ID {level_id} not found"}), 404

        # Delete the level
        success = ApprovalLevel.delete_level(session, level_id)

        if not success:
            return jsonify({"message": "Failed to delete approval level"}), 500

        return jsonify({"message": "Approval level deleted successfully"}), 200

# ==================== Approval Flows API ====================

@approval_api.route('/api/approval/flows', methods=['GET'])
@token_required
def get_approval_flows():
    """
    Get approval flows.

    Query Parameters:
    - company_id: Required. The ID of the company.
    - entity_id: Optional. Filter by entity ID.
    - active_only: Optional. Whether to return only active flows. Default: true.
    """
    company_id = request.args.get('company_id')
    entity_id = request.args.get('entity_id')
    active_only = request.args.get('active_only', 'true').lower() == 'true'

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        if entity_id:
            flows = ApprovalFlow.get_flows_by_entity(session, entity_id, active_only)
        else:
            flows = session.query(ApprovalFlow)
            if active_only:
                flows = flows.filter_by(is_active=True)
            flows = flows.all()

        flows_data = [flow.to_dict() for flow in flows]

        return jsons.dump(Msg.success().add("flows", flows_data))

@approval_api.route('/api/approval/flows/<flow_id>', methods=['GET'])
@token_required
def get_approval_flow(flow_id):
    """
    Get a specific approval flow by ID.

    Path Parameters:
    - flow_id: Required. The ID of the approval flow to retrieve.

    Query Parameters:
    - company_id: Required. The ID of the company.
    """
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        flow = ApprovalFlow.get_flow_by_id(session, flow_id)

        if not flow:
            return jsonify({"message": f"Approval flow with ID {flow_id} not found"}), 404

        return jsons.dump(Msg.success().add("flow", flow.to_dict()))

@approval_api.route('/api/approval/flows', methods=['POST'])
@token_required
@roles_required('admin')
def create_approval_flow():
    """
    Create a new approval flow.

    Request Body:
    - company_id: Required. The ID of the company.
    - entity_id: Required. The ID of the approvable entity.
    - level_id: Required. The ID of the approval level.
    - order: Required. The order in the approval chain.
    - is_active: Optional. Whether the approval flow is active. Default: true.
    """
    data = request.get_json()
    company_id = data.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Validate required fields
    required_fields = ['entity_id', 'level_id', 'order']
    for field in required_fields:
        if not data.get(field):
            return jsonify({"message": f"{field.replace('_', ' ').title()} is required"}), 400

    # Prepare flow data
    flow_data = {
        'entity_id': data.get('entity_id'),
        'level_id': data.get('level_id'),
        'order': data.get('order'),
        'is_active': data.get('is_active', True)
    }

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if the entity exists
        entity = ApprovableEntity.get_entity_by_id(session, flow_data['entity_id'])
        if not entity:
            return jsonify({"message": f"Approvable entity with ID {flow_data['entity_id']} not found"}), 404

        # Check if the level exists
        level = ApprovalLevel.get_level_by_id(session, flow_data['level_id'])
        if not level:
            return jsonify({"message": f"Approval level with ID {flow_data['level_id']} not found"}), 404

        # Create the flow
        flow, error = ApprovalFlow.create_flow(session, **flow_data)

        if not flow:
            return jsonify({"message": error or "Failed to create approval flow"}), 400

        return jsons.dump(Msg.success().add("flow", flow.to_dict())), 201

@approval_api.route('/api/approval/flows/<flow_id>', methods=['PATCH'])
@token_required
@roles_required('admin')
def update_approval_flow(flow_id):
    """
    Update an existing approval flow.

    Path Parameters:
    - flow_id: Required. The ID of the approval flow to update.

    Request Body:
    - company_id: Required. The ID of the company.
    - level_id: Optional. The ID of the approval level.
    - order: Optional. The order in the approval chain.
    - is_active: Optional. Whether the approval flow is active.
    """
    data = request.get_json()
    company_id = data.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Prepare flow data (only include fields that are provided)
    flow_data = {}
    for field in ['level_id', 'order', 'is_active']:
        if field in data:
            flow_data[field] = data.get(field)

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if the flow exists
        flow = ApprovalFlow.get_flow_by_id(session, flow_id)
        if not flow:
            return jsonify({"message": f"Approval flow with ID {flow_id} not found"}), 404

        # If changing the level, check if it exists
        if 'level_id' in flow_data:
            level = ApprovalLevel.get_level_by_id(session, flow_data['level_id'])
            if not level:
                return jsonify({"message": f"Approval level with ID {flow_data['level_id']} not found"}), 404

        # Update the flow
        updated_flow, error = ApprovalFlow.update_flow(session, flow_id, **flow_data)

        if not updated_flow:
            return jsonify({"message": error or "Failed to update approval flow"}), 400

        return jsons.dump(Msg.success().add("flow", updated_flow.to_dict()))

@approval_api.route('/api/approval/flows/<flow_id>', methods=['DELETE'])
@token_required
@roles_required('admin')
def delete_approval_flow(flow_id):
    """
    Delete an approval flow.

    Path Parameters:
    - flow_id: Required. The ID of the approval flow to delete.

    Query Parameters:
    - company_id: Required. The ID of the company.
    """
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if the flow exists
        flow = ApprovalFlow.get_flow_by_id(session, flow_id)
        if not flow:
            return jsonify({"message": f"Approval flow with ID {flow_id} not found"}), 404

        # Delete the flow
        success, error = ApprovalFlow.delete_flow(session, flow_id)

        if not success:
            return jsonify({"message": error or "Failed to delete approval flow"}), 400

        return jsonify({"message": "Approval flow deleted successfully"}), 200

# ==================== Approval Assignments API ====================

@approval_api.route('/api/approval/assignments', methods=['GET'])
@token_required
def get_approval_assignments():
    """
    Get approval assignments.

    Query Parameters:
    - company_id: Required. The ID of the company.
    - level_id: Optional. Filter by level ID.
    - approver_id: Optional. Filter by approver ID.
    - entity_id: Optional. Filter by entity ID.
    - active_only: Optional. Whether to return only active assignments. Default: true.
    """
    company_id = request.args.get('company_id')
    level_id = request.args.get('level_id')
    approver_id = request.args.get('approver_id')
    entity_id = request.args.get('entity_id')
    active_only = request.args.get('active_only', 'true').lower() == 'true'

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        if level_id:
            assignments = ApprovalAssignment.get_assignments_by_level(session, level_id, active_only)
        elif approver_id:
            assignments = ApprovalAssignment.get_assignments_by_approver(session, approver_id, active_only)
        elif entity_id:
            assignments = ApprovalAssignment.get_assignments_by_entity(session, entity_id, active_only)
        else:
            assignments = session.query(ApprovalAssignment)
            if active_only:
                assignments = assignments.filter_by(is_active=True)
            assignments = assignments.all()

        assignments_data = [assignment.to_dict() for assignment in assignments]

        return jsons.dump(Msg.success().add("assignments", assignments_data))

@approval_api.route('/api/approval/assignments/<assignment_id>', methods=['GET'])
@token_required
def get_approval_assignment(assignment_id):
    """
    Get a specific approval assignment by ID.

    Path Parameters:
    - assignment_id: Required. The ID of the approval assignment to retrieve.

    Query Parameters:
    - company_id: Required. The ID of the company.
    """
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        assignment = ApprovalAssignment.get_assignment_by_id(session, assignment_id)

        if not assignment:
            return jsonify({"message": f"Approval assignment with ID {assignment_id} not found"}), 404

        return jsons.dump(Msg.success().add("assignment", assignment.to_dict()))

@approval_api.route('/api/approval/assignments', methods=['POST'])
@token_required
@roles_required('admin')
def create_approval_assignment():
    """
    Create a new approval assignment.

    Request Body:
    - company_id: Required. The ID of the company.
    - level_id: Required. The ID of the approval level.
    - approver_id: Required. The ID of the approver.
    - entity_id: Optional. The ID of the approvable entity (if specific to an entity type).
    - is_active: Optional. Whether the approval assignment is active. Default: true.
    """
    data = request.get_json()
    company_id = data.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Validate required fields
    required_fields = ['level_id', 'approver_id']
    for field in required_fields:
        if not data.get(field):
            return jsonify({"message": f"{field.replace('_', ' ').title()} is required"}), 400

    # Prepare assignment data
    assignment_data = {
        'level_id': data.get('level_id'),
        'approver_id': data.get('approver_id'),
        'entity_id': data.get('entity_id'),
        'is_active': data.get('is_active', True)
    }

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if the level exists
        level = ApprovalLevel.get_level_by_id(session, assignment_data['level_id'])
        if not level:
            return jsonify({"message": f"Approval level with ID {assignment_data['level_id']} not found"}), 404

        # If entity_id is provided, check if it exists
        if assignment_data.get('entity_id'):
            entity = ApprovableEntity.get_entity_by_id(session, assignment_data['entity_id'])
            if not entity:
                return jsonify({"message": f"Approvable entity with ID {assignment_data['entity_id']} not found"}), 404

        # Create the assignment
        assignment, error = ApprovalAssignment.create_assignment(session, **assignment_data)

        if not assignment:
            return jsonify({"message": error or "Failed to create approval assignment"}), 400

        return jsons.dump(Msg.success().add("assignment", assignment.to_dict())), 201

@approval_api.route('/api/approval/assignments/<assignment_id>', methods=['PATCH'])
@token_required
@roles_required('admin')
def update_approval_assignment(assignment_id):
    """
    Update an existing approval assignment.

    Path Parameters:
    - assignment_id: Required. The ID of the approval assignment to update.

    Request Body:
    - company_id: Required. The ID of the company.
    - level_id: Optional. The ID of the approval level.
    - approver_id: Optional. The ID of the approver.
    - entity_id: Optional. The ID of the approvable entity.
    - is_active: Optional. Whether the approval assignment is active.
    """
    data = request.get_json()
    company_id = data.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Prepare assignment data (only include fields that are provided)
    assignment_data = {}
    for field in ['level_id', 'approver_id', 'entity_id', 'is_active']:
        if field in data:
            assignment_data[field] = data.get(field)

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if the assignment exists
        assignment = ApprovalAssignment.get_assignment_by_id(session, assignment_id)
        if not assignment:
            return jsonify({"message": f"Approval assignment with ID {assignment_id} not found"}), 404

        # If changing the level, check if it exists
        if 'level_id' in assignment_data:
            level = ApprovalLevel.get_level_by_id(session, assignment_data['level_id'])
            if not level:
                return jsonify({"message": f"Approval level with ID {assignment_data['level_id']} not found"}), 404

        # If changing the entity, check if it exists
        if 'entity_id' in assignment_data and assignment_data['entity_id']:
            entity = ApprovableEntity.get_entity_by_id(session, assignment_data['entity_id'])
            if not entity:
                return jsonify({"message": f"Approvable entity with ID {assignment_data['entity_id']} not found"}), 404

        # Update the assignment
        updated_assignment, error = ApprovalAssignment.update_assignment(session, assignment_id, **assignment_data)

        if not updated_assignment:
            return jsonify({"message": error or "Failed to update approval assignment"}), 400

        return jsons.dump(Msg.success().add("assignment", updated_assignment.to_dict()))

@approval_api.route('/api/approval/assignments/<assignment_id>', methods=['DELETE'])
@token_required
@roles_required('admin')
def delete_approval_assignment(assignment_id):
    """
    Delete an approval assignment.

    Path Parameters:
    - assignment_id: Required. The ID of the approval assignment to delete.

    Query Parameters:
    - company_id: Required. The ID of the company.
    """
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if the assignment exists
        assignment = ApprovalAssignment.get_assignment_by_id(session, assignment_id)
        if not assignment:
            return jsonify({"message": f"Approval assignment with ID {assignment_id} not found"}), 404

        # Delete the assignment
        success, error = ApprovalAssignment.delete_assignment(session, assignment_id)

        if not success:
            return jsonify({"message": error or "Failed to delete approval assignment"}), 400

        return jsonify({"message": "Approval assignment deleted successfully"}), 200

# ==================== Approval Workflows API ====================

@approval_api.route('/api/approval/workflows', methods=['GET'])
@token_required
def get_approval_workflows():
    """
    Get approval workflows.

    Query Parameters:
    - company_id: Required. The ID of the company.
    - entity_type: Optional. Filter by entity type.
    - entity_id: Optional. Filter by entity ID.
    - status: Optional. Filter by status (pending, approved, rejected).
    """
    company_id = request.args.get('company_id')
    entity_type = request.args.get('entity_type')
    entity_id = request.args.get('entity_id')
    status = request.args.get('status')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Build the query
        query = session.query(ApprovalWorkflow)

        if entity_type:
            query = query.filter(ApprovalWorkflow.entity_type == entity_type)

        if entity_id:
            query = query.filter(ApprovalWorkflow.entity_id == entity_id)

        if status:
            query = query.filter(ApprovalWorkflow.status == status)

        # Execute the query
        workflows = query.all()

        # Convert to dictionaries
        workflows_data = [workflow.to_dict() for workflow in workflows]

        return jsons.dump(Msg.success().add("workflows", workflows_data))

@approval_api.route('/api/approval/workflows/<workflow_id>', methods=['GET'])
@token_required
def get_approval_workflow(workflow_id):
    """
    Get a specific approval workflow by ID.

    Path Parameters:
    - workflow_id: Required. The ID of the approval workflow to retrieve.

    Query Parameters:
    - company_id: Required. The ID of the company.
    """
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        workflow = ApprovalWorkflow.get_workflow_by_id(session, workflow_id)

        if not workflow:
            return jsonify({"message": f"Approval workflow with ID {workflow_id} not found"}), 404

        # Get approval records for this workflow
        records = ApprovalRecord.get_records_by_workflow(session, workflow_id)
        records_data = [record.to_dict() for record in records]

        workflow_data = workflow.to_dict()
        workflow_data['records'] = records_data

        return jsons.dump(Msg.success().add("workflow", workflow_data))

@approval_api.route('/api/approval/workflows/<workflow_id>/approve', methods=['POST'])
@token_required
def approve_workflow(workflow_id):
    """
    Approve a workflow at the current level and move to the next level.

    Path Parameters:
    - workflow_id: Required. The ID of the approval workflow.

    Request Body:
    - company_id: Required. The ID of the company.
    - comments: Optional. Comments for the approval.
    """
    data = request.get_json()
    company_id = data.get('company_id')
    comments = data.get('comments')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Get the workflow
        workflow = ApprovalWorkflow.get_workflow_by_id(session, workflow_id)
        if not workflow:
            return jsonify({"message": f"Approval workflow with ID {workflow_id} not found"}), 404

        if workflow.status != 'pending':
            return jsonify({"message": f"Cannot approve workflow because it is {workflow.status}"}), 400

        # Get the current user's ID for the approver_id field
        current_user_id = g.user.get('user_id')

        # Check if the user has permission to approve at this level
        if workflow.current_flow_id:
            entity = ApprovableEntity.get_entity_by_code(session, workflow.entity_type)
            if not entity:
                return jsonify({"message": f"Approvable entity with code {workflow.entity_type} not found"}), 404

            can_approve = ApprovalAssignment.can_approve(
                session,
                current_user_id,
                workflow.current_flow.level_id,
                entity.entity_id
            )

            if not can_approve:
                return jsonify({"message": "You do not have permission to approve at this level"}), 403

        # Approve the workflow
        updated_workflow, error = ApprovalWorkflow.approve_current_level(session, workflow_id, current_user_id, comments)

        if not updated_workflow:
            return jsonify({"message": error or "Failed to approve workflow"}), 400

        return jsons.dump(Msg.success().add("workflow", updated_workflow.to_dict()))

@approval_api.route('/api/approval/workflows/<workflow_id>/reject', methods=['POST'])
@token_required
def reject_workflow(workflow_id):
    """
    Reject a workflow at the current level.

    Path Parameters:
    - workflow_id: Required. The ID of the approval workflow.

    Request Body:
    - company_id: Required. The ID of the company.
    - rejection_reason: Required. The reason for rejecting the workflow.
    """
    data = request.get_json()
    company_id = data.get('company_id')
    rejection_reason = data.get('rejection_reason')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    if not rejection_reason:
        return jsonify({"message": "Rejection reason is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Get the workflow
        workflow = ApprovalWorkflow.get_workflow_by_id(session, workflow_id)
        if not workflow:
            return jsonify({"message": f"Approval workflow with ID {workflow_id} not found"}), 404

        if workflow.status != 'pending':
            return jsonify({"message": f"Cannot reject workflow because it is {workflow.status}"}), 400

        # Get the current user's ID for the approver_id field
        current_user_id = g.user.get('user_id')

        # Check if the user has permission to reject at this level
        if workflow.current_flow_id:
            entity = ApprovableEntity.get_entity_by_code(session, workflow.entity_type)
            if not entity:
                return jsonify({"message": f"Approvable entity with code {workflow.entity_type} not found"}), 404

            can_approve = ApprovalAssignment.can_approve(
                session,
                current_user_id,
                workflow.current_flow.level_id,
                entity.entity_id
            )

            if not can_approve:
                return jsonify({"message": "You do not have permission to reject at this level"}), 403

        # Reject the workflow
        updated_workflow, error = ApprovalWorkflow.reject_workflow(session, workflow_id, current_user_id, rejection_reason)

        if not updated_workflow:
            return jsonify({"message": error or "Failed to reject workflow"}), 400

        return jsons.dump(Msg.success().add("workflow", updated_workflow.to_dict()))
