from application.Models.employees import Employee, Attendance
from application.Models.employees.department import Department
from application.Models.company import Company
from datetime import datetime, timedelta, date
from sqlalchemy import func, cast, Date
import uuid
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from flask import Blueprint, request, jsonify, current_app as app, make_response
from application.Helpers.helper_methods import HelperMethods
from application.Helpers.date_helper import DateHelper
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, PageBreak, KeepTogether
from reportlab.lib.units import inch
import io

# AI Integration (optional import to avoid breaking existing functionality)
try:
    from application.Services.ai.ai_insights_orchestrator import AIInsightsOrchestrator
    AI_AVAILABLE = True
except ImportError:
    AI_AVAILABLE = False
    app.logger.warning("AI services not available - AI insights will be disabled")

attendance_api = Blueprint('attendance_api', __name__)

# Attendance CRUD operations
@attendance_api.route('/api/attendance', methods=['GET'])
@token_required
def get_attendance_records():
    """
    Get attendance records with optional filtering and pagination.
    """
    company_id = request.args.get('company_id')
    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Get query parameters
    employee_id = request.args.get('employee_id')
    date_str = request.args.get('date')
    start_date_str = request.args.get('start_date')
    end_date_str = request.args.get('end_date')
    department_id = request.args.get('department_id')

    # Pagination parameters
    page = int(request.args.get('page', 1))
    per_page = min(int(request.args.get('per_page', 50)), 500)
    include_inactive = request.args.get('include_inactive', 'false').lower() == 'true'

    # Validate pagination
    if page < 1:
        return jsonify({"message": "Page number must be 1 or greater"}), 400
    if per_page < 1:
        return jsonify({"message": "Per page must be 1 or greater"}), 400

    # Parse dates
    date_value = HelperMethods.parse_date(date_str) if date_str else None
    start_date = HelperMethods.parse_date(start_date_str) if start_date_str else None
    end_date = HelperMethods.parse_date(end_date_str) if end_date_str else None

    if start_date and end_date and start_date > end_date:
        return jsonify({"message": "start_date cannot be after end_date"}), 400

    from app import db_connection
    with db_connection.get_session(database_name) as session:
        try:
            from application.Models.employees.employee import Employee

            if not include_inactive:
                query = session.query(Attendance).join(
                    Employee, Attendance.employee_id == Employee.employee_id
                ).filter(Employee.status == 'active')
            else:
                query = session.query(Attendance)

            if employee_id:
                try:
                    if isinstance(employee_id, str):
                        employee_id = uuid.UUID(employee_id)
                    query = query.filter(Attendance.employee_id == employee_id)
                except ValueError:
                    return jsonify({"message": "Invalid employee_id format"}), 400

            if date_value:
                query = query.filter(Attendance.date == date_value)
            elif start_date and end_date:
                query = query.filter(Attendance.date >= start_date, Attendance.date <= end_date)
            elif start_date:
                query = query.filter(Attendance.date >= start_date)
            elif end_date:
                query = query.filter(Attendance.date <= end_date)

            if department_id:
                if not include_inactive:
                    query = query.filter(Employee.department_id == uuid.UUID(department_id))
                else:
                    query = query.join(Employee, Attendance.employee_id == Employee.employee_id)\
                                 .filter(Employee.department_id == uuid.UUID(department_id))

            # Total records
            total_records = query.count()

            # Apply pagination
            offset = (page - 1) * per_page
            attendance_records = query.order_by(
                Attendance.date.desc(),
                Attendance.check_in_time.desc()
            ).offset(offset).limit(per_page).all()

            attendance_data = [record.to_dict() for record in attendance_records]

            total_pages = (total_records + per_page - 1) // per_page
            has_next = page < total_pages
            has_prev = page > 1

            return jsonify({
                "status": "success",
                "attendance_records": attendance_data,  # same key as before
                "pagination": {
                    "page": page,
                    "per_page": per_page,
                    "total_records": total_records,
                    "total_pages": total_pages,
                    "has_next": has_next,
                    "has_prev": has_prev,
                    "count": total_records,  # added
                    "next": page + 1 if has_next else None,  # added
                    "previous": page - 1 if has_prev else None  # added
                },
                "filters_applied": {
                    "employee_id": employee_id,
                    "date": date_str,
                    "start_date": start_date_str,
                    "end_date": end_date_str,
                    "include_inactive": include_inactive,
                    "department_id": department_id
                }
            }), 200

        except Exception as e:
            app.logger.error(f"Error getting attendance records: {e}")
            return jsonify({"message": "Internal server error"}), 500


@attendance_api.route('/api/attendance/download/pdf', methods=['GET'])
@token_required
def download_attendance_pdf():
    """
    Download attendance records as PDF.

    Query Parameters:
    - company_id: Required. The ID of the company
    - employee_id: Optional. Filter by specific employee
    - start_date: Optional. Start date for date range filter (YYYY-MM-DD)
    - end_date: Optional. End date for date range filter (YYYY-MM-DD)
    - include_inactive: Optional. Include inactive employees (true/false, default: false)
    """
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)

    # get company details:
    try:
        company = Company.query.filter_by(company_id=company_id).first()
    except Exception as e:
        app.logger.error(f"Error getting company details: {e}")
        return jsonify({"message": "company not found"}), 404
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Get query parameters
    employee_id = request.args.get('employee_id')
    start_date_str = request.args.get('start_date')
    end_date_str = request.args.get('end_date')
    include_inactive = request.args.get('include_inactive', 'false').lower() == 'true'

    # Parse dates
    start_date = HelperMethods.parse_date(start_date_str) if start_date_str else None
    end_date = HelperMethods.parse_date(end_date_str) if end_date_str else None

    # Default to current month if no dates provided
    if not start_date and not end_date:
        today = date.today()
        start_date = today.replace(day=1)
        # Get last day of current month
        if today.month == 12:
            end_date = today.replace(year=today.year + 1, month=1, day=1) - timedelta(days=1)
        else:
            end_date = today.replace(month=today.month + 1, day=1) - timedelta(days=1)

    # Validate date range
    if start_date and end_date and start_date > end_date:
        return jsonify({"message": "start_date cannot be after end_date"}), 400

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        try:
            
            # Build the query for attendance records
            from application.Models.employees.employee import Employee

            if not include_inactive:
                query = session.query(Attendance).join(
                    Employee, Attendance.employee_id == Employee.employee_id
                ).filter(Employee.status == 'active')
            else:
                query = session.query(Attendance)

            # Apply filters
            if employee_id:
                try:
                    if isinstance(employee_id, str):
                        employee_id = uuid.UUID(employee_id)
                    query = query.filter(Attendance.employee_id == employee_id)
                except ValueError:
                    return jsonify({"message": "Invalid employee_id format"}), 400

            if start_date:
                query = query.filter(Attendance.date >= start_date)
            if end_date:
                query = query.filter(Attendance.date <= end_date)

            # Get attendance records
            attendance_records = query.order_by(Attendance.date.desc(), Attendance.check_in_time.desc()).all()

            # Generate PDF even if no records found (will show "No records found" message)
            pdf_buffer = _generate_attendance_pdf(company, attendance_records, start_date, end_date, employee_id, session)

            # Create response
            response = make_response(pdf_buffer.getvalue())
            response.headers['Content-Type'] = 'application/pdf'

            # Generate filename
            filename_parts = ['attendance_report']
            if employee_id:
                employee = session.query(Employee).filter_by(employee_id=employee_id).first()
                if employee:
                    filename_parts.append(f"{employee.first_name}_{employee.last_name}")
            if start_date:
                filename_parts.append(start_date.strftime('%Y%m%d'))
            if end_date and end_date != start_date:
                filename_parts.append(f"to_{end_date.strftime('%Y%m%d')}")

            filename = '_'.join(filename_parts) + '.pdf'
            response.headers['Content-Disposition'] = f'attachment; filename="{filename}"'

            return response

        except Exception as e:
            app.logger.error(f"Error generating attendance PDF: {e}")
            return jsonify({"message": "Internal server error"}), 500


@attendance_api.route('/api/attendance/summary/pdf', methods=['GET'])
@token_required
def download_attendance_summary_pdf():
    """
    Download monthly attendance summary report as PDF.

    Query Parameters:
    - company_id: Required. The ID of the company
    - start_date: Optional. Start date for date range filter (YYYY-MM-DD)
    - end_date: Optional. End date for date range filter (YYYY-MM-DD)
    - employee_id: Optional. Filter by specific employee

    If start_date and end_date are not provided, defaults to the current month.
    """
    try:
        company_id = request.args.get('company_id')

        if not company_id:
            return jsonify({"message": "Company ID is required"}), 400

        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)

        # Get company details
        try:
            company = Company.query.filter_by(company_id=company_id).first()
        except Exception as e:
            app.logger.error(f"Error getting company details: {e}")
            return jsonify({"message": "Company not found"}), 404

        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404

        # Get query parameters
        employee_id = request.args.get('employee_id')
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')

        # Parse dates
        start_date = HelperMethods.parse_date(start_date_str) if start_date_str else None
        end_date = HelperMethods.parse_date(end_date_str) if end_date_str else None

        # Default to current month if no dates provided
        if not start_date and not end_date:
            today = date.today()
            start_date = today.replace(day=1)
            # Get last day of current month
            if today.month == 12:
                end_date = today.replace(year=today.year + 1, month=1, day=1) - timedelta(days=1)
            else:
                end_date = today.replace(month=today.month + 1, day=1) - timedelta(days=1)

        # Validate date range
        if start_date and end_date and start_date > end_date:
            return jsonify({"message": "start_date cannot be after end_date"}), 400

        app.logger.info(f"Generating monthly summary PDF for company {company_id} from {start_date} to {end_date}")

        # Connect to the database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Calculate employee summaries
            employee_summaries = _calculate_monthly_summary_data(session, start_date, end_date, employee_id)

            if not employee_summaries:
                return jsonify({"message": "No attendance data found for the specified period"}), 404

            # Calculate department summaries
            department_summaries = _calculate_department_summary(employee_summaries)

            # Generate PDF
            pdf_buffer = _generate_monthly_summary_pdf(company, employee_summaries, department_summaries, start_date, end_date)

            # Create response
            response = make_response(pdf_buffer.getvalue())
            response.headers['Content-Type'] = 'application/pdf'

            # Generate filename
            filename_parts = ['attendance_summary']
            if employee_id:
                employee = session.query(Employee).filter_by(employee_id=employee_id).first()
                if employee:
                    filename_parts.append(f"{employee.first_name}_{employee.last_name}")
            filename_parts.append(start_date.strftime('%Y%m%d'))
            if end_date and end_date != start_date:
                filename_parts.append(f"to_{end_date.strftime('%Y%m%d')}")

            filename = '_'.join(filename_parts) + '.pdf'
            response.headers['Content-Disposition'] = f'attachment; filename="{filename}"'

            app.logger.info(f"Successfully generated monthly summary PDF: {filename}")
            return response

    except Exception as e:
        app.logger.error(f"Error generating monthly summary PDF: {e}")
        return jsonify({"message": "Internal server error", "error": str(e)}), 500


@attendance_api.route('/api/attendance/summary/excel', methods=['GET'])
@token_required
def download_attendance_summary_excel():
    """
    Download monthly attendance summary report as Excel.

    Query Parameters:
    - company_id: Required. The ID of the company
    - start_date: Optional. Start date for date range filter (YYYY-MM-DD)
    - end_date: Optional. End date for date range filter (YYYY-MM-DD)
    - employee_id: Optional. Filter by specific employee

    If start_date and end_date are not provided, defaults to the current month.
    """
    try:
        company_id = request.args.get('company_id')

        if not company_id:
            return jsonify({"message": "Company ID is required"}), 400

        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)

        # Get company details
        try:
            company = Company.query.filter_by(company_id=company_id).first()
        except Exception as e:
            app.logger.error(f"Error getting company details: {e}")
            return jsonify({"message": "Company not found"}), 404

        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404

        # Get query parameters
        employee_id = request.args.get('employee_id')
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')

        # Parse dates
        start_date = HelperMethods.parse_date(start_date_str) if start_date_str else None
        end_date = HelperMethods.parse_date(end_date_str) if end_date_str else None

        # Default to current month if no dates provided
        if not start_date and not end_date:
            today = date.today()
            start_date = today.replace(day=1)
            # Get last day of current month
            if today.month == 12:
                end_date = today.replace(year=today.year + 1, month=1, day=1) - timedelta(days=1)
            else:
                end_date = today.replace(month=today.month + 1, day=1) - timedelta(days=1)

        # Validate date range
        if start_date and end_date and start_date > end_date:
            return jsonify({"message": "start_date cannot be after end_date"}), 400

        app.logger.info(f"Generating monthly summary Excel for company {company_id} from {start_date} to {end_date}")

        # Connect to the database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Calculate employee summaries
            employee_summaries = _calculate_monthly_summary_data(session, start_date, end_date, employee_id)

            if not employee_summaries:
                return jsonify({"message": "No attendance data found for the specified period"}), 404

            # Calculate department summaries
            department_summaries = _calculate_department_summary(employee_summaries)

            # Generate Excel
            excel_buffer = _generate_monthly_summary_excel(company, employee_summaries, department_summaries, start_date, end_date)

            # Create response
            response = make_response(excel_buffer.getvalue())
            response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'

            # Generate filename
            filename_parts = ['attendance_summary']
            if employee_id:
                employee = session.query(Employee).filter_by(employee_id=employee_id).first()
                if employee:
                    filename_parts.append(f"{employee.first_name}_{employee.last_name}")
            filename_parts.append(start_date.strftime('%Y%m%d'))
            if end_date and end_date != start_date:
                filename_parts.append(f"to_{end_date.strftime('%Y%m%d')}")

            filename = '_'.join(filename_parts) + '.xlsx'
            response.headers['Content-Disposition'] = f'attachment; filename="{filename}"'

            app.logger.info(f"Successfully generated monthly summary Excel: {filename}")
            return response

    except Exception as e:
        app.logger.error(f"Error generating monthly summary Excel: {e}")
        return jsonify({"message": "Internal server error", "error": str(e)}), 500


@attendance_api.route('/api/attendance/<attendance_id>', methods=['GET'])
@token_required
def get_attendance(attendance_id):
    """Get a specific attendance record by ID."""
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        attendance = Attendance.get_attendance_by_id(session, attendance_id)

        if not attendance:
            return jsonify({"message": f"Attendance record with ID {attendance_id} not found"}), 404

        return jsonify({
            "status": "success",
            "attendance": attendance.to_dict()
        }), 200

@attendance_api.route('/api/attendance/daily', methods=['GET'])
@token_required
def get_daily_attendance():
    """
    Get daily attendance records with optional date filtering and department-based statistics.

    Query Parameters:
    - company_id: Required. The ID of the company to get attendance for.
    - date: Optional. The date to get attendance for in YYYY-MM-DD format. Defaults to today.
    - page: Optional. Page number for pagination. Defaults to 1.
    - per_page: Optional. Number of items per page. Defaults to 50.
    - include_ai_insights: Optional. Include AI-generated insights (true/false). Defaults to false.

    Returns:
    - JSON object with attendance statistics, department-based summaries, and employee details grouped by status.
    - If include_ai_insights=true, also includes AI-generated insights about the attendance data.
    """
    try:
        # Get and validate required parameters
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"message": "Company ID is required"}), 400

        # Get optional parameters
        date_str = request.args.get('date')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 100))
        include_ai_insights = request.args.get('include_ai_insights', 'false').lower() == 'true'

        # Parse date (default to today if not provided)
        target_date = HelperMethods.parse_date(date_str) if date_str else datetime.now().date()
        if date_str and not target_date:
            return jsonify({"message": "Invalid date format. Use YYYY-MM-DD"}), 400

        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404

        # Connect to the database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Get all active employees
            all_employees = session.query(Employee).filter(Employee.status == 'active').all()
            total_employees = len(all_employees)

            # Get all departments
            all_departments = session.query(Department).all()

            # Get attendance records for the target date (active employees only)
            attendance_records = Attendance.get_attendance_by_date(session, target_date, active_employees_only=True)
            attendance_data = [record.to_dict() for record in attendance_records]

            # Create a mapping of employee_id to attendance record
            attendance_map = {record['employee_id']: record for record in attendance_data}

            # Initialize counters and employee lists
            present_count = 0
            absent_count = 0
            on_leave_count = 0
            present_employees = []
            absent_employees = []
            on_leave_employees = []

            # Initialize department statistics
            department_stats = {}
            # Add a special category for employees with no department
            department_stats['no_department'] = {
                'department_id': 'no_department',
                'department_name': 'No Department',
                'total_employees': 0,
                'present_count': 0,
                'absent_count': 0,
                'on_leave_count': 0,
                'attendance_percentage': 0
            }

            # Add all existing departments
            for dept in all_departments:
                department_stats[str(dept.department_id)] = {
                    'department_id': str(dept.department_id),
                    'department_name': dept.name,
                    'total_employees': 0,
                    'present_count': 0,
                    'absent_count': 0,
                    'on_leave_count': 0,
                    'attendance_percentage': 0
                }

            # Process each employee to determine their attendance status
            for employee in all_employees:
                employee_dict = employee.to_dict()
                employee_id_str = str(employee.employee_id)

                # Add department info if available
                department_id_str = None
                if employee.department_id and employee.department:
                    department_id_str = str(employee.department_id)
                    employee_dict['department_name'] = employee.department.name

                    # Update department total employee count
                    if department_id_str in department_stats:
                        department_stats[department_id_str]['total_employees'] += 1
                else:
                    # Employee has no department, add to "No Department" category
                    department_id_str = 'no_department'
                    employee_dict['department_name'] = 'No Department'
                    department_stats['no_department']['total_employees'] += 1

                # Check if employee has an attendance record for the day
                if employee_id_str in attendance_map:
                    record = attendance_map[employee_id_str]
                    status = record['status']

                    # Add attendance details to employee dict
                    employee_dict['attendance'] = {
                        'attendance_id': record['attendance_id'],
                        'check_in_time': record['check_in_time'],
                        'check_out_time': record['check_out_time'],
                        'total_hours': record['total_hours'],
                        'status': status
                    }

                    # Categorize by status
                    if status == 'present':
                        present_count += 1
                        present_employees.append(employee_dict)

                        # Update department stats
                        if department_id_str and department_id_str in department_stats:
                            department_stats[department_id_str]['present_count'] += 1
                    elif status == 'on leave':
                        on_leave_count += 1
                        on_leave_employees.append(employee_dict)

                        # Update department stats
                        if department_id_str and department_id_str in department_stats:
                            department_stats[department_id_str]['on_leave_count'] += 1
                    else:
                        # Any other status (like 'late') is still counted as present for attendance purposes
                        present_count += 1
                        present_employees.append(employee_dict)

                        # Update department stats
                        if department_id_str and department_id_str in department_stats:
                            department_stats[department_id_str]['present_count'] += 1
                else:
                    # No attendance record means absent
                    employee_dict['attendance'] = {
                        'status': 'absent',
                        'reason': 'No attendance record'
                    }
                    absent_count += 1
                    absent_employees.append(employee_dict)

                    # Update department stats
                    if department_id_str and department_id_str in department_stats:
                        department_stats[department_id_str]['absent_count'] += 1

            # Calculate attendance percentage for each department
            for dept_id, stats in department_stats.items():
                total = stats['total_employees']
                if total > 0:
                    # Include both present and late employees as "attended"
                    attended_count = stats['present_count'] + stats.get('late_count', 0)
                    stats['attendance_percentage'] = round((attended_count / total) * 100, 2)

            # Convert department stats to a list and sort by attendance percentage (descending)
            department_summary = list(department_stats.values())

            # Only include departments with employees (including "No Department" if it has employees)
            department_summary = [dept for dept in department_summary if dept['total_employees'] > 0]

            # Sort by attendance percentage (descending)
            department_summary.sort(key=lambda x: x['attendance_percentage'], reverse=True)

            # Apply pagination to each category
            def paginate_list(items, page, per_page):
                start = (page - 1) * per_page
                end = start + per_page
                return items[start:min(end, len(items))], len(items)

            present_employees_paginated, present_total = paginate_list(present_employees, page, per_page)
            absent_employees_paginated, absent_total = paginate_list(absent_employees, page, per_page)
            on_leave_employees_paginated, on_leave_total = paginate_list(on_leave_employees, page, per_page)

            # Prepare response data
            response_data = {
                "status": "success",
                "metadata": {
                    "date": target_date.strftime('%Y-%m-%d'),
                    "total_employees": total_employees
                },
                "summary": {
                    "present_count": present_count,
                    "absent_count": absent_count,
                    "on_leave_count": on_leave_count,
                    # Include late employees as attended for daily summary
                    "late_count": sum(1 for record in attendance_data if record.get('status') == 'late'),
                    "attendance_percentage": round(((present_count + sum(1 for record in attendance_data if record.get('status') == 'late')) / total_employees) * 100, 2) if total_employees > 0 else 0
                },
                "department_summary": department_summary,
                "present_employees": {
                    "count": present_total,
                    "data": present_employees_paginated
                },
                "absent_employees": {
                    "count": absent_total,
                    "data": absent_employees_paginated
                },
                "on_leave_employees": {
                    "count": on_leave_total,
                    "data": on_leave_employees_paginated
                }
            }

            # Add AI insights if requested and available
            if include_ai_insights and AI_AVAILABLE:
                try:
                    # Get company information with country details
                    company = Company.get_company_by_id(company_id)
                    company_name = company.company_name if company else "Your Company"
                    company_dict = company.to_dict(include_country_details=True) if company else {}

                    # Debug: Log company information
                    app.logger.info(f"Company data for AI: {company_dict}")

                    # Extract employee positions from attendance data for salary estimation
                    employee_positions = []
                    for emp in response_data.get('present_employees', {}).get('data', []):
                        if emp.get('position'):
                            employee_positions.append(emp['position'])
                    for emp in response_data.get('absent_employees', {}).get('data', []):
                        if emp.get('position'):
                            employee_positions.append(emp['position'])

                    # Prepare enhanced data for AI insights
                    ai_data = {
                        'company_name': company_name,
                        'date': target_date.strftime('%Y-%m-%d'),
                        'company_info': {
                            'country': company_dict.get('country', {}),
                            'employee_positions': list(set(employee_positions)),  # Remove duplicates
                            'total_workforce': response_data['metadata']['total_employees']
                        },
                        'attendance_data': {
                            'summary': {
                                **response_data['summary'],
                                'total_employees': response_data['metadata']['total_employees']
                            },
                            'departments': response_data['department_summary'],
                            'employee_details': {
                                'present': response_data.get('present_employees', {}).get('data', []),
                                'absent': response_data.get('absent_employees', {}).get('data', [])
                            }
                        },
                        'target_audience': 'management',
                        'tone': 'professional',
                        'detail_level': 'medium'
                    }

                    # Generate AI insights
                    orchestrator = AIInsightsOrchestrator(session, company_id)
                    ai_result = orchestrator.generate_attendance_insights(
                        data=ai_data,
                        insight_type='daily_summary',
                        user_id=getattr(request, 'user_id', None)
                    )

                    if ai_result.get('success'):
                        response_data['ai_insights'] = {
                            'content': ai_result['insight']['insight_content'],
                            'summary': ai_result['insight']['insight_summary'],
                            'recommendations': ai_result['insight'].get('recommendations', []),
                            'alerts': ai_result['insight'].get('alerts', []),
                            'priority': ai_result['insight']['priority'],
                            'cached': ai_result.get('cached', False)
                        }
                    else:
                        response_data['ai_insights'] = {
                            'error': ai_result.get('error', 'Failed to generate AI insights'),
                            'available': False
                        }

                except Exception as ai_error:
                    app.logger.error(f"Error generating AI insights: {ai_error}")
                    response_data['ai_insights'] = {
                        'error': 'AI insights temporarily unavailable',
                        'available': False
                    }
            elif include_ai_insights and not AI_AVAILABLE:
                response_data['ai_insights'] = {
                    'error': 'AI insights not available - feature not enabled',
                    'available': False
                }

            return jsonify(response_data), 200
    except ValueError as e:
        app.logger.error(f"Value error in get_daily_attendance: {str(e)}")
        return jsonify({"message": f"Invalid parameter: {str(e)}"}), 400
    except Exception as e:
        app.logger.error(f"Error in get_daily_attendance: {str(e)}")
        return jsonify({"message": "An error occurred while processing the request"}), 500

@attendance_api.route('/api/attendance', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def create_attendance():
    """Create a new attendance record."""
    data = request.get_json()
    company_id = data.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Validate required fields
    required_fields = ['employee_id', 'source']
    for field in required_fields:
        if not data.get(field):
            return jsonify({"message": f"{field.replace('_', ' ').title()} is required"}), 400

    # At least one of check_in_time or check_out_time must be provided
    if not data.get('check_in_time') and not data.get('check_out_time'):
        return jsonify({"message": "Either check-in time or check-out time is required"}), 400

    # Parse check-in time
    check_in_time = None
    if data.get('check_in_time'):
        try:
            check_in_time = datetime.strptime(data.get('check_in_time'), '%Y-%m-%d %H:%M:%S')
        except ValueError:
            return jsonify({"message": "Invalid check-in time format. Use YYYY-MM-DD HH:MM:SS"}), 400

    # Parse check-out time
    check_out_time = None
    if data.get('check_out_time'):
        try:
            check_out_time = datetime.strptime(data.get('check_out_time'), '%Y-%m-%d %H:%M:%S')
        except ValueError:
            return jsonify({"message": "Invalid check-out time format. Use YYYY-MM-DD HH:MM:SS"}), 400

    # Extract date from check_in_time (or check_out_time if check_in_time is not provided)
    date_value = None
    if check_in_time:
        date_value = check_in_time.date()
    elif check_out_time:
        date_value = check_out_time.date()
    else:
        # This should never happen due to the validation above, but just in case
        return jsonify({"message": "Either check-in time or check-out time is required"}), 400

    # Calculate total hours if both check-in and check-out times are provided
    total_hours = None
    if check_in_time and check_out_time:
        time_diff = check_out_time - check_in_time
        total_hours = time_diff.total_seconds() / 3600  # Convert to hours

    # Prepare attendance data
    attendance_data = {
        'employee_id': data.get('employee_id'),
        'date': date_value,
        'check_in_time': check_in_time,
        'check_out_time': check_out_time,
        'total_hours': total_hours,
        'status': data.get('status', 'present'),
        'source': data.get('source'),
        'source_record_id': data.get('source_record_id'),
        'notes': data.get('notes'),
        'created_by': data.get('created_by')
    }

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if employee exists
        employee = Employee.get_employee_by_id(session, attendance_data['employee_id'])
        if not employee:
            return jsonify({"message": f"Employee with ID {attendance_data['employee_id']} not found"}), 404

        # Create the attendance record
        attendance = Attendance.create_attendance(session, **attendance_data)

        if not attendance:
            return jsonify({"message": "Failed to create attendance record"}), 500

        return jsonify({
            "status": "success",
            "attendance": attendance.to_dict()
        }), 200

@attendance_api.route('/api/attendance/<attendance_id>', methods=['PATCH'])
@token_required
@roles_required('admin', 'hr')
def update_attendance(attendance_id):
    """Update an existing attendance record."""
    data = request.get_json()
    company_id = data.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Parse dates and times
    update_data = {}

    # Parse check-in time if provided
    if 'check_in_time' in data:
        try:
            check_in_time = datetime.strptime(data.get('check_in_time'), '%Y-%m-%d %H:%M:%S')
            update_data['check_in_time'] = check_in_time

            # If we're updating check_in_time, also update the date field
            update_data['date'] = check_in_time.date()
        except ValueError:
            return jsonify({"message": "Invalid check-in time format. Use YYYY-MM-DD HH:MM:SS"}), 400

    # Parse check-out time if provided
    if 'check_out_time' in data:
        try:
            check_out_time = datetime.strptime(data.get('check_out_time'), '%Y-%m-%d %H:%M:%S')
            update_data['check_out_time'] = check_out_time

            # If we're updating check_out_time and not check_in_time, use check_out_time's date
            if 'date' not in update_data:
                update_data['date'] = check_out_time.date()
        except ValueError:
            return jsonify({"message": "Invalid check-out time format. Use YYYY-MM-DD HH:MM:SS"}), 400

    # If date is explicitly provided, it will override the date extracted from timestamps
    if 'date' in data:
        date_value = HelperMethods.parse_date(data.get('date'))
        if not date_value:
            return jsonify({"message": "Invalid date format. Use YYYY-MM-DD"}), 400
        update_data['date'] = date_value

    # Add other fields to update
    for field in ['employee_id', 'status', 'source', 'source_record_id', 'notes']:
        if field in data:
            update_data[field] = data[field]

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if attendance record exists
        attendance = Attendance.get_attendance_by_id(session, attendance_id)
        if not attendance:
            return jsonify({"message": f"Attendance record with ID {attendance_id} not found"}), 404

        # We no longer need to update datetime objects with the attendance date
        # since we're now using full datetime values with their own dates

        # If employee_id is being updated, check if the new employee exists
        if 'employee_id' in update_data and update_data['employee_id'] != attendance.employee_id:
            employee = Employee.get_employee_by_id(session, update_data['employee_id'])
            if not employee:
                return jsonify({"message": f"Employee with ID {update_data['employee_id']} not found"}), 404

        # Update the attendance record
        updated_attendance = Attendance.update_attendance(session, attendance_id, **update_data)

        if not updated_attendance:
            return jsonify({"message": "Failed to update attendance record"}), 500

        return jsonify({
            "status": "success",
            "attendance": updated_attendance.to_dict()
        }), 200

@attendance_api.route('/api/attendance/<attendance_id>', methods=['DELETE'])
@token_required
@roles_required('admin', 'hr')
def delete_attendance(attendance_id):
    """Delete an attendance record."""
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if attendance record exists
        attendance = Attendance.get_attendance_by_id(session, attendance_id)
        if not attendance:
            return jsonify({"message": f"Attendance record with ID {attendance_id} not found"}), 404

        # Delete the attendance record
        success = Attendance.delete_attendance(session, attendance_id)

        if not success:
            return jsonify({"message": "Failed to delete attendance record"}), 500

        return jsonify({
            "status": "success",
            "message": "Attendance record deleted successfully"
        }), 200

@attendance_api.route('/api/attendance/statistics', methods=['GET'])
@token_required
def get_attendance_statistics():
    """
    Get attendance statistics for a specific period (weekly, monthly, annual, or custom date range).

    Query Parameters:
    - company_id: Required. The ID of the company to get statistics for.
    - period: Optional. The period type ('weekly', 'monthly', 'annual', 'custom'). Defaults to 'weekly'.
    - date: Optional. Reference date for weekly, monthly, or annual periods in YYYY-MM-DD format. Defaults to today.
    - start_date: Required for custom period. Start date in YYYY-MM-DD format.
    - end_date: Required for custom period. End date in YYYY-MM-DD format.
    - include_ai_insights: Optional. Whether to include AI-generated insights (true/false). Defaults to false.

    Returns:
    - JSON object with attendance statistics for the specified period.
    - If include_ai_insights=true, also includes AI-generated insights about the attendance data.
    """
    try:
        # Get and validate required parameters
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"message": "Company ID is required"}), 400

        # Get optional parameters
        period = request.args.get('period', 'weekly').lower()
        date_str = request.args.get('date')
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')
        include_ai_insights = request.args.get('include_ai_insights', 'false').lower() == 'true'

        # Parse reference date (default to today if not provided)
        reference_date = HelperMethods.parse_date(date_str) if date_str else datetime.now().date()
        if date_str and not reference_date:
            return jsonify({"message": "Invalid date format. Use YYYY-MM-DD"}), 400

        # Determine date range based on period
        if period == 'weekly':
            start_date, end_date = DateHelper.get_week_date_range(reference_date)
            period_description = f"Week of {start_date.strftime('%Y-%m-%d')}"
        elif period == 'monthly':
            start_date, end_date = DateHelper.get_month_date_range(reference_date)
            period_description = start_date.strftime('%B %Y')
        elif period == 'annual':
            start_date, end_date = DateHelper.get_year_date_range(reference_date)
            period_description = start_date.strftime('%Y')
        elif period == 'custom':
            # For custom period, both start_date and end_date are required
            start_date = HelperMethods.parse_date(start_date_str)
            end_date = HelperMethods.parse_date(end_date_str)

            if not start_date or not end_date:
                return jsonify({"message": "Both start_date and end_date are required for custom period in YYYY-MM-DD format"}), 400

            if start_date > end_date:
                return jsonify({"message": "start_date cannot be after end_date"}), 400

            period_description = f"{start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}"
        else:
            return jsonify({"message": "Invalid period. Use 'weekly', 'monthly', 'annual', or 'custom'"}), 400

        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404

        # Connect to the database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Get all active employees
            all_employees = session.query(Employee).filter(Employee.status == 'active').count()

            # Get all departments
            all_departments = session.query(Department).all()

            # Get all dates in the range first (needed for multiple calculations)
            dates_in_range = DateHelper.get_dates_in_range(start_date, end_date)

            # Get attendance records for the date range (active employees only)
            attendance_query = session.query(Attendance).join(
                Employee, Attendance.employee_id == Employee.employee_id
            ).filter(
                Attendance.date >= start_date,
                Attendance.date <= end_date,
                Employee.status == 'active'
            )

            # Count by status for the entire period
            present_count = attendance_query.filter(Attendance.status == 'present').count()
            on_leave_count = attendance_query.filter(Attendance.status == 'on leave').count()
            late_count = attendance_query.filter(Attendance.status == 'late').count()

            # Calculate total possible attendance based on employee shifts
            all_employees_list = session.query(Employee).filter(Employee.status == 'active').all()
            total_possible_attendance = _calculate_total_possible_attendance(session, all_employees_list, start_date, end_date)

            # Calculate absent count (total possible - present - on leave - late)
            absent_count = total_possible_attendance - present_count - on_leave_count - late_count

            # Calculate daily statistics
            daily_stats = []

            # For annual statistics, we'll group by month instead of showing each day
            # to avoid returning too much data
            if period == 'annual':
                # Create monthly statistics instead of daily
                monthly_stats = []
                current_month = None
                month_present = 0
                month_absent = 0
                month_on_leave = 0
                month_late = 0
                month_days = 0

                for current_date in dates_in_range:
                    # If we've moved to a new month, save the previous month's stats
                    if current_month and current_date.month != current_month:
                        # Calculate attendance percentage for the month (include late as present)
                        month_attendance_percentage = 0
                        # Calculate shift-aware possible attendance for this month
                        month_start = date(current_date.year, current_month, 1)
                        if current_month == 12:
                            month_end = date(current_date.year + 1, 1, 1) - timedelta(days=1)
                        else:
                            month_end = date(current_date.year, current_month + 1, 1) - timedelta(days=1)
                        month_end = min(month_end, end_date)
                        month_possible_attendance = _calculate_total_possible_attendance(session, all_employees_list, month_start, month_end)
                        if month_possible_attendance > 0:
                            month_attended = month_present + month_late
                            month_attendance_percentage = round((month_attended / month_possible_attendance) * 100, 2)

                        # Add the month's stats
                        month_date = date(current_date.year, current_month, 1)
                        monthly_stats.append({
                            "month": month_date.strftime('%B %Y'),
                            "days_in_month": month_days,
                            "present_count": month_present,
                            "absent_count": month_absent,
                            "on_leave_count": month_on_leave,
                            "late_count": month_late,
                            "attendance_percentage": month_attendance_percentage
                        })

                        # Reset counters for the new month
                        month_present = 0
                        month_absent = 0
                        month_on_leave = 0
                        month_late = 0
                        month_days = 0

                    # Set the current month
                    current_month = current_date.month
                    month_days += 1

                    # Get attendance records for this date (active employees only)
                    day_records = session.query(Attendance).join(
                        Employee, Attendance.employee_id == Employee.employee_id
                    ).filter(
                        Attendance.date == current_date,
                        Employee.status == 'active'
                    ).all()
                    day_attendance_map = {str(record.employee_id): record.status for record in day_records}

                    # Count by status for this day
                    day_present = sum(1 for status in day_attendance_map.values() if status == 'present')
                    day_on_leave = sum(1 for status in day_attendance_map.values() if status == 'on leave')
                    day_late = sum(1 for status in day_attendance_map.values() if status == 'late')
                    day_absent = all_employees - day_present - day_on_leave - day_late

                    # Add to monthly totals
                    month_present += day_present
                    month_absent += day_absent
                    month_on_leave += day_on_leave
                    month_late += day_late

                # Add the last month's stats
                if current_month:
                    # Calculate attendance percentage for the month (include late as present)
                    month_attendance_percentage = 0
                    # Calculate shift-aware possible attendance for this month
                    month_start = date(end_date.year, current_month, 1)
                    if current_month == 12:
                        month_end = date(end_date.year + 1, 1, 1) - timedelta(days=1)
                    else:
                        month_end = date(end_date.year, current_month + 1, 1) - timedelta(days=1)
                    month_end = min(month_end, end_date)
                    month_possible_attendance = _calculate_total_possible_attendance(session, all_employees_list, month_start, month_end)
                    if month_possible_attendance > 0:
                        month_attended = month_present + month_late
                        month_attendance_percentage = round((month_attended / month_possible_attendance) * 100, 2)

                    # Add the month's stats
                    month_date = date(end_date.year, current_month, 1)
                    monthly_stats.append({
                        "month": month_date.strftime('%B %Y'),
                        "days_in_month": month_days,
                        "present_count": month_present,
                        "absent_count": month_absent,
                        "on_leave_count": month_on_leave,
                        "late_count": month_late,
                        "attendance_percentage": month_attendance_percentage
                    })

                # For annual statistics, we'll use the monthly stats instead of daily
                daily_stats = monthly_stats
            elif period != 'annual':
                # For weekly, monthly, and custom periods, calculate daily statistics
                for current_date in dates_in_range:
                    # Get all attendance records for this date (active employees only)
                    day_records = session.query(Attendance).join(
                        Employee, Attendance.employee_id == Employee.employee_id
                    ).filter(
                        Attendance.date == current_date,
                        Employee.status == 'active'
                    ).all()

                    # Create a mapping of employee_id to their attendance status for this day
                    day_attendance_map = {str(record.employee_id): record.status for record in day_records}

                    # Count by status
                    day_present = sum(1 for status in day_attendance_map.values() if status == 'present')
                    day_on_leave = sum(1 for status in day_attendance_map.values() if status == 'on leave')
                    day_late = sum(1 for status in day_attendance_map.values() if status == 'late')

                    # Calculate attendance percentage for the day (include late as present)
                    # Use shift-aware calculation for daily attendance percentage
                    day_attendance_percentage = 0
                    day_expected_employees = _calculate_total_possible_attendance(session, all_employees_list, current_date, current_date)
                    if day_expected_employees > 0:
                        day_attended = day_present + day_late
                        day_attendance_percentage = round((day_attended / day_expected_employees) * 100, 2)

                    # Calculate absent count based on expected employees for this day
                    day_absent = day_expected_employees - day_present - day_on_leave - day_late

                    daily_stats.append({
                        "date": current_date.strftime('%Y-%m-%d'),
                        "day_of_week": current_date.strftime('%A'),
                        "present_count": day_present,
                        "absent_count": day_absent,
                        "on_leave_count": day_on_leave,
                        "late_count": day_late,
                        "attendance_percentage": day_attendance_percentage
                    })

            # Calculate department statistics
            department_stats = []

            # First, get employees without department
            no_dept_employees_count = session.query(Employee).filter(
                Employee.department_id.is_(None),
                Employee.status == 'active'
            ).count()

            # If there are employees without department, create a "No Department" category
            if no_dept_employees_count > 0:
                # Get all employee IDs with no department
                no_dept_employee_ids = [str(emp.employee_id) for emp in session.query(Employee.employee_id).filter(
                    Employee.department_id.is_(None),
                    Employee.status == 'active'
                ).all()]

                # Initialize counters
                no_dept_present = 0
                no_dept_on_leave = 0
                no_dept_late = 0

                # For each day in the period, count attendance for employees without department
                for current_date in dates_in_range:
                    # Get attendance records for employees without department on this day (active employees only)
                    day_records = session.query(Attendance).join(
                        Employee, Attendance.employee_id == Employee.employee_id
                    ).filter(
                        Attendance.employee_id.in_([uuid.UUID(emp_id) for emp_id in no_dept_employee_ids]),
                        Attendance.date == current_date,
                        Employee.status == 'active'
                    ).all()

                    # Create a mapping of employee_id to their attendance status for this day
                    day_attendance_map = {str(record.employee_id): record.status for record in day_records}

                    # Count by status for this day
                    no_dept_present += sum(1 for status in day_attendance_map.values() if status == 'present')
                    no_dept_on_leave += sum(1 for status in day_attendance_map.values() if status == 'on leave')
                    no_dept_late += sum(1 for status in day_attendance_map.values() if status == 'late')

                # Calculate total possible attendance based on employee shifts
                no_dept_employees_list = session.query(Employee).filter(
                    Employee.department_id.is_(None),
                    Employee.status == 'active'
                ).all()
                no_dept_possible_attendance = _calculate_total_possible_attendance(session, no_dept_employees_list, start_date, end_date)

                # Calculate absent count (total possible - present - on leave - late)
                no_dept_absent = no_dept_possible_attendance - no_dept_present - no_dept_on_leave - no_dept_late

                # Calculate average attendance for the period (include late as present)
                no_dept_avg_attendance = 0
                if no_dept_possible_attendance > 0:
                    no_dept_attended = no_dept_present + no_dept_late
                    no_dept_avg_attendance = round(no_dept_attended / no_dept_possible_attendance * 100, 2)

                # Add "No Department" category to department statistics
                department_stats.append({
                    "department_id": "no_department",
                    "department_name": "No Department",
                    "employee_count": no_dept_employees_count,
                    "present_count": no_dept_present,
                    "absent_count": no_dept_absent,
                    "on_leave_count": no_dept_on_leave,
                    "late_count": no_dept_late,
                    "avg_attendance_percentage": no_dept_avg_attendance
                })

            # Now process regular departments
            for dept in all_departments:
                # Get employees in this department
                dept_employees_count = session.query(Employee).filter(
                    Employee.department_id == dept.department_id,
                    Employee.status == 'active'
                ).count()

                if dept_employees_count > 0:
                    # Get all employee IDs in this department
                    dept_employee_ids = [str(emp.employee_id) for emp in session.query(Employee.employee_id).filter(
                        Employee.department_id == dept.department_id,
                        Employee.status == 'active'
                    ).all()]

                    # Initialize counters
                    dept_present = 0
                    dept_on_leave = 0
                    dept_late = 0

                    # For each day in the period, count attendance for this department
                    for current_date in dates_in_range:
                        # Get attendance records for this department on this day (active employees only)
                        day_records = session.query(Attendance).join(
                            Employee, Attendance.employee_id == Employee.employee_id
                        ).filter(
                            Attendance.employee_id.in_([uuid.UUID(emp_id) for emp_id in dept_employee_ids]),
                            Attendance.date == current_date,
                            Employee.status == 'active'
                        ).all()

                        # Create a mapping of employee_id to their attendance status for this day
                        day_attendance_map = {str(record.employee_id): record.status for record in day_records}

                        # Count by status for this day
                        dept_present += sum(1 for status in day_attendance_map.values() if status == 'present')
                        dept_on_leave += sum(1 for status in day_attendance_map.values() if status == 'on leave')
                        dept_late += sum(1 for status in day_attendance_map.values() if status == 'late')

                    # Calculate total possible attendance based on employee shifts
                    dept_employees_list = session.query(Employee).filter(
                        Employee.department_id == dept.department_id,
                        Employee.status == 'active'
                    ).all()
                    total_possible_attendance = _calculate_total_possible_attendance(session, dept_employees_list, start_date, end_date)

                    # Calculate absent count (total possible - present - on leave - late)
                    dept_absent = total_possible_attendance - dept_present - dept_on_leave - dept_late

                    # Calculate average attendance for the period (include late as present)
                    avg_attendance = 0
                    if total_possible_attendance > 0:
                        dept_attended = dept_present + dept_late
                        avg_attendance = round(dept_attended / total_possible_attendance * 100, 2)

                    department_stats.append({
                        "department_id": str(dept.department_id),
                        "department_name": dept.name,
                        "employee_count": dept_employees_count,
                        "present_count": dept_present,
                        "absent_count": dept_absent,
                        "on_leave_count": dept_on_leave,
                        "late_count": dept_late,
                        "avg_attendance_percentage": avg_attendance
                    })

            # Sort departments by average attendance (descending)
            department_stats.sort(key=lambda x: x['avg_attendance_percentage'], reverse=True)

            # Calculate overall statistics
            total_days = len(dates_in_range)  # dates_in_range is defined earlier in the function

            # Use the shift-aware total possible attendance calculated earlier
            # (total_possible_attendance is already calculated above)

            attendance_percentage = 0
            if total_possible_attendance > 0:
                # Include late employees as attended
                total_attended = present_count + late_count
                attendance_percentage = round((total_attended / total_possible_attendance) * 100, 2)

            # Prepare response data
            response_data = {
                "status": "success",
                "metadata": {
                    "period": period,
                    "period_description": period_description,
                    "start_date": start_date.strftime('%Y-%m-%d'),
                    "end_date": end_date.strftime('%Y-%m-%d'),
                    "total_days": total_days,
                    "total_employees": all_employees
                },
                "summary": {
                    "present_count": present_count,
                    "absent_count": absent_count,
                    "on_leave_count": on_leave_count,
                    "late_count": late_count,
                    "attendance_percentage": attendance_percentage
                },
                # Use appropriate name based on period type
                f"{period}_statistics": daily_stats,
                "department_statistics": department_stats
            }

            # Generate AI insights if requested
            if include_ai_insights and AI_AVAILABLE:
                try:
                    # Get company information with country details (same pattern as daily)
                    company = Company.get_company_by_id(company_id)
                    company_name = company.company_name if company else "Your Company"
                    company_dict = company.to_dict(include_country_details=True) if company else {}

                    # Get employee positions for AI context
                    employee_positions = [emp.position for emp in session.query(Employee.position).filter(
                        Employee.status == 'active', Employee.position.isnot(None)
                    ).distinct().all()]

                    # Normalize department data to match daily format
                    normalized_dept_stats = []
                    for dept in department_stats:
                        normalized_dept = dict(dept)  # Copy the department data
                        # Ensure attendance_percentage field exists (daily format expects this)
                        if 'avg_attendance_percentage' in normalized_dept and 'attendance_percentage' not in normalized_dept:
                            normalized_dept['attendance_percentage'] = normalized_dept['avg_attendance_percentage']
                        # Ensure total_employees field exists
                        if 'employee_count' in normalized_dept and 'total_employees' not in normalized_dept:
                            normalized_dept['total_employees'] = normalized_dept['employee_count']
                        normalized_dept_stats.append(normalized_dept)

                    # Prepare enhanced data for AI insights
                    ai_data = {
                        'company_name': company_name,
                        'period_info': {
                            'type': period,
                            'start_date': start_date.strftime('%Y-%m-%d'),
                            'end_date': end_date.strftime('%Y-%m-%d'),
                            'description': period_description,
                            'total_days': total_days
                        },
                        'company_info': {
                            'country': company_dict.get('country', {}),
                            'employee_positions': employee_positions,
                            'total_workforce': all_employees,
                            'departments': [dept['department_name'] for dept in department_stats if dept.get('department_name')]
                        },
                        'attendance_data': {
                            'summary': {
                                'total_employees': all_employees,
                                'present_count': present_count,
                                'absent_count': absent_count,
                                'on_leave_count': on_leave_count,
                                'late_count': late_count,
                                'attendance_percentage': attendance_percentage,
                                'total_possible_attendance': total_possible_attendance
                            },
                            'departments': normalized_dept_stats,  # Use normalized data to match daily format
                            'department_statistics': department_stats,  # Keep original for compatibility
                            f'{period}_breakdown': daily_stats
                        },
                        'target_audience': 'management',
                        'tone': 'professional',
                        'detail_level': 'comprehensive'
                    }

                    # Determine insight type based on period
                    insight_type_map = {
                        'weekly': 'weekly_summary',
                        'monthly': 'monthly_summary',
                        'annual': 'annual_summary',
                        'custom': 'custom_summary'
                    }
                    insight_type = insight_type_map.get(period, 'daily_summary')

                    # Debug: Log AI data structure
                    app.logger.info(f"AI data for {insight_type}: company={company_name}, period={period}")
                    app.logger.debug(f"AI data structure: {ai_data}")

                    # Generate AI insights
                    orchestrator = AIInsightsOrchestrator(session, company_id)
                    ai_result = orchestrator.generate_attendance_insights(
                        data=ai_data,
                        insight_type=insight_type,
                        user_id=getattr(request, 'user_id', None)
                    )

                    # Debug: Log AI result
                    app.logger.info(f"AI result success: {ai_result.get('success')}")
                    if not ai_result.get('success'):
                        app.logger.error(f"AI generation failed: {ai_result.get('error')}")

                    if ai_result.get('success'):
                        response_data['ai_insights'] = {
                            'content': ai_result['insight']['insight_content'],
                            'summary': ai_result['insight']['insight_summary'],
                            'recommendations': ai_result['insight'].get('recommendations', []),
                            'alerts': ai_result['insight'].get('alerts', []),
                            'priority': ai_result['insight']['priority'],
                            'cached': ai_result.get('cached', False)
                        }
                    else:
                        response_data['ai_insights'] = {
                            'error': ai_result.get('error', 'Failed to generate AI insights'),
                            'available': False
                        }

                except Exception as e:
                    app.logger.error(f"Error generating AI insights for attendance statistics: {e}")
                    response_data['ai_insights'] = {
                        'error': f'Error generating AI insights: {str(e)}',
                        'available': False
                    }
            elif include_ai_insights and not AI_AVAILABLE:
                response_data['ai_insights'] = {
                    'error': 'AI services are not available',
                    'available': False
                }

            return jsonify(response_data), 200
    except ValueError as e:
        app.logger.error(f"Value error in get_attendance_statistics: {str(e)}")
        return jsonify({"message": f"Invalid parameter: {str(e)}"}), 400
    except Exception as e:
        app.logger.error(f"Error in get_attendance_statistics: {str(e)}")
        return jsonify({"message": "An error occurred while processing the request"}), 500

@attendance_api.route('/api/attendance/employee/<employee_id>/statistics', methods=['GET'])
@token_required
def get_employee_attendance_statistics(employee_id):
    """
    Get comprehensive attendance statistics for a specific employee.

    This endpoint provides detailed attendance analytics including:
    - Personal attendance metrics and trends
    - Shift compliance analysis
    - Department and company comparisons
    - Time patterns and productivity insights
    - Behavioral analysis for AI integration

    Query Parameters:
    - company_id: Required. The ID of the company.
    - period: Optional. The period type ('daily', 'weekly', 'monthly', 'annual', 'custom'). Defaults to 'monthly'.
    - date: Optional. Reference date for daily, weekly, monthly, or annual periods in YYYY-MM-DD format. Defaults to today.
    - start_date: Required for custom period. Start date in YYYY-MM-DD format.
    - end_date: Required for custom period. End date in YYYY-MM-DD format.
    - include_comparisons: Optional. Whether to include department/company comparisons. Defaults to true.
    - include_trends: Optional. Whether to include trend analysis. Defaults to true.
    - include_patterns: Optional. Whether to include time patterns analysis. Defaults to true.
    - include_details: Optional. Whether to include detailed daily records. Defaults to false.

    Returns:
    - JSON object with comprehensive employee attendance statistics.
    """
    try:
        # Get and validate required parameters
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"message": "Company ID is required"}), 400

        # Get optional parameters
        period = request.args.get('period', 'monthly').lower()
        date_str = request.args.get('date')
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')
        include_comparisons = request.args.get('include_comparisons', 'true').lower() == 'true'
        include_trends = request.args.get('include_trends', 'true').lower() == 'true'
        include_patterns = request.args.get('include_patterns', 'true').lower() == 'true'
        include_details = request.args.get('include_details', 'false').lower() == 'true'

        # Validate period
        valid_periods = ['daily', 'weekly', 'monthly', 'annual', 'custom']
        if period not in valid_periods:
            return jsonify({"message": f"Invalid period. Must be one of: {', '.join(valid_periods)}"}), 400

        # Parse reference date (default to today if not provided)
        reference_date = HelperMethods.parse_date(date_str) if date_str else datetime.now().date()
        if date_str and not reference_date:
            return jsonify({"message": "Invalid date format. Use YYYY-MM-DD"}), 400

        # Validate that we're not querying too far into the future
        today = datetime.now().date()
        if reference_date > today:
            return jsonify({"message": f"Cannot query future dates. Reference date {reference_date} is after today {today}"}), 400

        # Handle custom period validation
        if period == 'custom':
            if not start_date_str or not end_date_str:
                return jsonify({"message": "start_date and end_date are required for custom period"}), 400

            start_date = HelperMethods.parse_date(start_date_str)
            end_date = HelperMethods.parse_date(end_date_str)

            if not start_date or not end_date:
                return jsonify({"message": "Invalid date format for start_date or end_date. Use YYYY-MM-DD"}), 400

            if start_date > end_date:
                return jsonify({"message": "start_date cannot be after end_date"}), 400

            # Validate custom dates are not in the future
            today = datetime.now().date()
            if end_date > today:
                return jsonify({"message": f"Cannot query future dates. End date {end_date} is after today {today}"}), 400
        else:
            # Calculate date range based on period using existing helper methods
            if period == 'daily':
                start_date = end_date = reference_date
            elif period == 'weekly':
                start_date, end_date = DateHelper.get_week_date_range(reference_date)
            elif period == 'monthly':
                start_date, end_date = DateHelper.get_month_date_range(reference_date)
            elif period == 'annual':
                start_date, end_date = DateHelper.get_year_date_range(reference_date)

        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404

        # Connect to the database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Get employee information
            employee = Employee.get_employee_by_id(session, employee_id)
            if not employee:
                return jsonify({"message": f"Employee with ID {employee_id} not found"}), 404

            # Check if employee is active
            if employee.status != 'active':
                return jsonify({"message": f"Employee with ID {employee_id} is not active"}), 400

            # Get employee's attendance records for the period
            attendance_records = session.query(Attendance).filter(
                Attendance.employee_id == employee_id,
                Attendance.date.between(start_date, end_date)
            ).order_by(Attendance.date).all()

            # Get employee's shift assignments for the period
            from application.Models.employees import EmployeeShift, Shift
            shift_assignments = session.query(EmployeeShift).join(Shift).filter(
                EmployeeShift.employee_id == employee_id,
                EmployeeShift.is_active == True,
                EmployeeShift.effective_start_date <= end_date,
                (EmployeeShift.effective_end_date.is_(None)) | (EmployeeShift.effective_end_date >= start_date)
            ).all()

            # Calculate working days based on shifts or default to weekdays
            dates_in_range = DateHelper.get_dates_in_range(start_date, end_date)
            working_days = _calculate_working_days_for_employee(dates_in_range, shift_assignments)
            total_days = len(dates_in_range)

            # Debug logging for working days calculation
            app.logger.info(f"Employee {employee_id} statistics calculation:")
            app.logger.info(f"  - Date range: {start_date} to {end_date}")
            app.logger.info(f"  - Total days in range: {total_days}")
            app.logger.info(f"  - Calculated working days: {working_days}")
            app.logger.info(f"  - Shift assignments count: {len(shift_assignments)}")
            app.logger.info(f"  - Attendance records count: {len(attendance_records)}")

            # Attendance counts and categorization
            present_records = [r for r in attendance_records if r.status in ['present', 'late']]
            late_records = [r for r in attendance_records if r.status == 'late']
            on_leave_records = [r for r in attendance_records if r.status == 'on leave']

            present_days = len(present_records)
            late_days = len(late_records)
            on_leave_days = len(on_leave_records)

            # Calculate absent days properly - ensure it's never negative
            absent_days = max(0, working_days - present_days - on_leave_days)

            # Time calculations
            total_hours = sum([r.total_hours for r in attendance_records if r.total_hours])
            average_daily_hours = total_hours / present_days if present_days > 0 else 0

            # Calculate expected hours based on shifts
            expected_hours = _calculate_expected_hours_for_employee(dates_in_range, shift_assignments, working_days)
            overtime_hours = max(0, total_hours - expected_hours) if expected_hours > 0 else 0
            undertime_hours = max(0, expected_hours - total_hours) if expected_hours > 0 else 0

            # Performance metrics
            attendance_rate = (present_days / working_days * 100) if working_days > 0 else 0
            punctuality_rate = ((present_days - late_days) / present_days * 100) if present_days > 0 else 0
            efficiency_rate = (total_hours / expected_hours * 100) if expected_hours > 0 else 0

            # Build employee info with enhanced details
            employee_info = {
                "employee_id": str(employee.employee_id),
                "first_name": employee.first_name,
                "last_name": employee.last_name,
                "full_name": f"{employee.first_name} {employee.last_name}",
                "email": employee.email,
                "phone_number": employee.phone_number,
                "id_number": employee.id_number,
                "position": employee.position,
                "hire_date": employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else None,
                "status": employee.status,
                "department": {
                    "department_id": str(employee.department_id) if employee.department_id else None,
                    "name": employee.department.name if employee.department else "No Department"
                },
                "tenure_days": (datetime.now().date() - employee.hire_date).days if employee.hire_date else None
            }

            # Build period info
            period_info = {
                "type": period,
                "start_date": start_date.strftime('%Y-%m-%d'),
                "end_date": end_date.strftime('%Y-%m-%d'),
                "total_days": total_days,
                "working_days": working_days,
                "reference_date": reference_date.strftime('%Y-%m-%d') if period != 'custom' else None,
                "period_description": _get_period_description(period, start_date, end_date, reference_date)
            }

            # Core attendance statistics
            attendance_stats = {
                "present_days": present_days,
                "absent_days": absent_days,
                "late_days": late_days,
                "on_leave_days": on_leave_days,
                "attendance_rate": round(attendance_rate, 2),
                "punctuality_rate": round(punctuality_rate, 2),
                "consistency_score": _calculate_consistency_score(attendance_records, working_days)
            }

            # Time and productivity statistics
            time_stats = {
                "total_hours": round(total_hours, 2),
                "expected_hours": round(expected_hours, 2),
                "average_daily_hours": round(average_daily_hours, 2),
                "overtime_hours": round(overtime_hours, 2),
                "undertime_hours": round(undertime_hours, 2),
                "efficiency_rate": round(efficiency_rate, 2),
                "productivity_score": _calculate_productivity_score(total_hours, expected_hours, punctuality_rate)
            }

            # Shift compliance analysis
            shift_compliance = _analyze_shift_compliance(attendance_records, shift_assignments, dates_in_range)

            # Time patterns analysis (if requested)
            time_patterns = {}
            if include_patterns:
                time_patterns = _analyze_time_patterns(attendance_records, dates_in_range)

            # Department and company comparisons (if requested)
            comparisons = {}
            if include_comparisons:
                comparisons = _get_employee_comparisons(session, employee, attendance_rate, punctuality_rate,
                                                      efficiency_rate, start_date, end_date)

            # Trend analysis (if requested)
            trends = {}
            if include_trends and period in ['monthly', 'annual']:
                trends = _analyze_attendance_trends(session, employee_id, start_date, end_date, period)

            # Detailed daily records (if requested)
            daily_details = []
            if include_details:
                daily_details = _get_daily_attendance_details(attendance_records, dates_in_range, shift_assignments)

            # Build comprehensive response
            response_data = {
                "status": "success",
                "employee": employee_info,
                "period": period_info,
                "statistics": {
                    "attendance": attendance_stats,
                    "time": time_stats,
                    "shift_compliance": shift_compliance
                }
            }

            # Add optional sections based on parameters
            if include_patterns:
                response_data["time_patterns"] = time_patterns

            if include_comparisons:
                response_data["comparisons"] = comparisons

            if include_trends:
                response_data["trends"] = trends

            if include_details:
                response_data["daily_details"] = daily_details

            # Add AI-ready insights
            response_data["ai_insights"] = _generate_ai_insights(
                attendance_stats, time_stats, shift_compliance, time_patterns, comparisons
            )

            return jsonify(response_data), 200

    except ValueError as e:
        app.logger.error(f"Value error in get_employee_attendance_statistics: {str(e)}")
        return jsonify({"message": f"Invalid parameter: {str(e)}"}), 400
    except Exception as e:
        app.logger.error(f"Error in get_employee_attendance_statistics: {str(e)}")
        return jsonify({"message": "An error occurred while processing the request"}), 500


# Helper functions for employee statistics
def _calculate_total_possible_attendance(session, employees, start_date, end_date):
    """
    Calculate total possible attendance based on employee shift assignments.
    For employees with shifts: use shift working days
    For employees without shifts: default to 7 days per week
    """
    total_possible = 0
    dates_in_range = DateHelper.get_dates_in_range(start_date, end_date)

    for employee in employees:
        # Get employee's shift assignments for the period
        from application.Models.employees import EmployeeShift
        shift_assignments = session.query(EmployeeShift).filter(
            EmployeeShift.employee_id == employee.employee_id,
            EmployeeShift.is_active == True,
            EmployeeShift.effective_start_date <= end_date,
            (EmployeeShift.effective_end_date.is_(None)) | (EmployeeShift.effective_end_date >= start_date)
        ).all()

        if shift_assignments:
            # Calculate working days based on shift assignments
            employee_working_days = _calculate_working_days_for_employee(dates_in_range, shift_assignments)
        else:
            # No shift assignment - default to all days in the period (7 days/week)
            employee_working_days = len(dates_in_range)

        total_possible += employee_working_days

    return total_possible

def _calculate_working_days_for_employee(dates_in_range, shift_assignments):
    """Calculate working days for an employee based on their shift assignments."""
    if not shift_assignments:
        # Default to weekdays if no shifts assigned (Monday=0 to Friday=4)
        return len([d for d in dates_in_range if d.weekday() < 5])

    working_days = 0
    for date in dates_in_range:
        is_working_day = False

        # Check if employee has an active shift assignment on this day
        for assignment in shift_assignments:
            if (assignment.effective_start_date <= date and
                (assignment.effective_end_date is None or assignment.effective_end_date >= date)):

                shift = assignment.shift
                if shift and hasattr(shift, 'working_days') and shift.working_days:
                    # Parse working days from shift (e.g., "1,2,3,4,5" for Mon-Fri)
                    # Monday=1, Tuesday=2, ..., Sunday=7
                    day_number = date.weekday() + 1  # Convert Python weekday (0=Monday) to 1-7 format
                    working_days_list = [int(day.strip()) for day in shift.working_days.split(',') if day.strip().isdigit()]
                    if day_number in working_days_list:
                        is_working_day = True
                        break
                else:
                    # If shift exists but no working_days specified, default to weekdays
                    if date.weekday() < 5:  # Monday=0 to Friday=4
                        is_working_day = True
                        break

        # If no shift assignment found, check if it's a weekday
        if not is_working_day and not shift_assignments:
            if date.weekday() < 5:
                is_working_day = True

        # If still no shift assignment but we have assignments, default to weekdays
        if not is_working_day and shift_assignments:
            # Check if any assignment covers this period (even if not this specific date)
            has_any_assignment = any(
                assignment.effective_start_date <= date and
                (assignment.effective_end_date is None or assignment.effective_end_date >= date)
                for assignment in shift_assignments
            )
            if has_any_assignment and date.weekday() < 5:
                is_working_day = True

        if is_working_day:
            working_days += 1

    return working_days


def _calculate_expected_hours_for_employee(dates_in_range, shift_assignments, working_days):
    """Calculate expected working hours for an employee based on their shifts."""
    if not shift_assignments:
        # Default to 8 hours per working day
        return working_days * 8.0

    total_expected_hours = 0.0

    for date in dates_in_range:
        # Skip weekends if no shift assignment covers this date
        if date.weekday() >= 5:  # Saturday=5, Sunday=6
            continue

        day_hours_added = False

        for assignment in shift_assignments:
            if (assignment.effective_start_date <= date and
                (assignment.effective_end_date is None or assignment.effective_end_date >= date)):

                shift = assignment.shift
                if shift:
                    # Use custom times if available, otherwise use shift defaults
                    start_time = assignment.custom_start_time or shift.start_time
                    end_time = assignment.custom_end_time or shift.end_time
                    break_duration = assignment.custom_break_duration or shift.break_duration or 0

                    if start_time and end_time:
                        # Calculate hours for this day
                        start_datetime = datetime.combine(date, start_time)
                        end_datetime = datetime.combine(date, end_time)

                        # Handle overnight shifts
                        if end_time < start_time:
                            end_datetime += timedelta(days=1)

                        shift_hours = (end_datetime - start_datetime).total_seconds() / 3600
                        shift_hours -= break_duration / 60.0  # Convert break minutes to hours
                        total_expected_hours += max(0, shift_hours)
                        day_hours_added = True
                        break
                else:
                    # Default to 8 hours if shift exists but no details
                    total_expected_hours += 8.0
                    day_hours_added = True
                    break

        # If no shift assignment found for this weekday, add default 8 hours
        if not day_hours_added and date.weekday() < 5:
            total_expected_hours += 8.0

    return total_expected_hours


def _get_period_description(period, start_date, end_date, reference_date):
    """Generate a human-readable description of the period."""
    if period == 'daily':
        return start_date.strftime('%A, %B %d, %Y')
    elif period == 'weekly':
        return f"Week of {start_date.strftime('%B %d, %Y')}"
    elif period == 'monthly':
        return start_date.strftime('%B %Y')
    elif period == 'annual':
        return start_date.strftime('%Y')
    elif period == 'custom':
        return f"{start_date.strftime('%B %d, %Y')} to {end_date.strftime('%B %d, %Y')}"
    else:
        return f"{start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}"


def _calculate_consistency_score(attendance_records, working_days):
    """Calculate a consistency score based on attendance patterns."""
    if working_days == 0:
        return 0.0

    # Create a list of attendance status for each working day
    attendance_pattern = []
    record_dates = {record.date: record.status for record in attendance_records}

    # Calculate consistency based on regularity of attendance
    present_days = len([r for r in attendance_records if r.status in ['present', 'late']])
    consistency_base = (present_days / working_days) * 100

    # Penalize for irregular patterns (many late days)
    late_days = len([r for r in attendance_records if r.status == 'late'])
    late_penalty = (late_days / working_days) * 20 if working_days > 0 else 0

    consistency_score = max(0, consistency_base - late_penalty)
    return round(consistency_score, 2)


def _calculate_productivity_score(total_hours, expected_hours, punctuality_rate):
    """Calculate a productivity score based on hours worked and punctuality."""
    if expected_hours == 0:
        return 0.0

    # Base score from hours efficiency
    hours_efficiency = min(100, (total_hours / expected_hours) * 100)

    # Weight: 70% hours efficiency, 30% punctuality
    productivity_score = (hours_efficiency * 0.7) + (punctuality_rate * 0.3)

    return round(productivity_score, 2)


def _analyze_shift_compliance(attendance_records, shift_assignments, dates_in_range):
    """Analyze how well the employee complies with their assigned shifts."""
    if not shift_assignments:
        return {
            "has_shifts": False,
            "compliance_rate": 0.0,
            "early_arrivals": 0,
            "late_arrivals": 0,
            "early_departures": 0,
            "late_departures": 0,
            "shift_violations": []
        }

    compliance_data = {
        "has_shifts": True,
        "total_shift_days": 0,
        "compliant_days": 0,
        "early_arrivals": 0,
        "late_arrivals": 0,
        "early_departures": 0,
        "late_departures": 0,
        "shift_violations": []
    }

    # Create a map of attendance records by date
    attendance_map = {record.date: record for record in attendance_records}

    for date in dates_in_range:
        # Find the shift assignment for this date
        current_assignment = None
        for assignment in shift_assignments:
            # Convert effective dates to datetime
            app.logger.info(f"The type of date is {type(date)} and assignment is {type(assignment.effective_start_date)}")
            if (assignment.effective_start_date <= date and
                (assignment.effective_end_date is None or assignment.effective_end_date >= date)):
                current_assignment = assignment
                break

        if current_assignment and date in attendance_map:
            compliance_data["total_shift_days"] += 1
            record = attendance_map[date]
            shift = current_assignment.shift

            # Get expected times (custom or shift default)
            expected_start_time = current_assignment.custom_start_time or shift.start_time
            expected_end_time = current_assignment.custom_end_time or shift.end_time

            # Convert to datetime using the current date
            expected_start = datetime.combine(date, expected_start_time)
            expected_end = datetime.combine(date, expected_end_time)

            if expected_start and expected_end and record.check_in_time and record.check_out_time:
                app.logger.info(f"the type of expected_start is {type(expected_start)} and expected_end is {type(expected_end)}")
                # Check arrival compliance
                if record.check_in_time <= expected_start:
                    compliance_data["early_arrivals"] += 1
                elif record.check_in_time > expected_start:
                    compliance_data["late_arrivals"] += 1
                    compliance_data["shift_violations"].append({
                        "date": date.strftime('%Y-%m-%d'),
                        "type": "late_arrival",
                        "expected": expected_start.strftime('%H:%M'),
                        "actual": record.check_in_time.strftime('%H:%M'),
                        "minutes_late": int((record.check_in_time.hour * 60 + record.check_in_time.minute) -
                                          (expected_start.hour * 60 + expected_start.minute))
                    })

                # Check departure compliance
                if record.check_out_time >= expected_end:
                    compliance_data["late_departures"] += 1
                elif record.check_out_time < expected_end:
                    compliance_data["early_departures"] += 1
                    compliance_data["shift_violations"].append({
                        "date": date.strftime('%Y-%m-%d'),
                        "type": "early_departure",
                        "expected": expected_end.strftime('%H:%M'),
                        "actual": record.check_out_time.strftime('%H:%M'),
                        "minutes_early": int((expected_end.hour * 60 + expected_end.minute) -
                                           (record.check_out_time.hour * 60 + record.check_out_time.minute))
                    })

                # Count as compliant if both arrival and departure are within tolerance
                if (record.check_in_time <= expected_start or
                    (record.check_in_time.hour * 60 + record.check_in_time.minute) -
                    (expected_start.hour * 60 + expected_start.minute) <= 15):  # 15 min tolerance
                    if record.check_out_time >= expected_end:
                        compliance_data["compliant_days"] += 1

    # Calculate compliance rate
    if compliance_data["total_shift_days"] > 0:
        compliance_data["compliance_rate"] = round(
            (compliance_data["compliant_days"] / compliance_data["total_shift_days"]) * 100, 2
        )
    else:
        compliance_data["compliance_rate"] = 0.0

    return compliance_data


def _analyze_time_patterns(attendance_records, dates_in_range):
    """Analyze time-based patterns in employee attendance."""
    if not attendance_records:
        return {
            "average_check_in_time": None,
            "average_check_out_time": None,
            "most_common_check_in_hour": None,
            "most_common_check_out_hour": None,
            "day_of_week_patterns": {},
            "time_consistency": 0.0
        }

    # Filter records with valid times
    valid_records = [r for r in attendance_records if r.check_in_time and r.check_out_time]

    if not valid_records:
        return {
            "average_check_in_time": None,
            "average_check_out_time": None,
            "most_common_check_in_hour": None,
            "most_common_check_out_hour": None,
            "day_of_week_patterns": {},
            "time_consistency": 0.0
        }

    # Calculate average times
    total_check_in_minutes = sum(r.check_in_time.hour * 60 + r.check_in_time.minute for r in valid_records)
    total_check_out_minutes = sum(r.check_out_time.hour * 60 + r.check_out_time.minute for r in valid_records)

    avg_check_in_minutes = total_check_in_minutes // len(valid_records)
    avg_check_out_minutes = total_check_out_minutes // len(valid_records)

    avg_check_in_time = f"{avg_check_in_minutes // 60:02d}:{avg_check_in_minutes % 60:02d}"
    avg_check_out_time = f"{avg_check_out_minutes // 60:02d}:{avg_check_out_minutes % 60:02d}"

    # Find most common hours
    check_in_hours = [r.check_in_time.hour for r in valid_records]
    check_out_hours = [r.check_out_time.hour for r in valid_records]

    from collections import Counter
    most_common_check_in = Counter(check_in_hours).most_common(1)
    most_common_check_out = Counter(check_out_hours).most_common(1)

    # Day of week patterns
    day_patterns = {}
    for record in valid_records:
        day_name = record.date.strftime('%A')
        if day_name not in day_patterns:
            day_patterns[day_name] = {
                "count": 0,
                "avg_check_in": 0,
                "avg_check_out": 0,
                "total_hours": 0.0
            }

        day_patterns[day_name]["count"] += 1
        day_patterns[day_name]["avg_check_in"] += record.check_in_time.hour * 60 + record.check_in_time.minute
        day_patterns[day_name]["avg_check_out"] += record.check_out_time.hour * 60 + record.check_out_time.minute
        if record.total_hours:
            day_patterns[day_name]["total_hours"] += record.total_hours

    # Calculate averages for each day
    for day in day_patterns:
        count = day_patterns[day]["count"]
        if count > 0:
            avg_in = day_patterns[day]["avg_check_in"] // count
            avg_out = day_patterns[day]["avg_check_out"] // count
            day_patterns[day]["avg_check_in"] = f"{avg_in // 60:02d}:{avg_in % 60:02d}"
            day_patterns[day]["avg_check_out"] = f"{avg_out // 60:02d}:{avg_out % 60:02d}"
            day_patterns[day]["avg_hours"] = round(day_patterns[day]["total_hours"] / count, 2)

    # Calculate time consistency (standard deviation of check-in times)
    if len(valid_records) > 1:
        check_in_minutes = [r.check_in_time.hour * 60 + r.check_in_time.minute for r in valid_records]
        mean_minutes = sum(check_in_minutes) / len(check_in_minutes)
        variance = sum((x - mean_minutes) ** 2 for x in check_in_minutes) / len(check_in_minutes)
        std_dev = variance ** 0.5
        # Convert to consistency score (lower std_dev = higher consistency)
        time_consistency = max(0, 100 - (std_dev / 2))  # Normalize to 0-100 scale
    else:
        time_consistency = 100.0

    return {
        "average_check_in_time": avg_check_in_time,
        "average_check_out_time": avg_check_out_time,
        "most_common_check_in_hour": most_common_check_in[0][0] if most_common_check_in else None,
        "most_common_check_out_hour": most_common_check_out[0][0] if most_common_check_out else None,
        "day_of_week_patterns": day_patterns,
        "time_consistency": round(time_consistency, 2)
    }


def _get_employee_comparisons(session, employee, attendance_rate, punctuality_rate, efficiency_rate, start_date, end_date):
    """Get department and company-wide comparisons for the employee."""
    comparisons = {
        "department": {
            "name": employee.department.name if employee.department else "No Department",
            "employee_count": 0,
            "avg_attendance_rate": 0.0,
            "avg_punctuality_rate": 0.0,
            "avg_efficiency_rate": 0.0,
            "employee_rank": 0
        },
        "company": {
            "total_employees": 0,
            "avg_attendance_rate": 0.0,
            "avg_punctuality_rate": 0.0,
            "avg_efficiency_rate": 0.0,
            "employee_rank": 0
        }
    }

    try:
        # Get department statistics
        if employee.department_id:
            dept_employees = session.query(Employee).filter(
                Employee.department_id == employee.department_id,
                Employee.status == 'active'
            ).all()

            if dept_employees:
                dept_stats = _calculate_department_averages(session, dept_employees, start_date, end_date)
                comparisons["department"].update(dept_stats)

                # Calculate accurate employee rank in department
                dept_attendance_rates = [dept_stats.get("employee_rates", {}).get(str(emp.employee_id), 0)
                                       for emp in dept_employees]
                employee_rank = _calculate_accurate_employee_rank(attendance_rate, dept_attendance_rates)
                comparisons["department"]["employee_rank"] = employee_rank

        # Get company statistics
        all_employees = session.query(Employee).filter(Employee.status == 'active').all()
        if all_employees:
            company_stats = _calculate_company_averages(session, all_employees, start_date, end_date)
            comparisons["company"].update(company_stats)

            # Calculate accurate employee rank in company
            company_attendance_rates = [company_stats.get("employee_rates", {}).get(str(emp.employee_id), 0)
                                      for emp in all_employees]
            employee_rank = _calculate_accurate_employee_rank(attendance_rate, company_attendance_rates)
            comparisons["company"]["employee_rank"] = employee_rank

    except Exception as e:
        app.logger.error(f"Error calculating comparisons: {str(e)}")

    return comparisons


def _calculate_accurate_employee_rank(employee_rate, all_rates):
    """
    Calculate accurate employee rank handling ties properly.
    Returns the rank where employees with better performance get lower rank numbers.
    Handles ties by giving the best possible rank (standard competition ranking).

    Examples:
    - Rates: [95, 90, 90, 85, 80], Employee rate: 90 -> Rank: 2 (tied for 2nd)
    - Rates: [95, 90, 90, 85, 80], Employee rate: 85 -> Rank: 4 (4th place)
    - Rates: [95, 90, 90, 85, 80], Employee rate: 100 -> Rank: 1 (1st place)
    """
    if not all_rates:
        return 1

    # Handle edge case where employee_rate is None or invalid
    if employee_rate is None:
        return len(all_rates)

    # Convert to float to handle any string/decimal issues
    try:
        employee_rate = float(employee_rate)
        all_rates = [float(rate) for rate in all_rates if rate is not None]
    except (ValueError, TypeError):
        app.logger.warning(f"Invalid attendance rate data: employee_rate={employee_rate}, all_rates sample={all_rates[:5]}")
        return len(all_rates)

    # Count employees with strictly better performance
    better_performers = len([rate for rate in all_rates if rate > employee_rate])

    # Rank is the number of better performers + 1
    # This handles ties correctly: if 3 people are tied for 2nd place, they all get rank 2
    return better_performers + 1


def _calculate_department_averages(session, dept_employees, start_date, end_date):
    """Calculate average statistics for a department with optimized queries."""
    if not dept_employees:
        return {"employee_count": 0, "avg_attendance_rate": 0.0, "avg_punctuality_rate": 0.0, "avg_efficiency_rate": 0.0, "employee_rates": {}}

    # Optimize: Get all attendance records for department employees in one query
    dept_employee_ids = [emp.employee_id for emp in dept_employees]
    all_attendance_records = session.query(Attendance).filter(
        Attendance.employee_id.in_(dept_employee_ids),
        Attendance.date.between(start_date, end_date)
    ).all()

    # Group attendance records by employee
    attendance_by_employee = {}
    for record in all_attendance_records:
        emp_id = record.employee_id
        if emp_id not in attendance_by_employee:
            attendance_by_employee[emp_id] = []
        attendance_by_employee[emp_id].append(record)

    # Optimize: Get all shift assignments for department employees in one query
    from application.Models.employees import EmployeeShift
    all_shift_assignments = session.query(EmployeeShift).filter(
        EmployeeShift.employee_id.in_(dept_employee_ids),
        EmployeeShift.is_active == True,
        EmployeeShift.effective_start_date <= end_date,
        (EmployeeShift.effective_end_date.is_(None)) | (EmployeeShift.effective_end_date >= start_date)
    ).all()

    # Group shift assignments by employee
    shifts_by_employee = {}
    for shift in all_shift_assignments:
        emp_id = shift.employee_id
        if emp_id not in shifts_by_employee:
            shifts_by_employee[emp_id] = []
        shifts_by_employee[emp_id].append(shift)

    total_attendance_rate = 0.0
    total_punctuality_rate = 0.0
    employee_rates = {}
    dates_in_range = DateHelper.get_dates_in_range(start_date, end_date)

    for emp in dept_employees:
        # Get employee's attendance records (already fetched)
        attendance_records = attendance_by_employee.get(emp.employee_id, [])

        # Get employee's shift assignments (already fetched)
        shift_assignments = shifts_by_employee.get(emp.employee_id, [])

        # Calculate working days and attendance rates
        working_days = _calculate_working_days_for_employee(dates_in_range, shift_assignments)
        present_days = len([r for r in attendance_records if r.status in ['present', 'late']])
        late_days = len([r for r in attendance_records if r.status == 'late'])

        emp_attendance_rate = (present_days / working_days * 100) if working_days > 0 else 0
        emp_punctuality_rate = ((present_days - late_days) / present_days * 100) if present_days > 0 else 0

        total_attendance_rate += emp_attendance_rate
        total_punctuality_rate += emp_punctuality_rate
        employee_rates[str(emp.employee_id)] = emp_attendance_rate

    count = len(dept_employees)
    return {
        "employee_count": count,
        "avg_attendance_rate": round(total_attendance_rate / count, 2) if count > 0 else 0.0,
        "avg_punctuality_rate": round(total_punctuality_rate / count, 2) if count > 0 else 0.0,
        "avg_efficiency_rate": 0.0,  # Simplified for now
        "employee_rates": employee_rates
    }


def _calculate_company_averages(session, all_employees, start_date, end_date):
    """Calculate average statistics for the entire company with optimized queries."""
    if not all_employees:
        return {"total_employees": 0, "avg_attendance_rate": 0.0, "avg_punctuality_rate": 0.0, "avg_efficiency_rate": 0.0, "employee_rates": {}}

    # Optimize: Get all attendance records for company employees in one query
    all_employee_ids = [emp.employee_id for emp in all_employees]
    all_attendance_records = session.query(Attendance).filter(
        Attendance.employee_id.in_(all_employee_ids),
        Attendance.date.between(start_date, end_date)
    ).all()

    # Group attendance records by employee
    attendance_by_employee = {}
    for record in all_attendance_records:
        emp_id = record.employee_id
        if emp_id not in attendance_by_employee:
            attendance_by_employee[emp_id] = []
        attendance_by_employee[emp_id].append(record)

    # Optimize: Get all shift assignments for company employees in one query
    from application.Models.employees import EmployeeShift
    all_shift_assignments = session.query(EmployeeShift).filter(
        EmployeeShift.employee_id.in_(all_employee_ids),
        EmployeeShift.is_active == True,
        EmployeeShift.effective_start_date <= end_date,
        (EmployeeShift.effective_end_date.is_(None)) | (EmployeeShift.effective_end_date >= start_date)
    ).all()

    # Group shift assignments by employee
    shifts_by_employee = {}
    for shift in all_shift_assignments:
        emp_id = shift.employee_id
        if emp_id not in shifts_by_employee:
            shifts_by_employee[emp_id] = []
        shifts_by_employee[emp_id].append(shift)

    total_attendance_rate = 0.0
    total_punctuality_rate = 0.0
    employee_rates = {}
    dates_in_range = DateHelper.get_dates_in_range(start_date, end_date)

    for emp in all_employees:
        # Get employee's attendance records (already fetched)
        attendance_records = attendance_by_employee.get(emp.employee_id, [])

        # Get employee's shift assignments (already fetched)
        shift_assignments = shifts_by_employee.get(emp.employee_id, [])

        # Calculate working days and attendance rates
        working_days = _calculate_working_days_for_employee(dates_in_range, shift_assignments)
        present_days = len([r for r in attendance_records if r.status in ['present', 'late']])
        late_days = len([r for r in attendance_records if r.status == 'late'])

        emp_attendance_rate = (present_days / working_days * 100) if working_days > 0 else 0
        emp_punctuality_rate = ((present_days - late_days) / present_days * 100) if present_days > 0 else 0

        total_attendance_rate += emp_attendance_rate
        total_punctuality_rate += emp_punctuality_rate
        employee_rates[str(emp.employee_id)] = emp_attendance_rate

    count = len(all_employees)
    return {
        "total_employees": count,
        "avg_attendance_rate": round(total_attendance_rate / count, 2) if count > 0 else 0.0,
        "avg_punctuality_rate": round(total_punctuality_rate / count, 2) if count > 0 else 0.0,
        "avg_efficiency_rate": 0.0,  # Simplified for now
        "employee_rates": employee_rates
    }


def _analyze_attendance_trends(session, employee_id, start_date, end_date, period):
    """Analyze attendance trends over time for the employee."""
    trends = {
        "trend_direction": "stable",
        "trend_strength": 0.0,
        "period_comparison": [],
        "improvement_areas": [],
        "recommendations": []
    }

    try:
        if period == 'monthly':
            # Compare weeks within the month
            weeks = []
            current_date = start_date
            while current_date <= end_date:
                week_start = current_date
                week_end = min(current_date + timedelta(days=6), end_date)

                week_records = session.query(Attendance).filter(
                    Attendance.employee_id == employee_id,
                    Attendance.date.between(week_start, week_end)
                ).all()

                week_working_days = len(DateHelper.get_dates_in_range(week_start, week_end))
                week_present_days = len([r for r in week_records if r.status in ['present', 'late']])
                week_attendance_rate = (week_present_days / week_working_days * 100) if week_working_days > 0 else 0

                weeks.append({
                    "period": f"Week {len(weeks) + 1}",
                    "start_date": week_start.strftime('%Y-%m-%d'),
                    "end_date": week_end.strftime('%Y-%m-%d'),
                    "attendance_rate": round(week_attendance_rate, 2),
                    "present_days": week_present_days,
                    "working_days": week_working_days
                })

                current_date += timedelta(days=7)

            trends["period_comparison"] = weeks

            # Calculate trend direction
            if len(weeks) >= 2:
                rates = [w["attendance_rate"] for w in weeks]
                if rates[-1] > rates[0]:
                    trends["trend_direction"] = "improving"
                elif rates[-1] < rates[0]:
                    trends["trend_direction"] = "declining"

                # Calculate trend strength (simple linear correlation)
                if len(rates) > 2:
                    x_values = list(range(len(rates)))
                    correlation = _calculate_correlation(x_values, rates)
                    trends["trend_strength"] = abs(correlation)

        elif period == 'annual':
            # Compare months within the year
            months = []
            current_month = start_date.replace(day=1)

            while current_month <= end_date:
                # Get last day of current month
                if current_month.month == 12:
                    month_end = current_month.replace(year=current_month.year + 1, month=1, day=1) - timedelta(days=1)
                else:
                    month_end = current_month.replace(month=current_month.month + 1, day=1) - timedelta(days=1)

                month_end = min(month_end, end_date)

                month_records = session.query(Attendance).filter(
                    Attendance.employee_id == employee_id,
                    Attendance.date.between(current_month, month_end)
                ).all()

                month_working_days = len(DateHelper.get_dates_in_range(current_month, month_end))
                month_present_days = len([r for r in month_records if r.status in ['present', 'late']])
                month_attendance_rate = (month_present_days / month_working_days * 100) if month_working_days > 0 else 0

                months.append({
                    "period": current_month.strftime('%B %Y'),
                    "start_date": current_month.strftime('%Y-%m-%d'),
                    "end_date": month_end.strftime('%Y-%m-%d'),
                    "attendance_rate": round(month_attendance_rate, 2),
                    "present_days": month_present_days,
                    "working_days": month_working_days
                })

                # Move to next month
                if current_month.month == 12:
                    current_month = current_month.replace(year=current_month.year + 1, month=1)
                else:
                    current_month = current_month.replace(month=current_month.month + 1)

            trends["period_comparison"] = months

    except Exception as e:
        app.logger.error(f"Error analyzing trends: {str(e)}")

    return trends


def _calculate_correlation(x_values, y_values):
    """Calculate simple correlation coefficient."""
    if len(x_values) != len(y_values) or len(x_values) < 2:
        return 0.0

    n = len(x_values)
    sum_x = sum(x_values)
    sum_y = sum(y_values)
    sum_xy = sum(x * y for x, y in zip(x_values, y_values))
    sum_x2 = sum(x * x for x in x_values)
    sum_y2 = sum(y * y for y in y_values)

    numerator = n * sum_xy - sum_x * sum_y
    denominator = ((n * sum_x2 - sum_x * sum_x) * (n * sum_y2 - sum_y * sum_y)) ** 0.5

    if denominator == 0:
        return 0.0

    return numerator / denominator


def _get_daily_attendance_details(attendance_records, dates_in_range, shift_assignments):
    """Get detailed daily attendance records with shift information."""
    daily_details = []
    attendance_map = {record.date: record for record in attendance_records}

    for date in dates_in_range:
        # Find shift assignment for this date
        current_shift = None
        for assignment in shift_assignments:
            if (assignment.effective_start_date <= date and
                (assignment.effective_end_date is None or assignment.effective_end_date >= date)):
                current_shift = assignment
                break

        # Get attendance record for this date
        record = attendance_map.get(date)

        detail = {
            "date": date.strftime('%Y-%m-%d'),
            "day_of_week": date.strftime('%A'),
            "status": record.status if record else "absent",
            "check_in_time": record.check_in_time.strftime('%H:%M:%S') if record and record.check_in_time else None,
            "check_out_time": record.check_out_time.strftime('%H:%M:%S') if record and record.check_out_time else None,
            "total_hours": record.total_hours if record else 0.0,
            "break_duration": record.break_duration if record else 0,
            "overtime_hours": record.overtime_hours if record else 0.0,
            "shift_info": {
                "has_shift": current_shift is not None,
                "shift_name": current_shift.shift.name if current_shift and current_shift.shift else None,
                "expected_start": current_shift.custom_start_time.strftime('%H:%M:%S') if current_shift and current_shift.custom_start_time
                                else current_shift.shift.start_time.strftime('%H:%M:%S') if current_shift and current_shift.shift and current_shift.shift.start_time else None,
                "expected_end": current_shift.custom_end_time.strftime('%H:%M:%S') if current_shift and current_shift.custom_end_time
                              else current_shift.shift.end_time.strftime('%H:%M:%S') if current_shift and current_shift.shift and current_shift.shift.end_time else None,
                "expected_hours": _calculate_expected_hours_for_single_day(current_shift) if current_shift else 8.0
            },
            "compliance": {
                "on_time_arrival": False,
                "on_time_departure": False,
                "hours_compliance": False,
                "overall_compliant": False
            }
        }

        # Calculate compliance if we have both record and shift
        if record and current_shift and current_shift.shift:
            expected_start = current_shift.custom_start_time or current_shift.shift.start_time
            expected_end = current_shift.custom_end_time or current_shift.shift.end_time

            if expected_start and expected_end and record.check_in_time and record.check_out_time:
                detail["compliance"]["on_time_arrival"] = record.check_in_time <= expected_start
                detail["compliance"]["on_time_departure"] = record.check_out_time >= expected_end

                expected_hours = detail["shift_info"]["expected_hours"]
                detail["compliance"]["hours_compliance"] = record.total_hours >= (expected_hours * 0.9) if record.total_hours else False

                detail["compliance"]["overall_compliant"] = (
                    detail["compliance"]["on_time_arrival"] and
                    detail["compliance"]["on_time_departure"] and
                    detail["compliance"]["hours_compliance"]
                )

        daily_details.append(detail)

    return daily_details


def _calculate_expected_hours_for_single_day(shift_assignment):
    """Calculate expected hours for a single day based on shift assignment."""
    if not shift_assignment or not shift_assignment.shift:
        return 8.0

    start_time = shift_assignment.custom_start_time or shift_assignment.shift.start_time
    end_time = shift_assignment.custom_end_time or shift_assignment.shift.end_time
    break_duration = shift_assignment.custom_break_duration or shift_assignment.shift.break_duration or 0

    if not start_time or not end_time:
        return 8.0

    # Calculate hours
    start_minutes = start_time.hour * 60 + start_time.minute
    end_minutes = end_time.hour * 60 + end_time.minute

    # Handle overnight shifts
    if end_minutes < start_minutes:
        end_minutes += 24 * 60

    total_minutes = end_minutes - start_minutes - break_duration
    return max(0, total_minutes / 60.0)


def _generate_ai_insights(attendance_stats, time_stats, shift_compliance, time_patterns, comparisons):
    """Generate AI-ready insights and recommendations for the employee."""
    insights = {
        "performance_summary": {
            "overall_score": 0.0,
            "strengths": [],
            "areas_for_improvement": [],
            "risk_level": "low"
        },
        "behavioral_patterns": {
            "consistency_level": "high",
            "punctuality_trend": "stable",
            "work_hours_pattern": "regular",
            "anomalies": []
        },
        "recommendations": {
            "immediate_actions": [],
            "long_term_goals": [],
            "manager_actions": []
        },
        "predictive_indicators": {
            "attendance_risk": "low",
            "burnout_risk": "low",
            "performance_trend": "stable"
        }
    }

    # Calculate overall performance score
    attendance_weight = 0.4
    punctuality_weight = 0.3
    efficiency_weight = 0.3

    overall_score = (
        attendance_stats["attendance_rate"] * attendance_weight +
        attendance_stats["punctuality_rate"] * punctuality_weight +
        time_stats["efficiency_rate"] * efficiency_weight
    )
    insights["performance_summary"]["overall_score"] = round(overall_score, 2)

    # Determine strengths and areas for improvement
    if attendance_stats["attendance_rate"] >= 95:
        insights["performance_summary"]["strengths"].append("Excellent attendance record")
    elif attendance_stats["attendance_rate"] < 85:
        insights["performance_summary"]["areas_for_improvement"].append("Improve attendance consistency")

    if attendance_stats["punctuality_rate"] >= 90:
        insights["performance_summary"]["strengths"].append("Very punctual")
    elif attendance_stats["punctuality_rate"] < 75:
        insights["performance_summary"]["areas_for_improvement"].append("Improve punctuality")

    if time_stats["efficiency_rate"] >= 100:
        insights["performance_summary"]["strengths"].append("Meets or exceeds expected work hours")
    elif time_stats["efficiency_rate"] < 90:
        insights["performance_summary"]["areas_for_improvement"].append("Increase productive work hours")

    # Determine risk level
    if attendance_stats["attendance_rate"] < 80 or attendance_stats["punctuality_rate"] < 70:
        insights["performance_summary"]["risk_level"] = "high"
        insights["predictive_indicators"]["attendance_risk"] = "high"
    elif attendance_stats["attendance_rate"] < 90 or attendance_stats["punctuality_rate"] < 85:
        insights["performance_summary"]["risk_level"] = "medium"
        insights["predictive_indicators"]["attendance_risk"] = "medium"

    # Analyze behavioral patterns
    if time_patterns.get("time_consistency", 0) >= 80:
        insights["behavioral_patterns"]["consistency_level"] = "high"
    elif time_patterns.get("time_consistency", 0) >= 60:
        insights["behavioral_patterns"]["consistency_level"] = "medium"
    else:
        insights["behavioral_patterns"]["consistency_level"] = "low"

    # Generate recommendations
    if attendance_stats["attendance_rate"] < 90:
        insights["recommendations"]["immediate_actions"].append("Schedule one-on-one meeting to discuss attendance concerns")
        insights["recommendations"]["manager_actions"].append("Implement attendance improvement plan")

    if attendance_stats["punctuality_rate"] < 85:
        insights["recommendations"]["immediate_actions"].append("Review and adjust work schedule if needed")
        insights["recommendations"]["long_term_goals"].append("Achieve 95% punctuality rate within 3 months")

    if time_stats["efficiency_rate"] < 90:
        insights["recommendations"]["immediate_actions"].append("Analyze workload and time management practices")
        insights["recommendations"]["long_term_goals"].append("Optimize work processes to meet expected hours")

    # Shift compliance insights
    if shift_compliance.get("has_shifts") and shift_compliance.get("compliance_rate", 0) < 85:
        insights["recommendations"]["immediate_actions"].append("Review shift requirements and provide additional training")
        insights["behavioral_patterns"]["anomalies"].append("Frequent shift compliance violations")

    # Overtime analysis
    if time_stats["overtime_hours"] > (time_stats["expected_hours"] * 0.2):  # More than 20% overtime
        insights["predictive_indicators"]["burnout_risk"] = "medium"
        insights["recommendations"]["manager_actions"].append("Monitor workload to prevent burnout")

    return insights


@attendance_api.route('/api/attendance/cleanup-old-records', methods=['POST'])
@token_required
@roles_required(['admin', 'hr_manager'])
def cleanup_old_attendance_records():
    """
    Clean up old open attendance records for a company.

    This endpoint can be used to manually trigger cleanup of old open attendance records
    that might be blocking new attendance creation.

    Request Body:
    {
        "company_id": "required - company ID",
        "max_hours": "optional - maximum hours for open records (default: 48)",
        "employee_id": "optional - specific employee ID to clean up"
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({"message": "Request body is required"}), 400

        company_id = data.get('company_id')
        if not company_id:
            return jsonify({"message": "Company ID is required"}), 400

        max_hours = data.get('max_hours', 48)
        employee_id = data.get('employee_id')

        # Validate max_hours
        if not isinstance(max_hours, (int, float)) or max_hours <= 0:
            return jsonify({"message": "max_hours must be a positive number"}), 400

        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404

        # Connect to the database and perform cleanup
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # If employee_id is provided, validate it exists
            if employee_id:
                employee = Employee.get_employee_by_id(session, employee_id)
                if not employee:
                    return jsonify({"message": f"Employee with ID {employee_id} not found"}), 404

            # Perform the cleanup
            cleaned_count = Attendance.cleanup_old_open_records(
                session,
                employee_id=employee_id,
                max_hours=max_hours
            )

            return jsonify({
                "status": "success",
                "message": f"Cleanup completed successfully",
                "cleaned_records": cleaned_count,
                "company_id": company_id,
                "employee_id": employee_id,
                "max_hours": max_hours
            }), 200

    except Exception as e:
        app.logger.error(f"Error in cleanup_old_attendance_records: {e}")
        return jsonify({
            "status": "error",
            "message": f"Error during cleanup: {str(e)}"
        }), 500


def _generate_attendance_pdf(company, attendance_records, start_date, end_date, employee_id, session):
    """Generate KaziSync-branded PDF report for attendance records."""
    buffer = io.BytesIO()

    # Create the PDF document
    doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=18)

    # Container for the 'Flowable' objects
    elements = []

    # Define KaziSync brand colors
    KAZISYNC_BLUE = colors.Color(0, 0.48, 1)      # #007BFF
    KAZISYNC_DARK = colors.Color(0.2, 0.2, 0.2)   # Dark gray
    KAZISYNC_LIGHT = colors.Color(0.96, 0.97, 0.98) # Light background
    KAZISYNC_SUCCESS = colors.Color(0.13, 0.69, 0.3) # Success green
    KAZISYNC_WARNING = colors.Color(1, 0.6, 0)     # Warning orange
    KAZISYNC_ERROR = colors.Color(0.9, 0.2, 0.2)   # Error red

    # Define styles
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle(
        'KaziSyncTitle',
        parent=styles['Heading1'],
        fontSize=24,
        fontName='Helvetica-Bold',
        textColor=KAZISYNC_BLUE,
        spaceAfter=20,
        alignment=1  # Center alignment
    )

    subtitle_style = ParagraphStyle(
        'KaziSyncSubtitle',
        parent=styles['Normal'],
        fontSize=12,
        fontName='Helvetica',
        textColor=KAZISYNC_DARK,
        spaceAfter=10,
        alignment=1  # Center alignment
    )

    # KaziSync branded header
    header_title = Paragraph("KaziSync Attendance Report", title_style)
    elements.append(header_title)

    header_subtitle = Paragraph("Modern HRMS for Teams of All Sizes", subtitle_style)
    elements.append(header_subtitle)

    elements.append(Spacer(1, 20))

    # Company information
    try:
        company_name = company.company_name if hasattr(company, 'company_name') else "Company Name"
    except Exception as e:
        app.logger.error(f"Error getting company name: {e}")
        company_name = "Company Name"
    try:
        company_phone = company.phone_number if hasattr(company, 'phone_number') else ""
    except Exception as e:
        app.logger.error(f"Error getting company contact info: {e}")
        company_phone = ""

    # Create company info table
    company_info = [['Company:', company_name]]
    if company_phone:
        company_info.append(['Phone:', company_phone])

    elements.append(Spacer(1, 12))

    # Add date range to company info
    if start_date and end_date:
        if start_date == end_date:
            date_range = start_date.strftime('%B %d, %Y')
        else:
            date_range = f"{start_date.strftime('%B %d, %Y')} to {end_date.strftime('%B %d, %Y')}"
    elif start_date:
        date_range = f"From {start_date.strftime('%B %d, %Y')}"
    elif end_date:
        date_range = f"Until {end_date.strftime('%B %d, %Y')}"
    else:
        date_range = "All Records"

    company_info.append(['Period:', date_range])

    # Add employee filter if specified
    if employee_id:
        employee = session.query(Employee).filter_by(employee_id=employee_id).first()
        if employee:
            employee_name = f"{employee.first_name} {employee.last_name}"
            company_info.append(['Employee:', employee_name])

    # Add generation timestamp
    generated_time = datetime.now().strftime('%B %d, %Y at %I:%M %p')
    company_info.append(['Generated:', generated_time])

    # Create styled info table
    info_table = Table(company_info, colWidths=[1.5*inch, 4*inch])
    info_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, -1), colors.white),
        ('TEXTCOLOR', (0, 0), (0, -1), KAZISYNC_DARK),
        ('TEXTCOLOR', (1, 0), (1, -1), KAZISYNC_DARK),
        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
        ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('GRID', (0, 0), (-1, -1), 0.5, colors.Color(0.9, 0.9, 0.9)),
        ('LEFTPADDING', (0, 0), (-1, -1), 8),
        ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ('TOPPADDING', (0, 0), (-1, -1), 6),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
    ]))

    elements.append(info_table)
    elements.append(Spacer(1, 20))

    # Create table data
    table_data = [
        ['Date', 'Employee', 'Check In', 'Check Out', 'Total Hours', 'Status', 'Late']
    ]

    if not attendance_records:
        # Add "No records found" row when no data
        table_data.append([
            'No attendance records found for the specified criteria',
            '', '', '', '', '', ''
        ])
    else:
        for record in attendance_records:
            # Get employee info
            employee = session.query(Employee).filter_by(employee_id=record.employee_id).first()
            employee_name = f"{employee.first_name} {employee.last_name}" if employee else "Unknown"

            # Format times
            check_in = record.check_in_time.strftime('%I:%M %p') if record.check_in_time else '-'
            check_out = record.check_out_time.strftime('%I:%M %p') if record.check_out_time else '-'

            # Calculate total hours
            total_hours = f"{record.total_hours:.2f}h" if record.total_hours else '-'

            # Status
            status = record.status.title() if record.status else 'Unknown'

            # Late indicator
            late_indicator = 'Yes' if record.status == 'late' else 'No'

            table_data.append([
                record.date.strftime('%m/%d/%Y'),
                employee_name,
                check_in,
                check_out,
                total_hours,
                status,
                late_indicator
            ])

    # Create table with KaziSync styling
    table = Table(table_data, colWidths=[1*inch, 1.5*inch, 1*inch, 1*inch, 1*inch, 0.8*inch, 0.7*inch])

    # Apply KaziSync brand styling
    table.setStyle(TableStyle([
        # Header row - KaziSync blue
        ('BACKGROUND', (0, 0), (-1, 0), KAZISYNC_BLUE),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 11),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 15),
        ('TOPPADDING', (0, 0), (-1, 0), 15),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),

        # Data rows - clean alternating
        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 1), (-1, -1), 9),
        ('ROWBACKGROUNDS', (0, 1), (-1, -1), [KAZISYNC_LIGHT, colors.white]),
        ('TEXTCOLOR', (0, 1), (-1, -1), KAZISYNC_DARK),

        # Borders - subtle and professional
        ('GRID', (0, 0), (-1, -1), 0.5, colors.Color(0.8, 0.8, 0.8)),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('LEFTPADDING', (0, 0), (-1, -1), 8),
        ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ('TOPPADDING', (0, 1), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 1), (-1, -1), 10),
    ]))

    # Handle special styling for "No records found" case
    if not attendance_records:
        # Style the "No records found" row
        table.setStyle(TableStyle([
            ('SPAN', (0, 1), (6, 1)),  # Merge all columns in the data row
            ('ALIGN', (0, 1), (6, 1), 'CENTER'),
            ('TEXTCOLOR', (0, 1), (6, 1), KAZISYNC_DARK),
            ('FONTNAME', (0, 1), (6, 1), 'Helvetica-Oblique'),
            ('FONTSIZE', (0, 1), (6, 1), 12),
            ('TOPPADDING', (0, 1), (6, 1), 20),
            ('BOTTOMPADDING', (0, 1), (6, 1), 20),
        ]))
    else:
        # Add status-based color coding for actual records
        for i, record in enumerate(attendance_records, 1):
            if record.status == 'present':
                table.setStyle(TableStyle([
                    ('TEXTCOLOR', (5, i), (5, i), KAZISYNC_SUCCESS),  # Green for present
                    ('FONTNAME', (5, i), (5, i), 'Helvetica-Bold'),
                ]))
            elif record.status == 'late':
                table.setStyle(TableStyle([
                    ('TEXTCOLOR', (5, i), (5, i), KAZISYNC_WARNING),  # Orange for late
                    ('FONTNAME', (5, i), (5, i), 'Helvetica-Bold'),
                ]))
            elif record.status == 'absent':
                table.setStyle(TableStyle([
                    ('TEXTCOLOR', (5, i), (5, i), KAZISYNC_ERROR),  # Red for absent
                    ('FONTNAME', (5, i), (5, i), 'Helvetica-Bold'),
                ]))

    elements.append(table)

    # Create comprehensive analytics summary based on report type
    summary_elements = []
    summary_elements.append(Spacer(1, 30))

    # Determine if this is a company-wide or employee-specific report
    is_employee_report = employee_id is not None

    if is_employee_report:
        # Generate individual employee analytics summary
        summary_elements.extend(_generate_employee_analytics_summary(
            company, attendance_records, start_date, end_date, employee_id, session,
            KAZISYNC_BLUE, KAZISYNC_DARK, KAZISYNC_SUCCESS, KAZISYNC_WARNING, KAZISYNC_ERROR
        ))
    else:
        # Generate executive company-wide analytics summary
        summary_elements.extend(_generate_executive_analytics_summary(
            company, attendance_records, start_date, end_date, session,
            KAZISYNC_BLUE, KAZISYNC_DARK, KAZISYNC_SUCCESS, KAZISYNC_WARNING, KAZISYNC_ERROR
        ))

    # Add KaziSync footer to summary elements
    summary_elements.append(Spacer(1, 30))

    footer_data = [
        ['Generated by KaziSync', f"www.kazisync.com"],
        ['Modern HRMS for Teams of All Sizes', '']
    ]

    footer_table = Table(footer_data, colWidths=[3*inch, 3*inch])
    footer_table.setStyle(TableStyle([
        ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 0), (-1, -1), 8),
        ('TEXTCOLOR', (0, 0), (0, -1), KAZISYNC_BLUE),
        ('TEXTCOLOR', (1, 0), (1, -1), KAZISYNC_BLUE),
        ('ALIGN', (0, 0), (0, -1), 'LEFT'),
        ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
    ]))

    summary_elements.append(footer_table)

    # Keep summary and footer together on the same page
    elements.append(KeepTogether(summary_elements))

    # Build PDF
    doc.build(elements)

    buffer.seek(0)
    return buffer


def _generate_executive_analytics_summary(company, attendance_records, start_date, end_date, session,
                                        KAZISYNC_BLUE, KAZISYNC_DARK, KAZISYNC_SUCCESS, KAZISYNC_WARNING, KAZISYNC_ERROR):
    """Generate comprehensive executive analytics summary for company-wide reports."""
    summary_elements = []

    # Get comprehensive company statistics
    try:
        # Fetch rich analytics data
        from application.Models.employees.department import Department

        # Filter attendance records to exclude future dates
        today = date.today()
        actual_end_date = min(end_date, today)
        filtered_records = [r for r in attendance_records if r.date <= actual_end_date]

        # Calculate shift-aware metrics for company
        company_metrics = _calculate_company_shift_aware_metrics(
            company, filtered_records, start_date, actual_end_date, session
        )

        # Extract metrics
        total_records = company_metrics['total_records']
        present_records = company_metrics['present_records']
        late_records = company_metrics['late_records']
        absent_records = company_metrics['absent_records']
        attendance_rate = company_metrics['attendance_rate']
        punctuality_rate = company_metrics['punctuality_rate']
        total_employees = company_metrics['total_employees']
        hours_analytics = company_metrics['hours_analytics']

        # Determine overall health score and status
        health_score, health_status, health_color = _calculate_company_health_score(
            attendance_rate, punctuality_rate, late_records, total_records,
            KAZISYNC_SUCCESS, KAZISYNC_WARNING, KAZISYNC_ERROR
        )

        # 1. EXECUTIVE SUMMARY SECTION
        exec_title = Paragraph("📈 Executive Attendance Analytics", ParagraphStyle(
            'ExecTitle', fontSize=16, fontName='Helvetica-Bold',
            textColor=KAZISYNC_BLUE, spaceAfter=15, alignment=1
        ))
        summary_elements.append(exec_title)

        # Comprehensive Executive Dashboard
        health_data = [
            ['🎯 Overall Health Score', f"{health_score}/100", health_status],
            ['📊 Attendance Rate', f"{attendance_rate:.1f}%", _get_performance_indicator(attendance_rate, 95, 85)],
            ['⏰ Punctuality Rate', f"{punctuality_rate:.1f}%", _get_performance_indicator(punctuality_rate, 90, 80)],
            ['👥 Total Workforce', f"{total_employees}", "Active Employees"],
            ['⏱️ Total Hours Worked', f"{hours_analytics['total_hours']:.1f}h", f"Avg: {hours_analytics['avg_hours_per_employee']:.1f}h/emp"],
            ['📈 Expected Hours', f"{hours_analytics['expected_hours']:.1f}h", f"Efficiency: {hours_analytics['efficiency_rate']:.1f}%"],
            ['⚡ Total Overtime', f"{hours_analytics['total_overtime']:.1f}h", _get_overtime_indicator(hours_analytics['overtime_percentage'])]
        ]

        # Wider columns to prevent text overlap
        health_table = Table(health_data, colWidths=[2.2*inch, 1.3*inch, 1.5*inch])
        health_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), KAZISYNC_BLUE),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 11),
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('TEXTCOLOR', (0, 1), (-1, -1), KAZISYNC_DARK),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.Color(0.9, 0.9, 0.9)),
            ('LEFTPADDING', (0, 0), (-1, -1), 8),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))

        summary_elements.append(health_table)
        summary_elements.append(Spacer(1, 20))

    except Exception as e:
        # Fallback to basic summary if analytics fail
        app.logger.error(f"Error generating executive analytics: {e}")
        summary_elements.extend(_generate_basic_summary(
            attendance_records, KAZISYNC_BLUE, KAZISYNC_DARK
        ))

        # 2. DEPARTMENT PERFORMANCE MATRIX
        dept_title = Paragraph("🏢 Department Performance Analysis", ParagraphStyle(
            'DeptTitle', fontSize=14, fontName='Helvetica-Bold',
            textColor=KAZISYNC_BLUE, spaceAfter=10, spaceBefore=20
        ))
        summary_elements.append(dept_title)

        # Get department statistics
        department_stats = _get_department_analytics(session, start_date, end_date)

        if department_stats:
            # Create department performance table
            dept_headers = ['Department', 'Employees', 'Attendance', 'Punctuality', 'Status', 'Risk Level']
            dept_data = [dept_headers]

            for dept in department_stats[:5]:  # Top 5 departments
                status_indicator = _get_department_status_indicator(dept['avg_attendance_percentage'])
                risk_level = _assess_department_risk(dept)

                dept_data.append([
                    dept['department_name'][:15] + "..." if len(dept['department_name']) > 15 else dept['department_name'],
                    str(dept['employee_count']),
                    f"{dept['avg_attendance_percentage']:.1f}%",
                    f"{dept.get('avg_punctuality_percentage', 0):.1f}%",
                    status_indicator,
                    risk_level
                ])

            dept_table = Table(dept_data, colWidths=[1.5*inch, 0.8*inch, 0.8*inch, 0.8*inch, 0.8*inch, 0.8*inch])
            dept_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), KAZISYNC_BLUE),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 9),
                ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                ('TEXTCOLOR', (0, 1), (-1, -1), KAZISYNC_DARK),
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 1), (-1, -1), 8),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.Color(0.9, 0.9, 0.9)),
                ('LEFTPADDING', (0, 0), (-1, -1), 6),
                ('RIGHTPADDING', (0, 0), (-1, -1), 6),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('ALIGN', (1, 0), (-1, -1), 'CENTER'),
            ]))

            summary_elements.append(dept_table)
            summary_elements.append(Spacer(1, 15))

        # 3. KEY INSIGHTS & RECOMMENDATIONS
        insights_title = Paragraph("💡 Executive Insights & Recommendations", ParagraphStyle(
            'InsightsTitle', fontSize=14, fontName='Helvetica-Bold',
            textColor=KAZISYNC_BLUE, spaceAfter=10, spaceBefore=15
        ))
        summary_elements.append(insights_title)

        # Generate actionable insights
        insights = _generate_executive_insights(
            attendance_rate, punctuality_rate, department_stats,
            total_records, late_records, absent_records
        )

        insights_data = [['🎯 Key Insights & Action Items', '']]
        for insight in insights:
            insights_data.append([insight, ''])

        insights_table = Table(insights_data, colWidths=[5.5*inch, 0.5*inch])
        insights_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), KAZISYNC_BLUE),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 11),
            ('SPAN', (0, 0), (1, 0)),
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('TEXTCOLOR', (0, 1), (-1, -1), KAZISYNC_DARK),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.Color(0.9, 0.9, 0.9)),
            ('LEFTPADDING', (0, 0), (-1, -1), 10),
            ('RIGHTPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ]))

        summary_elements.append(insights_table)

    except Exception as e:
        # Fallback to basic summary if analytics fail
        app.logger.error(f"Error generating executive analytics: {e}")
        summary_elements.extend(_generate_basic_summary(
            attendance_records, KAZISYNC_BLUE, KAZISYNC_DARK
        ))

    return summary_elements


def _generate_employee_analytics_summary(company, attendance_records, start_date, end_date, employee_id, session,
                                       KAZISYNC_BLUE, KAZISYNC_DARK, KAZISYNC_SUCCESS, KAZISYNC_WARNING, KAZISYNC_ERROR):
    """Generate comprehensive individual employee analytics summary."""
    summary_elements = []

    try:
        # Get employee information
        employee = session.query(Employee).filter_by(employee_id=employee_id).first()
        if not employee:
            return _generate_basic_summary(attendance_records, KAZISYNC_BLUE, KAZISYNC_DARK)

        # Filter attendance records to exclude future dates
        today = date.today()
        actual_end_date = min(end_date, today)
        filtered_records = [r for r in attendance_records if r.date <= actual_end_date]

        # Calculate shift-aware employee metrics
        employee_metrics = _calculate_employee_shift_aware_metrics(
            employee, filtered_records, start_date, actual_end_date, session
        )

        # Extract metrics
        attendance_rate = employee_metrics['attendance_rate']
        punctuality_rate = employee_metrics['punctuality_rate']
        working_days = employee_metrics['working_days']
        present_days = employee_metrics['present_days']
        hours_analytics = employee_metrics['hours_analytics']

        # Get performance score and rating
        performance_score = _calculate_employee_performance_score(attendance_rate, punctuality_rate)
        performance_rating, rating_color = _get_performance_rating(performance_score,
                                                                 KAZISYNC_SUCCESS, KAZISYNC_WARNING, KAZISYNC_ERROR)

        # 1. EMPLOYEE PERFORMANCE SCORECARD
        scorecard_title = Paragraph(f"👤 Performance Scorecard - {employee.first_name} {employee.last_name}",
                                   ParagraphStyle('ScoreTitle', fontSize=16, fontName='Helvetica-Bold',
                                                textColor=KAZISYNC_BLUE, spaceAfter=15, alignment=1))
        summary_elements.append(scorecard_title)

        # Comprehensive Performance Scorecard
        scorecard_data = [
            ['📊 Overall Performance Score', f"{performance_score}/100", performance_rating],
            ['📈 Attendance Rate', f"{attendance_rate:.1f}%", _get_performance_indicator(attendance_rate, 95, 85)],
            ['⏰ Punctuality Rate', f"{punctuality_rate:.1f}%", _get_performance_indicator(punctuality_rate, 90, 80)],
            ['📅 Working Days (Period)', f"{working_days}", f"{present_days} Present"],
            ['⏱️ Total Hours Worked', f"{hours_analytics['total_hours']:.1f}h", f"Avg: {hours_analytics['avg_hours_per_day']:.1f}h/day"],
            ['📋 Expected Hours', f"{hours_analytics['expected_hours']:.1f}h", f"Efficiency: {hours_analytics['efficiency_rate']:.1f}%"],
            ['⚡ Overtime Hours', f"{hours_analytics['overtime_hours']:.1f}h", _get_overtime_indicator(hours_analytics['overtime_percentage'])]
        ]

        # Wider columns to prevent text overlap
        scorecard_table = Table(scorecard_data, colWidths=[2.2*inch, 1.3*inch, 1.5*inch])
        scorecard_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), KAZISYNC_BLUE),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 11),
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('TEXTCOLOR', (0, 1), (-1, -1), KAZISYNC_DARK),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.Color(0.9, 0.9, 0.9)),
            ('LEFTPADDING', (0, 0), (-1, -1), 8),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))

        summary_elements.append(scorecard_table)
        summary_elements.append(Spacer(1, 20))

    except Exception as e:
        app.logger.error(f"Error generating employee analytics: {e}")
        summary_elements.extend(_generate_basic_summary(attendance_records, KAZISYNC_BLUE, KAZISYNC_DARK))

    return summary_elements


def _calculate_company_health_score(attendance_rate, punctuality_rate, late_records, total_records,
                                   KAZISYNC_SUCCESS, KAZISYNC_WARNING, KAZISYNC_ERROR):
    """Calculate overall company health score and status."""
    # Weight different factors
    attendance_weight = 0.5
    punctuality_weight = 0.3
    consistency_weight = 0.2

    # Calculate consistency (inverse of late percentage)
    late_percentage = (late_records / total_records * 100) if total_records > 0 else 0
    consistency_score = max(0, 100 - late_percentage * 2)  # Penalize lateness

    # Calculate weighted health score
    health_score = (
        attendance_rate * attendance_weight +
        punctuality_rate * punctuality_weight +
        consistency_score * consistency_weight
    )

    # Determine status and color
    if health_score >= 90:
        status = "🟢 Excellent"
        color = KAZISYNC_SUCCESS
    elif health_score >= 75:
        status = "🟡 Good"
        color = KAZISYNC_WARNING
    else:
        status = "🔴 Needs Attention"
        color = KAZISYNC_ERROR

    return round(health_score, 1), status, color


def _get_performance_indicator(value, excellent_threshold, good_threshold):
    """Get performance indicator based on value and thresholds."""
    if value >= excellent_threshold:
        return "🟢 Excellent"
    elif value >= good_threshold:
        return "🟡 Good"
    else:
        return "🔴 Poor"


def _get_department_analytics(session, start_date, end_date):
    """Get comprehensive department analytics."""
    try:
        from application.Models.employees.department import Department

        departments = session.query(Department).all()
        department_stats = []

        for dept in departments:
            # Get department employees
            dept_employees = session.query(Employee).filter(
                Employee.department_id == dept.department_id,
                Employee.status == 'active'
            ).all()

            if not dept_employees:
                continue

            # Get attendance records for department
            dept_employee_ids = [emp.employee_id for emp in dept_employees]
            dept_attendance = session.query(Attendance).filter(
                Attendance.employee_id.in_(dept_employee_ids),
                Attendance.date.between(start_date, end_date)
            ).all()

            # Calculate department metrics
            total_records = len(dept_attendance)
            present_records = len([r for r in dept_attendance if r.status in ['present', 'late']])
            late_records = len([r for r in dept_attendance if r.status == 'late'])

            attendance_percentage = (present_records / total_records * 100) if total_records > 0 else 0
            punctuality_percentage = ((present_records - late_records) / present_records * 100) if present_records > 0 else 0

            department_stats.append({
                'department_id': str(dept.department_id),
                'department_name': dept.name,
                'employee_count': len(dept_employees),
                'total_records': total_records,
                'present_count': present_records,
                'late_count': late_records,
                'absent_count': total_records - present_records,
                'avg_attendance_percentage': attendance_percentage,
                'avg_punctuality_percentage': punctuality_percentage
            })

        # Sort by attendance percentage (descending)
        department_stats.sort(key=lambda x: x['avg_attendance_percentage'], reverse=True)
        return department_stats

    except Exception as e:
        app.logger.error(f"Error getting department analytics: {e}")
        return []


def _get_department_status_indicator(attendance_percentage):
    """Get department status indicator based on attendance."""
    if attendance_percentage >= 95:
        return "🟢 Excellent"
    elif attendance_percentage >= 85:
        return "🟡 Good"
    elif attendance_percentage >= 75:
        return "🟠 Fair"
    else:
        return "🔴 Poor"


def _assess_department_risk(dept_stats):
    """Assess department risk level based on multiple factors."""
    attendance = dept_stats['avg_attendance_percentage']
    punctuality = dept_stats.get('avg_punctuality_percentage', 0)

    # Calculate risk score
    risk_score = 0
    if attendance < 85:
        risk_score += 3
    elif attendance < 90:
        risk_score += 1

    if punctuality < 80:
        risk_score += 2
    elif punctuality < 90:
        risk_score += 1

    # Determine risk level
    if risk_score >= 4:
        return "🔴 High"
    elif risk_score >= 2:
        return "🟡 Medium"
    else:
        return "🟢 Low"


def _generate_executive_insights(attendance_rate, punctuality_rate, department_stats,
                               total_records, late_records, absent_records):
    """Generate actionable executive insights and recommendations."""
    insights = []

    # Overall performance insights
    if attendance_rate >= 95:
        insights.append("✅ STRENGTH: Excellent overall attendance rate - maintain current policies")
    elif attendance_rate < 85:
        insights.append("🚨 CRITICAL: Low attendance rate requires immediate intervention")
        insights.append("📋 ACTION: Implement attendance improvement program within 30 days")

    # Punctuality insights
    if punctuality_rate < 80:
        insights.append("⏰ CONCERN: High tardiness rate affecting productivity")
        insights.append("💡 RECOMMENDATION: Review flexible work arrangements and start times")

    # Department-specific insights
    if department_stats:
        best_dept = department_stats[0]
        worst_dept = department_stats[-1]

        if best_dept['avg_attendance_percentage'] - worst_dept['avg_attendance_percentage'] > 20:
            insights.append(f"📊 DISPARITY: {worst_dept['department_name']} underperforming vs {best_dept['department_name']}")
            insights.append(f"🎯 ACTION: Investigate {worst_dept['department_name']} management practices")

    # Late arrival patterns
    if late_records > total_records * 0.15:  # More than 15% late
        insights.append("🕐 PATTERN: High late arrival rate suggests systemic issues")
        insights.append("🔍 INVESTIGATE: Traffic, public transport, or work-life balance factors")

    # Resource allocation insights
    if total_records > 0:
        productivity_loss = (absent_records + late_records) / total_records * 100
        if productivity_loss > 20:
            insights.append(f"💰 IMPACT: {productivity_loss:.1f}% productivity loss due to attendance issues")
            insights.append("💼 PRIORITY: Calculate financial impact and ROI of attendance initiatives")

    # Limit to top 6 most actionable insights
    return insights[:6]


def _calculate_employee_performance_score(attendance_rate, punctuality_rate):
    """Calculate overall employee performance score."""
    # Weight attendance more heavily than punctuality
    attendance_weight = 0.6
    punctuality_weight = 0.4

    score = (attendance_rate * attendance_weight) + (punctuality_rate * punctuality_weight)
    return round(score, 1)


def _get_performance_rating(score, KAZISYNC_SUCCESS, KAZISYNC_WARNING, KAZISYNC_ERROR):
    """Get performance rating and color based on score."""
    if score >= 90:
        return "🌟 Outstanding", KAZISYNC_SUCCESS
    elif score >= 80:
        return "✅ Good", KAZISYNC_SUCCESS
    elif score >= 70:
        return "⚠️ Needs Improvement", KAZISYNC_WARNING
    else:
        return "🚨 Critical", KAZISYNC_ERROR


def _generate_basic_summary(attendance_records, KAZISYNC_BLUE, KAZISYNC_DARK):
    """Generate basic summary as fallback."""
    total_records = len(attendance_records)
    present_records = len([r for r in attendance_records if r.status in ['present', 'late']])
    late_records = len([r for r in attendance_records if r.status == 'late'])
    absent_records = total_records - present_records

    attendance_rate = (present_records / total_records * 100) if total_records > 0 else 0
    punctuality_rate = ((present_records - late_records) / present_records * 100) if present_records > 0 else 0

    summary_data = [
        ['📊 Attendance Summary', ''],
        ['Total Records', str(total_records)],
        ['Present', f"{present_records}"],
        ['Late Arrivals', f"{late_records}"],
        ['Absent', f"{absent_records}"],
        ['Attendance Rate', f"{attendance_rate:.1f}%"],
        ['Punctuality Rate', f"{punctuality_rate:.1f}%"]
    ]

    summary_table = Table(summary_data, colWidths=[2.5*inch, 1.5*inch])
    summary_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), KAZISYNC_BLUE),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 12),
        ('SPAN', (0, 0), (1, 0)),
        ('BACKGROUND', (0, 1), (-1, -1), colors.white),
        ('TEXTCOLOR', (0, 1), (-1, -1), KAZISYNC_DARK),
        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 1), (-1, -1), 10),
        ('GRID', (0, 0), (-1, -1), 0.5, colors.Color(0.9, 0.9, 0.9)),
        ('LEFTPADDING', (0, 0), (-1, -1), 12),
        ('RIGHTPADDING', (0, 0), (-1, -1), 12),
        ('TOPPADDING', (0, 0), (-1, -1), 8),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
    ]))

    return [summary_table]


def _calculate_company_shift_aware_metrics(company, attendance_records, start_date, end_date, session):
    """Calculate comprehensive company-wide metrics based on employee shifts."""
    try:
        # Get all active employees
        all_employees = session.query(Employee).filter(Employee.status == 'active').all()
        total_employees = len(all_employees)

        # Calculate basic attendance metrics
        total_records = len(attendance_records)
        present_records = len([r for r in attendance_records if r.status in ['present', 'late']])
        late_records = len([r for r in attendance_records if r.status == 'late'])
        absent_records = total_records - present_records

        # Calculate rates
        attendance_rate = (present_records / total_records * 100) if total_records > 0 else 0
        punctuality_rate = ((present_records - late_records) / present_records * 100) if present_records > 0 else 0

        # Calculate comprehensive hours analytics
        total_hours = sum(r.total_hours for r in attendance_records if r.total_hours)
        avg_hours_per_employee = total_hours / total_employees if total_employees > 0 else 0

        # Calculate total expected hours based on shifts
        total_expected_hours = 0

        for employee in all_employees:
            employee_records = [r for r in attendance_records if r.employee_id == employee.employee_id]
            if employee_records:
                # Get shift info from employee
                employee_dict = employee.to_dict() if hasattr(employee, 'to_dict') else {}
                shift_info = employee_dict.get('shift', {})
                hours_per_day = shift_info.get('hours_per_day', 8)  # Default 8 hours

                # Calculate expected hours for this employee
                employee_working_days = len(employee_records)
                employee_expected = employee_working_days * hours_per_day
                total_expected_hours += employee_expected

        # Calculate company-wide overtime as simple difference (FIXED APPROACH)
        total_overtime = max(0, total_hours - total_expected_hours)

        # Calculate efficiency and overtime metrics
        efficiency_rate = (total_hours / total_expected_hours * 100) if total_expected_hours > 0 else 0
        overtime_percentage = (total_overtime / total_expected_hours * 100) if total_expected_hours > 0 else 0

        hours_analytics = {
            'total_hours': total_hours,
            'expected_hours': total_expected_hours,
            'avg_hours_per_employee': avg_hours_per_employee,
            'total_overtime': total_overtime,
            'efficiency_rate': efficiency_rate,
            'overtime_percentage': overtime_percentage
        }

        return {
            'total_records': total_records,
            'present_records': present_records,
            'late_records': late_records,
            'absent_records': absent_records,
            'attendance_rate': attendance_rate,
            'punctuality_rate': punctuality_rate,
            'total_employees': total_employees,
            'hours_analytics': hours_analytics
        }

    except Exception as e:
        app.logger.error(f"Error calculating company metrics: {e}")
        # Return basic fallback metrics
        total_records = len(attendance_records)
        present_records = len([r for r in attendance_records if r.status in ['present', 'late']])
        return {
            'total_records': total_records,
            'present_records': present_records,
            'late_records': 0,
            'absent_records': 0,
            'attendance_rate': (present_records / total_records * 100) if total_records > 0 else 0,
            'punctuality_rate': 0,
            'total_employees': 0,
            'hours_analytics': {
                'total_hours': 0,
                'expected_hours': 0,
                'avg_hours_per_employee': 0,
                'total_overtime': 0,
                'efficiency_rate': 0,
                'overtime_percentage': 0
            }
        }


def _calculate_employee_shift_aware_metrics(employee, attendance_records, start_date, end_date, session):
    """Calculate comprehensive employee metrics based on their shift."""
    try:
        # Get shift info from employee
        employee_dict = employee.to_dict() if hasattr(employee, 'to_dict') else {}
        shift_info = employee_dict.get('shift', {})
        hours_per_day = shift_info.get('hours_per_day', 8)  # Default 8 hours

        # Calculate working days based on actual attendance records (shift-aware)
        working_days = len(attendance_records)  # Only days they were supposed to work
        present_records = len([r for r in attendance_records if r.status in ['present', 'late']])
        late_records = len([r for r in attendance_records if r.status == 'late'])

        # Calculate rates based on actual working days
        attendance_rate = (present_records / working_days * 100) if working_days > 0 else 0
        punctuality_rate = ((present_records - late_records) / present_records * 100) if present_records > 0 else 0

        # Calculate comprehensive hours analytics
        total_hours = sum(r.total_hours for r in attendance_records if r.total_hours)
        expected_hours = working_days * hours_per_day
        avg_hours_per_day = total_hours / working_days if working_days > 0 else 0

        # Calculate overtime
        overtime_hours = max(0, total_hours - expected_hours)
        efficiency_rate = (total_hours / expected_hours * 100) if expected_hours > 0 else 0
        overtime_percentage = (overtime_hours / expected_hours * 100) if expected_hours > 0 else 0

        hours_analytics = {
            'total_hours': total_hours,
            'expected_hours': expected_hours,
            'avg_hours_per_day': avg_hours_per_day,
            'overtime_hours': overtime_hours,
            'efficiency_rate': efficiency_rate,
            'overtime_percentage': overtime_percentage
        }

        return {
            'attendance_rate': attendance_rate,
            'punctuality_rate': punctuality_rate,
            'working_days': working_days,
            'present_days': present_records,
            'hours_analytics': hours_analytics
        }

    except Exception as e:
        app.logger.error(f"Error calculating employee metrics: {e}")
        # Return basic fallback metrics
        working_days = len(attendance_records)
        present_records = len([r for r in attendance_records if r.status in ['present', 'late']])
        return {
            'attendance_rate': (present_records / working_days * 100) if working_days > 0 else 0,
            'punctuality_rate': 0,
            'working_days': working_days,
            'present_days': present_records,
            'hours_analytics': {
                'total_hours': 0,
                'expected_hours': 0,
                'avg_hours_per_day': 0,
                'overtime_hours': 0,
                'efficiency_rate': 0,
                'overtime_percentage': 0
            }
        }


def _get_overtime_indicator(overtime_percentage):
    """Get overtime indicator based on percentage."""
    if overtime_percentage <= 5:
        return "🟢 Normal"
    elif overtime_percentage <= 15:
        return "🟡 Moderate"
    else:
        return "🔴 High"


# ==================== MONTHLY SUMMARY REPORT HELPER FUNCTIONS ====================

def _calculate_monthly_summary_data(session, start_date, end_date, employee_id=None):
    """
    Calculate monthly summary data for employees.

    Returns a list of employee summary dictionaries with the following structure:
    {
        'employee_id': str,
        'employee_name': str,
        'department': str,
        'department_id': str,
        'days_present': int,
        'days_absent': int,
        'days_on_leave': int,
        'days_late': int,
        'total_hours': float,
        'avg_hours_per_day': float,
        'attendance_percentage': float,
        'total_working_days': int
    }
    """
    try:
        app.logger.info(f"Calculating monthly summary data from {start_date} to {end_date}")

        # Get all active employees or specific employee
        from application.Models.employees import EmployeeShift

        if employee_id:
            employees = session.query(Employee).filter(
                Employee.employee_id == employee_id,
                Employee.status == 'active'
            ).all()
        else:
            employees = session.query(Employee).filter(Employee.status == 'active').all()

        if not employees:
            app.logger.warning("No active employees found for summary calculation")
            return []

        # Get all dates in the range
        dates_in_range = DateHelper.get_dates_in_range(start_date, end_date)

        # Get all attendance records for the period
        attendance_query = session.query(Attendance).filter(
            Attendance.date >= start_date,
            Attendance.date <= end_date
        )

        if employee_id:
            attendance_query = attendance_query.filter(Attendance.employee_id == employee_id)

        all_attendance_records = attendance_query.all()

        # Group attendance records by employee
        attendance_by_employee = {}
        for record in all_attendance_records:
            emp_id = str(record.employee_id)
            if emp_id not in attendance_by_employee:
                attendance_by_employee[emp_id] = []
            attendance_by_employee[emp_id].append(record)

        # Calculate summary for each employee
        employee_summaries = []

        for employee in employees:
            emp_id_str = str(employee.employee_id)

            # Get employee's attendance records
            emp_attendance_records = attendance_by_employee.get(emp_id_str, [])

            # Get employee's shift assignments for the period
            shift_assignments = session.query(EmployeeShift).filter(
                EmployeeShift.employee_id == employee.employee_id,
                EmployeeShift.is_active == True,
                EmployeeShift.effective_start_date <= end_date,
                (EmployeeShift.effective_end_date.is_(None)) | (EmployeeShift.effective_end_date >= start_date)
            ).all()

            # Calculate working days based on shift or default to all days
            if shift_assignments:
                total_working_days = _calculate_working_days_for_employee(dates_in_range, shift_assignments)
            else:
                # No shift assignment - default to all days in the period (7 days/week)
                total_working_days = len(dates_in_range)

            # Count attendance by status
            days_present = 0
            days_late = 0
            days_on_leave = 0
            total_hours = 0.0

            for record in emp_attendance_records:
                status = record.status.lower() if record.status else ''

                if status == 'present':
                    days_present += 1
                elif status == 'late':
                    days_late += 1
                    days_present += 1  # Late is still considered present
                elif status == 'on leave':
                    days_on_leave += 1

                # Add hours worked
                if record.total_hours:
                    total_hours += record.total_hours

            # Calculate extra days worked (days beyond expected schedule)
            extra_days_worked = 0
            if days_present > total_working_days:
                extra_days_worked = days_present - total_working_days
                app.logger.info(f"Employee {emp_id_str} worked {extra_days_worked} extra days beyond schedule")

            # Calculate days absent
            # If employee worked more than expected, they have 0 absent days
            # Otherwise: Days absent = Total working days - (days present + days on leave)
            if days_present > total_working_days:
                days_absent = 0
            else:
                days_absent = total_working_days - days_present - days_on_leave
                # Ensure days_absent is not negative
                if days_absent < 0:
                    days_absent = 0

            # Calculate average hours per day (only for days actually present)
            avg_hours_per_day = 0.0
            if days_present > 0:
                avg_hours_per_day = total_hours / days_present

            # Get department info
            department_name = "No Department"
            department_id = None
            if employee.department_id and employee.department:
                department_name = employee.department.name
                department_id = str(employee.department_id)

            employee_summaries.append({
                'employee_id': emp_id_str,
                'employee_name': f"{employee.first_name} {employee.last_name}",
                'department': department_name,
                'department_id': department_id,
                'total_working_days': total_working_days,
                'days_present': days_present,
                'extra_days_worked': extra_days_worked,
                'days_absent': days_absent,
                'days_on_leave': days_on_leave,
                'days_late': days_late,
                'total_hours': round(total_hours, 2),
                'avg_hours_per_day': round(avg_hours_per_day, 2)
            })

        app.logger.info(f"Calculated summary for {len(employee_summaries)} employees")
        return employee_summaries

    except Exception as e:
        app.logger.error(f"Error calculating monthly summary data: {e}")
        return []


def _calculate_department_summary(employee_summaries):
    """
    Calculate department-level summary statistics from employee summaries.

    Returns a list of department summary dictionaries with the following structure:
    {
        'department_name': str,
        'department_id': str,
        'total_employees': int,
        'total_days_present': int,
        'total_days_absent': int,
        'total_days_on_leave': int,
        'total_days_late': int,
        'total_extra_days': int,
        'total_hours': float
    }
    """
    try:
        app.logger.info("Calculating department summary statistics")

        # Group employees by department
        departments = {}

        for emp_summary in employee_summaries:
            dept_id = emp_summary['department_id'] if emp_summary['department_id'] else 'no_department'
            dept_name = emp_summary['department']

            if dept_id not in departments:
                departments[dept_id] = {
                    'department_name': dept_name,
                    'department_id': dept_id,
                    'total_employees': 0,
                    'total_days_present': 0,
                    'total_days_absent': 0,
                    'total_days_on_leave': 0,
                    'total_days_late': 0,
                    'total_extra_days': 0,
                    'total_hours': 0.0
                }

            # Aggregate statistics
            departments[dept_id]['total_employees'] += 1
            departments[dept_id]['total_days_present'] += emp_summary['days_present']
            departments[dept_id]['total_days_absent'] += emp_summary['days_absent']
            departments[dept_id]['total_days_on_leave'] += emp_summary['days_on_leave']
            departments[dept_id]['total_days_late'] += emp_summary['days_late']
            departments[dept_id]['total_extra_days'] += emp_summary['extra_days_worked']
            departments[dept_id]['total_hours'] += emp_summary['total_hours']

        # Build department summaries list
        department_summaries = []
        for dept_id, dept_data in departments.items():
            department_summaries.append({
                'department_name': dept_data['department_name'],
                'department_id': dept_data['department_id'],
                'total_employees': dept_data['total_employees'],
                'total_days_present': dept_data['total_days_present'],
                'total_days_absent': dept_data['total_days_absent'],
                'total_days_on_leave': dept_data['total_days_on_leave'],
                'total_days_late': dept_data['total_days_late'],
                'total_extra_days': dept_data['total_extra_days'],
                'total_hours': round(dept_data['total_hours'], 2)
            })

        # Sort by department name
        department_summaries.sort(key=lambda x: x['department_name'])

        app.logger.info(f"Calculated summary for {len(department_summaries)} departments")
        return department_summaries

    except Exception as e:
        app.logger.error(f"Error calculating department summary: {e}")
        return []


def _generate_monthly_summary_pdf(company, employee_summaries, department_summaries, start_date, end_date):
    """Generate KaziSync-branded PDF report for monthly attendance summary."""
    buffer = io.BytesIO()

    # Create the PDF document
    doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=18)

    # Container for the 'Flowable' objects
    elements = []

    # Define KaziSync brand colors
    KAZISYNC_BLUE = colors.Color(0, 0.48, 1)      # #007BFF
    KAZISYNC_DARK = colors.Color(0.2, 0.2, 0.2)   # Dark gray
    KAZISYNC_LIGHT = colors.Color(0.96, 0.97, 0.98) # Light background

    # Define styles
    styles = getSampleStyleSheet()
    title_style = ParagraphStyle(
        'KaziSyncTitle',
        parent=styles['Heading1'],
        fontSize=24,
        fontName='Helvetica-Bold',
        textColor=KAZISYNC_BLUE,
        spaceAfter=20,
        alignment=1  # Center alignment
    )

    subtitle_style = ParagraphStyle(
        'KaziSyncSubtitle',
        parent=styles['Heading2'],
        fontSize=14,
        fontName='Helvetica',
        textColor=KAZISYNC_DARK,
        spaceAfter=12,
        alignment=1
    )

    section_title_style = ParagraphStyle(
        'SectionTitle',
        parent=styles['Heading2'],
        fontSize=16,
        fontName='Helvetica-Bold',
        textColor=KAZISYNC_BLUE,
        spaceAfter=12,
        spaceBefore=20
    )

    # Add title
    title = Paragraph("Monthly Attendance Summary Report", title_style)
    elements.append(title)

    # Add company name
    company_name = Paragraph(f"<b>{company.company_name}</b>", subtitle_style)
    elements.append(company_name)

    # Add date range
    date_range_text = f"Period: {start_date.strftime('%B %d, %Y')} to {end_date.strftime('%B %d, %Y')}"
    date_range = Paragraph(date_range_text, subtitle_style)
    elements.append(date_range)

    elements.append(Spacer(1, 0.3*inch))

    # Add Employee Summary Section
    section_title = Paragraph("Employee Summary", section_title_style)
    elements.append(section_title)

    if employee_summaries:
        # Create employee summary table
        table_data = [[
            'Employee Name',
            'Department',
            'Days\nExpected',
            'Days\nPresent',
            'Extra\nDays',
            'Days\nAbsent',
            'Days On\nLeave',
            'Days\nLate',
            'Total\nHours',
            'Avg Hours\n/Day'
        ]]

        for emp in employee_summaries:
            # Log warning if name is very long (may cause wrapping)
            if len(emp['employee_name']) > 30 or len(emp['department']) > 40:
                app.logger.info(f"Long name detected in PDF: {emp['employee_name']} ({emp['department']}) - text wrapping will be applied")

            table_data.append([
                emp['employee_name'],
                emp['department'],
                str(emp['total_working_days']),
                str(emp['days_present']),
                str(emp['extra_days_worked']),
                str(emp['days_absent']),
                str(emp['days_on_leave']),
                str(emp['days_late']),
                f"{emp['total_hours']:.2f}",
                f"{emp['avg_hours_per_day']:.2f}"
            ])

        # Create table with adjusted column widths to accommodate text wrapping
        # Increased widths for Employee Name and Department to allow 2-3 lines of wrapped text
        col_widths = [1.5*inch, 1.3*inch, 0.55*inch, 0.55*inch, 0.5*inch, 0.55*inch, 0.6*inch, 0.5*inch, 0.6*inch, 0.65*inch]
        table = Table(table_data, colWidths=col_widths, repeatRows=1)

        # Apply table styling
        table.setStyle(TableStyle([
            # Header row styling
            ('BACKGROUND', (0, 0), (-1, 0), KAZISYNC_BLUE),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 9),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('VALIGN', (0, 0), (-1, 0), 'MIDDLE'),

            # Data rows styling
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('TEXTCOLOR', (0, 1), (-1, -1), KAZISYNC_DARK),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
            ('ALIGN', (0, 1), (1, -1), 'LEFT'),  # Name and Department left-aligned
            ('ALIGN', (2, 1), (-1, -1), 'CENTER'),  # Numbers center-aligned
            ('VALIGN', (0, 1), (-1, -1), 'MIDDLE'),

            # Enable text wrapping for long names
            ('WORDWRAP', (0, 1), (1, -1), 'LTR'),  # Wrap text in Employee Name and Department columns

            # Grid and padding
            ('GRID', (0, 0), (-1, -1), 0.5, colors.Color(0.8, 0.8, 0.8)),
            ('LEFTPADDING', (0, 0), (-1, -1), 6),
            ('RIGHTPADDING', (0, 0), (-1, -1), 6),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),

            # Alternating row colors
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, KAZISYNC_LIGHT]),
        ]))

        elements.append(table)
    else:
        no_data = Paragraph("<i>No employee data available for this period.</i>", styles['Normal'])
        elements.append(no_data)

    elements.append(Spacer(1, 0.4*inch))

    # Add Department Summary Section
    section_title = Paragraph("Department Summary", section_title_style)
    elements.append(section_title)

    if department_summaries:
        # Create department summary table
        dept_table_data = [[
            'Department',
            'Total\nEmployees',
            'Total Days\nPresent',
            'Total Days\nAbsent',
            'Total Days\nOn Leave',
            'Total Days\nLate',
            'Total\nExtra Days',
            'Total\nHours'
        ]]

        for dept in department_summaries:
            dept_table_data.append([
                dept['department_name'],
                str(dept['total_employees']),
                str(dept['total_days_present']),
                str(dept['total_days_absent']),
                str(dept['total_days_on_leave']),
                str(dept['total_days_late']),
                str(dept['total_extra_days']),
                f"{dept['total_hours']:.2f}"
            ])

        # Create table with appropriate column widths
        dept_col_widths = [1.8*inch, 0.8*inch, 0.8*inch, 0.8*inch, 0.9*inch, 0.7*inch, 0.8*inch, 0.7*inch]
        dept_table = Table(dept_table_data, colWidths=dept_col_widths, repeatRows=1)

        # Apply table styling
        dept_table.setStyle(TableStyle([
            # Header row styling
            ('BACKGROUND', (0, 0), (-1, 0), KAZISYNC_BLUE),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 9),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('VALIGN', (0, 0), (-1, 0), 'MIDDLE'),

            # Data rows styling
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('TEXTCOLOR', (0, 1), (-1, -1), KAZISYNC_DARK),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
            ('ALIGN', (0, 1), (0, -1), 'LEFT'),  # Department name left-aligned
            ('ALIGN', (1, 1), (-1, -1), 'CENTER'),  # Numbers center-aligned
            ('VALIGN', (0, 1), (-1, -1), 'MIDDLE'),

            # Enable text wrapping for department names
            ('WORDWRAP', (0, 1), (0, -1), 'LTR'),  # Wrap text in Department column

            # Grid and padding
            ('GRID', (0, 0), (-1, -1), 0.5, colors.Color(0.8, 0.8, 0.8)),
            ('LEFTPADDING', (0, 0), (-1, -1), 6),
            ('RIGHTPADDING', (0, 0), (-1, -1), 6),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),

            # Alternating row colors
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, KAZISYNC_LIGHT]),
        ]))

        elements.append(dept_table)
    else:
        no_dept_data = Paragraph("<i>No department data available.</i>", styles['Normal'])
        elements.append(no_dept_data)

    # Add footer
    elements.append(Spacer(1, 0.5*inch))

    footer_text = f"Generated on {datetime.now().strftime('%B %d, %Y at %I:%M %p')} | Powered by KaziSync"
    footer = Paragraph(footer_text, ParagraphStyle(
        'Footer',
        parent=styles['Normal'],
        fontSize=8,
        textColor=colors.Color(0.5, 0.5, 0.5),
        alignment=1
    ))
    elements.append(footer)

    # Build PDF
    doc.build(elements)

    buffer.seek(0)
    return buffer


def _generate_monthly_summary_excel(company, employee_summaries, department_summaries, start_date, end_date):
    """Generate Excel report for monthly attendance summary using openpyxl."""
    from openpyxl import Workbook
    from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
    from openpyxl.utils import get_column_letter

    buffer = io.BytesIO()

    # Create workbook
    wb = Workbook()

    # Define colors (KaziSync branding)
    KAZISYNC_BLUE = "007BFF"
    LIGHT_BLUE = "E7F3FF"
    LIGHT_GRAY = "F5F5F5"

    # ==================== EMPLOYEE SUMMARY SHEET ====================
    ws_employees = wb.active
    ws_employees.title = "Employee Summary"

    # Add title and header information
    ws_employees['A1'] = "Monthly Attendance Summary Report"
    ws_employees['A1'].font = Font(size=16, bold=True, color=KAZISYNC_BLUE)
    ws_employees.merge_cells('A1:J1')

    ws_employees['A2'] = f"Company: {company.company_name}"
    ws_employees['A2'].font = Font(size=12, bold=True)
    ws_employees.merge_cells('A2:J2')

    ws_employees['A3'] = f"Period: {start_date.strftime('%B %d, %Y')} to {end_date.strftime('%B %d, %Y')}"
    ws_employees['A3'].font = Font(size=11)
    ws_employees.merge_cells('A3:J3')

    # Add employee summary table headers (starting at row 5)
    headers = [
        'Employee Name',
        'Department',
        'Days Expected',
        'Days Present',
        'Extra Days',
        'Days Absent',
        'Days On Leave',
        'Days Late',
        'Total Hours',
        'Avg Hours/Day'
    ]

    header_row = 5
    for col_num, header in enumerate(headers, 1):
        cell = ws_employees.cell(row=header_row, column=col_num)
        cell.value = header
        cell.font = Font(bold=True, color="FFFFFF")
        cell.fill = PatternFill(start_color=KAZISYNC_BLUE, end_color=KAZISYNC_BLUE, fill_type="solid")
        cell.alignment = Alignment(horizontal='center', vertical='center')
        cell.border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

    # Add employee data
    if employee_summaries:
        for row_num, emp in enumerate(employee_summaries, header_row + 1):
            ws_employees.cell(row=row_num, column=1, value=emp['employee_name'])
            ws_employees.cell(row=row_num, column=2, value=emp['department'])
            ws_employees.cell(row=row_num, column=3, value=emp['total_working_days'])
            ws_employees.cell(row=row_num, column=4, value=emp['days_present'])
            ws_employees.cell(row=row_num, column=5, value=emp['extra_days_worked'])
            ws_employees.cell(row=row_num, column=6, value=emp['days_absent'])
            ws_employees.cell(row=row_num, column=7, value=emp['days_on_leave'])
            ws_employees.cell(row=row_num, column=8, value=emp['days_late'])
            ws_employees.cell(row=row_num, column=9, value=emp['total_hours'])
            ws_employees.cell(row=row_num, column=10, value=emp['avg_hours_per_day'])

            # Apply styling to data rows
            for col_num in range(1, 11):
                cell = ws_employees.cell(row=row_num, column=col_num)
                cell.alignment = Alignment(horizontal='center' if col_num > 2 else 'left', vertical='center', wrap_text=True)
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
                # Alternating row colors
                if row_num % 2 == 0:
                    cell.fill = PatternFill(start_color=LIGHT_GRAY, end_color=LIGHT_GRAY, fill_type="solid")

    # Adjust column widths
    column_widths = [25, 20, 13, 12, 11, 12, 14, 10, 12, 14]
    for col_num, width in enumerate(column_widths, 1):
        ws_employees.column_dimensions[get_column_letter(col_num)].width = width

    # ==================== DEPARTMENT SUMMARY SHEET ====================
    ws_departments = wb.create_sheet(title="Department Summary")

    # Add title and header information
    ws_departments['A1'] = "Department Summary"
    ws_departments['A1'].font = Font(size=16, bold=True, color=KAZISYNC_BLUE)
    ws_departments.merge_cells('A1:H1')

    ws_departments['A2'] = f"Period: {start_date.strftime('%B %d, %Y')} to {end_date.strftime('%B %d, %Y')}"
    ws_departments['A2'].font = Font(size=11)
    ws_departments.merge_cells('A2:H2')

    # Add department summary table headers (starting at row 4)
    dept_headers = [
        'Department',
        'Total Employees',
        'Total Days Present',
        'Total Days Absent',
        'Total Days On Leave',
        'Total Days Late',
        'Total Extra Days',
        'Total Hours'
    ]

    dept_header_row = 4
    for col_num, header in enumerate(dept_headers, 1):
        cell = ws_departments.cell(row=dept_header_row, column=col_num)
        cell.value = header
        cell.font = Font(bold=True, color="FFFFFF")
        cell.fill = PatternFill(start_color=KAZISYNC_BLUE, end_color=KAZISYNC_BLUE, fill_type="solid")
        cell.alignment = Alignment(horizontal='center', vertical='center')
        cell.border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

    # Add department data
    if department_summaries:
        app.logger.info(f"Adding {len(department_summaries)} departments to Excel Department Summary sheet")
        for row_num, dept in enumerate(department_summaries, dept_header_row + 1):
            ws_departments.cell(row=row_num, column=1, value=dept['department_name'])
            ws_departments.cell(row=row_num, column=2, value=dept['total_employees'])
            ws_departments.cell(row=row_num, column=3, value=dept['total_days_present'])
            ws_departments.cell(row=row_num, column=4, value=dept['total_days_absent'])
            ws_departments.cell(row=row_num, column=5, value=dept['total_days_on_leave'])
            ws_departments.cell(row=row_num, column=6, value=dept['total_days_late'])
            ws_departments.cell(row=row_num, column=7, value=dept['total_extra_days'])
            ws_departments.cell(row=row_num, column=8, value=dept['total_hours'])

            # Apply styling to data rows
            for col_num in range(1, 9):
                cell = ws_departments.cell(row=row_num, column=col_num)
                cell.alignment = Alignment(horizontal='center' if col_num > 1 else 'left', vertical='center', wrap_text=True)
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
                # Alternating row colors
                if row_num % 2 == 0:
                    cell.fill = PatternFill(start_color=LIGHT_GRAY, end_color=LIGHT_GRAY, fill_type="solid")

    # Adjust column widths for department sheet
    dept_column_widths = [25, 15, 16, 15, 17, 14, 15, 12]
    for col_num, width in enumerate(dept_column_widths, 1):
        ws_departments.column_dimensions[get_column_letter(col_num)].width = width

    # Save workbook to buffer
    wb.save(buffer)
    buffer.seek(0)

    return buffer
