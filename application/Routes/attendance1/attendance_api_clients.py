"""This blueprint will be consumed by external applications to interact with the attendance system."""
from application.Models.employees import Employee, Attendance
from application.Models.employees.department import Department
from application.Models.company import Company
from datetime import datetime, timedelta, date
from sqlalchemy import func, cast, Date
import uuid
from application.decorators.api_client_required import api_client_required
from application.decorators.api_rate_limit import api_rate_limit
from flask import Blueprint, request, jsonify, current_app as app, make_response, g
from application.Helpers.helper_methods import HelperMethods
from application.Helpers.date_helper import DateHelper
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, PageBreak, KeepTogether
from reportlab.lib.units import inch
import io
# Import helper functions from the main attendance API
from application.Routes.attendance1.attendance_api import (
    _calculate_monthly_summary_data,
    _calculate_department_summary,
    _generate_monthly_summary_pdf,
    _generate_monthly_summary_excel
)

attendance_api_clients_bp = Blueprint("attendance_api_clients", __name__)

@attendance_api_clients_bp.route('/api/v1/attendance_records', methods=['GET'])
@api_client_required
@api_rate_limit(requests_per_hour=1000, requests_per_minute=50)
def get_attendance_records():
    """
    Get attendance records with optional filtering and pagination.

    Authentication: Requires valid API client token in Authorization header.
    The company_id is automatically extracted from the authenticated API client.

    Query Parameters:
    - employee_id (optional): Filter by specific employee UUID
    - date (optional): Filter by specific date (YYYY-MM-DD)
    - start_date (optional): Filter from this date (YYYY-MM-DD)
    - end_date (optional): Filter to this date (YYYY-MM-DD)
    - department_id (optional): Filter by department UUID
    - page (optional): Page number for pagination (default: 1)
    - per_page (optional): Records per page (default: 50, max: 500)
    - include_inactive (optional): Include inactive employees (default: false)

    Returns:
    - attendance_records: Array of attendance records
    - pagination: Pagination metadata
    - filters_applied: Summary of applied filters
    """
    # Company ID is automatically extracted from the authenticated API client token
    company_id = g.api_client_company_id

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({
            "success": False,
            "error": {
                "code": "COMPANY_NOT_FOUND",
                "message": "Company not found",
                "details": f"No database found for company ID: {company_id}. Please contact support."
            }   
        }), 404

    # Get query parameters
    employee_id = request.args.get('employee_id')
    date_str = request.args.get('date')
    start_date_str = request.args.get('start_date')
    end_date_str = request.args.get('end_date')
    department_id = request.args.get('department_id')

    # Pagination parameters
    page = int(request.args.get('page', 1))
    per_page = min(int(request.args.get('per_page', 50)), 500)
    include_inactive = request.args.get('include_inactive', 'false').lower() == 'true'

    # Validate pagination
    if page < 1:
        return jsonify({
            "success": False,
            "error": {
                "code": "INVALID_PAGE_NUMBER",
                "message": "Page number must be 1 or greater",
                "details": f"Received page: {page}"
            }
        }), 400
    if per_page < 1:
        return jsonify({
            "success": False,
            "error": {
                "code": "INVALID_PER_PAGE",
                "message": "Per page must be 1 or greater",
                "details": f"Received per_page: {per_page}"
            }
        }), 400

    # Parse dates
    date_value = HelperMethods.parse_date(date_str) if date_str else None
    start_date = HelperMethods.parse_date(start_date_str) if start_date_str else None
    end_date = HelperMethods.parse_date(end_date_str) if end_date_str else None

    if start_date and end_date and start_date > end_date:
        return jsonify({
            "success": False,
            "error": {
                "code": "INVALID_DATE_RANGE",
                "message": "start_date cannot be after end_date",
                "details": f"start_date: {start_date_str}, end_date: {end_date_str}"
            }
        }), 400

    from app import db_connection
    with db_connection.get_session(database_name) as session:
        try:
            from application.Models.employees.employee import Employee

            if not include_inactive:
                query = session.query(Attendance).join(
                    Employee, Attendance.employee_id == Employee.employee_id
                ).filter(Employee.status == 'active')
            else:
                query = session.query(Attendance)

            if employee_id:
                try:
                    if isinstance(employee_id, str):
                        employee_id = uuid.UUID(employee_id)
                    query = query.filter(Attendance.employee_id == employee_id)
                except ValueError:
                    return jsonify({
                        "success": False,
                        "error": {
                            "code": "INVALID_EMPLOYEE_ID",
                            "message": "Invalid employee_id format",
                            "details": f"employee_id must be a valid UUID. Received: {employee_id}"
                        }
                    }), 400

            if date_value:
                query = query.filter(Attendance.date == date_value)
            elif start_date and end_date:
                query = query.filter(Attendance.date >= start_date, Attendance.date <= end_date)
            elif start_date:
                query = query.filter(Attendance.date >= start_date)
            elif end_date:
                query = query.filter(Attendance.date <= end_date)

            if department_id:
                if not include_inactive:
                    query = query.filter(Employee.department_id == uuid.UUID(department_id))
                else:
                    query = query.join(Employee, Attendance.employee_id == Employee.employee_id)\
                                 .filter(Employee.department_id == uuid.UUID(department_id))

            # Total records
            total_records = query.count()

            # Apply pagination
            offset = (page - 1) * per_page
            attendance_records = query.order_by(
                Attendance.date.desc(),
                Attendance.check_in_time.desc()
            ).offset(offset).limit(per_page).all()

            attendance_data = [record.public_to_dict() for record in attendance_records]
            
            total_pages = (total_records + per_page - 1) // per_page
            has_next = page < total_pages
            has_prev = page > 1

            return jsonify({
                "status": "success",
                "attendance_records": attendance_data,  
                "pagination": {
                    "page": page,
                    "per_page": per_page,
                    "total_records": total_records,
                    "total_pages": total_pages,
                    "has_next": has_next,
                    "has_prev": has_prev,
                    "count": total_records,  # added
                    "next": page + 1 if has_next else None,  # added
                    "previous": page - 1 if has_prev else None  # added
                },
                "filters_applied": {
                    "employee_id": employee_id,
                    "date": date_str,
                    "start_date": start_date_str,
                    "end_date": end_date_str,
                    "include_inactive": include_inactive,
                    "department_id": department_id
                }
            }), 200

        except Exception as e:
            app.logger.error(f"[API_CLIENT] Error getting attendance records for company {company_id}: {e}")
            return jsonify({
                "success": False,
                "error": {
                    "code": "INTERNAL_SERVER_ERROR",
                    "message": "An error occurred while retrieving attendance records",
                    "details": "Please try again later or contact support if the issue persists"
                }
            }), 500

@attendance_api_clients_bp.route('/api/v1/attendance/summary/pdf', methods=['GET'])
@api_client_required
@api_rate_limit(requests_per_hour=100, requests_per_minute=10)  # Lower limits for resource-intensive operations
def download_attendance_summary_pdf():
    """
    Download monthly attendance summary report as PDF.

    Authentication: Requires valid API client token in Authorization header.
    The company_id is automatically extracted from the authenticated API client.

    Query Parameters:
    - start_date (optional): Start date for date range filter (YYYY-MM-DD)
    - end_date (optional): End date for date range filter (YYYY-MM-DD)
    - employee_id (optional): Filter by specific employee UUID

    Default Behavior:
    - If start_date and end_date are not provided, defaults to the current month
    - Returns PDF file as attachment with appropriate filename

    Returns:
    - PDF file download with Content-Type: application/pdf
    - Filename format: attendance_summary_[employee_name_]YYYYMMDD[_to_YYYYMMDD].pdf

    Rate Limits:
    - 100 requests per hour per client
    - 10 requests per minute per client (due to resource-intensive PDF generation)
    """
    try:
        company_id = g.api_client_company_id

        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)

        if not database_name:
            return jsonify({
                "success": False,
                "error": {
                    "code": "COMPANY_NOT_FOUND",
                    "message": "Company not found",
                    "details": f"No database found for company ID: {company_id}. Please contact support."
                }   
            }), 404

        # Get company details
        try:
            company = Company.query.filter_by(company_id=company_id).first()
        except Exception as e:
            app.logger.error(f"Error getting company details: {e}")
            return jsonify({
                "success": False,
                "error": {
                    "code": "COMPANY_NOT_FOUND",
                    "message": "Company not found",
                    "details": f"No database found for company ID: {company_id}. Please contact support."
                }   
            }), 404

        # Get query parameters
        employee_id = request.args.get('employee_id')
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')

        # Parse dates
        start_date = HelperMethods.parse_date(start_date_str) if start_date_str else None
        end_date = HelperMethods.parse_date(end_date_str) if end_date_str else None

        # Default to current month if no dates provided
        if not start_date and not end_date:
            today = date.today()
            start_date = today.replace(day=1)
            # Get last day of current month
            if today.month == 12:
                end_date = today.replace(year=today.year + 1, month=1, day=1) - timedelta(days=1)
            else:
                end_date = today.replace(month=today.month + 1, day=1) - timedelta(days=1)

        # Validate date range
        if start_date and end_date and start_date > end_date:
            return jsonify({
                "success": False,
                "error": {
                    "code": "INVALID_DATE_RANGE",
                    "message": "start_date cannot be after end_date",
                    "details": f"start_date: {start_date_str}, end_date: {end_date_str}"
                }
            }), 400

        app.logger.info(f"[API_CLIENT] Generating monthly summary PDF for company {company_id} from {start_date} to {end_date}")

        # Connect to the database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Calculate employee summaries
            employee_summaries = _calculate_monthly_summary_data(session, start_date, end_date, employee_id)

            if not employee_summaries:
                return jsonify({
                    "success": False,
                    "error": {
                        "code": "NO_DATA_FOUND",
                        "message": "No attendance data found for the specified period",
                        "details": f"Date range: {start_date} to {end_date}" + (f", Employee ID: {employee_id}" if employee_id else "")
                    }
                }), 404

            # Calculate department summaries
            department_summaries = _calculate_department_summary(employee_summaries)

            # Generate PDF
            pdf_buffer = _generate_monthly_summary_pdf(company, employee_summaries, department_summaries, start_date, end_date)

            # Create response
            response = make_response(pdf_buffer.getvalue())
            response.headers['Content-Type'] = 'application/pdf'

            # Generate filename
            filename_parts = ['attendance_summary']
            if employee_id:
                employee = session.query(Employee).filter_by(employee_id=employee_id).first()
                if employee:
                    filename_parts.append(f"{employee.first_name}_{employee.last_name}")
            filename_parts.append(start_date.strftime('%Y%m%d'))
            if end_date and end_date != start_date:
                filename_parts.append(f"to_{end_date.strftime('%Y%m%d')}")

            filename = '_'.join(filename_parts) + '.pdf'
            response.headers['Content-Disposition'] = f'attachment; filename="{filename}"'

            app.logger.info(f"[API_CLIENT] Successfully generated monthly summary PDF: {filename} for company {company_id}")
            return response

    except Exception as e:
        app.logger.error(f"[API_CLIENT] Error generating monthly summary PDF for company {company_id}: {e}")
        return jsonify({
            "success": False,
            "error": {
                "code": "PDF_GENERATION_ERROR",
                "message": "An error occurred while generating the PDF report",
                "details": "Please try again later or contact support if the issue persists"
            }
        }), 500


@attendance_api_clients_bp.route('/api/v1/attendance/summary/excel', methods=['GET'])
@api_client_required
@api_rate_limit(requests_per_hour=100, requests_per_minute=10)  # Lower limits for resource-intensive operations
def download_attendance_summary_excel():
    """
    Download monthly attendance summary report as Excel.

    Authentication: Requires valid API client token in Authorization header.
    The company_id is automatically extracted from the authenticated API client.

    Query Parameters:
    - start_date (optional): Start date for date range filter (YYYY-MM-DD)
    - end_date (optional): End date for date range filter (YYYY-MM-DD)
    - employee_id (optional): Filter by specific employee UUID

    Default Behavior:
    - If start_date and end_date are not provided, defaults to the current month
    - Returns Excel file as attachment with appropriate filename

    Returns:
    - Excel file download with Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
    - Filename format: attendance_summary_[employee_name_]YYYYMMDD[_to_YYYYMMDD].xlsx

    Rate Limits:
    - 100 requests per hour per client
    - 10 requests per minute per client (due to resource-intensive Excel generation)
    """
    try:
        # Company ID is automatically extracted from the authenticated API client token
        company_id = g.api_client_company_id

        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({
                "success": False,
                "error": {
                    "code": "COMPANY_DATABASE_NOT_FOUND",
                    "message": "Company database configuration not found",
                    "details": f"No database found for company ID: {company_id}"
                }
            }), 404

        # Get company details
        try:
            company = Company.query.filter_by(company_id=company_id).first()
        except Exception as e:
            app.logger.error(f"[API_CLIENT] Error getting company details: {e}")
            return jsonify({
                "success": False,
                "error": {
                    "code": "COMPANY_NOT_FOUND",
                    "message": "Company not found",
                    "details": f"No database found for company ID: {company_id}. Please contact support."
                }
            }), 404

        # Get query parameters
        employee_id = request.args.get('employee_id')
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')

        # Parse dates
        start_date = HelperMethods.parse_date(start_date_str) if start_date_str else None
        end_date = HelperMethods.parse_date(end_date_str) if end_date_str else None

        # Default to current month if no dates provided
        if not start_date and not end_date:
            today = date.today()
            start_date = today.replace(day=1)
            # Get last day of current month
            if today.month == 12:
                end_date = today.replace(year=today.year + 1, month=1, day=1) - timedelta(days=1)
            else:
                end_date = today.replace(month=today.month + 1, day=1) - timedelta(days=1)

        # Validate date range
        if start_date and end_date and start_date > end_date:
            return jsonify({
                "success": False,
                "error": {
                    "code": "INVALID_DATE_RANGE",
                    "message": "start_date cannot be after end_date",
                    "details": f"start_date: {start_date_str}, end_date: {end_date_str}"
                }
            }), 400

        app.logger.info(f"[API_CLIENT] Generating monthly summary Excel for company {company_id} from {start_date} to {end_date}")

        # Connect to the database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Calculate employee summaries
            employee_summaries = _calculate_monthly_summary_data(session, start_date, end_date, employee_id)

            if not employee_summaries:
                return jsonify({
                    "success": False,
                    "error": {
                        "code": "NO_DATA_FOUND",
                        "message": "No attendance data found for the specified period",
                        "details": f"Date range: {start_date} to {end_date}" + (f", Employee ID: {employee_id}" if employee_id else "")
                    }
                }), 404

            # Calculate department summaries
            department_summaries = _calculate_department_summary(employee_summaries)

            # Generate Excel
            excel_buffer = _generate_monthly_summary_excel(company, employee_summaries, department_summaries, start_date, end_date)

            # Create response
            response = make_response(excel_buffer.getvalue())
            response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'

            # Generate filename
            filename_parts = ['attendance_summary']
            if employee_id:
                employee = session.query(Employee).filter_by(employee_id=employee_id).first()
                if employee:
                    filename_parts.append(f"{employee.first_name}_{employee.last_name}")
            filename_parts.append(start_date.strftime('%Y%m%d'))
            if end_date and end_date != start_date:
                filename_parts.append(f"to_{end_date.strftime('%Y%m%d')}")

            filename = '_'.join(filename_parts) + '.xlsx'
            response.headers['Content-Disposition'] = f'attachment; filename="{filename}"'

            app.logger.info(f"[API_CLIENT] Successfully generated monthly summary Excel: {filename} for company {company_id}")
            return response

    except Exception as e:
        app.logger.error(f"[API_CLIENT] Error generating monthly summary Excel for company {company_id}: {e}")
        return jsonify({
            "success": False,
            "error": {
                "code": "EXCEL_GENERATION_ERROR",
                "message": "An error occurred while generating the Excel report",
                "details": "Please try again later or contact support if the issue persists"
            }
        }), 500



