from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from datetime import datetime
from application.Models.apiclient.api_client import APIClient
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.decorators.rate_limit import rate_limit
from application.database import central_db as db

api_client_auth_bp = Blueprint("api_client_auth", __name__)

@api_client_auth_bp.route("/api/auth/api_clients", methods=["POST"])
@token_required
@roles_required('hr')
def create_api_client():
    """Create a new API client with auto-generated credentials."""
    app.logger.info(f"Creating API client - Request data: {request.json}")
    data = request.json or {}
    name = data.get("name")
    company_id = g.user.get('company_id')
    user_id = g.user.get('user_id')
    
    if not name:
        return jsonify({"error": "Missing app name"}), 400

    try:
        client, secret = APIClient.create_client_auto(company_id, name, user_id)
        return jsonify({
            "client_id": str(client.client_id),
            "client_secret": secret,
            "name": client.name
        })
    except Exception as e:
        app.logger.error(f"Error creating API client: {e}")
        return jsonify({"error": "Failed to create API client"}),400

@api_client_auth_bp.route("/api/auth/api_clients/<client_id>", methods=["DELETE"])
@token_required
@roles_required('hr')
def delete_api_client(client_id):
    """
    Delete an API client.

    Security Features:
    - Requires HR role authentication
    - Validates company ownership
    - Comprehensive audit logging
    - Proper session management
    """
    user_id = g.user.get('user_id')
    company_id = g.user.get('company_id')

    app.logger.info(f"[SECURITY] API client deletion requested - Client: {client_id}, User: {user_id}, Company: {company_id}")

    try:
        # Find the client using the same session context as the model
        client = db.session.query(APIClient).filter_by(client_id=client_id).first()
        if not client:
            app.logger.warning(f"[SECURITY] Delete attempt for non-existent client: {client_id} by user: {user_id}")
            return jsonify({"error": "API client not found"}), 404

        # Verify company ownership
        if client.company_id != company_id:
            app.logger.warning(f"[SECURITY] Unauthorized delete attempt - Client: {client_id}, User: {user_id}, User Company: {company_id}, Client Company: {client.company_id}")
            return jsonify({"error": "Unauthorized access"}), 403

        # Store client info for logging before deletion
        client_name = client.name

        # Delete the client using the same session
        db.session.delete(client)
        db.session.commit()

        # Audit log the successful deletion
        app.logger.info(f"[SECURITY] API client deleted successfully - Client: {client_id} ({client_name}), Deleted by: {user_id}, Company: {company_id}")

        return jsonify({
            "message": "API client deleted successfully",
            "client_id": client_id,
            "deleted_at": datetime.utcnow().isoformat()
        }), 200

    except Exception as e:
        db.session.rollback()
        app.logger.error(f"[SECURITY] API client deletion system error - Client: {client_id}, User: {user_id}, Error: {str(e)}")
        return jsonify({"error": "Failed to delete API client"}), 500

@api_client_auth_bp.route("/api/auth/api_clients", methods=["GET"])
@token_required
@roles_required('hr')
def get_api_clients():
    """
    Get all API clients that belong to the user's company.

    Security Features:
    - Requires HR role authentication
    - Company-scoped results only
    - Comprehensive audit logging
    """
    user_id = g.user.get('user_id')
    company_id = g.user.get('company_id')

    app.logger.info(f"[SECURITY] API clients list requested - User: {user_id}, Company: {company_id}")

    try:
        # Query clients using explicit session for consistency
        clients = db.session.query(APIClient).filter_by(company_id=company_id, is_active=True).all()

        clients_data = [
            {
                "client_id": str(client.client_id),
                "name": client.name,
                "created_at": client.created_at.isoformat() if client.created_at else None,
                "last_used_at": client.last_used_at.isoformat() if client.last_used_at else None,
                "is_active": client.is_active
            }
            for client in clients
        ]

        app.logger.info(f"[SECURITY] API clients list retrieved - User: {user_id}, Company: {company_id}, Count: {len(clients_data)}")

        return jsonify({
            "clients": clients_data,
            "count": len(clients_data)
        }), 200

    except Exception as e:
        app.logger.error(f"[SECURITY] API clients list system error - User: {user_id}, Company: {company_id}, Error: {str(e)}")
        return jsonify({"error": "Failed to get API clients"}), 500


@api_client_auth_bp.route("/api/auth/api_clients/<client_id>/reset-secret", methods=["PATCH"])
@token_required
@roles_required('hr')
@rate_limit(max_requests=3, window_minutes=60, per_user=True)
def reset_api_client_secret(client_id):
    """
    Reset the secret for an API client.

    Security Features:
    - Requires HR role authentication
    - Validates company ownership
    - Generates cryptographically secure new secret
    - Immediately invalidates old secret
    - Comprehensive audit logging
    """
    user_id = g.user.get('user_id')
    company_id = g.user.get('company_id')

    app.logger.info(f"[SECURITY] API client secret reset requested - Client: {client_id}, User: {user_id}, Company: {company_id}")

    try:
        # Validate client exists and is active using explicit session
        client = db.session.query(APIClient).filter_by(client_id=client_id, is_active=True).first()
        if not client:
            app.logger.warning(f"[SECURITY] Reset attempt for non-existent client: {client_id} by user: {user_id}")
            return jsonify({"error": "API client not found"}), 404

        # Verify company ownership
        if client.company_id != company_id:
            app.logger.warning(f"[SECURITY] Unauthorized reset attempt - Client: {client_id}, User: {user_id}, User Company: {company_id}, Client Company: {client.company_id}")
            return jsonify({"error": "Unauthorized access"}), 403

        # Generate new secret and update client
        new_secret = APIClient.reset_client_secret(client_id)

        # Audit log the successful reset
        app.logger.info(f"[SECURITY] API client secret reset successful - Client: {client_id} ({client.name}), Reset by: {user_id}, Company: {company_id}")

        return jsonify({
            "message": "Client secret reset successfully",
            "client_id": str(client.client_id),
            "client_secret": new_secret,
            "name": client.name,
            "reset_at": datetime.utcnow().isoformat()
        }), 200

    except ValueError as e:
        app.logger.error(f"[SECURITY] Client secret reset validation error - Client: {client_id}, User: {user_id}, Error: {str(e)}")
        return jsonify({"error": str(e)}), 400
    except Exception as e:
        app.logger.error(f"[SECURITY] Client secret reset system error - Client: {client_id}, User: {user_id}, Error: {str(e)}")
        return jsonify({"error": "Failed to reset API client secret"}), 500
