"""
OTP Authentication API for KaziSync HRMS
Handles email-based OTP authentication flow
"""

from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from datetime import datetime
import logging

from application.Models.user import User
from application.Models.employees.company_user import CompanyUser
from application.Models.company import Company
from application.services.auth.otp_service import OTPService
from application.utils.db_connection import DatabaseConnection
from application.decorators.token_required import token_required
from application.Helpers.helper_methods import HelperMethods

logger = logging.getLogger(__name__)
otp_auth_api = Blueprint('otp_auth_api', __name__)


@otp_auth_api.route('/api/auth/login-with-otp', methods=['POST'])
def login_with_otp():
    """
    Enhanced login endpoint that requires OTP verification
    Step 1: Validate credentials and send OTP
    """
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        
        if not username or not password:
            return jsonify({
                "success": False,
                "message": "Username and password are required"
            }), 400
        
        # Extract company ID from username if present
        from application.utils.username_encoder import extract_company_id_from_username
        company_id, original_username = extract_company_id_from_username(username)
        
        user_info = None
        user_type = None
        
        if company_id:
            # Company user login
            try:
                database_name = Company.get_database_given_company_id(company_id)
                if not database_name:
                    return jsonify({
                        "success": False,
                        "message": "Company not found"
                    }), 404
                
                with DatabaseConnection.get_session(database_name) as session:
                    company_user = CompanyUser.get_user_by_username(session, original_username)
                    if not company_user:
                        return jsonify({
                            "success": False,
                            "message": "Invalid credentials"
                        }), 401
                    
                    # Verify password
                    if not CompanyUser.verify_password(session, original_username, password):
                        return jsonify({
                            "success": False,
                            "message": "Invalid credentials"
                        }), 401
                    
                    user_info = {
                        'user_id': str(company_user.user_id),
                        'username': company_user.username,
                        'email': company_user.email,
                        'first_name': company_user.first_name,
                        'last_name': company_user.last_name,
                        'role': company_user.role,
                        'company_id': company_id
                    }
                    user_type = 'company_user'
                    
            except Exception as e:
                logger.error(f"Error during company user authentication: {str(e)}")
                return jsonify({
                    "success": False,
                    "message": "Authentication failed"
                }), 500
        else:
            # Central user login
            try:
                user = User.get_user_by_username(username)
                if not user:
                    return jsonify({
                        "success": False,
                        "message": "Invalid credentials"
                    }), 401
                
                # Verify password
                if not User.login_user(username, password):
                    return jsonify({
                        "success": False,
                        "message": "Invalid credentials"
                    }), 401
                
                user_info = {
                    'user_id': str(user.user_id),
                    'username': user.username,
                    'email': user.email,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'role': user.role,
                    'company_id': None
                }
                user_type = 'central_user'
                
            except Exception as e:
                logger.error(f"Error during central user authentication: {str(e)}")
                return jsonify({
                    "success": False,
                    "message": "Authentication failed"
                }), 500
        
        if not user_info:
            return jsonify({
                "success": False,
                "message": "Authentication failed"
            }), 401
        
        # Generate and send OTP
        otp_service = OTPService()
        user_display_name = f"{user_info['first_name']} {user_info['last_name']}".strip()
        
        success, message, session_id = otp_service.send_otp_email(
            user_id=user_info['user_id'],
            email=user_info['email'],
            user_name=user_display_name,
            purpose='login',
            company_id=company_id
        )
        
        if not success:
            return jsonify({
                "success": False,
                "message": f"Failed to send OTP: {message}"
            }), 500
        
        # Store user info in session for OTP verification
        # In production, you might want to use Redis or encrypted tokens
        
        return jsonify({
            "success": True,
            "message": "OTP sent to your email address",
            "otp_session_id": session_id,
            "user_type": user_type,
            "email_masked": HelperMethods.mask_email(user_info['email']),
            "next_step": "verify_otp"
        }), 200
        
    except Exception as e:
        logger.error(f"Error in login_with_otp: {str(e)}")
        return jsonify({
            "success": False,
            "message": "An error occurred during login"
        }), 500


@otp_auth_api.route('/api/auth/verify-otp', methods=['POST'])
def verify_otp():
    """
    Step 2: Verify OTP and complete login
    """
    try:
        data = request.get_json()
        otp_session_id = data.get('otp_session_id')
        otp_code = data.get('otp_code')
        
        if not otp_session_id or not otp_code:
            return jsonify({
                "success": False,
                "message": "OTP session ID and code are required"
            }), 400
        
        # Validate OTP
        otp_service = OTPService()
        
        # Use central database for OTP validation
        with DatabaseConnection.get_session('central_db') as session:
            is_valid, message, user_info = otp_service.validate_otp(
                session, otp_session_id, otp_code
            )
            
            if not is_valid:
                return jsonify({
                    "success": False,
                    "message": message
                }), 401
        
        # Generate JWT token for authenticated user
        from application.Models.refreshtoken import RefreshToken
        
        # Create token payload
        token_payload = {
            'user_id': user_info['user_id'],
            'email': user_info['email'],
            'purpose': user_info['purpose']
        }
        
        # Generate access token
        access_token = RefreshToken.generate_token(token_payload)
        
        # Get complete user information
        complete_user_info = None
        if user_info.get('purpose') == 'login':
            # Determine if this is a company user or central user
            company_id = None
            
            # Try to find company association
            try:
                # Check if this is a company user
                from application.utils.username_encoder import extract_company_id_from_username
                # This is a simplified approach - in production you'd store this info during OTP creation
                
                # For now, we'll determine user type by checking both databases
                # First check central users
                central_user = User.get_user_by_id(user_info['user_id'])
                if central_user:
                    complete_user_info = {
                        'user_id': str(central_user.user_id),
                        'username': central_user.username,
                        'email': central_user.email,
                        'first_name': central_user.first_name,
                        'last_name': central_user.last_name,
                        'role': central_user.role,
                        'user_type': 'central_user',
                        'company_id': None,
                        'companies': [str(company.company_id) for company in central_user.companies]
                    }
                else:
                    # This might be a company user - we'd need to search company databases
                    # For now, return basic info
                    complete_user_info = {
                        'user_id': user_info['user_id'],
                        'email': user_info['email'],
                        'user_type': 'company_user',
                        'company_id': company_id
                    }
                    
            except Exception as e:
                logger.error(f"Error getting complete user info: {str(e)}")
                complete_user_info = user_info
        
        return jsonify({
            "success": True,
            "message": "Login successful",
            "access_token": access_token,
            "user_info": complete_user_info,
            "login_time": datetime.utcnow().isoformat()
        }), 200
        
    except Exception as e:
        logger.error(f"Error in verify_otp: {str(e)}")
        return jsonify({
            "success": False,
            "message": "An error occurred during OTP verification"
        }), 500


@otp_auth_api.route('/api/auth/resend-otp', methods=['POST'])
def resend_otp():
    """
    Resend OTP code to user's email
    """
    try:
        data = request.get_json()
        otp_session_id = data.get('otp_session_id')
        
        if not otp_session_id:
            return jsonify({
                "success": False,
                "message": "OTP session ID is required"
            }), 400
        
        # Get OTP token info
        with DatabaseConnection.get_session('central_db') as session:
            from application.Models.auth.otp_token import OTPToken
            
            otp_token = OTPToken.find_by_session_id(session, otp_session_id)
            if not otp_token:
                return jsonify({
                    "success": False,
                    "message": "Invalid OTP session"
                }), 404
            
            # Check if too many recent requests
            otp_service = OTPService()
            rate_limit_ok, rate_limit_msg = otp_service.check_rate_limit(
                session, user_id=otp_token.user_id
            )
            
            if not rate_limit_ok:
                return jsonify({
                    "success": False,
                    "message": rate_limit_msg
                }), 429
            
            # Mark old token as used
            otp_token.is_used = True
            session.commit()
            
            # Generate new OTP
            user_display_name = "User"  # You might want to get this from user info
            
            success, message, new_session_id = otp_service.send_otp_email(
                user_id=otp_token.user_id,
                email=otp_token.email,
                user_name=user_display_name,
                purpose=otp_token.purpose
            )
            
            if not success:
                return jsonify({
                    "success": False,
                    "message": f"Failed to resend OTP: {message}"
                }), 500
            
            return jsonify({
                "success": True,
                "message": "OTP resent successfully",
                "otp_session_id": new_session_id,
                "email_masked": HelperMethods.mask_email(otp_token.email)
            }), 200
            
    except Exception as e:
        logger.error(f"Error in resend_otp: {str(e)}")
        return jsonify({
            "success": False,
            "message": "An error occurred while resending OTP"
        }), 500


@otp_auth_api.route('/api/auth/otp-status/<session_id>', methods=['GET'])
def get_otp_status(session_id):
    """
    Get OTP session status
    """
    try:
        with DatabaseConnection.get_session('central_db') as session:
            from application.Models.auth.otp_token import OTPToken
            
            otp_token = OTPToken.find_by_session_id(session, session_id)
            if not otp_token:
                return jsonify({
                    "success": False,
                    "message": "OTP session not found"
                }), 404
            
            return jsonify({
                "success": True,
                "status": {
                    "is_valid": otp_token.is_valid(),
                    "is_expired": otp_token.is_expired(),
                    "is_used": otp_token.is_used,
                    "attempts": otp_token.attempts,
                    "expires_at": otp_token.expires_at.isoformat(),
                    "email_masked": HelperMethods.mask_email(otp_token.email)
                }
            }), 200
            
    except Exception as e:
        logger.error(f"Error in get_otp_status: {str(e)}")
        return jsonify({
            "success": False,
            "message": "An error occurred while checking OTP status"
        }), 500
