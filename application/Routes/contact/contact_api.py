from flask import Blueprint, request, jsonify, current_app as app
import re
import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime, timedelta
from typing import Dict, Any

from dotenv import load_dotenv

# load environment variables
load_dotenv()

# Create the blueprint
contact_api = Blueprint('contact_api', __name__)

# Simple in-memory rate limiting (for production, use Redis)
rate_limit_storage = {}

def send_simple_email(to_email, subject, message):
    """Send a simple email using SMTP"""
    try:
        # Get email settings from environment
        smtp_server = os.getenv('MAIL_SERVER', 'mail.kazisync.com')
        smtp_port = int(os.getenv('MAIL_PORT', 587))
        smtp_username = os.getenv('MAIL_USERNAME', '<EMAIL>')
        smtp_password = os.getenv('MAIL_PASSWORD', '')

        app.logger.info(f"Email config - Server: {smtp_server}, Port: {smtp_port}, Username: {smtp_username}")

        if not smtp_password:
            app.logger.error("MAIL_PASSWORD not set in environment")
            return False

        # Create message
        msg = MIMEMultipart()
        msg['From'] = smtp_username
        msg['To'] = to_email
        msg['Subject'] = subject
        msg.attach(MIMEText(message, 'plain'))

        # Send email
        app.logger.info(f"Connecting to SMTP server {smtp_server}:{smtp_port}")
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(smtp_username, smtp_password)
        server.send_message(msg)
        server.quit()

        app.logger.info(f"Email sent successfully to {to_email}")
        return True

    except Exception as e:
        app.logger.error(f"Failed to send email: {str(e)}")
        return False

def clean_rate_limit_storage():
    """Clean expired rate limit entries"""
    current_time = datetime.utcnow()
    expired_keys = []
    
    for key, data in rate_limit_storage.items():
        if current_time > data['reset_time']:
            expired_keys.append(key)
    
    for key in expired_keys:
        del rate_limit_storage[key]

def check_rate_limit(ip_address: str, limit: int = 5, window_minutes: int = 60) -> tuple[bool, str]:
    """
    Check if IP address has exceeded rate limit
    
    Args:
        ip_address: Client IP address
        limit: Maximum requests allowed in window
        window_minutes: Time window in minutes
    
    Returns:
        Tuple of (is_allowed, message)
    """
    clean_rate_limit_storage()
    
    current_time = datetime.utcnow()
    
    if ip_address not in rate_limit_storage:
        rate_limit_storage[ip_address] = {
            'count': 1,
            'reset_time': current_time + timedelta(minutes=window_minutes)
        }
        return True, ""
    
    data = rate_limit_storage[ip_address]
    
    if current_time > data['reset_time']:
        # Reset the counter
        rate_limit_storage[ip_address] = {
            'count': 1,
            'reset_time': current_time + timedelta(minutes=window_minutes)
        }
        return True, ""
    
    if data['count'] >= limit:
        return False, f"Rate limit exceeded. Please try again in {window_minutes} minutes."
    
    data['count'] += 1
    return True, ""

def validate_contact_data(data: Dict[str, Any]) -> tuple[bool, str, Dict[str, Any]]:
    """
    Validate and sanitize contact form data
    
    Args:
        data: Raw form data
    
    Returns:
        Tuple of (is_valid, error_message, cleaned_data)
    """
    errors = []
    cleaned_data = {}
    
    # Required fields
    required_fields = ['name', 'email', 'phone', 'message']
    for field in required_fields:
        if not data.get(field) or not str(data.get(field)).strip():
            errors.append(f"{field} is required")
    
    if errors:
        return False, "; ".join(errors), {}
    
    # Clean and validate name
    name = bleach.clean(str(data['name']).strip())
    if len(name) < 2 or len(name) > 100:
        errors.append("Name must be between 2 and 100 characters")
    cleaned_data['name'] = name
    
    # Validate email
    email = str(data['email']).strip().lower()
    try:
        validate_email(email)
        cleaned_data['email'] = email
    except EmailNotValidError:
        errors.append("Invalid email address")
    
    # Validate phone number (basic validation)
    phone = str(data['phone']).strip()
    # Remove common phone number characters for validation
    phone_digits = re.sub(r'[^\d+]', '', phone)
    if len(phone_digits) < 7 or len(phone_digits) > 20:
        errors.append("Invalid phone number")
    cleaned_data['phone'] = phone
    
    # Clean message
    message = bleach.clean(str(data['message']).strip())
    if len(message) < 10 or len(message) > 2000:
        errors.append("Message must be between 10 and 2000 characters")
    cleaned_data['message'] = message
    
    # Optional fields
    if data.get('company'):
        company = bleach.clean(str(data['company']).strip())
        if len(company) > 100:
            errors.append("Company name must be less than 100 characters")
        cleaned_data['company'] = company
    
    if data.get('subject'):
        subject = bleach.clean(str(data['subject']).strip())
        if len(subject) > 200:
            errors.append("Subject must be less than 200 characters")
        cleaned_data['subject'] = subject
    
    if data.get('inquiry_type'):
        inquiry_type = bleach.clean(str(data['inquiry_type']).strip())
        allowed_types = ['demo', 'support', 'sales', 'partnership', 'other']
        if inquiry_type not in allowed_types:
            inquiry_type = 'other'
        cleaned_data['inquiry_type'] = inquiry_type
    
    if errors:
        return False, "; ".join(errors), {}
    
    return True, "", cleaned_data

@contact_api.route('/api/contact/submit', methods=['POST'])
def submit_contact_form():
    """
    Submit contact us form
    
    Expected JSON payload:
    {
        "name": "John Doe",                    # Required
        "email": "<EMAIL>",           # Required  
        "phone": "+1234567890",                # Required
        "message": "I'm interested in...",     # Required
        "company": "Example Corp",             # Optional
        "subject": "Demo Request",             # Optional
        "inquiry_type": "demo"                 # Optional: demo, support, sales, partnership, other
    }
    """
    try:
        # Get client IP for rate limiting
        client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
        if client_ip and ',' in client_ip:
            client_ip = client_ip.split(',')[0].strip()
        
        # Check rate limit
        is_allowed, rate_limit_msg = check_rate_limit(client_ip)
        if not is_allowed:
            return jsonify({
                "success": False,
                "message": rate_limit_msg
            }), 429
        
        # Debug request information
        app.logger.info(f"Content-Type: {request.content_type}")
        app.logger.info(f"Request headers: {dict(request.headers)}")

        # Get and validate data
        data = request.get_json(force=True)  # Force JSON parsing
        if not data:
            app.logger.error("No JSON data received")
            return jsonify({
                "success": False,
                "message": "Invalid JSON data. Please ensure Content-Type is application/json"
            }), 400

        app.logger.info(f"Received contact form data: {data}")
        
        # Validate and clean data
        is_valid, error_msg, cleaned_data = validate_contact_data(data)
        if not is_valid:
            return jsonify({
                "success": False,
                "message": error_msg
            }), 400
        
        # Initialize email service
        try:
            from flask import current_app
            from flask_mail import Mail

            # Use the existing mail instance from app
            mail = current_app.extensions.get('mail')
            if not mail:
                mail = Mail(current_app)

            email_service = EmailService(mail)
            app.logger.info("Email service initialized successfully")
        except Exception as e:
            app.logger.error(f"Failed to initialize email service: {str(e)}")
            return jsonify({
                "success": False,
                "message": "Email service initialization failed"
            }), 500
        
        # Prepare email context
        email_context = {
            'customer_name': cleaned_data['name'],
            'customer_email': cleaned_data['email'],
            'customer_phone': cleaned_data['phone'],
            'customer_company': cleaned_data.get('company', 'Not provided'),
            'inquiry_subject': cleaned_data.get('subject', 'General Inquiry'),
            'inquiry_type': cleaned_data.get('inquiry_type', 'other'),
            'message': cleaned_data['message'],
            'submitted_at': datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC'),
            'client_ip': client_ip
        }
        
        # Create admin notification email content
        message_html = email_context['message'].replace('\n', '<br>')
        admin_html_content = f"""
        <h2>New Contact Inquiry - KaziSync</h2>
        <p><strong>Customer Information:</strong></p>
        <ul>
            <li><strong>Name:</strong> {email_context['customer_name']}</li>
            <li><strong>Email:</strong> <a href="mailto:{email_context['customer_email']}">{email_context['customer_email']}</a></li>
            <li><strong>Phone:</strong> <a href="tel:{email_context['customer_phone']}">{email_context['customer_phone']}</a></li>
            <li><strong>Company:</strong> {email_context['customer_company']}</li>
            <li><strong>Subject:</strong> {email_context['inquiry_subject']}</li>
            <li><strong>Type:</strong> {email_context['inquiry_type']}</li>
        </ul>
        <p><strong>Message:</strong></p>
        <div style="background-color: #f5f5f5; padding: 15px; border-left: 4px solid #007bff;">
            {message_html}
        </div>
        <p><strong>Submitted:</strong> {email_context['submitted_at']}</p>
        <p><strong>IP Address:</strong> {email_context['client_ip']}</p>
        """

        admin_text_content = f"""
        New Contact Inquiry - KaziSync

        Customer Information:
        Name: {email_context['customer_name']}
        Email: {email_context['customer_email']}
        Phone: {email_context['customer_phone']}
        Company: {email_context['customer_company']}
        Subject: {email_context['inquiry_subject']}
        Type: {email_context['inquiry_type']}

        Message:
        {email_context['message']}

        Submitted: {email_context['submitted_at']}
        IP Address: {email_context['client_ip']}
        """

        # Create customer auto-response email content
        customer_html_content = f"""
        <h2>Thank you for contacting KaziSync!</h2>
        <p>Dear {email_context['customer_name']},</p>
        <p>Thank you for reaching out to us. We have received your inquiry and will get back to you within 24 hours.</p>

        <p><strong>Your inquiry details:</strong></p>
        <ul>
            <li><strong>Subject:</strong> {email_context['inquiry_subject']}</li>
            <li><strong>Submitted:</strong> {email_context['submitted_at']}</li>
        </ul>

        <p>In the meantime, feel free to explore our website or contact us directly:</p>
        <ul>
            <li><strong>Email:</strong> <EMAIL></li>
            <li><strong>Website:</strong> https://kazisync.com</li>
        </ul>

        <p>Best regards,<br>The KaziSync Team</p>
        """

        customer_text_content = f"""
        Thank you for contacting KaziSync!

        Dear {email_context['customer_name']},

        Thank you for reaching out to us. We have received your inquiry and will get back to you within 24 hours.

        Your inquiry details:
        Subject: {email_context['inquiry_subject']}
        Submitted: {email_context['submitted_at']}

        In the meantime, feel free to explore our website or contact us directly:
        Email: <EMAIL>
        Website: https://kazisync.com

        Best regards,
        The KaziSync Team
        """

        # Send notification email to admin
        try:
            admin_message = email_service.create_message(
                recipients=['<EMAIL>'],
                subject=f"New Contact Inquiry: {email_context['inquiry_subject']}",
                html_body=admin_html_content,
                text_body=admin_text_content
            )
            app.logger.info("Admin message created successfully")
        except Exception as e:
            app.logger.error(f"Failed to create admin message: {str(e)}")
            return jsonify({
                "success": False,
                "message": "Failed to create admin notification"
            }), 500

        # Send auto-response to customer
        try:
            customer_message = email_service.create_message(
                recipients=[cleaned_data['email']],
                subject="Thank you for contacting KaziSync",
                html_body=customer_html_content,
                text_body=customer_text_content
            )
            app.logger.info("Customer message created successfully")
        except Exception as e:
            app.logger.error(f"Failed to create customer message: {str(e)}")
            return jsonify({
                "success": False,
                "message": "Failed to create customer response"
            }), 500

        # TEMPORARY: Skip email sending for testing - just log the emails
        app.logger.info("=== ADMIN EMAIL CONTENT ===")
        app.logger.info(f"To: <EMAIL>")
        app.logger.info(f"Subject: New Contact Inquiry: {email_context['inquiry_subject']}")
        app.logger.info(f"Content: {admin_text_content[:200]}...")

        app.logger.info("=== CUSTOMER EMAIL CONTENT ===")
        app.logger.info(f"To: {cleaned_data['email']}")
        app.logger.info(f"Subject: Thank you for contacting KaziSync")
        app.logger.info(f"Content: {customer_text_content[:200]}...")

        # For now, simulate successful email sending
        admin_success = True
        customer_success = True
        app.logger.info("Email sending simulated successfully (emails logged above)")
        
        if admin_success and customer_success:
            app.logger.info(f"Contact form submitted successfully by {cleaned_data['email']} from IP {client_ip}")
            return jsonify({
                "success": True,
                "message": "Thank you for contacting us! We'll get back to you within 24 hours."
            }), 200
        else:
            app.logger.error(f"Failed to send contact form emails for {cleaned_data['email']}")
            return jsonify({
                "success": False,
                "message": "There was an issue processing your request. Please try again or contact us <NAME_EMAIL>"
            }), 500
            
    except Exception as e:
        app.logger.error(f"Error in contact form submission: {str(e)}")
        return jsonify({
            "success": False,
            "message": "Internal server error. Please try again later."
        }), 500

@contact_api.route('/api/contact/health', methods=['GET'])
def contact_health_check():
    """Health check endpoint for contact service"""
    return jsonify({
        "success": True,
        "message": "Contact service is running",
        "timestamp": datetime.utcnow().isoformat()
    }), 200
