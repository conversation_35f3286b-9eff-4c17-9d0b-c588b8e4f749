from flask import Blueprint, request, jsonify, current_app as app
import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from datetime import datetime
from dotenv import load_dotenv

# load environment variables
load_dotenv()

# Create the blueprint
simple_contact_api = Blueprint('simple_contact_api', __name__)

def render_email_html(subject, body):
    """
    Returns a styled HTML email body with a hardcoded company footer.
    :param subject: Email subject/title
    :param body: Main content (string, can include HTML tags)
    """
    footer = """
        <strong>KaziSync – Work in Sync</strong><br>
        The modern HRMS built for teams of all sizes.<br>
        From payroll to performance, KaziSync helps you manage people, not paperwork.
    """
    return f"""
    <html>
    <head>
        <style>
            body {{
                font-family: Arial, Helvetica, sans-serif;
                background-color: #f9f9f9;
                color: #222;
                margin: 0;
                padding: 0;
            }}
            .container {{
                background: #fff;
                max-width: 600px;
                margin: 40px auto;
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.07);
                padding: 32px 24px;
            }}
            .header {{
                border-bottom: 1px solid #eee;
                padding-bottom: 12px;
                margin-bottom: 24px;
            }}
            .footer {{
                border-top: 1px solid #eee;
                margin-top: 32px;
                padding-top: 12px;
                font-size: 13px;
                color: #888;
                text-align: center;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h2>{subject}</h2>
            </div>
            <div class="content">
                {body}
            </div>
            <div class="footer">{footer}</div>
        </div>
    </body>
    </html>
    """

def send_simple_email(to_email, subject, message):
    """Send a simple email using SMTP"""
    try:
        # Get email settings from environment
        smtp_server = os.getenv('MAIL_SERVER', 'mail.kazisync.com')
        smtp_port = int(os.getenv('MAIL_PORT', 587))
        smtp_username = os.getenv('MAIL_USERNAME', '<EMAIL>')
        smtp_password = os.getenv('MAIL_PASSWORD', '')
        
        app.logger.info(f"Email config - Server: {smtp_server}, Port: {smtp_port}, Username: {smtp_username}")
        
        if not smtp_password:
            app.logger.error("MAIL_PASSWORD not set in environment")
            return False
        
        # Create message with only HTML part
        msg = MIMEMultipart('alternative')
        msg['From'] = smtp_username
        msg['To'] = to_email
        msg['Subject'] = subject

        # HTML part only
        html_body = render_email_html(subject, message.replace('\n', '<br>'))
        msg.attach(MIMEText(html_body, 'html'))
        
        # Send email
        app.logger.info(f"Connecting to SMTP server {smtp_server}:{smtp_port}")
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(smtp_username, smtp_password)
        server.send_message(msg)
        server.quit()
        
        app.logger.info(f"Email sent successfully to {to_email}")
        return True
        
    except Exception as e:
        app.logger.error(f"Failed to send email: {str(e)}")
        return False

@simple_contact_api.route('/api/contact/simple', methods=['POST'])
def submit_simple_contact_form():
    """Simple contact form submission"""
    try:
        # Get data
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "message": "No data provided"}), 400
        
        # Basic validation
        name = data.get('name', '').strip()
        email = data.get('email', '').strip()
        phone = data.get('phone', '').strip()
        message = data.get('message', '').strip()
        subject = data.get('subject', 'General Inquiry').strip()
        
        if not all([name, email, phone, message]):
            return jsonify({"success": False, "message": "Name, email, phone, and message are required"}), 400
        
        app.logger.info(f"Contact form submission from {name} ({email})")
        
        # Create simple email content
        admin_message = f"""
subject: New Contact Form Submission from {name}

Name: {name}
Email: {email}
Phone: {phone}
Company: {data.get('company', 'Not provided')}
Subject: {data.get('subject', 'General Inquiry')}

Message:
{message}

Submitted: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}
        """
        
        customer_message = f"""
Thank you for contacting KaziSync!

Dear {name},

We have received your inquiry and will get back to you within 24 hours.

Best regards,
The KaziSync Team
        """
        
        # Send emails
        admin_sent = send_simple_email('<EMAIL>', subject, admin_message)
        customer_sent = send_simple_email(email, 'Thank you for contacting KaziSync', customer_message)
        
        if admin_sent and customer_sent:
            return jsonify({
                "success": True,
                "message": "Thank you for contacting us! We'll get back to you within 24 hours."
            }), 200
        else:
            return jsonify({
                "success": False,
                "message": "There was an issue sending your message. Please try again."
            }), 500
            
    except Exception as e:
        app.logger.error(f"Contact form error: {str(e)}")
        return jsonify({
            "success": False,
            "message": "Internal server error"
        }), 500

@simple_contact_api.route('/api/contact/test-email', methods=['GET'])
def test_email():
    """Test email configuration"""
    try:
        # Check environment variables
        smtp_server = os.getenv('MAIL_SERVER')
        smtp_username = os.getenv('MAIL_USERNAME')
        smtp_password = os.getenv('MAIL_PASSWORD')
        
        missing = []
        if not smtp_server:
            missing.append('MAIL_SERVER')
        if not smtp_username:
            missing.append('MAIL_USERNAME')
        if not smtp_password:
            missing.append('MAIL_PASSWORD')
        
        if missing:
            return jsonify({
                "success": False,
                "message": f"Missing environment variables: {', '.join(missing)}"
            }), 400
        
        # Try to send a test email
        test_message = f"Test email from KaziSync contact form at {datetime.utcnow()}"
        success = send_simple_email('<EMAIL>', 'Test Email', test_message)
        
        if success:
            return jsonify({
                "success": True,
                "message": "Test email sent successfully!"
            }), 200
        else:
            return jsonify({
                "success": False,
                "message": "Failed to send test email"
            }), 500
            
    except Exception as e:
        app.logger.error(f"Test email error: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"Error: {str(e)}"
        }), 500
