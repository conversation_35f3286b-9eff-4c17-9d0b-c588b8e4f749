from flask import Blueprint, request, jsonify, current_app as app
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Create the blueprint
super_simple_contact = Blueprint('super_simple_contact', __name__)

def send_email(sender_email, receiver_email, password, smtp_server, smtp_port, subject, html_message):
    """Send email using your proven method"""
    try:
        # Connect to SMTP server
        server = smtplib.SMTP_SSL(smtp_server, smtp_port)
        server.login(sender_email, password)

        # Create a multipart message
        message = MIMEMultipart()
        message["From"] = sender_email
        message["To"] = receiver_email
        message["Subject"] = subject

        # Attach HTML message
        message.attach(MIMEText(html_message, "html"))

        # Send email
        server.sendmail(sender_email, receiver_email, message.as_string())
        server.quit()
        app.logger.info("Email sent successfully!")
        return True
    except Exception as e:
        app.logger.error(f"Failed to send email. Error: {e}")
        return False

@super_simple_contact.route('/api/contact/send', methods=['POST'])
def send_contact():
    """Super simple contact form using your email method"""
    try:
        data = request.get_json()

        # Basic validation
        if not data:
            return jsonify({"success": False, "message": "No data provided"}), 400

        # Required fields validation
        name = data.get('name', '').strip()
        email = data.get('email', '').strip()
        phone = data.get('phone', '').strip()
        message = data.get('message', '').strip()

        if not all([name, email, phone, message]):
            return jsonify({
                "success": False,
                "message": "Name, email, phone, and message are required"
            }), 400

        # Optional fields
        company = data.get('company', '').strip()
        subject = data.get('subject', '').strip()

        # Get email config
        sender_email = os.getenv('MAIL_USERNAME')
        password = os.getenv('MAIL_PASSWORD')
        smtp_server = os.getenv('MAIL_SERVER')
        smtp_port = 465  # SSL port like your script

        # Check email config
        if not all([sender_email, password, smtp_server]):
            app.logger.error("Email configuration missing")
            return jsonify({"success": False, "message": "Email service not configured"}), 500

        # Create HTML message
        html_message = f"""
        <html>
        <body>
        <h2>New Contact Form Submission - KaziSync</h2>
        <p><strong>Name:</strong> {name}</p>
        <p><strong>Email:</strong> {email}</p>
        <p><strong>Phone:</strong> {phone}</p>
        {f'<p><strong>Company:</strong> {company}</p>' if company else ''}
        {f'<p><strong>Subject:</strong> {subject}</p>' if subject else ''}
        <p><strong>Message:</strong></p>
        <p>{message}</p>
        <hr>
        <p><em>Submitted from KaziSync Contact Form</em></p>
        <p><em>Timestamp: {data.get('timestamp', 'Not provided')}</em></p>
        </body>
        </html>
        """

        # Send email to admin
        email_subject = f"Contact from {name}" + (f" - {subject}" if subject else "")
        success = send_email(
            sender_email=sender_email,
            receiver_email='<EMAIL>',
            password=password,
            smtp_server=smtp_server,
            smtp_port=smtp_port,
            subject=email_subject,
            html_message=html_message
        )

        if success:
            app.logger.info(f"Contact form submitted successfully by {email}")
            return jsonify({
                "success": True,
                "message": "Thank you for contacting us! We'll get back to you within 24 hours."
            })
        else:
            app.logger.error(f"Failed to send contact form email for {email}")
            return jsonify({"success": False, "message": "Failed to send email"})

    except Exception as e:
        app.logger.error(f"Contact form error: {str(e)}")
        return jsonify({"success": False, "message": "Internal server error"})


@super_simple_contact.route('/api/contact/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "service": "contact_form",
        "timestamp": os.getenv('TIMESTAMP', 'unknown')
    })


@super_simple_contact.route('/api/contact/test', methods=['POST'])
def test_contact():
    """Test contact form with sample data"""
    try:
        # Use sample data for testing
        test_data = {
            "name": "Test User",
            "email": "<EMAIL>",
            "phone": "+**********",
            "message": "This is a test message from the KaziSync contact form API.",
            "company": "Test Company",
            "subject": "API Test",
            "timestamp": "2024-01-01T12:00:00Z"
        }

        # Get email config
        sender_email = os.getenv('MAIL_USERNAME')
        password = os.getenv('MAIL_PASSWORD')
        smtp_server = os.getenv('MAIL_SERVER')
        smtp_port = 465

        if not all([sender_email, password, smtp_server]):
            return jsonify({
                "success": False,
                "message": "Email configuration missing",
                "config": {
                    "sender_email": "Set" if sender_email else "Missing",
                    "password": "Set" if password else "Missing",
                    "smtp_server": smtp_server or "Missing"
                }
            }), 500

        # Create test HTML message
        html_message = f"""
        <html>
        <body>
        <h2>TEST - Contact Form Submission - KaziSync</h2>
        <p><strong>Name:</strong> {test_data['name']}</p>
        <p><strong>Email:</strong> {test_data['email']}</p>
        <p><strong>Phone:</strong> {test_data['phone']}</p>
        <p><strong>Company:</strong> {test_data['company']}</p>
        <p><strong>Subject:</strong> {test_data['subject']}</p>
        <p><strong>Message:</strong></p>
        <p>{test_data['message']}</p>
        <hr>
        <p><em>This is a TEST submission from KaziSync Contact Form API</em></p>
        <p><em>Timestamp: {test_data['timestamp']}</em></p>
        </body>
        </html>
        """

        # Send test email
        success = send_email(
            sender_email=sender_email,
            receiver_email='<EMAIL>',
            password=password,
            smtp_server=smtp_server,
            smtp_port=smtp_port,
            subject="TEST - Contact Form API Test",
            html_message=html_message
        )

        if success:
            return jsonify({
                "success": True,
                "message": "Test email sent successfully!",
                "test_data": test_data
            })
        else:
            return jsonify({
                "success": False,
                "message": "Failed to send test email"
            })

    except Exception as e:
        app.logger.error(f"Test contact error: {str(e)}")
        return jsonify({"success": False, "message": f"Test error: {str(e)}"})
