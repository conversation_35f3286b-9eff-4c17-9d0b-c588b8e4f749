"""Customer management API routes."""

from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from application.Models.customers.customer import Customer
from application.Models.company import Company
from application.Models.Msg import Msg
import jsons
from datetime import datetime, date
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required


customer_api = Blueprint('customer_api', __name__)


def get_user_context():
    """Helper function to extract user and company context.

    This function handles both company users and central users:
    - Company users: company_id is extracted from JWT token automatically
    - Central users (HR/Admin): company_id must be provided in request

    Returns:
        tuple: (company_id, user_id, error_message)
    """
    user_info = getattr(g, 'user', None)
    if not user_info:
        return None, None, "Authentication required"

    user_id = user_info.get('user_id')
    if not user_id:
        return None, None, "User context required"

    # For company users, company_id is in the token
    company_id = user_info.get('company_id')
    if company_id:
        app.logger.info(f"Company user - using company_id from token: {company_id}")
        return company_id, user_id, None

    # For central users (HR/admin), get company_id from request
    company_id = request.args.get('company_id') or (request.json.get('company_id') if request.json else None)
    if not company_id:
        return None, None, "Company ID is required for central users"

    app.logger.info(f"Central user - using company_id from request: {company_id}")
    return company_id, user_id, None


@customer_api.route('/api/customers', methods=['GET'])
@token_required
def get_customers():
    """Get customers for a company with pagination and filtering."""
    # Get user context (automatically extracts company_id for company users)
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Pagination parameters
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    
    # Optional filtering parameters
    status = request.args.get('status')
    segment = request.args.get('segment')
    search_term = request.args.get('search')
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Get total count and paginated customers
        total_count, customers = Customer.get_paginated_customers(
            session,
            page=page,
            per_page=per_page,
            status=status,
            segment=segment,
            search_term=search_term
        )
        
        customers_data = [customer.to_dict() for customer in customers]
        
        # Prepare pagination metadata
        pagination = {
            "total_count": total_count,
            "page": page,
            "per_page": per_page,
            "pages": (total_count + per_page - 1) // per_page if per_page > 0 else 0,
            "has_next": page < ((total_count + per_page - 1) // per_page) if per_page > 0 else False,
            "has_prev": page > 1
        }
        
        return jsons.dump(Msg.success()
                         .add("customers", customers_data)
                         .add("pagination", pagination))


@customer_api.route('/api/customers/<customer_id>', methods=['GET'])
@token_required
def get_customer(customer_id):
    """Get a specific customer by ID."""
    # Get user context (automatically extracts company_id for company users)
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        customer = Customer.get_customer_by_id(session, customer_id)
        
        if not customer:
            return jsonify({"message": f"Customer with ID {customer_id} not found"}), 404
        
        # Get additional info
        customer_data = customer.to_dict()
        customer_data['visit_count'] = customer.get_visit_count(session)
        customer_data['available_rewards'] = customer.get_available_rewards(session)
        
        return jsons.dump(Msg.success().add("customer", customer_data))


@customer_api.route('/api/customers', methods=['POST'])
@token_required
@roles_required('hr')
def create_customer():
    """Create a new customer."""
    data = request.get_json()

    # Get user context (automatically extracts company_id for company users)
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Validate required fields
    if not data.get('first_name') or not data.get('last_name'):
        return jsonify({"message": "First name and last name are required"}), 400
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check for duplicate email
        if data.get('email'):
            existing = Customer.get_customer_by_email(session, data['email'])
            if existing:
                return jsonify({"message": "Customer with this email already exists"}), 400
        
        # Check for duplicate phone
        if data.get('phone_number'):
            existing = Customer.get_customer_by_phone(session, data['phone_number'])
            if existing:
                return jsonify({"message": "Customer with this phone number already exists"}), 400
        
        # Prepare customer data
        customer_data = {
            'first_name': data['first_name'],
            'last_name': data['last_name'],
            'email': data.get('email'),
            'phone_number': data.get('phone_number'),
            'membership_number': data.get('membership_number'),
            'customer_segment': data.get('customer_segment', 'Regular'),
            'date_of_birth': datetime.strptime(data['date_of_birth'], '%Y-%m-%d').date() if data.get('date_of_birth') else None,
            'preferred_contact_method': data.get('preferred_contact_method'),
            'marketing_consent': data.get('marketing_consent', False),
            'notes': data.get('notes'),
            'created_by': request.user_id if hasattr(request, 'user_id') else None
        }
        
        # Create customer
        customer = Customer.create_customer(session, **customer_data)

        if not customer:
            return jsonify({"message": "Failed to create customer"}), 500

        app.logger.info(f"Created customer: {customer.customer_id}")

        # Sync the customer with biometric devices
        from application.Models.employees.employee import Employee
        sync_result = Employee.sync_with_devices(session, customer, company_id, "create")

        # Return success even if device sync had issues, but include sync info in response
        response = Msg.success().add("customer", customer.to_dict())

        if sync_result["success"]:
            response.add("device_sync", {
                "status": "success",
                "message": sync_result["message"],
                "devices_synced": sync_result["devices_synced"]
            })
            response.add("message", "Customer created and synced to devices successfully")
        else:
            app.logger.warning(f"Customer created but device sync had issues: {sync_result['message']}")
            response.add("device_sync", {
                "status": "warning",
                "message": sync_result["message"],
                "devices_synced": sync_result["devices_synced"]
            })
            response.add("message", "Customer created successfully, but device sync had issues")

        return jsons.dump(response), 200


@customer_api.route('/api/customers/<customer_id>', methods=['PATCH'])
@token_required
@roles_required('hr')
def update_customer(customer_id):
    """Update an existing customer."""
    data = request.get_json()

    # Get user context (automatically extracts company_id for company users)
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if customer exists
        customer = Customer.get_customer_by_id(session, customer_id)
        if not customer:
            return jsonify({"message": f"Customer with ID {customer_id} not found"}), 404
        
        # Prepare update data
        update_data = {}
        
        allowed_fields = [
            'first_name', 'last_name', 'email', 'phone_number', 'membership_number',
            'customer_segment', 'status', 'preferred_contact_method', 
            'marketing_consent', 'notes'
        ]
        
        for field in allowed_fields:
            if field in data:
                update_data[field] = data[field]
        
        # Handle date_of_birth separately
        if 'date_of_birth' in data and data['date_of_birth']:
            update_data['date_of_birth'] = datetime.strptime(data['date_of_birth'], '%Y-%m-%d').date()
        
        # Update customer
        updated_customer = Customer.update_customer(session, customer_id, **update_data)

        if not updated_customer:
            return jsonify({"message": "Failed to update customer"}), 500

        app.logger.info(f"Updated customer: {customer_id}")

        # Sync with devices if name was updated
        response = Msg.success().add("customer", updated_customer.to_dict())

        if 'first_name' in update_data or 'last_name' in update_data:
            from application.Models.employees.employee import Employee
            sync_result = Employee.sync_with_devices(session, updated_customer, company_id, "update")

            if sync_result["success"]:
                response.add("device_sync", {
                    "status": "success",
                    "message": sync_result["message"],
                    "devices_synced": sync_result["devices_synced"]
                })
                response.add("message", "Customer updated and synced to devices successfully")
            else:
                app.logger.warning(f"Customer updated but device sync had issues: {sync_result['message']}")
                response.add("device_sync", {
                    "status": "warning",
                    "message": sync_result["message"],
                    "devices_synced": sync_result["devices_synced"]
                })
                response.add("message", "Customer updated successfully, but device sync had issues")
        else:
            response.add("message", "Customer updated successfully")

        return jsons.dump(response)


@customer_api.route('/api/customers/<customer_id>', methods=['DELETE'])
@token_required
@roles_required('hr')
def delete_customer(customer_id):
    """Delete (soft delete) a customer."""
    # Get user context (automatically extracts company_id for company users)
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if customer exists
        customer = Customer.get_customer_by_id(session, customer_id)
        if not customer:
            return jsonify({"message": f"Customer with ID {customer_id} not found"}), 404
        
        # Remove customer from devices before soft delete
        from application.Models.employees.employee import Employee
        sync_result = Employee.sync_with_devices(session, customer, company_id, "delete")

        # Soft delete
        success = Customer.delete_customer(session, customer_id)

        if not success:
            return jsonify({"message": "Failed to delete customer"}), 500

        app.logger.info(f"Deleted customer: {customer_id}")

        # Return response with device sync info
        response = Msg.success()

        if sync_result["success"]:
            response.add("device_sync", {
                "status": "success",
                "message": sync_result["message"],
                "devices_synced": sync_result["devices_synced"]
            })
            response.add("message", "Customer deleted and removed from devices successfully")
        else:
            app.logger.warning(f"Customer deleted but device removal had issues: {sync_result['message']}")
            response.add("device_sync", {
                "status": "warning",
                "message": sync_result["message"],
                "devices_synced": sync_result["devices_synced"]
            })
            response.add("message", "Customer deleted successfully, but device removal had issues")

        return jsons.dump(response)


@customer_api.route('/api/customers/<customer_id>/statistics', methods=['GET'])
@token_required
def get_customer_statistics(customer_id):
    """Get statistics for a specific customer."""
    # Get user context (automatically extracts company_id for company users)
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        from application.Services.CustomerVisitService import CustomerVisitService
        
        customer = Customer.get_customer_by_id(session, customer_id)
        if not customer:
            return jsonify({"message": f"Customer with ID {customer_id} not found"}), 404
        
        # Get visit statistics
        stats = CustomerVisitService.get_visit_statistics(session, customer_id)
        
        # Add customer info
        stats['customer'] = customer.to_dict()
        
        return jsons.dump(Msg.success().add("statistics", stats))

