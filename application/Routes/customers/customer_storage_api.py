"""Customer item storage management API routes."""

from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from application.Models.customers.services import CustomerItemStorage
from application.Models.company import Company
from application.Models.Msg import Msg
import jsons
from datetime import datetime, date
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required


customer_storage_api = Blueprint('customer_storage_api', __name__)


def get_user_context():
    """Helper function to extract user and company context.

    This function handles both company users and central users:
    - Company users: company_id is extracted from JWT token automatically
    - Central users (HR/Admin): company_id must be provided in request

    Returns:
        tuple: (company_id, user_id, error_message)
    """
    user_info = getattr(g, 'user', None)
    if not user_info:
        return None, None, "Authentication required"

    user_id = user_info.get('user_id')
    if not user_id:
        return None, None, "User context required"

    # For company users, company_id is in the token
    company_id = user_info.get('company_id')
    if company_id:
        app.logger.info(f"Company user - using company_id from token: {company_id}")
        return company_id, user_id, None

    # For central users (HR/admin), get company_id from request
    company_id = request.args.get('company_id') or (request.json.get('company_id') if request.json else None)
    if not company_id:
        return None, None, "Company ID is required for central users"

    app.logger.info(f"Central user - using company_id from request: {company_id}")
    return company_id, user_id, None


@customer_storage_api.route('/api/customer-storages', methods=['GET'])
@token_required
@roles_required('hr')
def get_customer_storages():
    """Get all customer item storages with optional filtering."""
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Optional filtering parameters
    customer_id = request.args.get('customer_id')
    location_id = request.args.get('location_id')
    status = request.args.get('status')
    visit_id = request.args.get('visit_id')
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        storages = CustomerItemStorage.get_all_storages(
            session=session,
            customer_id=customer_id,
            location_id=location_id,
            status=status,
            visit_id=visit_id
        )
        
        storages_data = [storage.to_dict() for storage in storages]
        
        return jsons.dump(Msg.success()
                         .add("customer_storages", storages_data)
                         .add("count", len(storages_data)))


@customer_storage_api.route('/api/customer-storages/<storage_id>', methods=['GET'])
@token_required
@roles_required('hr')
def get_customer_storage(storage_id):
    """Get a specific customer item storage by ID."""
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        storage = CustomerItemStorage.get_storage_by_id(session, storage_id)
        
        if not storage:
            return jsonify({"message": f"Customer storage with ID {storage_id} not found"}), 404
        
        return jsons.dump(Msg.success().add("customer_storage", storage.to_dict()))


@customer_storage_api.route('/api/customer-storages', methods=['POST'])
@token_required
@roles_required('hr')
def create_customer_storage():
    """Create a new customer item storage record."""
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401

    data = request.get_json()
    if not data:
        return jsonify({"message": "Request body is required"}), 400
    
    # Validate required fields
    if not data.get('customer_id'):
        return jsonify({"message": "Customer ID is required"}), 400
    if not data.get('location_id'):
        return jsonify({"message": "Location ID is required"}), 400
    if not data.get('items_description'):
        return jsonify({"message": "Items description is required"}), 400

    app.logger.info(f"Creating customer storage for customer {data.get('customer_id')} by user {user_id}")
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        try:
            # Parse stored_at if provided
            if data.get('stored_at'):
                data['stored_at'] = datetime.strptime(data['stored_at'], '%Y-%m-%d %H:%M:%S')
            
            storage = CustomerItemStorage.create_storage(session=session, **data)
            
            if not storage:
                return jsonify({"message": "Failed to create customer storage"}), 500

            app.logger.info(f"Created customer storage: {storage}")
            
            if isinstance(storage, str):
                return jsonify({"message": storage}), 400

            app.logger.info(f"Created customer storage: {storage.storage_id}")
            
            return jsons.dump(Msg.success()
                             .add("customer_storage", storage.to_dict())
                             .add("message", "Customer storage created successfully")), 201
        except ValueError as e:
            return jsonify({"message": str(e)}), 400
        except Exception as e:
            app.logger.error(f"Error creating customer storage: {e}")
            return jsonify({"message": str(e)}), 500


@customer_storage_api.route('/api/customer-storages/<storage_id>', methods=['PUT'])
@token_required
@roles_required('hr')
def update_customer_storage(storage_id):
    """Update an existing customer item storage."""
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401

    data = request.get_json()
    if not data:
        return jsonify({"message": "Request body is required"}), 400

    app.logger.info(f"Updating customer storage {storage_id} by user {user_id}")
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        try:
            # Parse datetime fields if provided
            if data.get('stored_at'):
                data['stored_at'] = datetime.strptime(data['stored_at'], '%Y-%m-%d %H:%M:%S')
            if data.get('retrieved_at'):
                data['retrieved_at'] = datetime.strptime(data['retrieved_at'], '%Y-%m-%d %H:%M:%S')
            
            storage = CustomerItemStorage.update_storage(
                session=session,
                storage_id=storage_id,
                **data
            )
            
            if not storage:
                return jsonify({"message": f"Customer storage with ID {storage_id} not found"}), 404
            
            app.logger.info(f"Updated customer storage: {storage_id}")
            
            return jsons.dump(Msg.success()
                             .add("customer_storage", storage.to_dict())
                             .add("message", "Customer storage updated successfully"))
        except ValueError as e:
            return jsonify({"message": str(e)}), 400
        except Exception as e:
            app.logger.error(f"Error updating customer storage: {e}")
            return jsonify({"message": str(e)}), 500


@customer_storage_api.route('/api/customer-storages/<storage_id>/retrieve', methods=['POST'])
@token_required
@roles_required('hr')
def retrieve_customer_items(storage_id):
    """Mark items as retrieved from storage."""
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401

    data = request.get_json() or {}
    retrieved_at = data.get('retrieved_at')
    
    if retrieved_at:
        try:
            retrieved_at = datetime.strptime(retrieved_at, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            return jsonify({"message": "Invalid retrieved_at format. Use YYYY-MM-DD HH:MM:SS"}), 400

    app.logger.info(f"Retrieving items from storage {storage_id} by user {user_id}")
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        try:
            success = CustomerItemStorage.retrieve_items(
                session=session,
                storage_id=storage_id,
                retrieved_at=retrieved_at
            )
            
            if not success:
                return jsonify({"message": f"Customer storage with ID {storage_id} not found or already retrieved"}), 404
            
            app.logger.info(f"Retrieved items from storage: {storage_id}")
            
            return jsons.dump(Msg.success()
                             .add("message", "Items retrieved successfully"))
        except Exception as e:
            app.logger.error(f"Error retrieving items: {e}")
            return jsonify({"message": str(e)}), 500


@customer_storage_api.route('/api/customer-storages/<storage_id>/abandon', methods=['POST'])
@token_required
@roles_required('hr')
def abandon_customer_items(storage_id):
    """Mark items as abandoned."""
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401

    app.logger.info(f"Abandoning items in storage {storage_id} by user {user_id}")
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        try:
            success = CustomerItemStorage.abandon_items(
                session=session,
                storage_id=storage_id
            )
            
            if not success:
                return jsonify({"message": f"Customer storage with ID {storage_id} not found"}), 404
            
            app.logger.info(f"Abandoned items in storage: {storage_id}")
            
            return jsons.dump(Msg.success()
                             .add("message", "Items marked as abandoned"))
        except Exception as e:
            app.logger.error(f"Error abandoning items: {e}")
            return jsonify({"message": str(e)}), 500


@customer_storage_api.route('/api/customers/<customer_id>/active-storages', methods=['GET'])
@token_required
@roles_required('hr')
def get_customer_active_storages(customer_id):
    """Get all active (stored) items for a customer."""
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        storages = CustomerItemStorage.get_active_storages_by_customer(session, customer_id)
        
        storages_data = [storage.to_dict() for storage in storages]
        
        return jsons.dump(Msg.success()
                         .add("active_storages", storages_data)
                         .add("count", len(storages_data)))


@customer_storage_api.route('/api/storage-locations/<location_id>/storages', methods=['GET'])
@token_required
@roles_required('hr')
def get_storages_by_location(location_id):
    """Get all storages for a specific location."""
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Optional status filter
    status = request.args.get('status')
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        storages = CustomerItemStorage.get_storages_by_location(
            session=session,
            location_id=location_id,
            status=status
        )
        
        storages_data = [storage.to_dict() for storage in storages]
        
        return jsons.dump(Msg.success()
                         .add("location_storages", storages_data)
                         .add("count", len(storages_data)))
