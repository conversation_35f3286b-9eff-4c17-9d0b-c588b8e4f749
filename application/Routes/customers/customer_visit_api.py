"""Customer visit tracking API routes."""

from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from application.Models.customers.customer_visit import CustomerVisit
from application.Models.company import Company
from application.Models.Msg import Msg
import jsons
from datetime import datetime, date, timedelta
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.Services.CustomerVisitService import CustomerVisitService


customer_visit_api = Blueprint('customer_visit_api', __name__)


def get_user_context():
    """Helper function to extract user and company context.

    This function handles both company users and central users:
    - Company users: company_id is extracted from JWT token automatically
    - Central users (HR/Admin): company_id must be provided in request

    Returns:
        tuple: (company_id, user_id, error_message)
    """
    user_info = getattr(g, 'user', None)
    if not user_info:
        return None, None, "Authentication required"

    user_id = user_info.get('user_id')
    if not user_id:
        return None, None, "User context required"

    # For company users, company_id is in the token
    company_id = user_info.get('company_id')
    if company_id:
        app.logger.info(f"Company user - using company_id from token: {company_id}")
        return company_id, user_id, None

    # For central users (HR/admin), get company_id from request
    company_id = request.args.get('company_id') or (request.json.get('company_id') if request.json else None)
    if not company_id:
        return None, None, "Company ID is required for central users"

    app.logger.info(f"Central user - using company_id from request: {company_id}")
    return company_id, user_id, None


@customer_visit_api.route('/api/customer-visits', methods=['GET'])
@token_required
@roles_required('hr')
def get_customer_visits():
    """Get customer visits with filtering and pagination."""
    # Get user context (automatically extracts company_id for company users)
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401

    # Optional filtering parameters
    customer_id = request.args.get('customer_id')
    date_str = request.args.get('date')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    # Pagination parameters
    page = int(request.args.get('page', 1))
    per_page = min(int(request.args.get('per_page', 10)), 100)  # Default 10, max 100

    # Validation
    if page < 1:
        return jsonify({"message": "Page number must be 1 or greater"}), 400
    if per_page < 1:
        return jsonify({"message": "Per page must be 1 or greater"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Parse dates with priority logic: date takes precedence over start_date/end_date
        date_obj = datetime.strptime(date_str, '%Y-%m-%d').date() if date_str else None
        start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date() if start_date and not date_obj else None
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date() if end_date and not date_obj else None

        # Build query
        query = session.query(CustomerVisit)

        if customer_id:
            query = query.filter(CustomerVisit.customer_id == customer_id)

        # Date filtering with priority logic
        if date_obj:
            # Single date takes precedence
            query = query.filter(CustomerVisit.visit_date == date_obj)
        else:
            # Fall back to date range filtering
            if start_date_obj:
                query = query.filter(CustomerVisit.visit_date >= start_date_obj)
            if end_date_obj:
                query = query.filter(CustomerVisit.visit_date <= end_date_obj)

        # Get total count
        total_records = query.count()

        # Apply pagination
        offset = (page - 1) * per_page
        visits = query.order_by(
            CustomerVisit.visit_date.desc(),
            CustomerVisit.visit_time.desc()
        ).offset(offset).limit(per_page).all()

        visits_data = [visit.to_dict() for visit in visits]

        # Calculate pagination metadata
        total_pages = (total_records + per_page - 1) // per_page
        has_next = page < total_pages
        has_prev = page > 1

        return jsonify({
            "status": "success",
            "visits": visits_data,
            "pagination": {
                "page": page,
                "per_page": per_page,
                "total_records": total_records,
                "total_pages": total_pages,
                "has_next": has_next,
                "has_prev": has_prev,
                "count": total_records,
                "next": page + 1 if has_next else None,
                "previous": page - 1 if has_prev else None
            },
            "filters_applied": {
                "customer_id": customer_id,
                "date": date_str,
                "start_date": start_date if not date_obj else None,
                "end_date": end_date if not date_obj else None
            }
        }), 200


@customer_visit_api.route('/api/customer-visits/<visit_id>', methods=['GET'])
@token_required
@roles_required('hr')
def get_customer_visit(visit_id):
    """Get a specific customer visit by ID."""
    # Get user context (automatically extracts company_id for company users)
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        visit = CustomerVisit.get_visit_by_id(session, visit_id)
        
        if not visit:
            return jsonify({"message": f"Visit with ID {visit_id} not found"}), 404
        
        return jsons.dump(Msg.success().add("visit", visit.to_dict()))


@customer_visit_api.route('/api/customer-visits', methods=['POST'])
@token_required
@roles_required('hr')
def create_manual_visit():
    """Create a manual customer visit (not from biometric device)."""
    data = request.get_json()
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400
    
    # Validate required fields
    if not data.get('customer_id'):
        return jsonify({"message": "Customer ID is required"}), 400
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Parse dates
        visit_date = datetime.strptime(data['visit_date'], '%Y-%m-%d').date() if data.get('visit_date') else None
        visit_time = datetime.strptime(data['visit_time'], '%Y-%m-%d %H:%M:%S') if data.get('visit_time') else None
        
        # Create manual visit
        visit = CustomerVisitService.create_manual_visit(
            session=session,
            customer_id=data['customer_id'],
            visit_date=visit_date,
            visit_time=visit_time,
            visit_type=data.get('visit_type', 'regular'),
            notes=data.get('notes'),
            created_by=request.user_id if hasattr(request, 'user_id') else None
        )
        
        if not visit:
            return jsonify({"message": "Failed to create visit"}), 500
        
        app.logger.info(f"Created manual visit: {visit.visit_id}")
        
        return jsons.dump(Msg.success()
                         .add("visit", visit.to_dict())
                         .add("message", "Visit created successfully")), 201


@customer_visit_api.route('/api/customer-visits/<visit_id>', methods=['PATCH'])
@token_required
@roles_required('hr')
def update_visit(visit_id):
    """Update an existing visit."""
    data = request.get_json()
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if visit exists
        visit = CustomerVisit.get_visit_by_id(session, visit_id)
        if not visit:
            return jsonify({"message": f"Visit with ID {visit_id} not found"}), 404
        
        # Prepare update data
        update_data = {}
        
        allowed_fields = ['visit_type', 'duration_minutes', 'notes']
        
        for field in allowed_fields:
            if field in data:
                update_data[field] = data[field]
        
        # Update visit
        updated_visit = CustomerVisit.update_visit(session, visit_id, **update_data)
        
        if not updated_visit:
            return jsonify({"message": "Failed to update visit"}), 500
        
        app.logger.info(f"Updated visit: {visit_id}")
        
        return jsons.dump(Msg.success()
                         .add("visit", updated_visit.to_dict())
                         .add("message", "Visit updated successfully"))


@customer_visit_api.route('/api/customer-visits/daily-summary', methods=['GET'])
@token_required
@roles_required('hr')
def get_daily_summary():
    """Get daily summary of customer visits."""
    # Get user context (automatically extracts company_id for company users)
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Optional date parameter
    date_str = request.args.get('date')
    visit_date = datetime.strptime(date_str, '%Y-%m-%d').date() if date_str else date.today()
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        summary = CustomerVisitService.get_daily_visit_summary(session, visit_date)
        
        return jsons.dump(Msg.success().add("summary", summary))


@customer_visit_api.route('/api/customers/<customer_id>/visits', methods=['GET'])
@token_required
@roles_required('hr')
def get_customer_visit_history(customer_id):
    """Get visit history for a specific customer with pagination."""
    # Get user context (automatically extracts company_id for company users)
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401

    # Optional filtering
    date_str = request.args.get('date')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    # Pagination parameters
    page = int(request.args.get('page', 1))
    per_page = min(int(request.args.get('per_page', 10)), 100)  # Default 10, max 100

    # Validation
    if page < 1:
        return jsonify({"message": "Page number must be 1 or greater"}), 400
    if per_page < 1:
        return jsonify({"message": "Per page must be 1 or greater"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Parse dates with priority logic: date takes precedence over start_date/end_date
        date_obj = datetime.strptime(date_str, '%Y-%m-%d').date() if date_str else None
        start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date() if start_date and not date_obj else None
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date() if end_date and not date_obj else None

        # Build query for this specific customer
        query = session.query(CustomerVisit).filter(CustomerVisit.customer_id == customer_id)

        # Date filtering with priority logic
        if date_obj:
            # Single date takes precedence
            query = query.filter(CustomerVisit.visit_date == date_obj)
        else:
            # Fall back to date range filtering
            if start_date_obj:
                query = query.filter(CustomerVisit.visit_date >= start_date_obj)
            if end_date_obj:
                query = query.filter(CustomerVisit.visit_date <= end_date_obj)

        # Get total count
        total_records = query.count()

        # Apply pagination
        offset = (page - 1) * per_page
        visits = query.order_by(
            CustomerVisit.visit_date.desc(),
            CustomerVisit.visit_time.desc()
        ).offset(offset).limit(per_page).all()

        visits_data = [visit.to_dict() for visit in visits]

        # Get statistics (for the entire filtered dataset, not just current page)
        stats = CustomerVisitService.get_visit_statistics(
            session=session,
            customer_id=customer_id,
            start_date=date_obj if date_obj else start_date_obj,
            end_date=date_obj if date_obj else end_date_obj
        )

        # Calculate pagination metadata
        total_pages = (total_records + per_page - 1) // per_page
        has_next = page < total_pages
        has_prev = page > 1

        return jsonify({
            "status": "success",
            "visits": visits_data,
            "statistics": stats,
            "pagination": {
                "page": page,
                "per_page": per_page,
                "total_records": total_records,
                "total_pages": total_pages,
                "has_next": has_next,
                "has_prev": has_prev,
                "count": total_records,
                "next": page + 1 if has_next else None,
                "previous": page - 1 if has_prev else None
            },
            "filters_applied": {
                "customer_id": customer_id,
                "date": date_str,
                "start_date": start_date if not date_obj else None,
                "end_date": end_date if not date_obj else None
            }
        }), 200

