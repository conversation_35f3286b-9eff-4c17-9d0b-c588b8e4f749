"""Loyalty program API routes."""

from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from application.Models.customers.customer_loyalty_balance import CustomerLoyaltyBalance
from application.Models.customers.reward_redemption import RewardRedemption
from application.Models.company import Company
from application.Models.Msg import Msg
import jsons
from datetime import datetime, date
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.Services.LoyaltyService import LoyaltyService


loyalty_api = Blueprint('loyalty_api', __name__)


def get_user_context():
    """Helper function to extract user and company context.

    This function handles both company users and central users:
    - Company users: company_id is extracted from JWT token automatically
    - Central users (HR/Admin): company_id must be provided in request

    Returns:
        tuple: (company_id, user_id, error_message)
    """
    user_info = getattr(g, 'user', None)
    if not user_info:
        return None, None, "Authentication required"

    user_id = user_info.get('user_id')
    if not user_id:
        return None, None, "User context required"

    # For company users, company_id is in the token
    company_id = user_info.get('company_id')
    if company_id:
        app.logger.info(f"Company user - using company_id from token: {company_id}")
        return company_id, user_id, None

    # For central users (HR/admin), get company_id from request
    company_id = request.args.get('company_id') or (request.json.get('company_id') if request.json else None)
    if not company_id:
        return None, None, "Company ID is required for central users"

    app.logger.info(f"Central user - using company_id from request: {company_id}")
    return company_id, user_id, None


@loyalty_api.route('/api/customers/<customer_id>/loyalty', methods=['GET'])
@token_required
def get_customer_loyalty(customer_id):
    """Get loyalty summary for a customer."""
    # Get user context (automatically extracts company_id for company users)
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        summary = LoyaltyService.get_customer_loyalty_summary(session, customer_id)
        
        if 'error' in summary:
            return jsonify({"message": summary['error']}), 404
        
        return jsons.dump(Msg.success().add("loyalty", summary))


@loyalty_api.route('/api/customers/<customer_id>/rewards', methods=['GET'])
@token_required
def get_customer_rewards(customer_id):
    """Get available rewards for a customer."""
    # Get user context (automatically extracts company_id for company users)
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        eligibility = LoyaltyService.check_reward_eligibility(session, customer_id)
        
        if 'error' in eligibility:
            return jsonify({"message": eligibility['error']}), 500
        
        return jsons.dump(Msg.success().add("rewards", eligibility))


@loyalty_api.route('/api/customers/<customer_id>/rewards/redeem', methods=['POST'])
@token_required
@roles_required('hr')
def redeem_customer_reward(customer_id):
    """Redeem a reward for a customer."""
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401

    data = request.get_json()
    if not data:
        return jsonify({"message": "Request body is required"}), 400
    
    # Validate required fields
    if not data.get('rule_id'):
        return jsonify({"message": "Rule ID is required"}), 400

    app.logger.info(f"Redeeming reward for customer {customer_id} by user {user_id}")
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        try:
            redemption = LoyaltyService.redeem_reward(
                session=session,
                customer_id=customer_id,
                rule_id=data['rule_id'],
                redeemed_by=user_id if user_id else None,
                visit_id=data.get('visit_id'),
                redemption_method=data.get('redemption_method', 'manual'),
                notes=data.get('notes')
            )
        except Exception as e:
            app.logger.error(f"Error redeeming reward: {e}")
            return jsonify({"message": str(e)}), 500
            
        if not redemption:
            return jsonify({"message": "Failed to redeem reward. Check if rewards are available."}), 400
        
        app.logger.info(f"Redeemed reward for customer {customer_id}: {redemption.redemption_id}")
        
        return jsons.dump(Msg.success()
                         .add("redemption", redemption.to_dict())
                         .add("message", "Reward redeemed successfully")), 201


@loyalty_api.route('/api/customers/<customer_id>/redemptions', methods=['GET'])
@token_required
def get_customer_redemptions(customer_id):
    """Get redemption history for a customer."""
    # Get user context (automatically extracts company_id for company users)
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Optional filtering
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    status = request.args.get('status')
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Parse dates
        start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date() if start_date else None
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date() if end_date else None
        
        redemptions = RewardRedemption.get_redemptions_by_customer(
            session=session,
            customer_id=customer_id,
            start_date=start_date_obj,
            end_date=end_date_obj,
            status=status
        )
        
        redemptions_data = [redemption.to_dict() for redemption in redemptions]
        
        return jsons.dump(Msg.success()
                         .add("redemptions", redemptions_data)
                         .add("count", len(redemptions_data)))


@loyalty_api.route('/api/loyalty/balances', methods=['GET'])
@token_required
def get_all_loyalty_balances():
    """Get all loyalty balances (for admin view)."""
    # Get user context (automatically extracts company_id for company users)
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Get all active balances with available rewards
        balances = session.query(CustomerLoyaltyBalance).filter(
            CustomerLoyaltyBalance.is_active == True,
            CustomerLoyaltyBalance.rewards_available > 0
        ).all()
        
        balances_data = [balance.to_dict() for balance in balances]
        
        return jsons.dump(Msg.success()
                         .add("balances", balances_data)
                         .add("count", len(balances_data)))


@loyalty_api.route('/api/loyalty/analytics', methods=['GET'])
@token_required
def get_loyalty_analytics():
    """Get loyalty program analytics."""
    # Get user context (automatically extracts company_id for company users)
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Optional date range
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Parse dates
        start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date() if start_date else None
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date() if end_date else None
        
        analytics = LoyaltyService.get_loyalty_analytics(
            session=session,
            start_date=start_date_obj,
            end_date=end_date_obj
        )
        
        if 'error' in analytics:
            return jsonify({"message": analytics['error']}), 500
        
        return jsons.dump(Msg.success().add("analytics", analytics))


@loyalty_api.route('/api/redemptions/<redemption_id>', methods=['GET'])
@token_required
def get_redemption(redemption_id):
    """Get a specific redemption by ID."""
    # Get user context (automatically extracts company_id for company users)
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        redemption = RewardRedemption.get_redemption_by_id(session, redemption_id)
        
        if not redemption:
            return jsonify({"message": f"Redemption with ID {redemption_id} not found"}), 404
        
        return jsons.dump(Msg.success().add("redemption", redemption.to_dict()))


@loyalty_api.route('/api/redemptions/<redemption_id>/cancel', methods=['POST'])
@token_required
@roles_required('hr')
def cancel_redemption(redemption_id):
    """Cancel a redemption."""
    data = request.get_json()
    company_id = data.get('company_id')
    
    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        success = RewardRedemption.cancel_redemption(session, redemption_id)
        
        if not success:
            return jsonify({"message": "Failed to cancel redemption"}), 500
        
        # Get updated redemption
        redemption = RewardRedemption.get_redemption_by_id(session, redemption_id)
        
        app.logger.info(f"Cancelled redemption: {redemption_id}")
        
        return jsons.dump(Msg.success()
                         .add("redemption", redemption.to_dict())
                         .add("message", "Redemption cancelled successfully"))

