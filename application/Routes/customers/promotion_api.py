"""Promotion rule management API routes."""

from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from application.Models.customers.promotion_rule import PromotionRule
from application.Models.company import Company
from application.Models.Msg import Msg
import jsons
from datetime import datetime, date
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required


promotion_api = Blueprint('promotion_api', __name__)


def get_user_context():
    """Helper function to extract user and company context.

    This function handles both company users and central users:
    - Company users: company_id is extracted from JWT token automatically
    - Central users (HR/Admin): company_id must be provided in request

    Returns:
        tuple: (company_id, user_id, error_message)
    """
    user_info = getattr(g, 'user', None)
    if not user_info:
        return None, None, "Authentication required"

    user_id = user_info.get('user_id')
    if not user_id:
        return None, None, "User context required"

    # For company users, company_id is in the token
    company_id = user_info.get('company_id')
    if company_id:
        app.logger.info(f"Company user - using company_id from token: {company_id}")
        return company_id, user_id, None

    # For central users (HR/admin), get company_id from request
    company_id = request.args.get('company_id') or (request.json.get('company_id') if request.json else None)
    if not company_id:
        return None, None, "Company ID is required for central users"

    app.logger.info(f"Central user - using company_id from request: {company_id}")
    return company_id, user_id, None


@promotion_api.route('/api/promotions', methods=['GET'])
@token_required
def get_promotions():
    """Get all promotion rules."""
    # Get user context (automatically extracts company_id for company users)
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Optional filtering
    active_only = request.args.get('active_only', 'true').lower() == 'true'
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        rules = PromotionRule.get_all_rules(session, active_only=active_only)
        
        rules_data = [rule.to_dict() for rule in rules]
        
        return jsons.dump(Msg.success()
                         .add("promotions", rules_data)
                         .add("count", len(rules_data)))


@promotion_api.route('/api/promotions/<rule_id>', methods=['GET'])
@token_required
def get_promotion(rule_id):
    """Get a specific promotion rule by ID."""
    # Get user context (automatically extracts company_id for company users)
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        rule = PromotionRule.get_rule_by_id(session, rule_id)
        
        if not rule:
            return jsonify({"message": f"Promotion rule with ID {rule_id} not found"}), 404
        
        return jsons.dump(Msg.success().add("promotion", rule.to_dict()))


@promotion_api.route('/api/promotions', methods=['POST'])
@token_required
@roles_required('hr')
def create_promotion():
    """Create a new promotion rule."""
    data = request.get_json()
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Validate required fields
    required_fields = ['name', 'rule_type', 'trigger_value', 'reward_type', 'reward_value']
    for field in required_fields:
        if field not in data:
            return jsonify({"message": f"{field} is required"}), 400
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Prepare rule data
        rule_data = {
            'name': data['name'],
            'description': data.get('description'),
            'rule_type': data['rule_type'],
            'trigger_value': data['trigger_value'],
            'trigger_period_days': data.get('trigger_period_days'),
            'reward_type': data['reward_type'],
            'reward_value': data['reward_value'],
            'reward_description': data.get('reward_description'),
            'applicable_customer_segments': data.get('applicable_customer_segments'),
            'applicable_visit_types': data.get('applicable_visit_types'),
            'is_active': data.get('is_active', True),
            'valid_from': datetime.strptime(data['valid_from'], '%Y-%m-%d').date() if data.get('valid_from') else None,
            'valid_until': datetime.strptime(data['valid_until'], '%Y-%m-%d').date() if data.get('valid_until') else None,
            'max_redemptions_per_customer': data.get('max_redemptions_per_customer'),
            'reward_expiry_days': data.get('reward_expiry_days'),
            'priority': data.get('priority', 0),
            'created_by': user_id
        }
        
        # Create rule
        rule = PromotionRule.create_rule(session, **rule_data)
        
        if not rule:
            return jsonify({"message": "Failed to create promotion rule"}), 500
        
        # Log rule creation
        from application.Models.customers.promotion_audit_log import PromotionAuditLog
        PromotionAuditLog.log_rule_created(
            session=session,
            rule_id=rule.rule_id,
            rule_data=rule.to_dict(),
            performed_by=user_id
        )
        
        app.logger.info(f"Created promotion rule: {rule.rule_id}")
        
        return jsons.dump(Msg.success()
                         .add("promotion", rule.to_dict())
                         .add("message", "Promotion rule created successfully")), 201


@promotion_api.route('/api/promotions/<rule_id>', methods=['PATCH'])
@token_required
@roles_required('hr')
def update_promotion(rule_id):
    """Update an existing promotion rule."""
    data = request.get_json()
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if rule exists
        rule = PromotionRule.get_rule_by_id(session, rule_id)
        if not rule:
            return jsonify({"message": f"Promotion rule with ID {rule_id} not found"}), 404
        
        # Store old rule for audit
        old_rule_dict = rule.to_dict()
        
        # Prepare update data
        update_data = {}
        
        allowed_fields = [
            'name', 'description', 'rule_type', 'trigger_value', 'trigger_period_days',
            'reward_type', 'reward_value', 'reward_description', 'applicable_customer_segments',
            'applicable_visit_types', 'is_active', 'max_redemptions_per_customer',
            'reward_expiry_days', 'priority'
        ]
        
        for field in allowed_fields:
            if field in data:
                update_data[field] = data[field]
        
        # Handle dates separately
        if 'valid_from' in data and data['valid_from']:
            update_data['valid_from'] = datetime.strptime(data['valid_from'], '%Y-%m-%d').date()
        
        if 'valid_until' in data and data['valid_until']:
            update_data['valid_until'] = datetime.strptime(data['valid_until'], '%Y-%m-%d').date()
        
        # Update rule
        updated_rule = PromotionRule.update_rule(session, rule_id, **update_data)
        
        if not updated_rule:
            return jsonify({"message": "Failed to update promotion rule"}), 500
        
        # Log rule update
        from application.Models.customers.promotion_audit_log import PromotionAuditLog
        PromotionAuditLog.log_rule_updated(
            session=session,
            rule_id=rule_id,
            old_rule=old_rule_dict,
            new_rule=updated_rule.to_dict(),
            performed_by=user_id
        )
        
        app.logger.info(f"Updated promotion rule: {rule_id}")
        
        return jsons.dump(Msg.success()
                         .add("promotion", updated_rule.to_dict())
                         .add("message", "Promotion rule updated successfully"))


@promotion_api.route('/api/promotions/<rule_id>/deactivate', methods=['POST'])
@token_required
@roles_required('hr')
def deactivate_promotion(rule_id):
    """Deactivate a promotion rule."""
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if rule exists
        rule = PromotionRule.get_rule_by_id(session, rule_id)
        if not rule:
            return jsonify({"message": f"Promotion rule with ID {rule_id} not found"}), 404
        
        # Store old rule for audit
        old_rule_dict = rule.to_dict()
        
        # Deactivate
        success = PromotionRule.deactivate_rule(session, rule_id)
        
        if not success:
            return jsonify({"message": "Failed to deactivate promotion rule"}), 500
        
        # Get updated rule
        updated_rule = PromotionRule.get_rule_by_id(session, rule_id)
        
        # Log rule update
        from application.Models.customers.promotion_audit_log import PromotionAuditLog
        PromotionAuditLog.log_rule_updated(
            session=session,
            rule_id=rule_id,
            old_rule=old_rule_dict,
            new_rule=updated_rule.to_dict(),
            performed_by=user_id
        )
        
        app.logger.info(f"Deactivated promotion rule: {rule_id}")
        
        return jsons.dump(Msg.success()
                         .add("promotion", updated_rule.to_dict())
                         .add("message", "Promotion rule deactivated successfully"))

