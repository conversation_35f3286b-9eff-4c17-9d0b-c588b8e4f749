"""Service management API routes."""

from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from application.Models.customers.services import Service
from application.Models.company import Company
from application.Models.Msg import Msg
import jsons
from datetime import datetime, date
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required


service_api = Blueprint('service_api', __name__)


def get_user_context():
    """Helper function to extract user and company context.

    This function handles both company users and central users:
    - Company users: company_id is extracted from JWT token automatically
    - Central users (HR/Admin): company_id must be provided in request

    Returns:
        tuple: (company_id, user_id, error_message)
    """
    user_info = getattr(g, 'user', None)
    if not user_info:
        return None, None, "Authentication required"

    user_id = user_info.get('user_id')
    if not user_id:
        return None, None, "User context required"

    # For company users, company_id is in the token
    company_id = user_info.get('company_id')
    if company_id:
        app.logger.info(f"Company user - using company_id from token: {company_id}")
        return company_id, user_id, None

    # For central users (HR/admin), get company_id from request
    company_id = request.args.get('company_id') or (request.json.get('company_id') if request.json else None)
    if not company_id:
        return None, None, "Company ID is required for central users"

    app.logger.info(f"Central user - using company_id from request: {company_id}")
    return company_id, user_id, None


@service_api.route('/api/services', methods=['GET'])
@token_required
@roles_required('hr')
def get_services():
    """Get all services with optional filtering."""
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Optional filtering parameters
    active_only = request.args.get('active_only', 'true').lower() == 'true'
        
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        services = Service.get_all_services(
            session=session,
            active_only=active_only
        )
        
        services_data = [service.to_dict() for service in services]
        
        return jsons.dump(Msg.success()
                         .add("services", services_data)
                         .add("count", len(services_data)))


@service_api.route('/api/services/<service_id>', methods=['GET'])
@token_required
@roles_required('hr')
def get_service(service_id):
    """Get a specific service by ID."""
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        service = Service.get_service_by_id(session, service_id)
        
        if not service:
            return jsonify({"message": f"Service with ID {service_id} not found"}), 404
        
        return jsons.dump(Msg.success().add("service", service.to_dict()))


@service_api.route('/api/services', methods=['POST'])
@token_required
@roles_required('hr')
def create_service():
    """Create a new service."""
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401

    data = request.get_json()
    if not data:
        return jsonify({"message": "Request body is required"}), 400
    
    # Validate required fields
    if not data.get('name'):
        return jsonify({"message": "Service name is required"}), 400

    app.logger.info(f"Creating service {data.get('name')} by user {user_id}")
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        try:
            # Add created_by to the data
            data['created_by'] = user_id
            
            service = Service.create_service(session=session, **data)
            
            if not service:
                return jsonify({"message": "Failed to create service"}), 500
            
            app.logger.info(f"Created service: {service.name}")
            
            return jsons.dump(Msg.success()
                             .add("service", service.to_dict())
                             .add("message", "Service created successfully")), 201
        except Exception as e:
            app.logger.error(f"Error creating service: {e}")
            return jsonify({"message": str(e)}), 500


@service_api.route('/api/services/<service_id>', methods=['PATCH'])
@token_required
@roles_required('hr')
def update_service(service_id):
    """Update an existing service."""
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401

    data = request.get_json()
    if not data:
        return jsonify({"message": "Request body is required"}), 400

    app.logger.info(f"Updating service {service_id} by user {user_id}")
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        try:
            service = Service.update_service(
                session=session,
                service_id=service_id,
                **data
            )
            
            if not service:
                return jsonify({"message": f"Service with ID {service_id} not found"}), 404
            
            app.logger.info(f"Updated service: {service.name}")
            
            return jsons.dump(Msg.success()
                             .add("service", service.to_dict())
                             .add("message", "Service updated successfully"))
        except Exception as e:
            app.logger.error(f"Error updating service: {e}")
            return jsonify({"message": str(e)}), 500


@service_api.route('/api/services/<service_id>', methods=['DELETE'])
@token_required
@roles_required('hr')
def deactivate_service(service_id):
    """Deactivate a service (soft delete)."""
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401

    app.logger.info(f"Deactivating service {service_id} by user {user_id}")
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        try:
            success = Service.deactivate_service(session=session, service_id=service_id)
            
            if not success:
                return jsonify({"message": f"Service with ID {service_id} not found"}), 404
            
            app.logger.info(f"Deactivated service: {service_id}")
            
            return jsons.dump(Msg.success()
                             .add("message", "Service deactivated successfully"))
        except Exception as e:
            app.logger.error(f"Error deactivating service: {e}")
            return jsonify({"message": str(e)}), 500