"""Service consumption tracking API routes."""

from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from application.Models.customers.services import CustomerServiceConsumption
from application.Models.company import Company
from application.Models.Msg import Msg
import jsons
from datetime import datetime, date
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required


service_consumption_api = Blueprint('service_consumption_api', __name__)


def get_user_context():
    """Helper function to extract user and company context.

    This function handles both company users and central users:
    - Company users: company_id is extracted from JWT token automatically
    - Central users (HR/Admin): company_id must be provided in request

    Returns:
        tuple: (company_id, user_id, error_message)
    """
    user_info = getattr(g, 'user', None)
    if not user_info:
        return None, None, "Authentication required"

    user_id = user_info.get('user_id')
    if not user_id:
        return None, None, "User context required"

    # For company users, company_id is in the token
    company_id = user_info.get('company_id')
    if company_id:
        app.logger.info(f"Company user - using company_id from token: {company_id}")
        return company_id, user_id, None

    # For central users (HR/admin), get company_id from request
    company_id = request.args.get('company_id') or (request.json.get('company_id') if request.json else None)
    if not company_id:
        return None, None, "Company ID is required for central users"

    app.logger.info(f"Central user - using company_id from request: {company_id}")
    return company_id, user_id, None


@service_consumption_api.route('/api/service-consumptions', methods=['GET'])
@token_required
@roles_required('hr')
def get_service_consumptions():
    """Get all service consumptions with optional filtering."""
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Optional filtering parameters
    customer_id = request.args.get('customer_id')
    service_id = request.args.get('service_id')
    visit_id = request.args.get('visit_id')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    # Parse dates if provided
    if start_date:
        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d')
        except ValueError:
            return jsonify({"message": "Invalid start_date format. Use YYYY-MM-DD"}), 400
    
    if end_date:
        try:
            end_date = datetime.strptime(end_date, '%Y-%m-%d')
        except ValueError:
            return jsonify({"message": "Invalid end_date format. Use YYYY-MM-DD"}), 400
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        consumptions = CustomerServiceConsumption.get_all_consumptions(
            session=session,
            customer_id=customer_id,
            service_id=service_id,
            visit_id=visit_id,
            start_date=start_date,
            end_date=end_date
        )
        
        consumptions_data = [consumption.to_dict() for consumption in consumptions]
        
        return jsons.dump(Msg.success()
                         .add("service_consumptions", consumptions_data)
                         .add("count", len(consumptions_data)))


@service_consumption_api.route('/api/service-consumptions/<consumption_id>', methods=['GET'])
@token_required
@roles_required('hr')
def get_service_consumption(consumption_id):
    """Get a specific service consumption by ID."""
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        consumption = CustomerServiceConsumption.get_consumption_by_id(session, consumption_id)
        
        if not consumption:
            return jsonify({"message": f"Service consumption with ID {consumption_id} not found"}), 404
        
        return jsons.dump(Msg.success().add("service_consumption", consumption.to_dict()))


@service_consumption_api.route('/api/service-consumptions', methods=['POST'])
@token_required
@roles_required('hr')
def create_service_consumption():
    """Create a new service consumption record."""
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401

    data = request.get_json()
    if not data:
        return jsonify({"message": "Request body is required"}), 400
    
    # Validate required fields
    if not data.get('customer_id'):
        return jsonify({"message": "Customer ID is required"}), 400
    if not data.get('service_id'):
        return jsonify({"message": "Service ID is required"}), 400
    # Note: price_id is now optional - will be auto-determined if not provided

    # Validate quantity if provided
    if 'quantity' in data:
        try:
            quantity = int(data['quantity'])
            if quantity <= 0:
                return jsonify({"message": "Quantity must be a positive integer"}), 400
            data['quantity'] = quantity
        except (ValueError, TypeError):
            return jsonify({"message": "Quantity must be a valid integer"}), 400

    app.logger.info(f"Creating service consumption for customer {data.get('customer_id')} by user {user_id}")
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        try:
            # Parse consumed_at if provided
            if data.get('consumed_at'):
                data['consumed_at'] = datetime.strptime(data['consumed_at'], '%Y-%m-%d %H:%M:%S')
            
            consumption = CustomerServiceConsumption.create_consumption(session=session, **data)
            
            if not consumption:
                return jsonify({"message": "Failed to create service consumption"}), 500
            
            app.logger.info(f"Created service consumption: {consumption.consumption_id}")
            
            return jsons.dump(Msg.success()
                             .add("service_consumption", consumption.to_dict())
                             .add("message", "Service consumption created successfully")), 201
        except ValueError as e:
            return jsonify({"message": str(e)}), 400
        except Exception as e:
            app.logger.error(f"Error creating service consumption: {e}")
            return jsonify({"message": str(e)}), 500


@service_consumption_api.route('/api/service-consumptions/bulk', methods=['POST'])
@token_required
@roles_required('hr')
def create_bulk_service_consumption():
    """Create multiple service consumption records in a single transaction.

    Request body example:
    {
        "customer_id": "uuid",
        "visit_id": "uuid",  // Optional
        "consumed_at": "2024-01-15 14:30:00",  // Optional
        "services": [
            {
                "service_id": "uuid-sauna",
                "quantity": 2,
                "notes": "Extended sauna session"
            },
            {
                "service_id": "uuid-massage",
                "quantity": 1,
                "notes": "Deep tissue massage"
            }
        ]
    }
    """
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401

    data = request.get_json()
    if not data:
        return jsonify({"message": "Request body is required"}), 400

    # Validate required fields
    if not data.get('customer_id'):
        return jsonify({"message": "Customer ID is required"}), 400
    if not data.get('services'):
        return jsonify({"message": "Services list is required"}), 400
    if not isinstance(data.get('services'), list):
        return jsonify({"message": "Services must be a list"}), 400
    if len(data.get('services')) == 0:
        return jsonify({"message": "At least one service must be provided"}), 400

    app.logger.info(f"Creating bulk service consumption for customer {data.get('customer_id')} with {len(data.get('services'))} services by user {user_id}")

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        try:
            # Prepare common data
            common_data = {}
            if data.get('visit_id'):
                common_data['visit_id'] = data['visit_id']
            if data.get('consumed_at'):
                common_data['consumed_at'] = datetime.strptime(data['consumed_at'], '%Y-%m-%d %H:%M:%S')

            # Create bulk consumption
            result = CustomerServiceConsumption.create_bulk_consumption(
                session=session,
                customer_id=data['customer_id'],
                services=data['services'],
                **common_data
            )

            app.logger.info(f"Created {result['summary']['total_services']} service consumptions with total amount ${result['summary']['total_amount']}")

            return jsons.dump(Msg.success()
                             .add("bulk_consumption", result)
                             .add("message", f"Successfully created {result['summary']['total_services']} service consumption records")), 201

        except ValueError as e:
            return jsonify({"message": str(e)}), 400
        except Exception as e:
            app.logger.error(f"Error creating bulk service consumption: {e}")
            return jsonify({"message": str(e)}), 500


@service_consumption_api.route('/api/service-consumptions/<consumption_id>', methods=['PUT'])
@token_required
@roles_required('hr')
def update_service_consumption(consumption_id):
    """Update an existing service consumption."""
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401

    data = request.get_json()
    if not data:
        return jsonify({"message": "Request body is required"}), 400

    app.logger.info(f"Updating service consumption {consumption_id} by user {user_id}")
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        try:
            # Parse consumed_at if provided
            if data.get('consumed_at'):
                data['consumed_at'] = datetime.strptime(data['consumed_at'], '%Y-%m-%d %H:%M:%S')
            
            consumption = CustomerServiceConsumption.update_consumption(
                session=session,
                consumption_id=consumption_id,
                **data
            )
            
            if not consumption:
                return jsonify({"message": f"Service consumption with ID {consumption_id} not found"}), 404
            
            app.logger.info(f"Updated service consumption: {consumption_id}")
            
            return jsons.dump(Msg.success()
                             .add("service_consumption", consumption.to_dict())
                             .add("message", "Service consumption updated successfully"))
        except ValueError as e:
            return jsonify({"message": str(e)}), 400
        except Exception as e:
            app.logger.error(f"Error updating service consumption: {e}")
            return jsonify({"message": str(e)}), 500


@service_consumption_api.route('/api/service-consumptions/<consumption_id>', methods=['DELETE'])
@token_required
@roles_required('hr')
def delete_service_consumption(consumption_id):
    """Delete a service consumption record."""
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401

    app.logger.info(f"Deleting service consumption {consumption_id} by user {user_id}")
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        try:
            success = CustomerServiceConsumption.delete_consumption(session=session, consumption_id=consumption_id)
            
            if not success:
                return jsonify({"message": f"Service consumption with ID {consumption_id} not found"}), 404
            
            app.logger.info(f"Deleted service consumption: {consumption_id}")
            
            return jsons.dump(Msg.success()
                             .add("message", "Service consumption deleted successfully"))
        except Exception as e:
            app.logger.error(f"Error deleting service consumption: {e}")
            return jsonify({"message": str(e)}), 500


@service_consumption_api.route('/api/service-consumptions/total', methods=['GET'])
@token_required
@roles_required('hr')
def get_consumption_total():
    """Calculate total consumption amount with optional filtering."""
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Optional filtering parameters
    customer_id = request.args.get('customer_id')
    visit_id = request.args.get('visit_id')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    # Parse dates if provided
    if start_date:
        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d')
        except ValueError:
            return jsonify({"message": "Invalid start_date format. Use YYYY-MM-DD"}), 400
    
    if end_date:
        try:
            end_date = datetime.strptime(end_date, '%Y-%m-%d')
        except ValueError:
            return jsonify({"message": "Invalid end_date format. Use YYYY-MM-DD"}), 400
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        total = CustomerServiceConsumption.get_consumption_total(
            session=session,
            customer_id=customer_id,
            visit_id=visit_id,
            start_date=start_date,
            end_date=end_date
        )
        
        return jsons.dump(Msg.success()
                         .add("total_amount", float(total))
                         .add("currency", "USD"))
