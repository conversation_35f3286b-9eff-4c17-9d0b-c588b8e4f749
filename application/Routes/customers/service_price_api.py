"""Service price management API routes."""

from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from application.Models.customers.services import ServicePrice
from application.Models.company import Company
from application.Models.Msg import Msg
import jsons
from datetime import datetime, date
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required


service_price_api = Blueprint('service_price_api', __name__)


def get_user_context():
    """Helper function to extract user and company context.

    This function handles both company users and central users:
    - Company users: company_id is extracted from JWT token automatically
    - Central users (HR/Admin): company_id must be provided in request

    Returns:
        tuple: (company_id, user_id, error_message)
    """
    user_info = getattr(g, 'user', None)
    if not user_info:
        return None, None, "Authentication required"

    user_id = user_info.get('user_id')
    if not user_id:
        return None, None, "User context required"

    # For company users, company_id is in the token
    company_id = user_info.get('company_id')
    if company_id:
        app.logger.info(f"Company user - using company_id from token: {company_id}")
        return company_id, user_id, None

    # For central users (HR/admin), get company_id from request
    company_id = request.args.get('company_id') or (request.json.get('company_id') if request.json else None)
    if not company_id:
        return None, None, "Company ID is required for central users"

    app.logger.info(f"Central user - using company_id from request: {company_id}")
    return company_id, user_id, None


@service_price_api.route('/api/service-prices', methods=['GET'])
@token_required
@roles_required('hr')
def get_service_prices():
    """Get all service prices with optional filtering."""
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Optional filtering parameters
    service_id = request.args.get('service_id')
    active_only = request.args.get('active_only', 'true').lower() == 'true'
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        prices = ServicePrice.get_all_prices(
            session=session,
            service_id=service_id,
            active_only=active_only
        )
        
        prices_data = [price.to_dict() for price in prices]
        
        return jsons.dump(Msg.success()
                         .add("service_prices", prices_data)
                         .add("count", len(prices_data)))


@service_price_api.route('/api/service-prices/<price_id>', methods=['GET'])
@token_required
@roles_required('hr')
def get_service_price(price_id):
    """Get a specific service price by ID."""
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        price = ServicePrice.get_price_by_id(session, price_id)
        
        if not price:
            return jsonify({"message": f"Service price with ID {price_id} not found"}), 404
        
        return jsons.dump(Msg.success().add("service_price", price.to_dict()))


@service_price_api.route('/api/service-prices', methods=['POST'])
@token_required
@roles_required('hr')
def create_service_price():
    """Create a new service price."""
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401

    data = request.get_json()
    if not data:
        return jsonify({"message": "Request body is required"}), 400
    
    # Validate required fields
    if not data.get('service_id'):
        return jsonify({"message": "Service ID is required"}), 400
    if not data.get('price_amount'):
        return jsonify({"message": "Price amount is required"}), 400

    app.logger.info(f"Creating service price for service {data.get('service_id')} by user {user_id}")
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        try:
            # Add created_by to the data
            data['created_by'] = user_id
            
            # Parse dates if provided
            if data.get('effective_from'):
                data['effective_from'] = datetime.strptime(data['effective_from'], '%Y-%m-%d').date()
            if data.get('effective_to'):
                data['effective_to'] = datetime.strptime(data['effective_to'], '%Y-%m-%d').date()
            
            price = ServicePrice.create_price(session=session, **data)
            
            if not price:
                return jsonify({"message": "Failed to create service price"}), 500
            
            app.logger.info(f"Created service price: {price.price_id}")
            
            return jsons.dump(Msg.success()
                             .add("service_price", price.to_dict())
                             .add("message", "Service price created successfully")), 201
        except ValueError as e:
            return jsonify({"message": str(e)}), 400
        except Exception as e:
            app.logger.error(f"Error creating service price: {e}")
            return jsonify({"message": str(e)}), 500


@service_price_api.route('/api/service-prices/<price_id>', methods=['PATCH'])
@token_required
@roles_required('hr')
def update_service_price(price_id):
    """Update an existing service price."""
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401

    data = request.get_json()
    if not data:
        return jsonify({"message": "Request body is required"}), 400

    app.logger.info(f"Updating service price {price_id} by user {user_id}")
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        try:
            # Parse dates if provided
            if data.get('effective_from'):
                data['effective_from'] = datetime.strptime(data['effective_from'], '%Y-%m-%d').date()
            if data.get('effective_to'):
                data['effective_to'] = datetime.strptime(data['effective_to'], '%Y-%m-%d').date()
            
            price = ServicePrice.update_price(
                session=session,
                price_id=price_id,
                **data
            )
            
            if not price:
                return jsonify({"message": f"Service price with ID {price_id} not found"}), 404
            
            app.logger.info(f"Updated service price: {price_id}")
            
            return jsons.dump(Msg.success()
                             .add("service_price", price.to_dict())
                             .add("message", "Service price updated successfully"))
        except ValueError as e:
            return jsonify({"message": str(e)}), 400
        except Exception as e:
            app.logger.error(f"Error updating service price: {e}")
            return jsonify({"message": str(e)}), 500


@service_price_api.route('/api/service-prices/<price_id>/expire', methods=['POST'])
@token_required
@roles_required('hr')
def expire_service_price(price_id):
    """Expire a service price by setting its effective_to date."""
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401

    data = request.get_json() or {}
    effective_to_date = data.get('effective_to_date')
    
    if effective_to_date:
        try:
            effective_to_date = datetime.strptime(effective_to_date, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({"message": "Invalid date format. Use YYYY-MM-DD"}), 400

    app.logger.info(f"Expiring service price {price_id} by user {user_id}")
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        try:
            success = ServicePrice.expire_price(
                session=session,
                price_id=price_id,
                effective_to_date=effective_to_date
            )
            
            if not success:
                return jsonify({"message": f"Service price with ID {price_id} not found"}), 404
            
            app.logger.info(f"Expired service price: {price_id}")
            
            return jsons.dump(Msg.success()
                             .add("message", "Service price expired successfully"))
        except Exception as e:
            app.logger.error(f"Error expiring service price: {e}")
            return jsonify({"message": str(e)}), 500


@service_price_api.route('/api/services/<service_id>/current-price', methods=['GET'])
@token_required
@roles_required('hr')
def get_current_service_price(service_id):
    """Get the current price for a service."""
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Optional date parameter
    date_check = request.args.get('date')
    if date_check:
        try:
            date_check = datetime.strptime(date_check, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({"message": "Invalid date format. Use YYYY-MM-DD"}), 400
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        price = ServicePrice.get_current_price(session, service_id, date_check)
        
        if not price:
            return jsonify({"message": f"No current price found for service {service_id}"}), 404
        
        return jsons.dump(Msg.success().add("current_price", price.to_dict()))
