"""Storage location management API routes."""

from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from application.Models.customers.services import StorageLocation
from application.Models.company import Company
from application.Models.Msg import Msg
import jsons
from datetime import datetime, date
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required


storage_location_api = Blueprint('storage_location_api', __name__)


def get_user_context():
    """Helper function to extract user and company context.

    This function handles both company users and central users:
    - Company users: company_id is extracted from JWT token automatically
    - Central users (HR/Admin): company_id must be provided in request

    Returns:
        tuple: (company_id, user_id, error_message)
    """
    user_info = getattr(g, 'user', None)
    if not user_info:
        return None, None, "Authentication required"

    user_id = user_info.get('user_id')
    if not user_id:
        return None, None, "User context required"

    # For company users, company_id is in the token
    company_id = user_info.get('company_id')
    if company_id:
        app.logger.info(f"Company user - using company_id from token: {company_id}")
        return company_id, user_id, None

    # For central users (HR/admin), get company_id from request
    company_id = request.args.get('company_id') or (request.json.get('company_id') if request.json else None)
    if not company_id:
        return None, None, "Company ID is required for central users"

    app.logger.info(f"Central user - using company_id from request: {company_id}")
    return company_id, user_id, None


@storage_location_api.route('/api/storage-locations', methods=['GET'])
@token_required
@roles_required('hr')
def get_storage_locations():
    """Get all storage locations with optional filtering."""
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Optional filtering parameters
    available_only = request.args.get('available_only', 'true').lower()
        
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        locations = StorageLocation.get_all_locations(
            session=session,
            available_only=available_only
        )
        
        locations_data = [location.to_dict() for location in locations]
        
        return jsons.dump(Msg.success()
                         .add("storage_locations", locations_data)
                         .add("count", len(locations_data)))


@storage_location_api.route('/api/storage-locations/<location_id>', methods=['GET'])
@token_required
@roles_required('hr')
def get_storage_location(location_id):
    """Get a specific storage location by ID."""
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        location = StorageLocation.get_location_by_id(session, location_id)
        
        if not location:
            return jsonify({"message": f"Storage location with ID {location_id} not found"}), 404
        
        return jsons.dump(Msg.success().add("storage_location", location.to_dict()))


@storage_location_api.route('/api/storage-locations', methods=['POST'])
@token_required
@roles_required('hr')
def create_storage_location():
    """Create a new storage location."""
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401

    data = request.get_json()
    if not data:
        return jsonify({"message": "Request body is required"}), 400
    
    # Validate required fields
    if not data.get('location_number'):
        return jsonify({"message": "Location number is required"}), 400

    app.logger.info(f"Creating storage location {data.get('location_number')} by user {user_id}")
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        try:
            location = StorageLocation.create_location(session=session, **data)
            
            if not location:
                return jsonify({"message": "Failed to create storage location"}), 500
            
            app.logger.info(f"Created storage location: {location.location_number}")
            
            return jsons.dump(Msg.success()
                             .add("storage_location", location.to_dict())
                             .add("message", "Storage location created successfully")), 201
        except ValueError as e:
            return jsonify({"message": str(e)}), 400
        except Exception as e:
            app.logger.error(f"Error creating storage location: {e}")
            return jsonify({"message": str(e)}), 500


@storage_location_api.route('/api/storage-locations/<location_id>', methods=['PATCH'])
@token_required
@roles_required('hr')
def update_storage_location(location_id):
    """Update an existing storage location."""
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401

    data = request.get_json()
    if not data:
        return jsonify({"message": "Request body is required"}), 400

    app.logger.info(f"Updating storage location {location_id} by user {user_id}")
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        try:
            location = StorageLocation.update_location(
                session=session,
                location_id=location_id,
                **data
            )
            
            if not location:
                return jsonify({"message": f"Storage location with ID {location_id} not found"}), 404
            
            app.logger.info(f"Updated storage location: {location.location_number}")
            
            return jsons.dump(Msg.success()
                             .add("storage_location", location.to_dict())
                             .add("message", "Storage location updated successfully"))
        except Exception as e:
            app.logger.error(f"Error updating storage location: {e}")
            return jsonify({"message": str(e)}), 500


@storage_location_api.route('/api/storage-locations/<location_id>', methods=['DELETE'])
@token_required
@roles_required('hr')
def delete_storage_location(location_id):
    """Delete a storage location."""
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401

    app.logger.info(f"Deleting storage location {location_id} by user {user_id}")
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        try:
            success = StorageLocation.delete_location(session=session, location_id=location_id)
            
            if not success:
                return jsonify({"message": f"Storage location with ID {location_id} not found"}), 404
            
            app.logger.info(f"Deleted storage location: {location_id}")
            
            return jsons.dump(Msg.success()
                             .add("message", "Storage location deleted successfully"))
        except Exception as e:
            app.logger.error(f"Error deleting storage location: {e}")
            return jsonify({"message": str(e)}), 500


@storage_location_api.route('/api/storage-locations/number/<location_number>', methods=['GET'])
@token_required
@roles_required('hr')
def get_storage_location_by_number(location_number):
    """Get a storage location by its number."""
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        location = StorageLocation.get_location_by_number(session, location_number)
        
        if not location:
            return jsonify({"message": f"Storage location with number {location_number} not found"}), 404
        
        return jsons.dump(Msg.success().add("storage_location", location.to_dict()))


@storage_location_api.route('/api/storage-locations/available', methods=['GET'])
@token_required
@roles_required('hr')
def get_available_storage_locations():
    """Get all available storage locations."""
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401
    
    # Optional filtering parameters
    location_type = request.args.get('location_type')
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        locations = StorageLocation.get_available_locations(
            session=session,
            location_type=location_type
        )
        
        locations_data = [location.to_dict() for location in locations]
        
        return jsons.dump(Msg.success()
                         .add("available_locations", locations_data)
                         .add("count", len(locations_data)))


@storage_location_api.route('/api/storage-locations/<location_id>/availability', methods=['POST'])
@token_required
@roles_required('hr')
def set_storage_location_availability(location_id):
    """Set the availability status of a storage location."""
    # Get user context
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401

    data = request.get_json()
    if not data or 'is_available' not in data:
        return jsonify({"message": "is_available field is required"}), 400

    is_available = data.get('is_available')
    if not isinstance(is_available, bool):
        return jsonify({"message": "is_available must be a boolean"}), 400

    app.logger.info(f"Setting storage location {location_id} availability to {is_available} by user {user_id}")
    
    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404
    
    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        try:
            success = StorageLocation.set_availability(
                session=session,
                location_id=location_id,
                is_available=is_available
            )
            
            if not success:
                return jsonify({"message": f"Storage location with ID {location_id} not found"}), 404
            
            app.logger.info(f"Set storage location {location_id} availability to {is_available}")
            
            return jsons.dump(Msg.success()
                             .add("message", f"Storage location availability set to {is_available}"))
        except Exception as e:
            app.logger.error(f"Error setting storage location availability: {e}")
            return jsonify({"message": str(e)}), 500
