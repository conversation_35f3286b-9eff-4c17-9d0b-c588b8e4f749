from flask import Blueprint, request, jsonify, current_app as app
from application.Models.company import Company
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.Models.employees import Department, Employee

departments_api = Blueprint('departments_api', __name__)

# Department CRUD operations
@departments_api.route('/api/departments', methods=['GET'])
@token_required
def get_departments():
    """Get departments for a company with pagination, filtering, and sorting."""
    company_id = request.args.get('company_id')

    if not company_id:
        app.logger.warning("Company ID is required but was not provided")
        return jsonify({"success": False, "message": "Company ID is required"}), 400

    # Pagination parameters
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)

    # Validate pagination parameters
    if page < 1:
        page = 1
    if per_page < 1:
        per_page = 10
    elif per_page > 100:  # Limit maximum page size
        per_page = 100

    # Search and sorting parameters
    search_term = request.args.get('search')
    sort_by = request.args.get('sort_by', 'name')
    sort_order = request.args.get('sort_order', 'asc')

    # Validate sort parameters
    valid_sort_fields = ['name', 'created_at']
    if sort_by not in valid_sort_fields:
        sort_by = 'name'

    valid_sort_orders = ['asc', 'desc']
    if sort_order.lower() not in valid_sort_orders:
        sort_order = 'asc'

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        app.logger.warning(f"Company with ID {company_id} not found")
        return jsonify({"success": False, "message": f"Company with ID {company_id} not found"}), 404

    try:
        # Connect to the database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Get total count and paginated departments
            total_count, departments = Department.get_paginated_departments(
                session,
                page=page,
                per_page=per_page,
                search_term=search_term,
                sort_by=sort_by,
                sort_order=sort_order
            )

            departments_data = [department.to_dict() for department in departments]

            # Prepare pagination metadata
            pagination = {
                "total_count": total_count,
                "page": page,
                "per_page": per_page,
                "pages": (total_count + per_page - 1) // per_page if per_page > 0 else 0,  # Ceiling division
                "has_next": page < ((total_count + per_page - 1) // per_page) if per_page > 0 else False,
                "has_prev": page > 1
            }

            app.logger.info(f"Retrieved {len(departments_data)} departments for company {company_id} (page {page}, total: {total_count})")

            return jsonify({
                "success": True,
                "departments": departments_data,
                "pagination": pagination
            })
    except Exception as e:
        app.logger.error(f"Error retrieving departments: {str(e)}")
        return jsonify({
            "success": False,
            "message": "An error occurred while retrieving departments"
        }), 500

@departments_api.route('/api/departments/<department_id>', methods=['GET'])
@token_required
def get_department(department_id):
    """Get a specific department by ID."""
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        department = Department.get_department_by_id(session, department_id)

        if not department:
            return jsonify({"message": f"Department with ID {department_id} not found"}), 404

        return jsonify({
            "success": True,
            "department": department.to_dict()
        })

@departments_api.route('/api/departments', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def create_department():
    """Create a new department."""
    data = request.get_json()
    company_id = data.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Validate required fields
    if not data.get('name'):
        return jsonify({"message": "Department name is required"}), 400

    # Prepare department data
    department_data = {
        'name': data.get('name'),
        'description': data.get('description'),
        'manager_id': data.get('manager_id')
    }

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if a department with the same name already exists
        existing_department = Department.get_department_by_name(session, department_data['name'])
        if existing_department:
            return jsonify({
                "success": False,
                "message": f"A department with the name '{department_data['name']}' already exists"
            }), 409  # 409 Conflict

        # Create the department
        department = Department.create_department(session, **department_data)

        if not department:
            return jsonify({"message": "Failed to create department"}), 500

        return jsonify({
            "success": True,
            "department": department.to_dict()
        }), 200

@departments_api.route('/api/departments/<department_id>', methods=['PUT'])
@token_required
@roles_required('admin', 'hr')
def update_department(department_id):
    """Update an existing department."""
    data = request.get_json()
    company_id = data.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Prepare update data
    update_data = {}
    for field in ['name', 'description', 'manager_id']:
        if field in data:
            update_data[field] = data[field]

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if department exists
        department = Department.get_department_by_id(session, department_id)
        if not department:
            return jsonify({"message": f"Department with ID {department_id} not found"}), 404

        # If name is being updated, check for duplicates
        if 'name' in update_data and update_data['name'] != department.name:
            existing_department = Department.get_department_by_name(session, update_data['name'])
            if existing_department and str(existing_department.department_id) != str(department_id):
                return jsonify({
                    "success": False,
                    "message": f"A department with the name '{update_data['name']}' already exists"
                }), 409  # 409 Conflict

        # Update the department
        updated_department = Department.update_department(session, department_id, **update_data)

        if not updated_department:
            return jsonify({"message": "Failed to update department"}), 500

        return jsonify({
            "success": True,
            "department": updated_department.to_dict()
        })

@departments_api.route('/api/departments/<department_id>', methods=['DELETE'])
@token_required
@roles_required('admin', 'hr')
def delete_department(department_id):
    """Delete a department."""
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if department exists
        department = Department.get_department_by_id(session, department_id)
        if not department:
            return jsonify({"success": False, "message": f"Department with ID {department_id} not found"}), 404

        # Check if there are employees in this department
        employee_count = Employee.count_employees_in_department(session, department_id)
        if employee_count > 0:
            app.logger.warning(f"Cannot delete department '{department.name}' (ID: {department_id}) because it has {employee_count} employees")
            return jsonify({
                "success": False,
                "message": f"Cannot delete department '{department.name}' because it has {employee_count} employees. Please reassign or remove these employees first."
            }), 409  # 409 Conflict

        # Delete the department
        success = Department.delete_department(session, department_id)

        if not success:
            return jsonify({"success": False, "message": "Failed to delete department"}), 500

        app.logger.info(f"Department '{department.name}' (ID: {department_id}) deleted successfully")

        return jsonify({
            "success": True,
            "message": f"Department '{department.name}' deleted successfully"
        })

