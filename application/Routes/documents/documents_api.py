from flask import Blueprint, request, jsonify, current_app as app, g, send_file
from application.database import db
from application.Models.documents.company_document import CompanyDocument
from application.Models.documents.document_reminder import DocumentReminder
from application.Models.company_storage_quota import CompanyStorageQuota
from application.Services.DocumentStorageService import DocumentStorageService
from application.utils.db_connection import DatabaseConnection
from application.Models.company import Company
from datetime import datetime
import json
import os
from werkzeug.utils import secure_filename
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required

documents_api = Blueprint('documents_api', __name__)

# Allowed file types
ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png', 'xls', 'xlsx'}

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@documents_api.route('/api/documents/upload', methods=['POST'])
@token_required
def upload_document():
    """Upload a new document"""
    from app import db_connection

    # Create a unique request ID for tracking this upload
    import uuid
    request_id = str(uuid.uuid4())[:8]

    # Get the request data. It is sent as form data.
    data = request.form
    app.logger.info(f"[DOCUMENT_UPLOAD_{request_id}] Starting document upload - Form data keys: {list(data.keys())}")

    try:
        # Get file from request
        if 'file' not in request.files:
            app.logger.error(f"[DOCUMENT_UPLOAD_{request_id}] No file in request.files")
            return jsonify({"success": False, "message": "No file provided"}), 400

        file = request.files['file']
        if file.filename == '':
            app.logger.error(f"[DOCUMENT_UPLOAD_{request_id}] Empty filename provided")
            return jsonify({"success": False, "message": "No file selected"}), 400

        app.logger.info(f"[DOCUMENT_UPLOAD_{request_id}] File details - Name: {file.filename}, Content-Type: {file.content_type}, Size: {getattr(file, 'content_length', 'unknown')}")

        # Validate file
        if not allowed_file(file.filename):
            app.logger.error(f"[DOCUMENT_UPLOAD_{request_id}] File type not allowed: {file.filename}")
            return jsonify({"success": False, "message": f"File type not allowed. Allowed: {', '.join(ALLOWED_EXTENSIONS)}"}), 400

        # Get company info
        company_id = g.user.get('company_id')
        user_id = g.user.get('user_id')
        app.logger.info(f"[DOCUMENT_UPLOAD_{request_id}] User: {user_id}, Company: {company_id}")

        if not company_id:
            app.logger.error(f"[DOCUMENT_UPLOAD_{request_id}] No company_id found in user context")
            return jsonify({"success": False, "message": "Company information not found"}), 400

        # Get tenant database connection
        app.logger.info(f"[DOCUMENT_UPLOAD_{request_id}] Getting database name for company: {company_id}")
        database_name = Company.get_database_given_company_id(company_id)

        if not database_name:
            app.logger.error(f"[DOCUMENT_UPLOAD_{request_id}] No database found for company: {company_id}")
            return jsonify({"success": False, "message": "Company database not found"}), 404

        app.logger.info(f"[DOCUMENT_UPLOAD_{request_id}] Using database: {database_name}")

        try:
            with db_connection.get_session(database_name) as session:
                app.logger.info(f"[DOCUMENT_UPLOAD_{request_id}] Database session established successfully")

                # Check storage quota
                app.logger.info(f"[DOCUMENT_UPLOAD_{request_id}] Checking storage quota...")
                quota = CompanyStorageQuota.get_quota_by_company_id(company_id)

                if quota:
                    app.logger.info(f"[DOCUMENT_UPLOAD_{request_id}] Quota found - Used: {quota.storage_used_bytes} bytes, Limit: {quota.storage_quota_gb}GB")
                    if not quota.can_upload_file(file.content_length or 0):
                        app.logger.error(f"[DOCUMENT_UPLOAD_{request_id}] Storage quota exceeded - File size: {file.content_length}, Available: {quota.storage_quota_gb * 1024 * 1024 * 1024 - quota.storage_used_bytes}")
                        return jsonify({"success": False, "message": "Storage quota exceeded"}), 400
                else:
                    app.logger.warning(f"[DOCUMENT_UPLOAD_{request_id}] No quota found for company, proceeding without quota check")

                # Upload file to storage
                app.logger.info(f"[DOCUMENT_UPLOAD_{request_id}] Initializing DocumentStorageService...")
                try:
                    storage_service = DocumentStorageService()
                    app.logger.info(f"[DOCUMENT_UPLOAD_{request_id}] DocumentStorageService initialized successfully")
                except Exception as storage_init_error:
                    app.logger.error(f"[DOCUMENT_UPLOAD_{request_id}] Failed to initialize DocumentStorageService: {storage_init_error}")
                    raise

                # Prepare metadata, filtering out None values to prevent S3 encoding errors
                raw_metadata = {
                    'uploaded_by': user_id,
                    'document_category': request.form.get('document_category', 'OTHER'),
                    'employee_id': request.form.get('employee_id')
                }
                # Filter out None values - S3 doesn't accept None in metadata
                metadata = {k: v for k, v in raw_metadata.items() if v is not None}
                app.logger.info(f"[DOCUMENT_UPLOAD_{request_id}] Prepared metadata: {metadata}")

                app.logger.info(f"[DOCUMENT_UPLOAD_{request_id}] Starting file upload to storage...")
                try:
                    storage_result = storage_service.upload_document(
                        file_obj=file,
                        filename=file.filename,
                        company_id=company_id,
                        metadata=metadata,
                        content_type=file.content_type
                    )
                    app.logger.info(f"[DOCUMENT_UPLOAD_{request_id}] Storage upload successful: {storage_result}")
                except Exception as storage_error:
                    app.logger.error(f"[DOCUMENT_UPLOAD_{request_id}] Storage upload failed: {storage_error}")
                    raise

                # Validate folder if provided
                folder_id = request.form.get('folder_id')
                if folder_id:
                    app.logger.info(f"[DOCUMENT_UPLOAD_{request_id}] Validating folder: {folder_id}")
                    from application.Models.documents.document_folder import DocumentFolder
                    folder = session.query(DocumentFolder).filter_by(
                        folder_id=folder_id
                    ).first()
                    if not folder:
                        app.logger.error(f"[DOCUMENT_UPLOAD_{request_id}] Invalid folder specified: {folder_id}")
                        # Clean up uploaded file if folder validation fails
                        try:
                            storage_service.delete_document(storage_result)
                            app.logger.info(f"[DOCUMENT_UPLOAD_{request_id}] Cleaned up uploaded file after folder validation failure")
                        except Exception as cleanup_error:
                            app.logger.error(f"[DOCUMENT_UPLOAD_{request_id}] Failed to cleanup uploaded file: {cleanup_error}")
                        return jsonify({"success": False, "message": "Invalid folder specified"}), 400
                    else:
                        app.logger.info(f"[DOCUMENT_UPLOAD_{request_id}] Folder validation successful: {folder.folder_name}")

                # Create document record
                app.logger.info(f"[DOCUMENT_UPLOAD_{request_id}] Creating document record...")
                document_data = {
                    'employee_id': request.form.get('employee_id'),
                    'folder_id': folder_id,  # Can be None (root level)
                    'document_name': request.form.get('document_name', file.filename),
                    'document_description': request.form.get('document_description'),
                    'document_category': request.form.get('document_category', 'OTHER'),
                    'original_filename': file.filename,
                    'file_size_bytes': storage_result['file_size_bytes'],
                    'file_type': file.content_type,
                    'mime_type': file.content_type,
                    'storage_provider': storage_result['storage_provider'],
                    'storage_bucket': storage_result['storage_bucket'],
                    'storage_key': storage_result['storage_key'],
                    'storage_url': storage_result['storage_url'],
                    'expiry_date': request.form.get('expiry_date'),  # Can be None
                    'is_confidential': request.form.get('is_confidential', 'false').lower() == 'true',
                    'access_level': request.form.get('access_level', 'STANDARD'),
                    'uploaded_by': user_id
                }

                try:
                    document, error = CompanyDocument.create_document(session, **document_data)
                    if error:
                        app.logger.error(f"[DOCUMENT_UPLOAD_{request_id}] Document creation failed: {error}")
                        # Clean up uploaded file if document creation failed
                        try:
                            storage_service.delete_document(storage_result)
                            app.logger.info(f"[DOCUMENT_UPLOAD_{request_id}] Cleaned up uploaded file after document creation failure")
                        except Exception as cleanup_error:
                            app.logger.error(f"[DOCUMENT_UPLOAD_{request_id}] Failed to cleanup uploaded file: {cleanup_error}")
                        return jsonify({"success": False, "message": error}), 400

                    app.logger.info(f"[DOCUMENT_UPLOAD_{request_id}] Document record created successfully: {document.document_id}")
                except Exception as doc_error:
                    app.logger.error(f"[DOCUMENT_UPLOAD_{request_id}] Exception during document creation: {doc_error}")
                    raise

                # Update storage quota
                if quota:
                    app.logger.info(f"[DOCUMENT_UPLOAD_{request_id}] Updating storage quota...")
                    try:
                        CompanyStorageQuota.update_usage(company_id, storage_result['file_size_bytes'])
                        app.logger.info(f"[DOCUMENT_UPLOAD_{request_id}] Storage quota updated successfully")
                    except Exception as quota_error:
                        app.logger.error(f"[DOCUMENT_UPLOAD_{request_id}] Failed to update storage quota: {quota_error}")
                        # Don't fail the upload for quota update errors

                # Create reminders if document has expiry date
                if document.expiry_date:
                    app.logger.info(f"[DOCUMENT_UPLOAD_{request_id}] Creating expiry reminders for document...")
                    try:
                        DocumentReminder.create_reminders_for_document(
                            session, document.document_id
                        )
                        app.logger.info(f"[DOCUMENT_UPLOAD_{request_id}] Expiry reminders created successfully")
                    except Exception as reminder_error:
                        app.logger.error(f"[DOCUMENT_UPLOAD_{request_id}] Failed to create expiry reminders: {reminder_error}")
                        # Don't fail the upload for reminder creation errors

                app.logger.info(f"[DOCUMENT_UPLOAD_{request_id}] Committing database transaction...")
                # session.commit() is handled by the context manager

                app.logger.info(f"[DOCUMENT_UPLOAD_{request_id}] Document upload completed successfully")
                return jsonify({
                    "success": True,
                    "message": "Document uploaded successfully",
                    "document": document.to_dict(),
                    "request_id": request_id
                }), 200

        except Exception as db_error:
            app.logger.error(f"[DOCUMENT_UPLOAD_{request_id}] Database session error: {db_error}")
            raise

    except Exception as e:
        app.logger.error(f"[DOCUMENT_UPLOAD_{request_id}] Unexpected error during document upload: {str(e)}", exc_info=True)
        return jsonify({
            "success": False,
            "message": "Internal server error",
            "request_id": request_id,
            "error_details": str(e) if app.debug else None
        }), 500

@documents_api.route('/api/documents/<document_id>/download', methods=['GET'])
@token_required
def download_document(document_id):
    """Download a document"""
    from app import db_connection
    try:
        company_id = g.user.get('company_id')
        if not company_id:
            return jsonify({"success": False, "message": "Company information not found"}), 400

        # Get tenant database connection
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"success": False, "message": "Company database not found"}), 404

        with db_connection.get_session(database_name) as session:
            # Get document
            document = CompanyDocument.get_document_by_id(session, document_id)
            if not document:
                return jsonify({"success": False, "message": "Document not found"}), 404

            # Check access permissions (implement role-based logic here)
            if not _check_document_access(document, g.user):
                return jsonify({"success": False, "message": "Access denied"}), 403

            # Get download URL
            storage_service = DocumentStorageService()
            storage_info = {
                'storage_provider': document.storage_provider,
                'storage_bucket': document.storage_bucket,
                'storage_key': document.storage_key
            }

            download_url = storage_service.generate_download_url(storage_info)

            # Log download activity
            app.logger.info(f"Document downloaded: {document_id} by user {g.user.get('user_id')}")

            # Return download URL or redirect
            return jsonify({
                "success": True,
                "download_url": download_url,
                "filename": document.original_filename
            })

    except Exception as e:
        app.logger.error(f"Error downloading document {document_id}: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500

@documents_api.route('/api/documents/<document_id>', methods=['GET'])
@token_required
def get_document(document_id):
    """Get document details"""
    from app import db_connection
    try:
        company_id = g.user.get('company_id')
        if not company_id:
            return jsonify({"success": False, "message": "Company information not found"}), 400

        # Get tenant database connection
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"success": False, "message": "Company database not found"}), 404

        with db_connection.get_session(database_name) as session:
            document = CompanyDocument.get_document_by_id(session, document_id)
            if not document:
                return jsonify({"success": False, "message": "Document not found"}), 404

            return jsonify({
                "success": True,
                "document": document.to_dict()
            })

    except Exception as e:
        app.logger.error(f"Error getting document {document_id}: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500

@documents_api.route('/api/documents', methods=['GET'])
@token_required
def get_documents():
    """Get documents with filtering and pagination.
    Description:
    - page: The page number to get.
    - per_page: The number of documents per page.
    - category: The category of the documents to get.
    - employee_id: The employee id of the documents to get.
    - folder_id: The folder id of the documents to get.
    - include_subfolders: Whether to include subfolders in the search.
    - search: The search term to search for in the documents.
    - status: The status of the documents to get.
    """
    from app import db_connection
    try:
        company_id = g.user.get('company_id')
        if not company_id:
            return jsonify({"success": False, "message": "Company information not found"}), 400

        # Query parameters
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        category = request.args.get('category')
        employee_id = request.args.get('employee_id')
        folder_id = request.args.get('folder_id')
        include_subfolders = request.args.get('include_subfolders', 'false').lower() == 'true'
        search_term = request.args.get('search')
        status = request.args.get('status', 'ACTIVE')

        # Get tenant database connection
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"success": False, "message": "Company database not found"}), 404

        with db_connection.get_session(database_name) as session:
            # Use the enhanced search method
            documents = CompanyDocument.search_documents(
                session=session,
                search_term=search_term,
                category=category,
                employee_id=employee_id,
                folder_id=folder_id,
                include_subfolders=include_subfolders,
                status=status # Pass the status parameter
            )

            # Apply pagination for all searches
            total_count = len(documents)
            documents = documents[(page - 1) * per_page:page * per_page]

            return jsonify({
                "success": True,
                "documents": [doc.to_dict() for doc in documents],
                "pagination": {
                    "page": page,
                    "per_page": per_page,
                    "total_count": total_count,
                    "total_pages": (total_count + per_page - 1) // per_page
                }
            })

    except Exception as e:
        app.logger.error(f"Error getting documents: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500

@documents_api.route('/api/documents/<document_id>', methods=['PUT'])
@token_required
@roles_required('hr', 'admin')
def update_document(document_id):
    """Update document metadata"""
    from app import db_connection
    try:
        company_id = g.user.get('company_id')
        if not company_id:
            return jsonify({"success": False, "message": "Company information not found"}), 400

        # Get tenant database connection
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"success": False, "message": "Company database not found"}), 404

        data = request.get_json()

        with db_connection.get_session(database_name) as session:
            # Update document
            updates = {}
            allowed_fields = [
                'document_name', 'document_description', 'document_category',
                'expiry_date', 'is_confidential', 'access_level'
            ]

            for field in allowed_fields:
                if field in data:
                    updates[field] = data[field]

            document, error = CompanyDocument.update_document(
                session, document_id, **updates
            )

            if error:
                return jsonify({"success": False, "message": error}), 400

            # If expiry date changed, reschedule reminders
            if 'expiry_date' in updates:
                DocumentReminder.reschedule_reminders_for_document(
                    session, document_id, updates['expiry_date']
                )

            session.commit()

            return jsonify({
                "success": True,
                "message": "Document updated successfully",
                "document": document.to_dict()
            })

    except Exception as e:
        app.logger.error(f"Error updating document {document_id}: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500

@documents_api.route('/api/documents/<document_id>/archive', methods=['PUT'])
@token_required
@roles_required('hr', 'admin')
def archive_document(document_id):
    """Archive a document"""
    from app import db_connection
    try:
        company_id = g.user.get('company_id')
        if not company_id:
            return jsonify({"success": False, "message": "Company information not found"}), 400

        # Get tenant database connection
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"success": False, "message": "Company database not found"}), 404

        with db_connection.get_session(database_name) as session:
            success, error = CompanyDocument.archive_document(session, document_id)
            if not success:
                return jsonify({"success": False, "message": error}), 400

            session.commit()

            return jsonify({
                "success": True,
                "message": "Document archived successfully"
            })

    except Exception as e:
        app.logger.error(f"Error archiving document {document_id}: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500

@documents_api.route('/api/documents/<document_id>', methods=['DELETE'])
@token_required
@roles_required('hr', 'admin')
def delete_document(document_id):
    """Soft delete a document"""
    from app import db_connection
    try:
        company_id = g.user.get('company_id')
        if not company_id:
            return jsonify({"success": False, "message": "Company information not found"}), 400

        # Get tenant database connection
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"success": False, "message": "Company database not found"}), 404

        with db_connection.get_session(database_name) as session:
            # Get document before deletion for cleanup
            document = CompanyDocument.get_document_by_id(session, document_id)
            if not document:
                return jsonify({"success": False, "message": "Document not found"}), 404

            # Delete from storage
            storage_service = DocumentStorageService()
            storage_info = {
                'storage_provider': document.storage_provider,
                'storage_bucket': document.storage_bucket,
                'storage_key': document.storage_key
            }

            storage_deleted = storage_service.delete_document(storage_info)

            # Soft delete from database
            success, error = CompanyDocument.delete_document(session, document_id)
            if not success:
                return jsonify({"success": False, "message": error}), 400

            # Update storage quota
            quota = CompanyStorageQuota.get_quota_by_company_id(company_id)
            if quota:
                CompanyStorageQuota.reduce_usage(company_id, document.file_size_bytes)

            session.commit()

            return jsonify({
                "success": True,
                "message": "Document deleted successfully",
                "storage_deleted": storage_deleted
            })

    except Exception as e:
        app.logger.error(f"Error deleting document {document_id}: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500

@documents_api.route('/api/documents/expiring', methods=['GET'])
@token_required
def get_expiring_documents():
    """Get documents expiring soon"""
    from app import db_connection
    try:
        company_id = g.user.get('company_id')
        if not company_id:
            return jsonify({"success": False, "message": "Company information not found"}), 400

        days_ahead = request.args.get('days_ahead', 30, type=int)

        # Get tenant database connection
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"success": False, "message": "Company database not found"}), 404

        with db_connection.get_session(database_name) as session:
            documents = CompanyDocument.get_expiring_documents(session, days_ahead)

            return jsonify({
                "success": True,
                "documents": [doc.to_dict() for doc in documents],
                "count": len(documents)
            })

    except Exception as e:
        app.logger.error(f"Error getting expiring documents: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500

@documents_api.route('/api/documents/storage/usage', methods=['GET'])
@token_required
def get_storage_usage():
    """Get storage usage for company"""
    from app import db_connection
    try:
        company_id = g.user.get('company_id')
        if not company_id:
            return jsonify({"success": False, "message": "Company information not found"}), 400

        # Get quota info
        quota = CompanyStorageQuota.get_quota_by_company_id(company_id)

        # Get actual usage from storage
        storage_service = DocumentStorageService()
        usage = storage_service.get_storage_usage(company_id)

        result = {
            "success": True,
            "company_id": company_id,
            "usage": usage
        }

        if quota:
            result["quota"] = quota.to_dict()

        return jsonify(result)

    except Exception as e:
        app.logger.error(f"Error getting storage usage for company {g.user.get('company_id')}: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500

@documents_api.route('/api/documents/categories', methods=['GET'])
def get_document_categories():
    """Get available document categories"""
    from app import db_connection
    try:
        from application.Models.document_category import DocumentCategory
        categories = DocumentCategory.get_categories_list()
        return jsonify({
            "success": True,
            "categories": categories
        })
    except Exception as e:
        app.logger.error(f"Error getting document categories: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500

@documents_api.route('/api/documents/search', methods=['GET'])
@token_required
def search_documents():
    """Advanced document search"""
    from app import db_connection
    try:
        company_id = g.user.get('company_id')
        if not company_id:
            return jsonify({"success": False, "message": "Company information not found"}), 400

        # Get tenant database connection
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"success": False, "message": "Company database not found"}), 404

        search_term = request.args.get('q')
        category = request.args.get('category')
        employee_id = request.args.get('employee_id')

        with db_connection.get_session(database_name) as session:
            documents = CompanyDocument.search_documents(
                session, search_term, category, employee_id
            )

            return jsonify({
                "success": True,
                "documents": [doc.to_dict() for doc in documents],
                "count": len(documents)
            })

    except Exception as e:
        app.logger.error(f"Error searching documents: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500

@documents_api.route('/api/documents/move-to-folder', methods=['PUT'])
@token_required
@roles_required('hr', 'admin')
def move_documents_to_folder():
    """Move multiple documents to a folder."""
    from app import db_connection
    try:
        company_id = g.user.get('company_id')
        if not company_id:
            return jsonify({"success": False, "message": "Company information not found"}), 400

        data = request.get_json()
        document_ids = data.get('document_ids', [])
        folder_id = data.get('folder_id')  # Can be None to move to root

        if not document_ids:
            return jsonify({"success": False, "message": "No documents specified"}), 400

        # Get tenant database connection
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"success": False, "message": "Company database not found"}), 404

        with db_connection.get_session(database_name) as session:
            # Validate target folder if provided
            if folder_id:
                from application.Models.documents.document_folder import DocumentFolder
                folder = session.query(DocumentFolder).filter_by(
                    folder_id=folder_id
                ).first()
                if not folder:
                    return jsonify({"success": False, "message": "Target folder not found"}), 404

            updated_count, error = CompanyDocument.move_documents_to_folder(
                session=session,
                document_ids=document_ids,
                new_folder_id=folder_id
            )

            if error:
                return jsonify({"success": False, "message": error}), 400

            return jsonify({
                "success": True,
                "message": f"Moved {updated_count} documents to folder",
                "updated_count": updated_count
            })

    except Exception as e:
        app.logger.error(f"Error moving documents to folder: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500

@documents_api.route('/api/documents/<document_id>/move-to-folder', methods=['PUT'])
@token_required
@roles_required('hr', 'admin')
def move_single_document_to_folder(document_id):
    """Move a single document to a folder."""
    from app import db_connection
    try:
        company_id = g.user.get('company_id')
        if not company_id:
            return jsonify({"success": False, "message": "Company information not found"}), 400

        data = request.get_json()
        folder_id = data.get('folder_id')  # Can be None to move to root

        # Get tenant database connection
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"success": False, "message": "Company database not found"}), 404

        with db_connection.get_session(database_name) as session:
            # Validate target folder if provided
            if folder_id:
                from application.Models.documents.document_folder import DocumentFolder
                folder = session.query(DocumentFolder).filter_by(
                    folder_id=folder_id
                ).first()
                if not folder:
                    return jsonify({"success": False, "message": "Target folder not found"}), 404

            document, error = CompanyDocument.move_document_to_folder(
                session=session,
                document_id=document_id,
                new_folder_id=folder_id
            )

            if error:
                return jsonify({"success": False, "message": error}), 400

            return jsonify({
                "success": True,
                "message": "Document moved to folder successfully",
                "document": document.to_dict()
            })

    except Exception as e:
        app.logger.error(f"Error moving document {document_id} to folder: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500

def _check_document_access(document, user):
    """Check if user has access to document"""
    # Implement role-based access logic here
    # For now, allow all authenticated users in the same company
    # The document is already retrieved from the company-specific database,
    # so it implicitly belongs to the user's company.
    return True # Or implement more granular access control if needed
