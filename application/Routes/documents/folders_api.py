from flask import Blueprint, request, jsonify, current_app as app, g
from application.database import db
from application.Models.documents.document_folder import DocumentFolder
from application.Models.documents.company_document import CompanyDocument
from application.utils.db_connection import DatabaseConnection
from application.Models.company import Company
from datetime import datetime
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required

folders_api = Blueprint('folders_api', __name__)

@folders_api.route('/api/folders', methods=['POST'])
@token_required
def create_folder():
    """Create a new folder.
    Request Body:
    - folder_name: Required. The name of the folder.
    - parent_folder_id: Optional. The ID of the parent folder.
    - description: Optional. A description of the folder.
    - color: Optional. The color of the folder.
    - icon: Optional. The icon of the folder.
    - is_private: Optional. Whether the folder is private. Defaults to false.
    - allowed_roles: Optional. Array of roles allowed to access the folder. Defaults to ['hr', 'admin', 'employee'].
    """

    try:
        company_id = g.user.get('company_id')
        if not company_id:
            return jsonify({"success": False, "message": "Company information not found"}), 400

        data = request.get_json()
        folder_name = data.get('folder_name')
        parent_folder_id = data.get('parent_folder_id')
        description = data.get('description')
        color = data.get('color')
        icon = data.get('icon')
        is_private = data.get('is_private', False)
        allowed_roles = data.get('allowed_roles', ['hr', 'admin', 'employee'])

        if not folder_name or not folder_name.strip():
            return jsonify({"success": False, "message": "Folder name is required"}), 400

        # Get tenant database connection
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"success": False, "message": "Company database not found"}), 404

        from app import db_connection
        with db_connection.get_session(database_name) as session:
            folder, error = DocumentFolder.create_folder(
                session=session,
                folder_name=folder_name.strip(),
                parent_folder_id=parent_folder_id,
                created_by=g.user.get('user_id'),
                description=description,
                color=color,
                icon=icon,
                is_private=is_private,
                allowed_roles=allowed_roles
            )

            if error:
                return jsonify({"success": False, "message": error}), 400

            return jsonify({    
                "success": True,
                "message": "Folder created successfully",
                "folder": folder.to_dict()
            }), 201

    except Exception as e:
        app.logger.error(f"Error creating folder: {e}")
        error_message = f"Error creating folder: {e}"           
        return jsonify({"success": False, "message": error_message}), 500

@folders_api.route('/api/folders/tree', methods=['GET'])
@token_required
def get_folder_tree():
    """Get the complete folder tree for the company."""
    try:
        company_id = g.user.get('company_id')
        if not company_id:
            return jsonify({"success": False, "message": "Company information not found"}), 400

        include_documents = request.args.get('include_documents', 'false').lower() == 'true'

        # Get tenant database connection
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"success": False, "message": "Company database not found"}), 404
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            tree = DocumentFolder.get_folder_tree(  
                session=session,
                include_documents=include_documents
            )

            return jsonify({
                "success": True,
                "tree": tree
            })

    except Exception as e:
        app.logger.error(f"Error getting folder tree: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500

@folders_api.route('/api/folders/<folder_id>', methods=['GET'])
@token_required
def get_folder(folder_id):
    """Get folder details."""
    try:
        company_id = g.user.get('company_id')
        if not company_id:
            return jsonify({"success": False, "message": "Company information not found"}), 400

        # Get tenant database connection
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"success": False, "message": "Company database not found"}), 404
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            folder = DocumentFolder.get_folder_by_id(session, folder_id, company_id)
            if not folder:
                return jsonify({"success": False, "message": "Folder not found"}), 404

            # Check access permissions
            if not folder.can_access(g.user):
                return jsonify({"success": False, "message": "Access denied"}), 403

            return jsonify({
                "success": True,
                "folder": folder.to_dict()
            })

    except Exception as e:
        app.logger.error(f"Error getting folder {folder_id}: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500

@folders_api.route('/api/folders/<folder_id>', methods=['PUT'])
@token_required
@roles_required('hr', 'admin')
def update_folder(folder_id):
    """Update folder details."""
    try:
        company_id = g.user.get('company_id')
        if not company_id:
            return jsonify({"success": False, "message": "Company information not found"}), 400

        data = request.get_json()

        # Get tenant database connection
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"success": False, "message": "Company database not found"}), 404
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            folder, error = DocumentFolder.update_folder(
                session=session,
                folder_id=folder_id,
                **data
            )

            if error:
                return jsonify({"success": False, "message": error}), 400

            return jsonify({
                "success": True,
                "message": "Folder updated successfully",
                "folder": folder.to_dict()
            })

    except Exception as e:
        app.logger.error(f"Error updating folder {folder_id}: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500

@folders_api.route('/api/folders/<folder_id>', methods=['DELETE'])
@token_required
@roles_required('hr', 'admin')
def delete_folder(folder_id):
    """Delete a folder."""
    try:
        company_id = g.user.get('company_id')
        if not company_id:
            return jsonify({"success": False, "message": "Company information not found"}), 400

        force_delete = request.args.get('force_delete', 'false').lower() == 'true'

        # Get tenant database connection
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"success": False, "message": "Company database not found"}), 404
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            success, error = DocumentFolder.delete_folder(
                session=session,
                folder_id=folder_id,
                force_delete=force_delete
            )

            if not success:
                return jsonify({"success": False, "message": error}), 400

            return jsonify({
                "success": True,
                "message": "Folder deleted successfully"
            })

    except Exception as e:
        app.logger.error(f"Error deleting folder {folder_id}: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500

@folders_api.route('/api/folders/<folder_id>/move', methods=['PUT'])
@token_required
@roles_required('hr', 'admin')
def move_folder(folder_id):
    """Move folder to a new parent."""
    try:
        company_id = g.user.get('company_id')
        if not company_id:
            return jsonify({"success": False, "message": "Company information not found"}), 400

        data = request.get_json()
        new_parent_id = data.get('new_parent_id')

        # Get tenant database connection
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"success": False, "message": "Company database not found"}), 404
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            success, error = DocumentFolder.move_folder(
                session=session,
                folder_id=folder_id,
                new_parent_id=new_parent_id
            )

            if not success:
                return jsonify({"success": False, "message": error}), 400

            return jsonify({
                "success": True,
                "message": "Folder moved successfully"
            })

    except Exception as e:
        app.logger.error(f"Error moving folder {folder_id}: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500

@folders_api.route('/api/folders/<folder_id>/path', methods=['GET'])
@token_required
def get_folder_path(folder_id):
    """Get the full path of a folder."""
    try:
        company_id = g.user.get('company_id')
        if not company_id:
            return jsonify({"success": False, "message": "Company information not found"}), 400

        # Get tenant database connection
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"success": False, "message": "Company database not found"}), 404
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            path = DocumentFolder.get_folder_path(session, folder_id, company_id)

            return jsonify({
                "success": True,
                "path": path
            })

    except Exception as e:
        app.logger.error(f"Error getting folder path for {folder_id}: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500

@folders_api.route('/api/folders/<folder_id>/documents', methods=['GET'])
@token_required
def get_folder_documents(folder_id):
    """Get documents in a specific folder."""
    try:
        company_id = g.user.get('company_id')
        if not company_id:
            return jsonify({"success": False, "message": "Company information not found"}), 400

        include_subfolders = request.args.get('include_subfolders', 'false').lower() == 'true'

        # Get tenant database connection
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"success": False, "message": "Company database not found"}), 404

        from app import db_connection
        with db_connection.get_session(database_name) as session:
            if include_subfolders:
                documents = CompanyDocument.get_documents_by_folder_recursive(
                    session, folder_id
                )
            else:
                documents = CompanyDocument.get_documents_by_folder(
                    session, folder_id
                )

            return jsonify({
                "success": True,
                "documents": [doc.to_dict() for doc in documents],
                "count": len(documents)
            })

    except Exception as e:
        app.logger.error(f"Error getting documents for folder {folder_id}: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500

@folders_api.route('/api/folders/search', methods=['GET'])
@token_required
def search_folders():
    """Search folders by name."""
    try:
        company_id = g.user.get('company_id')
        if not company_id:
            return jsonify({"success": False, "message": "Company information not found"}), 400

        search_term = request.args.get('q')
        include_private = request.args.get('include_private', 'false').lower() == 'true'

        # Get tenant database connection
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"success": False, "message": "Company database not found"}), 404

        from app import db_connection
        with db_connection.get_session(database_name) as session:
            folders = DocumentFolder.search_folders(
                session=session,
                search_term=search_term,
                include_private=include_private
            )

            return jsonify({
                "success": True,
                "folders": [folder.to_dict() for folder in folders],
                "count": len(folders)
            })

    except Exception as e:
        app.logger.error(f"Error searching folders: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500

@folders_api.route('/api/folders/<folder_id>/documents/move', methods=['PUT'])
@token_required
@roles_required('hr', 'admin')
def move_documents_to_folder(folder_id):
    """Move multiple documents to a folder."""
    try:
        company_id = g.user.get('company_id')
        if not company_id:
            return jsonify({"success": False, "message": "Company information not found"}), 400

        data = request.get_json()
        document_ids = data.get('document_ids', [])

        if not document_ids:
            return jsonify({"success": False, "message": "No documents specified"}), 400

        # Get tenant database connection
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"success": False, "message": "Company database not found"}), 404

        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Validate target folder
            folder = DocumentFolder.get_folder_by_id(session, folder_id, company_id)
            if not folder:
                return jsonify({"success": False, "message": "Target folder not found"}), 404

            updated_count, error = CompanyDocument.move_documents_to_folder(
                session=session,
                document_ids=document_ids,
                new_folder_id=folder_id
            )

            if error:
                return jsonify({"success": False, "message": error}), 400

            return jsonify({
                "success": True,
                "message": f"Moved {updated_count} documents to folder",
                "updated_count": updated_count
            })

    except Exception as e:
        app.logger.error(f"Error moving documents to folder {folder_id}: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500

@folders_api.route('/api/folders/bulk-create', methods=['POST'])
@token_required
@roles_required('hr', 'admin')
def bulk_create_folders():
    """Create multiple folders at once."""
    try:
        company_id = g.user.get('company_id')
        if not company_id:
            return jsonify({"success": False, "message": "Company information not found"}), 400

        data = request.get_json()
        folders_data = data.get('folders', [])

        if not folders_data:
            return jsonify({"success": False, "message": "No folders specified"}), 400

        # Get tenant database connection
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"success": False, "message": "Company database not found"}), 404

        created_folders = []
        errors = []

        from app import db_connection
        with db_connection.get_session(database_name) as session:
            for folder_data in folders_data:
                folder, error = DocumentFolder.create_folder(
                    session=session,
                    created_by=g.user.get('user_id'),
                    **folder_data
                )

                if folder:
                    created_folders.append(folder)
                else:
                    errors.append({
                        "folder_name": folder_data.get('folder_name', 'Unknown'),
                        "error": error
                    })

        return jsonify({
            "success": True,
            "message": f"Created {len(created_folders)} folders",
            "created_folders": [folder.to_dict() for folder in created_folders],
            "errors": errors,
            "created_count": len(created_folders),
            "error_count": len(errors)
        })

    except Exception as e:
        app.logger.error(f"Error bulk creating folders: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


