from flask import Blueprint, request, jsonify, current_app as app
import uuid
from application.Models.employees.company_user import CompanyUser
from application.Models.employees.employee import Employee
from application.Models.company import Company
from application.utils.username_encoder import encode_company_id_in_username, extract_company_id_from_username
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.Models.refreshtoken import RefreshToken

company_users_bp = Blueprint('company_users', __name__)

@company_users_bp.route('/api/company-users', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def create_company_user():
    """Create a new company user from an existing employee."""
    data = request.get_json()
    company_id = data.get('company_id')
    employee_id = data.get('employee_id')
    role = data.get('role', 'employee')  # Default to employee role

    # Validate required fields
    if not company_id:
        return jsonify({"success": False, "message": "Company ID is required"}), 400

    if not employee_id:
        return jsonify({"success": False, "message": "Employee ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"success": False, "message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    import secrets
    import string

    with db_connection.get_session(database_name) as session:
        # Get the employee details
        employee = Employee.get_employee_by_id(session, employee_id)
        if not employee:
            return jsonify({"success": False, "message": "Employee not found"}), 404

        # Check if user already exists for this employee
        existing_user = CompanyUser.get_user_by_employee_id(session, employee_id)
        if existing_user:
            return jsonify({
                "success": False,
                "message": f"User already exists for employee {employee.first_name} {employee.last_name}"
            }), 409

        # Generate username from employee names
        # Format: firstname.lastname (lowercase, handle special characters)
        first_name_clean = ''.join(c.lower() for c in employee.first_name if c.isalnum())
        last_name_clean = ''.join(c.lower() for c in employee.last_name if c.isalnum())
        base_username = f"{first_name_clean}.{last_name_clean}"

        # Ensure username is unique by adding number if needed
        username = base_username
        counter = 1
        while CompanyUser.get_user_by_username(session, username):
            username = f"{base_username}{counter}"
            counter += 1

        # Generate secure random password (12 characters)
        password_chars = string.ascii_letters + string.digits + "!@#$%^&*"
        auto_password = ''.join(secrets.choice(password_chars) for _ in range(12))

        # Create the user
        user, error = CompanyUser.create_user(
            session,
            username=username,
            password=auto_password,
            role=role,
            last_name=employee.last_name,
            first_name=employee.first_name,
            email=employee.email,  # Get from employee record
            phone_number=employee.phone_number,  # Get from employee record
            employee_id=employee_id,
            is_active=data.get('is_active', True)
        )

        if not user:
            return jsonify({"success": False, "message": error}), 400

        # Generate encoded username for login
        encoded_username = encode_company_id_in_username(user.username, company_id)

        return jsonify({
            "success": True,
            "message": "User created successfully",
            "user": user.to_dict(),
            "login_username": encoded_username,
            "generated_password": auto_password,  # Return the generated password
            "employee_info": {
                "employee_id": str(employee.employee_id),
                "name": f"{employee.first_name} {employee.last_name}",
                "department": employee.department.name if employee.department else None
            }
        }), 200

@company_users_bp.route('/api/company-users/bulk', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def create_bulk_company_users():
    """Create company users for multiple employees at once."""
    data = request.get_json()
    company_id = data.get('company_id')
    employee_ids = data.get('employee_ids', [])
    default_role = data.get('default_role', 'employee')

    # Validate required fields
    if not company_id:
        return jsonify({"success": False, "message": "Company ID is required"}), 400

    if not employee_ids or not isinstance(employee_ids, list):
        return jsonify({"success": False, "message": "Employee IDs list is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"success": False, "message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    import secrets
    import string

    results = {
        "success": True,
        "created_users": [],
        "skipped_users": [],
        "failed_users": [],
        "summary": {
            "total_requested": len(employee_ids),
            "created": 0,
            "skipped": 0,
            "failed": 0
        }
    }

    with db_connection.get_session(database_name) as session:
        for employee_id in employee_ids:
            try:
                # Get the employee details
                employee = Employee.get_employee_by_id(session, employee_id)
                if not employee:
                    results["failed_users"].append({
                        "employee_id": employee_id,
                        "reason": "Employee not found"
                    })
                    results["summary"]["failed"] += 1
                    continue

                # Check if user already exists for this employee
                existing_user = CompanyUser.get_user_by_employee_id(session, employee_id)
                if existing_user:
                    results["skipped_users"].append({
                        "employee_id": employee_id,
                        "employee_name": f"{employee.first_name} {employee.last_name}",
                        "reason": "User already exists",
                        "existing_username": existing_user.username
                    })
                    results["summary"]["skipped"] += 1
                    continue

                # Generate username from employee names
                first_name_clean = ''.join(c.lower() for c in employee.first_name if c.isalnum())
                last_name_clean = ''.join(c.lower() for c in employee.last_name if c.isalnum())
                base_username = f"{first_name_clean}.{last_name_clean}"

                # Ensure username is unique
                username = base_username
                counter = 1
                while CompanyUser.get_user_by_username(session, username):
                    username = f"{base_username}{counter}"
                    counter += 1

                # Generate secure random password
                password_chars = string.ascii_letters + string.digits + "!@#$%^&*"
                auto_password = ''.join(secrets.choice(password_chars) for _ in range(12))

                # Create the user
                user, error = CompanyUser.create_user(
                    session,
                    username=username,
                    password=auto_password,
                    role=default_role,
                    last_name=employee.last_name,
                    first_name=employee.first_name,
                    email=employee.email,
                    phone_number=employee.phone_number,
                    employee_id=employee_id,
                    is_active=True
                )

                if not user:
                    results["failed_users"].append({
                        "employee_id": employee_id,
                        "employee_name": f"{employee.first_name} {employee.last_name}",
                        "reason": error
                    })
                    results["summary"]["failed"] += 1
                    continue

                # Generate encoded username for login
                encoded_username = encode_company_id_in_username(user.username, company_id)

                results["created_users"].append({
                    "employee_id": employee_id,
                    "employee_name": f"{employee.first_name} {employee.last_name}",
                    "username": username,
                    "login_username": encoded_username,
                    "generated_password": auto_password,
                    "role": default_role,
                    "department": employee.department.name if employee.department else None
                })
                results["summary"]["created"] += 1

            except Exception as e:
                app.logger.error(f"Error creating user for employee {employee_id}: {str(e)}")
                results["failed_users"].append({
                    "employee_id": employee_id,
                    "reason": f"Unexpected error: {str(e)}"
                })
                results["summary"]["failed"] += 1

    return jsonify(results), 200

@company_users_bp.route('/api/company-users/create-all', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def create_users_for_all_employees():
    """Create company users for all employees who don't have user accounts yet."""
    data = request.get_json()
    company_id = data.get('company_id')
    default_role = data.get('default_role', 'employee')
    include_inactive = data.get('include_inactive', False)

    # Validate required fields
    if not company_id:
        return jsonify({"success": False, "message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"success": False, "message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    import secrets
    import string

    results = {
        "success": True,
        "created_users": [],
        "skipped_users": [],
        "failed_users": [],
        "summary": {
            "total_employees": 0,
            "created": 0,
            "skipped": 0,
            "failed": 0
        }
    }

    with db_connection.get_session(database_name) as session:
        # Get all employees without user accounts
        if include_inactive:
            employees = session.query(Employee).all()
        else:
            employees = session.query(Employee).filter(Employee.status == 'active').all()

        results["summary"]["total_employees"] = len(employees)

        for employee in employees:
            try:
                # Check if user already exists for this employee
                existing_user = CompanyUser.get_user_by_employee_id(session, employee.employee_id)
                if existing_user:
                    results["skipped_users"].append({
                        "employee_id": str(employee.employee_id),
                        "employee_name": f"{employee.first_name} {employee.last_name}",
                        "reason": "User already exists",
                        "existing_username": existing_user.username
                    })
                    results["summary"]["skipped"] += 1
                    continue

                # Generate username from employee names
                first_name_clean = ''.join(c.lower() for c in employee.first_name if c.isalnum())
                last_name_clean = ''.join(c.lower() for c in employee.last_name if c.isalnum())
                base_username = f"{first_name_clean}.{last_name_clean}"

                # Ensure username is unique
                username = base_username
                counter = 1
                while CompanyUser.get_user_by_username(session, username):
                    username = f"{base_username}{counter}"
                    counter += 1

                # Generate secure random password
                password_chars = string.ascii_letters + string.digits + "!@#$%^&*"
                auto_password = ''.join(secrets.choice(password_chars) for _ in range(12))

                # Create the user
                user, error = CompanyUser.create_user(
                    session,
                    username=username,
                    password=auto_password,
                    role=default_role,
                    last_name=employee.last_name,
                    first_name=employee.first_name,
                    email=employee.email,
                    phone_number=employee.phone_number,
                    employee_id=employee.employee_id,
                    is_active=True
                )

                if not user:
                    results["failed_users"].append({
                        "employee_id": str(employee.employee_id),
                        "employee_name": f"{employee.first_name} {employee.last_name}",
                        "reason": error
                    })
                    results["summary"]["failed"] += 1
                    continue

                # Generate encoded username for login
                encoded_username = encode_company_id_in_username(user.username, company_id)

                results["created_users"].append({
                    "employee_id": str(employee.employee_id),
                    "employee_name": f"{employee.first_name} {employee.last_name}",
                    "username": username,
                    "login_username": encoded_username,
                    "generated_password": auto_password,
                    "role": default_role,
                    "department": employee.department.name if employee.department else None
                })
                results["summary"]["created"] += 1

            except Exception as e:
                app.logger.error(f"Error creating user for employee {employee.employee_id}: {str(e)}")
                results["failed_users"].append({
                    "employee_id": str(employee.employee_id),
                    "employee_name": f"{employee.first_name} {employee.last_name}" if employee else "Unknown",
                    "reason": f"Unexpected error: {str(e)}"
                })
                results["summary"]["failed"] += 1

    return jsonify(results), 200

@company_users_bp.route('/api/company-users', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_company_users():
    """Get all company users with pagination."""
    company_id = request.args.get('company_id')
    page = int(request.args.get('page', 1))
    per_page = int(request.args.get('per_page', 10))

    if not company_id:
        return jsonify({"success": False, "message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"success": False, "message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Get users with pagination
        users, total = CompanyUser.get_all_users(session, page, per_page)

        # Convert to dict and add encoded usernames
        users_data = []
        for user in users:
            user_dict = user.to_dict()
            user_dict['login_username'] = encode_company_id_in_username(user.username, company_id)
            users_data.append(user_dict)

        # Prepare pagination info
        pagination = {
            "total": total,
            "page": page,
            "per_page": per_page,
            "pages": (total + per_page - 1) // per_page
        }

        return jsonify({
            "success": True,
            "users": users_data,
            "pagination": pagination
        }), 200

@company_users_bp.route('/api/company-users/<user_id>', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_company_user(user_id):
    """Get a specific company user by ID."""
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"success": False, "message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"success": False, "message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Get the user
        user = CompanyUser.get_user_by_id(session, user_id)
        if not user:
            return jsonify({"success": False, "message": "User not found"}), 404

        # Add encoded username for login
        user_dict = user.to_dict()
        user_dict['login_username'] = encode_company_id_in_username(user.username, company_id)

        return jsonify({
            "success": True,
            "user": user_dict
        }), 200

@company_users_bp.route('/api/company-users/employee/<employee_id>', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_user_by_employee_id(employee_id):
    """Get a company user by employee ID."""
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"success": False, "message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"success": False, "message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Get the user by employee ID
        user = CompanyUser.get_user_by_employee_id(session, employee_id)
        if not user:
            return jsonify({"success": False, "message": "No user found for this employee"}), 404

        # Add encoded username for login
        user_dict = user.to_dict()
        user_dict['login_username'] = encode_company_id_in_username(user.username, company_id)

        return jsonify({
            "success": True,
            "user": user_dict
        }), 200

@company_users_bp.route('/api/company-users/<user_id>', methods=['PATCH'])
@token_required
@roles_required('admin', 'hr')
def update_company_user(user_id):
    """Update a company user."""
    data = request.get_json()
    company_id = data.get('company_id')

    if not company_id:
        return jsonify({"success": False, "message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"success": False, "message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Prepare update data
        update_data = {}
        for field in ['username', 'first_name', 'last_name', 'email', 'phone_number', 'role', 'employee_id', 'is_active']:
            if field in data:
                update_data[field] = data[field]

        # Handle password update separately
        if 'password' in data:
            update_data['password'] = data['password']

        # Update the user
        success, error = CompanyUser.update_user(session, user_id, **update_data)
        if not success:
            return jsonify({"success": False, "message": error}), 400

        # Get updated user
        user = CompanyUser.get_user_by_id(session, user_id)

        # Add encoded username for login
        user_dict = user.to_dict()
        user_dict['login_username'] = encode_company_id_in_username(user.username, company_id)

        return jsonify({
            "success": True,
            "message": "User updated successfully",
            "user": user_dict
        }), 200

@company_users_bp.route('/api/company-users/<user_id>', methods=['DELETE'])
@token_required
@roles_required('admin', 'hr')
def delete_company_user(user_id):
    """Delete a company user."""
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"success": False, "message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"success": False, "message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Delete the user
        success, error = CompanyUser.delete_user(session, user_id)
        if not success:
            return jsonify({"success": False, "message": error}), 400

        return jsonify({
            "success": True,
            "message": "User deleted successfully"
        }), 200

@company_users_bp.route('/api/company-users/login', methods=['POST'])
def login_company_user():
    """Login a company user with encoded username."""
    data = request.get_json()
    encoded_username = data.get('username')
    password = data.get('password')

    if not encoded_username or not password:
        return jsonify({"success": False, "message": "Username and password are required"}), 400

    # Extract company ID from encoded username
    company_id, original_username = extract_company_id_from_username(encoded_username)
    if not company_id:
        return jsonify({"success": False, "message": "Invalid username format"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"success": False, "message": "Company not found"}), 404

    # Get company info
    company = Company.query.filter_by(company_id=company_id).first()

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Get the user
        user = CompanyUser.get_user_by_username(session, original_username)
        if not user:
            return jsonify({"success": False, "message": "Invalid credentials"}), 401

        # Verify password
        if not CompanyUser.verify_password(user, password):
            return jsonify({"success": False, "message": "Invalid credentials"}), 401

        # Check if user is active
        if not user.is_active:
            return jsonify({"success": False, "message": "Account is inactive"}), 401

        # Generate token with company context
        token_payload = {
            'user_id': str(user.id),
            'username': user.username,
            'role': user.role,
            'company_id': company_id,
            'company_db': database_name
        }

        access_token = RefreshToken.generate_custom_token(token_payload)
        refresh_token = RefreshToken.generate_custom_refresh_token(token_payload)

        return jsonify({
            "success": True,
            "message": "Login successful",
            "access_token": access_token,
            "refresh_token": refresh_token,
            "user": {
                "id": str(user.id),
                "username": user.username,
                "name": f"{user.first_name} {user.last_name}" if user.first_name else user.last_name,
                "role": user.role,
                "email": user.email
            },
            "company": {
                "id": company.company_id,
                "name": company.company_name
            }
        }), 200

@company_users_bp.route('/api/company-users/link-employee', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def link_employee_to_user():
    """Link an employee to a user account."""
    data = request.get_json()
    user_id = data.get('user_id')
    employee_id = data.get('employee_id')
    company_id = data.get('company_id')

    if not all([user_id, employee_id, company_id]):
        return jsonify({"success": False, "message": "User ID, employee ID, and company ID are required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"success": False, "message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Verify employee exists
        employee = session.query(Employee).filter_by(id=uuid.UUID(employee_id)).first()
        if not employee:
            return jsonify({"success": False, "message": "Employee not found"}), 404

        # Update the user with the employee ID
        success, error = CompanyUser.update_user(session, user_id, employee_id=employee_id)
        if not success:
            return jsonify({"success": False, "message": error}), 400

        # Get the updated user
        user = CompanyUser.get_user_by_id(session, user_id)

        return jsonify({
            "success": True,
            "message": "Employee linked to user successfully",
            "user": user.to_dict()
        }), 200

@company_users_bp.route('/api/company-users/reset-password', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def reset_user_password():
    """Reset a user's password (admin function)."""
    data = request.get_json()
    user_id = data.get('user_id')
    new_password = data.get('new_password')
    company_id = data.get('company_id')

    if not all([user_id, new_password, company_id]):
        return jsonify({"success": False, "message": "User ID, new password, and company ID are required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"success": False, "message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Update the password
        success, error = CompanyUser.update_user(session, user_id, password=new_password)
        if not success:
            return jsonify({"success": False, "message": error}), 400

        # Get the user for the response
        user = CompanyUser.get_user_by_id(session, user_id)

        # Generate encoded username for login
        encoded_username = encode_company_id_in_username(user.username, company_id)

        return jsonify({
            "success": True,
            "message": "Password reset successfully",
            "login_username": encoded_username
        }), 200
