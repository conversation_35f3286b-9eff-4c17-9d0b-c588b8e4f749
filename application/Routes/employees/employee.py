from flask import request, jsonify, Blueprint, current_app as app, render_template, redirect, url_for
import uuid
from application.Models.Person import Person
from application.Models.company import CompanyDevice
from application.Models.EnrollInfo import EnrollInfo
import jsons
from application.Models.Msg import Msg


employee = Blueprint('employee', __name__)

@employee.route('/register_employee', methods=['POST', 'GET'])
def register_employee():
    """Register a new employee."""
    from app import personService
    if request.method == 'POST':
        # Get JSON data from request
        data = request.form

        if not data:
            return jsonify({"message": "No data provided"}), 400

        # Extract fields from JSON
        first_name = data.get("first_name")
        last_name = data.get("last_name")
        name = f"{first_name} {last_name}"
        roll_id = data.get("roll_id", 0)  # Default to 0 if not provided
        
        employee_id = str(uuid.uuid4())

        if not name:
            return jsonify({"message": "Name is required"}), 400
        
        database_name = "bank_of_kigali_b16c4ca3_db"

        #connect to the database
        db_connection = app.db_connection
        with db_connection.get_session(database_name) as session:
           
            # construct a person dictionary
            person = {
                "name": name,
                "roll_id": roll_id,
                "employee_id": employee_id
            }
            # insert the person
            new_person = Person.add_employee(session, **person)
            # check if the person was inserted
            if new_person:
                # get the id of the person
                person_id = new_person.id
                app.logger.info(f"Inserted new employee with name '{new_person.name}'.")
                backupNum = -1
                #get company devices
                company_id = "10bd5d3a-1b7b-41ba-8cb1-63e5b31876f1"
                devices = CompanyDevice.get_company_devices(company_id)
                if devices is not None:
                    for device in devices:
                        if person_id is None or backupNum is None or device is None:
                            app.logger.warning("Missing required parameters.")
                            return jsonify({"message": "Missing required parameters"}), 400                
                       
                        # send the person to the device
                        device_sn = device.device_sn
                        try:
                            selected = Person.select_person_by_their_id(session, person_id)
                            app.logger.info(f"Selected person: {selected}")
                            enroll_info = EnrollInfo.selectbybackupnum(session, selected.id, backupNum)
                            app.logger.info(f"Selected enroll info: {enroll_info}")
                            if enroll_info is not None:
                                app.logger.info(f"Sending user {person_id} to device {device_sn}")
                                sent = personService.set_user_to_device(
                                    session, selected.id, selected.name, backupNum, selected.roll_id, enroll_info.signatures, device_sn
                                )
                                app.logger.info(f"information sent: {sent}")
                                if sent:
                                    return jsons.dump(Msg.success())
                                else:
                                    return jsons.dump(Msg.fail())
                            elif backupNum == -1:
                                app.logger.info(f"Sending user {person_id} to device {device_sn} with backupnum -1")
                                personService.set_user_to_device(
                                    session, person_id, name, backupNum, 0, "", device_sn
                                )
                                return jsons.dump(Msg.success())
                            else:
                                app.logger.error(f"Enroll info not found for enroll_id={person_id}, backupnum={backupNum}")
                                return jsons.dump(Msg.fail())
                        except Exception as e:
                            app.logger.error(f"Failed to send user to device {device_sn}: {str(e)}")
                            return jsons.dump(Msg.fail())
                else:
                    app.logger.error(f"No devices found for company ID {company_id}")
                    return jsons.dump(Msg.fail())
            else:
                app.logger.error(f"Failed to insert new employee: {new_person}")
                return jsons.dump(Msg.fail())
    else:
        return render_template('employees/register_employee.html')

        
