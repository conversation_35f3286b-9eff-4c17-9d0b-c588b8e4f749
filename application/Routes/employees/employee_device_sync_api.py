"""
HR-accessible API endpoints for managing employee/customer device synchronization.

These endpoints allow HR users to view sync status and retry failed device synchronizations
for their own company's employees and customers without requiring super-admin privileges.
"""

from flask import Blueprint, request, jsonify, current_app as app, g
from datetime import datetime
import json
import uuid

from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.Models.MachineCommand import MachineCommand
from application.Models.Person import Person
from application.Models.employees.employee import Employee
from application.Models.customers.customer import Customer
from application.Models.company import Company
from application.web_socket.WebSocketPool import ws_device

employee_device_sync_api = Blueprint('employee_device_sync_api', __name__)


def get_user_context():
    """Helper function to extract user and company context.
    
    This function handles both company users and central users:
    - Company users: company_id is extracted from JWT token automatically
    - Central users (HR/Admin): company_id must be provided in request
    
    Returns:
        tuple: (company_id, user_id, error_message)
    """
    user_info = getattr(g, 'user', None)
    if not user_info:
        return None, None, "Authentication required"

    user_id = user_info.get('user_id')
    if not user_id:
        return None, None, "User context required"

    # For company users, company_id is in the token
    company_id = user_info.get('company_id')
    if company_id:
        app.logger.info(f"[HR_SYNC_API] Company user - using company_id from token: {company_id}")
        return company_id, user_id, None

    # For central users (HR/admin), get company_id from request
    company_id = request.args.get('company_id') or (request.json.get('company_id') if request.json else None)
    if not company_id:
        return None, None, "Company ID is required for central users"

    app.logger.info(f"[HR_SYNC_API] Central user - using company_id from request: {company_id}")
    return company_id, user_id, None


def get_person_info(session, person_type, person_id):
    """Get person information and validate they belong to the company.
    
    Args:
        session: Database session
        person_type: 'employee' or 'customer'
        person_id: Employee ID or Customer ID
        
    Returns:
        tuple: (person_record, enroll_id, person_name, error_message)
    """
    try:
        if person_type == 'employee':
            # Find employee by employee_id
            employee = session.query(Employee).filter_by(employee_id=person_id).first()
            if not employee:
                return None, None, None, f"Employee {person_id} not found"
            
            # Find corresponding Person record
            person = session.query(Person).filter_by(employee_id=person_id).first()
            if not person:
                return None, None, None, f"Person record not found for employee {person_id}"
            
            return person, person.id, f"{employee.first_name} {employee.last_name}", None
            
        elif person_type == 'customer':
            # Convert string UUID to UUID object for query
            try:
                customer_uuid = uuid.UUID(person_id)
            except ValueError:
                return None, None, None, f"Invalid customer ID format: {person_id}"
            
            # Find customer by customer_id
            customer = session.query(Customer).filter_by(customer_id=customer_uuid).first()
            if not customer:
                return None, None, None, f"Customer {person_id} not found"
            
            # Find corresponding Person record
            person = session.query(Person).filter_by(customer_id=customer_uuid).first()
            if not person:
                return None, None, None, f"Person record not found for customer {person_id}"
            
            return person, person.id, f"{customer.first_name} {customer.last_name}", None
            
        else:
            return None, None, None, f"Invalid person_type: {person_type}. Must be 'employee' or 'customer'"
            
    except Exception as e:
        app.logger.error(f"[HR_SYNC_API] Error getting person info: {e}")
        return None, None, None, f"Error retrieving person information: {str(e)}"


def parse_command_content(content):
    """Parse machine command content to extract biometric information.
    
    Args:
        content: JSON string from machine command
        
    Returns:
        dict: Parsed information including enrollid, name, backupnum, etc.
    """
    try:
        content_json = json.loads(content)
        return {
            'enrollid': content_json.get('enrollid'),
            'name': content_json.get('name'),
            'backupnum': content_json.get('backupnum'),
            'admin': content_json.get('admin'),
            'cmd': content_json.get('cmd')
        }
    except (json.JSONDecodeError, TypeError):
        return {}


def get_biometric_type_name(backupnum):
    """Convert backupnum to human-readable biometric type.
    
    Args:
        backupnum: Backup number from device protocol
        
    Returns:
        str: Human-readable biometric type
    """
    if backupnum is None:
        return "unknown"
    elif 0 <= backupnum <= 9:
        return "fingerprint"
    elif backupnum == 10:
        return "password"
    elif backupnum == 11:
        return "rfid_card"
    elif 20 <= backupnum <= 27:
        return "face_static"
    elif backupnum == 50:
        return "face_dynamic"
    else:
        return f"unknown_type_{backupnum}"


@employee_device_sync_api.route('/api/hr/sync-status/<person_type>/<person_id>', methods=['GET'])
@token_required
@roles_required('hr', 'admin', 'super-admin')
def get_person_sync_status(person_type, person_id):
    """
    Get device synchronization status for a specific employee or customer.
    
    Path Parameters:
    - person_type: 'employee' or 'customer'
    - person_id: Employee ID (string) or Customer ID (UUID)
    
    Returns:
    - Comprehensive sync status for all devices
    """
    # Get user context and company_id
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401

    # Get database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company {company_id} not found"}), 404

    app.logger.info(f"[HR_SYNC_API] User {user_id} checking sync status for {person_type} {person_id} in company {company_id}")

    from app import db_connection

    try:
        with db_connection.get_session(database_name) as session:
            # Get person information and validate they belong to this company
            person, enroll_id, person_name, error_msg = get_person_info(session, person_type, person_id)
            if error_msg:
                return jsonify({"message": error_msg}), 404

            # Get all machine commands for this person (filter by enrollid in content)
            commands = session.query(MachineCommand).filter(
                MachineCommand.content.like(f'%"enrollid":{enroll_id}%')
            ).all()

            if not commands:
                return jsonify({
                    "person_type": person_type,
                    "person_id": person_id,
                    "person_name": person_name,
                    "enroll_id": enroll_id,
                    "sync_summary": {
                        "total_devices": 0,
                        "synced_devices": 0,
                        "failed_devices": 0,
                        "pending_devices": 0
                    },
                    "device_sync_details": [],
                    "message": "No device synchronization commands found for this person"
                }), 200

            # Group commands by device
            device_commands = {}
            for cmd in commands:
                device_sn = cmd.serial
                if device_sn not in device_commands:
                    device_commands[device_sn] = []
                device_commands[device_sn].append(cmd)

            # Analyze sync status for each device
            device_sync_details = []
            total_devices = len(device_commands)
            synced_devices = 0
            failed_devices = 0
            pending_devices = 0

            for device_sn, device_cmds in device_commands.items():
                # Check device connectivity
                device_connected = ws_device.get(device_sn) is not None

                # Analyze commands for this device
                command_details = []
                device_has_completed = False
                device_has_failed = False
                device_has_pending = False

                for cmd in device_cmds:
                    content_info = parse_command_content(cmd.content)
                    biometric_type = get_biometric_type_name(content_info.get('backupnum'))

                    # Determine command status
                    if cmd.status == 1:
                        status = "completed"
                        device_has_completed = True
                    elif cmd.err_count >= 15:
                        status = "failed"
                        device_has_failed = True
                    else:
                        status = "pending"
                        device_has_pending = True

                    command_details.append({
                        "command_id": cmd.id,
                        "command_type": cmd.name,
                        "biometric_type": biometric_type,
                        "backupnum": content_info.get('backupnum'),
                        "status": status,
                        "err_count": cmd.err_count,
                        "last_attempt": cmd.run_time.isoformat() if cmd.run_time else None,
                        "can_retry": cmd.err_count >= 15 and cmd.status == 0,
                        "created": cmd.gmt_crate.isoformat() if cmd.gmt_crate else None,
                        "modified": cmd.gmt_modified.isoformat() if cmd.gmt_modified else None
                    })

                # Determine overall device status
                if device_has_failed:
                    failed_devices += 1
                elif device_has_pending:
                    pending_devices += 1
                elif device_has_completed:
                    synced_devices += 1

                device_sync_details.append({
                    "device_sn": device_sn,
                    "device_connected": device_connected,
                    "commands": command_details
                })

            return jsonify({
                "person_type": person_type,
                "person_id": person_id,
                "person_name": person_name,
                "enroll_id": enroll_id,
                "sync_summary": {
                    "total_devices": total_devices,
                    "synced_devices": synced_devices,
                    "failed_devices": failed_devices,
                    "pending_devices": pending_devices
                },
                "device_sync_details": device_sync_details
            }), 200

    except Exception as e:
        app.logger.error(f"[HR_SYNC_API] Error getting sync status for {person_type} {person_id}: {e}")
        return jsonify({"message": "Internal server error", "error": str(e)}), 500


@employee_device_sync_api.route('/api/hr/sync-failures', methods=['GET'])
@token_required
@roles_required('hr', 'admin', 'super-admin')
def get_company_sync_failures():
    """
    Get all failed device synchronizations for the HR user's company.

    Query Parameters:
    - person_type: Filter by 'employee' or 'customer' (optional)
    - device_sn: Filter by specific device serial number (optional)

    Returns:
    - List of all failed syncs grouped by person and device
    """
    # Get user context and company_id
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401

    # Get optional filters
    person_type_filter = request.args.get('person_type')
    device_sn_filter = request.args.get('device_sn')

    # Validate person_type filter
    if person_type_filter and person_type_filter not in ['employee', 'customer']:
        return jsonify({"message": "person_type must be 'employee' or 'customer'"}), 400

    # Get database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company {company_id} not found"}), 404

    app.logger.info(f"[HR_SYNC_API] User {user_id} viewing sync failures for company {company_id}")

    from app import db_connection

    try:
        with db_connection.get_session(database_name) as session:
            # Query all failed commands (err_count >= 15 and status = 0)
            query = session.query(MachineCommand).filter(
                MachineCommand.err_count >= 15,
                MachineCommand.status == 0
            )

            # Apply device filter if provided
            if device_sn_filter:
                query = query.filter(MachineCommand.serial == device_sn_filter)

            failed_commands = query.all()

            if not failed_commands:
                return jsonify({
                    "company_id": company_id,
                    "total_failed_syncs": 0,
                    "failed_by_type": {
                        "employees": 0,
                        "customers": 0
                    },
                    "failures": [],
                    "message": "No failed synchronizations found"
                }), 200

            # Group failures by person
            person_failures = {}

            for cmd in failed_commands:
                content_info = parse_command_content(cmd.content)
                enrollid = content_info.get('enrollid')

                if not enrollid:
                    continue  # Skip commands without enrollid

                # Find the person record
                person = session.query(Person).filter_by(id=enrollid).first()
                if not person:
                    continue  # Skip if person not found

                # Determine person type and get details
                if person.employee_id:
                    current_person_type = 'employee'
                    person_id = person.employee_id

                    # Get employee name
                    employee = session.query(Employee).filter_by(employee_id=person.employee_id).first()
                    person_name = f"{employee.first_name} {employee.last_name}" if employee else "Unknown Employee"

                elif person.customer_id:
                    current_person_type = 'customer'
                    person_id = str(person.customer_id)

                    # Get customer name
                    customer = session.query(Customer).filter_by(customer_id=person.customer_id).first()
                    person_name = f"{customer.first_name} {customer.last_name}" if customer else "Unknown Customer"

                else:
                    continue  # Skip persons without employee_id or customer_id

                # Apply person_type filter
                if person_type_filter and current_person_type != person_type_filter:
                    continue

                # Create unique key for person-device combination
                key = f"{current_person_type}_{person_id}_{cmd.serial}"

                if key not in person_failures:
                    person_failures[key] = {
                        "person_type": current_person_type,
                        "person_id": person_id,
                        "person_name": person_name,
                        "enroll_id": enrollid,
                        "device_sn": cmd.serial,
                        "failed_commands": 0,
                        "oldest_failure": None,
                        "biometric_types_failed": set(),
                        "command_details": []
                    }

                # Update failure info
                failure_info = person_failures[key]
                failure_info["failed_commands"] += 1

                # Track oldest failure
                if failure_info["oldest_failure"] is None or cmd.run_time < failure_info["oldest_failure"]:
                    failure_info["oldest_failure"] = cmd.run_time

                # Track biometric types
                biometric_type = get_biometric_type_name(content_info.get('backupnum'))
                failure_info["biometric_types_failed"].add(biometric_type)

                # Add command details
                failure_info["command_details"].append({
                    "command_id": cmd.id,
                    "command_type": cmd.name,
                    "biometric_type": biometric_type,
                    "err_count": cmd.err_count,
                    "last_attempt": cmd.run_time.isoformat() if cmd.run_time else None
                })

            # Convert to list and format response
            failures = []
            employee_count = 0
            customer_count = 0

            for failure_info in person_failures.values():
                # Convert set to list for JSON serialization
                failure_info["biometric_types_failed"] = list(failure_info["biometric_types_failed"])

                # Format oldest failure time
                if failure_info["oldest_failure"]:
                    failure_info["oldest_failure"] = failure_info["oldest_failure"].isoformat()

                # Remove command_details from response (too verbose for summary)
                del failure_info["command_details"]

                failures.append(failure_info)

                # Count by type
                if failure_info["person_type"] == "employee":
                    employee_count += 1
                else:
                    customer_count += 1

            return jsonify({
                "company_id": company_id,
                "total_failed_syncs": len(failures),
                "failed_by_type": {
                    "employees": employee_count,
                    "customers": customer_count
                },
                "failures": failures
            }), 200

    except Exception as e:
        app.logger.error(f"[HR_SYNC_API] Error getting sync failures for company {company_id}: {e}")
        return jsonify({"message": "Internal server error", "error": str(e)}), 500


@employee_device_sync_api.route('/api/hr/retry-sync', methods=['POST'])
@token_required
@roles_required('hr', 'admin', 'super-admin')
def retry_person_sync():
    """
    Retry failed device synchronization for a specific employee or customer.

    Request Body:
    {
        "person_type": "employee",  // Required: 'employee' or 'customer'
        "person_id": "EMP001",      // Required: Employee ID or Customer ID
        "device_sn": "AYTI10087992", // Optional: specific device (if omitted, retry on all devices)
        "command_ids": [456, 789]   // Optional: specific commands (if omitted, retry all failed commands)
    }

    Returns:
    - Summary of commands reset and retry status
    """
    data = request.get_json()

    if not data:
        return jsonify({"message": "Request body is required"}), 400

    # Validate required fields
    person_type = data.get('person_type')
    person_id = data.get('person_id')

    if not person_type or not person_id:
        return jsonify({"message": "person_type and person_id are required"}), 400

    if person_type not in ['employee', 'customer']:
        return jsonify({"message": "person_type must be 'employee' or 'customer'"}), 400

    # Optional filters
    device_sn_filter = data.get('device_sn')
    command_ids_filter = data.get('command_ids', [])

    # Get user context and company_id
    company_id, user_id, error_msg = get_user_context()
    if error_msg:
        return jsonify({"message": error_msg}), 401

    # Get database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company {company_id} not found"}), 404

    app.logger.info(f"[HR_SYNC_API] User {user_id} retrying sync for {person_type} {person_id} in company {company_id}")

    from app import db_connection

    try:
        with db_connection.get_session(database_name) as session:
            # Get person information and validate they belong to this company
            person, enroll_id, person_name, error_msg = get_person_info(session, person_type, person_id)
            if error_msg:
                return jsonify({"message": error_msg}), 404

            # Find all failed commands for this person
            query = session.query(MachineCommand).filter(
                MachineCommand.content.like(f'%"enrollid":{enroll_id}%'),
                MachineCommand.err_count >= 15,
                MachineCommand.status == 0
            )

            # Apply device filter if provided
            if device_sn_filter:
                query = query.filter(MachineCommand.serial == device_sn_filter)

            # Apply command IDs filter if provided
            if command_ids_filter:
                query = query.filter(MachineCommand.id.in_(command_ids_filter))

            failed_commands = query.all()

            if not failed_commands:
                return jsonify({
                    "status": "success",
                    "message": f"No failed sync commands found for {person_type} {person_id}",
                    "person_type": person_type,
                    "person_id": person_id,
                    "person_name": person_name,
                    "commands_reset": 0,
                    "devices_affected": [],
                    "reset_details": [],
                    "warnings": []
                }), 200

            # Reset all failed commands
            reset_details = []
            devices_affected = set()
            warnings = []

            for cmd in failed_commands:
                old_err_count = cmd.err_count
                device_sn = cmd.serial

                # Check device connectivity
                device_connected = ws_device.get(device_sn) is not None
                if not device_connected:
                    warnings.append(f"Device {device_sn} is currently offline - command will be sent when device reconnects")

                # Reset the command
                cmd.status = 0
                cmd.send_status = 0
                cmd.err_count = 0
                cmd.run_time = datetime.now()
                cmd.gmt_modified = datetime.now()

                devices_affected.add(device_sn)

                # Parse command content for details
                content_info = parse_command_content(cmd.content)
                biometric_type = get_biometric_type_name(content_info.get('backupnum'))

                reset_details.append({
                    "command_id": cmd.id,
                    "device_sn": device_sn,
                    "command_type": cmd.name,
                    "biometric_type": biometric_type,
                    "previous_err_count": old_err_count,
                    "device_connected": device_connected
                })

            # Commit all changes
            session.commit()

            # Log the retry action for audit purposes
            app.logger.info(f"[HR_SYNC_API] User {user_id} reset {len(failed_commands)} failed commands for {person_type} {person_id}")

            return jsonify({
                "status": "success",
                "message": f"Reset {len(failed_commands)} failed sync command(s) for {person_type} {person_id}",
                "person_type": person_type,
                "person_id": person_id,
                "person_name": person_name,
                "commands_reset": len(failed_commands),
                "devices_affected": list(devices_affected),
                "reset_details": reset_details,
                "warnings": warnings
            }), 200

    except Exception as e:
        app.logger.error(f"[HR_SYNC_API] Error retrying sync for {person_type} {person_id}: {e}")
        return jsonify({"message": "Internal server error", "error": str(e)}), 500
