from flask import Blueprint, request, jsonify, current_app as app
import uuid
from application.Models.employees import Employee, Department, Attendance
from application.Models.company import Company
from application.Models.Msg import Msg
import jsons
from datetime import datetime, date
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.Helpers.helper_methods import HelperMethods
from datetime import timedelta


employees_api = Blueprint('employees_api', __name__)


# Employee CRUD operations
@employees_api.route('/api/employees', methods=['GET'])
@token_required
def get_employees():
    """Get employees for a company with pagination and filtering."""
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Pagination parameters
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)

    # Optional filtering parameters
    department_id = request.args.get('department_id')
    status = request.args.get('status')
    search_term = request.args.get('search')

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Get total count and paginated employees
        total_count, employees = Employee.get_paginated_employees(
            session,
            page=page,
            per_page=per_page,
            department_id=department_id,
            status=status,
            search_term=search_term
        )

        employees_data = [employee.to_dict() for employee in employees]

        # Prepare pagination metadata
        pagination = {
            "total_count": total_count,
            "page": page,
            "per_page": per_page,
            "pages": (total_count + per_page - 1) // per_page if per_page > 0 else 0,  # Ceiling division
            "has_next": page < ((total_count + per_page - 1) // per_page) if per_page > 0 else False,
            "has_prev": page > 1
        }

        return jsons.dump(Msg.success()
                         .add("employees", employees_data)
                         .add("pagination", pagination))

@employees_api.route('/api/employees/<employee_id>', methods=['GET'])
@token_required
def get_employee(employee_id):
    """Get a specific employee by ID."""
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        employee = Employee.get_employee_by_id(session, employee_id)

        if not employee:
            return jsonify({"message": f"Employee with ID {employee_id} not found"}), 404

        return jsons.dump(Msg.success().add("employee", employee.to_dict()))

@employees_api.route('/api/employees', methods=['POST'])
@token_required
@roles_required('hr')
def create_employee():
    """Create a new employee."""
    data = request.get_json()
    company_id = data.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Validate required fields
    required_fields = ['first_name', 'last_name']
    for field in required_fields:
        if not data.get(field):
            return jsonify({"message": f"{field.replace('_', ' ').title()} is required"}), 400

    # Parse hire date if provided
    hire_date_str = data.get('hire_date')
    hire_date = HelperMethods.parse_date(hire_date_str) if hire_date_str else None

    # Prepare employee data
    employee_data = {
        'first_name': data.get('first_name'),
        'last_name': data.get('last_name'),
        'id_number': data.get('id_number'),
        'email': data.get('email'),
        'phone_number': data.get('phone_number'),
        'department_id': data.get('department_id'),
        'employee_type_id': data.get('employee_type_id'),  # Optional field for payroll
        'position': data.get('position'),
        'hire_date': hire_date,
        'status': data.get('status', 'active')
    }

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if employee with same email or ID number already exists
        if employee_data.get('email'):
            existing_employee = session.query(Employee).filter_by(email=employee_data['email']).first()
            if existing_employee:
                return jsonify({"message": "An employee with this email already exists"}), 400

        if employee_data.get('id_number'):
            existing_employee = session.query(Employee).filter_by(id_number=employee_data['id_number']).first()
            if existing_employee:
                return jsonify({"message": "An employee with this ID number already exists"}), 400

        if employee_data.get('phone_number'):
            existing_employee = session.query(Employee).filter_by(phone_number=employee_data['phone_number']).first()
            if existing_employee:
                return jsonify({"message": "An employee with this phone number already exists"}), 400

        # Create the employee
        employee = Employee.create_employee(session, **employee_data)

        if not employee:
            return jsonify({"message": "Failed to create employee"}), 500

        # Sync the employee with biometric devices
        sync_result = Employee.sync_with_devices(session, employee, company_id, "create")

        # Return success even if device sync had issues, but include sync info in response
        response = Msg.success().add("employee", employee.to_dict())

        if sync_result["success"]:
            response.add("device_sync", {
                "status": "success",
                "message": sync_result["message"],
                "devices_synced": sync_result["devices_synced"]
            })
        else:
            app.logger.warning(f"Employee created but device sync had issues: {sync_result['message']}")
            response.add("device_sync", {
                "status": "warning",
                "message": sync_result["message"],
                "devices_synced": sync_result["devices_synced"]
            })

        return jsons.dump(response), 200

@employees_api.route('/api/employees/<employee_id>', methods=['PATCH'])
@token_required
@roles_required('admin', 'hr')
def update_employee(employee_id):
    """Update an existing employee."""
    data = request.get_json()
    company_id = data.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Parse hire date if provided
    hire_date_str = data.get('hire_date')
    hire_date = HelperMethods.parse_date(hire_date_str) if hire_date_str else None

    # Prepare update data
    update_data = {}
    for field in ['first_name', 'last_name', 'id_number', 'email', 'phone_number',
                 'department_id', 'position', 'status', 'employee_type_id']:
        if field in data:
            update_data[field] = data[field]

    if hire_date:
        update_data['hire_date'] = hire_date

    # PostgreSQL handles UUID conversion automatically - no manual conversion needed

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if employee exists
        employee = Employee.get_employee_by_id(session, employee_id)
        if not employee:
            return jsonify({"message": f"Employee with ID {employee_id} not found"}), 404

        # Store old hire date for leave balance recalculation
        old_hire_date = employee.hire_date

        # Check for duplicate email or ID number
        if 'email' in update_data and update_data['email'] != employee.email:
            existing_employee = session.query(Employee).filter_by(email=update_data['email']).first()
            if existing_employee:
                return jsonify({"message": "An employee with this email already exists"}), 400

        if 'id_number' in update_data and update_data['id_number'] != employee.id_number:
            existing_employee = session.query(Employee).filter_by(id_number=update_data['id_number']).first()
            if existing_employee:
                return jsonify({"message": "An employee with this ID number already exists"}), 400

        # Update the employee
        updated_employee = Employee.update_employee(session, employee_id, **update_data)

        if not updated_employee:
            return jsonify({"message": "Failed to update employee"}), 500

        # Handle hire date changes - recalculate leave balances
        hire_date_changed = False
        if 'hire_date' in update_data and update_data['hire_date'] != old_hire_date:
            hire_date_changed = True
            from application.Services.leave_balance_manager import LeaveBalanceManager
            from flask import g

            current_user_id = g.user.get('user_id', 'system')
            balance_result = LeaveBalanceManager.handle_hire_date_change(
                session, employee_id, old_hire_date, update_data['hire_date'], current_user_id
            )

            app.logger.info(f"Hire date changed for employee {employee_id}: {balance_result['message']}")

        # Sync the updated employee with biometric devices
        sync_result = Employee.sync_with_devices(session, updated_employee, company_id, "update")

        # Return success even if device sync had issues, but include sync info in response
        response = Msg.success().add("employee", updated_employee.to_dict())

        if sync_result["success"]:
            response.add("device_sync", {
                "status": "success",
                "message": sync_result["message"],
                "devices_synced": sync_result["devices_synced"]
            })
        else:
            app.logger.warning(f"Employee updated but device sync had issues: {sync_result['message']}")
            response.add("device_sync", {
                "status": "warning",
                "message": sync_result["message"],
                "devices_synced": sync_result["devices_synced"]
            })

        # Add hire date change info to response
        if hire_date_changed:
            response.add("leave_balance_update", {
                "hire_date_changed": True,
                "old_hire_date": old_hire_date.strftime('%Y-%m-%d') if old_hire_date else None,
                "new_hire_date": update_data['hire_date'].strftime('%Y-%m-%d') if update_data.get('hire_date') else None,
                "balance_recalculation": balance_result.get('message', 'Leave balances recalculated')
            })

        return jsons.dump(response)

@employees_api.route('/api/employees/<employee_id>', methods=['DELETE'])
@token_required
@roles_required('admin', 'hr')
def delete_employee(employee_id):
    """Delete an employee."""
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if employee exists
        employee = Employee.get_employee_by_id(session, employee_id)
        if not employee:
            return jsonify({"message": f"Employee with ID {employee_id} not found"}), 404

        # Sync with devices to remove the employee first (before deleting from database)
        sync_result = Employee.sync_with_devices(session, employee, company_id, "delete")

        # Now delete the employee from the database
        success = Employee.delete_employee(session, employee_id)

        if not success:
            return jsonify({"message": "Failed to delete employee"}), 500

        # Return success with device sync info
        response = Msg.success().add("message", "Employee deleted successfully")

        if sync_result["success"]:
            response.add("device_sync", {
                "status": "success",
                "message": sync_result["message"],
                "devices_synced": sync_result["devices_synced"]
            })
        else:
            app.logger.warning(f"Employee deleted but device sync had issues: {sync_result['message']}")
            response.add("device_sync", {
                "status": "warning",
                "message": sync_result["message"],
                "devices_synced": sync_result["devices_synced"]
            })

        return jsons.dump(response)
    
@employees_api.route('/api/get_upcoming_birthdays', methods=['GET'])
@token_required
def get_upcoming_birthdays():
    """Get upcoming employee birthdays within the next 30 days."""
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        today = date.today()
        end_date = today + timedelta(days=30)

        # Get employees with upcoming birthdays and days left
        upcoming_birthdays = Employee.get_upcoming_birthdays_with_days_left(session, today, end_date)

        if not upcoming_birthdays:
            return jsonify({"message": "No upcoming birthdays found"}), 404

        # Return serialized data with days left
        employees_data = [
            {
                **emp["employee"].to_dict(),
                "days_left": emp["days_left"]
            }
            for emp in upcoming_birthdays
        ]

        return jsonify({
            "message": "Upcoming birthdays retrieved successfully",
            "employees": employees_data
        }), 200
