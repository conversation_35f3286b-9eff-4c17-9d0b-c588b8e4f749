from application.Routes.leave.leave_types_api import leave_types_api
from application.Routes.leave.leave_policies_api import leave_policies_api
from application.Routes.leave.leave_balances_api import leave_balances_api
from application.Routes.leave.leave_requests_api import leave_requests_api
from application.Routes.leave.leave_balance_doctor_api import leave_balance_doctor_api
from application.Routes.leave.leave_audit_api import leave_audit_api
from application.Routes.leave.leave_analytics_api import leave_analytics_api

# This file makes the leave directory a proper Python package
# and allows for easier imports from other parts of the application
