from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from application.Models.employees import Employee, LeaveRequest, LeaveBalance, LeaveType
from application.Models.company import Company
from datetime import datetime, date, timedelta
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from decimal import Decimal
from sqlalchemy import func, and_, extract, case
from calendar import monthrange
import calendar

leave_analytics_api = Blueprint('leave_analytics_api', __name__)

@leave_analytics_api.route('/api/leave/analytics/overview', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_leave_analytics_overview():
    """
    Get comprehensive leave analytics overview with detailed HR insights.

    Query parameters:
    - company_id: Required
    - period: 'week', 'month', 'quarter', 'year' (default: 'month')
    - year: Specific year (default: current year)
    - month: Specific month (default: current month, only if period='month')
    - week: Specific week number 1-52 (default: current week, only if period='week')

    Returns detailed analytics including:
    - applications_received: Applications submitted in period (created_at filter)
    - decisions_made: Approval/rejection decisions made in period (approved_at/updated_at filter)
    - leave_utilization: Actual leave taken in period (start_date filter)
    - period_statistics: Combined overview with all three perspectives
    - all_time_statistics: Overall company context
    - leave_type_breakdown: Applications and days by leave type (all perspectives)
    - department_breakdown: Comprehensive department analysis (all perspectives)
    - status_breakdown: Status distribution with percentages and averages
    - top_applicants: Top 10 employees by leave applications in period
    - leave_patterns: Day-of-week patterns and monthly distribution
    - insights: Key HR insights (busiest department, highest approval rates, etc.)
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        period = request.args.get('period', 'month')
        year = int(request.args.get('year', datetime.now().year))
        month = request.args.get('month')
        week = request.args.get('week')  # Week number (1-52)

        if month:
            month = int(month)
        else:
            month = datetime.now().month if period == 'month' else None

        if week:
            week = int(week)
        else:
            week = datetime.now().isocalendar()[1] if period == 'week' else None  # Current ISO week

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Calculate date ranges
            if period == 'week' and week:
                # Use Python's built-in ISO week calculation
                # Week 1 is the first week with at least 4 days in the new year
                try:
                    # Create a date for the given ISO week
                    # ISO week date: year, week, weekday (1=Monday, 7=Sunday)
                    start_date = date.fromisocalendar(year, week, 1)  # Monday of the week
                    end_date = date.fromisocalendar(year, week, 7)    # Sunday of the week
                except ValueError:
                    # Handle invalid week numbers (e.g., week 53 in non-leap years)
                    # Fall back to last valid week of the year
                    try:
                        start_date = date.fromisocalendar(year, 52, 1)
                        end_date = date.fromisocalendar(year, 52, 7)
                    except ValueError:
                        # If even week 52 fails, use week 51
                        start_date = date.fromisocalendar(year, 51, 1)
                        end_date = date.fromisocalendar(year, 51, 7)
            elif period == 'month' and month:
                start_date = date(year, month, 1)
                end_date = date(year, month, monthrange(year, month)[1])
            elif period == 'quarter':
                quarter = ((month - 1) // 3) + 1 if month else 1
                start_month = (quarter - 1) * 3 + 1
                start_date = date(year, start_month, 1)
                end_month = start_month + 2
                end_date = date(year, end_month, monthrange(year, end_month)[1])
            else:  # year
                start_date = date(year, 1, 1)
                end_date = date(year, 12, 31)

            # === APPLICATIONS RECEIVED (created_at filter) ===
            # Applications submitted in this period
            applications_received_total = session.query(LeaveRequest).filter(
                LeaveRequest.created_at.between(start_date, end_date)
            ).count()

            applications_received_by_status = session.query(
                LeaveRequest.status,
                func.count(LeaveRequest.request_id).label('count')
            ).filter(
                LeaveRequest.created_at.between(start_date, end_date)
            ).group_by(LeaveRequest.status).all()

            # Convert to dict for easy access
            received_status_dict = {status.status: status.count for status in applications_received_by_status}

            # Unique employees who applied in period
            unique_employees_applied = session.query(func.count(func.distinct(LeaveRequest.employee_id))).filter(
                LeaveRequest.created_at.between(start_date, end_date)
            ).scalar() or 0

            # === DECISIONS MADE (approved_at/updated_at filter) ===
            # Decisions made in this period (approvals)
            decisions_approved = session.query(LeaveRequest).filter(
                LeaveRequest.approved_at.between(start_date, end_date),
                LeaveRequest.status == 'approved'
            ).count()

            # Decisions made in this period (rejections) - using updated_at for rejections
            decisions_rejected = session.query(LeaveRequest).filter(
                LeaveRequest.updated_at.between(start_date, end_date),
                LeaveRequest.status == 'rejected'
            ).count()

            # Total decisions made
            total_decisions_made = decisions_approved + decisions_rejected

            # Average processing time for decisions made in period
            avg_processing_time = session.query(
                func.avg(LeaveRequest.approved_at - LeaveRequest.created_at)
            ).filter(
                LeaveRequest.approved_at.between(start_date, end_date),
                LeaveRequest.status == 'approved',
                LeaveRequest.approved_at.isnot(None)
            ).scalar()

            # Convert processing time to days
            avg_processing_days = float(avg_processing_time.total_seconds() / 86400) if avg_processing_time else 0

            # === LEAVE UTILIZATION (start_date filter) ===
            # Actual leave taken in this period
            leave_taken_applications = session.query(LeaveRequest).filter(
                LeaveRequest.start_date.between(start_date, end_date),
                LeaveRequest.status == 'approved'
            ).count()

            # Total days of leave taken in period
            total_days_taken = session.query(func.sum(LeaveRequest.total_days)).filter(
                LeaveRequest.start_date.between(start_date, end_date),
                LeaveRequest.status == 'approved'
            ).scalar() or 0

            # Unique employees on leave in period
            unique_employees_on_leave = session.query(func.count(func.distinct(LeaveRequest.employee_id))).filter(
                LeaveRequest.start_date.between(start_date, end_date),
                LeaveRequest.status == 'approved'
            ).scalar() or 0

            # Departments affected by leave in period
            departments_affected = session.query(func.count(func.distinct(Employee.department_id))).select_from(
                LeaveRequest
            ).join(Employee, LeaveRequest.employee_id == Employee.employee_id).filter(
                LeaveRequest.start_date.between(start_date, end_date),
                LeaveRequest.status == 'approved'
            ).scalar() or 0

            # All-time statistics (for context)
            all_time_total = session.query(LeaveRequest).count()
            all_time_approved = session.query(LeaveRequest).filter(
                LeaveRequest.status == 'approved'
            ).count()
            all_time_pending = session.query(LeaveRequest).filter(
                LeaveRequest.status == 'pending'
            ).count()

            # === LEAVE TYPE BREAKDOWN (All Perspectives) ===
            # Applications received by leave type
            leave_type_received = session.query(
                LeaveType.name,
                LeaveType.code,
                func.count(LeaveRequest.request_id).label('applications_received'),
                func.sum(LeaveRequest.total_days).label('days_requested'),
                func.avg(LeaveRequest.total_days).label('avg_days_requested')
            ).join(LeaveRequest, LeaveType.leave_type_id == LeaveRequest.leave_type_id).filter(
                LeaveRequest.created_at.between(start_date, end_date)
            ).group_by(LeaveType.leave_type_id, LeaveType.name, LeaveType.code).all()

            # Decisions made by leave type
            leave_type_decisions = session.query(
                LeaveType.name,
                LeaveType.code,
                func.count(case((LeaveRequest.approved_at.between(start_date, end_date), 1))).label('decisions_approved'),
                func.count(case((and_(LeaveRequest.updated_at.between(start_date, end_date), LeaveRequest.status == 'rejected'), 1))).label('decisions_rejected')
            ).join(LeaveRequest, LeaveType.leave_type_id == LeaveRequest.leave_type_id).filter(
                and_(
                    LeaveRequest.approved_at.between(start_date, end_date),
                    LeaveRequest.status == 'approved'
                ) | and_(
                    LeaveRequest.updated_at.between(start_date, end_date),
                    LeaveRequest.status == 'rejected'
                )
            ).group_by(LeaveType.leave_type_id, LeaveType.name, LeaveType.code).all()

            # Leave utilization by leave type
            leave_type_utilization = session.query(
                LeaveType.name,
                LeaveType.code,
                func.count(LeaveRequest.request_id).label('leave_instances'),
                func.sum(LeaveRequest.total_days).label('days_taken'),
                func.avg(LeaveRequest.total_days).label('avg_days_taken')
            ).join(LeaveRequest, LeaveType.leave_type_id == LeaveRequest.leave_type_id).filter(
                LeaveRequest.start_date.between(start_date, end_date),
                LeaveRequest.status == 'approved'
            ).group_by(LeaveType.leave_type_id, LeaveType.name, LeaveType.code).all()

            # === DEPARTMENT BREAKDOWN (All Perspectives) ===
            from application.Models.employees.department import Department

            # Applications received by department
            dept_applications_received = session.query(
                Department.department_id,
                Department.name.label('department_name'),
                func.count(LeaveRequest.request_id).label('applications_received'),
                func.count(case((LeaveRequest.status == 'approved', 1))).label('currently_approved'),
                func.count(case((LeaveRequest.status == 'pending', 1))).label('currently_pending'),
                func.count(case((LeaveRequest.status == 'rejected', 1))).label('currently_rejected'),
                func.count(case((LeaveRequest.status == 'cancelled', 1))).label('currently_cancelled'),
                func.sum(LeaveRequest.total_days).label('total_days_requested'),
                func.count(func.distinct(LeaveRequest.employee_id)).label('unique_employees_applied'),
                func.min(LeaveRequest.created_at).label('first_application'),
                func.max(LeaveRequest.created_at).label('last_application')
            ).select_from(LeaveRequest).join(Employee, LeaveRequest.employee_id == Employee.employee_id).outerjoin(Department, Employee.department_id == Department.department_id).filter(
                LeaveRequest.created_at.between(start_date, end_date)
            ).group_by(Department.department_id, Department.name).all()

            # Decisions made by department
            dept_decisions_made = session.query(
                Department.department_id,
                Department.name.label('department_name'),
                func.count(case((LeaveRequest.approved_at.between(start_date, end_date), 1))).label('decisions_approved'),
                func.count(case((and_(LeaveRequest.updated_at.between(start_date, end_date), LeaveRequest.status == 'rejected'), 1))).label('decisions_rejected'),
                func.avg(LeaveRequest.approved_at - LeaveRequest.created_at).label('avg_processing_time')
            ).select_from(LeaveRequest).join(Employee, LeaveRequest.employee_id == Employee.employee_id).outerjoin(Department, Employee.department_id == Department.department_id).filter(
                and_(
                    LeaveRequest.approved_at.between(start_date, end_date),
                    LeaveRequest.status == 'approved'
                ) | and_(
                    LeaveRequest.updated_at.between(start_date, end_date),
                    LeaveRequest.status == 'rejected'
                )
            ).group_by(Department.department_id, Department.name).all()

            # Leave utilization by department
            dept_leave_utilization = session.query(
                Department.department_id,
                Department.name.label('department_name'),
                func.count(LeaveRequest.request_id).label('leave_instances'),
                func.sum(LeaveRequest.total_days).label('total_days_taken'),
                func.avg(LeaveRequest.total_days).label('avg_leave_duration'),
                func.count(func.distinct(LeaveRequest.employee_id)).label('unique_employees_on_leave')
            ).select_from(LeaveRequest).join(Employee, LeaveRequest.employee_id == Employee.employee_id).outerjoin(Department, Employee.department_id == Department.department_id).filter(
                LeaveRequest.start_date.between(start_date, end_date),
                LeaveRequest.status == 'approved'
            ).group_by(Department.department_id, Department.name).all()

            # Status breakdown with more details (based on applications received)
            status_stats = session.query(
                LeaveRequest.status,
                func.count(LeaveRequest.request_id).label('count'),
                func.sum(LeaveRequest.total_days).label('total_days'),
                func.avg(LeaveRequest.total_days).label('avg_days'),
                func.count(func.distinct(LeaveRequest.employee_id)).label('unique_employees')
            ).filter(
                LeaveRequest.created_at.between(start_date, end_date)
            ).group_by(LeaveRequest.status).all()

            # Top employees by leave applications received in period
            top_applicants = session.query(
                Employee.employee_id,
                Employee.first_name,
                Employee.last_name,
                Department.name.label('department_name'),
                func.count(LeaveRequest.request_id).label('total_applications'),
                func.count(case((LeaveRequest.status == 'approved', 1))).label('approved_count'),
                func.sum(LeaveRequest.total_days).label('total_days_requested'),
                func.sum(case((LeaveRequest.status == 'approved', LeaveRequest.total_days))).label('total_days_approved')
            ).select_from(LeaveRequest).join(Employee, LeaveRequest.employee_id == Employee.employee_id).outerjoin(Department, Employee.department_id == Department.department_id).filter(
                LeaveRequest.created_at.between(start_date, end_date)
            ).group_by(Employee.employee_id, Employee.first_name, Employee.last_name, Department.name).order_by(
                func.count(LeaveRequest.request_id).desc()
            ).limit(10).all()

            # Leave pattern analysis
            leave_patterns = session.query(
                func.extract('dow', LeaveRequest.start_date).label('day_of_week'),
                func.count(LeaveRequest.request_id).label('applications'),
                func.avg(LeaveRequest.total_days).label('avg_duration')
            ).filter(
                LeaveRequest.created_at.between(start_date, end_date),
                LeaveRequest.status == 'approved'
            ).group_by(func.extract('dow', LeaveRequest.start_date)).all()

            # Monthly distribution within the period (if period is quarter or year)
            monthly_distribution = []
            monthly_dist = []  # Initialize to empty list

            if period in ['quarter', 'year']:
                monthly_dist = session.query(
                    func.extract('month', LeaveRequest.created_at).label('month'),
                    func.count(LeaveRequest.request_id).label('applications'),
                    func.count(case((LeaveRequest.status == 'approved', 1))).label('approved'),
                    func.sum(case((LeaveRequest.status == 'approved', LeaveRequest.total_days))).label('days_taken')
                ).filter(
                    LeaveRequest.created_at.between(start_date, end_date)
                ).group_by(func.extract('month', LeaveRequest.created_at)).order_by(
                    func.extract('month', LeaveRequest.created_at)
                ).all()

                monthly_distribution = [
                    {
                        "month": int(stat.month),
                        "month_name": calendar.month_name[int(stat.month)],
                        "applications": stat.applications,
                        "approved": stat.approved,
                        "days_taken": float(stat.days_taken or 0),
                        "approval_rate": round(stat.approved / stat.applications * 100, 2) if stat.applications > 0 else 0
                    } for stat in monthly_dist
                ]

            # Calculate total applications safely
            total_applications = sum(stat.applications for stat in monthly_dist) if monthly_dist else applications_received_total

            return jsonify({
                "success": True,
                "data": {
                    "period_info": {
                        "period": period,
                        "year": year,
                        "month": month,
                        "week": week if period == 'week' else None,
                        "start_date": start_date.strftime('%Y-%m-%d'),
                        "end_date": end_date.strftime('%Y-%m-%d')
                    },
                    "applications_received": {
                        "description": "Leave applications submitted in this period",
                        "total": applications_received_total,
                        "by_status": {
                            "approved": received_status_dict.get('approved', 0),
                            "pending": received_status_dict.get('pending', 0),
                            "rejected": received_status_dict.get('rejected', 0),
                            "cancelled": received_status_dict.get('cancelled', 0)
                        },
                        "unique_employees": unique_employees_applied
                    },
                    "decisions_made": {
                        "description": "Approval/rejection decisions made in this period",
                        "total": total_decisions_made,
                        "approved": decisions_approved,
                        "rejected": decisions_rejected,
                        "approval_rate": round(decisions_approved / total_decisions_made * 100, 2) if total_decisions_made > 0 else 0,
                        "avg_processing_days": round(avg_processing_days, 1)
                    },
                    "leave_utilization": {
                        "description": "Actual leave taken in this period",
                        "leave_instances": leave_taken_applications,
                        "total_days_taken": float(total_days_taken),
                        "unique_employees_on_leave": unique_employees_on_leave,
                        "departments_affected": departments_affected,
                        "avg_days_per_leave": round(float(total_days_taken) / leave_taken_applications, 1) if leave_taken_applications > 0 else 0
                    },
                    "period_statistics": {
                        "description": "Combined overview of all three perspectives",
                        "applications_received": applications_received_total,
                        "decisions_made": total_decisions_made,
                        "leave_instances": leave_taken_applications,
                        "processing_efficiency": round(avg_processing_days, 1)
                    },
                    "all_time_statistics": {
                        "total_applications": all_time_total,
                        "approved_applications": all_time_approved,
                        "pending_applications": all_time_pending,
                        "approval_rate": round(all_time_approved / all_time_total * 100, 2) if all_time_total > 0 else 0
                    },
                    "leave_type_breakdown": {
                        "applications_received": [
                            {
                                "leave_type": stat.name,
                                "code": stat.code,
                                "applications_received": stat.applications_received,
                                "days_requested": float(stat.days_requested or 0),
                                "avg_days_requested": round(float(stat.avg_days_requested or 0), 1)
                            } for stat in leave_type_received
                        ],
                        "decisions_made": [
                            {
                                "leave_type": stat.name,
                                "code": stat.code,
                                "decisions_approved": stat.decisions_approved,
                                "decisions_rejected": stat.decisions_rejected,
                                "total_decisions": stat.decisions_approved + stat.decisions_rejected
                            } for stat in leave_type_decisions
                        ],
                        "leave_utilization": [
                            {
                                "leave_type": stat.name,
                                "code": stat.code,
                                "leave_instances": stat.leave_instances,
                                "days_taken": float(stat.days_taken or 0),
                                "avg_days_taken": round(float(stat.avg_days_taken or 0), 1)
                            } for stat in leave_type_utilization
                        ]
                    },
                    "department_breakdown": {
                        "applications_received": [
                            {
                                "department_id": str(stat.department_id) if stat.department_id else None,
                                "department_name": stat.department_name or "Unassigned",
                                "applications_received": stat.applications_received,
                                "current_status": {
                                    "approved": stat.currently_approved,
                                    "pending": stat.currently_pending,
                                    "rejected": stat.currently_rejected,
                                    "cancelled": stat.currently_cancelled
                                },
                                "days_requested": float(stat.total_days_requested or 0),
                                "unique_employees_applied": stat.unique_employees_applied,
                                "timeline": {
                                    "first_application": stat.first_application.strftime('%Y-%m-%d %H:%M:%S') if stat.first_application else None,
                                    "last_application": stat.last_application.strftime('%Y-%m-%d %H:%M:%S') if stat.last_application else None
                                }
                            } for stat in dept_applications_received
                        ],
                        "decisions_made": [
                            {
                                "department_id": str(stat.department_id) if stat.department_id else None,
                                "department_name": stat.department_name or "Unassigned",
                                "decisions_approved": stat.decisions_approved,
                                "decisions_rejected": stat.decisions_rejected,
                                "total_decisions": stat.decisions_approved + stat.decisions_rejected,
                                "avg_processing_days": round(float(stat.avg_processing_time.total_seconds() / 86400), 1) if stat.avg_processing_time else 0
                            } for stat in dept_decisions_made
                        ],
                        "leave_utilization": [
                            {
                                "department_id": str(stat.department_id) if stat.department_id else None,
                                "department_name": stat.department_name or "Unassigned",
                                "leave_instances": stat.leave_instances,
                                "total_days_taken": float(stat.total_days_taken or 0),
                                "avg_leave_duration": round(float(stat.avg_leave_duration or 0), 1),
                                "unique_employees_on_leave": stat.unique_employees_on_leave
                            } for stat in dept_leave_utilization
                        ]
                    },
                    "status_breakdown": [
                        {
                            "status": stat.status,
                            "count": stat.count,
                            "total_days": float(stat.total_days or 0),
                            "avg_days": round(float(stat.avg_days or 0), 1),
                            "unique_employees": stat.unique_employees,
                            "percentage": round(stat.count / total_applications * 100, 2) if total_applications > 0 else 0
                        } for stat in status_stats
                    ],
                    "top_applicants": [
                        {
                            "employee_id": str(applicant.employee_id),
                            "employee_name": f"{applicant.first_name} {applicant.last_name}",
                            "department": applicant.department_name or "Unassigned",
                            "applications": {
                                "total": applicant.total_applications,
                                "approved": applicant.approved_count
                            },
                            "days": {
                                "total_requested": float(applicant.total_days_requested or 0),
                                "total_approved": float(applicant.total_days_approved or 0)
                            },
                            "approval_rate": round(applicant.approved_count / applicant.total_applications * 100, 2) if applicant.total_applications > 0 else 0
                        } for applicant in top_applicants
                    ],
                    "leave_patterns": {
                        "by_day_of_week": [
                            {
                                "day_of_week": int(pattern.day_of_week),
                                "day_name": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"][int(pattern.day_of_week)],
                                "applications": pattern.applications,
                                "avg_duration": round(float(pattern.avg_duration or 0), 1)
                            } for pattern in leave_patterns
                        ],
                        "monthly_distribution": monthly_distribution
                    },
                    "insights": {
                        "applications_received": {
                            "busiest_department": max(dept_applications_received, key=lambda x: x.applications_received).department_name if dept_applications_received else None,
                            "most_employees_applying": max(dept_applications_received, key=lambda x: x.unique_employees_applied).department_name if dept_applications_received else None
                        },
                        "decisions_made": {
                            "most_decisions_dept": max(dept_decisions_made, key=lambda x: x.decisions_approved + x.decisions_rejected).department_name if dept_decisions_made else None,
                            "fastest_processing_dept": min(dept_decisions_made, key=lambda x: x.avg_processing_time.total_seconds() if x.avg_processing_time else float('inf')).department_name if dept_decisions_made else None
                        },
                        "leave_utilization": {
                            "most_days_taken_dept": max(dept_leave_utilization, key=lambda x: x.total_days_taken or 0).department_name if dept_leave_utilization else None,
                            "most_employees_on_leave_dept": max(dept_leave_utilization, key=lambda x: x.unique_employees_on_leave).department_name if dept_leave_utilization else None
                        },
                        "overall": {
                            "avg_leave_duration_company": round(sum(float(stat.total_days or 0) for stat in status_stats) / sum(stat.count for stat in status_stats), 1) if sum(stat.count for stat in status_stats) > 0 else 0,
                            "total_workforce_impact": f"{unique_employees_on_leave} employees on leave, {departments_affected} departments affected"
                        }
                    }
                }
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting leave analytics overview: {e}")
        return jsonify({"error": "Internal server error"}), 500

@leave_analytics_api.route('/api/leave/analytics/trends', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_leave_trends():
    """
    Get comprehensive leave trends over time with detailed HR insights.

    Query parameters:
    - company_id: Required
    - period: 'week'/'weekly', 'month'/'monthly', 'year'/'yearly' (default: 'monthly')
    - weeks_back: Number of weeks to go back (default: 12, only if period='weekly')
    - months_back: Number of months to go back (default: 12, only if period='monthly')
    - years_back: Number of years to go back (default: 3, only if period='yearly')

    Returns detailed trends including:
    - Applications received: Applications submitted in each period (created_at filter)
    - Decisions made: Approval/rejection decisions made in each period (approved_at/updated_at filter)
    - Leave utilization: Actual leave taken in each period (start_date filter)
    - Growth analysis and peak period identification for all three perspectives
    - Department and leave type insights per period
    - Trend averages and insights for strategic HR planning
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        period = request.args.get('period', 'monthly')
        # Support both 'month'/'monthly', 'week'/'weekly', and 'year'/'yearly' formats
        if period == 'month':
            period = 'monthly'
        elif period == 'week':
            period = 'weekly'
        elif period == 'year':
            period = 'yearly'

        weeks_back = int(request.args.get('weeks_back', 12))
        months_back = int(request.args.get('months_back', 12))
        years_back = int(request.args.get('years_back', 3))

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        from app import db_connection
        with db_connection.get_session(database_name) as session:
            trends = []

            if period == 'monthly':
                # Monthly trends
                for i in range(months_back):
                    target_date = date.today().replace(day=1) - timedelta(days=30*i)
                    month_start = target_date.replace(day=1)
                    month_end = date(target_date.year, target_date.month,
                                   monthrange(target_date.year, target_date.month)[1])

                    # === APPLICATIONS RECEIVED ===
                    applications_received = session.query(func.count(LeaveRequest.request_id)).filter(
                        LeaveRequest.created_at.between(month_start, month_end)
                    ).scalar() or 0

                    received_approved = session.query(func.count(LeaveRequest.request_id)).filter(
                        LeaveRequest.created_at.between(month_start, month_end),
                        LeaveRequest.status == 'approved'
                    ).scalar() or 0

                    received_pending = session.query(func.count(LeaveRequest.request_id)).filter(
                        LeaveRequest.created_at.between(month_start, month_end),
                        LeaveRequest.status == 'pending'
                    ).scalar() or 0

                    received_rejected = session.query(func.count(LeaveRequest.request_id)).filter(
                        LeaveRequest.created_at.between(month_start, month_end),
                        LeaveRequest.status == 'rejected'
                    ).scalar() or 0

                    received_cancelled = session.query(func.count(LeaveRequest.request_id)).filter(
                        LeaveRequest.created_at.between(month_start, month_end),
                        LeaveRequest.status == 'cancelled'
                    ).scalar() or 0

                    unique_applicants = session.query(func.count(func.distinct(LeaveRequest.employee_id))).filter(
                        LeaveRequest.created_at.between(month_start, month_end)
                    ).scalar() or 0

                    # === DECISIONS MADE ===
                    decisions_approved = session.query(func.count(LeaveRequest.request_id)).filter(
                        LeaveRequest.approved_at.between(month_start, month_end),
                        LeaveRequest.status == 'approved'
                    ).scalar() or 0

                    decisions_rejected = session.query(func.count(LeaveRequest.request_id)).filter(
                        LeaveRequest.updated_at.between(month_start, month_end),
                        LeaveRequest.status == 'rejected'
                    ).scalar() or 0

                    total_decisions = decisions_approved + decisions_rejected

                    # Average processing time for decisions made in period
                    avg_processing_time = session.query(
                        func.avg(LeaveRequest.approved_at - LeaveRequest.created_at)
                    ).filter(
                        LeaveRequest.approved_at.between(month_start, month_end),
                        LeaveRequest.status == 'approved',
                        LeaveRequest.approved_at.isnot(None)
                    ).scalar()

                    avg_processing_days = float(avg_processing_time.total_seconds() / 86400) if avg_processing_time else 0

                    # === LEAVE UTILIZATION ===
                    leave_instances = session.query(func.count(LeaveRequest.request_id)).filter(
                        LeaveRequest.start_date.between(month_start, month_end),
                        LeaveRequest.status == 'approved'
                    ).scalar() or 0

                    days_taken = session.query(func.sum(LeaveRequest.total_days)).filter(
                        LeaveRequest.start_date.between(month_start, month_end),
                        LeaveRequest.status == 'approved'
                    ).scalar() or 0

                    unique_employees_on_leave = session.query(func.count(func.distinct(LeaveRequest.employee_id))).filter(
                        LeaveRequest.start_date.between(month_start, month_end),
                        LeaveRequest.status == 'approved'
                    ).scalar() or 0

                    # Top department for this month
                    from application.Models.employees.department import Department
                    top_dept = session.query(
                        Department.name,
                        func.count(LeaveRequest.request_id).label('dept_applications')
                    ).select_from(LeaveRequest).join(Employee, LeaveRequest.employee_id == Employee.employee_id).outerjoin(Department, Employee.department_id == Department.department_id).filter(
                        LeaveRequest.created_at.between(month_start, month_end)
                    ).group_by(Department.name).order_by(func.count(LeaveRequest.request_id).desc()).first()

                    # Most popular leave type for this month
                    top_leave_type = session.query(
                        LeaveType.name,
                        func.count(LeaveRequest.request_id).label('type_applications')
                    ).join(LeaveRequest, LeaveType.leave_type_id == LeaveRequest.leave_type_id).filter(
                        LeaveRequest.created_at.between(month_start, month_end)
                    ).group_by(LeaveType.name).order_by(func.count(LeaveRequest.request_id).desc()).first()
                    
                    trends.append({
                        "period": target_date.strftime('%Y-%m'),
                        "period_name": target_date.strftime('%B %Y'),
                        "applications_received": {
                            "total": applications_received,
                            "by_status": {
                                "approved": received_approved,
                                "pending": received_pending,
                                "rejected": received_rejected,
                                "cancelled": received_cancelled
                            },
                            "unique_applicants": unique_applicants
                        },
                        "decisions_made": {
                            "total": total_decisions,
                            "approved": decisions_approved,
                            "rejected": decisions_rejected,
                            "approval_rate": round(decisions_approved / total_decisions * 100, 2) if total_decisions > 0 else 0,
                            "avg_processing_days": round(avg_processing_days, 1)
                        },
                        "leave_utilization": {
                            "leave_instances": leave_instances,
                            "total_days_taken": float(days_taken),
                            "unique_employees_on_leave": unique_employees_on_leave,
                            "avg_days_per_leave": round(float(days_taken) / leave_instances, 1) if leave_instances > 0 else 0
                        },
                        "insights": {
                            "busiest_department": top_dept.name if top_dept else None,
                            "top_leave_type": top_leave_type.name if top_leave_type else None
                        }
                    })

            elif period == 'weekly':
                # Weekly trends
                current_date = date.today()
                current_year, current_week, _ = current_date.isocalendar()

                for i in range(weeks_back):
                    # Calculate target week
                    target_week = current_week - i
                    target_year = current_year

                    # Handle year rollover
                    if target_week <= 0:
                        target_year -= 1
                        # Get the last week of the previous year
                        try:
                            last_week_prev_year = date(target_year, 12, 28).isocalendar()[1]
                        except:
                            last_week_prev_year = 52
                        target_week = last_week_prev_year + target_week

                    # Calculate week start and end dates
                    try:
                        week_start = date.fromisocalendar(target_year, target_week, 1)  # Monday
                        week_end = date.fromisocalendar(target_year, target_week, 7)    # Sunday
                    except ValueError:
                        # Handle invalid week numbers
                        continue

                    # === APPLICATIONS RECEIVED ===
                    applications_received = session.query(func.count(LeaveRequest.request_id)).filter(
                        LeaveRequest.created_at.between(week_start, week_end)
                    ).scalar() or 0

                    received_approved = session.query(func.count(LeaveRequest.request_id)).filter(
                        LeaveRequest.created_at.between(week_start, week_end),
                        LeaveRequest.status == 'approved'
                    ).scalar() or 0

                    received_pending = session.query(func.count(LeaveRequest.request_id)).filter(
                        LeaveRequest.created_at.between(week_start, week_end),
                        LeaveRequest.status == 'pending'
                    ).scalar() or 0

                    received_rejected = session.query(func.count(LeaveRequest.request_id)).filter(
                        LeaveRequest.created_at.between(week_start, week_end),
                        LeaveRequest.status == 'rejected'
                    ).scalar() or 0

                    received_cancelled = session.query(func.count(LeaveRequest.request_id)).filter(
                        LeaveRequest.created_at.between(week_start, week_end),
                        LeaveRequest.status == 'cancelled'
                    ).scalar() or 0

                    unique_applicants = session.query(func.count(func.distinct(LeaveRequest.employee_id))).filter(
                        LeaveRequest.created_at.between(week_start, week_end)
                    ).scalar() or 0

                    # === DECISIONS MADE ===
                    decisions_approved = session.query(func.count(LeaveRequest.request_id)).filter(
                        LeaveRequest.approved_at.between(week_start, week_end),
                        LeaveRequest.status == 'approved'
                    ).scalar() or 0

                    decisions_rejected = session.query(func.count(LeaveRequest.request_id)).filter(
                        LeaveRequest.updated_at.between(week_start, week_end),
                        LeaveRequest.status == 'rejected'
                    ).scalar() or 0

                    total_decisions = decisions_approved + decisions_rejected

                    # Average processing time for decisions made in period
                    avg_processing_time = session.query(
                        func.avg(LeaveRequest.approved_at - LeaveRequest.created_at)
                    ).filter(
                        LeaveRequest.approved_at.between(week_start, week_end),
                        LeaveRequest.status == 'approved',
                        LeaveRequest.approved_at.isnot(None)
                    ).scalar()

                    avg_processing_days = float(avg_processing_time.total_seconds() / 86400) if avg_processing_time else 0

                    # === LEAVE UTILIZATION ===
                    leave_instances = session.query(func.count(LeaveRequest.request_id)).filter(
                        LeaveRequest.start_date.between(week_start, week_end),
                        LeaveRequest.status == 'approved'
                    ).scalar() or 0

                    days_taken = session.query(func.sum(LeaveRequest.total_days)).filter(
                        LeaveRequest.start_date.between(week_start, week_end),
                        LeaveRequest.status == 'approved'
                    ).scalar() or 0

                    unique_employees_on_leave = session.query(func.count(func.distinct(LeaveRequest.employee_id))).filter(
                        LeaveRequest.start_date.between(week_start, week_end),
                        LeaveRequest.status == 'approved'
                    ).scalar() or 0

                    # Top department for this week
                    from application.Models.employees.department import Department
                    top_dept = session.query(
                        Department.name,
                        func.count(LeaveRequest.request_id).label('dept_applications')
                    ).select_from(LeaveRequest).join(Employee, LeaveRequest.employee_id == Employee.employee_id).outerjoin(Department, Employee.department_id == Department.department_id).filter(
                        LeaveRequest.created_at.between(week_start, week_end)
                    ).group_by(Department.name).order_by(func.count(LeaveRequest.request_id).desc()).first()

                    # Most popular leave type for this week
                    top_leave_type = session.query(
                        LeaveType.name,
                        func.count(LeaveRequest.request_id).label('type_applications')
                    ).join(LeaveRequest, LeaveType.leave_type_id == LeaveRequest.leave_type_id).filter(
                        LeaveRequest.created_at.between(week_start, week_end)
                    ).group_by(LeaveType.name).order_by(func.count(LeaveRequest.request_id).desc()).first()

                    trends.append({
                        "period": f"{target_year}-W{target_week:02d}",
                        "period_name": f"Week {target_week}, {target_year}",
                        "applications_received": {
                            "total": applications_received,
                            "by_status": {
                                "approved": received_approved,
                                "pending": received_pending,
                                "rejected": received_rejected,
                                "cancelled": received_cancelled
                            },
                            "unique_applicants": unique_applicants
                        },
                        "decisions_made": {
                            "total": total_decisions,
                            "approved": decisions_approved,
                            "rejected": decisions_rejected,
                            "approval_rate": round(decisions_approved / total_decisions * 100, 2) if total_decisions > 0 else 0,
                            "avg_processing_days": round(avg_processing_days, 1)
                        },
                        "leave_utilization": {
                            "leave_instances": leave_instances,
                            "total_days_taken": float(days_taken),
                            "unique_employees_on_leave": unique_employees_on_leave,
                            "avg_days_per_leave": round(float(days_taken) / leave_instances, 1) if leave_instances > 0 else 0
                        },
                        "insights": {
                            "busiest_department": top_dept.name if top_dept else None,
                            "top_leave_type": top_leave_type.name if top_leave_type else None
                        }
                    })

            else:  # yearly
                # Yearly trends
                current_year = datetime.now().year
                for i in range(years_back):
                    target_year = current_year - i

                    # === APPLICATIONS RECEIVED ===
                    applications_received = session.query(func.count(LeaveRequest.request_id)).filter(
                        extract('year', LeaveRequest.created_at) == target_year
                    ).scalar() or 0

                    received_approved = session.query(func.count(LeaveRequest.request_id)).filter(
                        extract('year', LeaveRequest.created_at) == target_year,
                        LeaveRequest.status == 'approved'
                    ).scalar() or 0

                    received_pending = session.query(func.count(LeaveRequest.request_id)).filter(
                        extract('year', LeaveRequest.created_at) == target_year,
                        LeaveRequest.status == 'pending'
                    ).scalar() or 0

                    received_rejected = session.query(func.count(LeaveRequest.request_id)).filter(
                        extract('year', LeaveRequest.created_at) == target_year,
                        LeaveRequest.status == 'rejected'
                    ).scalar() or 0

                    received_cancelled = session.query(func.count(LeaveRequest.request_id)).filter(
                        extract('year', LeaveRequest.created_at) == target_year,
                        LeaveRequest.status == 'cancelled'
                    ).scalar() or 0

                    unique_applicants = session.query(func.count(func.distinct(LeaveRequest.employee_id))).filter(
                        extract('year', LeaveRequest.created_at) == target_year
                    ).scalar() or 0

                    # === DECISIONS MADE ===
                    decisions_approved = session.query(func.count(LeaveRequest.request_id)).filter(
                        extract('year', LeaveRequest.approved_at) == target_year,
                        LeaveRequest.status == 'approved'
                    ).scalar() or 0

                    decisions_rejected = session.query(func.count(LeaveRequest.request_id)).filter(
                        extract('year', LeaveRequest.updated_at) == target_year,
                        LeaveRequest.status == 'rejected'
                    ).scalar() or 0

                    total_decisions = decisions_approved + decisions_rejected

                    avg_processing_time = session.query(
                        func.avg(LeaveRequest.approved_at - LeaveRequest.created_at)
                    ).filter(
                        extract('year', LeaveRequest.approved_at) == target_year,
                        LeaveRequest.status == 'approved',
                        LeaveRequest.approved_at.isnot(None)
                    ).scalar()

                    avg_processing_days = float(avg_processing_time.total_seconds() / 86400) if avg_processing_time else 0

                    # === LEAVE UTILIZATION ===
                    leave_instances = session.query(func.count(LeaveRequest.request_id)).filter(
                        extract('year', LeaveRequest.start_date) == target_year,
                        LeaveRequest.status == 'approved'
                    ).scalar() or 0

                    days_taken = session.query(func.sum(LeaveRequest.total_days)).filter(
                        extract('year', LeaveRequest.start_date) == target_year,
                        LeaveRequest.status == 'approved'
                    ).scalar() or 0

                    unique_employees_on_leave = session.query(func.count(func.distinct(LeaveRequest.employee_id))).filter(
                        extract('year', LeaveRequest.start_date) == target_year,
                        LeaveRequest.status == 'approved'
                    ).scalar() or 0

                    # Top department for this year
                    from application.Models.employees.department import Department
                    top_dept = session.query(
                        Department.name,
                        func.count(LeaveRequest.request_id).label('dept_applications')
                    ).select_from(LeaveRequest).join(Employee, LeaveRequest.employee_id == Employee.employee_id).outerjoin(Department, Employee.department_id == Department.department_id).filter(
                        extract('year', LeaveRequest.created_at) == target_year
                    ).group_by(Department.name).order_by(func.count(LeaveRequest.request_id).desc()).first()

                    # Most popular leave type for this year
                    top_leave_type = session.query(
                        LeaveType.name,
                        func.count(LeaveRequest.request_id).label('type_applications')
                    ).join(LeaveRequest, LeaveType.leave_type_id == LeaveRequest.leave_type_id).filter(
                        extract('year', LeaveRequest.created_at) == target_year
                    ).group_by(LeaveType.name).order_by(func.count(LeaveRequest.request_id).desc()).first()
                    
                    trends.append({
                        "period": str(target_year),
                        "period_name": str(target_year),
                        "applications_received": {
                            "total": applications_received,
                            "by_status": {
                                "approved": received_approved,
                                "pending": received_pending,
                                "rejected": received_rejected,
                                "cancelled": received_cancelled
                            },
                            "unique_applicants": unique_applicants
                        },
                        "decisions_made": {
                            "total": total_decisions,
                            "approved": decisions_approved,
                            "rejected": decisions_rejected,
                            "approval_rate": round(decisions_approved / total_decisions * 100, 2) if total_decisions > 0 else 0,
                            "avg_processing_days": round(avg_processing_days, 1)
                        },
                        "leave_utilization": {
                            "leave_instances": leave_instances,
                            "total_days_taken": float(days_taken),
                            "unique_employees_on_leave": unique_employees_on_leave,
                            "avg_days_per_leave": round(float(days_taken) / leave_instances, 1) if leave_instances > 0 else 0
                        },
                        "insights": {
                            "busiest_department": top_dept.name if top_dept else None,
                            "top_leave_type": top_leave_type.name if top_leave_type else None
                        }
                    })

            # Calculate trend insights
            trends_reversed = list(reversed(trends))  # Most recent first

            # Growth analysis
            if len(trends_reversed) >= 2:
                latest = trends_reversed[0]
                previous = trends_reversed[1]

                # Applications received growth
                app_received_growth = ((latest["applications_received"]["total"] - previous["applications_received"]["total"]) / previous["applications_received"]["total"] * 100) if previous["applications_received"]["total"] > 0 else 0

                # Decisions made growth
                decisions_growth = ((latest["decisions_made"]["total"] - previous["decisions_made"]["total"]) / previous["decisions_made"]["total"] * 100) if previous["decisions_made"]["total"] > 0 else 0

                # Leave utilization growth
                utilization_growth = ((latest["leave_utilization"]["total_days_taken"] - previous["leave_utilization"]["total_days_taken"]) / previous["leave_utilization"]["total_days_taken"] * 100) if previous["leave_utilization"]["total_days_taken"] > 0 else 0

                # Approval rate change
                approval_change = latest["decisions_made"]["approval_rate"] - previous["decisions_made"]["approval_rate"]
            else:
                app_received_growth = decisions_growth = utilization_growth = approval_change = 0

            # Peak period analysis
            peak_applications = max(trends, key=lambda x: x["applications_received"]["total"]) if trends else None
            peak_decisions = max(trends, key=lambda x: x["decisions_made"]["total"]) if trends else None
            peak_utilization = max(trends, key=lambda x: x["leave_utilization"]["total_days_taken"]) if trends else None
            best_approval_rate = max(trends, key=lambda x: x["decisions_made"]["approval_rate"]) if trends else None

            return jsonify({
                "success": True,
                "data": {
                    "period_type": period,
                    "periods_analyzed": len(trends),
                    "trends": trends_reversed,
                    "trend_insights": {
                        "growth_analysis": {
                            "applications_received_growth": round(app_received_growth, 2),
                            "decisions_made_growth": round(decisions_growth, 2),
                            "leave_utilization_growth": round(utilization_growth, 2),
                            "approval_rate_change": round(approval_change, 2)
                        },
                        "peak_periods": {
                            "highest_applications_received": {
                                "period": peak_applications["period_name"] if peak_applications else None,
                                "count": peak_applications["applications_received"]["total"] if peak_applications else 0
                            },
                            "most_decisions_made": {
                                "period": peak_decisions["period_name"] if peak_decisions else None,
                                "count": peak_decisions["decisions_made"]["total"] if peak_decisions else 0
                            },
                            "highest_leave_utilization": {
                                "period": peak_utilization["period_name"] if peak_utilization else None,
                                "days": peak_utilization["leave_utilization"]["total_days_taken"] if peak_utilization else 0
                            },
                            "best_approval_rate": {
                                "period": best_approval_rate["period_name"] if best_approval_rate else None,
                                "rate": best_approval_rate["decisions_made"]["approval_rate"] if best_approval_rate else 0
                            }
                        },
                        "averages": {
                            "avg_applications_received_per_period": round(sum(t["applications_received"]["total"] for t in trends) / len(trends), 1) if trends else 0,
                            "avg_decisions_made_per_period": round(sum(t["decisions_made"]["total"] for t in trends) / len(trends), 1) if trends else 0,
                            "avg_leave_utilization_per_period": round(sum(t["leave_utilization"]["total_days_taken"] for t in trends) / len(trends), 1) if trends else 0,
                            "avg_approval_rate": round(sum(t["decisions_made"]["approval_rate"] for t in trends) / len(trends), 2) if trends else 0
                        }
                    }
                }
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting leave trends: {e}")
        return jsonify({"error": "Internal server error"}), 500

@leave_analytics_api.route('/api/leave/analytics/by-department', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_leave_by_department():
    """
    Get comprehensive leave analytics by department with detailed HR insights.

    Query parameters:
    - company_id: Required
    - year: Specific year (default: current year)
    - month: Specific month (optional)
    - include_trends: Include monthly trends per department (default: false)

    Returns detailed department analysis including:
    - Applications breakdown by status per department
    - Approval and rejection rates per department
    - Days requested vs approved per department
    - Processing efficiency metrics per department
    - Top 5 leave applicants per department
    - Timeline analysis (first/last applications)
    - Department ranking and performance insights
    - Summary statistics and comparative analysis
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        year = int(request.args.get('year', datetime.now().year))
        month = request.args.get('month')
        include_trends = request.args.get('include_trends', 'false').lower() == 'true'

        if month:
            month = int(month)

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Enhanced department analytics query
            from application.Models.employees.department import Department
            query = session.query(
                Employee.department_id,
                Department.name.label('department_name'),
                func.count(LeaveRequest.request_id).label('total_applications'),
                func.count(case((LeaveRequest.status == 'approved', 1))).label('approved_applications'),
                func.count(case((LeaveRequest.status == 'pending', 1))).label('pending_applications'),
                func.count(case((LeaveRequest.status == 'rejected', 1))).label('rejected_applications'),
                func.count(case((LeaveRequest.status == 'cancelled', 1))).label('cancelled_applications'),
                func.sum(LeaveRequest.total_days).label('total_days_requested'),
                func.sum(case((LeaveRequest.status == 'approved', LeaveRequest.total_days))).label('total_days_approved'),
                func.avg(LeaveRequest.total_days).label('avg_leave_duration'),
                func.count(func.distinct(LeaveRequest.employee_id)).label('unique_employees_on_leave'),
                func.min(LeaveRequest.created_at).label('first_application'),
                func.max(LeaveRequest.created_at).label('last_application'),
                func.avg(LeaveRequest.approved_at - LeaveRequest.created_at).label('avg_processing_time')
            ).join(LeaveRequest, Employee.employee_id == LeaveRequest.employee_id).outerjoin(Department, Employee.department_id == Department.department_id)

            # Apply date filters
            if month:
                query = query.filter(
                    extract('year', LeaveRequest.created_at) == year,
                    extract('month', LeaveRequest.created_at) == month
                )
            else:
                query = query.filter(extract('year', LeaveRequest.created_at) == year)

            department_stats = query.group_by(
                Employee.department_id, Department.name
            ).all()

            # Calculate department trends if requested
            department_trends = {}
            if include_trends:
                for dept_stat in department_stats:
                    dept_id = dept_stat.department_id
                    monthly_trends = []

                    for month_num in range(1, 13):
                        month_stats = session.query(
                            func.count(LeaveRequest.request_id).label('applications'),
                            func.sum(LeaveRequest.total_days).label('days_taken')
                        ).join(Employee, LeaveRequest.employee_id == Employee.employee_id).filter(
                            Employee.department_id == dept_id,
                            extract('year', LeaveRequest.created_at) == year,
                            extract('month', LeaveRequest.created_at) == month_num,
                            LeaveRequest.status == 'approved'
                        ).first()

                        monthly_trends.append({
                            "month": month_num,
                            "month_name": calendar.month_name[month_num],
                            "applications": month_stats.applications or 0,
                            "days_taken": float(month_stats.days_taken or 0)
                        })

                    department_trends[str(dept_id)] = monthly_trends

            # Get top employees per department for detailed insights
            top_employees_per_dept = {}
            for stat in department_stats:
                if stat.department_id:
                    top_employees = session.query(
                        Employee.employee_id,
                        Employee.first_name,
                        Employee.last_name,
                        func.count(LeaveRequest.request_id).label('applications'),
                        func.count(case((LeaveRequest.status == 'approved', 1))).label('approved'),
                        func.sum(LeaveRequest.total_days).label('total_days')
                    ).join(LeaveRequest, Employee.employee_id == LeaveRequest.employee_id).filter(
                        Employee.department_id == stat.department_id
                    )

                    if month:
                        top_employees = top_employees.filter(
                            extract('year', LeaveRequest.created_at) == year,
                            extract('month', LeaveRequest.created_at) == month
                        )
                    else:
                        top_employees = top_employees.filter(extract('year', LeaveRequest.created_at) == year)

                    top_employees = top_employees.group_by(
                        Employee.employee_id, Employee.first_name, Employee.last_name
                    ).order_by(func.count(LeaveRequest.request_id).desc()).limit(5).all()

                    top_employees_per_dept[stat.department_id] = [
                        {
                            "employee_id": str(emp.employee_id),
                            "employee_name": f"{emp.first_name} {emp.last_name}",
                            "applications": emp.applications,
                            "approved": emp.approved,
                            "total_days": float(emp.total_days or 0),
                            "approval_rate": round(emp.approved / emp.applications * 100, 2) if emp.applications > 0 else 0
                        } for emp in top_employees
                    ]

            # Format results with enhanced insights
            department_analytics = []

            for stat in department_stats:
                avg_processing_days = float(stat.avg_processing_time.total_seconds() / 86400) if stat.avg_processing_time else 0

                dept_data = {
                    "department_id": str(stat.department_id) if stat.department_id else None,
                    "department_name": stat.department_name or "Unassigned",
                    "applications": {
                        "total": stat.total_applications,
                        "approved": stat.approved_applications,
                        "pending": stat.pending_applications,
                        "rejected": stat.rejected_applications,
                        "cancelled": stat.cancelled_applications
                    },
                    "rates": {
                        "approval_rate": round(stat.approved_applications / stat.total_applications * 100, 2) if stat.total_applications > 0 else 0,
                        "rejection_rate": round(stat.rejected_applications / stat.total_applications * 100, 2) if stat.total_applications > 0 else 0
                    },
                    "days": {
                        "total_days_requested": float(stat.total_days_requested or 0),
                        "total_days_approved": float(stat.total_days_approved or 0),
                        "avg_leave_duration": round(float(stat.avg_leave_duration or 0), 1),
                        "avg_processing_days": round(avg_processing_days, 1)
                    },
                    "employees": {
                        "unique_applicants": stat.unique_employees_on_leave,
                        "top_applicants": top_employees_per_dept.get(stat.department_id, [])
                    },
                    "timeline": {
                        "first_application": stat.first_application.strftime('%Y-%m-%d %H:%M:%S') if stat.first_application else None,
                        "last_application": stat.last_application.strftime('%Y-%m-%d %H:%M:%S') if stat.last_application else None
                    }
                }

                if include_trends:
                    dept_data["monthly_trends"] = department_trends.get(str(stat.department_id), [])

                department_analytics.append(dept_data)

            # Sort by total days approved (highest first)
            department_analytics.sort(key=lambda x: x["days"]["total_days_approved"], reverse=True)

            # Calculate summary insights
            total_applications = sum(dept["applications"]["total"] for dept in department_analytics)
            total_approved = sum(dept["applications"]["approved"] for dept in department_analytics)
            total_days_taken = sum(dept["days"]["total_days_approved"] for dept in department_analytics)

            # Find top performing departments
            busiest_dept = max(department_analytics, key=lambda x: x["applications"]["total"]) if department_analytics else None
            highest_approval_dept = max(department_analytics, key=lambda x: x["rates"]["approval_rate"]) if department_analytics else None
            most_days_dept = max(department_analytics, key=lambda x: x["days"]["total_days_approved"]) if department_analytics else None
            most_efficient_dept = min(department_analytics, key=lambda x: x["days"]["avg_processing_days"] if x["days"]["avg_processing_days"] > 0 else float('inf')) if department_analytics else None

            return jsonify({
                "success": True,
                "data": {
                    "period_info": {
                        "period": f"{year}-{month:02d}" if month else str(year),
                        "year": year,
                        "month": month
                    },
                    "summary": {
                        "total_departments": len(department_analytics),
                        "total_applications": total_applications,
                        "total_approved": total_approved,
                        "overall_approval_rate": round(total_approved / total_applications * 100, 2) if total_applications > 0 else 0,
                        "total_days_taken": total_days_taken
                    },
                    "department_analytics": department_analytics,
                    "insights": {
                        "busiest_department": {
                            "name": busiest_dept["department_name"] if busiest_dept else None,
                            "applications": busiest_dept["applications"]["total"] if busiest_dept else 0
                        },
                        "highest_approval_rate": {
                            "name": highest_approval_dept["department_name"] if highest_approval_dept else None,
                            "rate": highest_approval_dept["rates"]["approval_rate"] if highest_approval_dept else 0
                        },
                        "most_days_taken": {
                            "name": most_days_dept["department_name"] if most_days_dept else None,
                            "days": most_days_dept["days"]["total_days_approved"] if most_days_dept else 0
                        },
                        "most_efficient_processing": {
                            "name": most_efficient_dept["department_name"] if most_efficient_dept and most_efficient_dept["days"]["avg_processing_days"] > 0 else None,
                            "avg_days": most_efficient_dept["days"]["avg_processing_days"] if most_efficient_dept and most_efficient_dept["days"]["avg_processing_days"] > 0 else 0
                        }
                    },
                    "includes_trends": include_trends
                }
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting department leave analytics: {e}")
        return jsonify({"error": "Internal server error"}), 500

@leave_analytics_api.route('/api/leave/analytics/balance-analysis', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_leave_balance_analysis():
    """
    Get comprehensive leave balance analysis with risk assessment and utilization insights.

    Query parameters:
    - company_id: Required
    - department_id: Filter by department (optional)
    - leave_type: Filter by leave type (optional)

    Returns detailed balance analysis including:
    - Balance summary across all employees
    - Leave type analysis with utilization ranges
    - Department-wise balance patterns
    - Risk categorization (burnout, underutilization, expiry risk)
    - Employee-level balance details and recommendations
    - Strategic insights for HR policy decisions
    - Wellness program recommendations based on utilization patterns
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        department_id = request.args.get('department_id')
        leave_type = request.args.get('leave_type')

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Base query for balance analysis
            from application.Models.employees.department import Department
            query = session.query(
                Employee.employee_id,
                Employee.first_name,
                Employee.last_name,
                Department.name.label('department_name'),
                LeaveType.name.label('leave_type_name'),
                LeaveBalance.carried_over_days,
                LeaveBalance.total_days,
                LeaveBalance.used_days,
                LeaveBalance.pending_days,
                (LeaveBalance.total_days - LeaveBalance.used_days - LeaveBalance.pending_days).label('available_balance')
            ).join(LeaveBalance, Employee.employee_id == LeaveBalance.employee_id).join(LeaveType, LeaveBalance.leave_type_id == LeaveType.leave_type_id).outerjoin(Department, Employee.department_id == Department.department_id)

            # Apply filters
            if department_id:
                query = query.filter(Employee.department_id == department_id)

            if leave_type:
                query = query.filter(LeaveType.name == leave_type)

            balance_data = query.all()

            # Analyze balance patterns
            high_balance_employees = []
            low_utilization_employees = []
            over_utilized_employees = []

            balance_summary = {
                "total_employees": 0,
                "total_accrued": 0,
                "total_taken": 0,
                "total_available": 0,
                "average_utilization": 0
            }

            for balance in balance_data:
                utilization_rate = (balance.used_days / balance.total_days * 100) if balance.total_days > 0 else 0

                balance_summary["total_employees"] += 1
                balance_summary["total_accrued"] += float(balance.total_days)
                balance_summary["total_taken"] += float(balance.used_days)
                balance_summary["total_available"] += float(balance.available_balance)

                employee_data = {
                    "employee_id": str(balance.employee_id),
                    "employee_name": f"{balance.first_name} {balance.last_name}",
                    "department": balance.department_name,
                    "leave_type": balance.leave_type_name,
                    "carried_over_days": float(balance.carried_over_days),
                    "total_days": float(balance.total_days),
                    "used_days": float(balance.used_days),
                    "pending_days": float(balance.pending_days),
                    "available_balance": float(balance.available_balance),
                    "utilization_rate": round(utilization_rate, 2)
                }

                # Categorize employees
                if balance.available_balance > 15:  # High unused balance
                    high_balance_employees.append(employee_data)

                if utilization_rate < 30:  # Low utilization
                    low_utilization_employees.append(employee_data)

                if utilization_rate > 90:  # High utilization
                    over_utilized_employees.append(employee_data)

            # Calculate averages
            if balance_summary["total_employees"] > 0:
                balance_summary["average_utilization"] = round(
                    balance_summary["total_taken"] / balance_summary["total_accrued"] * 100, 2
                ) if balance_summary["total_accrued"] > 0 else 0

            # Enhanced leave type analysis
            leave_type_analysis = session.query(
                LeaveType.name.label('leave_type'),
                func.count(LeaveBalance.employee_id).label('employee_count'),
                func.sum(LeaveBalance.total_days).label('total_accrued'),
                func.sum(LeaveBalance.used_days).label('total_taken'),
                func.sum(LeaveBalance.pending_days).label('total_pending'),
                func.sum(LeaveBalance.total_days - LeaveBalance.used_days - LeaveBalance.pending_days).label('total_available'),
                func.avg(LeaveBalance.used_days / LeaveBalance.total_days * 100).label('avg_utilization'),
                func.min(LeaveBalance.used_days / LeaveBalance.total_days * 100).label('min_utilization'),
                func.max(LeaveBalance.used_days / LeaveBalance.total_days * 100).label('max_utilization')
            ).join(LeaveType, LeaveBalance.leave_type_id == LeaveType.leave_type_id).group_by(LeaveType.name).all()

            # Department-wise balance analysis
            dept_balance_analysis = session.query(
                Department.name.label('department_name'),
                func.count(LeaveBalance.employee_id).label('employee_count'),
                func.sum(LeaveBalance.total_days).label('total_accrued'),
                func.sum(LeaveBalance.used_days).label('total_taken'),
                func.sum(LeaveBalance.pending_days).label('total_pending'),
                func.avg(LeaveBalance.used_days / LeaveBalance.total_days * 100).label('avg_utilization')
            ).join(Employee, LeaveBalance.employee_id == Employee.employee_id).outerjoin(Department, Employee.department_id == Department.department_id).group_by(Department.name).all()

            # Risk analysis - employees with concerning patterns
            burnout_risk_employees = []
            underutilization_employees = []
            balance_expiry_risk = []

            for balance in balance_data:
                utilization_rate = (balance.used_days / balance.total_days * 100) if balance.total_days > 0 else 0

                employee_data = {
                    "employee_id": str(balance.employee_id),
                    "employee_name": f"{balance.first_name} {balance.last_name}",
                    "department": balance.department_name,
                    "leave_type": balance.leave_type_name,
                    "total_days": float(balance.total_days),
                    "used_days": float(balance.used_days),
                    "pending_days": float(balance.pending_days),
                    "available_balance": float(balance.available_balance),
                    "utilization_rate": round(utilization_rate, 2)
                }

                # Risk categorization with more nuanced criteria
                if utilization_rate > 85 and balance.available_balance < 3:  # High utilization + low balance
                    burnout_risk_employees.append(employee_data)

                if utilization_rate < 20 and balance.available_balance > 10:  # Very low utilization + high balance
                    underutilization_employees.append(employee_data)

                if balance.available_balance > 20:  # Risk of losing leave days
                    balance_expiry_risk.append(employee_data)

            return jsonify({
                "success": True,
                "data": {
                    "balance_summary": balance_summary,
                    "leave_type_analysis": [
                        {
                            "leave_type": analysis.leave_type,
                            "employee_count": analysis.employee_count,
                            "balances": {
                                "total_accrued": float(analysis.total_accrued or 0),
                                "total_taken": float(analysis.total_taken or 0),
                                "total_pending": float(analysis.total_pending or 0),
                                "total_available": float(analysis.total_available or 0)
                            },
                            "utilization": {
                                "average": round(float(analysis.avg_utilization or 0), 2),
                                "minimum": round(float(analysis.min_utilization or 0), 2),
                                "maximum": round(float(analysis.max_utilization or 0), 2)
                            }
                        } for analysis in leave_type_analysis
                    ],
                    "department_analysis": [
                        {
                            "department_name": dept.department_name or "Unassigned",
                            "employee_count": dept.employee_count,
                            "balances": {
                                "total_accrued": float(dept.total_accrued or 0),
                                "total_taken": float(dept.total_taken or 0),
                                "total_pending": float(dept.total_pending or 0)
                            },
                            "avg_utilization": round(float(dept.avg_utilization or 0), 2)
                        } for dept in dept_balance_analysis
                    ],
                    "risk_categories": {
                        "burnout_risk": {
                            "count": len(burnout_risk_employees),
                            "employees": burnout_risk_employees[:15],
                            "description": "High utilization (>85%) with low remaining balance (<3 days)"
                        },
                        "underutilization": {
                            "count": len(underutilization_employees),
                            "employees": underutilization_employees[:15],
                            "description": "Very low utilization (<20%) with high available balance (>10 days)"
                        },
                        "balance_expiry_risk": {
                            "count": len(balance_expiry_risk),
                            "employees": balance_expiry_risk[:15],
                            "description": "High unused balance (>20 days) at risk of expiry"
                        },
                        "legacy_categories": {
                            "high_balance_employees": high_balance_employees[:20],
                            "low_utilization_employees": low_utilization_employees[:20],
                            "over_utilized_employees": over_utilized_employees[:20]
                        }
                    },
                    "insights": {
                        "total_risk_employees": len(burnout_risk_employees) + len(underutilization_employees) + len(balance_expiry_risk),
                        "highest_utilization_dept": max(dept_balance_analysis, key=lambda x: x.avg_utilization or 0).department_name if dept_balance_analysis else None,
                        "lowest_utilization_dept": min(dept_balance_analysis, key=lambda x: x.avg_utilization or 100).department_name if dept_balance_analysis else None,
                        "most_popular_leave_type": max(leave_type_analysis, key=lambda x: x.employee_count).leave_type if leave_type_analysis else None
                    },
                    "recommendations": {
                        "immediate_action": {
                            "encourage_leave": len(underutilization_employees),
                            "monitor_burnout": len(burnout_risk_employees),
                            "balance_planning": len(balance_expiry_risk)
                        },
                        "policy_review": {
                            "needed": len(balance_expiry_risk) > balance_summary["total_employees"] * 0.15,
                            "reason": "High number of employees with expiry risk suggests policy adjustment needed"
                        },
                        "wellness_program": {
                            "needed": len(burnout_risk_employees) > balance_summary["total_employees"] * 0.1,
                            "reason": "Significant number of employees at burnout risk"
                        }
                    }
                }
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting leave balance analysis: {e}")
        return jsonify({"error": "Internal server error"}), 500
