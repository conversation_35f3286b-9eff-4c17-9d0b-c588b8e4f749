from flask import Blueprint, request, jsonify, current_app as app, g
from application.Models.employees import Employee
from application.Models.company import Company
from application.Models.Msg import Msg
from application.Models.employees.leave_audit_log import LeaveAuditLog
import jsons
from datetime import datetime, timedelta
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required

leave_audit_api = Blueprint('leave_audit_api', __name__)

@leave_audit_api.route('/api/leave/audit/employee/<employee_id>', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_employee_audit_trail(employee_id):
    """
    Get audit trail for a specific employee.
    
    Path Parameters:
    - employee_id: Required. The ID of the employee.
    
    Query Parameters:
    - company_id: Required. The ID of the company.
    - limit: Optional. Number of records to return (default: 50, max: 200).
    - offset: Optional. Number of records to skip (default: 0).
    - action_type: Optional. Filter by specific action type.
    - start_date: Optional. Filter from this date (YYYY-MM-DD).
    - end_date: Optional. Filter to this date (YYYY-MM-DD).
    """
    try:
        company_id = request.args.get('company_id')
        limit = min(int(request.args.get('limit', 50)), 200)  # Max 200 records
        offset = int(request.args.get('offset', 0))
        action_type = request.args.get('action_type')
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400
        
        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": f"Company with ID {company_id} not found"}), 404
        
        # Connect to the tenant database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Verify employee exists
            employee = Employee.get_employee_by_id(session, employee_id)
            if not employee:
                return jsonify({"error": f"Employee with ID {employee_id} not found"}), 404
            
            # Build query
            query = session.query(LeaveAuditLog).filter(LeaveAuditLog.employee_id == employee_id)
            
            # Apply filters
            if action_type:
                query = query.filter(LeaveAuditLog.action_type == action_type)
            
            if start_date_str:
                try:
                    start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
                    query = query.filter(LeaveAuditLog.created_at >= start_date)
                except ValueError:
                    return jsonify({"error": "Invalid start_date format. Use YYYY-MM-DD"}), 400
            
            if end_date_str:
                try:
                    end_date = datetime.strptime(end_date_str, '%Y-%m-%d') + timedelta(days=1)
                    query = query.filter(LeaveAuditLog.created_at < end_date)
                except ValueError:
                    return jsonify({"error": "Invalid end_date format. Use YYYY-MM-DD"}), 400
            
            # Get total count for pagination
            total_count = query.count()
            
            # Apply pagination and ordering
            audit_logs = query.order_by(LeaveAuditLog.created_at.desc())\
                             .limit(limit).offset(offset).all()
            
            # Convert to dictionaries
            audit_data = [log.to_dict() for log in audit_logs]
            
            return jsons.dump(Msg.success().add("audit_trail", {
                "employee_id": employee_id,
                "employee_name": f"{employee.first_name} {employee.last_name}",
                "total_records": total_count,
                "limit": limit,
                "offset": offset,
                "records": audit_data
            }))
    
    except Exception as e:
        app.logger.error(f"Error getting employee audit trail: {e}")
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

@leave_audit_api.route('/api/leave/audit/recent', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_recent_audit_logs():
    """
    Get recent audit logs across all employees.
    
    Query Parameters:
    - company_id: Required. The ID of the company.
    - hours: Optional. Number of hours to look back (default: 24, max: 168).
    - limit: Optional. Number of records to return (default: 100, max: 500).
    - action_type: Optional. Filter by specific action type.
    """
    try:
        company_id = request.args.get('company_id')
        hours = min(int(request.args.get('hours', 24)), 168)  # Max 1 week
        limit = min(int(request.args.get('limit', 100)), 500)  # Max 500 records
        action_type = request.args.get('action_type')
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400
        
        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": f"Company with ID {company_id} not found"}), 404
        
        # Connect to the tenant database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Build query
            cutoff_time = datetime.now() - timedelta(hours=hours)
            query = session.query(LeaveAuditLog).filter(LeaveAuditLog.created_at >= cutoff_time)
            
            # Apply action type filter
            if action_type:
                query = query.filter(LeaveAuditLog.action_type == action_type)
            
            # Get recent logs
            audit_logs = query.order_by(LeaveAuditLog.created_at.desc())\
                             .limit(limit).all()
            
            # Convert to dictionaries and add employee names
            audit_data = []
            for log in audit_logs:
                log_dict = log.to_dict()
                
                # Add employee name if available
                if log.employee:
                    log_dict['employee_name'] = f"{log.employee.first_name} {log.employee.last_name}"
                
                audit_data.append(log_dict)
            
            return jsons.dump(Msg.success().add("recent_audit_logs", {
                "hours_back": hours,
                "total_records": len(audit_data),
                "records": audit_data
            }))
    
    except Exception as e:
        app.logger.error(f"Error getting recent audit logs: {e}")
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

@leave_audit_api.route('/api/leave/audit/actions', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_audit_action_types():
    """
    Get all available audit action types for filtering.
    
    Query Parameters:
    - company_id: Required. The ID of the company.
    """
    try:
        company_id = request.args.get('company_id')
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400
        
        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": f"Company with ID {company_id} not found"}), 404
        
        # Connect to the tenant database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Get distinct action types
            action_types = session.query(LeaveAuditLog.action_type)\
                                 .distinct()\
                                 .order_by(LeaveAuditLog.action_type)\
                                 .all()
            
            action_list = [action[0] for action in action_types]
            
            return jsons.dump(Msg.success().add("action_types", action_list))
    
    except Exception as e:
        app.logger.error(f"Error getting audit action types: {e}")
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

@leave_audit_api.route('/api/leave/audit/summary', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_audit_summary():
    """
    Get audit activity summary for dashboard.
    
    Query Parameters:
    - company_id: Required. The ID of the company.
    - days: Optional. Number of days to analyze (default: 7, max: 90).
    """
    try:
        company_id = request.args.get('company_id')
        days = min(int(request.args.get('days', 7)), 90)  # Max 90 days
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400
        
        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": f"Company with ID {company_id} not found"}), 404
        
        # Connect to the tenant database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            from sqlalchemy import func, distinct
            
            cutoff_date = datetime.now() - timedelta(days=days)
            
            # Get activity summary
            total_actions = session.query(LeaveAuditLog)\
                                  .filter(LeaveAuditLog.created_at >= cutoff_date)\
                                  .count()
            
            # Actions by type
            actions_by_type = session.query(
                LeaveAuditLog.action_type,
                func.count(LeaveAuditLog.audit_id).label('count')
            ).filter(LeaveAuditLog.created_at >= cutoff_date)\
             .group_by(LeaveAuditLog.action_type)\
             .order_by(func.count(LeaveAuditLog.audit_id).desc())\
             .all()
            
            # Active users (who performed actions)
            active_users = session.query(distinct(LeaveAuditLog.performed_by))\
                                 .filter(LeaveAuditLog.created_at >= cutoff_date)\
                                 .count()
            
            # Affected employees
            affected_employees = session.query(distinct(LeaveAuditLog.employee_id))\
                                       .filter(LeaveAuditLog.created_at >= cutoff_date)\
                                       .count()
            
            # Daily activity (last 7 days)
            daily_activity = []
            for i in range(min(days, 7)):
                day_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=i)
                day_end = day_start + timedelta(days=1)
                
                day_count = session.query(LeaveAuditLog)\
                                  .filter(LeaveAuditLog.created_at >= day_start,
                                         LeaveAuditLog.created_at < day_end)\
                                  .count()
                
                daily_activity.append({
                    "date": day_start.strftime('%Y-%m-%d'),
                    "actions": day_count
                })
            
            summary = {
                "period_days": days,
                "total_actions": total_actions,
                "active_users": active_users,
                "affected_employees": affected_employees,
                "actions_by_type": [
                    {"action_type": action[0], "count": action[1]}
                    for action in actions_by_type
                ],
                "daily_activity": list(reversed(daily_activity))  # Most recent first
            }
            
            return jsons.dump(Msg.success().add("audit_summary", summary))
    
    except Exception as e:
        app.logger.error(f"Error getting audit summary: {e}")
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500
