from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from application.Models.employees import Employee, LeaveBalance
from application.Models.company import Company
from application.Models.Msg import Msg
from application.Services.leave_balance_manager import LeaveBalanceManager
from application.Models.employees.leave_audit_log import LeaveAuditLog
import jsons
from datetime import datetime
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required

leave_balance_doctor_api = Blueprint('leave_balance_doctor_api', __name__)

@leave_balance_doctor_api.route('/api/leave/balances/doctor', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def leave_balance_doctor():
    """
    Emergency fix tool for leave balance issues.
    
    Request Body:
    - company_id: Required. The ID of the company.
    - employee_id: Optional. Specific employee ID (if not provided, processes all employees).
    - year: Optional. Year to process (defaults to current year).
    - actions: Required. List of actions to perform:
        - 'initialize_missing': Create missing leave balances
        - 'fix_inconsistencies': Fix negative balances and calculation errors
        - 'remove_duplicates': Remove duplicate balance records
        - 'recalculate_all': Recalculate all balances based on current policies
    - dry_run: Optional. If true, only report what would be done without making changes.
    """
    try:
        data = request.get_json()
        company_id = data.get('company_id')
        employee_id = data.get('employee_id')
        year = data.get('year', datetime.now().year)
        actions = data.get('actions', [])
        dry_run = data.get('dry_run', False)
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400
        
        if not actions:
            return jsonify({"error": "actions list is required"}), 400
        
        valid_actions = ['initialize_missing', 'fix_inconsistencies', 'remove_duplicates', 'recalculate_all']
        invalid_actions = [action for action in actions if action not in valid_actions]
        if invalid_actions:
            return jsonify({
                "error": f"Invalid actions: {invalid_actions}. Valid actions: {valid_actions}"
            }), 400
        
        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": f"Company with ID {company_id} not found"}), 404
        
        # Get current user for audit logging
        current_user_id = g.user.get('user_id')
        
        # Connect to the tenant database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            results = {
                "company_id": company_id,
                "employee_id": employee_id,
                "year": year,
                "dry_run": dry_run,
                "actions_performed": [],
                "summary": {
                    "total_issues_found": 0,
                    "total_issues_fixed": 0,
                    "employees_processed": 0
                }
            }
            
            # Validate employee exists if specified
            if employee_id:
                employee = Employee.get_employee_by_id(session, employee_id)
                if not employee:
                    return jsonify({"error": f"Employee with ID {employee_id} not found"}), 404
            
            # Perform requested actions
            for action in actions:
                action_result = {"action": action, "success": False, "details": {}}
                
                try:
                    if action == 'initialize_missing':
                        if not dry_run:
                            result = LeaveBalanceManager.auto_initialize_missing_balances(
                                session, company_id, year, employee_id
                            )
                        else:
                            # Dry run - just count what would be created
                            result = _dry_run_initialize_missing(session, year, employee_id)
                        
                        action_result["success"] = result["success"]
                        action_result["details"] = result
                        if result["success"]:
                            results["summary"]["total_issues_fixed"] += len(result.get("created_balances", []))
                    
                    elif action == 'fix_inconsistencies':
                        if not dry_run:
                            result = LeaveBalanceManager.validate_and_fix_balances(
                                session, company_id, employee_id
                            )
                        else:
                            result = _dry_run_fix_inconsistencies(session, employee_id)
                        
                        action_result["success"] = result["success"]
                        action_result["details"] = result
                        if result["success"]:
                            results["summary"]["total_issues_fixed"] += len(result.get("fixed_issues", []))
                    
                    elif action == 'remove_duplicates':
                        if not dry_run:
                            result = LeaveBalanceManager.remove_duplicate_balances(session)
                        else:
                            result = LeaveBalanceManager.detect_duplicate_balances(session)
                        
                        action_result["success"] = result["success"]
                        action_result["details"] = result
                        if result["success"]:
                            if dry_run:
                                results["summary"]["total_issues_found"] += result.get("duplicates_found", 0)
                            else:
                                results["summary"]["total_issues_fixed"] += len(result.get("removed_balances", []))
                    
                    elif action == 'recalculate_all':
                        result = _recalculate_all_balances(session, company_id, year, employee_id, dry_run)
                        action_result["success"] = result["success"]
                        action_result["details"] = result
                        if result["success"]:
                            results["summary"]["total_issues_fixed"] += len(result.get("recalculated_balances", []))
                
                except Exception as e:
                    action_result["success"] = False
                    action_result["error"] = str(e)
                    app.logger.error(f"Error performing action {action}: {e}")
                
                results["actions_performed"].append(action_result)
            
            # Count employees processed
            if employee_id:
                results["summary"]["employees_processed"] = 1
            else:
                employee_count = session.query(Employee).filter(Employee.status == 'active').count()
                results["summary"]["employees_processed"] = employee_count
            
            # Log the doctor run if not dry run
            if not dry_run:
                LeaveAuditLog.log_action(
                    session=session,
                    employee_id=employee_id or uuid.uuid4(),  # Use dummy ID for company-wide actions
                    action_type='balance_doctor_run',
                    entity_type='leave_balance',
                    new_value={
                        "actions": actions,
                        "results_summary": results["summary"]
                    },
                    reason=f'Leave Balance Doctor run with actions: {", ".join(actions)}',
                    performed_by=current_user_id,
                    ip_address=request.remote_addr,
                    user_agent=request.headers.get('User-Agent')
                )
            
            return jsons.dump(Msg.success().add("doctor_results", results))
    
    except Exception as e:
        app.logger.error(f"Error in leave balance doctor: {e}")
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

@leave_balance_doctor_api.route('/api/leave/balances/health-check', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def leave_balance_health_check():
    """
    Perform a health check on leave balances without making any changes.
    
    Query Parameters:
    - company_id: Required. The ID of the company.
    - employee_id: Optional. Specific employee ID.
    - year: Optional. Year to check (defaults to current year).
    """
    try:
        company_id = request.args.get('company_id')
        employee_id = request.args.get('employee_id')
        year = request.args.get('year', datetime.now().year, type=int)
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400
        
        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": f"Company with ID {company_id} not found"}), 404
        
        # Connect to the tenant database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            health_report = {
                "company_id": company_id,
                "employee_id": employee_id,
                "year": year,
                "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "issues_found": [],
                "summary": {
                    "total_employees": 0,
                    "employees_with_balances": 0,
                    "employees_missing_balances": 0,
                    "negative_balances": 0,
                    "inconsistent_calculations": 0,
                    "duplicate_balances": 0
                }
            }
            
            # Get employees to check
            if employee_id:
                employees = session.query(Employee).filter(
                    Employee.employee_id == employee_id,
                    Employee.status == 'active'
                ).all()
            else:
                employees = session.query(Employee).filter(Employee.status == 'active').all()
            
            health_report["summary"]["total_employees"] = len(employees)
            
            # Check each employee
            for employee in employees:
                employee_issues = []
                
                # Check if employee has balances
                balances = session.query(LeaveBalance).filter(
                    LeaveBalance.employee_id == employee.employee_id,
                    LeaveBalance.year == year
                ).all()
                
                if balances:
                    health_report["summary"]["employees_with_balances"] += 1
                    
                    # Check each balance for issues
                    for balance in balances:
                        balance_issues = []
                        
                        # Calculate available days (not stored as attribute)
                        available_days = balance.total_days - balance.used_days - balance.pending_days

                        # Check for negative values
                        if available_days < 0:
                            balance_issues.append("negative_available_days")
                            health_report["summary"]["negative_balances"] += 1

                        if balance.used_days < 0:
                            balance_issues.append("negative_used_days")

                        if balance.pending_days < 0:
                            balance_issues.append("negative_pending_days")

                        # Check calculation consistency (available_days is calculated, so this check is redundant)
                        # The calculation is always consistent since we just calculated it above
                        
                        if balance_issues:
                            employee_issues.append({
                                "balance_id": str(balance.balance_id),
                                "leave_type_id": str(balance.leave_type_id),
                                "issues": balance_issues,
                                "current_values": {
                                    "total_days": float(balance.total_days),
                                    "used_days": float(balance.used_days),
                                    "pending_days": float(balance.pending_days),
                                    "available_days": float(available_days)
                                }
                            })
                
                else:
                    # Employee has no balances
                    if employee.hire_date:  # Only flag as issue if they have a hire date
                        health_report["summary"]["employees_missing_balances"] += 1
                        employee_issues.append({
                            "issue": "missing_leave_balances",
                            "hire_date": employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else None
                        })
                
                if employee_issues:
                    health_report["issues_found"].append({
                        "employee_id": str(employee.employee_id),
                        "employee_name": f"{employee.first_name} {employee.last_name}",
                        "hire_date": employee.hire_date.strftime('%Y-%m-%d') if employee.hire_date else None,
                        "issues": employee_issues
                    })
            
            # Check for duplicate balances
            duplicates_result = LeaveBalanceManager.detect_duplicate_balances(session)
            if duplicates_result["success"] and duplicates_result["duplicates_found"] > 0:
                health_report["summary"]["duplicate_balances"] = duplicates_result["duplicates_found"]
                health_report["duplicate_details"] = duplicates_result["duplicate_details"]
            
            return jsons.dump(Msg.success().add("health_report", health_report))
    
    except Exception as e:
        app.logger.error(f"Error in leave balance health check: {e}")
        return jsonify({"error": f"Internal server error: {str(e)}"}), 500

def _dry_run_initialize_missing(session, year, employee_id=None):
    """Dry run for initializing missing balances - just count what would be created."""
    try:
        from application.Models.employees import Employee, LeaveType, LeaveBalance

        # Get employees to check
        if employee_id:
            employees = session.query(Employee).filter(
                Employee.employee_id == employee_id,
                Employee.status == 'active'
            ).all()
        else:
            employees = session.query(Employee).filter(Employee.status == 'active').all()

        # Get all leave types
        leave_types = session.query(LeaveType).all()

        would_create = []
        skipped_employees = []

        for employee in employees:
            if not employee.hire_date:
                skipped_employees.append({
                    "employee_id": str(employee.employee_id),
                    "name": f"{employee.first_name} {employee.last_name}",
                    "reason": "No hire_date set"
                })
                continue

            for leave_type in leave_types:
                # Check if balance already exists
                existing_balance = session.query(LeaveBalance).filter(
                    LeaveBalance.employee_id == employee.employee_id,
                    LeaveBalance.leave_type_id == leave_type.leave_type_id,
                    LeaveBalance.year == year
                ).first()

                if not existing_balance:
                    would_create.append({
                        "employee_id": str(employee.employee_id),
                        "employee_name": f"{employee.first_name} {employee.last_name}",
                        "leave_type": leave_type.name,
                        "year": year
                    })

        return {
            "success": True,
            "message": f"Would create {len(would_create)} leave balances",
            "created_balances": would_create,
            "skipped_employees": skipped_employees
        }

    except Exception as e:
        return {"success": False, "message": f"Error: {str(e)}"}

def _dry_run_fix_inconsistencies(session, employee_id=None):
    """Dry run for fixing inconsistencies - just report what would be fixed."""
    try:
        # Get balances to validate
        query = session.query(LeaveBalance)
        if employee_id:
            query = query.filter(LeaveBalance.employee_id == employee_id)

        balances = query.all()
        would_fix = []

        for balance in balances:
            issues_found = []

            # Check for negative available days
            calculated_available = balance.total_days - balance.used_days - balance.pending_days
            if calculated_available != balance.available_days:
                issues_found.append("incorrect_available_days")

            # Check for negative values
            if balance.used_days < 0:
                issues_found.append("negative_used_days")

            if balance.pending_days < 0:
                issues_found.append("negative_pending_days")

            if balance.available_days < 0:
                issues_found.append("negative_available_days")

            if issues_found:
                would_fix.append({
                    "balance_id": str(balance.balance_id),
                    "employee_id": str(balance.employee_id),
                    "leave_type_id": str(balance.leave_type_id),
                    "year": balance.year,
                    "issues_found": issues_found
                })

        return {
            "success": True,
            "message": f"Would fix {len(would_fix)} balance issues",
            "fixed_issues": would_fix
        }

    except Exception as e:
        return {"success": False, "message": f"Error: {str(e)}"}

def _recalculate_all_balances(session, company_id, year, employee_id=None, dry_run=False):
    """Recalculate all balances based on current policies."""
    try:
        from application.Models.employees import Employee, LeaveBalance, LeavePolicy
        from application.Models.country import Country
        from application.Services.leave_balance_manager import LeaveBalanceManager

        # Get Rwanda country ID
        with app.app_context():
            rwanda = Country.get_country_by_code('RW')
            if not rwanda:
                return {"success": False, "message": "Rwanda country not found"}
            rwanda_id = rwanda.country_id

        # Get employees to process
        if employee_id:
            employees = session.query(Employee).filter(
                Employee.employee_id == employee_id,
                Employee.status == 'active'
            ).all()
        else:
            employees = session.query(Employee).filter(Employee.status == 'active').all()

        recalculated_balances = []

        for employee in employees:
            if not employee.hire_date:
                continue

            # Get all balances for this employee
            balances = session.query(LeaveBalance).filter(
                LeaveBalance.employee_id == employee.employee_id,
                LeaveBalance.year == year
            ).all()

            for balance in balances:
                # Get leave policy
                policy = session.query(LeavePolicy).filter(
                    LeavePolicy.leave_type_id == balance.leave_type_id
                ).first()

                if not policy:
                    continue

                # Recalculate total days based on current policy
                new_total_days = LeaveBalanceManager._calculate_prorated_leave(
                    employee.hire_date, year, policy.days_allowed, policy.is_prorated
                )

                old_total = balance.total_days

                # Calculate available days
                calculated_available = new_total_days - balance.used_days - balance.pending_days

                if not dry_run:
                    # Update the balance
                    balance.total_days = new_total_days

                    # Ensure available days don't go negative by adjusting total_days
                    if calculated_available < 0:
                        balance.total_days = balance.used_days + balance.pending_days
                        calculated_available = 0

                    session.commit()

                recalculated_balances.append({
                    "employee_id": str(employee.employee_id),
                    "employee_name": f"{employee.first_name} {employee.last_name}",
                    "leave_type_id": str(balance.leave_type_id),
                    "old_total_days": float(old_total),
                    "new_total_days": float(new_total_days if dry_run else balance.total_days),
                    "available_days": float(calculated_available)
                })

        action = "Would recalculate" if dry_run else "Recalculated"
        return {
            "success": True,
            "message": f"{action} {len(recalculated_balances)} leave balances",
            "recalculated_balances": recalculated_balances
        }

    except Exception as e:
        return {"success": False, "message": f"Error: {str(e)}"}
