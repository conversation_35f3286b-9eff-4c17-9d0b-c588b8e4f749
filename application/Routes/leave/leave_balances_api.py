from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from application.Models.employees import Leave<PERSON>alance, LeaveType, Employee
from application.Models.company import Company
from application.Models.Msg import Msg
import jsons
from datetime import datetime
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.Helpers.helper_methods import HelperMethods

leave_balances_api = Blueprint('leave_balances_api', __name__)

@leave_balances_api.route('/api/leave/balances', methods=['GET'])
@token_required
def get_leave_balances():
    """
    Get leave balances for an employee or all employees (HR/Admin view).

    Query Parameters:
    - company_id: Required. The ID of the company.
    - employee_id: Optional. The ID of the employee to get balances for. If not provided, returns all balances (HR/Admin only).
    - year: Optional. The year to get balances for. Defaults to current year.
    """
    company_id = request.args.get('company_id')
    employee_id = request.args.get('employee_id')
    year = request.args.get('year', datetime.now().year)

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:

        if employee_id:
            # Get balances for specific employee
            employee = Employee.get_employee_by_id(session, employee_id)
            if not employee:
                return jsonify({"message": f"Employee with ID {employee_id} not found"}), 404

            # Get leave balances for the employee
            balances = LeaveBalance.get_employee_balances(session, employee_id, year)
            balances_data = [balance.to_dict() for balance in balances]

            return jsons.dump(Msg.success()
                             .add("employee", employee.to_dict())
                             .add("leave_balances", balances_data))
        else:
            # Get all balances for the company (HR/Admin view)
            # Check if user has permission to view all balances
            current_user_role = g.user.get('role')
            if current_user_role not in ['admin', 'hr']:
                return jsonify({"message": "Employee ID is required for non-admin/HR users"}), 400

            # Get all balances for the company
            balances = session.query(LeaveBalance).filter_by(year=year).all()
            balances_data = []

            for balance in balances:
                balance_dict = balance.to_dict()
                # Add employee and leave type details for better frontend display
                if balance.employee:
                    balance_dict['employee_name'] = f"{balance.employee.first_name} {balance.employee.last_name}"
                if balance.leave_type:
                    balance_dict['leave_type_name'] = balance.leave_type.name
                    balance_dict['leave_type_code'] = balance.leave_type.code
                balances_data.append(balance_dict)

            return jsons.dump(Msg.success()
                             .add("year", year)
                             .add("total_balances", len(balances_data))
                             .add("leave_balances", balances_data))

@leave_balances_api.route('/api/leave/balances/<balance_id>', methods=['GET'])
@token_required
def get_leave_balance(balance_id):
    """
    Get a specific leave balance by ID.

    Path Parameters:
    - balance_id: Required. The ID of the leave balance to retrieve.

    Query Parameters:
    - company_id: Required. The ID of the company.
    """
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        balance = LeaveBalance.get_balance_by_id(session, balance_id)

        if not balance:
            return jsonify({"message": f"Leave balance with ID {balance_id} not found"}), 404

        return jsons.dump(Msg.success().add("leave_balance", balance.to_dict()))

@leave_balances_api.route('/api/leave/balances', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def create_leave_balance():
    """
    Create a new leave balance for an employee.
    
    Request Body:
    - company_id: Required. The ID of the company.
    - employee_id: Required. The ID of the employee.
    - leave_type_id: Required. The ID of the leave type.
    - year: Required. The year for which this balance applies.
    - total_days: Required. Total days allocated.
    - carried_over_days: Optional. Days carried over from previous year.
    """
    data = request.get_json()
    company_id = data.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Validate required fields
    required_fields = ['employee_id', 'leave_type_id', 'year', 'total_days']
    for field in required_fields:
        if not data.get(field):
            return jsonify({"message": f"{field.replace('_', ' ').title()} is required"}), 400

    # Prepare leave balance data
    balance_data = {
        'employee_id': data.get('employee_id'),
        'leave_type_id': data.get('leave_type_id'),
        'year': data.get('year'),
        'total_days': data.get('total_days'),
        'used_days': data.get('used_days', 0),
        'pending_days': data.get('pending_days', 0),
        'carried_over_days': data.get('carried_over_days', 0)
    }

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if the employee exists
        employee = Employee.get_employee_by_id(session, balance_data['employee_id'])
        if not employee:
            return jsonify({"message": f"Employee with ID {balance_data['employee_id']} not found"}), 404

        # Check if the leave type exists
        leave_type = LeaveType.get_leave_type_by_id(session, balance_data['leave_type_id'])
        if not leave_type:
            return jsonify({"message": f"Leave type with ID {balance_data['leave_type_id']} not found"}), 404

        # Check if a balance already exists for this employee, leave type, and year
        existing_balance = LeaveBalance.get_employee_balance(
            session, 
            balance_data['employee_id'], 
            balance_data['leave_type_id'], 
            balance_data['year']
        )
        
        if existing_balance:
            return jsonify({
                "message": f"A leave balance already exists for this employee, leave type, and year"
            }), 409  # 409 Conflict

        # Create the leave balance
        balance = LeaveBalance.create_balance(session, **balance_data)

        if not balance:
            return jsonify({"message": "Failed to create leave balance"}), 500

        return jsons.dump(Msg.success().add("leave_balance", balance.to_dict())), 201

@leave_balances_api.route('/api/leave/balances/<balance_id>', methods=['PATCH'])
@token_required
@roles_required('admin', 'hr')
def update_leave_balance(balance_id):
    """
    Update an existing leave balance.

    Path Parameters:
    - balance_id: Required. The ID of the leave balance to update.

    Request Body:
    - company_id: Required. The ID of the company.
    - total_days: Optional. Total days allocated.
    - used_days: Optional. Days used.
    - pending_days: Optional. Days pending approval.
    - carried_over_days: Optional. Days carried over from previous year.
    """
    try:
        app.logger.info(f"=== PATCH /api/leave/balances/{balance_id} endpoint called ===")

        data = request.get_json()
        app.logger.info(f"Request body: {data}")

        if not data:
            app.logger.error("No JSON data provided in request body")
            return jsonify({"message": "Request body is required"}), 400

        company_id = data.get('company_id')
        app.logger.info(f"Company ID: {company_id}, Balance ID: {balance_id}")

        if not company_id:
            app.logger.error("Company ID is missing from request body")
            return jsonify({"message": "Company ID is required"}), 400

        # Get the database name for the company
        app.logger.info(f"Getting database name for company_id: {company_id}")
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            app.logger.error(f"Company with ID {company_id} not found")
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404

        app.logger.info(f"Database name found: {database_name}")

        # Prepare leave balance data (only include fields that are provided)
        balance_data = {}
        for field in ['total_days', 'used_days', 'pending_days', 'carried_over_days']:
            if field in data:
                balance_data[field] = data.get(field)

        app.logger.info(f"Balance data to update: {balance_data}")

        # Connect to the database
        app.logger.info("Connecting to database...")
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            app.logger.info("Database session established")

            # Check if the balance exists
            app.logger.info(f"Looking for balance with ID: {balance_id}")
            balance = LeaveBalance.get_balance_by_id(session, balance_id)
            if not balance:
                app.logger.warning(f"Leave balance with ID {balance_id} not found")
                return jsonify({"message": f"Leave balance with ID {balance_id} not found"}), 404

            app.logger.info(f"Balance found: {balance}")

            # Update the balance
            app.logger.info("Updating balance...")
            updated_balance = LeaveBalance.update_balance(session, balance_id, **balance_data)

            if not updated_balance:
                app.logger.error("Failed to update leave balance")
                return jsonify({"message": "Failed to update leave balance"}), 500

            app.logger.info("Balance updated successfully")
            app.logger.info("Converting updated balance to dict...")
            balance_dict = updated_balance.to_dict()
            app.logger.info("Balance dict created successfully")

            result = Msg.success().add("leave_balance", balance_dict)
            app.logger.info("Creating response...")

            return jsons.dump(result)

    except Exception as e:
        app.logger.error(f"CRITICAL ERROR in update_leave_balance: {str(e)}")
        app.logger.error(f"Error type: {type(e)}")
        import traceback
        app.logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({"message": f"Internal server error: {str(e)}"}), 500

@leave_balances_api.route('/api/leave/balances/adjust', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def adjust_leave_balance():
    """
    Adjust a leave balance by adding or subtracting days.
    
    Request Body:
    - company_id: Required. The ID of the company.
    - employee_id: Required. The ID of the employee.
    - leave_type_id: Required. The ID of the leave type.
    - adjustment_days: Required. Number of days to add (positive) or subtract (negative).
    - reason: Optional. Reason for the adjustment.
    - year: Optional. The year for which to adjust the balance. Defaults to current year.
    """
    data = request.get_json()
    company_id = data.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Validate required fields
    required_fields = ['employee_id', 'leave_type_id', 'adjustment_days']
    for field in required_fields:
        if field not in data:
            return jsonify({"message": f"{field.replace('_', ' ').title()} is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if the employee exists
        employee = Employee.get_employee_by_id(session, data['employee_id'])
        if not employee:
            return jsonify({"message": f"Employee with ID {data['employee_id']} not found"}), 404

        # Check if the leave type exists
        leave_type = LeaveType.get_leave_type_by_id(session, data['leave_type_id'])
        if not leave_type:
            return jsonify({"message": f"Leave type with ID {data['leave_type_id']} not found"}), 404

        # Get the year (default to current year)
        year = data.get('year', datetime.now().year)

        # Check if a balance exists for this employee, leave type, and year
        balance = LeaveBalance.get_employee_balance(session, data['employee_id'], data['leave_type_id'], year)
        
        if not balance:
            return jsonify({
                "message": f"No leave balance found for this employee, leave type, and year"
            }), 404

        # Adjust the balance
        adjusted_balance = LeaveBalance.adjust_balance(
            session, 
            data['employee_id'], 
            data['leave_type_id'], 
            data['adjustment_days'], 
            data.get('reason'), 
            year
        )

        if not adjusted_balance:
            return jsonify({"message": "Failed to adjust leave balance"}), 500

        return jsons.dump(Msg.success().add("leave_balance", adjusted_balance.to_dict()))
