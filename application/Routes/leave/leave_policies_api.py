from flask import Blueprint, request, jsonify, current_app as app
import uuid
from application.Models.employees import LeavePolicy, LeaveType
from application.Models.company import Company
from application.Models.country import Country
from application.Models.Msg import Msg
import jsons
from datetime import datetime
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.Helpers.helper_methods import HelperMethods

leave_policies_api = Blueprint('leave_policies_api', __name__)

@leave_policies_api.route('/api/leave/policies', methods=['GET'])
@token_required
def get_leave_policies():
    """
    Get all leave policies for a company.
    
    Query Parameters:
    - company_id: Required. The ID of the company to get leave policies for.
    - country_id: Optional. Filter policies by country ID.
    """
    company_id = request.args.get('company_id')
    country_id = request.args.get('country_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        if country_id:
            # Get policies for a specific country
            policies = LeavePolicy.get_policies_for_country(session, country_id)
        else:
            # Get all policies
            policies = session.query(LeavePolicy).all()
        
        # Convert to dictionaries and include leave type name
        policies_data = []
        for policy in policies:
            policy_dict = policy.to_dict()
            leave_type = LeaveType.get_leave_type_by_id(session, policy.leave_type_id)
            if leave_type:
                policy_dict['leave_type_name'] = leave_type.name
            policies_data.append(policy_dict)

        return jsons.dump(Msg.success().add("leave_policies", policies_data))

@leave_policies_api.route('/api/leave/policies/<policy_id>', methods=['GET'])
@token_required
def get_leave_policy(policy_id):
    """
    Get a specific leave policy by ID.
    
    Path Parameters:
    - policy_id: Required. The ID of the leave policy to retrieve.
    
    Query Parameters:
    - company_id: Required. The ID of the company.
    """
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        policy = LeavePolicy.get_policy_by_id(session, policy_id)

        if not policy:
            return jsonify({"message": f"Leave policy with ID {policy_id} not found"}), 404

        # Get the leave type name
        policy_dict = policy.to_dict()
        leave_type = LeaveType.get_leave_type_by_id(session, policy.leave_type_id)
        if leave_type:
            policy_dict['leave_type_name'] = leave_type.name

        # Get the country name from the central database
        with app.app_context():
            country = Country.get_country_by_id(policy.country_id)
            if country:
                policy_dict['country_name'] = country.name

        return jsons.dump(Msg.success().add("leave_policy", policy_dict))

@leave_policies_api.route('/api/leave/policies', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def create_leave_policy():
    """
    Create a new leave policy.
    
    Request Body:
    - company_id: Required. The ID of the company.
    - leave_type_id: Required. The ID of the leave type.
    - country_id: Required. The ID of the country.
    - days_allowed: Required. Number of days allowed for this leave type.
    - accrual_period: Optional. Period for leave accrual (e.g., monthly, yearly, none).
    - accrual_rate: Optional. Rate at which leave accrues.
    - max_carryover: Optional. Maximum days that can be carried over to next year.
    - min_service_days: Optional. Minimum service days required to be eligible.
    - is_prorated: Optional. Whether leave is prorated for new employees.
    - gender_specific: Optional. Gender specificity (male, female, or null for both).
    """
    data = request.get_json()
    company_id = data.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Validate required fields
    required_fields = ['leave_type_id', 'country_id', 'days_allowed']
    for field in required_fields:
        if not data.get(field):
            return jsonify({"message": f"{field.replace('_', ' ').title()} is required"}), 400

    # Prepare leave policy data
    policy_data = {
        'leave_type_id': data.get('leave_type_id'),
        'country_id': data.get('country_id'),
        'days_allowed': data.get('days_allowed'),
        'accrual_period': data.get('accrual_period'),
        'accrual_rate': data.get('accrual_rate'),
        'max_carryover': data.get('max_carryover'),
        'min_service_days': data.get('min_service_days', 0),
        'is_prorated': data.get('is_prorated', False),
        'gender_specific': data.get('gender_specific')
    }

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if the leave type exists
        leave_type = LeaveType.get_leave_type_by_id(session, policy_data['leave_type_id'])
        if not leave_type:
            return jsonify({"message": f"Leave type with ID {policy_data['leave_type_id']} not found"}), 404

        # Check if the country exists
        with app.app_context():
            country = Country.get_country_by_id(policy_data['country_id'])
            if not country:
                return jsonify({"message": f"Country with ID {policy_data['country_id']} not found"}), 404

        # Check if a policy already exists for this leave type and country
        existing_policy = LeavePolicy.get_policy_for_leave_type_and_country(
            session, 
            policy_data['leave_type_id'], 
            policy_data['country_id'],
            policy_data.get('gender_specific')
        )
        
        if existing_policy:
            return jsonify({
                "message": f"A leave policy already exists for this leave type and country"
            }), 409  # 409 Conflict

        # Create the leave policy
        policy = LeavePolicy.create_policy(session, **policy_data)

        if not policy:
            return jsonify({"message": "Failed to create leave policy"}), 500

        # Get the leave type name
        policy_dict = policy.to_dict()
        policy_dict['leave_type_name'] = leave_type.name
        policy_dict['country_name'] = country.name

        return jsons.dump(Msg.success().add("leave_policy", policy_dict)), 201

@leave_policies_api.route('/api/leave/policies/<policy_id>', methods=['PATCH'])
@token_required
@roles_required('admin', 'hr')
def update_leave_policy(policy_id):
    """
    Update an existing leave policy.
    
    Path Parameters:
    - policy_id: Required. The ID of the leave policy to update.
    
    Request Body:
    - company_id: Required. The ID of the company.
    - days_allowed: Optional. Number of days allowed for this leave type.
    - accrual_period: Optional. Period for leave accrual.
    - accrual_rate: Optional. Rate at which leave accrues.
    - max_carryover: Optional. Maximum days that can be carried over to next year.
    - min_service_days: Optional. Minimum service days required to be eligible.
    - is_prorated: Optional. Whether leave is prorated for new employees.
    - gender_specific: Optional. Gender specificity.
    """
    data = request.get_json()
    company_id = data.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Prepare leave policy data (only include fields that are provided)
    policy_data = {}
    for field in ['days_allowed', 'accrual_period', 'accrual_rate', 'max_carryover', 
                 'min_service_days', 'is_prorated', 'gender_specific']:
        if field in data:
            policy_data[field] = data.get(field)

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if the policy exists
        policy = LeavePolicy.get_policy_by_id(session, policy_id)
        if not policy:
            return jsonify({"message": f"Leave policy with ID {policy_id} not found"}), 404

        # Update the policy
        updated_policy = LeavePolicy.update_policy(session, policy_id, **policy_data)

        if not updated_policy:
            return jsonify({"message": "Failed to update leave policy"}), 500

        # Get the leave type name
        policy_dict = updated_policy.to_dict()
        leave_type = LeaveType.get_leave_type_by_id(session, updated_policy.leave_type_id)
        if leave_type:
            policy_dict['leave_type_name'] = leave_type.name

        # Get the country name
        with app.app_context():
            country = Country.get_country_by_id(updated_policy.country_id)
            if country:
                policy_dict['country_name'] = country.name

        return jsons.dump(Msg.success().add("leave_policy", policy_dict))

@leave_policies_api.route('/api/leave/policies/<policy_id>', methods=['DELETE'])
@token_required
@roles_required('admin')
def delete_leave_policy(policy_id):
    """
    Delete a leave policy.
    
    Path Parameters:
    - policy_id: Required. The ID of the leave policy to delete.
    
    Query Parameters:
    - company_id: Required. The ID of the company.
    """
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if the policy exists
        policy = LeavePolicy.get_policy_by_id(session, policy_id)
        if not policy:
            return jsonify({"message": f"Leave policy with ID {policy_id} not found"}), 404

        # Delete the policy
        success = LeavePolicy.delete_policy(session, policy_id)

        if not success:
            return jsonify({"message": "Failed to delete leave policy"}), 500

        return jsonify({"message": "Leave policy deleted successfully"}), 200
