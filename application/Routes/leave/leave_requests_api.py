from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from application.Models.employees import LeaveRequest, LeaveType, Employee, LeaveBalance
from application.Models.company import Company
from application.Models.Msg import Msg
import jsons
from datetime import datetime, date, timedelta
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.Helpers.helper_methods import HelperMethods
from sqlalchemy import func, and_, extract, case
from calendar import monthrange
import calendar

leave_requests_api = Blueprint('leave_requests_api', __name__)

@leave_requests_api.route('/api/leave/requests', methods=['GET'])
@token_required
def get_leave_requests():
    """
    Get leave requests with optional filtering.
    
    Query Parameters:
    - company_id: Required. The ID of the company.
    - employee_id: Optional. Filter by employee ID. If not provided and user is not admin/HR, returns current user's requests.
    - status: Optional. Filter by status (pending, approved, rejected, cancelled).
    - year: Optional. Filter by year. Defaults to current year.
    - page: Optional. Page number for pagination. Defaults to 1.
    - per_page: Optional. Number of items per page. Defaults to 20.
    """
    company_id = request.args.get('company_id')
    employee_id = request.args.get('employee_id')
    status = request.args.get('status')
    year = request.args.get('year', datetime.now().year, type=int)
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # If employee_id is not provided and user is not admin/HR, use current user's employee_id
        current_user_role = g.user.get('role')
        if not employee_id and current_user_role not in ['admin', 'hr']:
            # Get the current user's employee ID
            from application.Models.employees.company_user import CompanyUser
            current_user_id = g.user.get('user_id')
            company_user = session.query(CompanyUser).filter_by(id=current_user_id).first()
            
            if company_user and company_user.employee_id:
                employee_id = str(company_user.employee_id)
            else:
                return jsonify({"message": "Employee ID is required"}), 400

        # Build the query
        query = session.query(LeaveRequest)
        
        if employee_id:
            query = query.filter(LeaveRequest.employee_id == employee_id)
        
        if status:
            query = query.filter(LeaveRequest.status == status)
        
        if year:
            # Filter requests that have start_date or end_date in the specified year
            query = query.filter(
                (func.extract('year', LeaveRequest.start_date) == year) |
                (func.extract('year', LeaveRequest.end_date) == year)
            )
        
        # Count total records for pagination
        total_count = query.count()
        
        # Apply pagination
        query = query.order_by(LeaveRequest.created_at.desc())
        query = query.offset((page - 1) * per_page).limit(per_page)
        
        # Execute the query
        leave_requests = query.all()
        
        # Convert to dictionaries
        requests_data = [request.to_dict() for request in leave_requests]
        
        # Prepare pagination metadata
        pagination = {
            "total_count": total_count,
            "page": page,
            "per_page": per_page,
            "pages": (total_count + per_page - 1) // per_page if per_page > 0 else 0,
            "has_next": page < ((total_count + per_page - 1) // per_page) if per_page > 0 else False,
            "has_prev": page > 1
        }
        
        return jsons.dump(Msg.success()
                         .add("leave_requests", requests_data)
                         .add("pagination", pagination))

@leave_requests_api.route('/api/leave/requests/<request_id>', methods=['GET'])
@token_required
def get_leave_request(request_id):
    """
    Get a specific leave request by ID.
    
    Path Parameters:
    - request_id: Required. The ID of the leave request to retrieve.
    
    Query Parameters:
    - company_id: Required. The ID of the company.
    """
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        leave_request = LeaveRequest.get_request_by_id(session, request_id)

        if not leave_request:
            return jsonify({"message": f"Leave request with ID {request_id} not found"}), 404

        # Check if the current user has permission to view this request
        current_user_role = g.user.get('role')
        if current_user_role not in ['admin', 'hr']:
            # Get the current user's employee ID
            from application.Models.employees.company_user import CompanyUser
            current_user_id = g.user.get('user_id')
            company_user = session.query(CompanyUser).filter_by(id=current_user_id).first()
            
            if not company_user or str(company_user.employee_id) != str(leave_request.employee_id):
                return jsonify({"message": "You do not have permission to view this leave request"}), 403

        return jsons.dump(Msg.success().add("leave_request", leave_request.to_dict()))

@leave_requests_api.route('/api/leave/requests', methods=['POST'])
@token_required
def create_leave_request():
    """
    Create a new leave request.

    Request Body:
    - company_id: Required. The ID of the company.
    - leave_type_id: Required. The ID of the leave type.
    - start_date: Required. The start date of the leave (YYYY-MM-DD).
    - end_date: Required. The end date of the leave (YYYY-MM-DD).
    - reason: Optional. The reason for the leave.
    - documentation_path: Optional. Path to supporting documentation.

    Note:
    - employee_id is automatically extracted from the JWT token.
    - total_days is automatically calculated based on start_date and end_date.
    - emergency_contact can be provided but is logged only (not stored in database).
    """
    data = request.get_json()
    company_id = data.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Validate required fields (employee_id is extracted from token)
    required_fields = ['leave_type_id', 'start_date', 'end_date']
    for field in required_fields:
        if not data.get(field):
            return jsonify({"message": f"{field.replace('_', ' ').title()} is required"}), 400

    # Get employee_id from JWT token
    current_user_role = g.user.get('role')
    current_user_id = g.user.get('user_id')
    employee_id = None

    # Debug logging
    app.logger.info(f"=== LEAVE REQUEST DEBUG ===")
    app.logger.info(f"JWT Token contents: {g.user}")
    app.logger.info(f"Current user role: {current_user_role}")
    app.logger.info(f"Current user ID: {current_user_id}")
    app.logger.info(f"Employee ID from token: {g.user.get('employee_id')}")
    app.logger.info(f"Company ID from request: {company_id}")
    app.logger.info(f"Database name: {database_name}")

    # Always look up the company user to get accurate role and employee_id
    from application.Models.employees.company_user import CompanyUser
    from app import db_connection

    with db_connection.get_session(database_name) as session:
        app.logger.info(f"Looking up company user with ID: {current_user_id}")
        company_user = session.query(CompanyUser).filter_by(id=current_user_id).first()

        if not company_user:
            app.logger.error(f"Company user not found with ID: {current_user_id}")
            return jsonify({"message": "User not found. Please contact HR."}), 404

        app.logger.info(f"Found company user: {company_user.username}, role: {company_user.role}, employee_id: {company_user.employee_id}")

        # Use the role from database if token doesn't have it
        actual_role = current_user_role or company_user.role
        app.logger.info(f"Using role: {actual_role} (token: {current_user_role}, db: {company_user.role})")

        if actual_role in ['admin', 'hr']:
            # Admin/HR can create requests for any employee (if employee_id provided in body)
            employee_id = data.get('employee_id')
            app.logger.info(f"Admin/HR user - employee_id from request body: {employee_id}")
            if not employee_id:
                return jsonify({"message": "Admin/HR must specify employee_id in request body"}), 400
        else:
            # Regular employees: use employee_id from database
            if not company_user.employee_id:
                app.logger.error(f"Company user {company_user.username} has no employee_id")
                return jsonify({"message": "Employee record not found. Please contact HR."}), 404

            employee_id = str(company_user.employee_id)
            app.logger.info(f"Regular employee - using employee_id from database: {employee_id}")

    app.logger.info(f"=== END LEAVE REQUEST DEBUG ===")

    # Prepare leave request data
    request_data = {
        'employee_id': employee_id,
        'leave_type_id': data.get('leave_type_id'),
        'start_date': data.get('start_date'),
        'end_date': data.get('end_date'),
        'reason': data.get('reason'),
        'documentation_path': data.get('documentation_path'),
        'status': 'pending'
    }

    # Log emergency contact if provided (for future reference)
    emergency_contact = data.get('emergency_contact')
    if emergency_contact:
        app.logger.info(f"Emergency contact provided for leave request: {emergency_contact}")

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Create the leave request
        leave_request, error = LeaveRequest.create_request(session, **request_data)

        if not leave_request:
            return jsonify({"message": error or "Failed to create leave request"}), 400

        return jsons.dump(Msg.success().add("leave_request", leave_request.to_dict())), 201

@leave_requests_api.route('/api/leave/requests/<request_id>', methods=['PATCH'])
@token_required
def update_leave_request(request_id):
    """
    Update an existing leave request (only if it's pending).
    
    Path Parameters:
    - request_id: Required. The ID of the leave request to update.
    
    Request Body:
    - company_id: Required. The ID of the company.
    - start_date: Optional. The start date of the leave.
    - end_date: Optional. The end date of the leave.
    - reason: Optional. The reason for the leave.
    - documentation_path: Optional. Path to supporting documentation.
    """
    data = request.get_json()
    company_id = data.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if the leave request exists
        leave_request = LeaveRequest.get_request_by_id(session, request_id)
        if not leave_request:
            return jsonify({"message": f"Leave request with ID {request_id} not found"}), 404

        # Check if the current user has permission to update this request
        current_user_role = g.user.get('role')
        if current_user_role not in ['admin', 'hr']:
            # Get the current user's employee ID
            from application.Models.employees.company_user import CompanyUser
            current_user_id = g.user.get('user_id')
            company_user = session.query(CompanyUser).filter_by(id=current_user_id).first()
            
            if not company_user or str(company_user.employee_id) != str(leave_request.employee_id):
                return jsonify({"message": "You can only update your own leave requests"}), 403

        # Prepare update data (only include fields that are provided)
        update_data = {}
        for field in ['start_date', 'end_date', 'reason', 'documentation_path']:
            if field in data:
                update_data[field] = data.get(field)

        # Update the leave request
        updated_request, error = LeaveRequest.update_request(session, request_id, **update_data)

        if not updated_request:
            return jsonify({"message": error or "Failed to update leave request"}), 400

        return jsons.dump(Msg.success().add("leave_request", updated_request.to_dict()))

@leave_requests_api.route('/api/leave/requests/<request_id>/approve', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def approve_leave_request(request_id):
    """
    Approve a leave request.
    
    Path Parameters:
    - request_id: Required. The ID of the leave request to approve.
    
    Request Body:
    - company_id: Required. The ID of the company.
    """
    data = request.get_json()
    company_id = data.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Get the current user's ID for the approved_by field
        current_user_id = g.user.get('user_id')

        # Approve the leave request
        approved_request, error = LeaveRequest.approve_request(session, request_id, current_user_id)

        if not approved_request:
            return jsonify({"message": error or "Failed to approve leave request"}), 400

        return jsons.dump(Msg.success().add("leave_request", approved_request.to_dict()))

@leave_requests_api.route('/api/leave/requests/<request_id>/reject', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def reject_leave_request(request_id):
    """
    Reject a leave request.
    
    Path Parameters:
    - request_id: Required. The ID of the leave request to reject.
    
    Request Body:
    - company_id: Required. The ID of the company.
    - rejection_reason: Required. The reason for rejecting the request.
    """
    data = request.get_json()
    company_id = data.get('company_id')
    rejection_reason = data.get('rejection_reason')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    if not rejection_reason:
        return jsonify({"message": "Rejection reason is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Get the current user's ID for the rejected_by field
        current_user_id = g.user.get('user_id')

        # Reject the leave request
        rejected_request, error = LeaveRequest.reject_request(session, request_id, rejection_reason, current_user_id)

        if not rejected_request:
            return jsonify({"message": error or "Failed to reject leave request"}), 400

        return jsons.dump(Msg.success().add("leave_request", rejected_request.to_dict()))

@leave_requests_api.route('/api/leave/requests/<request_id>/cancel', methods=['POST'])
@token_required
def cancel_leave_request(request_id):
    """
    Cancel a leave request.
    
    Path Parameters:
    - request_id: Required. The ID of the leave request to cancel.
    
    Request Body:
    - company_id: Required. The ID of the company.
    """
    data = request.get_json()
    company_id = data.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if the leave request exists
        leave_request = LeaveRequest.get_request_by_id(session, request_id)
        if not leave_request:
            return jsonify({"message": f"Leave request with ID {request_id} not found"}), 404

        # Check if the current user has permission to cancel this request
        current_user_role = g.user.get('role')
        if current_user_role not in ['admin', 'hr']:
            # Get the current user's employee ID
            from application.Models.employees.company_user import CompanyUser
            current_user_id = g.user.get('user_id')
            company_user = session.query(CompanyUser).filter_by(id=current_user_id).first()
            
            if not company_user or str(company_user.employee_id) != str(leave_request.employee_id):
                return jsonify({"message": "You can only cancel your own leave requests"}), 403

        # Cancel the leave request
        cancelled_request, error = LeaveRequest.cancel_request(session, request_id)

        if not cancelled_request:
            return jsonify({"message": error or "Failed to cancel leave request"}), 400

        return jsons.dump(Msg.success().add("leave_request", cancelled_request.to_dict()))


