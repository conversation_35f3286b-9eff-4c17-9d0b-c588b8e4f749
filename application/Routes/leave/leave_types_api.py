from flask import Blueprint, request, jsonify, current_app as app
import uuid
from application.Models.employees import LeaveType
from application.Models.company import Company
from application.Models.Msg import Msg
import jsons
from datetime import datetime
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.Helpers.helper_methods import HelperMethods

leave_types_api = Blueprint('leave_types_api', __name__)

@leave_types_api.route('/api/leave/types', methods=['GET'])
@token_required
def get_leave_types():
    """
    Get all leave types for a company.
    
    Query Parameters:
    - company_id: Required. The ID of the company to get leave types for.
    """
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        leave_types = LeaveType.get_all_leave_types(session)
        leave_types_data = [leave_type.to_dict() for leave_type in leave_types]

        return jsons.dump(Msg.success().add("leave_types", leave_types_data))

@leave_types_api.route('/api/leave/types/<leave_type_id>', methods=['GET'])
@token_required
def get_leave_type(leave_type_id):
    """
    Get a specific leave type by ID.
    
    Path Parameters:
    - leave_type_id: Required. The ID of the leave type to retrieve.
    
    Query Parameters:
    - company_id: Required. The ID of the company.
    """
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        leave_type = LeaveType.get_leave_type_by_id(session, leave_type_id)

        if not leave_type:
            return jsonify({"message": f"Leave type with ID {leave_type_id} not found"}), 404

        return jsons.dump(Msg.success().add("leave_type", leave_type.to_dict()))

@leave_types_api.route('/api/leave/types', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def create_leave_type():
    """
    Create a new leave type.
    
    Request Body:
    - company_id: Required. The ID of the company.
    - name: Required. The name of the leave type.
    - code: Required. A unique code for the leave type.
    - description: Optional. A description of the leave type.
    - is_paid: Optional. Whether the leave type is paid (default: true).
    - requires_approval: Optional. Whether the leave type requires approval (default: true).
    - requires_documentation: Optional. Whether the leave type requires documentation (default: false).
    """
    data = request.get_json()
    company_id = data.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Validate required fields
    required_fields = ['name', 'code']
    for field in required_fields:
        if not data.get(field):
            return jsonify({"message": f"{field.replace('_', ' ').title()} is required"}), 400

    # Prepare leave type data
    leave_type_data = {
        'name': data.get('name'),
        'code': data.get('code'),
        'description': data.get('description'),
        'is_paid': data.get('is_paid', True),
        'requires_approval': data.get('requires_approval', True),
        'requires_documentation': data.get('requires_documentation', False)
    }

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if a leave type with the same code already exists
        existing_leave_type = LeaveType.get_leave_type_by_code(session, leave_type_data['code'])
        if existing_leave_type:
            return jsonify({
                "message": f"A leave type with the code '{leave_type_data['code']}' already exists"
            }), 409  # 409 Conflict

        # Create the leave type
        leave_type = LeaveType.create_leave_type(session, **leave_type_data)

        if not leave_type:
            return jsonify({"message": "Failed to create leave type"}), 500

        return jsons.dump(Msg.success().add("leave_type", leave_type.to_dict())), 201

@leave_types_api.route('/api/leave/types/<leave_type_id>', methods=['PATCH'])
@token_required
@roles_required('admin', 'hr')
def update_leave_type(leave_type_id):
    """
    Update an existing leave type.
    
    Path Parameters:
    - leave_type_id: Required. The ID of the leave type to update.
    
    Request Body:
    - company_id: Required. The ID of the company.
    - name: Optional. The name of the leave type.
    - description: Optional. A description of the leave type.
    - is_paid: Optional. Whether the leave type is paid.
    - requires_approval: Optional. Whether the leave type requires approval.
    - requires_documentation: Optional. Whether the leave type requires documentation.
    """
    data = request.get_json()
    company_id = data.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Prepare leave type data (only include fields that are provided)
    leave_type_data = {}
    for field in ['name', 'description', 'is_paid', 'requires_approval', 'requires_documentation']:
        if field in data:
            leave_type_data[field] = data.get(field)

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if the leave type exists
        leave_type = LeaveType.get_leave_type_by_id(session, leave_type_id)
        if not leave_type:
            return jsonify({"message": f"Leave type with ID {leave_type_id} not found"}), 404

        # Update the leave type
        updated_leave_type = LeaveType.update_leave_type(session, leave_type_id, **leave_type_data)

        if not updated_leave_type:
            return jsonify({"message": "Failed to update leave type"}), 500

        return jsons.dump(Msg.success().add("leave_type", updated_leave_type.to_dict()))

@leave_types_api.route('/api/leave/types/<leave_type_id>', methods=['DELETE'])
@token_required
@roles_required('admin', 'hr')
def delete_leave_type(leave_type_id):
    """
    Delete a leave type.
    
    Path Parameters:
    - leave_type_id: Required. The ID of the leave type to delete.
    
    Query Parameters:
    - company_id: Required. The ID of the company.
    """
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if the leave type exists
        leave_type = LeaveType.get_leave_type_by_id(session, leave_type_id)
        if not leave_type:
            return jsonify({"message": f"Leave type with ID {leave_type_id} not found"}), 404

        # Delete the leave type (with safety checks)
        success, error_message = LeaveType.delete_leave_type(session, leave_type_id)

        if not success:
            if "policies" in error_message or "balances" in error_message or "requests" in error_message:
                # Business logic error - leave type has associated data
                return jsonify({"message": error_message}), 409  # 409 Conflict
            else:
                # Technical error
                return jsonify({"message": error_message or "Failed to delete leave type"}), 500

        return jsonify({"message": "Leave type deleted successfully"}), 200
