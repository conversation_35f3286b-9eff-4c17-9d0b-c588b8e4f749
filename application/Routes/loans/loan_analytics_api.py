from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from application.Models.employees.loan_type import LoanType
from application.Models.employees.employee_loan import EmployeeLoan
from application.Models.employees.loan_repayment_schedule import LoanRepaymentSchedule
from application.Models.employees.loan_transaction import LoanTransaction
from application.Models.employees import Employee
from application.Models.company import Company
from application.utils.db_connection import DatabaseConnection
from datetime import datetime, date, timedelta
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from decimal import Decimal
from sqlalchemy import func, and_, extract, case
from calendar import monthrange
import calendar

loan_analytics_api = Blueprint('loan_analytics_api', __name__)

@loan_analytics_api.route('/api/loans/analytics/overview', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_loan_analytics_overview():
    """
    Get comprehensive loan analytics overview.
    
    Query parameters:
    - company_id: Required
    - period: 'month', 'quarter', 'year' (default: 'month')
    - year: Specific year (default: current year)
    - month: Specific month (default: current month, only if period='month')
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        period = request.args.get('period', 'month')
        year = int(request.args.get('year', datetime.now().year))
        month = request.args.get('month')
        
        if month:
            month = int(month)
        else:
            month = datetime.now().month if period == 'month' else None

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            # Calculate date ranges
            if period == 'month' and month:
                start_date = date(year, month, 1)
                end_date = date(year, month, monthrange(year, month)[1])
            elif period == 'quarter':
                quarter = ((month - 1) // 3) + 1 if month else 1
                start_month = (quarter - 1) * 3 + 1
                start_date = date(year, start_month, 1)
                end_month = start_month + 2
                end_date = date(year, end_month, monthrange(year, end_month)[1])
            else:  # year
                start_date = date(year, 1, 1)
                end_date = date(year, 12, 31)

            # Overall statistics
            total_loans = session.query(EmployeeLoan).count()
            active_loans = session.query(EmployeeLoan).filter(
                EmployeeLoan.status.in_(['ACTIVE', 'DISBURSED'])
            ).count()
            
            total_disbursed = session.query(func.sum(EmployeeLoan.amount_disbursed)).scalar() or 0
            total_outstanding = session.query(func.sum(EmployeeLoan.outstanding_balance)).filter(
                EmployeeLoan.status.in_(['ACTIVE', 'DISBURSED'])
            ).scalar() or 0
            
            # Period-specific statistics
            period_disbursements = session.query(func.sum(LoanTransaction.amount)).filter(
                LoanTransaction.transaction_type == 'DISBURSEMENT',
                LoanTransaction.transaction_date.between(start_date, end_date)
            ).scalar() or 0
            
            period_repayments = session.query(func.sum(LoanTransaction.amount)).filter(
                LoanTransaction.transaction_type == 'REPAYMENT',
                LoanTransaction.transaction_date.between(start_date, end_date)
            ).scalar() or 0
            
            # Loan type breakdown
            loan_type_stats = session.query(
                LoanType.name,
                LoanType.code,
                func.count(EmployeeLoan.loan_id).label('count'),
                func.sum(EmployeeLoan.outstanding_balance).label('outstanding'),
                func.sum(EmployeeLoan.amount_disbursed).label('disbursed')
            ).join(EmployeeLoan).filter(
                EmployeeLoan.status.in_(['ACTIVE', 'DISBURSED', 'COMPLETED'])
            ).group_by(LoanType.loan_type_id, LoanType.name, LoanType.code).all()

            # Status breakdown
            status_stats = session.query(
                EmployeeLoan.status,
                func.count(EmployeeLoan.loan_id).label('count'),
                func.sum(EmployeeLoan.outstanding_balance).label('outstanding')
            ).group_by(EmployeeLoan.status).all()

            return jsonify({
                "success": True,
                "data": {
                    "period_info": {
                        "period": period,
                        "year": year,
                        "month": month,
                        "start_date": start_date.strftime('%Y-%m-%d'),
                        "end_date": end_date.strftime('%Y-%m-%d')
                    },
                    "overall_statistics": {
                        "total_loans": total_loans,
                        "active_loans": active_loans,
                        "total_disbursed": float(total_disbursed),
                        "total_outstanding": float(total_outstanding),
                        "completion_rate": round((total_loans - active_loans) / total_loans * 100, 2) if total_loans > 0 else 0
                    },
                    "period_statistics": {
                        "disbursements": float(period_disbursements),
                        "repayments": float(period_repayments),
                        "net_flow": float(period_repayments - period_disbursements)
                    },
                    "loan_type_breakdown": [
                        {
                            "loan_type": stat.name,
                            "code": stat.code,
                            "count": stat.count,
                            "outstanding": float(stat.outstanding or 0),
                            "disbursed": float(stat.disbursed or 0)
                        } for stat in loan_type_stats
                    ],
                    "status_breakdown": [
                        {
                            "status": stat.status,
                            "count": stat.count,
                            "outstanding": float(stat.outstanding or 0)
                        } for stat in status_stats
                    ]
                }
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting loan analytics overview: {e}")
        return jsonify({"error": "Internal server error"}), 500

@loan_analytics_api.route('/api/loans/analytics/trends', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_loan_trends():
    """
    Get loan trends over time (monthly/yearly).
    
    Query parameters:
    - company_id: Required
    - period: 'monthly', 'yearly' (default: 'monthly')
    - months_back: Number of months to go back (default: 12)
    - years_back: Number of years to go back (default: 3, only if period='yearly')
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        period = request.args.get('period', 'monthly')
        months_back = int(request.args.get('months_back', 12))
        years_back = int(request.args.get('years_back', 3))

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            trends = []
            
            if period == 'monthly':
                # Monthly trends
                for i in range(months_back):
                    target_date = date.today().replace(day=1) - timedelta(days=30*i)
                    month_start = target_date.replace(day=1)
                    month_end = date(target_date.year, target_date.month, 
                                   monthrange(target_date.year, target_date.month)[1])
                    
                    # Applications
                    applications = session.query(func.count(EmployeeLoan.loan_id)).filter(
                        EmployeeLoan.application_date.between(month_start, month_end)
                    ).scalar() or 0
                    
                    # Approvals
                    approvals = session.query(func.count(EmployeeLoan.loan_id)).filter(
                        EmployeeLoan.approval_date.between(month_start, month_end)
                    ).scalar() or 0
                    
                    # Disbursements
                    disbursements = session.query(func.sum(LoanTransaction.amount)).filter(
                        LoanTransaction.transaction_type == 'DISBURSEMENT',
                        LoanTransaction.transaction_date.between(month_start, month_end)
                    ).scalar() or 0
                    
                    # Repayments
                    repayments = session.query(func.sum(LoanTransaction.amount)).filter(
                        LoanTransaction.transaction_type == 'REPAYMENT',
                        LoanTransaction.transaction_date.between(month_start, month_end)
                    ).scalar() or 0
                    
                    # Completions
                    completions = session.query(func.count(EmployeeLoan.loan_id)).filter(
                        EmployeeLoan.actual_completion_date.between(month_start, month_end)
                    ).scalar() or 0
                    
                    trends.append({
                        "period": target_date.strftime('%Y-%m'),
                        "period_name": target_date.strftime('%B %Y'),
                        "applications": applications,
                        "approvals": approvals,
                        "approval_rate": round(approvals / applications * 100, 2) if applications > 0 else 0,
                        "disbursements": float(disbursements),
                        "repayments": float(repayments),
                        "net_flow": float(repayments - disbursements),
                        "completions": completions
                    })
            
            else:  # yearly
                # Yearly trends
                current_year = datetime.now().year
                for i in range(years_back):
                    target_year = current_year - i
                    year_start = date(target_year, 1, 1)
                    year_end = date(target_year, 12, 31)
                    
                    # Similar calculations for yearly data
                    applications = session.query(func.count(EmployeeLoan.loan_id)).filter(
                        extract('year', EmployeeLoan.application_date) == target_year
                    ).scalar() or 0
                    
                    approvals = session.query(func.count(EmployeeLoan.loan_id)).filter(
                        extract('year', EmployeeLoan.approval_date) == target_year
                    ).scalar() or 0
                    
                    disbursements = session.query(func.sum(LoanTransaction.amount)).filter(
                        LoanTransaction.transaction_type == 'DISBURSEMENT',
                        extract('year', LoanTransaction.transaction_date) == target_year
                    ).scalar() or 0
                    
                    repayments = session.query(func.sum(LoanTransaction.amount)).filter(
                        LoanTransaction.transaction_type == 'REPAYMENT',
                        extract('year', LoanTransaction.transaction_date) == target_year
                    ).scalar() or 0
                    
                    completions = session.query(func.count(EmployeeLoan.loan_id)).filter(
                        extract('year', EmployeeLoan.actual_completion_date) == target_year
                    ).scalar() or 0
                    
                    trends.append({
                        "period": str(target_year),
                        "period_name": str(target_year),
                        "applications": applications,
                        "approvals": approvals,
                        "approval_rate": round(approvals / applications * 100, 2) if applications > 0 else 0,
                        "disbursements": float(disbursements),
                        "repayments": float(repayments),
                        "net_flow": float(repayments - disbursements),
                        "completions": completions
                    })

            return jsonify({
                "success": True,
                "data": {
                    "period_type": period,
                    "periods_analyzed": len(trends),
                    "trends": list(reversed(trends))  # Most recent first
                }
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting loan trends: {e}")
        return jsonify({"error": "Internal server error"}), 500

@loan_analytics_api.route('/api/loans/analytics/by-department', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_loans_by_department():
    """
    Get loan analytics by department.

    Query parameters:
    - company_id: Required
    - year: Specific year (default: current year)
    - month: Specific month (optional)
    - include_trends: Include monthly trends per department (default: false)
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        year = int(request.args.get('year', datetime.now().year))
        month = request.args.get('month')
        include_trends = request.args.get('include_trends', 'false').lower() == 'true'

        if month:
            month = int(month)

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            # Base query for department analytics
            query = session.query(
                Employee.department_id,
                Employee.department_name,
                func.count(EmployeeLoan.loan_id).label('total_loans'),
                func.count(case([(EmployeeLoan.status.in_(['ACTIVE', 'DISBURSED']), 1)])).label('active_loans'),
                func.sum(EmployeeLoan.amount_disbursed).label('total_disbursed'),
                func.sum(EmployeeLoan.outstanding_balance).label('total_outstanding'),
                func.avg(EmployeeLoan.principal_amount).label('avg_loan_amount'),
                func.count(func.distinct(EmployeeLoan.employee_id)).label('unique_borrowers')
            ).join(EmployeeLoan, Employee.employee_id == EmployeeLoan.employee_id)

            # Apply date filters
            if month:
                query = query.filter(
                    extract('year', EmployeeLoan.application_date) == year,
                    extract('month', EmployeeLoan.application_date) == month
                )
            else:
                query = query.filter(extract('year', EmployeeLoan.application_date) == year)

            department_stats = query.group_by(
                Employee.department_id, Employee.department_name
            ).all()

            # Calculate department trends if requested
            department_trends = {}
            if include_trends:
                for dept_stat in department_stats:
                    dept_id = dept_stat.department_id
                    monthly_trends = []

                    for month_num in range(1, 13):
                        month_stats = session.query(
                            func.count(EmployeeLoan.loan_id).label('applications'),
                            func.sum(EmployeeLoan.amount_disbursed).label('disbursed')
                        ).join(Employee).filter(
                            Employee.department_id == dept_id,
                            extract('year', EmployeeLoan.application_date) == year,
                            extract('month', EmployeeLoan.application_date) == month_num
                        ).first()

                        monthly_trends.append({
                            "month": month_num,
                            "month_name": calendar.month_name[month_num],
                            "applications": month_stats.applications or 0,
                            "disbursed": float(month_stats.disbursed or 0)
                        })

                    department_trends[str(dept_id)] = monthly_trends

            # Format results
            department_analytics = []
            total_employees_with_loans = 0
            total_company_disbursed = Decimal('0')

            for stat in department_stats:
                total_employees_with_loans += stat.unique_borrowers
                total_company_disbursed += Decimal(str(stat.total_disbursed or 0))

                dept_data = {
                    "department_id": str(stat.department_id) if stat.department_id else None,
                    "department_name": stat.department_name or "Unassigned",
                    "statistics": {
                        "total_loans": stat.total_loans,
                        "active_loans": stat.active_loans,
                        "completed_loans": stat.total_loans - stat.active_loans,
                        "total_disbursed": float(stat.total_disbursed or 0),
                        "total_outstanding": float(stat.total_outstanding or 0),
                        "average_loan_amount": float(stat.avg_loan_amount or 0),
                        "unique_borrowers": stat.unique_borrowers,
                        "completion_rate": round((stat.total_loans - stat.active_loans) / stat.total_loans * 100, 2) if stat.total_loans > 0 else 0
                    }
                }

                if include_trends:
                    dept_data["monthly_trends"] = department_trends.get(str(stat.department_id), [])

                department_analytics.append(dept_data)

            # Sort by total disbursed (highest first)
            department_analytics.sort(key=lambda x: x["statistics"]["total_disbursed"], reverse=True)

            return jsonify({
                "success": True,
                "data": {
                    "period": f"{year}-{month:02d}" if month else str(year),
                    "total_departments": len(department_analytics),
                    "company_totals": {
                        "total_employees_with_loans": total_employees_with_loans,
                        "total_disbursed": float(total_company_disbursed)
                    },
                    "department_analytics": department_analytics,
                    "includes_trends": include_trends
                }
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting department loan analytics: {e}")
        return jsonify({"error": "Internal server error"}), 500

@loan_analytics_api.route('/api/loans/analytics/by-employee', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_loans_by_employee():
    """
    Get loan analytics by employee.

    Query parameters:
    - company_id: Required
    - department_id: Filter by department (optional)
    - year: Specific year (default: current year)
    - limit: Number of employees to return (default: 50)
    - sort_by: 'total_disbursed', 'outstanding_balance', 'loan_count' (default: 'total_disbursed')
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        department_id = request.args.get('department_id')
        year = int(request.args.get('year', datetime.now().year))
        limit = int(request.args.get('limit', 50))
        sort_by = request.args.get('sort_by', 'total_disbursed')

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            # Base query for employee analytics
            query = session.query(
                Employee.employee_id,
                Employee.first_name,
                Employee.last_name,
                Employee.employee_number,
                Employee.department_name,
                Employee.position,
                func.count(EmployeeLoan.loan_id).label('total_loans'),
                func.count(case([(EmployeeLoan.status.in_(['ACTIVE', 'DISBURSED']), 1)])).label('active_loans'),
                func.sum(EmployeeLoan.amount_disbursed).label('total_disbursed'),
                func.sum(EmployeeLoan.outstanding_balance).label('total_outstanding'),
                func.avg(EmployeeLoan.principal_amount).label('avg_loan_amount'),
                func.max(EmployeeLoan.application_date).label('last_application_date')
            ).join(EmployeeLoan, Employee.employee_id == EmployeeLoan.employee_id)

            # Apply filters
            query = query.filter(extract('year', EmployeeLoan.application_date) == year)

            if department_id:
                query = query.filter(Employee.department_id == department_id)

            # Group by employee
            query = query.group_by(
                Employee.employee_id, Employee.first_name, Employee.last_name,
                Employee.employee_number, Employee.department_name, Employee.position
            )

            # Apply sorting
            if sort_by == 'outstanding_balance':
                query = query.order_by(func.sum(EmployeeLoan.outstanding_balance).desc())
            elif sort_by == 'loan_count':
                query = query.order_by(func.count(EmployeeLoan.loan_id).desc())
            else:  # total_disbursed
                query = query.order_by(func.sum(EmployeeLoan.amount_disbursed).desc())

            employee_stats = query.limit(limit).all()

            # Format results
            employee_analytics = []
            for stat in employee_stats:
                employee_analytics.append({
                    "employee_id": str(stat.employee_id),
                    "employee_name": f"{stat.first_name} {stat.last_name}",
                    "employee_number": stat.employee_number,
                    "department": stat.department_name,
                    "position": stat.position,
                    "loan_statistics": {
                        "total_loans": stat.total_loans,
                        "active_loans": stat.active_loans,
                        "completed_loans": stat.total_loans - stat.active_loans,
                        "total_disbursed": float(stat.total_disbursed or 0),
                        "total_outstanding": float(stat.total_outstanding or 0),
                        "average_loan_amount": float(stat.avg_loan_amount or 0),
                        "last_application_date": stat.last_application_date.strftime('%Y-%m-%d') if stat.last_application_date else None,
                        "completion_rate": round((stat.total_loans - stat.active_loans) / stat.total_loans * 100, 2) if stat.total_loans > 0 else 0
                    }
                })

            return jsonify({
                "success": True,
                "data": {
                    "period": str(year),
                    "department_filter": department_id,
                    "sort_by": sort_by,
                    "limit": limit,
                    "total_employees": len(employee_analytics),
                    "employee_analytics": employee_analytics
                }
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting employee loan analytics: {e}")
        return jsonify({"error": "Internal server error"}), 500

@loan_analytics_api.route('/api/loans/analytics/risk-assessment', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_loan_risk_assessment():
    """
    Get loan risk assessment analytics.

    Query parameters:
    - company_id: Required
    - include_overdue_details: Include detailed overdue analysis (default: true)
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        include_overdue_details = request.args.get('include_overdue_details', 'true').lower() == 'true'

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            # Overall risk metrics
            total_active_loans = session.query(EmployeeLoan).filter(
                EmployeeLoan.status.in_(['ACTIVE', 'DISBURSED'])
            ).count()

            total_outstanding = session.query(func.sum(EmployeeLoan.outstanding_balance)).filter(
                EmployeeLoan.status.in_(['ACTIVE', 'DISBURSED'])
            ).scalar() or 0

            # Overdue analysis
            overdue_installments = session.query(LoanRepaymentSchedule).filter(
                LoanRepaymentSchedule.due_date < date.today(),
                LoanRepaymentSchedule.status.in_(['PENDING', 'PARTIAL'])
            ).all()

            overdue_count = len(overdue_installments)
            overdue_amount = sum(float(inst.amount - inst.amount_paid) for inst in overdue_installments)

            # Risk categories
            high_risk_loans = session.query(EmployeeLoan).join(LoanRepaymentSchedule).filter(
                LoanRepaymentSchedule.due_date < date.today() - timedelta(days=30),
                LoanRepaymentSchedule.status.in_(['PENDING', 'PARTIAL']),
                EmployeeLoan.status.in_(['ACTIVE', 'DISBURSED'])
            ).distinct().count()

            medium_risk_loans = session.query(EmployeeLoan).join(LoanRepaymentSchedule).filter(
                LoanRepaymentSchedule.due_date.between(
                    date.today() - timedelta(days=30),
                    date.today() - timedelta(days=7)
                ),
                LoanRepaymentSchedule.status.in_(['PENDING', 'PARTIAL']),
                EmployeeLoan.status.in_(['ACTIVE', 'DISBURSED'])
            ).distinct().count()

            # Department risk analysis
            dept_risk = session.query(
                Employee.department_name,
                func.count(func.distinct(EmployeeLoan.loan_id)).label('total_loans'),
                func.count(func.distinct(case([
                    (LoanRepaymentSchedule.due_date < date.today(), EmployeeLoan.loan_id)
                ]))).label('overdue_loans'),
                func.sum(case([
                    (LoanRepaymentSchedule.due_date < date.today(),
                     LoanRepaymentSchedule.amount - LoanRepaymentSchedule.amount_paid)
                ])).label('overdue_amount')
            ).join(EmployeeLoan).join(LoanRepaymentSchedule).filter(
                EmployeeLoan.status.in_(['ACTIVE', 'DISBURSED']),
                LoanRepaymentSchedule.status.in_(['PENDING', 'PARTIAL'])
            ).group_by(Employee.department_name).all()

            # Format overdue details
            overdue_details = []
            if include_overdue_details:
                for installment in overdue_installments[:20]:  # Limit to top 20
                    days_overdue = (date.today() - installment.due_date).days
                    overdue_details.append({
                        "loan_number": installment.loan.loan_number,
                        "employee_name": f"{installment.loan.employee.first_name} {installment.loan.employee.last_name}",
                        "department": installment.loan.employee.department_name,
                        "due_date": installment.due_date.strftime('%Y-%m-%d'),
                        "days_overdue": days_overdue,
                        "overdue_amount": float(installment.amount - installment.amount_paid),
                        "installment_number": installment.installment_number,
                        "risk_level": "HIGH" if days_overdue > 30 else "MEDIUM" if days_overdue > 7 else "LOW"
                    })

            return jsonify({
                "success": True,
                "data": {
                    "risk_summary": {
                        "total_active_loans": total_active_loans,
                        "total_outstanding": float(total_outstanding),
                        "overdue_installments": overdue_count,
                        "overdue_amount": overdue_amount,
                        "overdue_rate": round(overdue_count / total_active_loans * 100, 2) if total_active_loans > 0 else 0,
                        "risk_exposure": round(overdue_amount / float(total_outstanding) * 100, 2) if total_outstanding > 0 else 0
                    },
                    "risk_categories": {
                        "high_risk_loans": high_risk_loans,
                        "medium_risk_loans": medium_risk_loans,
                        "low_risk_loans": total_active_loans - high_risk_loans - medium_risk_loans
                    },
                    "department_risk_analysis": [
                        {
                            "department": dept.department_name,
                            "total_loans": dept.total_loans,
                            "overdue_loans": dept.overdue_loans or 0,
                            "overdue_amount": float(dept.overdue_amount or 0),
                            "risk_rate": round((dept.overdue_loans or 0) / dept.total_loans * 100, 2) if dept.total_loans > 0 else 0
                        } for dept in dept_risk
                    ],
                    "overdue_details": overdue_details if include_overdue_details else [],
                    "analysis_date": date.today().strftime('%Y-%m-%d')
                }
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting loan risk assessment: {e}")
        return jsonify({"error": "Internal server error"}), 500

@loan_analytics_api.route('/api/loans/analytics/performance-metrics', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_loan_performance_metrics():
    """
    Get comprehensive loan performance metrics.

    Query parameters:
    - company_id: Required
    - comparison_period: 'month', 'quarter', 'year' (default: 'month')
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        comparison_period = request.args.get('comparison_period', 'month')

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            # Calculate date ranges for comparison
            today = date.today()

            if comparison_period == 'month':
                current_start = today.replace(day=1)
                current_end = today
                prev_start = (current_start - timedelta(days=1)).replace(day=1)
                prev_end = current_start - timedelta(days=1)
            elif comparison_period == 'quarter':
                quarter = ((today.month - 1) // 3) + 1
                current_start = date(today.year, (quarter - 1) * 3 + 1, 1)
                current_end = today
                prev_quarter = quarter - 1 if quarter > 1 else 4
                prev_year = today.year if quarter > 1 else today.year - 1
                prev_start = date(prev_year, (prev_quarter - 1) * 3 + 1, 1)
                prev_end = current_start - timedelta(days=1)
            else:  # year
                current_start = date(today.year, 1, 1)
                current_end = today
                prev_start = date(today.year - 1, 1, 1)
                prev_end = date(today.year - 1, 12, 31)

            # Current period metrics
            current_applications = session.query(func.count(EmployeeLoan.loan_id)).filter(
                EmployeeLoan.application_date.between(current_start, current_end)
            ).scalar() or 0

            current_approvals = session.query(func.count(EmployeeLoan.loan_id)).filter(
                EmployeeLoan.approval_date.between(current_start, current_end)
            ).scalar() or 0

            current_disbursements = session.query(func.sum(LoanTransaction.amount)).filter(
                LoanTransaction.transaction_type == 'DISBURSEMENT',
                LoanTransaction.transaction_date.between(current_start, current_end)
            ).scalar() or 0

            current_repayments = session.query(func.sum(LoanTransaction.amount)).filter(
                LoanTransaction.transaction_type == 'REPAYMENT',
                LoanTransaction.transaction_date.between(current_start, current_end)
            ).scalar() or 0

            # Previous period metrics
            prev_applications = session.query(func.count(EmployeeLoan.loan_id)).filter(
                EmployeeLoan.application_date.between(prev_start, prev_end)
            ).scalar() or 0

            prev_approvals = session.query(func.count(EmployeeLoan.loan_id)).filter(
                EmployeeLoan.approval_date.between(prev_start, prev_end)
            ).scalar() or 0

            prev_disbursements = session.query(func.sum(LoanTransaction.amount)).filter(
                LoanTransaction.transaction_type == 'DISBURSEMENT',
                LoanTransaction.transaction_date.between(prev_start, prev_end)
            ).scalar() or 0

            prev_repayments = session.query(func.sum(LoanTransaction.amount)).filter(
                LoanTransaction.transaction_type == 'REPAYMENT',
                LoanTransaction.transaction_date.between(prev_start, prev_end)
            ).scalar() or 0

            # Calculate percentage changes
            def calculate_change(current, previous):
                if previous == 0:
                    return 100 if current > 0 else 0
                return round((current - previous) / previous * 100, 2)

            # Loan type performance
            loan_type_performance = session.query(
                LoanType.name,
                LoanType.code,
                func.count(EmployeeLoan.loan_id).label('applications'),
                func.count(case([(EmployeeLoan.status.in_(['APPROVED', 'DISBURSED', 'ACTIVE', 'COMPLETED']), 1)])).label('approvals'),
                func.avg(func.extract('day', EmployeeLoan.approval_date - EmployeeLoan.application_date)).label('avg_approval_days')
            ).join(EmployeeLoan).filter(
                EmployeeLoan.application_date.between(current_start, current_end)
            ).group_by(LoanType.loan_type_id, LoanType.name, LoanType.code).all()

            return jsonify({
                "success": True,
                "data": {
                    "comparison_period": comparison_period,
                    "current_period": {
                        "start_date": current_start.strftime('%Y-%m-%d'),
                        "end_date": current_end.strftime('%Y-%m-%d'),
                        "applications": current_applications,
                        "approvals": current_approvals,
                        "approval_rate": round(current_approvals / current_applications * 100, 2) if current_applications > 0 else 0,
                        "disbursements": float(current_disbursements),
                        "repayments": float(current_repayments),
                        "net_flow": float(current_repayments - current_disbursements)
                    },
                    "previous_period": {
                        "start_date": prev_start.strftime('%Y-%m-%d'),
                        "end_date": prev_end.strftime('%Y-%m-%d'),
                        "applications": prev_applications,
                        "approvals": prev_approvals,
                        "approval_rate": round(prev_approvals / prev_applications * 100, 2) if prev_applications > 0 else 0,
                        "disbursements": float(prev_disbursements),
                        "repayments": float(prev_repayments),
                        "net_flow": float(prev_repayments - prev_disbursements)
                    },
                    "performance_changes": {
                        "applications_change": calculate_change(current_applications, prev_applications),
                        "approvals_change": calculate_change(current_approvals, prev_approvals),
                        "disbursements_change": calculate_change(float(current_disbursements), float(prev_disbursements)),
                        "repayments_change": calculate_change(float(current_repayments), float(prev_repayments))
                    },
                    "loan_type_performance": [
                        {
                            "loan_type": perf.name,
                            "code": perf.code,
                            "applications": perf.applications,
                            "approvals": perf.approvals,
                            "approval_rate": round(perf.approvals / perf.applications * 100, 2) if perf.applications > 0 else 0,
                            "avg_approval_days": round(float(perf.avg_approval_days or 0), 1)
                        } for perf in loan_type_performance
                    ]
                }
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting loan performance metrics: {e}")
        return jsonify({"error": "Internal server error"}), 500
