from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from application.Models.employees.loan_type import LoanType
from application.Models.company import Company
from application.Models.Msg import Msg
from application.utils.db_connection import DatabaseConnection
import jsons
from datetime import datetime, date
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.Helpers.helper_methods import HelperMethods
from decimal import Decimal

loan_configuration_api = Blueprint('loan_configuration_api', __name__)

@loan_configuration_api.route('/api/loans/types/configure', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def create_loan_type():
    """
    Create a new loan type with HR-configurable parameters.
    
    Request body:
    {
        "name": "Personal Loan",
        "code": "PERSONAL",
        "description": "Personal loan for various financial needs",
        "min_amount": 10000,
        "max_amount": 500000,
        "max_salary_multiple": 6.0,
        "interest_rate": 0.12,
        "interest_calculation_method": "SIMPLE",
        "min_term_months": 6,
        "max_term_months": 60,
        "max_deduction_percentage": 0.33,
        "allow_early_repayment": true,
        "early_repayment_penalty_rate": 0.02,
        "requires_approval": true,
        "auto_approve_limit": 100000,
        "approval_levels": 2,
        "min_employment_months": 12,
        "max_active_loans": 1,
        "requires_guarantor": false
    }
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        data = request.get_json()
        if not data:
            return jsonify({"error": "Request body is required"}), 400

        # Validate required fields
        required_fields = ['name', 'code']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"{field} is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            # Check if loan type code already exists
            existing = LoanType.get_loan_type_by_code(session, data['code'])
            if existing:
                return jsonify({"error": f"Loan type with code '{data['code']}' already exists"}), 400

            # Create loan type with HR-configured parameters
            loan_type = LoanType.create_loan_type(session, **data)

            if loan_type:
                return jsonify({
                    "success": True,
                    "data": loan_type.to_dict(),
                    "message": "Loan type created successfully"
                }), 201
            else:
                return jsonify({"error": "Failed to create loan type"}), 500

    except ValueError as e:
        app.logger.error(f"Validation error in loan type creation: {e}")
        return jsonify({"error": str(e)}), 400
    except Exception as e:
        app.logger.error(f"Error creating loan type: {e}")
        return jsonify({"error": "Internal server error"}), 500

@loan_configuration_api.route('/api/loans/types/<loan_type_id>/configure', methods=['PUT'])
@token_required
@roles_required('admin', 'hr')
def update_loan_type(loan_type_id):
    """
    Update loan type parameters - HR can change any setting.
    
    Request body: Same as create, but all fields are optional
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        data = request.get_json()
        if not data:
            return jsonify({"error": "Request body is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            # Get existing loan type
            loan_type = LoanType.get_loan_type_by_id(session, loan_type_id)
            if not loan_type:
                return jsonify({"error": "Loan type not found"}), 404

            # Update fields that are provided
            updatable_fields = [
                'name', 'description', 'min_amount', 'max_amount', 'max_salary_multiple',
                'interest_rate', 'interest_calculation_method', 'min_term_months', 'max_term_months',
                'max_deduction_percentage', 'allow_early_repayment', 'early_repayment_penalty_rate',
                'requires_approval', 'auto_approve_limit', 'approval_levels',
                'min_employment_months', 'max_active_loans', 'requires_guarantor', 'is_active'
            ]

            for field in updatable_fields:
                if field in data:
                    setattr(loan_type, field, data[field])

            loan_type.updated_at = datetime.now()
            session.commit()

            app.logger.info(f"Updated loan type {loan_type.code} by user {g.current_user_id}")

            return jsonify({
                "success": True,
                "data": loan_type.to_dict(),
                "message": "Loan type updated successfully"
            }), 200

    except ValueError as e:
        app.logger.error(f"Validation error in loan type update: {e}")
        return jsonify({"error": str(e)}), 400
    except Exception as e:
        app.logger.error(f"Error updating loan type: {e}")
        return jsonify({"error": "Internal server error"}), 500

@loan_configuration_api.route('/api/loans/types/<loan_type_id>/quick-update', methods=['PATCH'])
@token_required
@roles_required('admin', 'hr')
def quick_update_loan_parameters(loan_type_id):
    """
    Quick update for common parameters HR changes frequently.
    
    Request body:
    {
        "max_salary_multiple": 8.0,     // Change from 6x to 8x salary
        "interest_rate": 0.10,          // Change from 12% to 10%
        "max_deduction_percentage": 0.40, // Change from 33% to 40%
        "auto_approve_limit": 200000    // Change auto-approve limit
    }
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        data = request.get_json()
        if not data:
            return jsonify({"error": "Request body is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            # Get existing loan type
            loan_type = LoanType.get_loan_type_by_id(session, loan_type_id)
            if not loan_type:
                return jsonify({"error": "Loan type not found"}), 404

            # Track changes for audit
            changes = {}
            
            # Quick update fields
            quick_fields = {
                'max_salary_multiple': 'Maximum Salary Multiple',
                'interest_rate': 'Interest Rate',
                'max_deduction_percentage': 'Maximum Deduction Percentage',
                'auto_approve_limit': 'Auto Approve Limit',
                'min_employment_months': 'Minimum Employment Months',
                'max_active_loans': 'Maximum Active Loans'
            }

            for field, description in quick_fields.items():
                if field in data:
                    old_value = getattr(loan_type, field)
                    new_value = data[field]
                    if old_value != new_value:
                        changes[description] = {
                            'old_value': float(old_value) if old_value else None,
                            'new_value': float(new_value) if new_value else None
                        }
                        setattr(loan_type, field, new_value)

            if changes:
                loan_type.updated_at = datetime.now()
                session.commit()

                app.logger.info(f"Quick updated loan type {loan_type.code}: {changes}")

                return jsonify({
                    "success": True,
                    "data": loan_type.to_dict(),
                    "changes": changes,
                    "message": f"Updated {len(changes)} parameters successfully"
                }), 200
            else:
                return jsonify({
                    "success": True,
                    "message": "No changes detected"
                }), 200

    except Exception as e:
        app.logger.error(f"Error in quick update: {e}")
        return jsonify({"error": "Internal server error"}), 500

@loan_configuration_api.route('/api/loans/types/<loan_type_id>/preview-impact', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def preview_parameter_impact(loan_type_id):
    """
    Preview the impact of parameter changes before applying them.
    
    Request body:
    {
        "proposed_changes": {
            "max_salary_multiple": 8.0,
            "interest_rate": 0.10
        },
        "test_scenarios": [
            {"salary": 300000, "amount": 1000000, "term_months": 24},
            {"salary": 500000, "amount": 2000000, "term_months": 36}
        ]
    }
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        data = request.get_json()
        if not data:
            return jsonify({"error": "Request body is required"}), 400

        proposed_changes = data.get('proposed_changes', {})
        test_scenarios = data.get('test_scenarios', [])

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            # Get existing loan type
            loan_type = LoanType.get_loan_type_by_id(session, loan_type_id)
            if not loan_type:
                return jsonify({"error": "Loan type not found"}), 404

            # Create a temporary copy with proposed changes
            temp_loan_type = LoanType()
            for attr in dir(loan_type):
                if not attr.startswith('_') and hasattr(temp_loan_type, attr):
                    setattr(temp_loan_type, attr, getattr(loan_type, attr))

            # Apply proposed changes to temp copy
            for field, value in proposed_changes.items():
                if hasattr(temp_loan_type, field):
                    setattr(temp_loan_type, field, value)

            # Test scenarios
            impact_analysis = {
                "current_parameters": loan_type.to_dict(),
                "proposed_parameters": {field: value for field, value in proposed_changes.items()},
                "scenario_comparisons": []
            }

            for scenario in test_scenarios:
                salary = scenario.get('salary', 300000)
                amount = scenario.get('amount', 1000000)
                term_months = scenario.get('term_months', 24)

                # Current calculations
                current_max = loan_type.calculate_max_loan_amount(salary)
                current_valid, current_errors = loan_type.validate_loan_terms(amount, term_months, salary)
                current_monthly = loan_type.calculate_monthly_payment(amount, term_months) if current_valid else 0

                # Proposed calculations
                proposed_max = temp_loan_type.calculate_max_loan_amount(salary)
                proposed_valid, proposed_errors = temp_loan_type.validate_loan_terms(amount, term_months, salary)
                proposed_monthly = temp_loan_type.calculate_monthly_payment(amount, term_months) if proposed_valid else 0

                impact_analysis["scenario_comparisons"].append({
                    "scenario": scenario,
                    "current": {
                        "max_loan_amount": float(current_max) if current_max else None,
                        "is_valid": current_valid,
                        "errors": current_errors,
                        "monthly_payment": float(current_monthly)
                    },
                    "proposed": {
                        "max_loan_amount": float(proposed_max) if proposed_max else None,
                        "is_valid": proposed_valid,
                        "errors": proposed_errors,
                        "monthly_payment": float(proposed_monthly)
                    },
                    "impact": {
                        "max_amount_change": float(proposed_max - current_max) if (proposed_max and current_max) else None,
                        "monthly_payment_change": float(proposed_monthly - current_monthly) if (proposed_monthly and current_monthly) else None,
                        "eligibility_change": proposed_valid != current_valid
                    }
                })

            return jsonify({
                "success": True,
                "data": impact_analysis,
                "message": "Parameter impact analysis completed"
            }), 200

    except Exception as e:
        app.logger.error(f"Error in impact preview: {e}")
        return jsonify({"error": "Internal server error"}), 500

@loan_configuration_api.route('/api/loans/types/bulk-update', methods=['POST'])
@token_required
@roles_required('admin')
def bulk_update_loan_types():
    """
    Bulk update multiple loan types - useful for company-wide policy changes.
    
    Request body:
    {
        "updates": [
            {
                "loan_type_id": "uuid1",
                "changes": {"interest_rate": 0.10}
            },
            {
                "loan_type_id": "uuid2", 
                "changes": {"max_salary_multiple": 8.0}
            }
        ]
    }
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        data = request.get_json()
        if not data or 'updates' not in data:
            return jsonify({"error": "Updates array is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        results = []
        
        with DatabaseConnection.get_session(database_name) as session:
            for update in data['updates']:
                loan_type_id = update.get('loan_type_id')
                changes = update.get('changes', {})
                
                try:
                    loan_type = LoanType.get_loan_type_by_id(session, loan_type_id)
                    if not loan_type:
                        results.append({
                            "loan_type_id": loan_type_id,
                            "success": False,
                            "error": "Loan type not found"
                        })
                        continue

                    # Apply changes
                    for field, value in changes.items():
                        if hasattr(loan_type, field):
                            setattr(loan_type, field, value)

                    loan_type.updated_at = datetime.now()
                    
                    results.append({
                        "loan_type_id": loan_type_id,
                        "loan_type_name": loan_type.name,
                        "success": True,
                        "changes_applied": len(changes)
                    })

                except Exception as e:
                    results.append({
                        "loan_type_id": loan_type_id,
                        "success": False,
                        "error": str(e)
                    })

            session.commit()

        successful_updates = len([r for r in results if r['success']])
        
        return jsonify({
            "success": True,
            "data": results,
            "summary": {
                "total_updates": len(data['updates']),
                "successful": successful_updates,
                "failed": len(data['updates']) - successful_updates
            },
            "message": f"Bulk update completed: {successful_updates} successful"
        }), 200

    except Exception as e:
        app.logger.error(f"Error in bulk update: {e}")
        return jsonify({"error": "Internal server error"}), 500
