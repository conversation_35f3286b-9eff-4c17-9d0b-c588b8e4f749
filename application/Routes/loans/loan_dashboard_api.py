from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from application.Models.employees.loan_type import LoanType
from application.Models.employees.employee_loan import Employee<PERSON>oan
from application.Models.employees.loan_repayment_schedule import LoanRepaymentSchedule
from application.Models.employees.loan_transaction import LoanTransaction
from application.Models.company import Company
from application.utils.db_connection import DatabaseConnection
from datetime import datetime, date, timedelta
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from decimal import Decimal
from sqlalchemy import func, and_

loan_dashboard_api = Blueprint('loan_dashboard_api', __name__)

@loan_dashboard_api.route('/api/loans/dashboard/overview', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_loan_dashboard_overview():
    """
    Get comprehensive loan dashboard overview for HR.
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            # Loan Type Statistics
            loan_types = LoanType.get_active_loan_types(session)
            loan_type_stats = []
            
            for loan_type in loan_types:
                active_loans = session.query(EmployeeLoan).filter(
                    EmployeeLoan.loan_type_id == loan_type.loan_type_id,
                    EmployeeLoan.status.in_(['ACTIVE', 'DISBURSED'])
                ).count()
                
                total_disbursed = session.query(func.sum(EmployeeLoan.amount_disbursed)).filter(
                    EmployeeLoan.loan_type_id == loan_type.loan_type_id,
                    EmployeeLoan.status.in_(['ACTIVE', 'DISBURSED', 'COMPLETED'])
                ).scalar() or 0
                
                outstanding_balance = session.query(func.sum(EmployeeLoan.outstanding_balance)).filter(
                    EmployeeLoan.loan_type_id == loan_type.loan_type_id,
                    EmployeeLoan.status.in_(['ACTIVE', 'DISBURSED'])
                ).scalar() or 0
                
                loan_type_stats.append({
                    "loan_type": loan_type.to_dict(),
                    "active_loans": active_loans,
                    "total_disbursed": float(total_disbursed),
                    "outstanding_balance": float(outstanding_balance)
                })

            # Overall Statistics
            total_active_loans = session.query(EmployeeLoan).filter(
                EmployeeLoan.status.in_(['ACTIVE', 'DISBURSED'])
            ).count()
            
            total_outstanding = session.query(func.sum(EmployeeLoan.outstanding_balance)).filter(
                EmployeeLoan.status.in_(['ACTIVE', 'DISBURSED'])
            ).scalar() or 0
            
            pending_applications = session.query(EmployeeLoan).filter(
                EmployeeLoan.status == 'PENDING'
            ).count()
            
            overdue_installments = session.query(LoanRepaymentSchedule).filter(
                LoanRepaymentSchedule.due_date < date.today(),
                LoanRepaymentSchedule.status.in_(['PENDING', 'PARTIAL'])
            ).count()

            # Monthly Trends (last 12 months)
            monthly_trends = []
            for i in range(12):
                month_date = date.today().replace(day=1) - timedelta(days=30*i)
                
                disbursements = session.query(func.sum(LoanTransaction.amount)).filter(
                    LoanTransaction.transaction_type == 'DISBURSEMENT',
                    func.extract('year', LoanTransaction.transaction_date) == month_date.year,
                    func.extract('month', LoanTransaction.transaction_date) == month_date.month
                ).scalar() or 0
                
                repayments = session.query(func.sum(LoanTransaction.amount)).filter(
                    LoanTransaction.transaction_type == 'REPAYMENT',
                    func.extract('year', LoanTransaction.transaction_date) == month_date.year,
                    func.extract('month', LoanTransaction.transaction_date) == month_date.month
                ).scalar() or 0
                
                monthly_trends.append({
                    "month": month_date.strftime('%Y-%m'),
                    "disbursements": float(disbursements),
                    "repayments": float(repayments),
                    "net_flow": float(repayments - disbursements)
                })

            return jsonify({
                "success": True,
                "data": {
                    "overview": {
                        "total_active_loans": total_active_loans,
                        "total_outstanding": float(total_outstanding),
                        "pending_applications": pending_applications,
                        "overdue_installments": overdue_installments
                    },
                    "loan_type_statistics": loan_type_stats,
                    "monthly_trends": list(reversed(monthly_trends))  # Most recent first
                }
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting loan dashboard overview: {e}")
        return jsonify({"error": "Internal server error"}), 500

@loan_dashboard_api.route('/api/loans/dashboard/configuration-summary', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_configuration_summary():
    """
    Get summary of all loan type configurations for HR review.
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            loan_types = LoanType.get_active_loan_types(session)
            
            configuration_summary = []
            
            for loan_type in loan_types:
                # Calculate usage statistics
                total_applications = session.query(EmployeeLoan).filter(
                    EmployeeLoan.loan_type_id == loan_type.loan_type_id
                ).count()
                
                approved_applications = session.query(EmployeeLoan).filter(
                    EmployeeLoan.loan_type_id == loan_type.loan_type_id,
                    EmployeeLoan.status.in_(['APPROVED', 'DISBURSED', 'ACTIVE', 'COMPLETED'])
                ).count()
                
                approval_rate = (approved_applications / total_applications * 100) if total_applications > 0 else 0
                
                # Key configuration highlights
                config_highlights = {
                    "max_salary_multiple": f"{loan_type.max_salary_multiple}x salary" if loan_type.max_salary_multiple else "No limit",
                    "interest_rate": f"{float(loan_type.interest_rate * 100):.1f}% annually",
                    "max_term": f"{loan_type.max_term_months} months",
                    "max_deduction": f"{float(loan_type.max_deduction_percentage * 100):.0f}% of salary",
                    "auto_approve_limit": f"RWF {float(loan_type.auto_approve_limit):,.0f}" if loan_type.auto_approve_limit else "None",
                    "min_employment": f"{loan_type.min_employment_months} months"
                }
                
                configuration_summary.append({
                    "loan_type": {
                        "id": str(loan_type.loan_type_id),
                        "name": loan_type.name,
                        "code": loan_type.code,
                        "is_active": loan_type.is_active
                    },
                    "configuration_highlights": config_highlights,
                    "usage_statistics": {
                        "total_applications": total_applications,
                        "approved_applications": approved_applications,
                        "approval_rate": round(approval_rate, 1)
                    },
                    "last_updated": loan_type.updated_at.strftime('%Y-%m-%d %H:%M:%S') if loan_type.updated_at else None
                })

            return jsonify({
                "success": True,
                "data": {
                    "total_loan_types": len(loan_types),
                    "configurations": configuration_summary
                }
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting configuration summary: {e}")
        return jsonify({"error": "Internal server error"}), 500

@loan_dashboard_api.route('/api/loans/dashboard/parameter-usage', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_parameter_usage_analysis():
    """
    Analyze how current loan parameters are being utilized.
    Helps HR understand if limits are too high/low.
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            loan_types = LoanType.get_active_loan_types(session)
            
            parameter_analysis = []
            
            for loan_type in loan_types:
                # Get all approved loans for this type
                loans = session.query(EmployeeLoan).filter(
                    EmployeeLoan.loan_type_id == loan_type.loan_type_id,
                    EmployeeLoan.status.in_(['APPROVED', 'DISBURSED', 'ACTIVE', 'COMPLETED'])
                ).all()
                
                if not loans:
                    continue
                
                # Analyze loan amounts vs limits
                loan_amounts = [float(loan.principal_amount) for loan in loans]
                max_amount_used = max(loan_amounts) if loan_amounts else 0
                avg_amount_used = sum(loan_amounts) / len(loan_amounts) if loan_amounts else 0
                
                # Analyze terms vs limits
                terms_used = [loan.term_months for loan in loans]
                max_term_used = max(terms_used) if terms_used else 0
                avg_term_used = sum(terms_used) / len(terms_used) if terms_used else 0
                
                # Calculate utilization rates
                max_possible_amount = float(loan_type.max_amount) if loan_type.max_amount else None
                amount_utilization = (max_amount_used / max_possible_amount * 100) if max_possible_amount else None
                
                term_utilization = (max_term_used / loan_type.max_term_months * 100) if loan_type.max_term_months else None
                
                parameter_analysis.append({
                    "loan_type": {
                        "name": loan_type.name,
                        "code": loan_type.code
                    },
                    "amount_analysis": {
                        "max_limit": float(loan_type.max_amount) if loan_type.max_amount else None,
                        "max_used": max_amount_used,
                        "average_used": round(avg_amount_used, 2),
                        "utilization_rate": round(amount_utilization, 1) if amount_utilization else None,
                        "recommendation": self._get_amount_recommendation(amount_utilization)
                    },
                    "term_analysis": {
                        "max_limit": loan_type.max_term_months,
                        "max_used": max_term_used,
                        "average_used": round(avg_term_used, 1),
                        "utilization_rate": round(term_utilization, 1) if term_utilization else None,
                        "recommendation": self._get_term_recommendation(term_utilization)
                    },
                    "total_loans_analyzed": len(loans)
                })

            return jsonify({
                "success": True,
                "data": {
                    "parameter_analysis": parameter_analysis,
                    "analysis_date": date.today().strftime('%Y-%m-%d')
                }
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting parameter usage analysis: {e}")
        return jsonify({"error": "Internal server error"}), 500

def _get_amount_recommendation(utilization_rate):
    """Get recommendation based on amount utilization."""
    if utilization_rate is None:
        return "No limit set - consider setting maximum amount"
    elif utilization_rate < 30:
        return "Low utilization - consider reducing maximum amount"
    elif utilization_rate > 80:
        return "High utilization - consider increasing maximum amount"
    else:
        return "Good utilization - current limit is appropriate"

def _get_term_recommendation(utilization_rate):
    """Get recommendation based on term utilization."""
    if utilization_rate is None:
        return "No analysis available"
    elif utilization_rate < 40:
        return "Low utilization - consider reducing maximum term"
    elif utilization_rate > 85:
        return "High utilization - consider increasing maximum term"
    else:
        return "Good utilization - current term limit is appropriate"

@loan_dashboard_api.route('/api/loans/dashboard/quick-actions', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_quick_actions():
    """
    Get quick actions HR can take - pending approvals, overdue payments, etc.
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            # Pending loan applications
            pending_loans = session.query(EmployeeLoan).filter(
                EmployeeLoan.status == 'PENDING'
            ).order_by(EmployeeLoan.application_date).limit(10).all()

            # Overdue installments
            overdue_installments = session.query(LoanRepaymentSchedule).join(
                EmployeeLoan
            ).filter(
                LoanRepaymentSchedule.due_date < date.today(),
                LoanRepaymentSchedule.status.in_(['PENDING', 'PARTIAL'])
            ).order_by(LoanRepaymentSchedule.due_date).limit(10).all()

            # Loans nearing completion (last 2 installments)
            completing_loans = session.query(EmployeeLoan).filter(
                EmployeeLoan.status == 'ACTIVE',
                EmployeeLoan.outstanding_balance <= EmployeeLoan.monthly_deduction * 2
            ).limit(5).all()

            quick_actions = {
                "pending_approvals": {
                    "count": len(pending_loans),
                    "items": [
                        {
                            "loan_id": str(loan.loan_id),
                            "employee_name": f"{loan.employee.first_name} {loan.employee.last_name}",
                            "loan_type": loan.loan_type.name,
                            "amount": float(loan.principal_amount),
                            "application_date": loan.application_date.strftime('%Y-%m-%d'),
                            "days_pending": (date.today() - loan.application_date).days
                        } for loan in pending_loans
                    ]
                },
                "overdue_payments": {
                    "count": len(overdue_installments),
                    "items": [
                        {
                            "loan_number": installment.loan.loan_number,
                            "employee_name": f"{installment.loan.employee.first_name} {installment.loan.employee.last_name}",
                            "due_date": installment.due_date.strftime('%Y-%m-%d'),
                            "amount": float(installment.amount - installment.amount_paid),
                            "days_overdue": (date.today() - installment.due_date).days
                        } for installment in overdue_installments
                    ]
                },
                "completing_loans": {
                    "count": len(completing_loans),
                    "items": [
                        {
                            "loan_number": loan.loan_number,
                            "employee_name": f"{loan.employee.first_name} {loan.employee.last_name}",
                            "remaining_balance": float(loan.outstanding_balance),
                            "estimated_completion": loan.expected_completion_date.strftime('%Y-%m-%d') if loan.expected_completion_date else None
                        } for loan in completing_loans
                    ]
                }
            }

            return jsonify({
                "success": True,
                "data": quick_actions
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting quick actions: {e}")
        return jsonify({"error": "Internal server error"}), 500
