from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from application.Models.employees import Employee, EmployeeS<PERSON>ry
from application.Models.employees.loan_type import LoanType
from application.Models.employees.employee_loan import EmployeeLoan
from application.Models.employees.loan_repayment_schedule import LoanRepaymentSchedule
from application.Models.employees.loan_transaction import LoanTransaction
from application.Models.company import Company
from application.Models.Msg import Msg
from application.utils.db_connection import DatabaseConnection
import jsons
from datetime import datetime, date
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.Helpers.helper_methods import HelperMethods
from decimal import Decimal

loan_management_api = Blueprint('loan_management_api', __name__)

@loan_management_api.route('/api/loans/types', methods=['GET'])
@token_required
@roles_required('admin', 'hr', 'employee')
def get_loan_types():
    """Get all active loan types."""
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            loan_types = LoanType.get_active_loan_types(session)
            
            return jsonify({
                "success": True,
                "data": [loan_type.to_dict() for loan_type in loan_types],
                "total": len(loan_types)
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting loan types: {e}")
        return jsonify({"error": "Internal server error"}), 500

@loan_management_api.route('/api/loans/calculate', methods=['POST'])
@token_required
@roles_required('admin', 'hr', 'employee')
def calculate_loan_terms():
    """
    Calculate loan terms and monthly payments.
    
    Request body:
    {
        "loan_type_id": "uuid",
        "principal_amount": 50000,
        "term_months": 12,
        "employee_id": "uuid" (optional, for validation)
    }
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        data = request.get_json()
        if not data:
            return jsonify({"error": "Request body is required"}), 400

        # Validate required fields
        required_fields = ['loan_type_id', 'principal_amount', 'term_months']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"{field} is required"}), 400

        loan_type_id = data['loan_type_id']
        principal_amount = Decimal(str(data['principal_amount']))
        term_months = int(data['term_months'])
        employee_id = data.get('employee_id')

        # Ensure Decimal precision for all calculations
        def ensure_decimal(value):
            if isinstance(value, Decimal):
                return value
            elif isinstance(value, (int, float)):
                return Decimal(str(value))
            elif isinstance(value, str):
                return Decimal(value)
            else:
                return Decimal('0')

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            # Get loan type
            loan_type = LoanType.get_loan_type_by_id(session, loan_type_id)
            if not loan_type:
                return jsonify({"error": "Loan type not found"}), 404

            # Get employee salary if employee_id provided
            employee_salary = None
            if employee_id:
                salary = EmployeeSalary.get_current_salary(session, employee_id)
                employee_salary = salary.basic_salary if salary else None

            # Validate loan terms
            is_valid, errors = loan_type.validate_loan_terms(
                principal_amount, term_months, employee_salary
            )

            # Calculate loan details using Decimal precision
            total_interest = loan_type.calculate_interest(principal_amount, term_months)
            total_amount = principal_amount + total_interest
            monthly_payment = loan_type.calculate_monthly_payment(principal_amount, term_months)

            # Calculate maximum allowed amount
            max_amount = loan_type.calculate_max_loan_amount(employee_salary) if employee_salary else loan_type.max_amount

            return jsonify({
                "success": True,
                "data": {
                    "loan_type": loan_type.to_dict(convert_decimals_to_float=True),
                    "calculation": {
                        "principal_amount": float(principal_amount),
                        "term_months": term_months,
                        "interest_rate": float(loan_type.interest_rate),
                        "total_interest": float(total_interest),
                        "total_amount": float(total_amount),
                        "monthly_payment": float(monthly_payment),
                        "max_allowed_amount": float(max_amount) if max_amount else None
                    },
                    "validation": {
                        "is_valid": is_valid,
                        "errors": errors
                    }
                }
            }), 200

    except ValueError as e:
        app.logger.error(f"Validation error in loan calculation: {e}")
        return jsonify({"error": str(e)}), 400
    except Exception as e:
        app.logger.error(f"Error calculating loan terms: {e}")
        return jsonify({"error": "Internal server error"}), 500

@loan_management_api.route('/api/loans/apply', methods=['POST'])
@token_required
@roles_required('admin', 'hr', 'employee')
def apply_for_loan():
    """
    Apply for a loan.
    
    Request body:
    {
        "employee_id": "uuid",
        "loan_type_id": "uuid", 
        "principal_amount": 50000,
        "term_months": 12,
        "purpose": "Personal expenses"
    }
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        data = request.get_json()
        if not data:
            return jsonify({"error": "Request body is required"}), 400

        # Validate required fields
        required_fields = ['employee_id', 'loan_type_id', 'principal_amount', 'term_months']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"{field} is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            # Verify employee exists
            employee = session.query(Employee).filter_by(employee_id=data['employee_id']).first()
            if not employee:
                return jsonify({"error": "Employee not found"}), 404

            # Create loan application
            loan, error = EmployeeLoan.create_loan_application(
                session=session,
                employee_id=data['employee_id'],
                loan_type_id=data['loan_type_id'],
                principal_amount=data['principal_amount'],
                term_months=data['term_months'],
                purpose=data.get('purpose')
            )

            if not loan:
                return jsonify({"error": error}), 400

            # Create approval workflow using existing system
            from application.Models.approval.approval_workflow import ApprovalWorkflow
            workflow, workflow_error = ApprovalWorkflow.create_workflow(
                session=session,
                entity_type='loan_request',
                entity_id=loan.loan_id
            )

            if workflow_error:
                app.logger.warning(f"Could not create approval workflow: {workflow_error}")

            return jsonify({
                "success": True,
                "data": {
                    "loan": loan.to_dict(),
                    "workflow_id": str(workflow.workflow_id) if workflow else None
                },
                "message": "Loan application submitted successfully"
            }), 201

    except ValueError as e:
        app.logger.error(f"Validation error in loan application: {e}")
        return jsonify({"error": str(e)}), 400
    except Exception as e:
        app.logger.error(f"Error applying for loan: {e}")
        return jsonify({"error": "Internal server error"}), 500

@loan_management_api.route('/api/loans/<loan_id>/approve', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def approve_loan(loan_id):
    """
    Approve a loan application.
    
    Request body:
    {
        "disbursement_date": "2025-01-15" (optional)
    }
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        data = request.get_json() or {}
        disbursement_date = None
        
        if data.get('disbursement_date'):
            disbursement_date = datetime.strptime(data['disbursement_date'], '%Y-%m-%d').date()

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            # Get loan
            loan = session.query(EmployeeLoan).filter_by(loan_id=loan_id).first()
            if not loan:
                return jsonify({"error": "Loan not found"}), 404

            if loan.status != 'PENDING':
                return jsonify({"error": f"Loan is already {loan.status.lower()}"}), 400

            # Approve loan
            success, error = loan.approve_loan(
                session=session,
                approved_by=g.current_user_id,  # From token
                disbursement_date=disbursement_date
            )

            if not success:
                return jsonify({"error": error}), 400

            # Update approval workflow
            from application.Models.approval.approval_workflow import ApprovalWorkflow
            workflow = ApprovalWorkflow.get_workflow_by_entity(session, 'loan_request', loan_id)
            if workflow:
                ApprovalWorkflow.approve_workflow(session, workflow.workflow_id, g.current_user_id)

            return jsonify({
                "success": True,
                "data": loan.to_dict(),
                "message": "Loan approved successfully"
            }), 200

    except Exception as e:
        app.logger.error(f"Error approving loan: {e}")
        return jsonify({"error": "Internal server error"}), 500

@loan_management_api.route('/api/loans', methods=['GET'])
@token_required
@roles_required('admin', 'hr', 'employee')
def get_loans():
    """
    Get loans with optional filtering.
    
    Query parameters:
    - employee_id: Filter by employee
    - status: Filter by status
    - loan_type_id: Filter by loan type
    - page: Page number (default: 1)
    - per_page: Items per page (default: 20)
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get filters
        employee_id = request.args.get('employee_id')
        status = request.args.get('status')
        loan_type_id = request.args.get('loan_type_id')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            # Build query
            query = session.query(EmployeeLoan)

            if employee_id:
                query = query.filter_by(employee_id=employee_id)
            
            if status:
                query = query.filter_by(status=status)
            
            if loan_type_id:
                query = query.filter_by(loan_type_id=loan_type_id)

            # For employees, only show their own loans
            if g.current_user_role == 'employee':
                # You'll need to get employee_id from user context
                query = query.filter_by(employee_id=g.current_employee_id)

            # Get total count
            total = query.count()

            # Apply pagination
            loans = query.order_by(EmployeeLoan.created_at.desc()).offset(
                (page - 1) * per_page
            ).limit(per_page).all()

            return jsonify({
                "success": True,
                "data": [loan.to_dict() for loan in loans],
                "pagination": {
                    "page": page,
                    "per_page": per_page,
                    "total": total,
                    "pages": (total + per_page - 1) // per_page
                }
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting loans: {e}")
        return jsonify({"error": "Internal server error"}), 500
