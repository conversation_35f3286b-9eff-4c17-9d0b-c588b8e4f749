from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from application.Models.onboarding.onboarding_instance import OnboardingInstance
from application.Models.onboarding.onboarding_workflow import OnboardingWorkflow
from application.Models.onboarding.onboarding_task import OnboardingTask
from application.Models.onboarding.onboarding_document import OnboardingDocument
from application.Models.onboarding.onboarding_feedback import OnboardingFeedback
from application.Models.onboarding.onboarding_asset import OnboardingAsset
from application.Models.employees import Employee
from application.Models.company import Company
from application.utils.db_connection import DatabaseConnection
from datetime import datetime, date, timedelta
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from sqlalchemy import func, and_, extract, case
from calendar import monthrange
import calendar

onboarding_analytics_api = Blueprint('onboarding_analytics_api', __name__)

@onboarding_analytics_api.route('/api/onboarding/analytics/overview', methods=['GET'])
@token_required
@roles_required('admin', 'hr', 'finance')
def get_onboarding_analytics_overview():
    """
    Get comprehensive onboarding analytics overview.
    
    Query parameters:
    - company_id: Required
    - period: 'month', 'quarter', 'year' (default: 'month')
    - year: Specific year (default: current year)
    - month: Specific month (default: current month, only if period='month')
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        period = request.args.get('period', 'month')
        year = int(request.args.get('year', datetime.now().year))
        month = request.args.get('month')
        
        if month:
            month = int(month)
        else:
            month = datetime.now().month if period == 'month' else None

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            # Calculate date ranges
            if period == 'month' and month:
                start_date = date(year, month, 1)
                end_date = date(year, month, monthrange(year, month)[1])
            elif period == 'quarter':
                quarter = ((month - 1) // 3) + 1 if month else 1
                start_month = (quarter - 1) * 3 + 1
                start_date = date(year, start_month, 1)
                end_month = start_month + 2
                end_date = date(year, end_month, monthrange(year, end_month)[1])
            else:  # year
                start_date = date(year, 1, 1)
                end_date = date(year, 12, 31)

            # Overall onboarding statistics
            total_onboardings = session.query(OnboardingInstance).filter(
                OnboardingInstance.start_date.between(start_date, end_date)
            ).count()
            
            completed_onboardings = session.query(OnboardingInstance).filter(
                OnboardingInstance.start_date.between(start_date, end_date),
                OnboardingInstance.status == 'COMPLETED'
            ).count()
            
            in_progress_onboardings = session.query(OnboardingInstance).filter(
                OnboardingInstance.start_date.between(start_date, end_date),
                OnboardingInstance.status == 'IN_PROGRESS'
            ).count()
            
            overdue_onboardings = session.query(OnboardingInstance).filter(
                OnboardingInstance.start_date.between(start_date, end_date),
                OnboardingInstance.expected_completion_date < date.today(),
                OnboardingInstance.status.in_(['IN_PROGRESS', 'NOT_STARTED'])
            ).count()

            # Average completion time
            avg_completion_days = session.query(
                func.avg(
                    OnboardingInstance.actual_completion_date - OnboardingInstance.start_date
                )
            ).filter(
                OnboardingInstance.start_date.between(start_date, end_date),
                OnboardingInstance.status == 'COMPLETED'
            ).scalar() or 0

            # Workflow breakdown
            workflow_stats = session.query(
                OnboardingWorkflow.name,
                OnboardingWorkflow.workflow_type,
                func.count(OnboardingInstance.instance_id).label('total_instances'),
                func.sum(case([(OnboardingInstance.status == 'COMPLETED', 1)], else_=0)).label('completed'),
                func.avg(OnboardingInstance.completion_percentage).label('avg_completion')
            ).join(OnboardingInstance).filter(
                OnboardingInstance.start_date.between(start_date, end_date)
            ).group_by(OnboardingWorkflow.name, OnboardingWorkflow.workflow_type).all()

            # Department breakdown (would need to join with Employee table)
            dept_stats = session.query(
                Employee.department_name,
                func.count(OnboardingInstance.instance_id).label('total_onboardings'),
                func.sum(case([(OnboardingInstance.status == 'COMPLETED', 1)], else_=0)).label('completed'),
                func.avg(OnboardingInstance.completion_percentage).label('avg_completion'),
                func.avg(
                    func.extract('day', OnboardingInstance.actual_completion_date - OnboardingInstance.start_date)
                ).label('avg_days')
            ).join(OnboardingInstance, Employee.employee_id == OnboardingInstance.employee_id).filter(
                OnboardingInstance.start_date.between(start_date, end_date)
            ).group_by(Employee.department_name).all()

            # Task completion statistics
            task_stats = session.query(
                OnboardingTask.task_type,
                OnboardingTask.assigned_to_role,
                func.count(OnboardingTask.task_id).label('total_tasks'),
                func.sum(case([(OnboardingTask.status == 'COMPLETED', 1)], else_=0)).label('completed_tasks'),
                func.sum(case([(OnboardingTask.due_date < date.today(), 1)], else_=0)).label('overdue_tasks'),
                func.avg(
                    func.extract('day', OnboardingTask.completed_date - OnboardingTask.assigned_date)
                ).label('avg_completion_days')
            ).join(OnboardingInstance).filter(
                OnboardingInstance.start_date.between(start_date, end_date)
            ).group_by(OnboardingTask.task_type, OnboardingTask.assigned_to_role).all()

            # Document verification statistics
            doc_stats = session.query(
                OnboardingDocument.verification_status,
                func.count(OnboardingDocument.document_id).label('count')
            ).join(OnboardingInstance).filter(
                OnboardingInstance.start_date.between(start_date, end_date)
            ).group_by(OnboardingDocument.verification_status).all()

            # Feedback statistics
            feedback_stats = OnboardingFeedback.get_feedback_statistics(
                session, date_from=start_date, date_to=end_date
            )

            # Asset assignment statistics
            asset_stats = session.query(
                OnboardingAsset.asset_type,
                OnboardingAsset.status,
                func.count(OnboardingAsset.assignment_id).label('count'),
                func.avg(OnboardingAsset.current_value).label('avg_value')
            ).join(OnboardingInstance).filter(
                OnboardingInstance.start_date.between(start_date, end_date)
            ).group_by(OnboardingAsset.asset_type, OnboardingAsset.status).all()

            return jsonify({
                "success": True,
                "data": {
                    "period_info": {
                        "period": period,
                        "year": year,
                        "month": month,
                        "start_date": start_date.strftime('%Y-%m-%d'),
                        "end_date": end_date.strftime('%Y-%m-%d')
                    },
                    "overall_statistics": {
                        "total_onboardings": total_onboardings,
                        "completed_onboardings": completed_onboardings,
                        "in_progress_onboardings": in_progress_onboardings,
                        "overdue_onboardings": overdue_onboardings,
                        "completion_rate": round(completed_onboardings / total_onboardings * 100, 2) if total_onboardings > 0 else 0,
                        "average_completion_days": round(float(avg_completion_days), 1),
                        "on_time_completion_rate": round((completed_onboardings - overdue_onboardings) / total_onboardings * 100, 2) if total_onboardings > 0 else 0
                    },
                    "workflow_breakdown": [
                        {
                            "workflow_name": stat.name,
                            "workflow_type": stat.workflow_type,
                            "total_instances": stat.total_instances,
                            "completed_instances": stat.completed,
                            "completion_rate": round(stat.completed / stat.total_instances * 100, 2) if stat.total_instances > 0 else 0,
                            "average_completion_percentage": round(float(stat.avg_completion or 0), 2)
                        } for stat in workflow_stats
                    ],
                    "department_breakdown": [
                        {
                            "department": stat.department_name or "Unassigned",
                            "total_onboardings": stat.total_onboardings,
                            "completed_onboardings": stat.completed,
                            "completion_rate": round(stat.completed / stat.total_onboardings * 100, 2) if stat.total_onboardings > 0 else 0,
                            "average_completion_percentage": round(float(stat.avg_completion or 0), 2),
                            "average_completion_days": round(float(stat.avg_days or 0), 1)
                        } for stat in dept_stats
                    ],
                    "task_statistics": [
                        {
                            "task_type": stat.task_type,
                            "assigned_to_role": stat.assigned_to_role,
                            "total_tasks": stat.total_tasks,
                            "completed_tasks": stat.completed_tasks,
                            "overdue_tasks": stat.overdue_tasks,
                            "completion_rate": round(stat.completed_tasks / stat.total_tasks * 100, 2) if stat.total_tasks > 0 else 0,
                            "average_completion_days": round(float(stat.avg_completion_days or 0), 1)
                        } for stat in task_stats
                    ],
                    "document_statistics": [
                        {
                            "verification_status": stat.verification_status,
                            "count": stat.count
                        } for stat in doc_stats
                    ],
                    "feedback_statistics": feedback_stats,
                    "asset_statistics": [
                        {
                            "asset_type": stat.asset_type,
                            "status": stat.status,
                            "count": stat.count,
                            "average_value": float(stat.avg_value or 0)
                        } for stat in asset_stats
                    ]
                }
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting onboarding analytics overview: {e}")
        return jsonify({"error": "Internal server error"}), 500

@onboarding_analytics_api.route('/api/onboarding/analytics/trends', methods=['GET'])
@token_required
@roles_required('admin', 'hr', 'finance')
def get_onboarding_trends():
    """
    Get onboarding trends over time (monthly/yearly).
    
    Query parameters:
    - company_id: Required
    - period: 'monthly', 'yearly' (default: 'monthly')
    - months_back: Number of months to go back (default: 12)
    - years_back: Number of years to go back (default: 3, only if period='yearly')
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        period = request.args.get('period', 'monthly')
        months_back = int(request.args.get('months_back', 12))
        years_back = int(request.args.get('years_back', 3))

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            trends = []
            
            if period == 'monthly':
                # Monthly trends
                for i in range(months_back):
                    target_date = date.today().replace(day=1) - timedelta(days=30*i)
                    month_start = target_date.replace(day=1)
                    month_end = date(target_date.year, target_date.month, 
                                   monthrange(target_date.year, target_date.month)[1])
                    
                    # Onboarding statistics
                    total_onboardings = session.query(OnboardingInstance).filter(
                        OnboardingInstance.start_date.between(month_start, month_end)
                    ).count()
                    
                    completed_onboardings = session.query(OnboardingInstance).filter(
                        OnboardingInstance.start_date.between(month_start, month_end),
                        OnboardingInstance.status == 'COMPLETED'
                    ).count()
                    
                    avg_completion_days = session.query(
                        func.avg(
                            func.extract('day', OnboardingInstance.actual_completion_date - OnboardingInstance.start_date)
                        )
                    ).filter(
                        OnboardingInstance.start_date.between(month_start, month_end),
                        OnboardingInstance.status == 'COMPLETED'
                    ).scalar() or 0
                    
                    # Task completion rate
                    total_tasks = session.query(OnboardingTask).join(OnboardingInstance).filter(
                        OnboardingInstance.start_date.between(month_start, month_end)
                    ).count()
                    
                    completed_tasks = session.query(OnboardingTask).join(OnboardingInstance).filter(
                        OnboardingInstance.start_date.between(month_start, month_end),
                        OnboardingTask.status == 'COMPLETED'
                    ).count()
                    
                    # Document verification rate
                    total_documents = session.query(OnboardingDocument).join(OnboardingInstance).filter(
                        OnboardingInstance.start_date.between(month_start, month_end)
                    ).count()
                    
                    verified_documents = session.query(OnboardingDocument).join(OnboardingInstance).filter(
                        OnboardingInstance.start_date.between(month_start, month_end),
                        OnboardingDocument.verification_status == 'VERIFIED'
                    ).count()
                    
                    # Average feedback rating
                    avg_feedback_rating = session.query(
                        func.avg(OnboardingFeedback.overall_rating)
                    ).join(OnboardingInstance).filter(
                        OnboardingInstance.start_date.between(month_start, month_end),
                        OnboardingFeedback.overall_rating.isnot(None)
                    ).scalar() or 0
                    
                    trends.append({
                        "period": target_date.strftime('%Y-%m'),
                        "period_name": target_date.strftime('%B %Y'),
                        "total_onboardings": total_onboardings,
                        "completed_onboardings": completed_onboardings,
                        "completion_rate": round(completed_onboardings / total_onboardings * 100, 2) if total_onboardings > 0 else 0,
                        "average_completion_days": round(float(avg_completion_days), 1),
                        "task_completion_rate": round(completed_tasks / total_tasks * 100, 2) if total_tasks > 0 else 0,
                        "document_verification_rate": round(verified_documents / total_documents * 100, 2) if total_documents > 0 else 0,
                        "average_feedback_rating": round(float(avg_feedback_rating), 2)
                    })
            
            else:  # yearly
                # Yearly trends
                current_year = datetime.now().year
                for i in range(years_back):
                    target_year = current_year - i
                    
                    # Similar calculations for yearly data
                    total_onboardings = session.query(OnboardingInstance).filter(
                        extract('year', OnboardingInstance.start_date) == target_year
                    ).count()
                    
                    completed_onboardings = session.query(OnboardingInstance).filter(
                        extract('year', OnboardingInstance.start_date) == target_year,
                        OnboardingInstance.status == 'COMPLETED'
                    ).count()
                    
                    avg_completion_days = session.query(
                        func.avg(
                            OnboardingInstance.actual_completion_date - OnboardingInstance.start_date
                        )
                    ).filter(
                        extract('year', OnboardingInstance.start_date) == target_year,
                        OnboardingInstance.status == 'COMPLETED'
                    ).scalar() or 0
                    
                    avg_feedback_rating = session.query(
                        func.avg(OnboardingFeedback.overall_rating)
                    ).join(OnboardingInstance).filter(
                        extract('year', OnboardingInstance.start_date) == target_year,
                        OnboardingFeedback.overall_rating.isnot(None)
                    ).scalar() or 0
                    
                    trends.append({
                        "period": str(target_year),
                        "period_name": str(target_year),
                        "total_onboardings": total_onboardings,
                        "completed_onboardings": completed_onboardings,
                        "completion_rate": round(completed_onboardings / total_onboardings * 100, 2) if total_onboardings > 0 else 0,
                        "average_completion_days": round(float(avg_completion_days), 1),
                        "average_feedback_rating": round(float(avg_feedback_rating), 2)
                    })

            return jsonify({
                "success": True,
                "data": {
                    "period_type": period,
                    "periods_analyzed": len(trends),
                    "trends": list(reversed(trends))  # Most recent first
                }
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting onboarding trends: {e}")
        return jsonify({"error": "Internal server error"}), 500

@onboarding_analytics_api.route('/api/onboarding/analytics/performance', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_onboarding_performance():
    """
    Get detailed onboarding performance metrics.

    Query parameters:
    - company_id: Required
    - workflow_id: Optional - Filter by specific workflow
    - department: Optional - Filter by department
    - date_from: Optional - Start date (YYYY-MM-DD)
    - date_to: Optional - End date (YYYY-MM-DD)
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        workflow_id = request.args.get('workflow_id')
        department = request.args.get('department')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            # Build base query
            query = session.query(OnboardingInstance)

            if workflow_id:
                query = query.filter(OnboardingInstance.workflow_id == workflow_id)

            if date_from:
                query = query.filter(OnboardingInstance.start_date >= datetime.strptime(date_from, '%Y-%m-%d').date())

            if date_to:
                query = query.filter(OnboardingInstance.start_date <= datetime.strptime(date_to, '%Y-%m-%d').date())

            if department:
                query = query.join(Employee, Employee.employee_id == OnboardingInstance.employee_id).filter(
                    Employee.department_name == department
                )

            instances = query.all()

            # Performance metrics
            total_instances = len(instances)
            completed_instances = len([i for i in instances if i.status == 'COMPLETED'])
            overdue_instances = len([i for i in instances if i.is_overdue()])

            # Time-based metrics
            completion_times = [
                (i.actual_completion_date - i.start_date).days
                for i in instances
                if i.status == 'COMPLETED' and i.actual_completion_date
            ]

            avg_completion_time = sum(completion_times) / len(completion_times) if completion_times else 0
            min_completion_time = min(completion_times) if completion_times else 0
            max_completion_time = max(completion_times) if completion_times else 0

            # Task performance
            all_tasks = []
            for instance in instances:
                all_tasks.extend(instance.tasks.all())

            task_performance = {
                "total_tasks": len(all_tasks),
                "completed_tasks": len([t for t in all_tasks if t.status == 'COMPLETED']),
                "overdue_tasks": len([t for t in all_tasks if t.is_overdue()]),
                "average_task_completion_time": 0
            }

            task_completion_times = [
                (t.completed_date.date() - t.assigned_date.date()).days
                for t in all_tasks
                if t.status == 'COMPLETED' and t.completed_date and t.assigned_date
            ]

            if task_completion_times:
                task_performance["average_task_completion_time"] = sum(task_completion_times) / len(task_completion_times)

            # Document performance
            all_documents = []
            for instance in instances:
                all_documents.extend(instance.documents.all())

            document_performance = {
                "total_documents": len(all_documents),
                "verified_documents": len([d for d in all_documents if d.verification_status == 'VERIFIED']),
                "pending_documents": len([d for d in all_documents if d.verification_status == 'PENDING']),
                "rejected_documents": len([d for d in all_documents if d.verification_status == 'REJECTED'])
            }

            # Feedback analysis
            all_feedback = []
            for instance in instances:
                all_feedback.extend(instance.feedback_entries.all())

            feedback_analysis = {
                "total_feedback_entries": len(all_feedback),
                "average_overall_rating": 0,
                "average_process_rating": 0,
                "recommendation_rate": 0
            }

            if all_feedback:
                overall_ratings = [f.overall_rating for f in all_feedback if f.overall_rating]
                process_ratings = [f.process_rating for f in all_feedback if f.process_rating]
                recommendations = [f.would_recommend for f in all_feedback if f.would_recommend is not None]

                if overall_ratings:
                    feedback_analysis["average_overall_rating"] = sum(overall_ratings) / len(overall_ratings)
                if process_ratings:
                    feedback_analysis["average_process_rating"] = sum(process_ratings) / len(process_ratings)
                if recommendations:
                    feedback_analysis["recommendation_rate"] = sum(recommendations) / len(recommendations) * 100

            # Bottleneck analysis - tasks that take longest
            task_type_performance = {}
            for task in all_tasks:
                if task.task_type not in task_type_performance:
                    task_type_performance[task.task_type] = {
                        "total": 0,
                        "completed": 0,
                        "overdue": 0,
                        "completion_times": []
                    }

                task_type_performance[task.task_type]["total"] += 1

                if task.status == 'COMPLETED':
                    task_type_performance[task.task_type]["completed"] += 1
                    if task.completed_date and task.assigned_date:
                        days = (task.completed_date.date() - task.assigned_date.date()).days
                        task_type_performance[task.task_type]["completion_times"].append(days)

                if task.is_overdue():
                    task_type_performance[task.task_type]["overdue"] += 1

            # Convert to list format
            bottleneck_analysis = []
            for task_type, stats in task_type_performance.items():
                avg_time = sum(stats["completion_times"]) / len(stats["completion_times"]) if stats["completion_times"] else 0
                completion_rate = stats["completed"] / stats["total"] * 100 if stats["total"] > 0 else 0

                bottleneck_analysis.append({
                    "task_type": task_type,
                    "total_tasks": stats["total"],
                    "completion_rate": round(completion_rate, 2),
                    "average_completion_days": round(avg_time, 1),
                    "overdue_tasks": stats["overdue"],
                    "bottleneck_score": round((100 - completion_rate) + avg_time, 2)  # Higher score = bigger bottleneck
                })

            # Sort by bottleneck score
            bottleneck_analysis.sort(key=lambda x: x["bottleneck_score"], reverse=True)

            return jsonify({
                "success": True,
                "data": {
                    "filter_info": {
                        "workflow_id": workflow_id,
                        "department": department,
                        "date_from": date_from,
                        "date_to": date_to,
                        "total_instances_analyzed": total_instances
                    },
                    "completion_metrics": {
                        "total_instances": total_instances,
                        "completed_instances": completed_instances,
                        "completion_rate": round(completed_instances / total_instances * 100, 2) if total_instances > 0 else 0,
                        "overdue_instances": overdue_instances,
                        "on_time_rate": round((completed_instances - overdue_instances) / total_instances * 100, 2) if total_instances > 0 else 0,
                        "average_completion_days": round(avg_completion_time, 1),
                        "fastest_completion_days": min_completion_time,
                        "slowest_completion_days": max_completion_time
                    },
                    "task_performance": task_performance,
                    "document_performance": document_performance,
                    "feedback_analysis": feedback_analysis,
                    "bottleneck_analysis": bottleneck_analysis[:10]  # Top 10 bottlenecks
                }
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting onboarding performance: {e}")
        return jsonify({"error": "Internal server error"}), 500

@onboarding_analytics_api.route('/api/onboarding/analytics/satisfaction', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_satisfaction_analytics():
    """
    Get detailed satisfaction and feedback analytics.

    Query parameters:
    - company_id: Required
    - feedback_type: Optional - Filter by feedback type
    - rating_threshold: Optional - Minimum rating to consider (default: 3)
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        feedback_type = request.args.get('feedback_type')
        rating_threshold = int(request.args.get('rating_threshold', 3))

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            # Build feedback query
            query = session.query(OnboardingFeedback).filter_by(status='ACTIVE')

            if feedback_type:
                query = query.filter_by(feedback_type=feedback_type)

            feedback_entries = query.all()

            # Overall satisfaction metrics
            total_feedback = len(feedback_entries)

            # Rating distributions
            rating_distribution = {
                "overall_rating": {},
                "process_rating": {},
                "communication_rating": {},
                "support_rating": {},
                "training_rating": {}
            }

            for rating_type in rating_distribution.keys():
                for i in range(1, 6):  # 1-5 scale
                    rating_distribution[rating_type][str(i)] = len([
                        f for f in feedback_entries
                        if getattr(f, rating_type) == i
                    ])

            # Satisfaction levels
            high_satisfaction = len([
                f for f in feedback_entries
                if f.overall_rating and f.overall_rating >= 4
            ])

            low_satisfaction = len([
                f for f in feedback_entries
                if f.overall_rating and f.overall_rating <= 2
            ])

            # Recommendation analysis
            recommendations = [f.would_recommend for f in feedback_entries if f.would_recommend is not None]
            recommendation_rate = sum(recommendations) / len(recommendations) * 100 if recommendations else 0

            # Sentiment analysis (if available)
            sentiment_distribution = {}
            for sentiment in ['POSITIVE', 'NEGATIVE', 'NEUTRAL']:
                sentiment_distribution[sentiment] = len([
                    f for f in feedback_entries
                    if f.sentiment_label == sentiment
                ])

            # Common themes in feedback
            improvement_suggestions = [
                f.what_could_improve for f in feedback_entries
                if f.what_could_improve
            ]

            positive_feedback = [
                f.what_went_well for f in feedback_entries
                if f.what_went_well
            ]

            # Feedback by stage
            stage_analysis = {}
            for stage in ['PRE_BOARDING', 'FIRST_DAY', 'FIRST_WEEK', 'FIRST_MONTH', 'COMPLETION']:
                stage_feedback = [f for f in feedback_entries if f.feedback_stage == stage]
                if stage_feedback:
                    avg_rating = sum([f.overall_rating for f in stage_feedback if f.overall_rating]) / len([f for f in stage_feedback if f.overall_rating])
                    stage_analysis[stage] = {
                        "feedback_count": len(stage_feedback),
                        "average_rating": round(avg_rating, 2) if stage_feedback else 0
                    }

            return jsonify({
                "success": True,
                "data": {
                    "summary": {
                        "total_feedback_entries": total_feedback,
                        "high_satisfaction_count": high_satisfaction,
                        "low_satisfaction_count": low_satisfaction,
                        "recommendation_rate": round(recommendation_rate, 2),
                        "average_overall_rating": round(sum([f.overall_rating for f in feedback_entries if f.overall_rating]) / len([f for f in feedback_entries if f.overall_rating]), 2) if feedback_entries else 0
                    },
                    "rating_distribution": rating_distribution,
                    "sentiment_distribution": sentiment_distribution,
                    "stage_analysis": stage_analysis,
                    "improvement_themes": {
                        "total_suggestions": len(improvement_suggestions),
                        "sample_suggestions": improvement_suggestions[:10]  # First 10 suggestions
                    },
                    "positive_themes": {
                        "total_positive_feedback": len(positive_feedback),
                        "sample_positive": positive_feedback[:10]  # First 10 positive comments
                    }
                }
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting satisfaction analytics: {e}")
        return jsonify({"error": "Internal server error"}), 500
