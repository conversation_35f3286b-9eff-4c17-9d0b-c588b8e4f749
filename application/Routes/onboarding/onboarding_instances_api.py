from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from application.Models.onboarding.onboarding_instance import OnboardingInstance
from application.Models.onboarding.onboarding_workflow import OnboardingWorkflow
from application.Models.onboarding.onboarding_workflow_task import OnboardingWorkflowTask
from application.Models.onboarding.onboarding_document_requirement import OnboardingDocumentRequirement
from application.Models.onboarding.onboarding_task import OnboardingTask
from application.Models.onboarding.onboarding_document import OnboardingDocument
from application.Models.onboarding.onboarding_feedback import OnboardingFeedback
from application.Models.company import Company
from application.utils.db_connection import DatabaseConnection
from datetime import datetime, date, timedelta
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.Helpers.helper_methods import HelperMethods

onboarding_instances_api = Blueprint('onboarding_instances_api', __name__)

@onboarding_instances_api.route('/api/onboarding/instances', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def start_onboarding():
    """Start onboarding process for an employee."""
    try:
        data = request.get_json()
        company_id = data.get('company_id')
        employee_id = data.get('employee_id')
        workflow_id = data.get('workflow_id')
        
        if not all([company_id, employee_id]):
            return jsonify({"error": "company_id and employee_id are required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            # Check if employee already has active onboarding
            existing_instance = OnboardingInstance.get_instance_by_employee(session, employee_id, active_only=True)
            if existing_instance:
                return jsonify({"error": "Employee already has an active onboarding process"}), 409

            # Get workflow (use default if not specified)
            if workflow_id:
                workflow = OnboardingWorkflow.get_workflow_by_id(session, workflow_id)
            else:
                workflow = OnboardingWorkflow.get_default_workflow(session, company_id)
            
            if not workflow:
                return jsonify({"error": "No workflow found"}), 404

            # Create onboarding instance
            instance_data = {
                'employee_id': employee_id,
                'workflow_id': workflow.workflow_id,
                'start_date': datetime.strptime(data.get('start_date'), '%Y-%m-%d').date() if data.get('start_date') else date.today(),
                'first_day_date': datetime.strptime(data.get('first_day_date'), '%Y-%m-%d').date() if data.get('first_day_date') else None,
                'assigned_hr': data.get('assigned_hr') or g.user.get('user_id'),
                'assigned_manager': data.get('assigned_manager'),
                'assigned_buddy': data.get('assigned_buddy'),
                'created_by': g.user.get('user_id')
            }

            instance, error = OnboardingInstance.create_instance(session, **instance_data)
            if error:
                return jsonify({"error": error}), 400

            # Create tasks from workflow template
            workflow_tasks = OnboardingWorkflowTask.get_tasks_by_workflow(session, workflow.workflow_id)
            for task_template in workflow_tasks:
                task_data = {
                    'instance_id': instance.instance_id,
                    'task_template_id': task_template.task_template_id,
                    'task_name': task_template.task_name,
                    'description': task_template.description,
                    'instructions': task_template.instructions,
                    'completion_criteria': task_template.completion_criteria,
                    'assigned_to_role': task_template.assigned_to_role,
                    'due_date': instance.start_date + timedelta(days=task_template.due_days_from_start),
                    'estimated_duration_hours': task_template.estimated_duration_hours,
                    'is_mandatory': task_template.is_mandatory,
                    'is_blocking': task_template.is_blocking,
                    'task_type': task_template.task_type,
                    'task_category': task_template.task_category,
                    'task_order': task_template.task_order,
                    'verification_required': task_template.task_type in ['DOCUMENT', 'COMPLIANCE'],
                    'assigned_by': g.user.get('user_id')
                }
                
                OnboardingTask.create_task(session, **task_data)

            # Create document placeholders from requirements
            doc_requirements = OnboardingDocumentRequirement.get_requirements_by_workflow(session, workflow.workflow_id)
            for requirement in doc_requirements:
                # Create placeholder document record
                doc_data = {
                    'instance_id': instance.instance_id,
                    'requirement_id': requirement.requirement_id,
                    'original_filename': f"pending_{requirement.document_code}",
                    'stored_filename': f"pending_{requirement.document_code}",
                    'file_path': '',
                    'verification_status': 'PENDING',
                    'uploaded_by': employee_id
                }
                
                OnboardingDocument.create_document(session, **doc_data)

            # Update instance counts
            instance.update_progress_counts(session)

            # Send welcome email if configured
            if workflow.send_welcome_email:
                instance.send_welcome_email(session)

            session.commit()

            return jsonify({
                "success": True,
                "instance": instance.to_dict(),
                "message": "Onboarding process started successfully"
            }), 201

    except Exception as e:
        app.logger.error(f"Error starting onboarding: {e}")
        return jsonify({"error": "Internal server error"}), 500

@onboarding_instances_api.route('/api/onboarding/instances/<instance_id>', methods=['GET'])
@token_required
def get_onboarding_instance(instance_id):
    """Get detailed onboarding instance information."""
    try:
        company_id = request.args.get('company_id')
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            instance = OnboardingInstance.get_instance_by_id(session, instance_id)
            if not instance:
                return jsonify({"error": "Onboarding instance not found"}), 404

            # Get related data
            tasks = OnboardingTask.get_tasks_by_instance(session, instance_id)
            documents = OnboardingDocument.get_documents_by_instance(session, instance_id)
            feedback = OnboardingFeedback.get_feedback_by_instance(session, instance_id)

            instance_dict = instance.to_dict()
            instance_dict['tasks'] = [task.to_dict() for task in tasks]
            instance_dict['documents'] = [doc.to_dict() for doc in documents]
            instance_dict['feedback'] = [fb.to_dict() for fb in feedback]
            instance_dict['next_tasks'] = [task.to_dict() for task in instance.get_next_tasks(session)]
            instance_dict['overdue_tasks'] = [task.to_dict() for task in instance.get_overdue_tasks(session)]

            return jsonify({
                "success": True,
                "instance": instance_dict
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting onboarding instance: {e}")
        return jsonify({"error": "Internal server error"}), 500

@onboarding_instances_api.route('/api/onboarding/instances', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_onboarding_instances():
    """Get onboarding instances with filtering options."""
    try:
        company_id = request.args.get('company_id')
        status = request.args.get('status')
        employee_id = request.args.get('employee_id')
        limit = request.args.get('limit', type=int)
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            if employee_id:
                instance = OnboardingInstance.get_instance_by_employee(session, employee_id, active_only=False)
                instances = [instance] if instance else []
            elif status:
                instances = OnboardingInstance.get_instances_by_status(session, company_id, status, limit)
            else:
                # Get all instances for company (would need to join with employee table)
                query = session.query(OnboardingInstance)
                if limit:
                    query = query.limit(limit)
                instances = query.order_by(OnboardingInstance.created_at.desc()).all()

            instances_data = []
            for instance in instances:
                instance_dict = instance.to_dict()
                # Add summary counts
                instance_dict['pending_tasks_count'] = instance.tasks.filter_by(status='PENDING').count()
                instance_dict['pending_documents_count'] = instance.documents.filter_by(verification_status='PENDING').count()
                instances_data.append(instance_dict)

            return jsonify({
                "success": True,
                "instances": instances_data
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting onboarding instances: {e}")
        return jsonify({"error": "Internal server error"}), 500

@onboarding_instances_api.route('/api/onboarding/instances/<instance_id>/status', methods=['PUT'])
@token_required
@roles_required('admin', 'hr')
def update_instance_status(instance_id):
    """Update onboarding instance status."""
    try:
        data = request.get_json()
        company_id = data.get('company_id')
        new_status = data.get('status')
        
        if not all([company_id, new_status]):
            return jsonify({"error": "company_id and status are required"}), 400

        valid_statuses = ['NOT_STARTED', 'IN_PROGRESS', 'COMPLETED', 'PAUSED', 'CANCELLED']
        if new_status not in valid_statuses:
            return jsonify({"error": f"Invalid status. Must be one of: {', '.join(valid_statuses)}"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            instance = OnboardingInstance.get_instance_by_id(session, instance_id)
            if not instance:
                return jsonify({"error": "Onboarding instance not found"}), 404

            success, error = instance.update_status(session, new_status, g.user.get('user_id'))
            if not success:
                return jsonify({"error": error}), 400

            return jsonify({
                "success": True,
                "instance": instance.to_dict(),
                "message": f"Status updated to {new_status}"
            }), 200

    except Exception as e:
        app.logger.error(f"Error updating instance status: {e}")
        return jsonify({"error": "Internal server error"}), 500

@onboarding_instances_api.route('/api/onboarding/instances/<instance_id>/feedback', methods=['POST'])
@token_required
def add_feedback(instance_id):
    """Add feedback to an onboarding instance."""
    try:
        data = request.get_json()
        company_id = data.get('company_id')
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            instance = OnboardingInstance.get_instance_by_id(session, instance_id)
            if not instance:
                return jsonify({"error": "Onboarding instance not found"}), 404

            feedback_data = {
                'instance_id': instance_id,
                'feedback_type': data.get('feedback_type', 'EMPLOYEE'),
                'feedback_stage': data.get('feedback_stage'),
                'feedback_category': data.get('feedback_category'),
                'feedback_text': data.get('feedback_text'),
                'feedback_title': data.get('feedback_title'),
                'overall_rating': data.get('overall_rating'),
                'process_rating': data.get('process_rating'),
                'communication_rating': data.get('communication_rating'),
                'support_rating': data.get('support_rating'),
                'training_rating': data.get('training_rating'),
                'what_went_well': data.get('what_went_well'),
                'what_could_improve': data.get('what_could_improve'),
                'suggestions': data.get('suggestions'),
                'would_recommend': data.get('would_recommend'),
                'structured_responses': data.get('structured_responses', {}),
                'provided_by': g.user.get('user_id'),
                'provider_role': data.get('provider_role'),
                'is_anonymous': data.get('is_anonymous', False),
                'onboarding_day': (date.today() - instance.start_date).days if instance.start_date else 0,
                'requires_follow_up': data.get('requires_follow_up', False)
            }

            feedback, error = OnboardingFeedback.create_feedback(session, **feedback_data)
            if error:
                return jsonify({"error": error}), 400

            session.commit()

            return jsonify({
                "success": True,
                "feedback": feedback.to_dict(),
                "message": "Feedback added successfully"
            }), 201

    except Exception as e:
        app.logger.error(f"Error adding feedback: {e}")
        return jsonify({"error": "Internal server error"}), 500
