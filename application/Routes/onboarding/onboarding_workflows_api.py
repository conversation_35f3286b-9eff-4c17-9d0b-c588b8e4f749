from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from application.Models.onboarding.onboarding_workflow import OnboardingWorkflow
from application.Models.onboarding.onboarding_workflow_task import OnboardingWorkflowTask
from application.Models.onboarding.onboarding_document_requirement import OnboardingDocumentRequirement
from application.Models.company import Company
from application.utils.db_connection import DatabaseConnection
from datetime import datetime, date
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.Helpers.helper_methods import HelperMethods
import json

onboarding_workflows_api = Blueprint('onboarding_workflows_api', __name__)

@onboarding_workflows_api.route('/api/onboarding/workflows', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_workflows():
    """Get all onboarding workflows for a company."""
    try:
        company_id = request.args.get('company_id')
        include_inactive = request.args.get('include_inactive', 'false').lower() == 'true'
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            workflows = OnboardingWorkflow.get_workflows_by_company(
                session, company_id, include_inactive
            )
            
            workflows_data = []
            for workflow in workflows:
                workflow_dict = workflow.to_dict()
                workflow_dict['completion_stats'] = workflow.get_completion_stats(session)
                workflows_data.append(workflow_dict)

            return jsonify({
                "success": True,
                "workflows": workflows_data
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting workflows: {e}")
        return jsonify({"error": "Internal server error"}), 500

@onboarding_workflows_api.route('/api/onboarding/workflows', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def create_workflow():
    """Create a new onboarding workflow."""
    try:
        data = request.get_json()
        company_id = data.get('company_id')
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            # Create workflow
            workflow_data = {
                'company_id': company_id,
                'name': data.get('name'),
                'description': data.get('description'),
                'workflow_type': data.get('workflow_type', 'STANDARD'),
                'department': data.get('department'),
                'position_level': data.get('position_level'),
                'duration_days': data.get('duration_days', 30),
                'auto_start': data.get('auto_start', True),
                'requires_manager_approval': data.get('requires_manager_approval', False),
                'requires_hr_approval': data.get('requires_hr_approval', True),
                'send_welcome_email': data.get('send_welcome_email', True),
                'create_system_accounts': data.get('create_system_accounts', True),
                'assign_buddy': data.get('assign_buddy', False),
                'schedule_orientation': data.get('schedule_orientation', True),
                'is_default': data.get('is_default', False),
                'created_by': g.user.get('user_id')
            }

            workflow, error = OnboardingWorkflow.create_workflow(session, **workflow_data)
            if error:
                return jsonify({"error": error}), 400

            # If this is set as default, unset other defaults
            if workflow.is_default:
                session.query(OnboardingWorkflow).filter(
                    OnboardingWorkflow.company_id == company_id,
                    OnboardingWorkflow.workflow_id != workflow.workflow_id
                ).update({'is_default': False})

            session.commit()

            return jsonify({
                "success": True,
                "workflow": workflow.to_dict()
            }), 201

    except Exception as e:
        app.logger.error(f"Error creating workflow: {e}")
        return jsonify({"error": "Internal server error"}), 500

@onboarding_workflows_api.route('/api/onboarding/workflows/<workflow_id>', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_workflow(workflow_id):
    """Get a specific workflow with tasks and document requirements."""
    try:
        company_id = request.args.get('company_id')
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            workflow = OnboardingWorkflow.get_workflow_by_id(session, workflow_id)
            if not workflow:
                return jsonify({"error": "Workflow not found"}), 404

            # Get tasks and document requirements
            tasks = OnboardingWorkflowTask.get_tasks_by_workflow(session, workflow_id)
            doc_requirements = OnboardingDocumentRequirement.get_requirements_by_workflow(session, workflow_id)

            workflow_dict = workflow.to_dict()
            workflow_dict['tasks'] = [task.to_dict() for task in tasks]
            workflow_dict['document_requirements'] = [req.to_dict() for req in doc_requirements]
            workflow_dict['completion_stats'] = workflow.get_completion_stats(session)

            return jsonify({
                "success": True,
                "workflow": workflow_dict
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting workflow: {e}")
        return jsonify({"error": "Internal server error"}), 500

@onboarding_workflows_api.route('/api/onboarding/workflows/<workflow_id>', methods=['PUT'])
@token_required
@roles_required('admin', 'hr')
def update_workflow(workflow_id):
    """Update an existing workflow."""
    try:
        data = request.get_json()
        company_id = data.get('company_id')
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            workflow = OnboardingWorkflow.get_workflow_by_id(session, workflow_id)
            if not workflow:
                return jsonify({"error": "Workflow not found"}), 404

            # Update workflow
            update_data = {
                'name': data.get('name'),
                'description': data.get('description'),
                'workflow_type': data.get('workflow_type'),
                'department': data.get('department'),
                'position_level': data.get('position_level'),
                'duration_days': data.get('duration_days'),
                'auto_start': data.get('auto_start'),
                'requires_manager_approval': data.get('requires_manager_approval'),
                'requires_hr_approval': data.get('requires_hr_approval'),
                'send_welcome_email': data.get('send_welcome_email'),
                'create_system_accounts': data.get('create_system_accounts'),
                'assign_buddy': data.get('assign_buddy'),
                'schedule_orientation': data.get('schedule_orientation'),
                'is_default': data.get('is_default')
            }

            # Remove None values
            update_data = {k: v for k, v in update_data.items() if v is not None}

            success, error = workflow.update_workflow(session, **update_data)
            if not success:
                return jsonify({"error": error}), 400

            # If this is set as default, unset other defaults
            if update_data.get('is_default'):
                session.query(OnboardingWorkflow).filter(
                    OnboardingWorkflow.company_id == company_id,
                    OnboardingWorkflow.workflow_id != workflow.workflow_id
                ).update({'is_default': False})
                session.commit()

            return jsonify({
                "success": True,
                "workflow": workflow.to_dict()
            }), 200

    except Exception as e:
        app.logger.error(f"Error updating workflow: {e}")
        return jsonify({"error": "Internal server error"}), 500

@onboarding_workflows_api.route('/api/onboarding/workflows/<workflow_id>', methods=['DELETE'])
@token_required
@roles_required('admin', 'hr')
def delete_workflow(workflow_id):
    """Deactivate a workflow (soft delete)."""
    try:
        company_id = request.args.get('company_id')
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            workflow = OnboardingWorkflow.get_workflow_by_id(session, workflow_id)
            if not workflow:
                return jsonify({"error": "Workflow not found"}), 404

            # Check if workflow has active instances
            active_instances = workflow.instances.filter_by(status='IN_PROGRESS').count()
            if active_instances > 0:
                return jsonify({
                    "error": f"Cannot delete workflow with {active_instances} active onboarding instances"
                }), 400

            success, error = workflow.deactivate_workflow(session)
            if not success:
                return jsonify({"error": error}), 400

            return jsonify({
                "success": True,
                "message": "Workflow deactivated successfully"
            }), 200

    except Exception as e:
        app.logger.error(f"Error deleting workflow: {e}")
        return jsonify({"error": "Internal server error"}), 500

@onboarding_workflows_api.route('/api/onboarding/workflows/<workflow_id>/clone', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def clone_workflow(workflow_id):
    """Clone an existing workflow."""
    try:
        data = request.get_json()
        company_id = data.get('company_id')
        new_name = data.get('new_name')
        
        if not company_id or not new_name:
            return jsonify({"error": "company_id and new_name are required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            workflow = OnboardingWorkflow.get_workflow_by_id(session, workflow_id)
            if not workflow:
                return jsonify({"error": "Workflow not found"}), 404

            new_workflow, error = workflow.clone_workflow(
                session, new_name, g.user.get('user_id')
            )
            if error:
                return jsonify({"error": error}), 400

            return jsonify({
                "success": True,
                "workflow": new_workflow.to_dict(),
                "message": f"Workflow cloned successfully as '{new_name}'"
            }), 201

    except Exception as e:
        app.logger.error(f"Error cloning workflow: {e}")
        return jsonify({"error": "Internal server error"}), 500

@onboarding_workflows_api.route('/api/onboarding/workflows/<workflow_id>/tasks', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_workflow_tasks(workflow_id):
    """Get all tasks for a workflow."""
    try:
        company_id = request.args.get('company_id')
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            tasks = OnboardingWorkflowTask.get_tasks_by_workflow(session, workflow_id)
            
            tasks_data = []
            for task in tasks:
                task_dict = task.to_dict()
                task_dict['completion_stats'] = task.get_completion_stats(session)
                tasks_data.append(task_dict)

            return jsonify({
                "success": True,
                "tasks": tasks_data
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting workflow tasks: {e}")
        return jsonify({"error": "Internal server error"}), 500

@onboarding_workflows_api.route('/api/onboarding/workflows/<workflow_id>/tasks', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def create_workflow_task(workflow_id):
    """Create a new task for a workflow."""
    try:
        data = request.get_json()
        company_id = data.get('company_id')
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            # Verify workflow exists
            workflow = OnboardingWorkflow.get_workflow_by_id(session, workflow_id)
            if not workflow:
                return jsonify({"error": "Workflow not found"}), 404

            # Create task
            task_data = {
                'workflow_id': workflow_id,
                'task_name': data.get('task_name'),
                'description': data.get('description'),
                'instructions': data.get('instructions'),
                'completion_criteria': data.get('completion_criteria'),
                'assigned_to_role': data.get('assigned_to_role'),
                'due_days_from_start': data.get('due_days_from_start', 1),
                'estimated_duration_hours': data.get('estimated_duration_hours', 1),
                'is_mandatory': data.get('is_mandatory', True),
                'is_blocking': data.get('is_blocking', False),
                'task_type': data.get('task_type', 'GENERAL'),
                'task_category': data.get('task_category'),
                'task_order': data.get('task_order', 1),
                'depends_on_tasks': data.get('depends_on_tasks', []),
                'auto_assign': data.get('auto_assign', True),
                'send_reminder': data.get('send_reminder', True),
                'reminder_days_before': data.get('reminder_days_before', 1),
                'external_system': data.get('external_system'),
                'external_config': data.get('external_config', {})
            }

            task, error = OnboardingWorkflowTask.create_task(session, **task_data)
            if error:
                return jsonify({"error": error}), 400

            session.commit()

            return jsonify({
                "success": True,
                "task": task.to_dict()
            }), 201

    except Exception as e:
        app.logger.error(f"Error creating workflow task: {e}")
        return jsonify({"error": "Internal server error"}), 500

@onboarding_workflows_api.route('/api/onboarding/workflows/<workflow_id>/document-requirements', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_workflow_document_requirements(workflow_id):
    """Get all document requirements for a workflow."""
    try:
        company_id = request.args.get('company_id')

        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            requirements = OnboardingDocumentRequirement.get_requirements_by_workflow(session, workflow_id)

            requirements_data = []
            for req in requirements:
                req_dict = req.to_dict()
                req_dict['submission_stats'] = req.get_submission_stats(session)
                requirements_data.append(req_dict)

            return jsonify({
                "success": True,
                "document_requirements": requirements_data
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting document requirements: {e}")
        return jsonify({"error": "Internal server error"}), 500

@onboarding_workflows_api.route('/api/onboarding/workflows/<workflow_id>/document-requirements', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def create_document_requirement(workflow_id):
    """Create a new document requirement for a workflow."""
    try:
        data = request.get_json()
        company_id = data.get('company_id')

        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            # Verify workflow exists
            workflow = OnboardingWorkflow.get_workflow_by_id(session, workflow_id)
            if not workflow:
                return jsonify({"error": "Workflow not found"}), 404

            # Create document requirement
            req_data = {
                'workflow_id': workflow_id,
                'document_name': data.get('document_name'),
                'document_code': data.get('document_code'),
                'description': data.get('description'),
                'category': data.get('category'),
                'is_mandatory': data.get('is_mandatory', True),
                'is_sensitive': data.get('is_sensitive', False),
                'requires_verification': data.get('requires_verification', True),
                'requires_original': data.get('requires_original', False),
                'file_types_allowed': data.get('file_types_allowed', 'pdf,jpg,jpeg,png'),
                'max_file_size_mb': data.get('max_file_size_mb', 5),
                'min_file_size_kb': data.get('min_file_size_kb', 10),
                'max_files_count': data.get('max_files_count', 1),
                'validation_rules': data.get('validation_rules', {}),
                'expiry_check': data.get('expiry_check', False),
                'min_validity_days': data.get('min_validity_days'),
                'display_order': data.get('display_order', 1),
                'help_text': data.get('help_text'),
                'example_url': data.get('example_url'),
                'due_days_from_start': data.get('due_days_from_start', 3),
                'blocks_other_tasks': data.get('blocks_other_tasks', False),
                'auto_remind': data.get('auto_remind', True),
                'reminder_frequency_days': data.get('reminder_frequency_days', 1)
            }

            requirement, error = OnboardingDocumentRequirement.create_requirement(session, **req_data)
            if error:
                return jsonify({"error": error}), 400

            session.commit()

            return jsonify({
                "success": True,
                "document_requirement": requirement.to_dict()
            }), 201

    except Exception as e:
        app.logger.error(f"Error creating document requirement: {e}")
        return jsonify({"error": "Internal server error"}), 500

@onboarding_workflows_api.route('/api/onboarding/workflows/default', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_default_workflow():
    """Get the default workflow for a company."""
    try:
        company_id = request.args.get('company_id')
        department = request.args.get('department')
        position_level = request.args.get('position_level')

        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            workflow = OnboardingWorkflow.get_default_workflow(
                session, company_id, department, position_level
            )

            if not workflow:
                return jsonify({"error": "No default workflow found"}), 404

            # Get tasks and document requirements
            tasks = OnboardingWorkflowTask.get_tasks_by_workflow(session, workflow.workflow_id)
            doc_requirements = OnboardingDocumentRequirement.get_requirements_by_workflow(session, workflow.workflow_id)

            workflow_dict = workflow.to_dict()
            workflow_dict['tasks'] = [task.to_dict() for task in tasks]
            workflow_dict['document_requirements'] = [req.to_dict() for req in doc_requirements]

            return jsonify({
                "success": True,
                "workflow": workflow_dict
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting default workflow: {e}")
        return jsonify({"error": "Internal server error"}), 500
