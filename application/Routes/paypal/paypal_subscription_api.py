from flask import Blueprint, request, jsonify, current_app
from application.services.paypal.paypal_service import PayPalService
from application.services.billing.subscription_billing_service import SubscriptionBillingService
from application.Models.company_subscription import CompanySubscription
from application.Models.paypal_billing_agreement import PayPalBillingAgreement
from application.Models.subscription_plan import SubscriptionPlan
from application.config.paypal_config import PayPalConfig
import json

paypal_subscription_api = Blueprint('paypal_subscription_api', __name__, url_prefix='/api/paypal')

@paypal_subscription_api.route('/config', methods=['GET'])
def get_paypal_config():
    """Get PayPal configuration for frontend SDK."""
    try:
        config = PayPalConfig.get_sdk_config()
        
        return jsonify({
            "message": "PayPal configuration retrieved successfully",
            "config": config
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting PayPal config: {e}")
        return jsonify({
            "message": "Error retrieving PayPal configuration",
            "error": str(e)
        }), 500

@paypal_subscription_api.route('/setup-billing', methods=['POST'])
def setup_billing_agreement():
    """Set up PayPal billing agreement for a subscription."""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['company_id', 'plan_id']
        missing_fields = [field for field in required_fields if not data.get(field)]
        if missing_fields:
            return jsonify({
                "message": f"Missing required fields: {', '.join(missing_fields)}"
            }), 400
        
        company_id = data['company_id']
        plan_id = data['plan_id']
        return_url = data.get('return_url')
        cancel_url = data.get('cancel_url')
        
        # Get or create subscription
        subscription = CompanySubscription.get_subscription_by_company(company_id)
        if not subscription:
            # Create new subscription
            subscription, error = CompanySubscription.create_subscription(company_id, plan_id)
            if error:
                return jsonify({
                    "message": "Error creating subscription",
                    "error": error
                }), 400
        
        # Check if PayPal billing is already set up
        if subscription.has_paypal_billing():
            return jsonify({
                "message": "PayPal billing is already set up for this subscription",
                "subscription_id": str(subscription.subscription_id)
            }), 400
        
        # Set up PayPal billing
        billing_service = SubscriptionBillingService()
        result, error = billing_service.setup_paypal_billing_for_subscription(
            subscription, return_url, cancel_url
        )
        
        if error:
            return jsonify({
                "message": "Error setting up PayPal billing",
                "error": error
            }), 400
        
        return jsonify({
            "message": "PayPal billing agreement created successfully",
            "subscription_id": str(subscription.subscription_id),
            "agreement_id": result['agreement_id'],
            "approval_url": result['approval_url']
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error setting up billing agreement: {e}")
        return jsonify({
            "message": "Error setting up PayPal billing",
            "error": str(e)
        }), 500

@paypal_subscription_api.route('/execute-agreement', methods=['POST'])
def execute_billing_agreement():
    """Execute (activate) PayPal billing agreement after user approval."""
    try:
        data = request.get_json()
        
        # Validate required fields
        if not data.get('token'):
            return jsonify({
                "message": "Missing agreement token"
            }), 400
        
        token = data['token']
        company_id = data.get('company_id')
        
        # Execute the agreement with PayPal
        paypal_service = PayPalService()
        agreement_response, error = paypal_service.execute_billing_agreement(token)
        
        if error:
            return jsonify({
                "message": "Error executing PayPal agreement",
                "error": error
            }), 400
        
        # Extract agreement details
        agreement_id = agreement_response.get('id')
        payer_info = agreement_response.get('payer', {})
        
        # Determine payment method type
        payment_method_type = 'PAYPAL_ACCOUNT'
        payment_method_details = {
            'paypal_email': payer_info.get('payer_info', {}).get('email'),
            'payer_id': payer_info.get('payer_info', {}).get('payer_id')
        }
        
        # Check if credit card was used
        if 'funding_instruments' in payer_info:
            funding = payer_info['funding_instruments'][0]
            if 'credit_card' in funding:
                payment_method_type = 'CREDIT_CARD'
                card_info = funding['credit_card']
                payment_method_details = {
                    'card_type': card_info.get('type'),
                    'last_four': card_info.get('number', '')[-4:],
                    'expiry_month': card_info.get('expire_month'),
                    'expiry_year': card_info.get('expire_year')
                }
        
        # Create billing agreement record
        billing_agreement, ba_error = PayPalBillingAgreement.create_agreement(
            company_id=company_id,
            paypal_agreement_id=agreement_id,
            payment_method_type=payment_method_type,
            payment_method_details=payment_method_details,
            payer_info=payer_info,
            agreement_details=agreement_response
        )
        
        if ba_error:
            return jsonify({
                "message": "Error saving billing agreement",
                "error": ba_error
            }), 400
        
        # Update subscription with PayPal details
        subscription = CompanySubscription.get_subscription_by_company(company_id)
        if subscription:
            subscription.setup_paypal_billing(agreement_id)
        
        return jsonify({
            "message": "PayPal billing agreement activated successfully",
            "agreement_id": agreement_id,
            "payment_method_type": payment_method_type,
            "subscription_id": str(subscription.subscription_id) if subscription else None
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error executing billing agreement: {e}")
        return jsonify({
            "message": "Error activating PayPal billing",
            "error": str(e)
        }), 500

@paypal_subscription_api.route('/cancel-billing', methods=['POST'])
def cancel_billing_agreement():
    """Cancel PayPal billing agreement."""
    try:
        data = request.get_json()
        
        # Validate required fields
        if not data.get('company_id'):
            return jsonify({
                "message": "Missing company_id"
            }), 400
        
        company_id = data['company_id']
        reason = data.get('reason', 'Cancelled by user')
        
        # Get subscription
        subscription = CompanySubscription.get_subscription_by_company(company_id)
        if not subscription or not subscription.paypal_agreement_id:
            return jsonify({
                "message": "No PayPal billing agreement found"
            }), 404
        
        # Cancel with PayPal
        paypal_service = PayPalService()
        _, error = paypal_service.cancel_billing_agreement(subscription.paypal_agreement_id, reason)
        
        if error:
            current_app.logger.warning(f"Error cancelling with PayPal: {error}")
            # Continue with local cancellation even if PayPal fails
        
        # Update local records
        subscription.disable_paypal_billing(reason)
        
        # Update billing agreement record
        billing_agreement = PayPalBillingAgreement.get_by_company_id(company_id)
        if billing_agreement:
            billing_agreement.cancel_agreement(reason)
        
        return jsonify({
            "message": "PayPal billing agreement cancelled successfully"
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error cancelling billing agreement: {e}")
        return jsonify({
            "message": "Error cancelling PayPal billing",
            "error": str(e)
        }), 500

@paypal_subscription_api.route('/billing-status/<company_id>', methods=['GET'])
def get_billing_status(company_id):
    """Get PayPal billing status for a company."""
    try:
        # Get subscription
        subscription = CompanySubscription.get_subscription_by_company(company_id)
        if not subscription:
            return jsonify({
                "message": "No subscription found",
                "has_billing": False
            }), 404
        
        # Get billing agreement
        billing_agreement = PayPalBillingAgreement.get_by_company_id(company_id)
        
        response_data = {
            "has_billing": subscription.has_paypal_billing(),
            "auto_billing_enabled": subscription.auto_billing_enabled,
            "payment_method": subscription.payment_method,
            "next_billing_date": subscription.next_billing_date.strftime('%Y-%m-%d') if subscription.next_billing_date else None,
            "employee_count": subscription.employee_count,
            "amount_due": float(subscription.amount_due),
            "billing_retry_count": subscription.billing_retry_count,
            "last_billing_attempt": subscription.last_billing_attempt.strftime('%Y-%m-%d %H:%M:%S') if subscription.last_billing_attempt else None
        }
        
        if billing_agreement:
            response_data.update({
                "payment_method_type": billing_agreement.payment_method_type,
                "payment_method_details": billing_agreement.to_dict().get('payment_method_details', {}),
                "agreement_status": billing_agreement.status
            })
        
        return jsonify({
            "message": "Billing status retrieved successfully",
            "billing_status": response_data
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting billing status: {e}")
        return jsonify({
            "message": "Error retrieving billing status",
            "error": str(e)
        }), 500

@paypal_subscription_api.route('/process-billing', methods=['POST'])
def process_manual_billing():
    """Manually trigger billing for a subscription (admin only)."""
    try:
        data = request.get_json()
        
        if not data.get('company_id'):
            return jsonify({
                "message": "Missing company_id"
            }), 400
        
        company_id = data['company_id']
        
        # Get subscription
        subscription = CompanySubscription.get_subscription_by_company(company_id)
        if not subscription:
            return jsonify({
                "message": "No subscription found"
            }), 404
        
        if not subscription.has_paypal_billing():
            return jsonify({
                "message": "No PayPal billing set up for this subscription"
            }), 400
        
        # Process billing
        billing_service = SubscriptionBillingService()
        success, error = billing_service.process_subscription_billing(subscription)
        
        if not success:
            return jsonify({
                "message": "Billing failed",
                "error": error
            }), 400
        
        return jsonify({
            "message": "Billing processed successfully",
            "amount_charged": float(subscription.amount_due),
            "employee_count": subscription.employee_count
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error processing manual billing: {e}")
        return jsonify({
            "message": "Error processing billing",
            "error": str(e)
        }), 500
