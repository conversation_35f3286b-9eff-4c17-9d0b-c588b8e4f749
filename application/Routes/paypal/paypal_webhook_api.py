from flask import Blueprint, request, jsonify, current_app
from application.services.paypal.paypal_service import PayPalService
from application.Models.paypal_billing_agreement import PayPalBillingAgreement
from application.Models.company_subscription import CompanySubscription
from application.Models.subscription_invoice import SubscriptionInvoice
from application.Models.subscription_payment import SubscriptionPayment
from application.config.paypal_config import PayPalConfig
import json
from datetime import datetime

paypal_webhook_api = Blueprint('paypal_webhook_api', __name__, url_prefix='/api/paypal')

@paypal_webhook_api.route('/webhook', methods=['POST'])
def handle_paypal_webhook():
    """Handle PayPal webhook notifications."""
    try:
        # Get webhook data
        webhook_data = request.get_json()
        headers = dict(request.headers)
        
        current_app.logger.info(f"Received PayPal webhook: {webhook_data.get('event_type', 'Unknown')}")
        
        # Verify webhook signature (if enabled)
        config = PayPalConfig.get_paypal_config()
        webhook_id = config.get('PAYPAL_WEBHOOK_ID')
        
        if webhook_id and config.get('PAYPAL_VALIDATE_WEBHOOK', True):
            paypal_service = PayPalService()
            is_valid = paypal_service.verify_webhook_signature(headers, webhook_data, webhook_id)
            
            if not is_valid:
                current_app.logger.warning("Invalid PayPal webhook signature")
                return jsonify({"message": "Invalid signature"}), 400
        
        # Process webhook based on event type
        event_type = webhook_data.get('event_type')
        resource = webhook_data.get('resource', {})
        
        if event_type == 'BILLING.SUBSCRIPTION.ACTIVATED':
            return handle_subscription_activated(resource)
        elif event_type == 'BILLING.SUBSCRIPTION.CANCELLED':
            return handle_subscription_cancelled(resource)
        elif event_type == 'BILLING.SUBSCRIPTION.SUSPENDED':
            return handle_subscription_suspended(resource)
        elif event_type == 'BILLING.SUBSCRIPTION.RE-ACTIVATED':
            return handle_subscription_reactivated(resource)
        elif event_type == 'PAYMENT.SALE.COMPLETED':
            return handle_payment_completed(resource)
        elif event_type == 'PAYMENT.SALE.DENIED':
            return handle_payment_denied(resource)
        elif event_type == 'BILLING.SUBSCRIPTION.PAYMENT.FAILED':
            return handle_payment_failed(resource)
        else:
            current_app.logger.info(f"Unhandled webhook event type: {event_type}")
            return jsonify({"message": "Event type not handled"}), 200
        
    except Exception as e:
        current_app.logger.error(f"Error handling PayPal webhook: {e}")
        return jsonify({"message": "Webhook processing failed"}), 500

def handle_subscription_activated(resource):
    """Handle subscription activation webhook."""
    try:
        agreement_id = resource.get('id')
        
        # Find billing agreement
        billing_agreement = PayPalBillingAgreement.get_by_paypal_id(agreement_id)
        if not billing_agreement:
            current_app.logger.warning(f"Billing agreement not found: {agreement_id}")
            return jsonify({"message": "Agreement not found"}), 404
        
        # Update agreement status
        billing_agreement.status = 'ACTIVE'
        
        # Update subscription
        subscription = CompanySubscription.get_subscription_by_company(billing_agreement.company_id)
        if subscription:
            subscription.setup_paypal_billing(agreement_id)
            current_app.logger.info(f"Activated PayPal billing for subscription {subscription.subscription_id}")
        
        return jsonify({"message": "Subscription activated"}), 200
        
    except Exception as e:
        current_app.logger.error(f"Error handling subscription activation: {e}")
        return jsonify({"message": "Error processing activation"}), 500

def handle_subscription_cancelled(resource):
    """Handle subscription cancellation webhook."""
    try:
        agreement_id = resource.get('id')
        
        # Find billing agreement
        billing_agreement = PayPalBillingAgreement.get_by_paypal_id(agreement_id)
        if not billing_agreement:
            current_app.logger.warning(f"Billing agreement not found: {agreement_id}")
            return jsonify({"message": "Agreement not found"}), 404
        
        # Update agreement status
        billing_agreement.cancel_agreement("Cancelled via PayPal webhook")
        
        # Update subscription
        subscription = CompanySubscription.get_subscription_by_company(billing_agreement.company_id)
        if subscription:
            subscription.disable_paypal_billing("Cancelled via PayPal")
            current_app.logger.info(f"Cancelled PayPal billing for subscription {subscription.subscription_id}")
        
        return jsonify({"message": "Subscription cancelled"}), 200
        
    except Exception as e:
        current_app.logger.error(f"Error handling subscription cancellation: {e}")
        return jsonify({"message": "Error processing cancellation"}), 500

def handle_subscription_suspended(resource):
    """Handle subscription suspension webhook."""
    try:
        agreement_id = resource.get('id')
        
        # Find billing agreement
        billing_agreement = PayPalBillingAgreement.get_by_paypal_id(agreement_id)
        if not billing_agreement:
            current_app.logger.warning(f"Billing agreement not found: {agreement_id}")
            return jsonify({"message": "Agreement not found"}), 404
        
        # Update agreement status
        billing_agreement.suspend_agreement("Suspended via PayPal webhook")
        
        # Update subscription
        subscription = CompanySubscription.get_subscription_by_company(billing_agreement.company_id)
        if subscription:
            subscription.suspend_subscription("PayPal billing suspended")
            current_app.logger.info(f"Suspended subscription {subscription.subscription_id}")
        
        return jsonify({"message": "Subscription suspended"}), 200
        
    except Exception as e:
        current_app.logger.error(f"Error handling subscription suspension: {e}")
        return jsonify({"message": "Error processing suspension"}), 500

def handle_subscription_reactivated(resource):
    """Handle subscription reactivation webhook."""
    try:
        agreement_id = resource.get('id')
        
        # Find billing agreement
        billing_agreement = PayPalBillingAgreement.get_by_paypal_id(agreement_id)
        if not billing_agreement:
            current_app.logger.warning(f"Billing agreement not found: {agreement_id}")
            return jsonify({"message": "Agreement not found"}), 404
        
        # Update agreement status
        billing_agreement.reactivate_agreement()
        
        # Update subscription
        subscription = CompanySubscription.get_subscription_by_company(billing_agreement.company_id)
        if subscription:
            subscription.setup_paypal_billing(agreement_id)
            current_app.logger.info(f"Reactivated subscription {subscription.subscription_id}")
        
        return jsonify({"message": "Subscription reactivated"}), 200
        
    except Exception as e:
        current_app.logger.error(f"Error handling subscription reactivation: {e}")
        return jsonify({"message": "Error processing reactivation"}), 500

def handle_payment_completed(resource):
    """Handle successful payment webhook."""
    try:
        payment_id = resource.get('id')
        amount = resource.get('amount', {}).get('total', '0')
        currency = resource.get('amount', {}).get('currency', 'USD')
        
        # Try to find related invoice by payment reference
        invoice = None
        if 'invoice_number' in resource:
            invoice_number = resource['invoice_number']
            invoice = SubscriptionInvoice.get_invoice_by_number(invoice_number)
        
        if invoice:
            # Create payment record
            payment, error = SubscriptionPayment.create_payment(
                invoice_id=invoice.invoice_id,
                amount=float(amount),
                payment_method='PAYPAL',
                payment_reference=payment_id,
                transaction_id=payment_id,
                processor_response=json.dumps(resource),
                status='COMPLETED'
            )
            
            if error:
                current_app.logger.error(f"Error creating payment record: {error}")
            else:
                current_app.logger.info(f"Recorded payment {payment_id} for invoice {invoice.invoice_number}")
        else:
            current_app.logger.warning(f"No invoice found for payment {payment_id}")
        
        return jsonify({"message": "Payment completed"}), 200
        
    except Exception as e:
        current_app.logger.error(f"Error handling payment completion: {e}")
        return jsonify({"message": "Error processing payment"}), 500

def handle_payment_denied(resource):
    """Handle denied payment webhook."""
    try:
        payment_id = resource.get('id')
        reason = resource.get('reason_code', 'Payment denied')
        
        current_app.logger.warning(f"Payment denied: {payment_id} - {reason}")
        
        # Try to find related subscription and record failed billing attempt
        if 'billing_agreement_id' in resource:
            agreement_id = resource['billing_agreement_id']
            billing_agreement = PayPalBillingAgreement.get_by_paypal_id(agreement_id)
            
            if billing_agreement:
                subscription = CompanySubscription.get_subscription_by_company(billing_agreement.company_id)
                if subscription:
                    subscription.record_billing_attempt(success=False, error_message=reason)
                    current_app.logger.info(f"Recorded failed billing attempt for subscription {subscription.subscription_id}")
        
        return jsonify({"message": "Payment denial processed"}), 200
        
    except Exception as e:
        current_app.logger.error(f"Error handling payment denial: {e}")
        return jsonify({"message": "Error processing payment denial"}), 500

def handle_payment_failed(resource):
    """Handle failed payment webhook."""
    try:
        agreement_id = resource.get('billing_agreement_id')
        reason = resource.get('failure_reason', 'Payment failed')
        
        current_app.logger.warning(f"Payment failed for agreement {agreement_id}: {reason}")
        
        # Find billing agreement and update subscription
        billing_agreement = PayPalBillingAgreement.get_by_paypal_id(agreement_id)
        if billing_agreement:
            subscription = CompanySubscription.get_subscription_by_company(billing_agreement.company_id)
            if subscription:
                subscription.record_billing_attempt(success=False, error_message=reason)
                
                # Suspend subscription after multiple failures
                if subscription.billing_retry_count >= 3:
                    subscription.suspend_subscription(f"Multiple payment failures: {reason}")
                    current_app.logger.warning(f"Suspended subscription {subscription.subscription_id} due to payment failures")
        
        return jsonify({"message": "Payment failure processed"}), 200
        
    except Exception as e:
        current_app.logger.error(f"Error handling payment failure: {e}")
        return jsonify({"message": "Error processing payment failure"}), 500

@paypal_webhook_api.route('/webhook/test', methods=['POST'])
def test_webhook():
    """Test webhook endpoint for development."""
    try:
        if not PayPalConfig.is_sandbox():
            return jsonify({"message": "Test endpoint only available in sandbox mode"}), 403
        
        data = request.get_json()
        current_app.logger.info(f"Test webhook received: {data}")
        
        return jsonify({
            "message": "Test webhook received successfully",
            "data": data
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error in test webhook: {e}")
        return jsonify({"message": "Test webhook failed"}), 500
