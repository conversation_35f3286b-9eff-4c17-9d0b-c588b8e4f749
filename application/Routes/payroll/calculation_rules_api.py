from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from application.Models.calculation_rule import CalculationRule
from application.Models.country import Country
from application.Models.Msg import Msg
import jsons
from datetime import datetime, date
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.Helpers.helper_methods import HelperMethods

calculation_rules_api = Blueprint('calculation_rules_api', __name__)

@calculation_rules_api.route('/api/calculation-rules', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_calculation_rules():
    """Get all calculation rules for a country."""
    try:
        country_id = request.args.get('country_id')
        calculation_type = request.args.get('calculation_type')  # Optional filter
        
        if not country_id:
            return jsonify({"error": "country_id is required"}), 400

        if calculation_type:
            rules = CalculationRule.get_active_rules_for_country(country_id, calculation_type)
        else:
            rules = CalculationRule.get_by_country(country_id)

        rules_data = [rule.to_dict() for rule in rules]

        return jsonify({
            "success": True,
            "data": rules_data,
            "total": len(rules_data)
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting calculation rules: {e}")
        return jsonify({"error": "Internal server error"}), 500

@calculation_rules_api.route('/api/calculation-rules', methods=['POST'])
@token_required
@roles_required('admin')
def create_calculation_rule():
    """Create a new calculation rule."""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "Request body is required"}), 400

        # Validate required fields
        required_fields = ['country_id', 'calculation_type', 'type_code', 'calculation_base', 'effective_from']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"{field} is required"}), 400

        # Validate calculation_type
        valid_types = ['TAX', 'DEDUCTION']
        if data['calculation_type'] not in valid_types:
            return jsonify({"error": f"calculation_type must be one of: {valid_types}"}), 400

        # Validate calculation_base
        valid_bases = ['GROSS_SALARY', 'GROSS_MINUS_TRANSPORT', 'NET_BEFORE_CBHI', 'NET_SALARY', 'BASIC_SALARY']
        if data['calculation_base'] not in valid_bases:
            return jsonify({"error": f"calculation_base must be one of: {valid_bases}"}), 400

        # Convert date string to date object
        data['effective_from'] = datetime.strptime(data['effective_from'], '%Y-%m-%d').date()
        if data.get('effective_to'):
            data['effective_to'] = datetime.strptime(data['effective_to'], '%Y-%m-%d').date()

        # Create the rule
        rule = CalculationRule.create_rule(**data)

        if rule:
            return jsonify({
                "success": True,
                "data": rule.to_dict(),
                "message": "Calculation rule created successfully"
            }), 201
        else:
            return jsonify({"error": "Failed to create calculation rule"}), 500

    except ValueError as e:
        app.logger.error(f"Validation error in calculation rule creation: {e}")
        return jsonify({"error": str(e)}), 400
    except Exception as e:
        app.logger.error(f"Error creating calculation rule: {e}")
        return jsonify({"error": "Internal server error"}), 500

@calculation_rules_api.route('/api/calculation-rules/<rule_id>', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_calculation_rule(rule_id):
    """Get a specific calculation rule by ID."""
    try:
        rule = CalculationRule.query.filter_by(rule_id=rule_id, is_active=True).first()
        
        if not rule:
            return jsonify({"error": "Calculation rule not found"}), 404

        return jsonify({
            "success": True,
            "data": rule.to_dict()
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting calculation rule: {e}")
        return jsonify({"error": "Internal server error"}), 500

@calculation_rules_api.route('/api/calculation-rules/<rule_id>/new-version', methods=['POST'])
@token_required
@roles_required('admin')
def create_rule_version(rule_id):
    """Create a new version of an existing calculation rule."""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"error": "Request body is required"}), 400

        # Validate required fields
        required_fields = ['effective_from']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"{field} is required"}), 400

        # Convert date string to date object
        new_effective_from = datetime.strptime(data['effective_from'], '%Y-%m-%d').date()

        # Remove effective_from from data as it's handled separately
        update_data = {k: v for k, v in data.items() if k != 'effective_from'}

        # Create new version
        new_rule = CalculationRule.create_new_version(rule_id, new_effective_from, **update_data)

        if new_rule:
            return jsonify({
                "success": True,
                "data": new_rule.to_dict(),
                "message": "New calculation rule version created successfully"
            }), 201
        else:
            return jsonify({"error": "Failed to create new rule version"}), 500

    except ValueError as e:
        app.logger.error(f"Validation error in rule version creation: {e}")
        return jsonify({"error": str(e)}), 400
    except Exception as e:
        app.logger.error(f"Error creating rule version: {e}")
        return jsonify({"error": "Internal server error"}), 500

@calculation_rules_api.route('/api/calculation-rules/<rule_id>', methods=['DELETE'])
@token_required
@roles_required('admin')
def deactivate_calculation_rule(rule_id):
    """Deactivate a calculation rule."""
    try:
        success = CalculationRule.deactivate_rule(rule_id)
        
        if success:
            return jsonify({
                "success": True,
                "message": "Calculation rule deactivated successfully"
            }), 200
        else:
            return jsonify({"error": "Failed to deactivate calculation rule"}), 500

    except Exception as e:
        app.logger.error(f"Error deactivating calculation rule: {e}")
        return jsonify({"error": "Internal server error"}), 500

@calculation_rules_api.route('/api/calculation-rules/lookup', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def lookup_calculation_rule():
    """Lookup active calculation rule for specific criteria."""
    try:
        country_id = request.args.get('country_id')
        calculation_type = request.args.get('calculation_type')
        type_code = request.args.get('type_code')
        date_str = request.args.get('date')
        
        if not all([country_id, calculation_type, type_code]):
            return jsonify({"error": "country_id, calculation_type, and type_code are required"}), 400

        lookup_date = datetime.strptime(date_str, '%Y-%m-%d').date() if date_str else date.today()

        rule = CalculationRule.get_active_rule(country_id, calculation_type, type_code, lookup_date)

        if rule:
            return jsonify({
                "success": True,
                "data": rule.to_dict()
            }), 200
        else:
            return jsonify({
                "success": True,
                "data": None,
                "message": "No active rule found for the specified criteria"
            }), 200

    except ValueError as e:
        app.logger.error(f"Validation error in rule lookup: {e}")
        return jsonify({"error": str(e)}), 400
    except Exception as e:
        app.logger.error(f"Error looking up calculation rule: {e}")
        return jsonify({"error": "Internal server error"}), 500

@calculation_rules_api.route('/api/calculation-rules/types', methods=['GET'])
@token_required
def get_calculation_rule_types():
    """Get available calculation rule types and bases."""
    try:
        return jsonify({
            "success": True,
            "data": {
                "calculation_types": [
                    {"code": "TAX", "name": "Tax Calculation"},
                    {"code": "DEDUCTION", "name": "Deduction Calculation"}
                ],
                "calculation_bases": [
                    {"code": "GROSS_SALARY", "name": "Gross Salary", "description": "Full gross salary including all allowances"},
                    {"code": "GROSS_MINUS_TRANSPORT", "name": "Gross Minus Transport", "description": "Gross salary excluding transport allowance"},
                    {"code": "NET_BEFORE_CBHI", "name": "Net Before CBHI", "description": "Net salary after tax and other deductions, before CBHI"},
                    {"code": "NET_SALARY", "name": "Net Salary", "description": "Net salary after all deductions"},
                    {"code": "BASIC_SALARY", "name": "Basic Salary", "description": "Basic salary only, excluding allowances"}
                ]
            }
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting calculation rule types: {e}")
        return jsonify({"error": "Internal server error"}), 500
