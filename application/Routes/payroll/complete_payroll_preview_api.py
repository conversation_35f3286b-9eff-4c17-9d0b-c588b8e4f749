"""
Complete Payroll Preview API
Shows full payroll breakdown including all statutory deductions and employer contributions
"""

from flask import Blueprint, request, jsonify, current_app as app, g
from application.Models.employees.employee import Employee
from application.Models.employees.employee_salary import EmployeeSalary
from application.Models.company import Company
from application.Services.payroll_calculation_service import PayrollCalculationService
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.database import DatabaseConnection
from datetime import datetime, date
import uuid

complete_payroll_preview_api = Blueprint('complete_payroll_preview_api', __name__)

@complete_payroll_preview_api.route('/api/employees/<employee_id>/payroll/preview', methods=['POST'])
@token_required
@roles_required('admin', 'hr', 'payroll')
def preview_complete_payroll(employee_id):
    """
    Preview complete payroll calculation for an employee including all statutory deductions.
    
    This endpoint shows what the employee's payslip would look like with:
    - Basic salary and allowances
    - Income tax (PAYE)
    - Statutory deductions (Pension, CBHI, Maternity)
    - Employer contributions (Pension, Occupational Hazard, Maternity)
    - Final net pay
    """
    try:
        data = request.get_json()
        company_id = request.args.get('company_id')
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400
        
        # Validate employee_id format
        try:
            uuid.UUID(employee_id)
        except ValueError:
            return jsonify({"error": "Invalid employee_id format"}), 400
        
        # Get company and database info
        company = Company.get_company_by_id(company_id)
        if not company:
            return jsonify({"error": "Company not found"}), 404
        
        database_name = company.database_name
        if not database_name:
            return jsonify({"error": "Company database not configured"}), 400
        
        # Get calculation date (default to today)
        calculation_date = data.get('calculation_date')
        if calculation_date:
            calculation_date = datetime.strptime(calculation_date, '%Y-%m-%d').date()
        else:
            calculation_date = date.today()
        
        # Connect to the database
        with DatabaseConnection.get_session(database_name) as session:
            # Verify employee exists
            employee = session.query(Employee).filter_by(employee_id=employee_id).first()
            if not employee:
                return jsonify({"error": "Employee not found"}), 404
            
            if not employee.employee_type_id:
                return jsonify({"error": "Employee must have employee_type_id set"}), 400
            
            # Get current salary
            salary = EmployeeSalary.get_current_salary(session, employee_id, calculation_date)
            if not salary:
                return jsonify({"error": "No active salary found for employee"}), 404
            
            # Initialize payroll calculation service
            calc_service = PayrollCalculationService(company.country_id)
            
            # Calculate complete payroll
            payroll_result = calc_service.calculate_employee_payroll(
                session, employee_id, calculation_date, calculation_date
            )
            
            # Format the response for better readability
            formatted_result = {
                "employee_info": {
                    "employee_id": employee_id,
                    "employee_name": f"{employee.first_name} {employee.last_name}",
                    "employee_type": employee.employee_type.name if employee.employee_type else None,
                    "calculation_date": calculation_date.isoformat()
                },
                "salary_breakdown": {
                    "basic_salary": float(payroll_result['basic_salary']),
                    "allowances": {
                        name: {
                            "amount": float(details['amount']),
                            "is_taxable": details.get('is_taxable', True),
                            "is_pensionable": details.get('is_pensionable', True)
                        }
                        for name, details in payroll_result['allowances'].items()
                    },
                    "gross_pay": float(payroll_result['gross_pay'])
                },
                "deductions": {
                    "income_tax": {
                        "total_tax": float(payroll_result['tax_calculations']['total_tax']),
                        "brackets": payroll_result['tax_calculations'].get('brackets', [])
                    },
                    "statutory_deductions": {
                        name: {
                            "employee_amount": float(details['employee_amount']),
                            "calculation_base": float(details['calculation_base']),
                            "rate_percentage": float(details['rate'] * 100) if details.get('rate') else None
                        }
                        for name, details in payroll_result['employee_deductions'].items()
                    },
                    "total_employee_deductions": sum(
                        float(details['employee_amount']) 
                        for details in payroll_result['employee_deductions'].values()
                    )
                },
                "employer_contributions": {
                    name: {
                        "employer_amount": float(details['employer_amount']),
                        "calculation_base": float(details['calculation_base']),
                        "rate_percentage": float(details['rate'] * 100) if details.get('rate') else None
                    }
                    for name, details in payroll_result['employer_contributions'].items()
                },
                "summary": {
                    "gross_pay": float(payroll_result['gross_pay']),
                    "total_tax": float(payroll_result['tax_calculations']['total_tax']),
                    "total_statutory_deductions": sum(
                        float(details['employee_amount']) 
                        for details in payroll_result['employee_deductions'].values()
                    ),
                    "net_pay": float(payroll_result['net_pay']),
                    "total_employer_cost": float(payroll_result['gross_pay']) + sum(
                        float(details['employer_amount']) 
                        for details in payroll_result['employer_contributions'].values()
                    )
                }
            }
            
            return jsonify({
                "success": True,
                "data": formatted_result,
                "message": "Complete payroll preview calculated successfully"
            }), 200
    
    except Exception as e:
        app.logger.error(f"Error calculating complete payroll preview: {e}")
        return jsonify({"error": "Internal server error"}), 500

@complete_payroll_preview_api.route('/api/employees/<employee_id>/payroll/scenario-preview', methods=['POST'])
@token_required
@roles_required('admin', 'hr', 'payroll')
def preview_payroll_from_scenario(employee_id):
    """
    Preview complete payroll calculation from a salary scenario.
    
    This shows what the complete payroll would look like if we set up
    the employee's salary using a specific scenario.
    """
    try:
        data = request.get_json()
        company_id = request.args.get('company_id')
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400
        
        # Required fields
        required_fields = ['scenario', 'known_values']
        for field in required_fields:
            if not data.get(field):
                return jsonify({"error": f"{field} is required"}), 400
        
        scenario = data.get('scenario')
        known_values = data.get('known_values')
        effective_from = data.get('effective_from', date.today().isoformat())
        
        # Parse effective_from
        if isinstance(effective_from, str):
            effective_from = datetime.strptime(effective_from, '%Y-%m-%d').date()
        
        # Get company and database info
        company = Company.get_company_by_id(company_id)
        if not company:
            return jsonify({"error": "Company not found"}), 404
        
        database_name = company.database_name
        if not database_name:
            return jsonify({"error": "Company database not configured"}), 400
        
        # Connect to the database
        with DatabaseConnection.get_session(database_name) as session:
            # Verify employee exists
            employee = session.query(Employee).filter_by(employee_id=employee_id).first()
            if not employee:
                return jsonify({"error": "Employee not found"}), 404
            
            if not employee.employee_type_id:
                return jsonify({"error": "Employee must have employee_type_id set"}), 400
            
            # Initialize payroll calculation service
            calc_service = PayrollCalculationService(company.country_id)
            
            # Calculate payroll from scenario
            payroll_result = calc_service.calculate_from_scenario(
                session, employee_id, scenario, known_values, effective_from, effective_from
            )
            
            # Convert Decimal values to float for JSON serialization
            json_result = calc_service.decimal_to_float_for_json(payroll_result)
            
            return jsonify({
                "success": True,
                "data": json_result,
                "scenario_used": scenario,
                "message": "Payroll scenario preview calculated successfully"
            }), 200
    
    except Exception as e:
        app.logger.error(f"Error calculating payroll scenario preview: {e}")
        return jsonify({"error": "Internal server error"}), 500
