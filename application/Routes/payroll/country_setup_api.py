"""Country Setup Workflow API for Central Database Administration."""

from flask import Blueprint, request, jsonify, current_app as app, g
from application.Models.country import Country
from application.Models.payroll_policy import PayrollPolicy
from application.Models.payroll_policy_type import PayrollPolicyType
from application.Models.employee_type import EmployeeType
from application.Models.deduction_type import DeductionType
from application.Models.deduction_policy import DeductionPolicy
from application.Models.tax_bracket import TaxBracket
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.database import central_db as db
from datetime import datetime, date
from decimal import Decimal
import uuid

country_setup_api = Blueprint('country_setup_api', __name__)

# ============================================================================
# COUNTRY SETUP WORKFLOW ENDPOINTS
# ============================================================================

@country_setup_api.route('/api/countries/<country_code>/setup/status', methods=['GET'])
@token_required
@roles_required('admin', 'super-admin', 'hr')
def get_setup_status(country_code):
    """Get comprehensive setup status for a country."""
    try:
        country = Country.get_country_by_code(country_code.upper())
        if not country:
            return jsonify({"error": f"Country with code '{country_code}' not found"}), 404
        
        # Check setup completion
        setup_steps = {
            'country_created': True,  # Already exists if we're here
            'employee_types_created': False,
            'policy_types_available': False,
            'tax_policies_created': False,
            'deduction_types_created': False,
            'deduction_policies_created': False,
            'default_employee_type_set': False
        }
        
        # Check employee types
        employee_types = EmployeeType.get_by_country(country.country_id)
        setup_steps['employee_types_created'] = len(employee_types) > 0
        setup_steps['default_employee_type_set'] = any(et.is_default for et in employee_types)
        
        # Check policy types availability
        policy_types = PayrollPolicyType.get_all_active()
        setup_steps['policy_types_available'] = len(policy_types) > 0
        
        # Check tax policies
        tax_policies = PayrollPolicy.query.join(PayrollPolicyType).filter(
            PayrollPolicy.country_id == country.country_id,
            PayrollPolicyType.calculation_method == 'PROGRESSIVE_TAX'
        ).count()
        setup_steps['tax_policies_created'] = tax_policies > 0
        
        # Check deduction types
        deduction_types = DeductionType.get_by_country(country.country_id)
        setup_steps['deduction_types_created'] = len(deduction_types) > 0
        
        # Check deduction policies
        deduction_policies = DeductionPolicy.query.filter_by(country_id=country.country_id).count()
        setup_steps['deduction_policies_created'] = deduction_policies > 0
        
        # Calculate completion percentage
        completed_steps = sum(setup_steps.values())
        total_steps = len(setup_steps)
        completion_percentage = (completed_steps / total_steps) * 100
        
        # Determine setup phase
        if completion_percentage == 100:
            setup_phase = "complete"
        elif completion_percentage >= 75:
            setup_phase = "nearly_complete"
        elif completion_percentage >= 50:
            setup_phase = "in_progress"
        elif completion_percentage >= 25:
            setup_phase = "started"
        else:
            setup_phase = "not_started"
        
        # Generate next steps
        next_steps = []
        if not setup_steps['employee_types_created']:
            next_steps.append("Create employee types (Permanent, Contract, Casual)")
        if not setup_steps['tax_policies_created']:
            next_steps.append("Set up PAYE tax policies with progressive tax brackets")
        if not setup_steps['deduction_types_created']:
            next_steps.append("Define deduction types (Pension, Health Insurance, etc.)")
        if not setup_steps['deduction_policies_created']:
            next_steps.append("Create deduction policies with rates")
        if not setup_steps['default_employee_type_set'] and setup_steps['employee_types_created']:
            next_steps.append("Set a default employee type")
        
        return jsonify({
            "success": True,
            "country": {
                "country_id": str(country.country_id),
                "name": country.name,
                "code": country.code,
                "currency": country.currency
            },
            "setup_status": {
                "phase": setup_phase,
                "completion_percentage": round(completion_percentage, 1),
                "completed_steps": completed_steps,
                "total_steps": total_steps,
                "steps": setup_steps
            },
            "statistics": {
                "employee_types": len(employee_types),
                "policy_types_available": len(policy_types),
                "tax_policies": tax_policies,
                "deduction_types": len(deduction_types),
                "deduction_policies": deduction_policies
            },
            "next_steps": next_steps,
            "recommendations": [
                "Test payroll calculations after setup",
                "Configure company-specific allowances",
                "Set up payroll processing schedules",
                "Train HR staff on the system"
            ] if setup_phase == "complete" else next_steps
        }), 200
        
    except Exception as e:
        app.logger.error(f"Error getting setup status: {e}")
        return jsonify({"error": "Failed to get setup status"}), 500

@country_setup_api.route('/api/countries/<country_code>/setup/initialize', methods=['POST'])
@token_required
@roles_required('admin', 'super-admin')
def initialize_country_setup(country_code):
    """Initialize a country for payroll with basic configurations."""
    try:
        country = Country.get_country_by_code(country_code.upper())
        if not country:
            return jsonify({"error": f"Country with code '{country_code}' not found"}), 404
        
        data = request.get_json()
        
        # Check if already initialized
        existing_employee_types = EmployeeType.get_by_country(country.country_id)
        if existing_employee_types:
            return jsonify({
                "error": "Country already initialized",
                "existing_employee_types": len(existing_employee_types)
            }), 409
        
        created_items = {
            'employee_types': [],
            'deduction_types': [],
            'errors': []
        }
        
        # Create basic employee types
        basic_employee_types = [
            {"name": "Permanent Employee", "code": "PERMANENT", "is_default": True},
            {"name": "Contract Employee", "code": "CONTRACT", "is_default": False},
            {"name": "Casual Employee", "code": "CASUAL", "is_default": False}
        ]
        
        for et_data in basic_employee_types:
            try:
                employee_type = EmployeeType(
                    country_id=country.country_id,
                    name=et_data['name'],
                    code=et_data['code'],
                    is_default=et_data['is_default'],
                    is_active=True
                )
                db.session.add(employee_type)
                created_items['employee_types'].append(employee_type)
            except Exception as e:
                created_items['errors'].append(f"Employee type {et_data['name']}: {str(e)}")
        
        # Create basic deduction types based on country
        basic_deduction_types = []
        
        if country_code.upper() == 'RW':  # Rwanda
            basic_deduction_types = [
                {"name": "Pension Contribution", "code": "PENSION", "is_mandatory": True},
                {"name": "Community Based Health Insurance", "code": "CBHI", "is_mandatory": True}
            ]
        else:  # Generic
            basic_deduction_types = [
                {"name": "Pension Contribution", "code": "PENSION", "is_mandatory": True},
                {"name": "Health Insurance", "code": "HEALTH_INSURANCE", "is_mandatory": True}
            ]
        
        for dt_data in basic_deduction_types:
            try:
                deduction_type = DeductionType(
                    country_id=country.country_id,
                    name=dt_data['name'],
                    code=dt_data['code'],
                    is_mandatory=dt_data['is_mandatory'],
                    is_active=True
                )
                db.session.add(deduction_type)
                created_items['deduction_types'].append(deduction_type)
            except Exception as e:
                created_items['errors'].append(f"Deduction type {dt_data['name']}: {str(e)}")
        
        db.session.commit()
        
        app.logger.info(f"Country initialized: {country.name} by user {g.user.get('user_id')}")
        
        return jsonify({
            "success": True,
            "message": f"Country '{country.name}' initialized successfully",
            "created": {
                "employee_types": len(created_items['employee_types']),
                "deduction_types": len(created_items['deduction_types'])
            },
            "details": {
                "employee_types": [et.to_dict() for et in created_items['employee_types']],
                "deduction_types": [dt.to_dict() for dt in created_items['deduction_types']],
                "errors": created_items['errors']
            },
            "next_steps": [
                "Create PAYE tax policy with tax brackets",
                "Set up deduction policies with rates",
                "Test payroll calculations"
            ]
        }), 201
        
    except Exception as e:
        db.session.rollback()
        app.logger.error(f"Error initializing country: {e}")
        return jsonify({"error": "Failed to initialize country"}), 500

@country_setup_api.route('/api/countries/<country_code>/setup/quick-setup', methods=['POST'])
@token_required
@roles_required('admin', 'super-admin')
def quick_setup_country(country_code):
    """Quick setup with predefined templates for specific countries."""
    try:
        country = Country.get_country_by_code(country_code.upper())
        if not country:
            return jsonify({"error": f"Country with code '{country_code}' not found"}), 404
        
        data = request.get_json()
        template = data.get('template', 'generic')
        
        # Check if already has significant setup
        existing_policies = PayrollPolicy.query.filter_by(country_id=country.country_id).count()
        if existing_policies > 0:
            return jsonify({
                "error": "Country already has payroll policies configured",
                "existing_policies": existing_policies
            }), 409
        
        created_items = {
            'employee_types': [],
            'deduction_types': [],
            'policy_types': [],
            'policies': [],
            'deduction_policies': [],
            'tax_brackets': [],
            'errors': []
        }
        
        # Initialize basic setup first
        initialize_result = initialize_country_setup(country_code)
        if initialize_result[1] not in [201, 409]:  # 409 means already initialized
            return initialize_result
        
        # Apply template-specific configurations
        if template == 'rwanda':
            success = _apply_rwanda_template(country, created_items)
        elif template == 'kenya':
            success = _apply_kenya_template(country, created_items)
        else:
            success = _apply_generic_template(country, created_items)
        
        if success:
            db.session.commit()
            app.logger.info(f"Quick setup completed for {country.name} using {template} template by user {g.user.get('user_id')}")
        else:
            db.session.rollback()
            return jsonify({"error": "Failed to apply template"}), 500
        
        return jsonify({
            "success": True,
            "message": f"Quick setup completed for '{country.name}' using {template} template",
            "template_used": template,
            "created": {
                "policies": len(created_items['policies']),
                "deduction_policies": len(created_items['deduction_policies']),
                "tax_brackets": len(created_items['tax_brackets'])
            },
            "errors": created_items['errors'],
            "next_steps": [
                "Review and adjust tax brackets if needed",
                "Test payroll calculations",
                "Configure company-specific settings"
            ]
        }), 201
        
    except Exception as e:
        db.session.rollback()
        app.logger.error(f"Error in quick setup: {e}")
        return jsonify({"error": "Failed to complete quick setup"}), 500

def _apply_rwanda_template(country, created_items):
    """Apply Rwanda-specific payroll template."""
    try:
        # Get or create PAYE policy type
        paye_policy_type = PayrollPolicyType.get_by_code('PAYE')
        if not paye_policy_type:
            return False
        
        # Get permanent employee type
        permanent_employee = EmployeeType.query.filter_by(
            country_id=country.country_id,
            code='PERMANENT'
        ).first()
        
        if not permanent_employee:
            return False
        
        # Create PAYE policy
        paye_policy = PayrollPolicy(
            country_id=country.country_id,
            policy_type_id=paye_policy_type.policy_type_id,
            employee_type_id=permanent_employee.employee_type_id,
            effective_from=date.today(),
            is_active=True
        )
        db.session.add(paye_policy)
        db.session.flush()  # Get the ID
        
        # Create Rwanda tax brackets (2025 rates)
        rwanda_tax_brackets = [
            {"order": 1, "min": 0, "max": 360000, "rate": 0.0},
            {"order": 2, "min": 360000, "max": 720000, "rate": 0.2},
            {"order": 3, "min": 720000, "max": None, "rate": 0.3}
        ]
        
        for bracket_data in rwanda_tax_brackets:
            tax_bracket = TaxBracket(
                policy_id=paye_policy.policy_id,
                bracket_order=bracket_data['order'],
                min_amount=Decimal(str(bracket_data['min'])),
                max_amount=Decimal(str(bracket_data['max'])) if bracket_data['max'] else None,
                tax_rate=Decimal(str(bracket_data['rate']))
            )
            db.session.add(tax_bracket)
            created_items['tax_brackets'].append(tax_bracket)
        
        created_items['policies'].append(paye_policy)
        
        # Create deduction policies
        pension_type = DeductionType.query.filter_by(
            country_id=country.country_id,
            code='PENSION'
        ).first()
        
        cbhi_type = DeductionType.query.filter_by(
            country_id=country.country_id,
            code='CBHI'
        ).first()
        
        if pension_type:
            pension_policy = DeductionPolicy(
                country_id=country.country_id,
                deduction_type_id=pension_type.deduction_type_id,
                employee_rate=Decimal('0.03'),  # 3%
                employer_rate=Decimal('0.05'),  # 5%
                effective_from=date.today(),
                is_active=True
            )
            db.session.add(pension_policy)
            created_items['deduction_policies'].append(pension_policy)
        
        if cbhi_type:
            cbhi_policy = DeductionPolicy(
                country_id=country.country_id,
                deduction_type_id=cbhi_type.deduction_type_id,
                employee_rate=Decimal('0.025'),  # 2.5%
                employer_rate=Decimal('0.025'),  # 2.5%
                effective_from=date.today(),
                is_active=True
            )
            db.session.add(cbhi_policy)
            created_items['deduction_policies'].append(cbhi_policy)
        
        return True
        
    except Exception as e:
        created_items['errors'].append(f"Rwanda template error: {str(e)}")
        return False

def _apply_generic_template(country, created_items):
    """Apply generic payroll template."""
    try:
        # Basic setup with common deduction rates
        pension_type = DeductionType.query.filter_by(
            country_id=country.country_id,
            code='PENSION'
        ).first()
        
        if pension_type:
            pension_policy = DeductionPolicy(
                country_id=country.country_id,
                deduction_type_id=pension_type.deduction_type_id,
                employee_rate=Decimal('0.05'),  # 5%
                employer_rate=Decimal('0.05'),  # 5%
                effective_from=date.today(),
                is_active=True
            )
            db.session.add(pension_policy)
            created_items['deduction_policies'].append(pension_policy)
        
        return True
        
    except Exception as e:
        created_items['errors'].append(f"Generic template error: {str(e)}")
        return False

def _apply_kenya_template(country, created_items):
    """Apply Kenya-specific payroll template (placeholder)."""
    # This would contain Kenya-specific tax brackets and deduction rates
    return _apply_generic_template(country, created_items)
