"""Deduction Types Management API for Central Database Administration."""

from flask import Blueprint, request, jsonify, current_app as app, g
from application.Models.deduction_type import DeductionType
from application.Models.deduction_policy import DeductionPolicy
from application.Models.country import Country
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.database import central_db as db
from datetime import datetime
import uuid

deduction_types_api = Blueprint('deduction_types_api', __name__)

# ============================================================================
# DEDUCTION TYPES MANAGEMENT ENDPOINTS
# ============================================================================

@deduction_types_api.route('/api/countries/<country_code>/deduction-types', methods=['GET'])
@token_required
@roles_required('admin', 'super-admin', 'hr')
def get_deduction_types_for_country(country_code):
    """Get all deduction types for a specific country."""
    try:
        country = Country.get_country_by_code(country_code.upper())
        if not country:
            return jsonify({"error": f"Country with code '{country_code}' not found"}), 404
        
        deduction_types = DeductionType.get_by_country(country.country_id)
        
        deduction_types_data = []
        for deduction_type in deduction_types:
            deduction_type_dict = deduction_type.to_dict()
            
            # Add usage statistics
            policies_count = DeductionPolicy.query.filter_by(
                deduction_type_id=deduction_type.deduction_type_id
            ).count()
            active_policies = DeductionPolicy.query.filter_by(
                deduction_type_id=deduction_type.deduction_type_id,
                is_active=True
            ).count()
            
            deduction_type_dict['usage_stats'] = {
                'total_policies': policies_count,
                'active_policies': active_policies,
                'is_in_use': policies_count > 0
            }
            
            deduction_types_data.append(deduction_type_dict)
        
        return jsonify({
            "success": True,
            "country": {
                "country_id": str(country.country_id),
                "name": country.name,
                "code": country.code
            },
            "deduction_types": deduction_types_data,
            "total_count": len(deduction_types_data)
        }), 200
        
    except Exception as e:
        app.logger.error(f"Error getting deduction types: {e}")
        return jsonify({"error": "Failed to get deduction types"}), 500

@deduction_types_api.route('/api/countries/<country_code>/deduction-types', methods=['POST'])
@token_required
@roles_required('admin', 'super-admin', 'super-admin')
def create_deduction_type(country_code):
    """Create a new deduction type for a country."""
    try:
        country = Country.get_country_by_code(country_code.upper())
        if not country:
            return jsonify({"error": f"Country with code '{country_code}' not found"}), 404
        
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['name', 'code', 'calculation_base']
        for field in required_fields:
            if not data.get(field):
                return jsonify({"error": f"{field} is required"}), 400
        
        # Check if code already exists for this country
        existing_type = DeductionType.query.filter_by(
            country_id=country.country_id,
            code=data.get('code').upper()
        ).first()
        
        if existing_type:
            return jsonify({
                "error": f"Deduction type with code '{data.get('code')}' already exists for {country.name}"
            }), 409
        
        # Validate calculation_base
        calculation_base = data.get('calculation_base')
        if not calculation_base:
            return jsonify({"error": "calculation_base is required"}), 400

        valid_bases = ['BASIC_SALARY', 'GROSS_SALARY', 'PENSIONABLE_SALARY', 'NET_BEFORE_CBHI', 'NET_SALARY']
        if calculation_base not in valid_bases:
            return jsonify({
                "error": f"Invalid calculation_base. Must be one of: {', '.join(valid_bases)}"
            }), 400

        # Create deduction type
        deduction_type_data = {
            'country_id': country.country_id,
            'name': data.get('name'),
            'code': data.get('code').upper(),
            'description': data.get('description'),
            'has_employee_contribution': data.get('has_employee_contribution', True),
            'has_employer_contribution': data.get('has_employer_contribution', False),
            'calculation_base': calculation_base,
            'is_mandatory': data.get('is_mandatory', False),
            'is_active': data.get('is_active', True)
        }
        
        deduction_type = DeductionType.create_deduction_type(**deduction_type_data)
        if not deduction_type:
            return jsonify({"error": "Failed to create deduction type"}), 500
        
        app.logger.info(f"Deduction type created: {deduction_type.name} ({deduction_type.code}) for {country.name} by user {g.user.get('user_id')}")
        
        return jsonify({
            "success": True,
            "message": f"Deduction type '{deduction_type.name}' created successfully for {country.name}",
            "deduction_type": deduction_type.to_dict()
        }), 201
        
    except Exception as e:
        app.logger.error(f"Error creating deduction type: {e}")
        return jsonify({"error": "Failed to create deduction type"}), 500

@deduction_types_api.route('/api/countries/<country_code>/deduction-types/<deduction_type_id>', methods=['GET'])
@token_required
@roles_required('admin', 'super-admin', 'hr', 'super-admin')
def get_deduction_type_by_id(country_code, deduction_type_id):
    """Get a specific deduction type with detailed information."""
    try:
        country = Country.get_country_by_code(country_code.upper())
        if not country:
            return jsonify({"error": f"Country with code '{country_code}' not found"}), 404
        
        deduction_type = DeductionType.get_by_id(deduction_type_id)
        if not deduction_type or deduction_type.country_id != country.country_id:
            return jsonify({"error": "Deduction type not found"}), 404
        
        deduction_type_dict = deduction_type.to_dict()
        
        # Get detailed usage information
        policies = DeductionPolicy.query.filter_by(
            deduction_type_id=deduction_type.deduction_type_id
        ).all()
        
        deduction_type_dict['detailed_usage'] = {
            'total_policies': len(policies),
            'active_policies': len([p for p in policies if p.is_active]),
            'policies': [
                {
                    'policy_id': str(p.deduction_policy_id),
                    'employee_rate': float(p.employee_rate) if p.employee_rate else None,
                    'employer_rate': float(p.employer_rate) if p.employer_rate else None,
                    'effective_from': p.effective_from.isoformat() if p.effective_from else None,
                    'is_active': p.is_active
                } for p in policies
            ]
        }
        
        return jsonify({
            "success": True,
            "country": {
                "country_id": str(country.country_id),
                "name": country.name,
                "code": country.code
            },
            "deduction_type": deduction_type_dict
        }), 200
        
    except Exception as e:
        app.logger.error(f"Error getting deduction type: {e}")
        return jsonify({"error": "Failed to get deduction type"}), 500

@deduction_types_api.route('/api/countries/<country_code>/deduction-types/<deduction_type_id>', methods=['PUT'])
@token_required
@roles_required('admin', 'super-admin')
def update_deduction_type(country_code, deduction_type_id):
    """Update a deduction type's details."""
    try:
        country = Country.get_country_by_code(country_code.upper())
        if not country:
            return jsonify({"error": f"Country with code '{country_code}' not found"}), 404
        
        deduction_type = DeductionType.get_by_id(deduction_type_id)
        if not deduction_type or deduction_type.country_id != country.country_id:
            return jsonify({"error": "Deduction type not found"}), 404
        
        data = request.get_json()
        
        # Check if deduction type is in use before allowing certain changes
        policies_count = DeductionPolicy.query.filter_by(
            deduction_type_id=deduction_type.deduction_type_id
        ).count()
        
        # Validate code change
        if 'code' in data and policies_count > 0:
            if data['code'].upper() != deduction_type.code:
                return jsonify({
                    "error": "Cannot change code for deduction type that is in use",
                    "policies_using": policies_count
                }), 409
        
        # Update allowed fields
        allowed_fields = ['name', 'description', 'is_mandatory', 'is_active']
        for field in allowed_fields:
            if field in data:
                setattr(deduction_type, field, data[field])
        
        # Handle code update if not in use
        if 'code' in data and policies_count == 0:
            deduction_type.code = data['code'].upper()
        
        deduction_type.updated_at = db.func.now()
        db.session.commit()
        
        app.logger.info(f"Deduction type updated: {deduction_type.name} ({deduction_type.code}) by user {g.user.get('user_id')}")
        
        return jsonify({
            "success": True,
            "message": f"Deduction type '{deduction_type.name}' updated successfully",
            "deduction_type": deduction_type.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        app.logger.error(f"Error updating deduction type: {e}")
        return jsonify({"error": "Failed to update deduction type"}), 500

@deduction_types_api.route('/api/countries/<country_code>/deduction-types/<deduction_type_id>', methods=['DELETE'])
@token_required
@roles_required('admin', 'super-admin')
def delete_deduction_type(country_code, deduction_type_id):
    """Delete a deduction type."""
    try:
        country = Country.get_country_by_code(country_code.upper())
        if not country:
            return jsonify({"error": f"Country with code '{country_code}' not found"}), 404
        
        deduction_type = DeductionType.get_by_id(deduction_type_id)
        if not deduction_type or deduction_type.country_id != country.country_id:
            return jsonify({"error": "Deduction type not found"}), 404
        
        # Check if deduction type is in use
        policies_count = DeductionPolicy.query.filter_by(
            deduction_type_id=deduction_type.deduction_type_id
        ).count()
        
        if policies_count > 0:
            return jsonify({
                "error": "Cannot delete deduction type that is in use",
                "policies_using": policies_count
            }), 409
        
        deduction_type_name = deduction_type.name
        deduction_type_code = deduction_type.code
        
        db.session.delete(deduction_type)
        db.session.commit()
        
        app.logger.warning(f"Deduction type deleted: {deduction_type_name} ({deduction_type_code}) from {country.name} by user {g.user.get('user_id')}")
        
        return jsonify({
            "success": True,
            "message": f"Deduction type '{deduction_type_name}' deleted successfully"
        }), 200
        
    except Exception as e:
        db.session.rollback()
        app.logger.error(f"Error deleting deduction type: {e}")
        return jsonify({"error": "Failed to delete deduction type"}), 500

# ============================================================================
# BULK OPERATIONS
# ============================================================================

@deduction_types_api.route('/api/countries/<country_code>/deduction-types/bulk-create', methods=['POST'])
@token_required
@roles_required('admin', 'super-admin')
def bulk_create_deduction_types(country_code):
    """Bulk create deduction types for a country."""
    try:
        country = Country.get_country_by_code(country_code.upper())
        if not country:
            return jsonify({"error": f"Country with code '{country_code}' not found"}), 404
        
        data = request.get_json()
        deduction_types_data = data.get('deduction_types', [])
        
        if not deduction_types_data:
            return jsonify({"error": "deduction_types array is required"}), 400
        
        created_types = []
        errors = []
        
        for i, type_data in enumerate(deduction_types_data):
            try:
                # Validate required fields
                if not type_data.get('name') or not type_data.get('code') or not type_data.get('calculation_base'):
                    errors.append(f"Item {i+1}: name, code, and calculation_base are required")
                    continue

                # Validate calculation_base
                calculation_base = type_data.get('calculation_base')
                valid_bases = ['BASIC_SALARY', 'GROSS_SALARY', 'PENSIONABLE_SALARY', 'NET_BEFORE_CBHI', 'NET_SALARY']
                if calculation_base not in valid_bases:
                    errors.append(f"Item {i+1}: Invalid calculation_base. Must be one of: {', '.join(valid_bases)}")
                    continue

                # Check if code already exists
                existing_type = DeductionType.query.filter_by(
                    country_id=country.country_id,
                    code=type_data.get('code').upper()
                ).first()

                if existing_type:
                    errors.append(f"Item {i+1}: Code '{type_data.get('code')}' already exists")
                    continue

                # Create deduction type
                deduction_type = DeductionType(
                    country_id=country.country_id,
                    name=type_data.get('name'),
                    code=type_data.get('code').upper(),
                    description=type_data.get('description'),
                    has_employee_contribution=type_data.get('has_employee_contribution', True),
                    has_employer_contribution=type_data.get('has_employer_contribution', False),
                    calculation_base=calculation_base,
                    is_mandatory=type_data.get('is_mandatory', False),
                    is_active=type_data.get('is_active', True)
                )
                
                db.session.add(deduction_type)
                created_types.append(deduction_type)
                
            except Exception as e:
                errors.append(f"Item {i+1}: {str(e)}")
        
        if created_types:
            db.session.commit()
            app.logger.info(f"Bulk created {len(created_types)} deduction types for {country.name} by user {g.user.get('user_id')}")
        
        return jsonify({
            "success": len(created_types) > 0,
            "message": f"Created {len(created_types)} deduction types",
            "created_count": len(created_types),
            "errors_count": len(errors),
            "created_types": [dt.to_dict() for dt in created_types],
            "errors": errors
        }), 201 if len(created_types) > 0 else 400
        
    except Exception as e:
        db.session.rollback()
        app.logger.error(f"Error bulk creating deduction types: {e}")
        return jsonify({"error": "Failed to create deduction types"}), 500

@deduction_types_api.route('/api/deduction-types/templates', methods=['GET'])
@token_required
@roles_required('admin', 'super-admin', 'hr')
def get_deduction_type_templates():
    """Get common deduction type templates."""
    try:
        templates = [
            {
                "name": "Pension Contribution",
                "code": "PENSION",
                "description": "Mandatory pension/retirement contribution",
                "is_mandatory": True,
                "category": "retirement"
            },
            {
                "name": "Health Insurance",
                "code": "HEALTH_INSURANCE",
                "description": "Health insurance contribution",
                "is_mandatory": True,
                "category": "insurance"
            },
            {
                "name": "Community Based Health Insurance",
                "code": "CBHI",
                "description": "Community-based health insurance (Rwanda)",
                "is_mandatory": True,
                "category": "insurance"
            },
            {
                "name": "Professional Tax",
                "code": "PROFESSIONAL_TAX",
                "description": "Professional tax deduction",
                "is_mandatory": True,
                "category": "tax"
            },
            {
                "name": "Union Dues",
                "code": "UNION_DUES",
                "description": "Trade union membership fees",
                "is_mandatory": False,
                "category": "membership"
            },
            {
                "name": "Life Insurance",
                "code": "LIFE_INSURANCE",
                "description": "Life insurance premium deduction",
                "is_mandatory": False,
                "category": "insurance"
            },
            {
                "name": "Loan Repayment",
                "code": "LOAN_REPAYMENT",
                "description": "Employee loan repayment deduction",
                "is_mandatory": False,
                "category": "loan"
            }
        ]
        
        return jsonify({
            "success": True,
            "templates": templates,
            "total_count": len(templates)
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting deduction type templates: {e}")
        return jsonify({"error": "Failed to get templates"}), 500
