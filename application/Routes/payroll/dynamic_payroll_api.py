from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from application.Models.employees import Employ<PERSON>Sala<PERSON>, Employee
from application.Models.company import Company
from application.Models.Msg import Msg

import jsons
from datetime import datetime, date
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.Helpers.helper_methods import HelperMethods
from application.Services.payroll_calculation_service import PayrollCalculationService, PayrollCalculationScenario
from decimal import Decimal

dynamic_payroll_api = Blueprint('dynamic_payroll_api', __name__)

@dynamic_payroll_api.route('/api/payroll/calculate/dynamic', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def calculate_payroll_dynamic():
    """
    Calculate payroll from different known values and scenarios.
    
    Request body examples:
    {
        "employee_id": "uuid",
        "scenario": "net_plus_allowances",
        "known_values": {
            "net_salary": 450000,
            "transport": 50000,
            "housing": 100000
        },
        "pay_period_start": "2025-01-01",
        "pay_period_end": "2025-01-31"
    }
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        data = request.get_json()
        if not data:
            return jsonify({"error": "Request body is required"}), 400

        # Validate required fields
        required_fields = ['employee_id', 'scenario', 'known_values', 'pay_period_start', 'pay_period_end']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"{field} is required"}), 400

        employee_id = data['employee_id']
        scenario = data['scenario']
        known_values = data['known_values']
        pay_period_start = datetime.strptime(data['pay_period_start'], '%Y-%m-%d').date()
        pay_period_end = datetime.strptime(data['pay_period_end'], '%Y-%m-%d').date()

        # Validate scenario
        valid_scenarios = [s.value for s in PayrollCalculationScenario]
        if scenario not in valid_scenarios:
            return jsonify({"error": f"Invalid scenario. Must be one of: {valid_scenarios}"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        # Connect to the database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Verify employee exists
            employee = session.query(Employee).filter_by(employee_id=employee_id).first()
            if not employee:
                return jsonify({"error": "Employee not found"}), 404

            # Initialize payroll calculation service
            calc_service = PayrollCalculationService(company_id)

            # Calculate payroll using dynamic method
            result = calc_service.calculate_payroll_dynamic(
                session, employee_id, scenario, known_values, pay_period_start, pay_period_end
            )

            # Convert Decimal values to float for JSON serialization
            json_result = calc_service.decimal_to_float_for_json(result)

            return jsonify({
                "success": True,
                "data": json_result,
                "scenario_used": scenario,
                "calculation_date": datetime.now().isoformat()
            }), 200

    except ValueError as e:
        app.logger.error(f"Validation error in dynamic payroll calculation: {e}")
        return jsonify({"error": str(e)}), 400
    except Exception as e:
        app.logger.error(f"Error in dynamic payroll calculation: {e}")
        return jsonify({"error": "Internal server error"}), 500

@dynamic_payroll_api.route('/api/employees/<employee_id>/salary/create-from-scenario', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def create_salary_from_scenario(employee_id):
    """
    Create a salary record from different calculation scenarios.
    Automatically creates allowance records for any allowances in known_values.

    Request body:
    {
        "scenario": "net_plus_allowances",
        "known_values": {
            "net_salary": 450000,
            "transport": 50000,
            "housing": 100000,
            "communication": 25000
        },
        "effective_from": "2025-01-01"
    }

    Note: Employee must have employee_type_id set before calling this endpoint.
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        data = request.get_json()
        if not data:
            return jsonify({"error": "Request body is required"}), 400

        # Validate required fields
        required_fields = ['scenario', 'known_values', 'effective_from']
        for field in required_fields:
            if field not in data:
                return jsonify({"error": f"{field} is required"}), 400

        scenario = data['scenario']
        known_values = data['known_values']
        effective_from = datetime.strptime(data['effective_from'], '%Y-%m-%d').date()

        # Validate scenario
        valid_scenarios = [s.value for s in PayrollCalculationScenario]
        if scenario not in valid_scenarios:
            return jsonify({"error": f"Invalid scenario. Must be one of: {valid_scenarios}"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        # Connect to the database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Verify employee exists and has employee_type_id
            employee = session.query(Employee).filter_by(employee_id=employee_id).first()
            if not employee:
                return jsonify({"error": "Employee not found"}), 404

            if not employee.employee_type_id:
                return jsonify({"error": "Employee must have employee_type_id set before creating salary record"}), 400

            # Create salary record from scenario
            salary = EmployeeSalary.create_from_scenario(
                session, employee_id, scenario,
                known_values, effective_from, company_id
            )

            if salary:
                return jsonify({
                    "success": True,
                    "data": salary.to_dict_with_scenario(),
                    "message": "Salary record created successfully"
                }), 201
            else:
                return jsonify({"error": "Failed to create salary record"}), 500

    except ValueError as e:
        app.logger.error(f"Validation error in salary creation: {e}")
        return jsonify({"error": str(e)}), 400
    except Exception as e:
        app.logger.error(f"Error creating salary from scenario: {e}")
        return jsonify({"error": "Internal server error"}), 500

@dynamic_payroll_api.route('/api/payroll/scenarios', methods=['GET'])
@token_required
def get_calculation_scenarios():
    """Get available payroll calculation scenarios."""
    try:
        scenarios = []
        for scenario in PayrollCalculationScenario:
            scenarios.append({
                "code": scenario.value,
                "name": scenario.name.replace('_', ' ').title(),
                "description": _get_scenario_description(scenario.value)
            })

        return jsonify({
            "success": True,
            "data": scenarios
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting calculation scenarios: {e}")
        return jsonify({"error": "Internal server error"}), 500

def _get_scenario_description(scenario_code):
    """Get description for a calculation scenario."""
    descriptions = {
        "basic_plus_allowances": "Calculate from basic salary and individual allowances",
        "gross_plus_allowances": "Calculate from gross salary and individual allowances", 
        "net_plus_allowances": "Calculate backwards from net salary and allowances",
        "total_cost_breakdown": "Calculate backwards from total staff cost"
    }
    return descriptions.get(scenario_code, "Unknown scenario")

@dynamic_payroll_api.route('/api/payroll/preview/<employee_id>', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def preview_payroll_calculation(employee_id):
    """
    Preview payroll calculation without saving any data.
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        data = request.get_json()
        scenario = data.get('scenario', 'basic_plus_allowances')
        known_values = data.get('known_values', {})
        calculation_date = data.get('calculation_date')
        
        if calculation_date:
            calculation_date = datetime.strptime(calculation_date, '%Y-%m-%d').date()
        else:
            calculation_date = date.today()

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        # Connect to the database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Initialize payroll calculation service
            calc_service = PayrollCalculationService(company_id)

            if known_values:
                # Use dynamic calculation
                result = calc_service.calculate_payroll_dynamic(
                    session, employee_id, scenario, known_values, calculation_date, calculation_date
                )
            else:
                # Use standard calculation
                result = calc_service.calculate_employee_payroll(
                    session, employee_id, calculation_date, calculation_date
                )

            # Convert Decimal values to float for JSON serialization
            json_result = calc_service.decimal_to_float_for_json(result)

            return jsonify({
                "success": True,
                "data": json_result,
                "preview": True
            }), 200

    except Exception as e:
        app.logger.error(f"Error in payroll preview: {e}")
        return jsonify({"error": "Internal server error"}), 500
