from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from application.Models.employees import EmployeeSalary, AllowanceType, EmployeeAllowance, Employee
from application.Models.company import Company
from application.Models.Msg import Msg
from application.utils.db_connection import DatabaseConnection
import jsons
from datetime import datetime, date
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.Helpers.helper_methods import HelperMethods

employee_salary_api = Blueprint('employee_salary_api', __name__)

@employee_salary_api.route('/api/employees/<employee_id>/salary', methods=['GET'])
@token_required
def get_employee_salary(employee_id):
    """Get current salary for an employee."""
    try:
        company_id = request.args.get('company_id')
        date_str = request.args.get('date')
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        # Parse date
        query_date = datetime.strptime(date_str, '%Y-%m-%d').date() if date_str else date.today()

        with DatabaseConnection.get_session(database_name) as session:
            # Get current salary
            salary = EmployeeSalary.get_current_salary(session, employee_id, query_date)
            if not salary:
                return jsonify({"error": "Employee salary not found"}), 404

            # Get active allowances
            allowances = EmployeeAllowance.get_active_allowances(session, employee_id, query_date)
            
            salary_dict = salary.to_dict()
            salary_dict['allowances'] = [a.to_dict() for a in allowances]
            salary_dict['gross_salary'] = salary.calculate_gross_salary(session, query_date, query_date)

            return jsonify({
                "success": True,
                "salary": salary_dict
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting employee salary: {e}")
        return jsonify({"error": "Failed to get employee salary"}), 500

@employee_salary_api.route('/api/employees/<employee_id>/salary', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def create_employee_salary(employee_id):
    """Create or update employee salary."""
    try:
        data = request.get_json()
        company_id = data.get('company_id')
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            # Check if employee exists
            employee = session.query(Employee).filter_by(employee_id=employee_id).first()
            if not employee:
                return jsonify({"error": "Employee not found"}), 404

            # Create salary data
            salary_data = {
                'employee_id': employee_id,
                'employee_type_id': data.get('employee_type_id'),
                'basic_salary': data.get('basic_salary'),
                'currency': data.get('currency', 'RWF'),
                'pay_frequency': data.get('pay_frequency', 'MONTHLY'),
                'effective_from': datetime.strptime(data.get('effective_from'), '%Y-%m-%d').date(),
                'effective_to': datetime.strptime(data.get('effective_to'), '%Y-%m-%d').date() if data.get('effective_to') else None
            }

            # Check if updating existing salary
            current_salary = EmployeeSalary.get_current_salary(session, employee_id)
            if current_salary:
                # Update salary (creates new record and ends current one)
                salary = EmployeeSalary.update_salary(
                    session=session,
                    employee_id=employee_id,
                    new_salary=salary_data['basic_salary'],
                    new_employee_type_id=salary_data['employee_type_id'],
                    effective_from=salary_data['effective_from']
                )
            else:
                # Create new salary
                salary = EmployeeSalary.create_salary(session, **salary_data)

            if not salary:
                return jsonify({"error": "Failed to create/update salary"}), 500

            return jsonify({
                "success": True,
                "salary": salary.to_dict()
            }), 201

    except Exception as e:
        app.logger.error(f"Error creating employee salary: {e}")
        return jsonify({"error": "Failed to create employee salary"}), 500

@employee_salary_api.route('/api/employees/<employee_id>/salary/history', methods=['GET'])
@token_required
def get_employee_salary_history(employee_id):
    """Get salary history for an employee."""
    try:
        company_id = request.args.get('company_id')
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            salary_history = EmployeeSalary.get_salary_history(session, employee_id)

            return jsonify({
                "success": True,
                "salary_history": [s.to_dict() for s in salary_history]
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting salary history: {e}")
        return jsonify({"error": "Failed to get salary history"}), 500

@employee_salary_api.route('/api/allowance-types', methods=['GET'])
@token_required
def get_allowance_types():
    """Get all allowance types for a company."""
    try:
        company_id = request.args.get('company_id')
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            allowance_types = AllowanceType.get_all_active(session)

            return jsonify({
                "success": True,
                "allowance_types": [at.to_dict() for at in allowance_types]
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting allowance types: {e}")
        return jsonify({"error": "Failed to get allowance types"}), 500

@employee_salary_api.route('/api/allowance-types', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def create_allowance_type():
    """Create a new allowance type."""
    try:
        data = request.get_json()
        company_id = data.get('company_id')
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            allowance_type_data = {
                'name': data.get('name'),
                'code': data.get('code'),
                'description': data.get('description'),
                'is_taxable': data.get('is_taxable', True),
                'is_pensionable': data.get('is_pensionable', True),
                'calculation_type': data.get('calculation_type', 'FIXED_AMOUNT'),
                'default_amount': data.get('default_amount')
            }

            allowance_type = AllowanceType.create_allowance_type(session, **allowance_type_data)
            if not allowance_type:
                return jsonify({"error": "Failed to create allowance type"}), 500

            return jsonify({
                "success": True,
                "allowance_type": allowance_type.to_dict()
            }), 201

    except Exception as e:
        app.logger.error(f"Error creating allowance type: {e}")
        return jsonify({"error": "Failed to create allowance type"}), 500

@employee_salary_api.route('/api/employees/<employee_id>/allowances', methods=['GET'])
@token_required
def get_employee_allowances(employee_id):
    """Get allowances for an employee."""
    try:
        company_id = request.args.get('company_id')
        date_str = request.args.get('date')
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        # Parse date
        query_date = datetime.strptime(date_str, '%Y-%m-%d').date() if date_str else date.today()

        with DatabaseConnection.get_session(database_name) as session:
            allowances = EmployeeAllowance.get_active_allowances(session, employee_id, query_date)

            return jsonify({
                "success": True,
                "allowances": [a.to_dict() for a in allowances]
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting employee allowances: {e}")
        return jsonify({"error": "Failed to get employee allowances"}), 500

@employee_salary_api.route('/api/employees/<employee_id>/allowances', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def add_employee_allowance(employee_id):
    """Add an allowance to an employee."""
    try:
        data = request.get_json()
        company_id = data.get('company_id')
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            effective_from = datetime.strptime(data.get('effective_from'), '%Y-%m-%d').date() if data.get('effective_from') else date.today()
            
            allowance = EmployeeAllowance.add_allowance_to_employee(
                session=session,
                employee_id=employee_id,
                allowance_type_id=data.get('allowance_type_id'),
                amount=data.get('amount'),
                effective_from=effective_from
            )

            if not allowance:
                return jsonify({"error": "Failed to add allowance"}), 500

            return jsonify({
                "success": True,
                "allowance": allowance.to_dict()
            }), 201

    except Exception as e:
        app.logger.error(f"Error adding employee allowance: {e}")
        return jsonify({"error": "Failed to add employee allowance"}), 500
