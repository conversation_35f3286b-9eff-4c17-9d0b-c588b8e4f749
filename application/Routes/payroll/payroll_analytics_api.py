from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from application.Models.payroll.payroll_run import PayrollRun
from application.Models.payroll.payslip import Payslip
from application.Models.payroll.payroll_deduction import PayrollDeduction
from application.Models.employees import Employee
from application.Models.company import Company
from application.utils.db_connection import DatabaseConnection
from datetime import datetime, date, timedelta
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from decimal import Decimal
from sqlalchemy import func, and_, extract, case
from calendar import monthrange
import calendar

payroll_analytics_api = Blueprint('payroll_analytics_api', __name__)

@payroll_analytics_api.route('/api/payroll/analytics/overview', methods=['GET'])
@token_required
@roles_required('admin', 'hr', 'finance')
def get_payroll_analytics_overview():
    """
    Get comprehensive payroll analytics overview.
    
    Query parameters:
    - company_id: Required
    - period: 'month', 'quarter', 'year' (default: 'month')
    - year: Specific year (default: current year)
    - month: Specific month (default: current month, only if period='month')
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        period = request.args.get('period', 'month')
        year = int(request.args.get('year', datetime.now().year))
        month = request.args.get('month')
        
        if month:
            month = int(month)
        else:
            month = datetime.now().month if period == 'month' else None

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            # Calculate date ranges
            if period == 'month' and month:
                start_date = date(year, month, 1)
                end_date = date(year, month, monthrange(year, month)[1])
            elif period == 'quarter':
                quarter = ((month - 1) // 3) + 1 if month else 1
                start_month = (quarter - 1) * 3 + 1
                start_date = date(year, start_month, 1)
                end_month = start_month + 2
                end_date = date(year, end_month, monthrange(year, end_month)[1])
            else:  # year
                start_date = date(year, 1, 1)
                end_date = date(year, 12, 31)

            # Overall payroll statistics
            total_payroll_runs = session.query(PayrollRun).filter(
                PayrollRun.pay_period_end.between(start_date, end_date)
            ).count()
            
            total_employees_paid = session.query(func.count(func.distinct(Payslip.employee_id))).join(PayrollRun).filter(
                PayrollRun.pay_period_end.between(start_date, end_date),
                PayrollRun.status == 'FINALIZED'
            ).scalar() or 0
            
            # Financial totals
            financial_totals = session.query(
                func.sum(Payslip.gross_pay).label('total_gross'),
                func.sum(Payslip.total_deductions).label('total_deductions'),
                func.sum(Payslip.net_pay).label('total_net'),
                func.sum(Payslip.employer_contributions).label('total_employer_contributions')
            ).join(PayrollRun).filter(
                PayrollRun.pay_period_end.between(start_date, end_date),
                PayrollRun.status == 'FINALIZED'
            ).first()

            # Department breakdown
            dept_stats = session.query(
                Employee.department_name,
                func.count(Payslip.payslip_id).label('employee_count'),
                func.sum(Payslip.gross_pay).label('total_gross'),
                func.sum(Payslip.net_pay).label('total_net'),
                func.avg(Payslip.gross_pay).label('avg_gross'),
                func.avg(Payslip.net_pay).label('avg_net')
            ).join(Payslip).join(PayrollRun).filter(
                PayrollRun.pay_period_end.between(start_date, end_date),
                PayrollRun.status == 'FINALIZED'
            ).group_by(Employee.department_name).all()

            # Deduction breakdown
            deduction_stats = session.query(
                PayrollDeduction.deduction_type,
                func.sum(PayrollDeduction.employee_amount).label('total_employee'),
                func.sum(PayrollDeduction.employer_amount).label('total_employer'),
                func.count(PayrollDeduction.deduction_id).label('count')
            ).join(Payslip).join(PayrollRun).filter(
                PayrollRun.pay_period_end.between(start_date, end_date),
                PayrollRun.status == 'FINALIZED'
            ).group_by(PayrollDeduction.deduction_type).all()

            # Payroll run status breakdown
            status_stats = session.query(
                PayrollRun.status,
                func.count(PayrollRun.run_id).label('count')
            ).filter(
                PayrollRun.pay_period_end.between(start_date, end_date)
            ).group_by(PayrollRun.status).all()

            return jsonify({
                "success": True,
                "data": {
                    "period_info": {
                        "period": period,
                        "year": year,
                        "month": month,
                        "start_date": start_date.strftime('%Y-%m-%d'),
                        "end_date": end_date.strftime('%Y-%m-%d')
                    },
                    "overall_statistics": {
                        "total_payroll_runs": total_payroll_runs,
                        "total_employees_paid": total_employees_paid,
                        "total_gross_pay": float(financial_totals.total_gross or 0),
                        "total_deductions": float(financial_totals.total_deductions or 0),
                        "total_net_pay": float(financial_totals.total_net or 0),
                        "total_employer_contributions": float(financial_totals.total_employer_contributions or 0),
                        "total_staff_cost": float((financial_totals.total_gross or 0) + (financial_totals.total_employer_contributions or 0)),
                        "average_gross_per_employee": float((financial_totals.total_gross or 0) / total_employees_paid) if total_employees_paid > 0 else 0,
                        "average_net_per_employee": float((financial_totals.total_net or 0) / total_employees_paid) if total_employees_paid > 0 else 0
                    },
                    "department_breakdown": [
                        {
                            "department": stat.department_name or "Unassigned",
                            "employee_count": stat.employee_count,
                            "total_gross": float(stat.total_gross or 0),
                            "total_net": float(stat.total_net or 0),
                            "average_gross": float(stat.avg_gross or 0),
                            "average_net": float(stat.avg_net or 0)
                        } for stat in dept_stats
                    ],
                    "deduction_breakdown": [
                        {
                            "deduction_type": stat.deduction_type,
                            "total_employee_amount": float(stat.total_employee or 0),
                            "total_employer_amount": float(stat.total_employer or 0),
                            "total_combined": float((stat.total_employee or 0) + (stat.total_employer or 0)),
                            "count": stat.count
                        } for stat in deduction_stats
                    ],
                    "payroll_run_status": [
                        {
                            "status": stat.status,
                            "count": stat.count
                        } for stat in status_stats
                    ]
                }
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting payroll analytics overview: {e}")
        return jsonify({"error": "Internal server error"}), 500

@payroll_analytics_api.route('/api/payroll/analytics/trends', methods=['GET'])
@token_required
@roles_required('admin', 'hr', 'finance')
def get_payroll_trends():
    """
    Get payroll trends over time (monthly/yearly).
    
    Query parameters:
    - company_id: Required
    - period: 'monthly', 'yearly' (default: 'monthly')
    - months_back: Number of months to go back (default: 12)
    - years_back: Number of years to go back (default: 3, only if period='yearly')
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        period = request.args.get('period', 'monthly')
        months_back = int(request.args.get('months_back', 12))
        years_back = int(request.args.get('years_back', 3))

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            trends = []
            
            if period == 'monthly':
                # Monthly trends
                for i in range(months_back):
                    target_date = date.today().replace(day=1) - timedelta(days=30*i)
                    month_start = target_date.replace(day=1)
                    month_end = date(target_date.year, target_date.month, 
                                   monthrange(target_date.year, target_date.month)[1])
                    
                    # Payroll runs
                    payroll_runs = session.query(func.count(PayrollRun.run_id)).filter(
                        PayrollRun.pay_period_end.between(month_start, month_end)
                    ).scalar() or 0
                    
                    # Financial data
                    financial_data = session.query(
                        func.sum(Payslip.gross_pay).label('gross'),
                        func.sum(Payslip.net_pay).label('net'),
                        func.sum(Payslip.total_deductions).label('deductions'),
                        func.sum(Payslip.employer_contributions).label('employer_contrib'),
                        func.count(func.distinct(Payslip.employee_id)).label('employees')
                    ).join(PayrollRun).filter(
                        PayrollRun.pay_period_end.between(month_start, month_end),
                        PayrollRun.status == 'FINALIZED'
                    ).first()
                    
                    trends.append({
                        "period": target_date.strftime('%Y-%m'),
                        "period_name": target_date.strftime('%B %Y'),
                        "payroll_runs": payroll_runs,
                        "employees_paid": financial_data.employees or 0,
                        "total_gross": float(financial_data.gross or 0),
                        "total_net": float(financial_data.net or 0),
                        "total_deductions": float(financial_data.deductions or 0),
                        "total_employer_contributions": float(financial_data.employer_contrib or 0),
                        "total_staff_cost": float((financial_data.gross or 0) + (financial_data.employer_contrib or 0)),
                        "average_gross_per_employee": float((financial_data.gross or 0) / (financial_data.employees or 1)) if financial_data.employees else 0
                    })
            
            else:  # yearly
                # Yearly trends
                current_year = datetime.now().year
                for i in range(years_back):
                    target_year = current_year - i
                    
                    # Similar calculations for yearly data
                    payroll_runs = session.query(func.count(PayrollRun.run_id)).filter(
                        extract('year', PayrollRun.pay_period_end) == target_year
                    ).scalar() or 0
                    
                    financial_data = session.query(
                        func.sum(Payslip.gross_pay).label('gross'),
                        func.sum(Payslip.net_pay).label('net'),
                        func.sum(Payslip.total_deductions).label('deductions'),
                        func.sum(Payslip.employer_contributions).label('employer_contrib'),
                        func.count(func.distinct(Payslip.employee_id)).label('employees')
                    ).join(PayrollRun).filter(
                        extract('year', PayrollRun.pay_period_end) == target_year,
                        PayrollRun.status == 'FINALIZED'
                    ).first()
                    
                    trends.append({
                        "period": str(target_year),
                        "period_name": str(target_year),
                        "payroll_runs": payroll_runs,
                        "employees_paid": financial_data.employees or 0,
                        "total_gross": float(financial_data.gross or 0),
                        "total_net": float(financial_data.net or 0),
                        "total_deductions": float(financial_data.deductions or 0),
                        "total_employer_contributions": float(financial_data.employer_contrib or 0),
                        "total_staff_cost": float((financial_data.gross or 0) + (financial_data.employer_contrib or 0)),
                        "average_gross_per_employee": float((financial_data.gross or 0) / (financial_data.employees or 1)) if financial_data.employees else 0
                    })

            return jsonify({
                "success": True,
                "data": {
                    "period_type": period,
                    "periods_analyzed": len(trends),
                    "trends": list(reversed(trends))  # Most recent first
                }
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting payroll trends: {e}")
        return jsonify({"error": "Internal server error"}), 500
