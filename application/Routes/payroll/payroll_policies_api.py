from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from application.Models import EmployeeType, PayrollPolicyType, PayrollPolicy, TaxBracket, DeductionType, DeductionPolicy
from application.Models.company import Company
from application.Models.country import Country
from application.Models.Msg import Msg
import jsons
from datetime import datetime, date
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.Helpers.helper_methods import HelperMethods

payroll_policies_api = Blueprint('payroll_policies_api', __name__)

@payroll_policies_api.route('/api/payroll/employee-types', methods=['GET'])
@token_required
def get_employee_types():
    """Get employee types for a country."""
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get company's country from central DB
        company = Company.get_company_by_id(company_id)
        if not company:
            return jsonify({"error": "Company not found"}), 404

        country_id = company.country_id
        employee_types = EmployeeType.get_by_country(country_id)

        return jsonify({
            "success": True,
            "employee_types": [et.to_dict() for et in employee_types],
            "country": company.country.name if company.country else None
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting employee types: {e}")
        return jsonify({"error": "Failed to get employee types"}), 500

@payroll_policies_api.route('/api/payroll/employee-types', methods=['POST'])
@token_required
@roles_required('admin', 'hr', 'super-admin')
def create_employee_type():
    """Create a new employee type."""
    try:
        data = request.get_json()
        company_id = data.get('company_id')
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get company's country
        company = Company.get_company_by_id(company_id)
        if not company:
            return jsonify({"error": "Company not found"}), 404

        # Create employee type
        employee_type_data = {
            'country_id': company.country_id,
            'name': data.get('name'),
            'code': data.get('code'),
            'description': data.get('description'),
            'is_default': data.get('is_default', False),
            'sort_order': data.get('sort_order', 1)
        }

        employee_type = EmployeeType.create_employee_type(**employee_type_data)
        if not employee_type:
            return jsonify({"error": "Failed to create employee type"}), 500

        return jsonify({
            "success": True,
            "employee_type": employee_type.to_dict()
        }), 201

    except Exception as e:
        app.logger.error(f"Error creating employee type: {e}")
        return jsonify({"error": "Failed to create employee type"}), 500

@payroll_policies_api.route('/api/payroll/policies', methods=['GET'])
@token_required
def get_payroll_policies():
    """Get payroll policies for a country and employee type."""
    try:
        company_id = request.args.get('company_id')
        employee_type_id = request.args.get('employee_type_id')
        policy_type_code = request.args.get('policy_type_code')
        date_str = request.args.get('date')

        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get company's country
        company = Company.get_company_by_id(company_id)
        if not company:
            return jsonify({"error": "Company not found"}), 404

        # Parse date
        query_date = datetime.strptime(date_str, '%Y-%m-%d').date() if date_str else date.today()

        if policy_type_code:
            # Get specific policy
            policy = PayrollPolicy.get_active_policy(
                policy_type_code=policy_type_code,
                country_id=company.country_id,
                employee_type_id=employee_type_id,
                date=query_date
            )
            
            if not policy:
                return jsonify({"error": "Policy not found"}), 404

            policy_dict = policy.to_dict()
            
            # Include tax brackets if it's a PAYE policy
            if policy_type_code == 'PAYE':
                brackets = TaxBracket.get_brackets_for_policy(policy.policy_id)
                policy_dict['tax_brackets'] = [b.to_dict() for b in brackets]

            return jsonify({
                "success": True,
                "policy": policy_dict
            }), 200
        else:
            # Get all policies for country
            policies = PayrollPolicy.get_policies_for_country(company.country_id, query_date)
            
            return jsonify({
                "success": True,
                "policies": [p.to_dict() for p in policies]
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting payroll policies: {e}")
        return jsonify({"error": "Failed to get payroll policies"}), 500

# ============================================================================
# COUNTRY-SPECIFIC ENDPOINTS
# ============================================================================

@payroll_policies_api.route('/api/countries/<country_code>/tax-policies', methods=['GET'])
@token_required
@roles_required('admin', 'super-admin')
def get_country_tax_policies(country_code):
    """Get all tax policies for a specific country."""
    try:
        # Get country by code
        country = Country.get_country_by_code(country_code.upper())
        if not country:
            return jsonify({"error": f"Country with code '{country_code}' not found"}), 404

        # Get query parameters
        employee_type_id = request.args.get('employee_type_id')
        date_str = request.args.get('date')
        policy_type_code = request.args.get('policy_type_code')

        # Parse date
        query_date = datetime.strptime(date_str, '%Y-%m-%d').date() if date_str else date.today()

        # Build query filters
        filters = {
            'country_id': country.country_id,
            'date': query_date
        }

        if employee_type_id:
            filters['employee_type_id'] = employee_type_id
        if policy_type_code:
            filters['policy_type_code'] = policy_type_code

        # Get policies for country
        policies = PayrollPolicy.get_policies_for_country(country.country_id, query_date)

        # Filter by employee type and policy type if specified
        if employee_type_id:
            policies = [p for p in policies if p.employee_type_id == employee_type_id]
        if policy_type_code:
            policies = [p for p in policies if p.policy_type and p.policy_type.code == policy_type_code]

        # Format response
        policies_data = []
        for policy in policies:
            policy_dict = policy.to_dict()
            # Include tax brackets if it's a tax policy
            if policy.policy_type and policy.policy_type.calculation_method == 'PROGRESSIVE_TAX':
                tax_brackets = TaxBracket.get_brackets_for_policy(policy.policy_id)
                policy_dict['tax_brackets'] = [bracket.to_dict() for bracket in tax_brackets]
            policies_data.append(policy_dict)

        return jsonify({
            "success": True,
            "country": {
                "code": country.code,
                "name": country.name
            },
            "policies": policies_data,
            "query_date": query_date.strftime('%Y-%m-%d')
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting country tax policies: {e}")
        return jsonify({"error": "Internal server error"}), 500

@payroll_policies_api.route('/api/countries/<country_code>/tax-policies', methods=['POST'])
@token_required
@roles_required('admin', 'super-admin')
def create_country_tax_policy(country_code):
    """Create a new tax policy for a specific country."""
    try:
        data = request.get_json()

        # Get country by code
        country = Country.get_country_by_code(country_code.upper())
        if not country:
            return jsonify({"error": f"Country with code '{country_code}' not found"}), 404

        # Validate required fields
        required_fields = ['policy_type_code', 'employee_type_id', 'effective_from']
        for field in required_fields:
            if not data.get(field):
                return jsonify({"error": f"{field} is required"}), 400

        # Get policy type
        policy_type = PayrollPolicyType.get_by_code(data.get('policy_type_code'))
        if not policy_type:
            return jsonify({"error": "Invalid policy type code"}), 400

        # Validate employee type belongs to this country
        employee_type = EmployeeType.query.filter_by(
            employee_type_id=data.get('employee_type_id'),
            country_id=country.country_id
        ).first()
        if not employee_type:
            return jsonify({"error": "Employee type not found for this country"}), 400

        # Create policy
        policy_data = {
            'policy_type_id': policy_type.policy_type_id,
            'country_id': country.country_id,
            'employee_type_id': employee_type.employee_type_id,
            'version_number': data.get('version_number', 1),
            'effective_from': datetime.strptime(data.get('effective_from'), '%Y-%m-%d').date(),
            'effective_to': datetime.strptime(data.get('effective_to'), '%Y-%m-%d').date() if data.get('effective_to') else None,
            'created_by': g.user.get('user_id'),
            'change_reason': data.get('change_reason', f'Created tax policy for {country.name}')
        }

        policy = PayrollPolicy.create_policy(**policy_data)
        if not policy:
            return jsonify({"error": "Failed to create policy"}), 500

        # Create tax brackets if provided
        tax_brackets = data.get('tax_brackets', [])
        created_brackets = []
        if tax_brackets and policy_type.calculation_method == 'PROGRESSIVE_TAX':
            brackets_data = []
            for i, bracket in enumerate(tax_brackets):
                bracket_data = {
                    'bracket_order': bracket.get('bracket_order', i + 1),
                    'min_amount': bracket.get('min_amount'),
                    'max_amount': bracket.get('max_amount'),
                    'tax_rate': bracket.get('tax_rate'),
                    'effective_from': policy.effective_from,
                    'effective_to': policy.effective_to
                }
                brackets_data.append(bracket_data)

            created_brackets = TaxBracket.create_brackets_for_policy(policy.policy_id, brackets_data)
            if not created_brackets:
                return jsonify({"error": "Failed to create tax brackets"}), 500

        # Format response
        policy_dict = policy.to_dict()
        if created_brackets:
            policy_dict['tax_brackets'] = [b.to_dict() for b in created_brackets]

        return jsonify({
            "success": True,
            "message": f"Tax policy created for {country.name}",
            "country": {
                "code": country.code,
                "name": country.name
            },
            "policy": policy_dict
        }), 201

    except Exception as e:
        app.logger.error(f"Error creating country tax policy: {e}")
        return jsonify({"error": "Internal server error"}), 500

@payroll_policies_api.route('/api/countries/<country_code>/employee-types', methods=['GET'])
@token_required
@roles_required('admin', 'super-admin', 'hr')
def get_country_employee_types(country_code):
    """Get all employee types for a specific country."""
    try:
        # Get country by code
        country = Country.get_country_by_code(country_code.upper())
        if not country:
            return jsonify({"error": f"Country with code '{country_code}' not found"}), 404

        # Get employee types for this country
        employee_types = EmployeeType.get_by_country(country.country_id)

        return jsonify({
            "success": True,
            "country": {
                "code": country.code,
                "name": country.name
            },
            "employee_types": [et.to_dict() for et in employee_types]
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting country employee types: {e}")
        return jsonify({"error": "Internal server error"}), 500

@payroll_policies_api.route('/api/countries/<country_code>/employee-types', methods=['POST'])
@token_required
@roles_required('admin', 'super-admin')
def create_country_employee_type(country_code):
    """Create a new employee type for a specific country."""
    try:
        data = request.get_json()

        # Get country by code
        country = Country.get_country_by_code(country_code.upper())
        if not country:
            return jsonify({"error": f"Country with code '{country_code}' not found"}), 404

        # Validate required fields
        required_fields = ['name', 'code']
        for field in required_fields:
            if not data.get(field):
                return jsonify({"error": f"{field} is required"}), 400

        # Create employee type
        employee_type_data = {
            'country_id': country.country_id,
            'name': data.get('name'),
            'code': data.get('code').upper(),
            'description': data.get('description'),
            'is_default': data.get('is_default', False),
            'sort_order': data.get('sort_order', 1)
        }

        employee_type = EmployeeType.create_employee_type(**employee_type_data)
        if not employee_type:
            return jsonify({"error": "Failed to create employee type"}), 500

        return jsonify({
            "success": True,
            "message": f"Employee type created for {country.name}",
            "country": {
                "code": country.code,
                "name": country.name
            },
            "employee_type": employee_type.to_dict()
        }), 201

    except Exception as e:
        app.logger.error(f"Error creating country employee type: {e}")
        return jsonify({"error": "Internal server error"}), 500

@payroll_policies_api.route('/api/countries/<country_code>/deduction-policies', methods=['GET'])
@token_required
@roles_required('admin', 'super-admin', 'hr')
def get_country_deduction_policies(country_code):
    """Get all deduction policies for a specific country."""
    try:
        # Get country by code
        country = Country.get_country_by_code(country_code.upper())
        if not country:
            return jsonify({"error": f"Country with code '{country_code}' not found"}), 404

        # Get query parameters
        date_str = request.args.get('date')
        deduction_type_code = request.args.get('deduction_type_code')

        # Parse date
        query_date = datetime.strptime(date_str, '%Y-%m-%d').date() if date_str else date.today()

        # Get deduction policies
        filters = {
            'country_id': country.country_id,
            'date': query_date
        }

        if deduction_type_code:
            filters['deduction_type_code'] = deduction_type_code

        deduction_policies = DeductionPolicy.get_active_policies_for_country(
            country_id=country.country_id,
            date=query_date
        )

        # Filter by deduction type if specified
        if deduction_type_code:
            deduction_policies = [dp for dp in deduction_policies
                                if dp.deduction_type and dp.deduction_type.code == deduction_type_code]

        return jsonify({
            "success": True,
            "country": {
                "code": country.code,
                "name": country.name
            },
            "deduction_policies": [dp.to_dict() for dp in deduction_policies],
            "query_date": query_date.strftime('%Y-%m-%d')
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting country deduction policies: {e}")
        return jsonify({"error": "Internal server error"}), 500

@payroll_policies_api.route('/api/countries/<country_code>/deduction-policies', methods=['POST'])
@token_required
@roles_required('admin', 'super-admin')
def create_country_deduction_policy(country_code):
    """Create a new deduction policy for a specific country."""
    try:
        data = request.get_json()

        # Get country by code
        country = Country.get_country_by_code(country_code.upper())
        if not country:
            return jsonify({"error": f"Country with code '{country_code}' not found"}), 404

        # Validate required fields
        required_fields = ['deduction_type_code', 'effective_from']
        for field in required_fields:
            if not data.get(field):
                return jsonify({"error": f"{field} is required"}), 400

        # Validate employee_rate is provided (can be 0)
        if 'employee_rate' not in data:
            return jsonify({"error": "employee_rate is required"}), 400

        # Get deduction type
        deduction_type = DeductionType.query.filter_by(
            code=data.get('deduction_type_code'),
            country_id=country.country_id
        ).first()
        if not deduction_type:
            return jsonify({"error": "Deduction type not found for this country"}), 400

        # Parse effective_from date
        effective_from_date = datetime.strptime(data.get('effective_from'), '%Y-%m-%d').date()

        # Check for duplicate policy (same deduction type, country, employee type, and effective date)
        employee_type_id = data.get('employee_type_id')  # Can be None for "applies to all"

        existing_policy = DeductionPolicy.query.filter_by(
            deduction_type_id=deduction_type.deduction_type_id,
            country_id=country.country_id,
            employee_type_id=employee_type_id,
            effective_from=effective_from_date,
            is_active=True
        ).first()

        if existing_policy:
            employee_type_desc = f" for employee type '{existing_policy.employee_type.name}'" if existing_policy.employee_type else " for all employee types"
            return jsonify({
                "error": f"A policy for '{data.get('deduction_type_code')}' already exists for {country.name}{employee_type_desc} with effective date {effective_from_date}",
                "existing_policy_id": str(existing_policy.policy_id),
                "suggestion": "Use a different effective date or update the existing policy"
            }), 409

        # Create deduction policy
        policy_data = {
            'deduction_type_id': deduction_type.deduction_type_id,
            'country_id': country.country_id,
            'employee_type_id': employee_type_id,
            'employee_rate': data.get('employee_rate'),
            'employer_rate': data.get('employer_rate', 0),
            'effective_from': effective_from_date,
            'effective_to': datetime.strptime(data.get('effective_to'), '%Y-%m-%d').date() if data.get('effective_to') else None,
            'created_by': g.user.get('user_id'),
            'change_reason': data.get('change_reason', f'Created deduction policy for {country.name}')
        }

        deduction_policy = DeductionPolicy.create_policy(**policy_data)
        if not deduction_policy:
            return jsonify({"error": "Failed to create deduction policy"}), 500

        return jsonify({
            "success": True,
            "message": f"Deduction policy created for {country.name}",
            "country": {
                "code": country.code,
                "name": country.name
            },
            "deduction_policy": deduction_policy.to_dict()
        }), 201

    except Exception as e:
        app.logger.error(f"Error creating country deduction policy: {e}")
        return jsonify({"error": "Internal server error"}), 500

@payroll_policies_api.route('/api/payroll/policies', methods=['POST'])
@token_required
@roles_required('admin', 'hr', 'super-admin')
def create_payroll_policy():
    """Create a new payroll policy with tax brackets."""
    try:
        data = request.get_json()
        company_id = data.get('company_id')
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get company's country
        company = Company.get_company_by_id(company_id)
        if not company:
            return jsonify({"error": "Company not found"}), 404

        # Create policy
        policy_data = {
            'policy_type_id': data.get('policy_type_id'),
            'country_id': company.country_id,
            'employee_type_id': data.get('employee_type_id'),
            'version_number': data.get('version_number', 1),
            'effective_from': datetime.strptime(data.get('effective_from'), '%Y-%m-%d').date(),
            'effective_to': datetime.strptime(data.get('effective_to'), '%Y-%m-%d').date() if data.get('effective_to') else None,
            'created_by': g.user.get('user_id'),
            'change_reason': data.get('change_reason')
        }

        policy = PayrollPolicy.create_policy(**policy_data)
        if not policy:
            return jsonify({"error": "Failed to create policy"}), 500

        # Create tax brackets if provided
        tax_brackets = data.get('tax_brackets', [])
        if tax_brackets:
            brackets_data = []
            for bracket in tax_brackets:
                bracket_data = {
                    'bracket_order': bracket.get('bracket_order'),
                    'min_amount': bracket.get('min_amount'),
                    'max_amount': bracket.get('max_amount'),
                    'tax_rate': bracket.get('tax_rate'),
                    'effective_from': policy.effective_from,
                    'effective_to': policy.effective_to
                }
                brackets_data.append(bracket_data)

            created_brackets = TaxBracket.create_brackets_for_policy(policy.policy_id, brackets_data)
            if not created_brackets:
                return jsonify({"error": "Failed to create tax brackets"}), 500

        policy_dict = policy.to_dict()
        if tax_brackets:
            policy_dict['tax_brackets'] = [b.to_dict() for b in created_brackets]

        return jsonify({
            "success": True,
            "policy": policy_dict
        }), 201

    except Exception as e:
        app.logger.error(f"Error creating payroll policy: {e}")
        return jsonify({"error": "Failed to create payroll policy"}), 500

@payroll_policies_api.route('/api/payroll/deduction-policies', methods=['GET'])
@token_required
def get_deduction_policies():
    """Get deduction policies for a country and employee type."""
    try:
        company_id = request.args.get('company_id')
        employee_type_id = request.args.get('employee_type_id')
        deduction_type_code = request.args.get('deduction_type_code')
        date_str = request.args.get('date')

        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get company's country
        company = Company.get_company_by_id(company_id)
        if not company:
            return jsonify({"error": "Company not found"}), 404

        # Parse date
        query_date = datetime.strptime(date_str, '%Y-%m-%d').date() if date_str else date.today()

        if deduction_type_code:
            # Get specific deduction policy
            policy = DeductionPolicy.get_active_policy(
                deduction_type_code=deduction_type_code,
                country_id=company.country_id,
                employee_type_id=employee_type_id,
                date=query_date
            )
            
            if not policy:
                return jsonify({"error": "Deduction policy not found"}), 404

            return jsonify({
                "success": True,
                "policy": policy.to_dict()
            }), 200
        else:
            # Get all deduction policies for country and employee type
            policies = DeductionPolicy.get_active_policies_for_country(
                country_id=company.country_id,
                employee_type_id=employee_type_id,
                date=query_date
            )
            
            return jsonify({
                "success": True,
                "policies": [p.to_dict() for p in policies]
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting deduction policies: {e}")
        return jsonify({"error": "Failed to get deduction policies"}), 500

@payroll_policies_api.route('/api/payroll/deduction-policies', methods=['POST'])
@token_required
@roles_required('admin', 'hr', 'super-admin')
def create_deduction_policy():
    """Create a new deduction policy."""
    try:
        data = request.get_json()
        company_id = data.get('company_id')
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get company's country
        company = Company.get_company_by_id(company_id)
        if not company:
            return jsonify({"error": "Company not found"}), 404

        # Create deduction policy
        policy_data = {
            'deduction_type_id': data.get('deduction_type_id'),
            'country_id': company.country_id,
            'employee_type_id': data.get('employee_type_id'),
            'version_number': data.get('version_number', 1),
            'employee_rate': data.get('employee_rate'),
            'employer_rate': data.get('employer_rate'),
            'effective_from': datetime.strptime(data.get('effective_from'), '%Y-%m-%d').date(),
            'effective_to': datetime.strptime(data.get('effective_to'), '%Y-%m-%d').date() if data.get('effective_to') else None,
            'created_by': g.user.get('user_id'),
            'change_reason': data.get('change_reason')
        }

        policy = DeductionPolicy.create_policy(**policy_data)
        if not policy:
            return jsonify({"error": "Failed to create deduction policy"}), 500

        return jsonify({
            "success": True,
            "policy": policy.to_dict()
        }), 201

    except Exception as e:
        app.logger.error(f"Error creating deduction policy: {e}")
        return jsonify({"error": "Failed to create deduction policy"}), 500
