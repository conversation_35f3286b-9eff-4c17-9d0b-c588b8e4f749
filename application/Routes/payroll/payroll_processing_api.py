from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from application.Models.employees import PayrollRun, Payslip, PayrollDeduction, EmployeeSalary, Employee
from application.Models.company import Company
from application.Models.Msg import Msg
from application.utils.db_connection import DatabaseConnection
import jsons
from datetime import datetime, date, timedelta
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.Helpers.helper_methods import HelperMethods
from application.Services.payroll_calculation_service import PayrollCalculationService
from decimal import Decimal
from sqlalchemy import func, and_, extract, case
from calendar import monthrange
import calendar

payroll_processing_api = Blueprint('payroll_processing_api', __name__)

@payroll_processing_api.route('/api/payroll/calculate-preview', methods=['POST'])
@token_required
def preview_payroll_calculation():
    """Preview payroll calculation for an employee without saving."""
    try:
        data = request.get_json()
        company_id = data.get('company_id')
        employee_id = data.get('employee_id')
        calculation_date_str = data.get('calculation_date')

        if not company_id or not employee_id:
            return jsonify({"error": "company_id and employee_id are required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        # Parse calculation date
        calculation_date = datetime.strptime(calculation_date_str, '%Y-%m-%d').date() if calculation_date_str else date.today()

        with DatabaseConnection.get_session(database_name) as session:
            # Preview calculation
            calculation = PayrollCalculationService.preview_calculation(
                company_id=company_id,
                employee_id=employee_id,
                session=session,
                calculation_date=calculation_date
            )

            return jsonify({
                "success": True,
                "calculation": calculation
            }), 200

    except Exception as e:
        app.logger.error(f"Error previewing payroll calculation: {e}")
        return jsonify({"error": f"Failed to preview calculation: {str(e)}"}), 500

@payroll_processing_api.route('/api/payroll/runs', methods=['GET'])
@token_required
def get_payroll_runs():
    """Get payroll runs for a company."""
    try:
        company_id = request.args.get('company_id')
        status = request.args.get('status')
        limit = request.args.get('limit', type=int)
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            if status:
                runs = PayrollRun.get_runs_by_status(session, company_id, status)
            else:
                runs = PayrollRun.get_runs_for_company(session, company_id, limit)

            return jsonify({
                "success": True,
                "payroll_runs": [run.to_dict() for run in runs]
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting payroll runs: {e}")
        return jsonify({"error": "Failed to get payroll runs"}), 500

@payroll_processing_api.route('/api/payroll/runs', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def create_payroll_run():
    """Create a new payroll run."""
    try:
        data = request.get_json()
        company_id = data.get('company_id')
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            # Check if payroll run already exists for this period
            existing_run = PayrollRun.get_by_company_and_period(
                session=session,
                company_id=company_id,
                pay_period_start=datetime.strptime(data.get('pay_period_start'), '%Y-%m-%d').date(),
                pay_period_end=datetime.strptime(data.get('pay_period_end'), '%Y-%m-%d').date()
            )

            if existing_run:
                return jsonify({"error": "Payroll run already exists for this period"}), 409

            # Create payroll run
            run_data = {
                'company_id': company_id,
                'pay_period_start': datetime.strptime(data.get('pay_period_start'), '%Y-%m-%d').date(),
                'pay_period_end': datetime.strptime(data.get('pay_period_end'), '%Y-%m-%d').date(),
                'run_date': datetime.strptime(data.get('run_date'), '%Y-%m-%d').date() if data.get('run_date') else date.today(),
                'notes': data.get('notes'),
                'created_by': g.user.get('user_id')
            }

            payroll_run = PayrollRun.create_payroll_run(session, **run_data)
            if not payroll_run:
                return jsonify({"error": "Failed to create payroll run"}), 500

            return jsonify({
                "success": True,
                "payroll_run": payroll_run.to_dict()
            }), 201

    except Exception as e:
        app.logger.error(f"Error creating payroll run: {e}")
        return jsonify({"error": "Failed to create payroll run"}), 500

@payroll_processing_api.route('/api/payroll/runs/<run_id>', methods=['GET'])
@token_required
def get_payroll_run(run_id):
    """Get a specific payroll run."""
    try:
        company_id = request.args.get('company_id')
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            payroll_run = PayrollRun.get_by_id(session, run_id)
            if not payroll_run:
                return jsonify({"error": "Payroll run not found"}), 404

            # Get payslips for this run
            payslips = Payslip.get_payslips_for_run(session, run_id)

            run_dict = payroll_run.to_dict()
            run_dict['payslips'] = [p.to_dict() for p in payslips]

            return jsonify({
                "success": True,
                "payroll_run": run_dict
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting payroll run: {e}")
        return jsonify({"error": "Failed to get payroll run"}), 500

@payroll_processing_api.route('/api/payroll/runs/<run_id>/process', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def process_payroll_run(run_id):
    """Process a payroll run (calculate payslips for all employees)."""
    try:
        company_id = request.args.get('company_id')
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            payroll_run = PayrollRun.get_by_id(session, run_id)
            if not payroll_run:
                return jsonify({"error": "Payroll run not found"}), 404

            if not payroll_run.can_be_modified():
                return jsonify({"error": "Payroll run cannot be modified"}), 400

            # Update status to processing
            PayrollRun.update_status(session, run_id, 'PROCESSING', g.user.get('user_id'))

            # Get all active employees for the pay period
            employees = session.query(Employee).filter_by(is_active=True).all()
            
            total_employees = 0
            total_gross_pay = Decimal('0')
            total_tax = Decimal('0')
            total_employee_deductions = Decimal('0')
            total_employer_contributions = Decimal('0')
            total_net_pay = Decimal('0')

            # Initialize payroll calculation service
            calc_service = PayrollCalculationService(company_id)

            # Process each employee using the calculation service
            for employee in employees:
                try:
                    # Calculate payroll for employee
                    calculation = calc_service.calculate_employee_payroll(
                        session=session,
                        employee_id=employee.employee_id,
                        pay_period_start=payroll_run.pay_period_start,
                        pay_period_end=payroll_run.pay_period_end
                    )

                    # Convert Decimal values for JSON storage in payslip
                    json_calculation = calc_service.decimal_to_float_for_json(calculation)

                    # Create payslip from calculation
                    payslip_data = {
                        'run_id': run_id,
                        'employee_id': employee.employee_id,
                        'pay_period_start': payroll_run.pay_period_start,
                        'pay_period_end': payroll_run.pay_period_end,
                        'employee_type_id': json_calculation['employee_type_id'],
                        'basic_salary': json_calculation['basic_salary'],
                        'allowances': json_calculation['allowances'],
                        'gross_pay': json_calculation['gross_pay'],
                        'tax_calculations': json_calculation['tax_calculations'],
                        'employee_deductions': json_calculation['employee_deductions'],
                        'employer_contributions': json_calculation['employer_contributions'],
                        'net_pay': json_calculation['net_pay']
                    }

                    payslip = Payslip.create_payslip(session, **payslip_data)
                    if payslip:
                        total_employees += 1

                        # Use Decimal values for totals calculation
                        total_gross_pay += Decimal(str(calculation['gross_pay']))
                        total_tax += Decimal(str(calculation['tax_calculations']['total_tax']))

                        # Sum employee deductions using Decimal
                        for deduction in calculation['employee_deductions'].values():
                            total_employee_deductions += Decimal(str(deduction['employee_amount']))

                        # Sum employer contributions using Decimal
                        for contribution in calculation['employer_contributions'].values():
                            total_employer_contributions += Decimal(str(contribution['employer_amount']))

                        total_net_pay += Decimal(str(calculation['net_pay']))

                        # Create detailed deduction records
                        _create_payroll_deductions(session, payslip.payslip_id, calculation)

                        # Process loan deductions
                        loan_deductions = {k: v for k, v in calculation['employee_deductions'].items()
                                         if k.startswith('LOAN_')}
                        if loan_deductions:
                            calc_service.process_loan_deductions(
                                session, employee.employee_id, payslip.payslip_id,
                                loan_deductions, payroll_run.pay_period_end
                            )

                except Exception as e:
                    app.logger.error(f"Error processing payroll for employee {employee.employee_id}: {e}")
                    continue

            # Update payroll run totals (convert Decimal to float for database storage)
            totals = {
                'total_employees': total_employees,
                'total_gross_pay': float(total_gross_pay),
                'total_tax': float(total_tax),
                'total_employee_deductions': float(total_employee_deductions),
                'total_employer_contributions': float(total_employer_contributions),
                'total_net_pay': float(total_net_pay)
            }

            PayrollRun.update_totals(session, run_id, totals)
            PayrollRun.update_status(session, run_id, 'COMPLETED', g.user.get('user_id'))

            # Get updated payroll run
            updated_run = PayrollRun.get_by_id(session, run_id)

            return jsonify({
                "success": True,
                "message": f"Processed payroll for {total_employees} employees",
                "payroll_run": updated_run.to_dict()
            }), 200

    except Exception as e:
        app.logger.error(f"Error processing payroll run: {e}")
        return jsonify({"error": "Failed to process payroll run"}), 500

@payroll_processing_api.route('/api/payroll/runs/<run_id>/approve', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def approve_payroll_run(run_id):
    """Approve a completed payroll run."""
    try:
        company_id = request.args.get('company_id')
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            payroll_run = PayrollRun.get_by_id(session, run_id)
            if not payroll_run:
                return jsonify({"error": "Payroll run not found"}), 404

            if not payroll_run.can_be_approved():
                return jsonify({"error": "Payroll run cannot be approved"}), 400

            # Update status to approved
            updated_run = PayrollRun.update_status(session, run_id, 'APPROVED', g.user.get('user_id'))

            # Update all payslips to approved status
            payslips = Payslip.get_payslips_for_run(session, run_id)
            for payslip in payslips:
                Payslip.update_status(session, payslip.payslip_id, 'APPROVED')

            return jsonify({
                "success": True,
                "message": "Payroll run approved successfully",
                "payroll_run": updated_run.to_dict()
            }), 200

    except Exception as e:
        app.logger.error(f"Error approving payroll run: {e}")
        return jsonify({"error": "Failed to approve payroll run"}), 500

@payroll_processing_api.route('/api/payroll/payslips', methods=['GET'])
@token_required
def get_payslips():
    """Get payslips with optional filtering."""
    try:
        company_id = request.args.get('company_id')
        employee_id = request.args.get('employee_id')
        run_id = request.args.get('run_id')
        limit = request.args.get('limit', type=int)
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            if run_id:
                payslips = Payslip.get_payslips_for_run(session, run_id)
            elif employee_id:
                payslips = Payslip.get_payslips_for_employee(session, employee_id, limit)
            else:
                # Get recent payslips for all employees
                payslips = session.query(Payslip).order_by(Payslip.pay_period_end.desc())
                if limit:
                    payslips = payslips.limit(limit)
                payslips = payslips.all()

            return jsonify({
                "success": True,
                "payslips": [p.to_dict() for p in payslips]
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting payslips: {e}")
        return jsonify({"error": "Failed to get payslips"}), 500

@payroll_processing_api.route('/api/payroll/payslips/<payslip_id>', methods=['GET'])
@token_required
def get_payslip(payslip_id):
    """Get a specific payslip with detailed breakdown."""
    try:
        company_id = request.args.get('company_id')
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            payslip = Payslip.get_by_id(session, payslip_id)
            if not payslip:
                return jsonify({"error": "Payslip not found"}), 404

            # Get deductions for this payslip
            deductions = PayrollDeduction.get_deductions_for_payslip(session, payslip_id)

            payslip_dict = payslip.to_dict()
            payslip_dict['deductions'] = [d.to_dict() for d in deductions]

            return jsonify({
                "success": True,
                "payslip": payslip_dict
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting payslip: {e}")
        return jsonify({"error": "Failed to get payslip"}), 500

def _create_payroll_deductions(session, payslip_id, calculation):
    """Create detailed payroll deduction records."""
    try:
        deductions_data = []

        # Create employee deduction records
        for deduction_code, deduction in calculation['employee_deductions'].items():
            deduction_data = {
                'payslip_id': payslip_id,
                'deduction_type_id': None,  # Would need to lookup by code
                'deduction_name': deduction['name'],
                'deduction_code': deduction_code,
                'calculation_base': deduction['calculation_base'],
                'employee_rate_applied': deduction['rate'],
                'employer_rate_applied': None,
                'employee_amount': deduction['employee_amount'],
                'employer_amount': 0
            }
            deductions_data.append(deduction_data)

        # Create employer contribution records
        for contribution_code, contribution in calculation['employer_contributions'].items():
            # Check if we already have a record for this deduction type
            existing_deduction = next((d for d in deductions_data if d['deduction_code'] == contribution_code), None)

            if existing_deduction:
                # Update existing record with employer contribution
                existing_deduction['employer_rate_applied'] = contribution['rate']
                existing_deduction['employer_amount'] = contribution['employer_amount']
            else:
                # Create new record for employer-only contribution
                deduction_data = {
                    'payslip_id': payslip_id,
                    'deduction_type_id': None,  # Would need to lookup by code
                    'deduction_name': contribution['name'],
                    'deduction_code': contribution_code,
                    'calculation_base': contribution['calculation_base'],
                    'employee_rate_applied': None,
                    'employer_rate_applied': contribution['rate'],
                    'employee_amount': 0,
                    'employer_amount': contribution['employer_amount']
                }
                deductions_data.append(deduction_data)

        # Create all deduction records
        PayrollDeduction.create_deductions_for_payslip(session, payslip_id, deductions_data)

    except Exception as e:
        app.logger.error(f"Error creating payroll deductions: {e}")
        # Don't raise exception as this is supplementary data

# ==================== PAYROLL ANALYTICS ENDPOINTS ====================

@payroll_processing_api.route('/api/payroll/analytics/overview', methods=['GET'])
@token_required
@roles_required('admin', 'hr', 'finance')
def get_payroll_analytics_overview():
    """
    Get comprehensive payroll analytics overview.

    Query parameters:
    - company_id: Required
    - period: 'month', 'quarter', 'year' (default: 'month')
    - year: Specific year (default: current year)
    - month: Specific month (default: current month, only if period='month')
    """
    try:
        company_id = request.args.get('company_id')
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        period = request.args.get('period', 'month')
        year = int(request.args.get('year', datetime.now().year))
        month = request.args.get('month')

        if month:
            month = int(month)
        else:
            month = datetime.now().month if period == 'month' else None

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            # Calculate date ranges
            if period == 'month' and month:
                start_date = date(year, month, 1)
                end_date = date(year, month, monthrange(year, month)[1])
            elif period == 'quarter':
                quarter = ((month - 1) // 3) + 1 if month else 1
                start_month = (quarter - 1) * 3 + 1
                start_date = date(year, start_month, 1)
                end_month = start_month + 2
                end_date = date(year, end_month, monthrange(year, end_month)[1])
            else:  # year
                start_date = date(year, 1, 1)
                end_date = date(year, 12, 31)

            # Overall payroll statistics
            total_payroll_runs = session.query(PayrollRun).filter(
                PayrollRun.pay_period_end.between(start_date, end_date)
            ).count()

            total_employees_paid = session.query(func.count(func.distinct(Payslip.employee_id))).join(PayrollRun).filter(
                PayrollRun.pay_period_end.between(start_date, end_date),
                PayrollRun.status == 'FINALIZED'
            ).scalar() or 0

            # Financial totals
            financial_totals = session.query(
                func.sum(Payslip.gross_pay).label('total_gross'),
                func.sum(Payslip.total_deductions).label('total_deductions'),
                func.sum(Payslip.net_pay).label('total_net'),
                func.sum(Payslip.employer_contributions).label('total_employer_contributions')
            ).join(PayrollRun).filter(
                PayrollRun.pay_period_end.between(start_date, end_date),
                PayrollRun.status == 'FINALIZED'
            ).first()

            # Department breakdown
            dept_stats = session.query(
                Employee.department_name,
                func.count(Payslip.payslip_id).label('employee_count'),
                func.sum(Payslip.gross_pay).label('total_gross'),
                func.sum(Payslip.net_pay).label('total_net'),
                func.avg(Payslip.gross_pay).label('avg_gross'),
                func.avg(Payslip.net_pay).label('avg_net')
            ).join(Payslip).join(PayrollRun).filter(
                PayrollRun.pay_period_end.between(start_date, end_date),
                PayrollRun.status == 'FINALIZED'
            ).group_by(Employee.department_name).all()

            # Deduction breakdown
            deduction_stats = session.query(
                PayrollDeduction.deduction_code,
                PayrollDeduction.deduction_name,
                func.sum(PayrollDeduction.employee_amount).label('total_employee'),
                func.sum(PayrollDeduction.employer_amount).label('total_employer'),
                func.count(PayrollDeduction.deduction_id).label('count')
            ).join(Payslip).join(PayrollRun).filter(
                PayrollRun.pay_period_end.between(start_date, end_date),
                PayrollRun.status == 'FINALIZED'
            ).group_by(PayrollDeduction.deduction_code, PayrollDeduction.deduction_name).all()

            # Payroll run status breakdown
            status_stats = session.query(
                PayrollRun.status,
                func.count(PayrollRun.run_id).label('count')
            ).filter(
                PayrollRun.pay_period_end.between(start_date, end_date)
            ).group_by(PayrollRun.status).all()

            return jsonify({
                "success": True,
                "data": {
                    "period_info": {
                        "period": period,
                        "year": year,
                        "month": month,
                        "start_date": start_date.strftime('%Y-%m-%d'),
                        "end_date": end_date.strftime('%Y-%m-%d')
                    },
                    "overall_statistics": {
                        "total_payroll_runs": total_payroll_runs,
                        "total_employees_paid": total_employees_paid,
                        "total_gross_pay": float(financial_totals.total_gross or 0),
                        "total_deductions": float(financial_totals.total_deductions or 0),
                        "total_net_pay": float(financial_totals.total_net or 0),
                        "total_employer_contributions": float(financial_totals.total_employer_contributions or 0),
                        "total_staff_cost": float((financial_totals.total_gross or 0) + (financial_totals.total_employer_contributions or 0)),
                        "average_gross_per_employee": float((financial_totals.total_gross or 0) / total_employees_paid) if total_employees_paid > 0 else 0,
                        "average_net_per_employee": float((financial_totals.total_net or 0) / total_employees_paid) if total_employees_paid > 0 else 0
                    },
                    "department_breakdown": [
                        {
                            "department": stat.department_name or "Unassigned",
                            "employee_count": stat.employee_count,
                            "total_gross": float(stat.total_gross or 0),
                            "total_net": float(stat.total_net or 0),
                            "average_gross": float(stat.avg_gross or 0),
                            "average_net": float(stat.avg_net or 0)
                        } for stat in dept_stats
                    ],
                    "deduction_breakdown": [
                        {
                            "deduction_code": stat.deduction_code,
                            "deduction_name": stat.deduction_name,
                            "total_employee_amount": float(stat.total_employee or 0),
                            "total_employer_amount": float(stat.total_employer or 0),
                            "total_combined": float((stat.total_employee or 0) + (stat.total_employer or 0)),
                            "count": stat.count
                        } for stat in deduction_stats
                    ],
                    "payroll_run_status": [
                        {
                            "status": stat.status,
                            "count": stat.count
                        } for stat in status_stats
                    ]
                }
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting payroll analytics overview: {e}")
        return jsonify({"error": "Internal server error"}), 500
