from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from application.Models.employees import CompanyPayrollSettings
from application.Models.company import Company
from application.Models.Msg import Msg
from application.utils.db_connection import DatabaseConnection
import jsons
from datetime import datetime, date
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.Helpers.helper_methods import HelperMethods

payroll_settings_api = Blueprint('payroll_settings_api', __name__)

@payroll_settings_api.route('/api/payroll/settings', methods=['GET'])
@token_required
def get_payroll_settings():
    """Get current payroll settings for a company."""
    try:
        company_id = request.args.get('company_id')
        date_str = request.args.get('date')
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        # Parse date
        query_date = datetime.strptime(date_str, '%Y-%m-%d').date() if date_str else date.today()

        with DatabaseConnection.get_session(database_name) as session:
            settings = CompanyPayrollSettings.get_current_settings(session, company_id, query_date)
            
            if not settings:
                return jsonify({"error": "Payroll settings not found"}), 404

            return jsonify({
                "success": True,
                "settings": settings.to_dict()
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting payroll settings: {e}")
        return jsonify({"error": "Failed to get payroll settings"}), 500

@payroll_settings_api.route('/api/payroll/settings', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def create_payroll_settings():
    """Create or update payroll settings for a company."""
    try:
        data = request.get_json()
        company_id = data.get('company_id')
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            # Check if settings already exist
            existing_settings = CompanyPayrollSettings.get_current_settings(session, company_id)
            
            settings_data = {
                'default_pay_frequency': data.get('default_pay_frequency', 'MONTHLY'),
                'payroll_cutoff_day': data.get('payroll_cutoff_day', 25),
                'payment_day': data.get('payment_day', 30),
                'overtime_enabled': data.get('overtime_enabled', False),
                'overtime_rate_multiplier': data.get('overtime_rate_multiplier', 1.5),
                'weekend_overtime_multiplier': data.get('weekend_overtime_multiplier', 2.0),
                'holiday_overtime_multiplier': data.get('holiday_overtime_multiplier', 2.5),
                'auto_generate_payslips': data.get('auto_generate_payslips', True),
                'require_approval': data.get('require_approval', True)
            }

            if existing_settings:
                # Update existing settings
                effective_from = datetime.strptime(data.get('effective_from'), '%Y-%m-%d').date() if data.get('effective_from') else date.today()
                settings = CompanyPayrollSettings.update_settings(session, company_id, settings_data, effective_from)
            else:
                # Create new settings
                settings_data['company_id'] = company_id
                settings_data['effective_from'] = datetime.strptime(data.get('effective_from'), '%Y-%m-%d').date() if data.get('effective_from') else date.today()
                settings = CompanyPayrollSettings.create_settings(session, **settings_data)

            if not settings:
                return jsonify({"error": "Failed to create/update payroll settings"}), 500

            return jsonify({
                "success": True,
                "settings": settings.to_dict()
            }), 201

    except Exception as e:
        app.logger.error(f"Error creating payroll settings: {e}")
        return jsonify({"error": "Failed to create payroll settings"}), 500

@payroll_settings_api.route('/api/payroll/settings/history', methods=['GET'])
@token_required
def get_payroll_settings_history():
    """Get payroll settings history for a company."""
    try:
        company_id = request.args.get('company_id')
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            settings_history = CompanyPayrollSettings.get_settings_history(session, company_id)

            return jsonify({
                "success": True,
                "settings_history": [s.to_dict() for s in settings_history]
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting settings history: {e}")
        return jsonify({"error": "Failed to get settings history"}), 500

@payroll_settings_api.route('/api/payroll/settings/next-dates', methods=['GET'])
@token_required
def get_next_payroll_dates():
    """Get next payroll cutoff and payment dates."""
    try:
        company_id = request.args.get('company_id')
        from_date_str = request.args.get('from_date')
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        # Parse from_date
        from_date = datetime.strptime(from_date_str, '%Y-%m-%d').date() if from_date_str else date.today()

        with DatabaseConnection.get_session(database_name) as session:
            settings = CompanyPayrollSettings.get_current_settings(session, company_id, from_date)
            
            if not settings:
                return jsonify({"error": "Payroll settings not found"}), 404

            next_cutoff = settings.get_next_payroll_cutoff(from_date)
            next_payment = settings.get_next_payment_date(from_date)

            return jsonify({
                "success": True,
                "next_cutoff_date": next_cutoff.strftime('%Y-%m-%d'),
                "next_payment_date": next_payment.strftime('%Y-%m-%d'),
                "current_settings": settings.to_dict()
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting next payroll dates: {e}")
        return jsonify({"error": "Failed to get next payroll dates"}), 500

@payroll_settings_api.route('/api/payroll/settings/overtime-rate', methods=['POST'])
@token_required
def calculate_overtime_rate():
    """Calculate overtime rate based on base rate and overtime type."""
    try:
        data = request.get_json()
        company_id = data.get('company_id')
        base_rate = data.get('base_rate')
        overtime_type = data.get('overtime_type', 'REGULAR')  # REGULAR, WEEKEND, HOLIDAY
        
        if not company_id or not base_rate:
            return jsonify({"error": "company_id and base_rate are required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            settings = CompanyPayrollSettings.get_current_settings(session, company_id)
            
            if not settings:
                return jsonify({"error": "Payroll settings not found"}), 404

            overtime_rate = settings.calculate_overtime_rate(base_rate, overtime_type)

            return jsonify({
                "success": True,
                "base_rate": float(base_rate),
                "overtime_type": overtime_type,
                "overtime_rate": overtime_rate,
                "multiplier_used": {
                    'REGULAR': float(settings.overtime_rate_multiplier),
                    'WEEKEND': float(settings.weekend_overtime_multiplier),
                    'HOLIDAY': float(settings.holiday_overtime_multiplier)
                }.get(overtime_type, float(settings.overtime_rate_multiplier))
            }), 200

    except Exception as e:
        app.logger.error(f"Error calculating overtime rate: {e}")
        return jsonify({"error": "Failed to calculate overtime rate"}), 500

@payroll_settings_api.route('/api/payroll/settings/default', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def create_default_settings():
    """Create default payroll settings for a company."""
    try:
        data = request.get_json()
        company_id = data.get('company_id')
        
        if not company_id:
            return jsonify({"error": "company_id is required"}), 400

        # Get tenant database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"error": "Company database not found"}), 404

        with DatabaseConnection.get_session(database_name) as session:
            # Check if settings already exist
            existing_settings = CompanyPayrollSettings.get_current_settings(session, company_id)
            if existing_settings:
                return jsonify({"error": "Payroll settings already exist for this company"}), 409

            # Create default settings
            settings = CompanyPayrollSettings.create_default_settings(session, company_id)
            
            if not settings:
                return jsonify({"error": "Failed to create default settings"}), 500

            return jsonify({
                "success": True,
                "message": "Default payroll settings created successfully",
                "settings": settings.to_dict()
            }), 201

    except Exception as e:
        app.logger.error(f"Error creating default settings: {e}")
        return jsonify({"error": "Failed to create default settings"}), 500
