"""Payroll Policy Types Management API for Central Database Administration."""

from flask import Blueprint, request, jsonify, current_app as app, g
from application.Models.payroll_policy_type import PayrollPolicyType
from application.Models.payroll_policy import PayrollPolicy
from application.Models.country import Country
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.database import central_db as db
from datetime import datetime
import uuid

policy_types_api = Blueprint('policy_types_api', __name__)

# ============================================================================
# PAYROLL POLICY TYPES MANAGEMENT ENDPOINTS
# ============================================================================

@policy_types_api.route('/api/payroll/policy-types', methods=['GET'])
@token_required
@roles_required('admin', 'super-admin', 'hr')
def get_all_policy_types():
    """Get all payroll policy types with usage statistics."""
    try:
        policy_types = PayrollPolicyType.get_all_active()
        
        policy_types_data = []
        for policy_type in policy_types:
            policy_type_dict = policy_type.to_dict()
            
            # Add usage statistics
            policies_count = PayrollPolicy.query.filter_by(policy_type_id=policy_type.policy_type_id).count()
            countries_using = db.session.query(PayrollPolicy.country_id).filter_by(
                policy_type_id=policy_type.policy_type_id
            ).distinct().count()
            
            policy_type_dict['usage_stats'] = {
                'total_policies': policies_count,
                'countries_using': countries_using,
                'is_in_use': policies_count > 0
            }
            
            policy_types_data.append(policy_type_dict)
        
        return jsonify({
            "success": True,
            "policy_types": policy_types_data,
            "total_count": len(policy_types_data)
        }), 200
        
    except Exception as e:
        app.logger.error(f"Error getting policy types: {e}")
        return jsonify({"error": "Failed to get policy types"}), 500

@policy_types_api.route('/api/payroll/policy-types', methods=['POST'])
@token_required
@roles_required('admin', 'super-admin')
def create_policy_type():
    """Create a new payroll policy type."""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['name', 'code', 'calculation_method']
        for field in required_fields:
            if not data.get(field):
                return jsonify({"error": f"{field} is required"}), 400
        
        # Validate calculation method
        valid_methods = ['PROGRESSIVE_TAX', 'FLAT_RATE', 'PERCENTAGE', 'FIXED_AMOUNT']
        if data.get('calculation_method') not in valid_methods:
            return jsonify({
                "error": f"Invalid calculation method. Must be one of: {', '.join(valid_methods)}"
            }), 400
        
        # Check if code already exists
        existing_type = PayrollPolicyType.get_by_code(data.get('code').upper())
        if existing_type:
            return jsonify({"error": f"Policy type with code '{data.get('code')}' already exists"}), 409
        
        # Create policy type
        policy_type_data = {
            'name': data.get('name'),
            'code': data.get('code').upper(),
            'description': data.get('description'),
            'calculation_method': data.get('calculation_method'),
            'applies_to': data.get('applies_to', 'ALL_EMPLOYEES'),
            'is_active': data.get('is_active', True)
        }
        
        policy_type = PayrollPolicyType.create_policy_type(**policy_type_data)
        if not policy_type:
            return jsonify({"error": "Failed to create policy type"}), 500
        
        app.logger.info(f"Policy type created: {policy_type.name} ({policy_type.code}) by user {g.user.get('user_id')}")
        
        return jsonify({
            "success": True,
            "message": f"Policy type '{policy_type.name}' created successfully",
            "policy_type": policy_type.to_dict()
        }), 201
        
    except Exception as e:
        app.logger.error(f"Error creating policy type: {e}")
        return jsonify({"error": "Failed to create policy type"}), 500

@policy_types_api.route('/api/payroll/policy-types/<policy_type_id>', methods=['GET'])
@token_required
@roles_required('admin', 'super-admin', 'hr', 'super-admin')
def get_policy_type_by_id(policy_type_id):
    """Get a policy type by its ID with detailed usage information."""
    try:
        policy_type = PayrollPolicyType.get_by_id(policy_type_id)
        if not policy_type:
            return jsonify({"error": "Policy type not found"}), 404
        
        policy_type_dict = policy_type.to_dict()
        
        # Get detailed usage information
        policies = PayrollPolicy.query.filter_by(policy_type_id=policy_type.policy_type_id).all()
        
        countries_usage = {}
        for policy in policies:
            country_id = str(policy.country_id)
            if country_id not in countries_usage:
                countries_usage[country_id] = {
                    'country_name': policy.country.name if policy.country else 'Unknown',
                    'country_code': policy.country.code if policy.country else 'Unknown',
                    'policies_count': 0,
                    'active_policies': 0
                }
            countries_usage[country_id]['policies_count'] += 1
            if policy.is_active:
                countries_usage[country_id]['active_policies'] += 1
        
        policy_type_dict['detailed_usage'] = {
            'total_policies': len(policies),
            'active_policies': len([p for p in policies if p.is_active]),
            'countries_using': list(countries_usage.values()),
            'policies': [
                {
                    'policy_id': str(p.policy_id),
                    'country_name': p.country.name if p.country else 'Unknown',
                    'country_code': p.country.code if p.country else 'Unknown',
                    'effective_from': p.effective_from.isoformat() if p.effective_from else None,
                    'is_active': p.is_active
                } for p in policies
            ]
        }
        
        return jsonify({
            "success": True,
            "policy_type": policy_type_dict
        }), 200
        
    except Exception as e:
        app.logger.error(f"Error getting policy type: {e}")
        return jsonify({"error": "Failed to get policy type"}), 500

@policy_types_api.route('/api/payroll/policy-types/<policy_type_id>', methods=['PATCH'])
@token_required
@roles_required('admin', 'super-admin', 'super-admin')
def update_policy_type(policy_type_id):
    """Update a policy type's details."""
    try:
        policy_type = PayrollPolicyType.get_by_id(policy_type_id)
        if not policy_type:
            return jsonify({"error": "Policy type not found"}), 404
        
        data = request.get_json()
        
        # Check if policy type is in use before allowing certain changes
        policies_count = PayrollPolicy.query.filter_by(policy_type_id=policy_type.policy_type_id).count()
        
        # Validate calculation method change
        if 'calculation_method' in data and policies_count > 0:
            if data['calculation_method'] != policy_type.calculation_method:
                return jsonify({
                    "error": "Cannot change calculation method for policy type that is in use",
                    "policies_using": policies_count
                }), 409
        
        # Update allowed fields
        allowed_fields = ['name', 'description', 'calculation_method', 'is_mandatory', 'is_active']
        for field in allowed_fields:
            if field in data:
                setattr(policy_type, field, data[field])
        
        policy_type.updated_at = db.func.now()
        db.session.commit()
        
        app.logger.info(f"Policy type updated: {policy_type.name} ({policy_type.code}) by user {g.user.get('user_id')}")
        
        return jsonify({
            "success": True,
            "message": f"Policy type '{policy_type.name}' updated successfully",
            "policy_type": policy_type.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        app.logger.error(f"Error updating policy type: {e}")
        return jsonify({"error": "Failed to update policy type"}), 500

@policy_types_api.route('/api/payroll/policy-types/<policy_type_id>', methods=['DELETE'])
@token_required
@roles_required('admin','super-admin')
def delete_policy_type(policy_type_id):
    """Delete a policy type (admin only)."""
    try:
        policy_type = PayrollPolicyType.get_by_id(policy_type_id)
        if not policy_type:
            return jsonify({"error": "Policy type not found"}), 404
        
        # Check if policy type is in use
        policies_count = PayrollPolicy.query.filter_by(policy_type_id=policy_type.policy_type_id).count()
        if policies_count > 0:
            return jsonify({
                "error": "Cannot delete policy type that is in use",
                "policies_using": policies_count
            }), 409
        
        policy_type_name = policy_type.name
        policy_type_code = policy_type.code
        
        db.session.delete(policy_type)
        db.session.commit()
        
        app.logger.warning(f"Policy type deleted: {policy_type_name} ({policy_type_code}) by user {g.user.get('user_id')}")
        
        return jsonify({
            "success": True,
            "message": f"Policy type '{policy_type_name}' deleted successfully"
        }), 200
        
    except Exception as e:
        db.session.rollback()
        app.logger.error(f"Error deleting policy type: {e}")
        return jsonify({"error": "Failed to delete policy type"}), 500

@policy_types_api.route('/api/payroll/policy-types/<policy_type_id>/countries', methods=['GET'])
@token_required
@roles_required('admin', 'super-admin', 'hr', 'super-admin')
def get_countries_using_policy_type(policy_type_id):
    """Get all countries using a specific policy type."""
    try:
        policy_type = PayrollPolicyType.get_by_id(policy_type_id)
        if not policy_type:
            return jsonify({"error": "Policy type not found"}), 404
        
        # Get countries using this policy type
        countries_query = db.session.query(
            PayrollPolicy.country_id,
            db.func.count(PayrollPolicy.policy_id).label('policies_count'),
            db.func.count(db.case((PayrollPolicy.is_active == True, 1))).label('active_policies')
        ).filter_by(policy_type_id=policy_type.policy_type_id).group_by(PayrollPolicy.country_id).all()
        
        countries_data = []
        for country_usage in countries_query:
            country = Country.get_country_by_id(country_usage.country_id)
            if country:
                countries_data.append({
                    'country_id': str(country.country_id),
                    'country_name': country.name,
                    'country_code': country.code,
                    'policies_count': country_usage.policies_count,
                    'active_policies': country_usage.active_policies
                })
        
        return jsonify({
            "success": True,
            "policy_type": {
                "policy_type_id": str(policy_type.policy_type_id),
                "name": policy_type.name,
                "code": policy_type.code
            },
            "countries": countries_data,
            "total_countries": len(countries_data)
        }), 200
        
    except Exception as e:
        app.logger.error(f"Error getting countries for policy type: {e}")
        return jsonify({"error": "Failed to get countries"}), 500
