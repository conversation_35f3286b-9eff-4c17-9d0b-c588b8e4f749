"""Tax Bracket Management API for Central Database Administration."""

from flask import Blueprint, request, jsonify, current_app as app, g
from application.Models.tax_bracket import TaxBracket
from application.Models.payroll_policy import PayrollPolicy
from application.Models.payroll_policy_type import PayrollPolicyType
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.database import central_db as db
from datetime import datetime
import uuid
from decimal import Decimal

tax_brackets_api = Blueprint('tax_brackets_api', __name__)

# ============================================================================
# TAX BRACKET MANAGEMENT ENDPOINTS
# ============================================================================

@tax_brackets_api.route('/api/payroll/policies/<policy_id>/tax-brackets', methods=['GET'])
@token_required
@roles_required('admin', 'super-admin', 'hr')
def get_tax_brackets_for_policy(policy_id):
    """Get all tax brackets for a specific policy."""
    try:
        policy = PayrollPolicy.get_by_id(policy_id)
        if not policy:
            return jsonify({"error": "Policy not found"}), 404
        
        # Verify this is a tax policy
        if policy.policy_type.calculation_method != 'PROGRESSIVE_TAX':
            return jsonify({"error": "This policy does not use progressive tax brackets"}), 400
        
        tax_brackets = TaxBracket.get_by_policy(policy_id)
        
        brackets_data = []
        for bracket in tax_brackets:
            bracket_dict = bracket.to_dict()
            
            # Add calculated range information
            if bracket.max_amount:
                bracket_dict['range_description'] = f"{bracket.min_amount:,.0f} - {bracket.max_amount:,.0f}"
                bracket_dict['range_size'] = float(bracket.max_amount - bracket.min_amount)
            else:
                bracket_dict['range_description'] = f"{bracket.min_amount:,.0f} and above"
                bracket_dict['range_size'] = None
            
            brackets_data.append(bracket_dict)
        
        # Calculate total tax for sample amounts
        sample_calculations = []
        sample_amounts = [100000, 500000, 1000000, 2000000, 5000000]
        
        for amount in sample_amounts:
            total_tax = TaxBracket.calculate_progressive_tax(policy_id, amount)
            effective_rate = (total_tax / amount * 100) if amount > 0 else 0
            
            sample_calculations.append({
                'gross_amount': amount,
                'total_tax': float(total_tax),
                'effective_rate': round(effective_rate, 2),
                'net_amount': amount - float(total_tax)
            })
        
        return jsonify({
            "success": True,
            "policy": {
                "policy_id": str(policy.policy_id),
                "country_name": policy.country.name if policy.country else None,
                "country_code": policy.country.code if policy.country else None,
                "policy_type": policy.policy_type.name if policy.policy_type else None,
                "effective_from": policy.effective_from.isoformat() if policy.effective_from else None
            },
            "tax_brackets": brackets_data,
            "total_brackets": len(brackets_data),
            "sample_calculations": sample_calculations
        }), 200
        
    except Exception as e:
        app.logger.error(f"Error getting tax brackets: {e}")
        return jsonify({"error": "Failed to get tax brackets"}), 500

@tax_brackets_api.route('/api/payroll/policies/<policy_id>/tax-brackets', methods=['POST'])
@token_required
@roles_required('admin', 'super-admin')
def create_tax_brackets(policy_id):
    """Create tax brackets for a policy (bulk operation)."""
    try:
        policy = PayrollPolicy.get_by_id(policy_id)
        if not policy:
            return jsonify({"error": "Policy not found"}), 404
        
        # Verify this is a tax policy
        if policy.policy_type.calculation_method != 'PROGRESSIVE_TAX':
            return jsonify({"error": "This policy does not use progressive tax brackets"}), 400
        
        data = request.get_json()
        brackets_data = data.get('tax_brackets', [])
        
        if not brackets_data:
            return jsonify({"error": "tax_brackets array is required"}), 400
        
        # Validate brackets data
        for i, bracket in enumerate(brackets_data):
            required_fields = ['bracket_order', 'min_amount', 'tax_rate']
            for field in required_fields:
                if field not in bracket:
                    return jsonify({"error": f"Bracket {i+1}: {field} is required"}), 400
            
            # Validate tax rate
            if not 0 <= bracket['tax_rate'] <= 1:
                return jsonify({"error": f"Bracket {i+1}: tax_rate must be between 0 and 1"}), 400
        
        # Sort brackets by order
        brackets_data.sort(key=lambda x: x['bracket_order'])
        
        # Validate bracket progression
        for i, bracket in enumerate(brackets_data):
            if i > 0:
                prev_bracket = brackets_data[i-1]
                if bracket['min_amount'] <= prev_bracket['min_amount']:
                    return jsonify({
                        "error": f"Bracket {i+1}: min_amount must be greater than previous bracket"
                    }), 400
                
                # Set max_amount for previous bracket
                if 'max_amount' not in prev_bracket or prev_bracket['max_amount'] is None:
                    prev_bracket['max_amount'] = bracket['min_amount']
        
        # Delete existing brackets for this policy
        existing_brackets = TaxBracket.get_by_policy(policy_id)
        for bracket in existing_brackets:
            db.session.delete(bracket)
        
        # Create new brackets
        created_brackets = []
        for bracket_data in brackets_data:
            bracket = TaxBracket(
                policy_id=policy_id,
                bracket_order=bracket_data['bracket_order'],
                min_amount=Decimal(str(bracket_data['min_amount'])),
                max_amount=Decimal(str(bracket_data['max_amount'])) if bracket_data.get('max_amount') else None,
                tax_rate=Decimal(str(bracket_data['tax_rate'])),
                effective_from=policy.effective_from,
                effective_to=policy.effective_to
            )
            db.session.add(bracket)
            created_brackets.append(bracket)
        
        db.session.commit()
        
        app.logger.info(f"Tax brackets created for policy {policy_id}: {len(created_brackets)} brackets by user {g.user.get('user_id')}")
        
        return jsonify({
            "success": True,
            "message": f"{len(created_brackets)} tax brackets created successfully",
            "tax_brackets": [bracket.to_dict() for bracket in created_brackets]
        }), 201
        
    except Exception as e:
        db.session.rollback()
        app.logger.error(f"Error creating tax brackets: {e}")
        return jsonify({"error": "Failed to create tax brackets"}), 500

@tax_brackets_api.route('/api/payroll/policies/<policy_id>/tax-brackets/<bracket_id>', methods=['PUT'])
@token_required
@roles_required('admin', 'super-admin')
def update_tax_bracket(policy_id, bracket_id):
    """Update a specific tax bracket."""
    try:
        bracket = TaxBracket.get_by_id(bracket_id)
        if not bracket or str(bracket.policy_id) != policy_id:
            return jsonify({"error": "Tax bracket not found"}), 404
        
        data = request.get_json()
        
        # Update allowed fields
        if 'min_amount' in data:
            bracket.min_amount = Decimal(str(data['min_amount']))
        if 'max_amount' in data:
            bracket.max_amount = Decimal(str(data['max_amount'])) if data['max_amount'] else None
        if 'tax_rate' in data:
            if not 0 <= data['tax_rate'] <= 1:
                return jsonify({"error": "tax_rate must be between 0 and 1"}), 400
            bracket.tax_rate = Decimal(str(data['tax_rate']))
        
        bracket.updated_at = db.func.now()
        db.session.commit()
        
        app.logger.info(f"Tax bracket updated: {bracket_id} by user {g.user.get('user_id')}")
        
        return jsonify({
            "success": True,
            "message": "Tax bracket updated successfully",
            "tax_bracket": bracket.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        app.logger.error(f"Error updating tax bracket: {e}")
        return jsonify({"error": "Failed to update tax bracket"}), 500

@tax_brackets_api.route('/api/payroll/policies/<policy_id>/tax-brackets/<bracket_id>', methods=['DELETE'])
@token_required
@roles_required('admin', 'super-admin')
def delete_tax_bracket(policy_id, bracket_id):
    """Delete a specific tax bracket."""
    try:
        bracket = TaxBracket.get_by_id(bracket_id)
        if not bracket or str(bracket.policy_id) != policy_id:
            return jsonify({"error": "Tax bracket not found"}), 404
        
        bracket_order = bracket.bracket_order
        db.session.delete(bracket)
        db.session.commit()
        
        app.logger.info(f"Tax bracket deleted: {bracket_id} (order {bracket_order}) by user {g.user.get('user_id')}")
        
        return jsonify({
            "success": True,
            "message": "Tax bracket deleted successfully"
        }), 200
        
    except Exception as e:
        db.session.rollback()
        app.logger.error(f"Error deleting tax bracket: {e}")
        return jsonify({"error": "Failed to delete tax bracket"}), 500

@tax_brackets_api.route('/api/payroll/policies/<policy_id>/tax-brackets/bulk-update', methods=['PUT'])
@token_required
@roles_required('admin', 'super-admin')
def bulk_update_tax_brackets(policy_id):
    """Bulk update all tax brackets for a policy."""
    try:
        policy = PayrollPolicy.get_by_id(policy_id)
        if not policy:
            return jsonify({"error": "Policy not found"}), 404
        
        data = request.get_json()
        brackets_data = data.get('tax_brackets', [])
        
        if not brackets_data:
            return jsonify({"error": "tax_brackets array is required"}), 400
        
        # Delete all existing brackets
        existing_brackets = TaxBracket.get_by_policy(policy_id)
        for bracket in existing_brackets:
            db.session.delete(bracket)
        
        # Create new brackets (reuse create logic)
        brackets_data.sort(key=lambda x: x['bracket_order'])
        
        created_brackets = []
        for bracket_data in brackets_data:
            bracket = TaxBracket(
                policy_id=policy_id,
                bracket_order=bracket_data['bracket_order'],
                min_amount=Decimal(str(bracket_data['min_amount'])),
                max_amount=Decimal(str(bracket_data['max_amount'])) if bracket_data.get('max_amount') else None,
                tax_rate=Decimal(str(bracket_data['tax_rate'])),
                effective_from=policy.effective_from,
                effective_to=policy.effective_to
            )
            db.session.add(bracket)
            created_brackets.append(bracket)
        
        db.session.commit()
        
        app.logger.info(f"Tax brackets bulk updated for policy {policy_id}: {len(created_brackets)} brackets by user {g.user.get('user_id')}")
        
        return jsonify({
            "success": True,
            "message": f"{len(created_brackets)} tax brackets updated successfully",
            "tax_brackets": [bracket.to_dict() for bracket in created_brackets]
        }), 200
        
    except Exception as e:
        db.session.rollback()
        app.logger.error(f"Error bulk updating tax brackets: {e}")
        return jsonify({"error": "Failed to update tax brackets"}), 500

@tax_brackets_api.route('/api/payroll/tax-brackets/calculate-test', methods=['POST'])
@token_required
@roles_required('admin', 'super-admin', 'hr')
def test_tax_calculation():
    """Test tax calculation for given amounts and policy."""
    try:
        data = request.get_json()
        
        policy_id = data.get('policy_id')
        amounts = data.get('amounts', [])
        
        if not policy_id:
            return jsonify({"error": "policy_id is required"}), 400
        
        if not amounts:
            return jsonify({"error": "amounts array is required"}), 400
        
        policy = PayrollPolicy.get_by_id(policy_id)
        if not policy:
            return jsonify({"error": "Policy not found"}), 404
        
        calculations = []
        for amount in amounts:
            try:
                amount = float(amount)
                total_tax = TaxBracket.calculate_progressive_tax(policy_id, amount)
                effective_rate = (total_tax / amount * 100) if amount > 0 else 0
                
                calculations.append({
                    'gross_amount': amount,
                    'total_tax': float(total_tax),
                    'effective_rate': round(effective_rate, 2),
                    'net_amount': amount - float(total_tax)
                })
            except (ValueError, TypeError):
                calculations.append({
                    'gross_amount': amount,
                    'error': 'Invalid amount'
                })
        
        return jsonify({
            "success": True,
            "policy": {
                "policy_id": str(policy.policy_id),
                "country_name": policy.country.name if policy.country else None,
                "policy_type": policy.policy_type.name if policy.policy_type else None
            },
            "calculations": calculations
        }), 200
        
    except Exception as e:
        app.logger.error(f"Error testing tax calculation: {e}")
        return jsonify({"error": "Failed to calculate tax"}), 500
