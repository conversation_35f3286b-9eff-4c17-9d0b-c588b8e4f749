from flask import Blueprint, request, jsonify, current_app
from application.database import db
from application.Models.performance.development_plan import DevelopmentPlan, DevelopmentAction
from datetime import datetime, date
import json

development_plans_bp = Blueprint('development_plans', __name__)


@development_plans_bp.route('/api/performance/development-plans', methods=['POST'])
def create_development_plan():
    """Create a new development plan."""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['employee_id', 'plan_name', 'target_completion_date']
        for field in required_fields:
            if field not in data:
                return jsonify({"success": False, "message": f"Missing required field: {field}"}), 400
        
        # Convert date strings to date objects
        date_fields = ['start_date', 'target_completion_date', 'last_review_date', 'next_review_date', 'role_change_date']
        for field in date_fields:
            if field in data and data[field]:
                if isinstance(data[field], str):
                    data[field] = datetime.strptime(data[field], '%Y-%m-%d').date()
        
        plan, error = DevelopmentPlan.create_plan(db.session, **data)
        if error:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Development plan created successfully",
            "plan": plan.to_dict()
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"Error creating development plan: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@development_plans_bp.route('/api/performance/development-plans', methods=['GET'])
def get_development_plans():
    """Get development plans for an employee."""
    try:
        employee_id = request.args.get('employee_id')
        status = request.args.get('status')
        
        if not employee_id:
            return jsonify({"success": False, "message": "employee_id is required"}), 400
        
        status_list = status.split(',') if status else None
        plans = DevelopmentPlan.get_plans_by_employee(db.session, employee_id, status_list)
        
        return jsonify({
            "success": True,
            "plans": [plan.to_dict() for plan in plans]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting development plans: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@development_plans_bp.route('/api/performance/development-plans/<plan_id>', methods=['GET'])
def get_development_plan(plan_id):
    """Get a specific development plan."""
    try:
        plan = DevelopmentPlan.get_plan_by_id(db.session, plan_id)
        if not plan:
            return jsonify({"success": False, "message": "Development plan not found"}), 404
        
        return jsonify({
            "success": True,
            "plan": plan.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting development plan: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@development_plans_bp.route('/api/performance/development-plans/<plan_id>', methods=['PUT'])
def update_development_plan(plan_id):
    """Update a development plan."""
    try:
        data = request.get_json()
        
        plan = DevelopmentPlan.get_plan_by_id(db.session, plan_id)
        if not plan:
            return jsonify({"success": False, "message": "Development plan not found"}), 404
        
        # Convert date strings to date objects
        date_fields = ['start_date', 'target_completion_date', 'last_review_date', 'next_review_date', 'role_change_date']
        for field in date_fields:
            if field in data and data[field]:
                if isinstance(data[field], str):
                    data[field] = datetime.strptime(data[field], '%Y-%m-%d').date()
        
        # Update plan fields
        updatable_fields = [
            'plan_name', 'description', 'plan_type', 'start_date', 'target_completion_date',
            'primary_focus_area', 'current_role', 'target_role', 'career_path', 'promotion_timeline',
            'success_criteria', 'manager_support_needed', 'mentor_assigned', 'budget_allocated',
            'time_allocation', 'last_review_date', 'next_review_date', 'review_frequency',
            'completion_summary', 'promotion_achieved', 'role_change_date'
        ]
        
        for field in updatable_fields:
            if field in data:
                setattr(plan, field, data[field])
        
        # Handle JSON fields
        json_fields = ['secondary_focus_areas', 'skill_gaps_identified', 'competency_targets', 
                      'development_objectives', 'skills_developed', 'certifications_earned']
        
        for field in json_fields:
            if field in data:
                if isinstance(data[field], (list, dict)):
                    setattr(plan, field, json.dumps(data[field]))
                else:
                    setattr(plan, field, data[field])
        
        plan.updated_at = datetime.now()
        db.session.commit()
        
        return jsonify({
            "success": True,
            "message": "Development plan updated successfully",
            "plan": plan.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error updating development plan: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@development_plans_bp.route('/api/performance/development-plans/<plan_id>/approve', methods=['PUT'])
def approve_development_plan(plan_id):
    """Approve a development plan."""
    try:
        data = request.get_json()
        approved_by = data.get('approved_by')
        approval_comments = data.get('approval_comments')
        
        if not approved_by:
            return jsonify({"success": False, "message": "approved_by is required"}), 400
        
        plan = DevelopmentPlan.get_plan_by_id(db.session, plan_id)
        if not plan:
            return jsonify({"success": False, "message": "Development plan not found"}), 404
        
        success, error = plan.approve_plan(db.session, approved_by, approval_comments)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Development plan approved successfully",
            "plan": plan.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error approving development plan: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@development_plans_bp.route('/api/performance/development-plans/<plan_id>/start', methods=['PUT'])
def start_development_plan(plan_id):
    """Start a development plan."""
    try:
        plan = DevelopmentPlan.get_plan_by_id(db.session, plan_id)
        if not plan:
            return jsonify({"success": False, "message": "Development plan not found"}), 404
        
        success, error = plan.start_plan(db.session)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Development plan started successfully",
            "plan": plan.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error starting development plan: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@development_plans_bp.route('/api/performance/development-plans/<plan_id>/complete', methods=['PUT'])
def complete_development_plan(plan_id):
    """Complete a development plan."""
    try:
        data = request.get_json() or {}
        completion_summary = data.get('completion_summary')
        completed_by = data.get('completed_by')
        
        plan = DevelopmentPlan.get_plan_by_id(db.session, plan_id)
        if not plan:
            return jsonify({"success": False, "message": "Development plan not found"}), 404
        
        success, error = plan.complete_plan(db.session, completion_summary, completed_by)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Development plan completed successfully",
            "plan": plan.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error completing development plan: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@development_plans_bp.route('/api/performance/development-plans/<plan_id>/actions', methods=['POST'])
def add_development_action(plan_id):
    """Add a development action to a plan."""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['action_title', 'action_type', 'target_completion_date']
        for field in required_fields:
            if field not in data:
                return jsonify({"success": False, "message": f"Missing required field: {field}"}), 400
        
        plan = DevelopmentPlan.get_plan_by_id(db.session, plan_id)
        if not plan:
            return jsonify({"success": False, "message": "Development plan not found"}), 404
        
        # Convert date strings to date objects
        date_fields = ['start_date', 'target_completion_date']
        for field in date_fields:
            if field in data and data[field]:
                if isinstance(data[field], str):
                    data[field] = datetime.strptime(data[field], '%Y-%m-%d').date()
        
        action, error = plan.add_development_action(db.session, data)
        if error:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Development action added successfully",
            "action": action.to_dict()
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"Error adding development action: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@development_plans_bp.route('/api/performance/development-plans/<plan_id>/actions', methods=['GET'])
def get_development_actions(plan_id):
    """Get all development actions for a plan."""
    try:
        plan = DevelopmentPlan.get_plan_by_id(db.session, plan_id)
        if not plan:
            return jsonify({"success": False, "message": "Development plan not found"}), 404
        
        actions = plan.development_actions.all()
        
        return jsonify({
            "success": True,
            "actions": [action.to_dict() for action in actions]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting development actions: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@development_plans_bp.route('/api/performance/development-actions/<action_id>', methods=['GET'])
def get_development_action(action_id):
    """Get a specific development action."""
    try:
        action = db.session.query(DevelopmentAction).filter_by(action_id=action_id).first()
        if not action:
            return jsonify({"success": False, "message": "Development action not found"}), 404
        
        return jsonify({
            "success": True,
            "action": action.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting development action: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@development_plans_bp.route('/api/performance/development-actions/<action_id>', methods=['PUT'])
def update_development_action(action_id):
    """Update a development action."""
    try:
        data = request.get_json()
        
        action = db.session.query(DevelopmentAction).filter_by(action_id=action_id).first()
        if not action:
            return jsonify({"success": False, "message": "Development action not found"}), 404
        
        # Convert date strings to date objects
        date_fields = ['start_date', 'target_completion_date']
        for field in date_fields:
            if field in data and data[field]:
                if isinstance(data[field], str):
                    data[field] = datetime.strptime(data[field], '%Y-%m-%d').date()
        
        # Update action fields
        updatable_fields = [
            'action_title', 'action_description', 'action_type', 'action_category',
            'start_date', 'target_completion_date', 'provider', 'cost', 'time_required',
            'prerequisites', 'status', 'progress_percentage', 'completion_evidence',
            'weight', 'priority', 'success_criteria', 'actual_outcomes',
            'requires_manager_approval'
        ]
        
        for field in updatable_fields:
            if field in data:
                setattr(action, field, data[field])
        
        # Handle JSON fields
        if 'skills_targeted' in data:
            action.skills_targeted = json.dumps(data['skills_targeted']) if data['skills_targeted'] else None
        
        if 'competencies_addressed' in data:
            action.competencies_addressed = json.dumps(data['competencies_addressed']) if data['competencies_addressed'] else None
        
        action.updated_at = datetime.now()
        db.session.commit()
        
        return jsonify({
            "success": True,
            "message": "Development action updated successfully",
            "action": action.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error updating development action: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@development_plans_bp.route('/api/performance/development-actions/<action_id>/complete', methods=['PUT'])
def complete_development_action(action_id):
    """Complete a development action."""
    try:
        data = request.get_json() or {}
        completion_evidence = data.get('completion_evidence')
        actual_outcomes = data.get('actual_outcomes')
        
        action = db.session.query(DevelopmentAction).filter_by(action_id=action_id).first()
        if not action:
            return jsonify({"success": False, "message": "Development action not found"}), 404
        
        success, error = action.complete_action(db.session, completion_evidence, actual_outcomes)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Development action completed successfully",
            "action": action.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error completing development action: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@development_plans_bp.route('/api/performance/development-actions/<action_id>/approve', methods=['PUT'])
def approve_development_action(action_id):
    """Approve a development action (manager approval)."""
    try:
        data = request.get_json() or {}
        
        action = db.session.query(DevelopmentAction).filter_by(action_id=action_id).first()
        if not action:
            return jsonify({"success": False, "message": "Development action not found"}), 404
        
        action.manager_approved = True
        action.approval_date = datetime.now()
        action.updated_at = datetime.now()
        
        db.session.commit()
        
        return jsonify({
            "success": True,
            "message": "Development action approved successfully",
            "action": action.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error approving development action: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500
