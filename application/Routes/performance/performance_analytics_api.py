from flask import Blueprint, request, jsonify, current_app
from application.database import db
from application.Models.performance.performance_review_cycle import PerformanceReviewCycle
from application.Models.performance.performance_goal import PerformanceGoal
from application.Models.performance.performance_review import PerformanceReview
from application.Models.performance.performance_feedback import PerformanceFeedback
from application.Models.performance.development_plan import DevelopmentPlan
from application.Models.performance.goal_progress_update import GoalProgressUpdate
from datetime import datetime, date, timedelta
from decimal import Decimal
import json

performance_analytics_bp = Blueprint('performance_analytics', __name__)


@performance_analytics_bp.route('/api/performance/analytics/overview', methods=['GET'])
def get_performance_overview():
    """Get comprehensive performance overview analytics."""
    try:
        company_id = request.args.get('company_id')
        period = request.args.get('period', 'month')  # month, quarter, year
        year = request.args.get('year', date.today().year, type=int)
        month = request.args.get('month', date.today().month, type=int)
        quarter = request.args.get('quarter', type=int)
        department = request.args.get('department')
        
        if not company_id:
            return jsonify({"success": False, "message": "company_id is required"}), 400
        
        # Calculate date range based on period
        if period == 'month':
            start_date = date(year, month, 1)
            if month == 12:
                end_date = date(year + 1, 1, 1) - timedelta(days=1)
            else:
                end_date = date(year, month + 1, 1) - timedelta(days=1)
        elif period == 'quarter':
            if not quarter:
                quarter = (month - 1) // 3 + 1
            start_month = (quarter - 1) * 3 + 1
            start_date = date(year, start_month, 1)
            end_month = start_month + 2
            if end_month == 12:
                end_date = date(year + 1, 1, 1) - timedelta(days=1)
            else:
                end_date = date(year, end_month + 1, 1) - timedelta(days=1)
        elif period == 'year':
            start_date = date(year, 1, 1)
            end_date = date(year, 12, 31)
        else:
            return jsonify({"success": False, "message": "Invalid period. Use 'month', 'quarter', or 'year'"}), 400
        
        # Get cycles in the period
        cycles = db.session.query(PerformanceReviewCycle).filter(
            PerformanceReviewCycle.company_id == company_id,
            PerformanceReviewCycle.start_date <= end_date,
            PerformanceReviewCycle.end_date >= start_date
        ).all()
        
        cycle_ids = [str(cycle.cycle_id) for cycle in cycles]
        
        # Overall statistics
        total_goals = db.session.query(PerformanceGoal).filter(
            PerformanceGoal.cycle_id.in_(cycle_ids)
        ).count()
        
        completed_goals = db.session.query(PerformanceGoal).filter(
            PerformanceGoal.cycle_id.in_(cycle_ids),
            PerformanceGoal.status == 'COMPLETED'
        ).count()
        
        total_reviews = db.session.query(PerformanceReview).filter(
            PerformanceReview.cycle_id.in_(cycle_ids)
        ).count()
        
        completed_reviews = db.session.query(PerformanceReview).filter(
            PerformanceReview.cycle_id.in_(cycle_ids),
            PerformanceReview.status == 'COMPLETED'
        ).count()
        
        # Goal completion rate
        goal_completion_rate = round(completed_goals / total_goals * 100, 2) if total_goals > 0 else 0
        
        # Review completion rate
        review_completion_rate = round(completed_reviews / total_reviews * 100, 2) if total_reviews > 0 else 0
        
        # Average goal progress
        goal_progress_query = db.session.query(PerformanceGoal.completion_percentage).filter(
            PerformanceGoal.cycle_id.in_(cycle_ids),
            PerformanceGoal.completion_percentage.isnot(None)
        )
        
        goal_progress_values = [float(g.completion_percentage) for g in goal_progress_query.all()]
        avg_goal_progress = round(sum(goal_progress_values) / len(goal_progress_values), 2) if goal_progress_values else 0
        
        # Average review ratings
        review_ratings_query = db.session.query(PerformanceReview.overall_rating).filter(
            PerformanceReview.cycle_id.in_(cycle_ids),
            PerformanceReview.overall_rating.isnot(None)
        )
        
        review_ratings = [float(r.overall_rating) for r in review_ratings_query.all()]
        avg_review_rating = round(sum(review_ratings) / len(review_ratings), 2) if review_ratings else 0
        
        # Cycle breakdown
        cycle_breakdown = []
        for cycle in cycles:
            cycle_stats = cycle.get_completion_stats(db.session)
            cycle_breakdown.append({
                "cycle_id": str(cycle.cycle_id),
                "cycle_name": cycle.name,
                "cycle_type": cycle.cycle_type,
                "status": cycle.status,
                "completion_percentage": float(cycle.completion_percentage) if cycle.completion_percentage else 0,
                "total_reviews": cycle_stats["total_reviews"],
                "completed_reviews": cycle_stats["completed_reviews"],
                "completion_rate": cycle_stats["completion_rate"]
            })
        
        # Department breakdown (if department filter not applied)
        department_breakdown = []
        if not department:
            # This would require joining with employee table to get department info
            # For now, return empty array
            pass
        
        # Goal statistics by status
        goal_status_stats = {}
        goal_statuses = db.session.query(
            PerformanceGoal.status,
            db.func.count(PerformanceGoal.goal_id).label('count')
        ).filter(
            PerformanceGoal.cycle_id.in_(cycle_ids)
        ).group_by(PerformanceGoal.status).all()
        
        for status, count in goal_statuses:
            goal_status_stats[status] = count
        
        # Review statistics by status
        review_status_stats = {}
        review_statuses = db.session.query(
            PerformanceReview.status,
            db.func.count(PerformanceReview.review_id).label('count')
        ).filter(
            PerformanceReview.cycle_id.in_(cycle_ids)
        ).group_by(PerformanceReview.status).all()
        
        for status, count in review_statuses:
            review_status_stats[status] = count
        
        # Development plans statistics
        total_dev_plans = db.session.query(DevelopmentPlan).filter(
            DevelopmentPlan.cycle_id.in_(cycle_ids)
        ).count()
        
        completed_dev_plans = db.session.query(DevelopmentPlan).filter(
            DevelopmentPlan.cycle_id.in_(cycle_ids),
            DevelopmentPlan.status == 'COMPLETED'
        ).count()
        
        dev_plan_completion_rate = round(completed_dev_plans / total_dev_plans * 100, 2) if total_dev_plans > 0 else 0
        
        # Feedback statistics
        feedback_stats = PerformanceFeedback.get_feedback_statistics(db.session, cycle_id=cycle_ids[0] if cycle_ids else None)
        
        response_data = {
            "period_info": {
                "period": period,
                "year": year,
                "month": month if period == 'month' else None,
                "quarter": quarter if period == 'quarter' else None,
                "start_date": start_date.strftime('%Y-%m-%d'),
                "end_date": end_date.strftime('%Y-%m-%d')
            },
            "overall_statistics": {
                "total_goals": total_goals,
                "completed_goals": completed_goals,
                "goal_completion_rate": goal_completion_rate,
                "average_goal_progress": avg_goal_progress,
                "total_reviews": total_reviews,
                "completed_reviews": completed_reviews,
                "review_completion_rate": review_completion_rate,
                "average_review_rating": avg_review_rating,
                "total_development_plans": total_dev_plans,
                "completed_development_plans": completed_dev_plans,
                "development_plan_completion_rate": dev_plan_completion_rate
            },
            "cycle_breakdown": cycle_breakdown,
            "department_breakdown": department_breakdown,
            "goal_statistics": {
                "by_status": goal_status_stats,
                "total_count": total_goals,
                "completion_rate": goal_completion_rate
            },
            "review_statistics": {
                "by_status": review_status_stats,
                "total_count": total_reviews,
                "completion_rate": review_completion_rate,
                "average_rating": avg_review_rating
            },
            "feedback_statistics": feedback_stats
        }
        
        return jsonify({
            "success": True,
            "data": response_data
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting performance overview: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_analytics_bp.route('/api/performance/analytics/trends', methods=['GET'])
def get_performance_trends():
    """Get performance trends over time."""
    try:
        company_id = request.args.get('company_id')
        period = request.args.get('period', 'monthly')  # weekly, monthly, quarterly
        months_back = request.args.get('months_back', 6, type=int)
        
        if not company_id:
            return jsonify({"success": False, "message": "company_id is required"}), 400
        
        # Calculate date range
        end_date = date.today()
        start_date = end_date - timedelta(days=months_back * 30)
        
        # Get cycles in the period
        cycles = db.session.query(PerformanceReviewCycle).filter(
            PerformanceReviewCycle.company_id == company_id,
            PerformanceReviewCycle.start_date >= start_date,
            PerformanceReviewCycle.end_date <= end_date
        ).order_by(PerformanceReviewCycle.start_date).all()
        
        trends = []
        
        if period == 'monthly':
            # Group by month
            current_date = start_date.replace(day=1)
            while current_date <= end_date:
                month_end = (current_date.replace(month=current_date.month + 1, day=1) - timedelta(days=1)) if current_date.month < 12 else current_date.replace(year=current_date.year + 1, month=1, day=1) - timedelta(days=1)
                
                # Get cycles for this month
                month_cycles = [c for c in cycles if c.start_date <= month_end and c.end_date >= current_date]
                cycle_ids = [str(c.cycle_id) for c in month_cycles]
                
                if cycle_ids:
                    # Calculate metrics for this month
                    total_goals = db.session.query(PerformanceGoal).filter(
                        PerformanceGoal.cycle_id.in_(cycle_ids)
                    ).count()
                    
                    completed_goals = db.session.query(PerformanceGoal).filter(
                        PerformanceGoal.cycle_id.in_(cycle_ids),
                        PerformanceGoal.status == 'COMPLETED'
                    ).count()
                    
                    total_reviews = db.session.query(PerformanceReview).filter(
                        PerformanceReview.cycle_id.in_(cycle_ids)
                    ).count()
                    
                    completed_reviews = db.session.query(PerformanceReview).filter(
                        PerformanceReview.cycle_id.in_(cycle_ids),
                        PerformanceReview.status == 'COMPLETED'
                    ).count()
                    
                    # Average ratings
                    review_ratings = db.session.query(PerformanceReview.overall_rating).filter(
                        PerformanceReview.cycle_id.in_(cycle_ids),
                        PerformanceReview.overall_rating.isnot(None)
                    ).all()
                    
                    avg_rating = round(sum(float(r.overall_rating) for r in review_ratings) / len(review_ratings), 2) if review_ratings else 0
                    
                    trends.append({
                        "period": current_date.strftime('%Y-%m'),
                        "period_name": current_date.strftime('%B %Y'),
                        "total_goals": total_goals,
                        "completed_goals": completed_goals,
                        "goal_completion_rate": round(completed_goals / total_goals * 100, 2) if total_goals > 0 else 0,
                        "total_reviews": total_reviews,
                        "completed_reviews": completed_reviews,
                        "review_completion_rate": round(completed_reviews / total_reviews * 100, 2) if total_reviews > 0 else 0,
                        "average_review_rating": avg_rating
                    })
                
                # Move to next month
                if current_date.month == 12:
                    current_date = current_date.replace(year=current_date.year + 1, month=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 1)
        
        return jsonify({
            "success": True,
            "data": {
                "period_type": period,
                "months_analyzed": months_back,
                "start_date": start_date.strftime('%Y-%m-%d'),
                "end_date": end_date.strftime('%Y-%m-%d'),
                "trends": trends
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting performance trends: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_analytics_bp.route('/api/performance/analytics/performance', methods=['GET'])
def get_performance_metrics():
    """Get detailed performance metrics and bottleneck analysis."""
    try:
        company_id = request.args.get('company_id')
        cycle_id = request.args.get('cycle_id')
        department = request.args.get('department')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        if not company_id:
            return jsonify({"success": False, "message": "company_id is required"}), 400
        
        # Build base query
        query_filters = [PerformanceReviewCycle.company_id == company_id]
        
        if cycle_id:
            query_filters.append(PerformanceReviewCycle.cycle_id == cycle_id)
        
        if date_from:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            query_filters.append(PerformanceReviewCycle.start_date >= date_from_obj)
        
        if date_to:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            query_filters.append(PerformanceReviewCycle.end_date <= date_to_obj)
        
        cycles = db.session.query(PerformanceReviewCycle).filter(*query_filters).all()
        cycle_ids = [str(c.cycle_id) for c in cycles]
        
        if not cycle_ids:
            return jsonify({
                "success": True,
                "data": {
                    "filter_info": {
                        "company_id": company_id,
                        "cycle_id": cycle_id,
                        "department": department,
                        "date_from": date_from,
                        "date_to": date_to,
                        "cycles_analyzed": 0
                    },
                    "performance_metrics": {},
                    "bottleneck_analysis": []
                }
            }), 200
        
        # Performance metrics
        goals = db.session.query(PerformanceGoal).filter(
            PerformanceGoal.cycle_id.in_(cycle_ids)
        ).all()
        
        reviews = db.session.query(PerformanceReview).filter(
            PerformanceReview.cycle_id.in_(cycle_ids)
        ).all()
        
        # Goal performance metrics
        total_goals = len(goals)
        completed_goals = len([g for g in goals if g.status == 'COMPLETED'])
        overdue_goals = len([g for g in goals if g.is_overdue()])
        on_track_goals = len([g for g in goals if g.is_on_track()])
        
        # Review performance metrics
        total_reviews = len(reviews)
        completed_reviews = len([r for r in reviews if r.status == 'COMPLETED'])
        overdue_reviews = len([r for r in reviews if r.is_overdue()])
        
        # Average completion times
        completed_goal_times = []
        for goal in goals:
            if goal.status == 'COMPLETED' and goal.start_date and goal.actual_completion_date:
                days = (goal.actual_completion_date - goal.start_date).days
                completed_goal_times.append(days)
        
        avg_goal_completion_days = round(sum(completed_goal_times) / len(completed_goal_times), 1) if completed_goal_times else 0
        
        # Bottleneck analysis
        bottlenecks = []
        
        # Goal type bottlenecks
        goal_types = {}
        for goal in goals:
            goal_type = goal.goal_type or 'UNKNOWN'
            if goal_type not in goal_types:
                goal_types[goal_type] = {'total': 0, 'completed': 0, 'overdue': 0}
            
            goal_types[goal_type]['total'] += 1
            if goal.status == 'COMPLETED':
                goal_types[goal_type]['completed'] += 1
            if goal.is_overdue():
                goal_types[goal_type]['overdue'] += 1
        
        for goal_type, stats in goal_types.items():
            completion_rate = round(stats['completed'] / stats['total'] * 100, 2) if stats['total'] > 0 else 0
            overdue_rate = round(stats['overdue'] / stats['total'] * 100, 2) if stats['total'] > 0 else 0
            
            # Calculate bottleneck score (higher = more problematic)
            bottleneck_score = (100 - completion_rate) + (overdue_rate * 2)
            
            bottlenecks.append({
                "type": "GOAL_TYPE",
                "category": goal_type,
                "total_items": stats['total'],
                "completed_items": stats['completed'],
                "overdue_items": stats['overdue'],
                "completion_rate": completion_rate,
                "overdue_rate": overdue_rate,
                "bottleneck_score": round(bottleneck_score, 2)
            })
        
        # Review type bottlenecks
        review_types = {}
        for review in reviews:
            review_type = review.review_type or 'UNKNOWN'
            if review_type not in review_types:
                review_types[review_type] = {'total': 0, 'completed': 0, 'overdue': 0}
            
            review_types[review_type]['total'] += 1
            if review.status == 'COMPLETED':
                review_types[review_type]['completed'] += 1
            if review.is_overdue():
                review_types[review_type]['overdue'] += 1
        
        for review_type, stats in review_types.items():
            completion_rate = round(stats['completed'] / stats['total'] * 100, 2) if stats['total'] > 0 else 0
            overdue_rate = round(stats['overdue'] / stats['total'] * 100, 2) if stats['total'] > 0 else 0
            
            bottleneck_score = (100 - completion_rate) + (overdue_rate * 2)
            
            bottlenecks.append({
                "type": "REVIEW_TYPE",
                "category": review_type,
                "total_items": stats['total'],
                "completed_items": stats['completed'],
                "overdue_items": stats['overdue'],
                "completion_rate": completion_rate,
                "overdue_rate": overdue_rate,
                "bottleneck_score": round(bottleneck_score, 2)
            })
        
        # Sort bottlenecks by score (highest first)
        bottlenecks.sort(key=lambda x: x['bottleneck_score'], reverse=True)
        
        response_data = {
            "filter_info": {
                "company_id": company_id,
                "cycle_id": cycle_id,
                "department": department,
                "date_from": date_from,
                "date_to": date_to,
                "cycles_analyzed": len(cycles)
            },
            "performance_metrics": {
                "goals": {
                    "total_goals": total_goals,
                    "completed_goals": completed_goals,
                    "overdue_goals": overdue_goals,
                    "on_track_goals": on_track_goals,
                    "completion_rate": round(completed_goals / total_goals * 100, 2) if total_goals > 0 else 0,
                    "on_track_rate": round(on_track_goals / total_goals * 100, 2) if total_goals > 0 else 0,
                    "average_completion_days": avg_goal_completion_days
                },
                "reviews": {
                    "total_reviews": total_reviews,
                    "completed_reviews": completed_reviews,
                    "overdue_reviews": overdue_reviews,
                    "completion_rate": round(completed_reviews / total_reviews * 100, 2) if total_reviews > 0 else 0,
                    "overdue_rate": round(overdue_reviews / total_reviews * 100, 2) if total_reviews > 0 else 0
                }
            },
            "bottleneck_analysis": bottlenecks[:10]  # Top 10 bottlenecks
        }
        
        return jsonify({
            "success": True,
            "data": response_data
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting performance metrics: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_analytics_bp.route('/api/performance/analytics/satisfaction', methods=['GET'])
def get_satisfaction_analytics():
    """Get satisfaction and feedback analytics."""
    try:
        company_id = request.args.get('company_id')
        cycle_id = request.args.get('cycle_id')
        feedback_type = request.args.get('feedback_type')

        if not company_id:
            return jsonify({"success": False, "message": "company_id is required"}), 400

        # Build query filters
        query_filters = []

        if cycle_id:
            query_filters.append(PerformanceFeedback.cycle_id == cycle_id)

        if feedback_type:
            query_filters.append(PerformanceFeedback.feedback_type == feedback_type)

        # Get feedback entries
        feedback_query = db.session.query(PerformanceFeedback).filter(
            PerformanceFeedback.status.in_(['SUBMITTED', 'REVIEWED', 'SHARED'])
        )

        if query_filters:
            feedback_query = feedback_query.filter(*query_filters)

        feedback_entries = feedback_query.all()

        if not feedback_entries:
            return jsonify({
                "success": True,
                "data": {
                    "summary": {
                        "total_feedback_entries": 0,
                        "average_overall_rating": 0,
                        "recommendation_rate": 0
                    },
                    "rating_distribution": {},
                    "source_analysis": {},
                    "satisfaction_trends": []
                }
            }), 200

        # Summary statistics
        total_feedback = len(feedback_entries)

        # Overall ratings
        overall_ratings = [float(f.overall_rating) for f in feedback_entries if f.overall_rating]
        avg_overall_rating = round(sum(overall_ratings) / len(overall_ratings), 2) if overall_ratings else 0

        # Rating distribution
        rating_distribution = {}
        for rating in overall_ratings:
            rating_int = int(rating)
            rating_distribution[rating_int] = rating_distribution.get(rating_int, 0) + 1

        # Source analysis
        source_analysis = {}
        for feedback in feedback_entries:
            source = feedback.feedback_source
            if source not in source_analysis:
                source_analysis[source] = {
                    "count": 0,
                    "ratings": [],
                    "avg_rating": 0
                }

            source_analysis[source]["count"] += 1
            if feedback.overall_rating:
                source_analysis[source]["ratings"].append(float(feedback.overall_rating))

        # Calculate average ratings by source
        for source in source_analysis:
            ratings = source_analysis[source]["ratings"]
            source_analysis[source]["avg_rating"] = round(sum(ratings) / len(ratings), 2) if ratings else 0

        # Satisfaction categories
        high_satisfaction = len([f for f in feedback_entries if f.overall_rating and float(f.overall_rating) >= 4])
        medium_satisfaction = len([f for f in feedback_entries if f.overall_rating and 2.5 <= float(f.overall_rating) < 4])
        low_satisfaction = len([f for f in feedback_entries if f.overall_rating and float(f.overall_rating) < 2.5])

        # Promotion readiness analysis
        promotion_ready = len([f for f in feedback_entries if f.promotion_readiness == 'READY'])
        promotion_developing = len([f for f in feedback_entries if f.promotion_readiness == 'DEVELOPING'])
        promotion_not_ready = len([f for f in feedback_entries if f.promotion_readiness == 'NOT_READY'])

        # Retention risk analysis
        retention_low_risk = len([f for f in feedback_entries if f.retention_risk == 'LOW'])
        retention_medium_risk = len([f for f in feedback_entries if f.retention_risk == 'MEDIUM'])
        retention_high_risk = len([f for f in feedback_entries if f.retention_risk == 'HIGH'])

        response_data = {
            "summary": {
                "total_feedback_entries": total_feedback,
                "average_overall_rating": avg_overall_rating,
                "high_satisfaction_count": high_satisfaction,
                "medium_satisfaction_count": medium_satisfaction,
                "low_satisfaction_count": low_satisfaction,
                "high_satisfaction_rate": round(high_satisfaction / total_feedback * 100, 2) if total_feedback > 0 else 0
            },
            "rating_distribution": rating_distribution,
            "source_analysis": source_analysis,
            "promotion_readiness": {
                "ready": promotion_ready,
                "developing": promotion_developing,
                "not_ready": promotion_not_ready,
                "ready_rate": round(promotion_ready / total_feedback * 100, 2) if total_feedback > 0 else 0
            },
            "retention_risk": {
                "low_risk": retention_low_risk,
                "medium_risk": retention_medium_risk,
                "high_risk": retention_high_risk,
                "high_risk_rate": round(retention_high_risk / total_feedback * 100, 2) if total_feedback > 0 else 0
            },
            "feedback_quality": {
                "total_entries": total_feedback,
                "with_ratings": len(overall_ratings),
                "rating_completion_rate": round(len(overall_ratings) / total_feedback * 100, 2) if total_feedback > 0 else 0
            }
        }

        return jsonify({
            "success": True,
            "data": response_data
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error getting satisfaction analytics: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_analytics_bp.route('/api/performance/analytics/employee/<employee_id>', methods=['GET'])
def get_employee_performance_analytics(employee_id):
    """Get comprehensive performance analytics for a specific employee."""
    try:
        cycle_id = request.args.get('cycle_id')
        include_historical = request.args.get('include_historical', 'true').lower() == 'true'

        # Build query filters
        query_filters = [PerformanceGoal.employee_id == employee_id]
        if cycle_id:
            query_filters.append(PerformanceGoal.cycle_id == cycle_id)

        # Get employee goals
        goals = db.session.query(PerformanceGoal).filter(*query_filters).all()

        # Get employee reviews
        review_filters = [PerformanceReview.employee_id == employee_id]
        if cycle_id:
            review_filters.append(PerformanceReview.cycle_id == cycle_id)

        reviews = db.session.query(PerformanceReview).filter(*review_filters).all()

        # Get employee feedback
        feedback_filters = [PerformanceFeedback.employee_id == employee_id]
        if cycle_id:
            feedback_filters.append(PerformanceFeedback.cycle_id == cycle_id)

        feedback_entries = db.session.query(PerformanceFeedback).filter(*feedback_filters).all()

        # Get development plans
        dev_plan_filters = [DevelopmentPlan.employee_id == employee_id]
        if cycle_id:
            dev_plan_filters.append(DevelopmentPlan.cycle_id == cycle_id)

        dev_plans = db.session.query(DevelopmentPlan).filter(*dev_plan_filters).all()

        # Goal analytics
        total_goals = len(goals)
        completed_goals = len([g for g in goals if g.status == 'COMPLETED'])
        overdue_goals = len([g for g in goals if g.is_overdue()])
        on_track_goals = len([g for g in goals if g.is_on_track()])

        goal_completion_rate = round(completed_goals / total_goals * 100, 2) if total_goals > 0 else 0

        # Average goal progress
        goal_progress_values = [float(g.completion_percentage) for g in goals if g.completion_percentage]
        avg_goal_progress = round(sum(goal_progress_values) / len(goal_progress_values), 2) if goal_progress_values else 0

        # Review analytics
        total_reviews = len(reviews)
        completed_reviews = len([r for r in reviews if r.status == 'COMPLETED'])

        # Average review ratings
        review_ratings = [float(r.overall_rating) for r in reviews if r.overall_rating]
        avg_review_rating = round(sum(review_ratings) / len(review_ratings), 2) if review_ratings else 0

        # Latest review details
        latest_review = None
        if reviews:
            latest_review_obj = max(reviews, key=lambda r: r.created_at)
            latest_review = {
                "review_id": str(latest_review_obj.review_id),
                "overall_rating": float(latest_review_obj.overall_rating) if latest_review_obj.overall_rating else None,
                "status": latest_review_obj.status,
                "review_type": latest_review_obj.review_type,
                "submitted_date": latest_review_obj.submitted_date.strftime('%Y-%m-%d') if latest_review_obj.submitted_date else None,
                "strengths": latest_review_obj.strengths,
                "areas_for_improvement": latest_review_obj.areas_for_improvement
            }

        # Feedback analytics
        feedback_summary = PerformanceFeedback.get_360_feedback_summary(db.session, employee_id, cycle_id)

        # Development plan analytics
        total_dev_plans = len(dev_plans)
        completed_dev_plans = len([p for p in dev_plans if p.status == 'COMPLETED'])

        dev_plan_progress = []
        for plan in dev_plans:
            dev_plan_progress.append({
                "plan_id": str(plan.plan_id),
                "plan_name": plan.plan_name,
                "status": plan.status,
                "progress_percentage": float(plan.overall_progress_percentage) if plan.overall_progress_percentage else 0,
                "target_completion_date": plan.target_completion_date.strftime('%Y-%m-%d') if plan.target_completion_date else None
            })

        # Performance trends (if historical data included)
        performance_trends = []
        if include_historical:
            # Get historical cycles for this employee
            historical_cycles = db.session.query(PerformanceReviewCycle).join(
                PerformanceGoal, PerformanceReviewCycle.cycle_id == PerformanceGoal.cycle_id
            ).filter(PerformanceGoal.employee_id == employee_id).distinct().order_by(
                PerformanceReviewCycle.start_date
            ).all()

            for cycle in historical_cycles:
                cycle_goals = [g for g in goals if str(g.cycle_id) == str(cycle.cycle_id)]
                cycle_reviews = [r for r in reviews if str(r.cycle_id) == str(cycle.cycle_id)]

                cycle_goal_completion = len([g for g in cycle_goals if g.status == 'COMPLETED'])
                cycle_goal_rate = round(cycle_goal_completion / len(cycle_goals) * 100, 2) if cycle_goals else 0

                cycle_review_ratings = [float(r.overall_rating) for r in cycle_reviews if r.overall_rating]
                cycle_avg_rating = round(sum(cycle_review_ratings) / len(cycle_review_ratings), 2) if cycle_review_ratings else 0

                performance_trends.append({
                    "cycle_id": str(cycle.cycle_id),
                    "cycle_name": cycle.name,
                    "period": cycle.start_date.strftime('%Y-%m'),
                    "goal_completion_rate": cycle_goal_rate,
                    "average_review_rating": cycle_avg_rating,
                    "total_goals": len(cycle_goals),
                    "total_reviews": len(cycle_reviews)
                })

        response_data = {
            "employee_id": employee_id,
            "analysis_period": {
                "cycle_id": cycle_id,
                "include_historical": include_historical
            },
            "goal_performance": {
                "total_goals": total_goals,
                "completed_goals": completed_goals,
                "overdue_goals": overdue_goals,
                "on_track_goals": on_track_goals,
                "completion_rate": goal_completion_rate,
                "average_progress": avg_goal_progress,
                "on_track_rate": round(on_track_goals / total_goals * 100, 2) if total_goals > 0 else 0
            },
            "review_performance": {
                "total_reviews": total_reviews,
                "completed_reviews": completed_reviews,
                "average_rating": avg_review_rating,
                "latest_review": latest_review
            },
            "feedback_summary": feedback_summary,
            "development_plans": {
                "total_plans": total_dev_plans,
                "completed_plans": completed_dev_plans,
                "completion_rate": round(completed_dev_plans / total_dev_plans * 100, 2) if total_dev_plans > 0 else 0,
                "plan_details": dev_plan_progress
            },
            "performance_trends": performance_trends
        }

        return jsonify({
            "success": True,
            "data": response_data
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error getting employee performance analytics: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_analytics_bp.route('/api/performance/analytics/team/<manager_id>', methods=['GET'])
def get_team_performance_analytics(manager_id):
    """Get team performance analytics for a manager."""
    try:
        cycle_id = request.args.get('cycle_id')
        days = request.args.get('days', 30, type=int)

        # Get team progress summary
        team_summary = GoalProgressUpdate.get_team_progress_summary(db.session, manager_id, days)

        # This would require joining with employee table to get team members
        # For now, return the basic team summary

        response_data = {
            "manager_id": manager_id,
            "analysis_period": {
                "cycle_id": cycle_id,
                "days_analyzed": days
            },
            "team_summary": team_summary,
            "team_members": [],  # Would populate with actual team member data
            "team_goals": [],    # Would populate with team goal data
            "team_reviews": []   # Would populate with team review data
        }

        return jsonify({
            "success": True,
            "data": response_data
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error getting team performance analytics: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500
