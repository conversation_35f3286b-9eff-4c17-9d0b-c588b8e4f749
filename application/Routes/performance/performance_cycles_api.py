from flask import Blueprint, request, jsonify, current_app
from application.database import db
from application.Models.performance.performance_review_cycle import PerformanceReviewCycle
from datetime import datetime, date
import json

performance_cycles_bp = Blueprint('performance_cycles', __name__)


@performance_cycles_bp.route('/api/performance/cycles', methods=['POST'])
def create_review_cycle():
    """Create a new performance review cycle."""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['company_id', 'name', 'start_date', 'end_date']
        for field in required_fields:
            if field not in data:
                return jsonify({"success": False, "message": f"Missing required field: {field}"}), 400
        
        # Convert date strings to date objects
        if isinstance(data['start_date'], str):
            data['start_date'] = datetime.strptime(data['start_date'], '%Y-%m-%d').date()
        if isinstance(data['end_date'], str):
            data['end_date'] = datetime.strptime(data['end_date'], '%Y-%m-%d').date()
        
        # Convert optional date fields
        date_fields = ['review_period_start', 'review_period_end', 'goal_setting_deadline', 
                      'self_review_deadline', 'manager_review_deadline', 'peer_feedback_deadline', 
                      'final_review_deadline', 'calibration_deadline']
        
        for field in date_fields:
            if field in data and data[field]:
                if isinstance(data[field], str):
                    data[field] = datetime.strptime(data[field], '%Y-%m-%d').date()
        
        cycle, error = PerformanceReviewCycle.create_cycle(db.session, **data)
        if error:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Performance review cycle created successfully",
            "cycle": cycle.to_dict()
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"Error creating review cycle: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_cycles_bp.route('/api/performance/cycles', methods=['GET'])
def get_review_cycles():
    """Get all review cycles for a company."""
    try:
        company_id = request.args.get('company_id')
        status = request.args.get('status')
        include_inactive = request.args.get('include_inactive', 'false').lower() == 'true'
        
        if not company_id:
            return jsonify({"success": False, "message": "company_id is required"}), 400
        
        status_list = status.split(',') if status else None
        cycles = PerformanceReviewCycle.get_cycles_by_company(
            db.session, company_id, status_list, include_inactive
        )
        
        return jsonify({
            "success": True,
            "cycles": [cycle.to_dict() for cycle in cycles]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting review cycles: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_cycles_bp.route('/api/performance/cycles/<cycle_id>', methods=['GET'])
def get_review_cycle(cycle_id):
    """Get a specific review cycle."""
    try:
        cycle = PerformanceReviewCycle.get_cycle_by_id(db.session, cycle_id)
        if not cycle:
            return jsonify({"success": False, "message": "Review cycle not found"}), 404
        
        return jsonify({
            "success": True,
            "cycle": cycle.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting review cycle: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_cycles_bp.route('/api/performance/cycles/<cycle_id>', methods=['PUT'])
def update_review_cycle(cycle_id):
    """Update a review cycle."""
    try:
        data = request.get_json()
        
        cycle = PerformanceReviewCycle.get_cycle_by_id(db.session, cycle_id)
        if not cycle:
            return jsonify({"success": False, "message": "Review cycle not found"}), 404
        
        # Convert date strings to date objects
        date_fields = ['start_date', 'end_date', 'review_period_start', 'review_period_end', 
                      'goal_setting_deadline', 'self_review_deadline', 'manager_review_deadline', 
                      'peer_feedback_deadline', 'final_review_deadline', 'calibration_deadline']
        
        for field in date_fields:
            if field in data and data[field]:
                if isinstance(data[field], str):
                    data[field] = datetime.strptime(data[field], '%Y-%m-%d').date()
        
        # Update cycle fields
        updatable_fields = [
            'name', 'description', 'cycle_type', 'start_date', 'end_date',
            'review_period_start', 'review_period_end', 'goal_setting_deadline',
            'self_review_deadline', 'manager_review_deadline', 'peer_feedback_deadline',
            'final_review_deadline', 'requires_goals', 'requires_self_review',
            'requires_manager_review', 'requires_peer_feedback', 'requires_subordinate_feedback',
            'include_all_employees', 'minimum_tenure_days', 'auto_assign_reviewers',
            'send_reminders', 'reminder_frequency_days', 'allow_late_submissions',
            'requires_calibration', 'calibration_deadline', 'forced_ranking'
        ]
        
        for field in updatable_fields:
            if field in data:
                setattr(cycle, field, data[field])
        
        # Handle JSON fields
        if 'included_departments' in data:
            cycle.included_departments = json.dumps(data['included_departments']) if data['included_departments'] else None
        
        if 'excluded_employees' in data:
            cycle.excluded_employees = json.dumps(data['excluded_employees']) if data['excluded_employees'] else None
        
        if 'distribution_guidelines' in data:
            cycle.distribution_guidelines = json.dumps(data['distribution_guidelines']) if data['distribution_guidelines'] else None
        
        cycle.updated_at = datetime.now()
        db.session.commit()
        
        return jsonify({
            "success": True,
            "message": "Review cycle updated successfully",
            "cycle": cycle.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error updating review cycle: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_cycles_bp.route('/api/performance/cycles/<cycle_id>/activate', methods=['PUT'])
def activate_review_cycle(cycle_id):
    """Activate a review cycle."""
    try:
        data = request.get_json() or {}
        activated_by = data.get('activated_by')
        
        cycle = PerformanceReviewCycle.get_cycle_by_id(db.session, cycle_id)
        if not cycle:
            return jsonify({"success": False, "message": "Review cycle not found"}), 404
        
        success, error = cycle.activate_cycle(db.session, activated_by)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Review cycle activated successfully",
            "cycle": cycle.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error activating review cycle: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_cycles_bp.route('/api/performance/cycles/<cycle_id>/complete', methods=['PUT'])
def complete_review_cycle(cycle_id):
    """Complete a review cycle."""
    try:
        data = request.get_json() or {}
        completed_by = data.get('completed_by')
        
        cycle = PerformanceReviewCycle.get_cycle_by_id(db.session, cycle_id)
        if not cycle:
            return jsonify({"success": False, "message": "Review cycle not found"}), 404
        
        success, error = cycle.complete_cycle(db.session, completed_by)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Review cycle completed successfully",
            "cycle": cycle.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error completing review cycle: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_cycles_bp.route('/api/performance/cycles/active', methods=['GET'])
def get_active_cycles():
    """Get currently active cycles for a company."""
    try:
        company_id = request.args.get('company_id')
        
        if not company_id:
            return jsonify({"success": False, "message": "company_id is required"}), 400
        
        cycles = PerformanceReviewCycle.get_active_cycles(db.session, company_id)
        
        return jsonify({
            "success": True,
            "cycles": [cycle.to_dict() for cycle in cycles]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting active cycles: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_cycles_bp.route('/api/performance/cycles/<cycle_id>/statistics', methods=['GET'])
def get_cycle_statistics(cycle_id):
    """Get completion statistics for a cycle."""
    try:
        cycle = PerformanceReviewCycle.get_cycle_by_id(db.session, cycle_id)
        if not cycle:
            return jsonify({"success": False, "message": "Review cycle not found"}), 404
        
        stats = cycle.get_completion_stats(db.session)
        
        return jsonify({
            "success": True,
            "statistics": stats
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting cycle statistics: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_cycles_bp.route('/api/performance/cycles/<cycle_id>/participants', methods=['GET'])
def get_cycle_participants(cycle_id):
    """Get participants in a review cycle."""
    try:
        cycle = PerformanceReviewCycle.get_cycle_by_id(db.session, cycle_id)
        if not cycle:
            return jsonify({"success": False, "message": "Review cycle not found"}), 404
        
        # Get unique employees participating in this cycle
        from application.Models.performance.performance_review import PerformanceReview
        
        participants = db.session.query(PerformanceReview.employee_id).filter_by(
            cycle_id=cycle_id
        ).distinct().all()
        
        participant_ids = [str(p.employee_id) for p in participants]
        
        return jsonify({
            "success": True,
            "participant_count": len(participant_ids),
            "participant_ids": participant_ids
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting cycle participants: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_cycles_bp.route('/api/performance/cycles/<cycle_id>/send-reminders', methods=['POST'])
def send_cycle_reminders(cycle_id):
    """Send reminder notifications for a cycle."""
    try:
        cycle = PerformanceReviewCycle.get_cycle_by_id(db.session, cycle_id)
        if not cycle:
            return jsonify({"success": False, "message": "Review cycle not found"}), 404
        
        cycle.send_reminders(db.session)
        
        return jsonify({
            "success": True,
            "message": "Reminders sent successfully"
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error sending cycle reminders: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500
