from flask import Blueprint, request, jsonify, current_app
from application.database import db
from application.Models.performance.performance_feedback import PerformanceFeedback
from datetime import datetime, date, timedelta
import json

performance_feedback_bp = Blueprint('performance_feedback', __name__)


@performance_feedback_bp.route('/api/performance/feedback', methods=['POST'])
def create_feedback():
    """Create or request performance feedback."""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['employee_id', 'feedback_provider_id', 'feedback_type', 'feedback_source']
        for field in required_fields:
            if field not in data:
                return jsonify({"success": False, "message": f"Missing required field: {field}"}), 400
        
        # Convert date strings to date objects
        date_fields = ['due_date', 'feedback_period_start', 'feedback_period_end']
        for field in date_fields:
            if field in data and data[field]:
                if isinstance(data[field], str):
                    data[field] = datetime.strptime(data[field], '%Y-%m-%d').date()
        
        feedback, error = PerformanceFeedback.create_feedback(db.session, **data)
        if error:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Performance feedback created successfully",
            "feedback": feedback.to_dict()
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"Error creating feedback: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_feedback_bp.route('/api/performance/feedback/request', methods=['POST'])
def request_feedback():
    """Request feedback from specific providers."""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['employee_id', 'feedback_provider_id', 'requested_by']
        for field in required_fields:
            if field not in data:
                return jsonify({"success": False, "message": f"Missing required field: {field}"}), 400
        
        # Convert due_date if provided
        if 'due_date' in data and data['due_date']:
            if isinstance(data['due_date'], str):
                data['due_date'] = datetime.strptime(data['due_date'], '%Y-%m-%d').date()
        
        feedback, error = PerformanceFeedback.request_feedback(db.session, **data)
        if error:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Feedback requested successfully",
            "feedback": feedback.to_dict()
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"Error requesting feedback: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_feedback_bp.route('/api/performance/feedback', methods=['GET'])
def get_feedback():
    """Get performance feedback with filtering options."""
    try:
        employee_id = request.args.get('employee_id')
        feedback_provider_id = request.args.get('feedback_provider_id')
        feedback_type = request.args.get('feedback_type')
        cycle_id = request.args.get('cycle_id')
        status = request.args.get('status')
        
        if employee_id:
            feedback_list = PerformanceFeedback.get_feedback_for_employee(
                db.session, employee_id, feedback_type, cycle_id
            )
        elif feedback_provider_id:
            status_list = status.split(',') if status else None
            feedback_list = PerformanceFeedback.get_feedback_by_provider(
                db.session, feedback_provider_id, status_list
            )
        else:
            return jsonify({"success": False, "message": "employee_id or feedback_provider_id is required"}), 400
        
        return jsonify({
            "success": True,
            "feedback": [feedback.to_dict() for feedback in feedback_list]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting feedback: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_feedback_bp.route('/api/performance/feedback/<feedback_id>', methods=['GET'])
def get_feedback_by_id(feedback_id):
    """Get specific feedback by ID."""
    try:
        feedback = PerformanceFeedback.get_feedback_by_id(db.session, feedback_id)
        if not feedback:
            return jsonify({"success": False, "message": "Feedback not found"}), 404
        
        return jsonify({
            "success": True,
            "feedback": feedback.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting feedback: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_feedback_bp.route('/api/performance/feedback/<feedback_id>', methods=['PUT'])
def update_feedback(feedback_id):
    """Update performance feedback content."""
    try:
        data = request.get_json()
        
        feedback = PerformanceFeedback.get_feedback_by_id(db.session, feedback_id)
        if not feedback:
            return jsonify({"success": False, "message": "Feedback not found"}), 404
        
        # Update feedback content
        updatable_fields = [
            'overall_rating', 'strengths', 'areas_for_improvement', 'specific_examples', 'recommendations',
            'communication_rating', 'communication_comments', 'teamwork_rating', 'teamwork_comments',
            'leadership_rating', 'leadership_comments', 'technical_skills_rating', 'technical_skills_comments',
            'problem_solving_rating', 'problem_solving_comments', 'initiative_rating', 'initiative_comments',
            'development_suggestions', 'career_advice', 'training_recommendations', 'mentoring_suggestions',
            'promotion_readiness', 'potential_rating', 'retention_risk', 'feedback_quality'
        ]
        
        for field in updatable_fields:
            if field in data:
                setattr(feedback, field, data[field])
        
        # Handle competency feedback
        if 'competency_feedback' in data:
            feedback.set_competency_feedback(data['competency_feedback'])
        
        # Handle custom questions responses
        if 'custom_questions_responses' in data:
            feedback.set_custom_questions_responses(data['custom_questions_responses'])
        
        feedback.updated_at = datetime.now()
        db.session.commit()
        
        return jsonify({
            "success": True,
            "message": "Feedback updated successfully",
            "feedback": feedback.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error updating feedback: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_feedback_bp.route('/api/performance/feedback/<feedback_id>/submit', methods=['PUT'])
def submit_feedback(feedback_id):
    """Submit performance feedback."""
    try:
        feedback = PerformanceFeedback.get_feedback_by_id(db.session, feedback_id)
        if not feedback:
            return jsonify({"success": False, "message": "Feedback not found"}), 404
        
        success, error = feedback.submit_feedback(db.session)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Feedback submitted successfully",
            "feedback": feedback.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error submitting feedback: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_feedback_bp.route('/api/performance/feedback/<feedback_id>/review', methods=['PUT'])
def review_feedback(feedback_id):
    """Mark feedback as reviewed."""
    try:
        data = request.get_json() or {}
        reviewed_by = data.get('reviewed_by')
        
        feedback = PerformanceFeedback.get_feedback_by_id(db.session, feedback_id)
        if not feedback:
            return jsonify({"success": False, "message": "Feedback not found"}), 404
        
        success, error = feedback.review_feedback(db.session, reviewed_by)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Feedback reviewed successfully",
            "feedback": feedback.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error reviewing feedback: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_feedback_bp.route('/api/performance/feedback/<feedback_id>/share', methods=['PUT'])
def share_feedback(feedback_id):
    """Share feedback with employee."""
    try:
        feedback = PerformanceFeedback.get_feedback_by_id(db.session, feedback_id)
        if not feedback:
            return jsonify({"success": False, "message": "Feedback not found"}), 404
        
        success, error = feedback.share_with_employee(db.session)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Feedback shared with employee successfully",
            "feedback": feedback.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error sharing feedback: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_feedback_bp.route('/api/performance/feedback/<feedback_id>/respond', methods=['PUT'])
def respond_to_feedback(feedback_id):
    """Employee responds to feedback."""
    try:
        data = request.get_json()
        response_text = data.get('response_text')
        
        if not response_text:
            return jsonify({"success": False, "message": "response_text is required"}), 400
        
        feedback = PerformanceFeedback.get_feedback_by_id(db.session, feedback_id)
        if not feedback:
            return jsonify({"success": False, "message": "Feedback not found"}), 404
        
        success, error = feedback.employee_respond(db.session, response_text)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Response submitted successfully",
            "feedback": feedback.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error responding to feedback: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_feedback_bp.route('/api/performance/feedback/360-summary/<employee_id>', methods=['GET'])
def get_360_feedback_summary(employee_id):
    """Get 360-degree feedback summary for an employee."""
    try:
        cycle_id = request.args.get('cycle_id')
        
        summary = PerformanceFeedback.get_360_feedback_summary(db.session, employee_id, cycle_id)
        
        return jsonify({
            "success": True,
            "summary": summary
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting 360 feedback summary: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_feedback_bp.route('/api/performance/feedback/overdue', methods=['GET'])
def get_overdue_feedback():
    """Get overdue feedback requests."""
    try:
        feedback_provider_id = request.args.get('feedback_provider_id')
        days_overdue = request.args.get('days_overdue', 0, type=int)
        
        feedback_list = PerformanceFeedback.get_overdue_feedback(
            db.session, feedback_provider_id, days_overdue
        )
        
        return jsonify({
            "success": True,
            "feedback": [feedback.to_dict() for feedback in feedback_list]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting overdue feedback: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_feedback_bp.route('/api/performance/feedback/pending/<feedback_provider_id>', methods=['GET'])
def get_pending_feedback_requests(feedback_provider_id):
    """Get pending feedback requests for a provider."""
    try:
        feedback_list = PerformanceFeedback.get_pending_feedback_requests(
            db.session, feedback_provider_id
        )
        
        return jsonify({
            "success": True,
            "feedback": [feedback.to_dict() for feedback in feedback_list]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting pending feedback requests: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_feedback_bp.route('/api/performance/feedback/statistics', methods=['GET'])
def get_feedback_statistics():
    """Get feedback statistics."""
    try:
        employee_id = request.args.get('employee_id')
        cycle_id = request.args.get('cycle_id')
        
        stats = PerformanceFeedback.get_feedback_statistics(db.session, employee_id, cycle_id)
        
        return jsonify({
            "success": True,
            "statistics": stats
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting feedback statistics: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500
