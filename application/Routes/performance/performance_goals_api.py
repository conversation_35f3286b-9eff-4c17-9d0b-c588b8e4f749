from flask import Blueprint, request, jsonify, current_app
from application.database import db
from application.Models.performance.performance_goal import PerformanceGoal
from application.Models.performance.goal_progress_update import GoalProgressUpdate
from datetime import datetime, date
import json

performance_goals_bp = Blueprint('performance_goals', __name__)


@performance_goals_bp.route('/api/performance/goals', methods=['POST'])
def create_goal():
    """Create a new performance goal."""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['employee_id', 'cycle_id', 'title', 'target_completion_date']
        for field in required_fields:
            if field not in data:
                return jsonify({"success": False, "message": f"Missing required field: {field}"}), 400
        
        # Convert date strings to date objects
        date_fields = ['start_date', 'target_completion_date', 'time_bound_deadline', 'next_review_date']
        for field in date_fields:
            if field in data and data[field]:
                if isinstance(data[field], str):
                    data[field] = datetime.strptime(data[field], '%Y-%m-%d').date()
        
        goal, error = PerformanceGoal.create_goal(db.session, **data)
        if error:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Performance goal created successfully",
            "goal": goal.to_dict()
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"Error creating goal: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_goals_bp.route('/api/performance/goals', methods=['GET'])
def get_goals():
    """Get performance goals with filtering options."""
    try:
        employee_id = request.args.get('employee_id')
        cycle_id = request.args.get('cycle_id')
        status = request.args.get('status')
        
        if employee_id:
            status_list = status.split(',') if status else None
            goals = PerformanceGoal.get_goals_by_employee(db.session, employee_id, cycle_id, status_list)
        elif cycle_id:
            goals = PerformanceGoal.get_goals_by_cycle(db.session, cycle_id, status)
        else:
            return jsonify({"success": False, "message": "employee_id or cycle_id is required"}), 400
        
        return jsonify({
            "success": True,
            "goals": [goal.to_dict() for goal in goals]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting goals: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_goals_bp.route('/api/performance/goals/<goal_id>', methods=['GET'])
def get_goal(goal_id):
    """Get a specific goal."""
    try:
        goal = PerformanceGoal.get_goal_by_id(db.session, goal_id)
        if not goal:
            return jsonify({"success": False, "message": "Goal not found"}), 404
        
        return jsonify({
            "success": True,
            "goal": goal.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting goal: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_goals_bp.route('/api/performance/goals/<goal_id>', methods=['PUT'])
def update_goal(goal_id):
    """Update a performance goal."""
    try:
        data = request.get_json()
        
        goal = PerformanceGoal.get_goal_by_id(db.session, goal_id)
        if not goal:
            return jsonify({"success": False, "message": "Goal not found"}), 404
        
        # Convert date strings to date objects
        date_fields = ['start_date', 'target_completion_date', 'time_bound_deadline', 'next_review_date']
        for field in date_fields:
            if field in data and data[field]:
                if isinstance(data[field], str):
                    data[field] = datetime.strptime(data[field], '%Y-%m-%d').date()
        
        # Update goal fields
        updatable_fields = [
            'title', 'description', 'goal_category', 'goal_type', 'specific_description',
            'measurable_criteria', 'achievable_rationale', 'relevant_justification',
            'time_bound_deadline', 'target_value', 'unit_of_measure', 'weight', 'priority',
            'difficulty_level', 'start_date', 'target_completion_date', 'review_frequency',
            'aligned_company_objective', 'department_alignment', 'required_resources',
            'support_needed', 'potential_obstacles', 'success_factors', 'performance_impact',
            'business_impact', 'skill_development_areas', 'update_frequency'
        ]
        
        for field in updatable_fields:
            if field in data:
                setattr(goal, field, data[field])
        
        # Handle JSON fields
        if 'milestones' in data:
            goal.set_milestones(data['milestones'])
        
        goal.updated_at = datetime.now()
        db.session.commit()
        
        return jsonify({
            "success": True,
            "message": "Goal updated successfully",
            "goal": goal.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error updating goal: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_goals_bp.route('/api/performance/goals/<goal_id>/progress', methods=['PUT'])
def update_goal_progress(goal_id):
    """Update goal progress."""
    try:
        data = request.get_json()
        
        goal = PerformanceGoal.get_goal_by_id(db.session, goal_id)
        if not goal:
            return jsonify({"success": False, "message": "Goal not found"}), 404
        
        current_value = data.get('current_value')
        completion_percentage = data.get('completion_percentage')
        progress_notes = data.get('progress_notes')
        updated_by = data.get('updated_by')
        
        success, error = goal.update_progress(
            db.session, current_value, completion_percentage, progress_notes, updated_by
        )
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Goal progress updated successfully",
            "goal": goal.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error updating goal progress: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_goals_bp.route('/api/performance/goals/<goal_id>/approve', methods=['PUT'])
def approve_goal(goal_id):
    """Approve a goal."""
    try:
        data = request.get_json()
        approved_by = data.get('approved_by')
        approval_comments = data.get('approval_comments')
        
        if not approved_by:
            return jsonify({"success": False, "message": "approved_by is required"}), 400
        
        goal = PerformanceGoal.get_goal_by_id(db.session, goal_id)
        if not goal:
            return jsonify({"success": False, "message": "Goal not found"}), 404
        
        success, error = goal.approve_goal(db.session, approved_by, approval_comments)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Goal approved successfully",
            "goal": goal.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error approving goal: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_goals_bp.route('/api/performance/goals/<goal_id>/reject', methods=['PUT'])
def reject_goal(goal_id):
    """Reject a goal."""
    try:
        data = request.get_json()
        rejected_by = data.get('rejected_by')
        rejection_comments = data.get('rejection_comments')
        
        if not rejected_by or not rejection_comments:
            return jsonify({"success": False, "message": "rejected_by and rejection_comments are required"}), 400
        
        goal = PerformanceGoal.get_goal_by_id(db.session, goal_id)
        if not goal:
            return jsonify({"success": False, "message": "Goal not found"}), 404
        
        success, error = goal.reject_goal(db.session, rejected_by, rejection_comments)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Goal rejected successfully",
            "goal": goal.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error rejecting goal: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_goals_bp.route('/api/performance/goals/<goal_id>/complete', methods=['PUT'])
def complete_goal(goal_id):
    """Complete a goal."""
    try:
        data = request.get_json()
        completed_by = data.get('completed_by')
        completion_notes = data.get('completion_notes')
        
        if not completed_by:
            return jsonify({"success": False, "message": "completed_by is required"}), 400
        
        goal = PerformanceGoal.get_goal_by_id(db.session, goal_id)
        if not goal:
            return jsonify({"success": False, "message": "Goal not found"}), 404
        
        success, error = goal.complete_goal(db.session, completed_by, completion_notes)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Goal completed successfully",
            "goal": goal.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error completing goal: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_goals_bp.route('/api/performance/goals/<goal_id>/milestones', methods=['POST'])
def add_milestone(goal_id):
    """Add a milestone to a goal."""
    try:
        data = request.get_json()
        
        required_fields = ['milestone_title', 'milestone_date']
        for field in required_fields:
            if field not in data:
                return jsonify({"success": False, "message": f"Missing required field: {field}"}), 400
        
        goal = PerformanceGoal.get_goal_by_id(db.session, goal_id)
        if not goal:
            return jsonify({"success": False, "message": "Goal not found"}), 404
        
        milestone_date = data['milestone_date']
        if isinstance(milestone_date, str):
            milestone_date = datetime.strptime(milestone_date, '%Y-%m-%d').date()
        
        success, error = goal.add_milestone(
            db.session, 
            data['milestone_title'], 
            milestone_date, 
            data.get('milestone_description')
        )
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Milestone added successfully",
            "goal": goal.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error adding milestone: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_goals_bp.route('/api/performance/goals/<goal_id>/milestones/<milestone_id>/complete', methods=['PUT'])
def complete_milestone(goal_id, milestone_id):
    """Complete a milestone."""
    try:
        goal = PerformanceGoal.get_goal_by_id(db.session, goal_id)
        if not goal:
            return jsonify({"success": False, "message": "Goal not found"}), 404
        
        success, error = goal.complete_milestone(db.session, milestone_id)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Milestone completed successfully",
            "goal": goal.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error completing milestone: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_goals_bp.route('/api/performance/goals/<goal_id>/progress-updates', methods=['GET'])
def get_goal_progress_updates(goal_id):
    """Get progress updates for a goal."""
    try:
        limit = request.args.get('limit', type=int)
        
        updates = GoalProgressUpdate.get_updates_by_goal(db.session, goal_id, limit)
        
        return jsonify({
            "success": True,
            "updates": [update.to_dict() for update in updates]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting goal progress updates: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_goals_bp.route('/api/performance/goals/overdue', methods=['GET'])
def get_overdue_goals():
    """Get overdue goals."""
    try:
        employee_id = request.args.get('employee_id')
        days_overdue = request.args.get('days_overdue', 0, type=int)
        
        goals = PerformanceGoal.get_overdue_goals(db.session, employee_id, days_overdue)
        
        return jsonify({
            "success": True,
            "goals": [goal.to_dict() for goal in goals]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting overdue goals: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_goals_bp.route('/api/performance/goals/needing-review', methods=['GET'])
def get_goals_needing_review():
    """Get goals that need review."""
    try:
        employee_id = request.args.get('employee_id')
        
        goals = PerformanceGoal.get_goals_needing_review(db.session, employee_id)
        
        return jsonify({
            "success": True,
            "goals": [goal.to_dict() for goal in goals]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting goals needing review: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500
