from flask import Blueprint, request, jsonify, current_app
from application.database import db
from application.Models.performance.performance_review import PerformanceReview
from datetime import datetime, date
import json

performance_reviews_bp = Blueprint('performance_reviews', __name__)


@performance_reviews_bp.route('/api/performance/reviews', methods=['POST'])
def create_review():
    """Create a new performance review."""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['cycle_id', 'employee_id', 'reviewer_id', 'review_type']
        for field in required_fields:
            if field not in data:
                return jsonify({"success": False, "message": f"Missing required field: {field}"}), 400
        
        # Convert date strings to date objects
        if 'due_date' in data and data['due_date']:
            if isinstance(data['due_date'], str):
                data['due_date'] = datetime.strptime(data['due_date'], '%Y-%m-%d').date()
        
        review, error = PerformanceReview.create_review(db.session, **data)
        if error:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Performance review created successfully",
            "review": review.to_dict()
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"Error creating review: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_reviews_bp.route('/api/performance/reviews', methods=['GET'])
def get_reviews():
    """Get performance reviews with filtering options."""
    try:
        employee_id = request.args.get('employee_id')
        reviewer_id = request.args.get('reviewer_id')
        cycle_id = request.args.get('cycle_id')
        review_type = request.args.get('review_type')
        status = request.args.get('status')
        
        if employee_id:
            reviews = PerformanceReview.get_reviews_by_employee(db.session, employee_id, cycle_id, review_type)
        elif reviewer_id:
            status_list = status.split(',') if status else None
            reviews = PerformanceReview.get_reviews_by_reviewer(db.session, reviewer_id, status_list)
        else:
            return jsonify({"success": False, "message": "employee_id or reviewer_id is required"}), 400
        
        return jsonify({
            "success": True,
            "reviews": [review.to_dict() for review in reviews]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting reviews: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_reviews_bp.route('/api/performance/reviews/<review_id>', methods=['GET'])
def get_review(review_id):
    """Get a specific review."""
    try:
        review = PerformanceReview.get_review_by_id(db.session, review_id)
        if not review:
            return jsonify({"success": False, "message": "Review not found"}), 404
        
        return jsonify({
            "success": True,
            "review": review.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting review: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_reviews_bp.route('/api/performance/reviews/<review_id>', methods=['PUT'])
def update_review(review_id):
    """Update a performance review."""
    try:
        data = request.get_json()
        
        review = PerformanceReview.get_review_by_id(db.session, review_id)
        if not review:
            return jsonify({"success": False, "message": "Review not found"}), 404
        
        # Update review fields
        updatable_fields = [
            'overall_rating', 'overall_rating_justification',
            'job_knowledge_rating', 'job_knowledge_comments',
            'quality_of_work_rating', 'quality_of_work_comments',
            'productivity_rating', 'productivity_comments',
            'communication_rating', 'communication_comments',
            'teamwork_rating', 'teamwork_comments',
            'leadership_rating', 'leadership_comments',
            'initiative_rating', 'initiative_comments',
            'problem_solving_rating', 'problem_solving_comments',
            'goals_achievement_rating', 'goals_achievement_comments',
            'strengths', 'areas_for_improvement', 'achievements', 'challenges_faced',
            'development_needs', 'career_aspirations', 'training_recommendations',
            'mentoring_needs', 'short_term_goals', 'long_term_goals', 'support_needed',
            'promotion_readiness', 'retention_risk', 'succession_potential',
            'requires_pip', 'pip_reason', 'employee_comments', 'employee_agreement'
        ]
        
        for field in updatable_fields:
            if field in data:
                setattr(review, field, data[field])
        
        # Handle competency ratings
        if 'competency_ratings' in data:
            review.set_competency_ratings(data['competency_ratings'])
        
        # Handle custom fields
        if 'custom_fields' in data:
            review.set_custom_fields(data['custom_fields'])
        
        review.updated_at = datetime.now()
        
        # Update completion percentage
        review.update_completion_percentage(db.session)
        
        return jsonify({
            "success": True,
            "message": "Review updated successfully",
            "review": review.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error updating review: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_reviews_bp.route('/api/performance/reviews/<review_id>/start', methods=['PUT'])
def start_review(review_id):
    """Start a performance review."""
    try:
        review = PerformanceReview.get_review_by_id(db.session, review_id)
        if not review:
            return jsonify({"success": False, "message": "Review not found"}), 404
        
        success, error = review.start_review(db.session)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Review started successfully",
            "review": review.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error starting review: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_reviews_bp.route('/api/performance/reviews/<review_id>/submit', methods=['PUT'])
def submit_review(review_id):
    """Submit a performance review."""
    try:
        review = PerformanceReview.get_review_by_id(db.session, review_id)
        if not review:
            return jsonify({"success": False, "message": "Review not found"}), 404
        
        success, error = review.submit_review(db.session)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Review submitted successfully",
            "review": review.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error submitting review: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_reviews_bp.route('/api/performance/reviews/<review_id>/approve', methods=['PUT'])
def approve_review(review_id):
    """Approve a performance review."""
    try:
        data = request.get_json()
        approved_by = data.get('approved_by')
        approval_comments = data.get('approval_comments')
        
        if not approved_by:
            return jsonify({"success": False, "message": "approved_by is required"}), 400
        
        review = PerformanceReview.get_review_by_id(db.session, review_id)
        if not review:
            return jsonify({"success": False, "message": "Review not found"}), 404
        
        success, error = review.approve_review(db.session, approved_by, approval_comments)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Review approved successfully",
            "review": review.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error approving review: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_reviews_bp.route('/api/performance/reviews/<review_id>/acknowledge', methods=['PUT'])
def acknowledge_review(review_id):
    """Employee acknowledges a performance review."""
    try:
        data = request.get_json()
        employee_comments = data.get('employee_comments')
        agreement = data.get('agreement')  # AGREE, PARTIALLY_AGREE, DISAGREE
        
        review = PerformanceReview.get_review_by_id(db.session, review_id)
        if not review:
            return jsonify({"success": False, "message": "Review not found"}), 404
        
        success, error = review.employee_acknowledge(db.session, employee_comments, agreement)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Review acknowledged successfully",
            "review": review.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error acknowledging review: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_reviews_bp.route('/api/performance/reviews/<review_id>/calibrate', methods=['PUT'])
def calibrate_review(review_id):
    """Calibrate a performance review."""
    try:
        data = request.get_json()
        calibrated_by = data.get('calibrated_by')
        calibration_notes = data.get('calibration_notes')
        
        if not calibrated_by:
            return jsonify({"success": False, "message": "calibrated_by is required"}), 400
        
        review = PerformanceReview.get_review_by_id(db.session, review_id)
        if not review:
            return jsonify({"success": False, "message": "Review not found"}), 404
        
        success, error = review.calibrate_review(db.session, calibrated_by, calibration_notes)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Review calibrated successfully",
            "review": review.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error calibrating review: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_reviews_bp.route('/api/performance/reviews/overdue', methods=['GET'])
def get_overdue_reviews():
    """Get overdue reviews."""
    try:
        reviewer_id = request.args.get('reviewer_id')
        days_overdue = request.args.get('days_overdue', 0, type=int)
        
        reviews = PerformanceReview.get_overdue_reviews(db.session, reviewer_id, days_overdue)
        
        return jsonify({
            "success": True,
            "reviews": [review.to_dict() for review in reviews]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting overdue reviews: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_reviews_bp.route('/api/performance/reviews/pending-approvals', methods=['GET'])
def get_pending_approvals():
    """Get reviews pending approval."""
    try:
        approver_id = request.args.get('approver_id')
        
        reviews = PerformanceReview.get_pending_approvals(db.session, approver_id)
        
        return jsonify({
            "success": True,
            "reviews": [review.to_dict() for review in reviews]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting pending approvals: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_reviews_bp.route('/api/performance/reviews/statistics', methods=['GET'])
def get_review_statistics():
    """Get review statistics."""
    try:
        cycle_id = request.args.get('cycle_id')
        employee_id = request.args.get('employee_id')
        reviewer_id = request.args.get('reviewer_id')
        
        stats = PerformanceReview.get_review_statistics(
            db.session, cycle_id, employee_id, reviewer_id
        )
        
        return jsonify({
            "success": True,
            "statistics": stats
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting review statistics: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@performance_reviews_bp.route('/api/performance/reviews/<review_id>/complete', methods=['PUT'])
def complete_review(review_id):
    """Complete a performance review."""
    try:
        data = request.get_json() or {}
        completed_by = data.get('completed_by')
        
        review = PerformanceReview.get_review_by_id(db.session, review_id)
        if not review:
            return jsonify({"success": False, "message": "Review not found"}), 404
        
        success, error = review.complete_review(db.session, completed_by)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Review completed successfully",
            "review": review.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error completing review: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500
