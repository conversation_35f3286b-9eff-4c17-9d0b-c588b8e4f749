from flask import Blueprint, request, jsonify, current_app
from application.database import db
from application.Models.performance.performance_rating_scale import PerformanceRatingScale
from application.Models.performance.competency_framework import CompetencyFramework, Competency
from datetime import datetime
import json

rating_scales_bp = Blueprint('rating_scales', __name__)


@rating_scales_bp.route('/api/performance/rating-scales', methods=['POST'])
def create_rating_scale():
    """Create a new rating scale."""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['company_id', 'name', 'scale_points']
        for field in required_fields:
            if field not in data:
                return jsonify({"success": False, "message": f"Missing required field: {field}"}), 400
        
        scale, error = PerformanceRatingScale.create_scale(db.session, **data)
        if error:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Rating scale created successfully",
            "scale": scale.to_dict()
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"Error creating rating scale: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@rating_scales_bp.route('/api/performance/rating-scales', methods=['GET'])
def get_rating_scales():
    """Get all rating scales for a company."""
    try:
        company_id = request.args.get('company_id')
        include_inactive = request.args.get('include_inactive', 'false').lower() == 'true'
        
        if not company_id:
            return jsonify({"success": False, "message": "company_id is required"}), 400
        
        scales = PerformanceRatingScale.get_scales_by_company(db.session, company_id, include_inactive)
        
        return jsonify({
            "success": True,
            "scales": [scale.to_dict() for scale in scales]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting rating scales: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@rating_scales_bp.route('/api/performance/rating-scales/<scale_id>', methods=['GET'])
def get_rating_scale(scale_id):
    """Get a specific rating scale."""
    try:
        scale = PerformanceRatingScale.get_scale_by_id(db.session, scale_id)
        if not scale:
            return jsonify({"success": False, "message": "Rating scale not found"}), 404
        
        return jsonify({
            "success": True,
            "scale": scale.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting rating scale: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@rating_scales_bp.route('/api/performance/rating-scales/<scale_id>', methods=['PUT'])
def update_rating_scale(scale_id):
    """Update a rating scale."""
    try:
        data = request.get_json()
        
        scale = PerformanceRatingScale.get_scale_by_id(db.session, scale_id)
        if not scale:
            return jsonify({"success": False, "message": "Rating scale not found"}), 404
        
        success, error = scale.update_scale(db.session, **data)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Rating scale updated successfully",
            "scale": scale.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error updating rating scale: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@rating_scales_bp.route('/api/performance/rating-scales/<scale_id>/deactivate', methods=['PUT'])
def deactivate_rating_scale(scale_id):
    """Deactivate a rating scale."""
    try:
        scale = PerformanceRatingScale.get_scale_by_id(db.session, scale_id)
        if not scale:
            return jsonify({"success": False, "message": "Rating scale not found"}), 404
        
        success, error = scale.deactivate_scale(db.session)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Rating scale deactivated successfully",
            "scale": scale.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error deactivating rating scale: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@rating_scales_bp.route('/api/performance/rating-scales/default', methods=['GET'])
def get_default_rating_scale():
    """Get the default rating scale for a company."""
    try:
        company_id = request.args.get('company_id')
        
        if not company_id:
            return jsonify({"success": False, "message": "company_id is required"}), 400
        
        scale = PerformanceRatingScale.get_default_scale(db.session, company_id)
        if not scale:
            return jsonify({"success": False, "message": "No default rating scale found"}), 404
        
        return jsonify({
            "success": True,
            "scale": scale.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting default rating scale: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@rating_scales_bp.route('/api/performance/rating-scales/<scale_id>/clone', methods=['POST'])
def clone_rating_scale(scale_id):
    """Clone a rating scale with a new name."""
    try:
        data = request.get_json()
        new_name = data.get('new_name')
        created_by = data.get('created_by')
        
        if not new_name:
            return jsonify({"success": False, "message": "new_name is required"}), 400
        
        scale = PerformanceRatingScale.get_scale_by_id(db.session, scale_id)
        if not scale:
            return jsonify({"success": False, "message": "Rating scale not found"}), 404
        
        new_scale, error = scale.clone_scale(db.session, new_name, created_by)
        if error:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Rating scale cloned successfully",
            "scale": new_scale.to_dict()
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"Error cloning rating scale: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@rating_scales_bp.route('/api/performance/rating-scales/<scale_id>/validate', methods=['POST'])
def validate_rating():
    """Validate a rating value against a scale."""
    try:
        data = request.get_json()
        rating_value = data.get('rating_value')
        
        if rating_value is None:
            return jsonify({"success": False, "message": "rating_value is required"}), 400
        
        scale = PerformanceRatingScale.get_scale_by_id(db.session, scale_id)
        if not scale:
            return jsonify({"success": False, "message": "Rating scale not found"}), 404
        
        is_valid, error_message = scale.validate_rating(rating_value)
        
        response_data = {
            "is_valid": is_valid,
            "rating_value": rating_value,
            "scale_id": scale_id
        }
        
        if not is_valid:
            response_data["error_message"] = error_message
        else:
            response_data["rating_description"] = scale.get_rating_description(rating_value)
            response_data["is_extreme_rating"] = scale.is_extreme_rating(rating_value)
        
        return jsonify({
            "success": True,
            "validation": response_data
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error validating rating: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@rating_scales_bp.route('/api/performance/rating-scales/<scale_id>/usage-statistics', methods=['GET'])
def get_scale_usage_statistics(scale_id):
    """Get usage statistics for a rating scale."""
    try:
        scale = PerformanceRatingScale.get_scale_by_id(db.session, scale_id)
        if not scale:
            return jsonify({"success": False, "message": "Rating scale not found"}), 404
        
        stats = scale.get_usage_statistics(db.session)
        
        return jsonify({
            "success": True,
            "statistics": stats
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting scale usage statistics: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@rating_scales_bp.route('/api/performance/rating-scales/create-defaults', methods=['POST'])
def create_default_scales():
    """Create default rating scales for a new company."""
    try:
        data = request.get_json()
        company_id = data.get('company_id')
        created_by = data.get('created_by')
        
        if not company_id:
            return jsonify({"success": False, "message": "company_id is required"}), 400
        
        success, error = PerformanceRatingScale.create_default_scales(db.session, company_id, created_by)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        # Get the created scales
        scales = PerformanceRatingScale.get_scales_by_company(db.session, company_id)
        
        return jsonify({
            "success": True,
            "message": "Default rating scales created successfully",
            "scales": [scale.to_dict() for scale in scales]
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"Error creating default scales: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


# ============================================================================
# COMPETENCY FRAMEWORKS
# ============================================================================

@rating_scales_bp.route('/api/performance/competency-frameworks', methods=['POST'])
def create_competency_framework():
    """Create a new competency framework."""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['company_id', 'name', 'proficiency_levels']
        for field in required_fields:
            if field not in data:
                return jsonify({"success": False, "message": f"Missing required field: {field}"}), 400
        
        framework, error = CompetencyFramework.create_framework(db.session, **data)
        if error:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Competency framework created successfully",
            "framework": framework.to_dict()
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"Error creating competency framework: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@rating_scales_bp.route('/api/performance/competency-frameworks', methods=['GET'])
def get_competency_frameworks():
    """Get all competency frameworks for a company."""
    try:
        company_id = request.args.get('company_id')
        include_inactive = request.args.get('include_inactive', 'false').lower() == 'true'
        
        if not company_id:
            return jsonify({"success": False, "message": "company_id is required"}), 400
        
        frameworks = CompetencyFramework.get_frameworks_by_company(db.session, company_id, include_inactive)
        
        return jsonify({
            "success": True,
            "frameworks": [framework.to_dict() for framework in frameworks]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting competency frameworks: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@rating_scales_bp.route('/api/performance/competency-frameworks/<framework_id>', methods=['GET'])
def get_competency_framework(framework_id):
    """Get a specific competency framework."""
    try:
        framework = CompetencyFramework.get_framework_by_id(db.session, framework_id)
        if not framework:
            return jsonify({"success": False, "message": "Competency framework not found"}), 404
        
        return jsonify({
            "success": True,
            "framework": framework.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting competency framework: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@rating_scales_bp.route('/api/performance/competency-frameworks/<framework_id>/competencies', methods=['GET'])
def get_framework_competencies(framework_id):
    """Get all competencies for a framework."""
    try:
        category = request.args.get('category')
        include_inactive = request.args.get('include_inactive', 'false').lower() == 'true'
        
        competencies = Competency.get_competencies_by_framework(
            db.session, framework_id, category, include_inactive
        )
        
        return jsonify({
            "success": True,
            "competencies": [competency.to_dict() for competency in competencies]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting framework competencies: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@rating_scales_bp.route('/api/performance/competency-frameworks/<framework_id>/competencies', methods=['POST'])
def add_competency_to_framework(framework_id):
    """Add a competency to a framework."""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['name', 'description']
        for field in required_fields:
            if field not in data:
                return jsonify({"success": False, "message": f"Missing required field: {field}"}), 400
        
        framework = CompetencyFramework.get_framework_by_id(db.session, framework_id)
        if not framework:
            return jsonify({"success": False, "message": "Competency framework not found"}), 404
        
        competency, error = framework.add_competency(db.session, data)
        if error:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Competency added successfully",
            "competency": competency.to_dict()
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"Error adding competency: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@rating_scales_bp.route('/api/performance/competency-frameworks/default', methods=['GET'])
def get_default_competency_framework():
    """Get the default competency framework for a company."""
    try:
        company_id = request.args.get('company_id')
        
        if not company_id:
            return jsonify({"success": False, "message": "company_id is required"}), 400
        
        framework = CompetencyFramework.get_default_framework(db.session, company_id)
        if not framework:
            return jsonify({"success": False, "message": "No default competency framework found"}), 404
        
        return jsonify({
            "success": True,
            "framework": framework.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting default competency framework: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@rating_scales_bp.route('/api/performance/competency-frameworks/create-default', methods=['POST'])
def create_default_competency_framework():
    """Create default competency framework for a new company."""
    try:
        data = request.get_json()
        company_id = data.get('company_id')
        created_by = data.get('created_by')
        
        if not company_id:
            return jsonify({"success": False, "message": "company_id is required"}), 400
        
        framework, error = CompetencyFramework.create_default_framework(db.session, company_id, created_by)
        if error:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Default competency framework created successfully",
            "framework": framework.to_dict()
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"Error creating default competency framework: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@rating_scales_bp.route('/api/performance/competencies/<competency_id>', methods=['GET'])
def get_competency(competency_id):
    """Get a specific competency."""
    try:
        competency = Competency.get_competency_by_id(db.session, competency_id)
        if not competency:
            return jsonify({"success": False, "message": "Competency not found"}), 404
        
        return jsonify({
            "success": True,
            "competency": competency.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting competency: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500
