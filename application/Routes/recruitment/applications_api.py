from flask import Blueprint, request, jsonify, current_app
from application.database import db
from application.Models.recruitment.job_application import JobApplication, ApplicationStageHistory
from datetime import datetime, date
import json

applications_bp = Blueprint('applications', __name__)


@applications_bp.route('/api/recruitment/applications', methods=['POST'])
def create_application():
    """Create a new job application."""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['posting_id', 'candidate_id']
        for field in required_fields:
            if field not in data:
                return jsonify({"success": False, "message": f"Missing required field: {field}"}), 400
        
        application, error = JobApplication.create_application(db.session, **data)
        if error:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Job application created successfully",
            "application": application.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error creating job application: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@applications_bp.route('/api/recruitment/applications', methods=['GET'])
def get_applications():
    """Get job applications with filters."""
    try:
        posting_id = request.args.get('posting_id')
        candidate_id = request.args.get('candidate_id')
        status = request.args.get('status')
        company_id = request.args.get('company_id')
        
        if posting_id:
            status_list = status.split(',') if status else None
            applications = JobApplication.get_applications_by_posting(db.session, posting_id, status_list)
        elif candidate_id:
            status_list = status.split(',') if status else None
            applications = JobApplication.get_applications_by_candidate(db.session, candidate_id, status_list)
        elif company_id:
            # Get all applications for company
            query = db.session.query(JobApplication).join('job_posting').filter_by(company_id=company_id)
            if status:
                status_list = status.split(',')
                query = query.filter(JobApplication.status.in_(status_list))
            applications = query.order_by(JobApplication.submitted_date.desc()).all()
        else:
            return jsonify({"success": False, "message": "posting_id, candidate_id, or company_id is required"}), 400
        
        return jsonify({
            "success": True,
            "applications": [app.to_dict() for app in applications]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting job applications: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@applications_bp.route('/api/recruitment/applications/<application_id>', methods=['GET'])
def get_application(application_id):
    """Get a specific job application."""
    try:
        application = JobApplication.get_application_by_id(db.session, application_id)
        if not application:
            return jsonify({"success": False, "message": "Job application not found"}), 404
        
        return jsonify({
            "success": True,
            "application": application.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting job application: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@applications_bp.route('/api/recruitment/applications/<application_id>/stage', methods=['PUT'])
def move_application_stage(application_id):
    """Move application to a new stage."""
    try:
        data = request.get_json()
        stage_name = data.get('stage_name')
        notes = data.get('notes')
        
        if not stage_name:
            return jsonify({"success": False, "message": "stage_name is required"}), 400
        
        application = JobApplication.get_application_by_id(db.session, application_id)
        if not application:
            return jsonify({"success": False, "message": "Job application not found"}), 404
        
        success, error = application.move_to_stage(db.session, stage_name, notes=notes)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": f"Application moved to {stage_name}",
            "application": application.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error moving application stage: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@applications_bp.route('/api/recruitment/applications/<application_id>/reject', methods=['PUT'])
def reject_application(application_id):
    """Reject a job application."""
    try:
        data = request.get_json()
        reason = data.get('reason')
        rejected_by = data.get('rejected_by')
        feedback = data.get('feedback')
        
        if not reason or not rejected_by:
            return jsonify({"success": False, "message": "reason and rejected_by are required"}), 400
        
        application = JobApplication.get_application_by_id(db.session, application_id)
        if not application:
            return jsonify({"success": False, "message": "Job application not found"}), 404
        
        success, error = application.reject_application(db.session, reason, rejected_by, feedback)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Application rejected",
            "application": application.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error rejecting application: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@applications_bp.route('/api/recruitment/applications/<application_id>/offer', methods=['PUT'])
def extend_offer(application_id):
    """Extend job offer to candidate."""
    try:
        data = request.get_json()
        offer_details = data.get('offer_details', {})
        extended_by = data.get('extended_by')
        
        if not extended_by:
            return jsonify({"success": False, "message": "extended_by is required"}), 400
        
        application = JobApplication.get_application_by_id(db.session, application_id)
        if not application:
            return jsonify({"success": False, "message": "Job application not found"}), 404
        
        success, error = application.extend_offer(db.session, offer_details, extended_by)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Job offer extended",
            "application": application.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error extending offer: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@applications_bp.route('/api/recruitment/applications/<application_id>/offer/accept', methods=['PUT'])
def accept_offer(application_id):
    """Accept job offer."""
    try:
        application = JobApplication.get_application_by_id(db.session, application_id)
        if not application:
            return jsonify({"success": False, "message": "Job application not found"}), 404
        
        success, error = application.accept_offer(db.session)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Job offer accepted",
            "application": application.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error accepting offer: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@applications_bp.route('/api/recruitment/applications/<application_id>/offer/decline', methods=['PUT'])
def decline_offer(application_id):
    """Decline job offer."""
    try:
        data = request.get_json()
        reason = data.get('reason', 'No reason provided')
        
        application = JobApplication.get_application_by_id(db.session, application_id)
        if not application:
            return jsonify({"success": False, "message": "Job application not found"}), 404
        
        success, error = application.decline_offer(db.session, reason)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Job offer declined",
            "application": application.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error declining offer: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@applications_bp.route('/api/recruitment/applications/<application_id>/withdraw', methods=['PUT'])
def withdraw_application(application_id):
    """Withdraw job application."""
    try:
        data = request.get_json() or {}
        reason = data.get('reason')
        
        application = JobApplication.get_application_by_id(db.session, application_id)
        if not application:
            return jsonify({"success": False, "message": "Job application not found"}), 404
        
        success, error = application.withdraw_application(db.session, reason)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Application withdrawn",
            "application": application.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error withdrawing application: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@applications_bp.route('/api/recruitment/applications/<application_id>/scores', methods=['PUT'])
def update_application_scores(application_id):
    """Update application scores."""
    try:
        data = request.get_json()
        
        application = JobApplication.get_application_by_id(db.session, application_id)
        if not application:
            return jsonify({"success": False, "message": "Job application not found"}), 404
        
        # Extract score fields
        score_fields = ['overall_score', 'screening_score', 'interview_score', 'assessment_score']
        scores = {field: data[field] for field in score_fields if field in data}
        
        application.update_scores(db.session, **scores)
        
        return jsonify({
            "success": True,
            "message": "Application scores updated",
            "application": application.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error updating application scores: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@applications_bp.route('/api/recruitment/applications/bulk-action', methods=['PUT'])
def bulk_application_action():
    """Perform bulk action on multiple applications."""
    try:
        data = request.get_json()
        application_ids = data.get('application_ids', [])
        action = data.get('action')
        action_data = data.get('action_data', {})
        
        if not application_ids or not action:
            return jsonify({"success": False, "message": "application_ids and action are required"}), 400
        
        results = []
        
        for app_id in application_ids:
            try:
                application = JobApplication.get_application_by_id(db.session, app_id)
                if not application:
                    results.append({"application_id": app_id, "success": False, "message": "Application not found"})
                    continue
                
                if action == 'move_stage':
                    stage_name = action_data.get('stage_name')
                    notes = action_data.get('notes')
                    success, error = application.move_to_stage(db.session, stage_name, notes=notes)
                elif action == 'reject':
                    reason = action_data.get('reason')
                    rejected_by = action_data.get('rejected_by')
                    success, error = application.reject_application(db.session, reason, rejected_by)
                elif action == 'update_scores':
                    scores = action_data.get('scores', {})
                    application.update_scores(db.session, **scores)
                    success, error = True, None
                else:
                    success, error = False, f"Unknown action: {action}"
                
                results.append({
                    "application_id": app_id,
                    "success": success,
                    "message": error if not success else "Action completed successfully"
                })
                
            except Exception as e:
                results.append({
                    "application_id": app_id,
                    "success": False,
                    "message": str(e)
                })
        
        return jsonify({
            "success": True,
            "message": f"Bulk action '{action}' completed",
            "results": results
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error performing bulk action: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@applications_bp.route('/api/recruitment/applications/statistics', methods=['GET'])
def get_application_statistics():
    """Get application statistics."""
    try:
        posting_id = request.args.get('posting_id')
        company_id = request.args.get('company_id')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        # Convert date strings
        date_from_obj = datetime.strptime(date_from, '%Y-%m-%d') if date_from else None
        date_to_obj = datetime.strptime(date_to, '%Y-%m-%d') if date_to else None
        
        statistics = JobApplication.get_application_statistics(
            db.session, posting_id, company_id, date_from_obj, date_to_obj
        )
        
        return jsonify({
            "success": True,
            "statistics": statistics
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting application statistics: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@applications_bp.route('/api/recruitment/applications/pipeline', methods=['GET'])
def get_application_pipeline():
    """Get application pipeline view."""
    try:
        company_id = request.args.get('company_id')
        posting_id = request.args.get('posting_id')
        
        if not company_id:
            return jsonify({"success": False, "message": "company_id is required"}), 400
        
        query = db.session.query(JobApplication).join('job_posting').filter_by(company_id=company_id)
        
        if posting_id:
            query = query.filter(JobApplication.posting_id == posting_id)
        
        applications = query.all()
        
        # Group by stage
        pipeline = {}
        stages = ['SUBMITTED', 'SCREENING', 'INTERVIEWING', 'OFFER', 'HIRED', 'REJECTED', 'WITHDRAWN']
        
        for stage in stages:
            stage_applications = [app for app in applications if app.status == stage]
            pipeline[stage] = {
                'count': len(stage_applications),
                'applications': [app.to_dict() for app in stage_applications]
            }
        
        return jsonify({
            "success": True,
            "pipeline": pipeline,
            "total_applications": len(applications)
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting application pipeline: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500
