from flask import Blueprint, request, jsonify, current_app
from application.database import db
from application.Models.recruitment.candidate_assessment import Candidate<PERSON>sessment
from datetime import datetime, date, timedelta
import json

assessments_bp = Blueprint('assessments', __name__)


@assessments_bp.route('/api/recruitment/assessments', methods=['POST'])
def create_assessment():
    """Create a new candidate assessment."""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['candidate_id', 'assessment_name', 'assessment_type', 'created_by']
        for field in required_fields:
            if field not in data:
                return jsonify({"success": False, "message": f"Missing required field: {field}"}), 400
        
        # Convert date strings to datetime objects
        date_fields = ['due_date']
        for field in date_fields:
            if field in data and data[field]:
                if isinstance(data[field], str):
                    data[field] = datetime.strptime(data[field], '%Y-%m-%dT%H:%M:%S')
        
        assessment, error = CandidateAssessment.create_assessment(db.session, **data)
        if error:
            return jsonify({"success": False, "message": error}), 400
        
        db.session.commit()
        
        return jsonify({
            "success": True,
            "message": "Assessment created successfully",
            "assessment": assessment.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error creating assessment: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@assessments_bp.route('/api/recruitment/assessments', methods=['GET'])
def get_assessments():
    """Get assessments with filters."""
    try:
        candidate_id = request.args.get('candidate_id')
        application_id = request.args.get('application_id')
        status = request.args.get('status')
        assessment_type = request.args.get('assessment_type')
        
        if candidate_id:
            status_list = status.split(',') if status else None
            assessments = CandidateAssessment.get_assessments_by_candidate(db.session, candidate_id, status_list)
        elif application_id:
            assessments = CandidateAssessment.get_assessments_by_application(db.session, application_id)
        else:
            # Get all assessments with optional filters
            query = db.session.query(CandidateAssessment)
            
            if status:
                status_list = status.split(',')
                query = query.filter(CandidateAssessment.status.in_(status_list))
            
            if assessment_type:
                query = query.filter_by(assessment_type=assessment_type)
            
            assessments = query.order_by(CandidateAssessment.assigned_date.desc()).all()
        
        return jsonify({
            "success": True,
            "assessments": [assessment.to_dict() for assessment in assessments]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting assessments: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@assessments_bp.route('/api/recruitment/assessments/<assessment_id>', methods=['GET'])
def get_assessment(assessment_id):
    """Get a specific assessment."""
    try:
        assessment = CandidateAssessment.get_assessment_by_id(db.session, assessment_id)
        if not assessment:
            return jsonify({"success": False, "message": "Assessment not found"}), 404
        
        return jsonify({
            "success": True,
            "assessment": assessment.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting assessment: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@assessments_bp.route('/api/recruitment/assessments/<assessment_id>', methods=['PUT'])
def update_assessment(assessment_id):
    """Update an assessment."""
    try:
        data = request.get_json()
        
        assessment = CandidateAssessment.get_assessment_by_id(db.session, assessment_id)
        if not assessment:
            return jsonify({"success": False, "message": "Assessment not found"}), 404
        
        # Convert date strings
        if 'due_date' in data and isinstance(data['due_date'], str):
            data['due_date'] = datetime.strptime(data['due_date'], '%Y-%m-%dT%H:%M:%S')
        
        # Update assessment fields
        updatable_fields = [
            'assessment_name', 'assessment_type', 'assessment_category', 'provider',
            'platform_url', 'time_limit_minutes', 'max_attempts', 'passing_score',
            'total_possible_score', 'instructions', 'due_date', 'access_link',
            'access_code', 'proctored'
        ]
        
        for field in updatable_fields:
            if field in data:
                setattr(assessment, field, data[field])
        
        # Handle JSON fields
        json_fields = ['questions', 'skills_tested']
        for field in json_fields:
            if field in data:
                if isinstance(data[field], list):
                    setattr(assessment, field, json.dumps(data[field]))
                else:
                    setattr(assessment, field, data[field])
        
        assessment.updated_at = datetime.now()
        db.session.commit()
        
        return jsonify({
            "success": True,
            "message": "Assessment updated successfully",
            "assessment": assessment.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error updating assessment: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@assessments_bp.route('/api/recruitment/assessments/<assessment_id>/start', methods=['PUT'])
def start_assessment(assessment_id):
    """Start an assessment."""
    try:
        assessment = CandidateAssessment.get_assessment_by_id(db.session, assessment_id)
        if not assessment:
            return jsonify({"success": False, "message": "Assessment not found"}), 404
        
        success, error = assessment.start_assessment(db.session)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Assessment started",
            "assessment": assessment.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error starting assessment: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@assessments_bp.route('/api/recruitment/assessments/<assessment_id>/complete', methods=['PUT'])
def complete_assessment(assessment_id):
    """Complete an assessment with results."""
    try:
        data = request.get_json()
        
        assessment = CandidateAssessment.get_assessment_by_id(db.session, assessment_id)
        if not assessment:
            return jsonify({"success": False, "message": "Assessment not found"}), 404
        
        success, error = assessment.complete_assessment(db.session, data)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Assessment completed",
            "assessment": assessment.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error completing assessment: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@assessments_bp.route('/api/recruitment/assessments/<assessment_id>/cancel', methods=['PUT'])
def cancel_assessment(assessment_id):
    """Cancel an assessment."""
    try:
        data = request.get_json() or {}
        reason = data.get('reason')
        
        assessment = CandidateAssessment.get_assessment_by_id(db.session, assessment_id)
        if not assessment:
            return jsonify({"success": False, "message": "Assessment not found"}), 404
        
        success, error = assessment.cancel_assessment(db.session, reason)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Assessment cancelled",
            "assessment": assessment.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error cancelling assessment: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@assessments_bp.route('/api/recruitment/assessments/<assessment_id>/extend-deadline', methods=['PUT'])
def extend_assessment_deadline(assessment_id):
    """Extend assessment deadline."""
    try:
        data = request.get_json()
        new_due_date = data.get('new_due_date')
        reason = data.get('reason')
        
        if not new_due_date:
            return jsonify({"success": False, "message": "new_due_date is required"}), 400
        
        assessment = CandidateAssessment.get_assessment_by_id(db.session, assessment_id)
        if not assessment:
            return jsonify({"success": False, "message": "Assessment not found"}), 404
        
        # Convert date string
        new_due_date_obj = datetime.strptime(new_due_date, '%Y-%m-%dT%H:%M:%S')
        
        success, error = assessment.extend_deadline(db.session, new_due_date_obj, reason)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Assessment deadline extended",
            "assessment": assessment.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error extending assessment deadline: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@assessments_bp.route('/api/recruitment/assessments/<assessment_id>/security-flag', methods=['POST'])
def add_security_flag(assessment_id):
    """Add a security flag to an assessment."""
    try:
        data = request.get_json()
        flag_type = data.get('flag_type')
        description = data.get('description')
        
        if not flag_type or not description:
            return jsonify({"success": False, "message": "flag_type and description are required"}), 400
        
        assessment = CandidateAssessment.get_assessment_by_id(db.session, assessment_id)
        if not assessment:
            return jsonify({"success": False, "message": "Assessment not found"}), 404
        
        assessment.add_security_flag(db.session, flag_type, description)
        
        return jsonify({
            "success": True,
            "message": "Security flag added",
            "assessment": assessment.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error adding security flag: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@assessments_bp.route('/api/recruitment/assessments/overdue', methods=['GET'])
def get_overdue_assessments():
    """Get overdue assessments."""
    try:
        company_id = request.args.get('company_id')
        days_overdue = request.args.get('days_overdue', 0, type=int)
        
        assessments = CandidateAssessment.get_overdue_assessments(db.session, company_id, days_overdue)
        
        return jsonify({
            "success": True,
            "assessments": [assessment.to_dict() for assessment in assessments]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting overdue assessments: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@assessments_bp.route('/api/recruitment/assessments/statistics', methods=['GET'])
def get_assessment_statistics():
    """Get assessment statistics."""
    try:
        company_id = request.args.get('company_id')
        assessment_type = request.args.get('assessment_type')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        # Convert date strings
        date_from_obj = datetime.strptime(date_from, '%Y-%m-%d') if date_from else None
        date_to_obj = datetime.strptime(date_to, '%Y-%m-%d') if date_to else None
        
        statistics = CandidateAssessment.get_assessment_statistics(
            db.session, company_id, assessment_type, date_from_obj, date_to_obj
        )
        
        return jsonify({
            "success": True,
            "statistics": statistics
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting assessment statistics: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@assessments_bp.route('/api/recruitment/assessments/templates', methods=['GET'])
def get_assessment_templates():
    """Get assessment templates."""
    try:
        assessment_type = request.args.get('assessment_type')
        category = request.args.get('category')
        
        # Sample assessment templates
        templates = [
            {
                "template_id": "python_coding",
                "name": "Python Coding Challenge",
                "type": "CODING",
                "category": "PROGRAMMING",
                "time_limit_minutes": 120,
                "passing_score": 70,
                "total_possible_score": 100,
                "skills_tested": ["Python", "Algorithms", "Data Structures"],
                "questions": [
                    {
                        "id": 1,
                        "type": "coding",
                        "title": "Array Manipulation",
                        "description": "Implement a function to find the maximum sum subarray",
                        "difficulty": "MEDIUM"
                    },
                    {
                        "id": 2,
                        "type": "coding",
                        "title": "String Processing",
                        "description": "Write a function to validate parentheses",
                        "difficulty": "EASY"
                    }
                ]
            },
            {
                "template_id": "sales_simulation",
                "name": "Sales Role Play Assessment",
                "type": "BEHAVIORAL",
                "category": "SALES",
                "time_limit_minutes": 45,
                "passing_score": 75,
                "total_possible_score": 100,
                "skills_tested": ["Communication", "Persuasion", "Customer Focus"],
                "questions": [
                    {
                        "id": 1,
                        "type": "scenario",
                        "title": "Difficult Customer",
                        "description": "Handle an upset customer who wants to cancel their subscription",
                        "difficulty": "HARD"
                    }
                ]
            },
            {
                "template_id": "cognitive_ability",
                "name": "Cognitive Ability Test",
                "type": "COGNITIVE",
                "category": "ANALYTICAL",
                "time_limit_minutes": 30,
                "passing_score": 60,
                "total_possible_score": 100,
                "skills_tested": ["Logical Reasoning", "Pattern Recognition", "Numerical Ability"],
                "questions": [
                    {
                        "id": 1,
                        "type": "multiple_choice",
                        "title": "Pattern Recognition",
                        "description": "Complete the sequence: 2, 4, 8, 16, ?",
                        "difficulty": "MEDIUM"
                    }
                ]
            }
        ]
        
        # Filter templates
        if assessment_type:
            templates = [t for t in templates if t['type'] == assessment_type]
        
        if category:
            templates = [t for t in templates if t['category'] == category]
        
        return jsonify({
            "success": True,
            "templates": templates
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting assessment templates: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@assessments_bp.route('/api/recruitment/assessments/bulk-assign', methods=['POST'])
def bulk_assign_assessments():
    """Assign assessments to multiple candidates."""
    try:
        data = request.get_json()
        candidate_ids = data.get('candidate_ids', [])
        assessment_template = data.get('assessment_template')
        assigned_by = data.get('assigned_by')
        
        if not candidate_ids or not assessment_template or not assigned_by:
            return jsonify({"success": False, "message": "candidate_ids, assessment_template, and assigned_by are required"}), 400
        
        results = []
        
        for candidate_id in candidate_ids:
            try:
                assessment_data = {
                    'candidate_id': candidate_id,
                    'assessment_name': assessment_template['name'],
                    'assessment_type': assessment_template['type'],
                    'assessment_category': assessment_template.get('category'),
                    'time_limit_minutes': assessment_template.get('time_limit_minutes'),
                    'passing_score': assessment_template.get('passing_score'),
                    'total_possible_score': assessment_template.get('total_possible_score'),
                    'instructions': assessment_template.get('instructions'),
                    'questions': assessment_template.get('questions', []),
                    'skills_tested': assessment_template.get('skills_tested', []),
                    'created_by': assigned_by
                }
                
                # Set due date if provided
                if 'due_date' in data:
                    assessment_data['due_date'] = datetime.strptime(data['due_date'], '%Y-%m-%dT%H:%M:%S')
                
                assessment, error = CandidateAssessment.create_assessment(db.session, **assessment_data)
                
                if error:
                    results.append({
                        "candidate_id": candidate_id,
                        "success": False,
                        "message": error
                    })
                else:
                    results.append({
                        "candidate_id": candidate_id,
                        "success": True,
                        "assessment_id": str(assessment.assessment_id)
                    })
                    
            except Exception as e:
                results.append({
                    "candidate_id": candidate_id,
                    "success": False,
                    "message": str(e)
                })
        
        db.session.commit()
        
        return jsonify({
            "success": True,
            "message": "Bulk assessment assignment completed",
            "results": results
        }), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error bulk assigning assessments: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500
