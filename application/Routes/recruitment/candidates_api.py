from flask import Blueprint, request, jsonify, current_app
from application.database import db
from application.Models.recruitment.candidate import Candidate
from datetime import datetime, date
import json

candidates_bp = Blueprint('candidates', __name__)


@candidates_bp.route('/api/recruitment/candidates', methods=['POST'])
def create_candidate():
    """Create a new candidate."""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['company_id', 'first_name', 'last_name', 'email']
        for field in required_fields:
            if field not in data:
                return jsonify({"success": False, "message": f"Missing required field: {field}"}), 400
        
        # Convert date strings to date objects
        date_fields = ['availability_date', 'last_contact_date', 'next_contact_date', 'data_retention_date']
        for field in date_fields:
            if field in data and data[field]:
                if isinstance(data[field], str):
                    data[field] = datetime.strptime(data[field], '%Y-%m-%d').date()
        
        candidate, error = Candidate.create_candidate(db.session, **data)
        if error:
            return jsonify({"success": False, "message": error}), 400
        
        db.session.commit()
        
        return jsonify({
            "success": True,
            "message": "Candidate created successfully",
            "candidate": candidate.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error creating candidate: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@candidates_bp.route('/api/recruitment/candidates', methods=['GET'])
def get_candidates():
    """Get candidates with optional filters."""
    try:
        company_id = request.args.get('company_id')
        status = request.args.get('status')
        talent_pool = request.args.get('talent_pool')
        source = request.args.get('source')
        pipeline_stage = request.args.get('pipeline_stage')
        
        if not company_id:
            return jsonify({"success": False, "message": "company_id is required"}), 400
        
        status_list = status.split(',') if status else None
        candidates = Candidate.get_candidates_by_company(
            db.session, company_id, status_list, talent_pool, source
        )
        
        # Additional filter by pipeline stage
        if pipeline_stage:
            candidates = [c for c in candidates if c.pipeline_stage == pipeline_stage]
        
        return jsonify({
            "success": True,
            "candidates": [candidate.to_dict() for candidate in candidates]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting candidates: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@candidates_bp.route('/api/recruitment/candidates/search', methods=['GET'])
def search_candidates():
    """Search candidates with advanced filters."""
    try:
        company_id = request.args.get('company_id')
        search_term = request.args.get('q', '')
        skills = request.args.get('skills')
        location = request.args.get('location')
        experience_min = request.args.get('experience_min', type=int)
        experience_max = request.args.get('experience_max', type=int)
        education = request.args.get('education')
        availability = request.args.get('availability')
        
        if not company_id:
            return jsonify({"success": False, "message": "company_id is required"}), 400
        
        # Parse skills filter
        skills_list = skills.split(',') if skills else None
        
        candidates = Candidate.search_candidates(
            db.session, company_id, search_term, skills_list, location, experience_min, experience_max
        )
        
        # Additional filters
        if education:
            candidates = [c for c in candidates if c.highest_education == education]
        
        if availability:
            if availability == 'IMMEDIATE':
                candidates = [c for c in candidates if c.notice_period == 'IMMEDIATE']
            elif availability == 'AVAILABLE':
                candidates = [c for c in candidates if c.availability_date and c.availability_date <= date.today()]
        
        return jsonify({
            "success": True,
            "candidates": [candidate.to_dict() for candidate in candidates],
            "total_results": len(candidates)
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error searching candidates: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@candidates_bp.route('/api/recruitment/candidates/<candidate_id>', methods=['GET'])
def get_candidate(candidate_id):
    """Get a specific candidate."""
    try:
        candidate = Candidate.get_candidate_by_id(db.session, candidate_id)
        if not candidate:
            return jsonify({"success": False, "message": "Candidate not found"}), 404
        
        return jsonify({
            "success": True,
            "candidate": candidate.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting candidate: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@candidates_bp.route('/api/recruitment/candidates/<candidate_id>', methods=['PUT'])
def update_candidate(candidate_id):
    """Update a candidate."""
    try:
        data = request.get_json()
        
        candidate = Candidate.get_candidate_by_id(db.session, candidate_id)
        if not candidate:
            return jsonify({"success": False, "message": "Candidate not found"}), 404
        
        # Convert date strings to date objects
        date_fields = ['availability_date', 'last_contact_date', 'next_contact_date', 'data_retention_date']
        for field in date_fields:
            if field in data and data[field]:
                if isinstance(data[field], str):
                    data[field] = datetime.strptime(data[field], '%Y-%m-%d').date()
        
        # Update candidate fields
        updatable_fields = [
            'first_name', 'last_name', 'email', 'phone', 'address_line1', 'address_line2',
            'city', 'state_province', 'postal_code', 'country', 'current_job_title',
            'current_company', 'current_salary', 'expected_salary', 'currency',
            'years_of_experience', 'highest_education', 'field_of_study', 'university',
            'graduation_year', 'availability_date', 'notice_period', 'work_authorization',
            'willing_to_relocate', 'remote_work_preference', 'linkedin_url', 'portfolio_url',
            'github_url', 'status', 'source', 'source_details', 'talent_pool',
            'pipeline_stage', 'last_contact_date', 'next_contact_date',
            'communication_preference', 'best_time_to_contact', 'timezone',
            'gdpr_consent', 'marketing_consent', 'data_retention_date', 'notes',
            'referred_by', 'referral_bonus_eligible'
        ]
        
        for field in updatable_fields:
            if field in data:
                setattr(candidate, field, data[field])
        
        # Handle JSON fields
        json_fields = ['skills', 'certifications', 'languages', 'preferred_locations', 'other_profiles', 'tags']
        for field in json_fields:
            if field in data:
                if isinstance(data[field], (list, dict)):
                    setattr(candidate, field, json.dumps(data[field]))
                else:
                    setattr(candidate, field, data[field])
        
        candidate.updated_at = datetime.now()
        db.session.commit()
        
        return jsonify({
            "success": True,
            "message": "Candidate updated successfully",
            "candidate": candidate.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error updating candidate: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@candidates_bp.route('/api/recruitment/candidates/<candidate_id>/contact', methods=['PUT'])
def update_contact_date(candidate_id):
    """Update candidate contact date."""
    try:
        data = request.get_json() or {}
        next_contact_date = data.get('next_contact_date')
        
        candidate = Candidate.get_candidate_by_id(db.session, candidate_id)
        if not candidate:
            return jsonify({"success": False, "message": "Candidate not found"}), 404
        
        if next_contact_date:
            next_contact_date = datetime.strptime(next_contact_date, '%Y-%m-%d').date()
        
        candidate.update_contact_date(db.session, next_contact_date)
        
        return jsonify({
            "success": True,
            "message": "Contact date updated successfully",
            "candidate": candidate.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error updating contact date: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@candidates_bp.route('/api/recruitment/candidates/<candidate_id>/pipeline-stage', methods=['PUT'])
def move_pipeline_stage(candidate_id):
    """Move candidate to a different pipeline stage."""
    try:
        data = request.get_json()
        stage = data.get('stage')
        
        if not stage:
            return jsonify({"success": False, "message": "stage is required"}), 400
        
        candidate = Candidate.get_candidate_by_id(db.session, candidate_id)
        if not candidate:
            return jsonify({"success": False, "message": "Candidate not found"}), 404
        
        candidate.move_to_pipeline_stage(db.session, stage)
        
        return jsonify({
            "success": True,
            "message": f"Candidate moved to {stage} stage",
            "candidate": candidate.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error moving candidate to pipeline stage: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@candidates_bp.route('/api/recruitment/candidates/<candidate_id>/tags', methods=['POST'])
def add_candidate_tag(candidate_id):
    """Add a tag to a candidate."""
    try:
        data = request.get_json()
        tag = data.get('tag')
        
        if not tag:
            return jsonify({"success": False, "message": "tag is required"}), 400
        
        candidate = Candidate.get_candidate_by_id(db.session, candidate_id)
        if not candidate:
            return jsonify({"success": False, "message": "Candidate not found"}), 404
        
        candidate.add_tag(db.session, tag)
        
        return jsonify({
            "success": True,
            "message": "Tag added successfully",
            "candidate": candidate.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error adding candidate tag: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@candidates_bp.route('/api/recruitment/candidates/<candidate_id>/tags/<tag>', methods=['DELETE'])
def remove_candidate_tag(candidate_id, tag):
    """Remove a tag from a candidate."""
    try:
        candidate = Candidate.get_candidate_by_id(db.session, candidate_id)
        if not candidate:
            return jsonify({"success": False, "message": "Candidate not found"}), 404
        
        candidate.remove_tag(db.session, tag)
        
        return jsonify({
            "success": True,
            "message": "Tag removed successfully",
            "candidate": candidate.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error removing candidate tag: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@candidates_bp.route('/api/recruitment/candidates/needing-contact', methods=['GET'])
def get_candidates_needing_contact():
    """Get candidates that need to be contacted."""
    try:
        company_id = request.args.get('company_id')
        days_overdue = request.args.get('days_overdue', 0, type=int)
        
        if not company_id:
            return jsonify({"success": False, "message": "company_id is required"}), 400
        
        candidates = Candidate.get_candidates_needing_contact(db.session, company_id, days_overdue)
        
        return jsonify({
            "success": True,
            "candidates": [candidate.to_dict() for candidate in candidates]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting candidates needing contact: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@candidates_bp.route('/api/recruitment/candidates/talent-pipeline', methods=['GET'])
def get_talent_pipeline():
    """Get talent pipeline summary."""
    try:
        company_id = request.args.get('company_id')
        
        if not company_id:
            return jsonify({"success": False, "message": "company_id is required"}), 400
        
        pipeline_summary = Candidate.get_talent_pipeline_summary(db.session, company_id)
        
        return jsonify({
            "success": True,
            "pipeline_summary": pipeline_summary
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting talent pipeline: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@candidates_bp.route('/api/recruitment/candidates/import', methods=['POST'])
def import_candidates():
    """Import candidates from CSV or other sources."""
    try:
        data = request.get_json()
        company_id = data.get('company_id')
        candidates_data = data.get('candidates', [])
        source = data.get('source', 'IMPORT')
        
        if not company_id or not candidates_data:
            return jsonify({"success": False, "message": "company_id and candidates data are required"}), 400
        
        imported_count = 0
        errors = []
        
        for candidate_data in candidates_data:
            try:
                candidate_data['company_id'] = company_id
                candidate_data['source'] = source
                
                candidate, error = Candidate.create_candidate(db.session, **candidate_data)
                if error:
                    errors.append(f"Row {imported_count + 1}: {error}")
                else:
                    imported_count += 1
                    
            except Exception as e:
                errors.append(f"Row {imported_count + 1}: {str(e)}")
        
        db.session.commit()
        
        return jsonify({
            "success": True,
            "message": f"Imported {imported_count} candidates",
            "imported_count": imported_count,
            "errors": errors
        }), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error importing candidates: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@candidates_bp.route('/api/recruitment/candidates/export', methods=['GET'])
def export_candidates():
    """Export candidates data."""
    try:
        company_id = request.args.get('company_id')
        status = request.args.get('status')
        format_type = request.args.get('format', 'json')  # json, csv
        
        if not company_id:
            return jsonify({"success": False, "message": "company_id is required"}), 400
        
        status_list = status.split(',') if status else None
        candidates = Candidate.get_candidates_by_company(db.session, company_id, status_list)
        
        if format_type == 'csv':
            # TODO: Implement CSV export
            return jsonify({"success": False, "message": "CSV export not yet implemented"}), 501
        
        # JSON export
        export_data = []
        for candidate in candidates:
            candidate_dict = candidate.to_dict()
            # Remove sensitive data for export
            candidate_dict.pop('gdpr_consent', None)
            candidate_dict.pop('data_retention_date', None)
            export_data.append(candidate_dict)
        
        return jsonify({
            "success": True,
            "candidates": export_data,
            "total_exported": len(export_data)
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error exporting candidates: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@candidates_bp.route('/api/recruitment/candidates/statistics', methods=['GET'])
def get_candidate_statistics():
    """Get candidate statistics."""
    try:
        company_id = request.args.get('company_id')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        if not company_id:
            return jsonify({"success": False, "message": "company_id is required"}), 400
        
        query = db.session.query(Candidate).filter_by(company_id=company_id)
        
        if date_from:
            query = query.filter(Candidate.created_at >= datetime.strptime(date_from, '%Y-%m-%d'))
        
        if date_to:
            query = query.filter(Candidate.created_at <= datetime.strptime(date_to, '%Y-%m-%d'))
        
        candidates = query.all()
        
        # Calculate statistics
        total_candidates = len(candidates)
        
        status_breakdown = {}
        source_breakdown = {}
        pipeline_breakdown = {}
        location_breakdown = {}
        experience_breakdown = {}
        
        for candidate in candidates:
            # Status breakdown
            status = candidate.status
            status_breakdown[status] = status_breakdown.get(status, 0) + 1
            
            # Source breakdown
            source = candidate.source or 'Unknown'
            source_breakdown[source] = source_breakdown.get(source, 0) + 1
            
            # Pipeline breakdown
            pipeline = candidate.pipeline_stage or 'Unknown'
            pipeline_breakdown[pipeline] = pipeline_breakdown.get(pipeline, 0) + 1
            
            # Location breakdown
            location = candidate.city or candidate.country or 'Unknown'
            location_breakdown[location] = location_breakdown.get(location, 0) + 1
            
            # Experience breakdown
            exp = candidate.years_of_experience
            if exp is None:
                exp_range = 'Unknown'
            elif exp < 2:
                exp_range = '0-2 years'
            elif exp < 5:
                exp_range = '2-5 years'
            elif exp < 10:
                exp_range = '5-10 years'
            else:
                exp_range = '10+ years'
            
            experience_breakdown[exp_range] = experience_breakdown.get(exp_range, 0) + 1
        
        # Active candidates
        active_candidates = status_breakdown.get('ACTIVE', 0)
        
        # Candidates needing contact
        needing_contact = len(Candidate.get_candidates_needing_contact(db.session, company_id))
        
        return jsonify({
            "success": True,
            "statistics": {
                "total_candidates": total_candidates,
                "active_candidates": active_candidates,
                "needing_contact": needing_contact,
                "status_breakdown": status_breakdown,
                "source_breakdown": source_breakdown,
                "pipeline_breakdown": pipeline_breakdown,
                "location_breakdown": location_breakdown,
                "experience_breakdown": experience_breakdown
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting candidate statistics: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@candidates_bp.route('/api/recruitment/candidates/duplicate-check', methods=['POST'])
def check_duplicate_candidate():
    """Check for duplicate candidates by email."""
    try:
        data = request.get_json()
        company_id = data.get('company_id')
        email = data.get('email')
        
        if not company_id or not email:
            return jsonify({"success": False, "message": "company_id and email are required"}), 400
        
        existing_candidate = Candidate.get_candidate_by_email(db.session, company_id, email)
        
        if existing_candidate:
            return jsonify({
                "success": True,
                "is_duplicate": True,
                "existing_candidate": existing_candidate.to_dict()
            }), 200
        else:
            return jsonify({
                "success": True,
                "is_duplicate": False
            }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error checking duplicate candidate: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500
