from flask import Blueprint, request, jsonify, current_app, send_file
from application.database import db
from application.Models.recruitment.candidate_document import CandidateDocument
from datetime import datetime, date
import json
import os
from werkzeug.utils import secure_filename

documents_bp = Blueprint('documents', __name__)


@documents_bp.route('/api/recruitment/documents', methods=['POST'])
def upload_document():
    """Upload a candidate document."""
    try:
        # Check if file is in request
        if 'file' not in request.files:
            return jsonify({"success": False, "message": "No file provided"}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({"success": False, "message": "No file selected"}), 400
        
        # Get form data
        candidate_id = request.form.get('candidate_id')
        application_id = request.form.get('application_id')
        document_type = request.form.get('document_type')
        document_name = request.form.get('document_name')
        uploaded_by = request.form.get('uploaded_by')
        
        if not candidate_id or not document_type or not uploaded_by:
            return jsonify({"success": False, "message": "candidate_id, document_type, and uploaded_by are required"}), 400
        
        # Secure filename
        filename = secure_filename(file.filename)
        file_extension = os.path.splitext(filename)[1].lower()
        
        # Validate file type
        allowed_extensions = {'.pdf', '.doc', '.docx', '.txt', '.jpg', '.jpeg', '.png'}
        if file_extension not in allowed_extensions:
            return jsonify({"success": False, "message": "File type not allowed"}), 400
        
        # Create document record
        document_data = {
            'candidate_id': candidate_id,
            'application_id': application_id,
            'document_name': document_name or filename,
            'document_type': document_type,
            'original_filename': filename,
            'file_extension': file_extension,
            'file_type': file.content_type,
            'file_size': len(file.read()),
            'uploaded_by': uploaded_by
        }
        
        # Reset file pointer
        file.seek(0)
        
        # TODO: Implement actual file storage (Azure Blob, S3, etc.)
        # For now, save locally
        upload_folder = os.path.join(current_app.config.get('UPLOAD_FOLDER', 'uploads'), 'documents')
        os.makedirs(upload_folder, exist_ok=True)
        
        file_path = os.path.join(upload_folder, f"{candidate_id}_{filename}")
        file.save(file_path)
        
        document_data['file_path'] = file_path
        document_data['storage_provider'] = 'LOCAL'
        
        document, error = CandidateDocument.create_document(db.session, **document_data)
        if error:
            return jsonify({"success": False, "message": error}), 400
        
        db.session.commit()
        
        return jsonify({
            "success": True,
            "message": "Document uploaded successfully",
            "document": document.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error uploading document: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@documents_bp.route('/api/recruitment/documents', methods=['GET'])
def get_documents():
    """Get documents with filters."""
    try:
        candidate_id = request.args.get('candidate_id')
        application_id = request.args.get('application_id')
        document_type = request.args.get('document_type')
        latest_only = request.args.get('latest_only', 'true').lower() == 'true'
        
        if candidate_id:
            documents = CandidateDocument.get_documents_by_candidate(
                db.session, candidate_id, document_type, latest_only
            )
        elif application_id:
            documents = CandidateDocument.get_documents_by_application(db.session, application_id)
        else:
            return jsonify({"success": False, "message": "candidate_id or application_id is required"}), 400
        
        return jsonify({
            "success": True,
            "documents": [doc.to_dict() for doc in documents]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting documents: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@documents_bp.route('/api/recruitment/documents/<document_id>', methods=['GET'])
def get_document(document_id):
    """Get a specific document."""
    try:
        document = CandidateDocument.get_document_by_id(db.session, document_id)
        if not document:
            return jsonify({"success": False, "message": "Document not found"}), 404
        
        return jsonify({
            "success": True,
            "document": document.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting document: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@documents_bp.route('/api/recruitment/documents/<document_id>/download', methods=['GET'])
def download_document(document_id):
    """Download a document file."""
    try:
        document = CandidateDocument.get_document_by_id(db.session, document_id)
        if not document:
            return jsonify({"success": False, "message": "Document not found"}), 404
        
        if not os.path.exists(document.file_path):
            return jsonify({"success": False, "message": "File not found on disk"}), 404
        
        return send_file(
            document.file_path,
            as_attachment=True,
            download_name=document.original_filename,
            mimetype=document.file_type
        )
        
    except Exception as e:
        current_app.logger.error(f"Error downloading document: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@documents_bp.route('/api/recruitment/documents/<document_id>/parse', methods=['PUT'])
def parse_document(document_id):
    """Parse document content."""
    try:
        data = request.get_json() or {}
        parsed_content = data.get('parsed_content')
        parsed_data = data.get('parsed_data')
        
        document = CandidateDocument.get_document_by_id(db.session, document_id)
        if not document:
            return jsonify({"success": False, "message": "Document not found"}), 404
        
        success, error = document.parse_document(db.session, parsed_content, parsed_data)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Document parsed successfully",
            "document": document.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error parsing document: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@documents_bp.route('/api/recruitment/documents/<document_id>/verify', methods=['PUT'])
def verify_document(document_id):
    """Verify a document."""
    try:
        data = request.get_json()
        verified_by = data.get('verified_by')
        verification_status = data.get('verification_status')
        notes = data.get('notes')
        
        if not verified_by or not verification_status:
            return jsonify({"success": False, "message": "verified_by and verification_status are required"}), 400
        
        document = CandidateDocument.get_document_by_id(db.session, document_id)
        if not document:
            return jsonify({"success": False, "message": "Document not found"}), 404
        
        success, error = document.verify_document(db.session, verified_by, verification_status, notes)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Document verification updated",
            "document": document.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error verifying document: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@documents_bp.route('/api/recruitment/documents/<document_id>/archive', methods=['PUT'])
def archive_document(document_id):
    """Archive a document."""
    try:
        document = CandidateDocument.get_document_by_id(db.session, document_id)
        if not document:
            return jsonify({"success": False, "message": "Document not found"}), 404
        
        success, error = document.archive_document(db.session)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Document archived",
            "document": document.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error archiving document: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@documents_bp.route('/api/recruitment/documents/<document_id>', methods=['DELETE'])
def delete_document(document_id):
    """Delete a document."""
    try:
        data = request.get_json() or {}
        soft_delete = data.get('soft_delete', True)
        
        document = CandidateDocument.get_document_by_id(db.session, document_id)
        if not document:
            return jsonify({"success": False, "message": "Document not found"}), 404
        
        success, error = document.delete_document(db.session, soft_delete)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Document deleted"
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error deleting document: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@documents_bp.route('/api/recruitment/documents/<document_id>/ai-analysis', methods=['PUT'])
def update_ai_analysis(document_id):
    """Update AI analysis results for a document."""
    try:
        data = request.get_json()
        
        document = CandidateDocument.get_document_by_id(db.session, document_id)
        if not document:
            return jsonify({"success": False, "message": "Document not found"}), 404
        
        success, error = document.update_ai_analysis(db.session, data)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "AI analysis updated",
            "document": document.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error updating AI analysis: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@documents_bp.route('/api/recruitment/documents/needing-parsing', methods=['GET'])
def get_documents_needing_parsing():
    """Get documents that need parsing."""
    try:
        limit = request.args.get('limit', 50, type=int)
        
        documents = CandidateDocument.get_documents_needing_parsing(db.session, limit)
        
        return jsonify({
            "success": True,
            "documents": [doc.to_dict() for doc in documents]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting documents needing parsing: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@documents_bp.route('/api/recruitment/documents/needing-verification', methods=['GET'])
def get_documents_needing_verification():
    """Get documents that need verification."""
    try:
        company_id = request.args.get('company_id')
        
        documents = CandidateDocument.get_documents_needing_verification(db.session, company_id)
        
        return jsonify({
            "success": True,
            "documents": [doc.to_dict() for doc in documents]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting documents needing verification: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@documents_bp.route('/api/recruitment/documents/expiring', methods=['GET'])
def get_expiring_documents():
    """Get documents expiring soon."""
    try:
        days_ahead = request.args.get('days_ahead', 30, type=int)
        
        documents = CandidateDocument.get_expiring_documents(db.session, days_ahead)
        
        return jsonify({
            "success": True,
            "documents": [doc.to_dict() for doc in documents]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting expiring documents: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@documents_bp.route('/api/recruitment/documents/cleanup-expired', methods=['POST'])
def cleanup_expired_documents():
    """Clean up expired documents."""
    try:
        deleted_count = CandidateDocument.cleanup_expired_documents(db.session)
        
        return jsonify({
            "success": True,
            "message": f"Cleaned up {deleted_count} expired documents",
            "deleted_count": deleted_count
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error cleaning up expired documents: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@documents_bp.route('/api/recruitment/documents/statistics', methods=['GET'])
def get_document_statistics():
    """Get document statistics."""
    try:
        candidate_id = request.args.get('candidate_id')
        company_id = request.args.get('company_id')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        # Convert date strings
        date_from_obj = datetime.strptime(date_from, '%Y-%m-%d') if date_from else None
        date_to_obj = datetime.strptime(date_to, '%Y-%m-%d') if date_to else None
        
        statistics = CandidateDocument.get_document_statistics(
            db.session, candidate_id, company_id, date_from_obj, date_to_obj
        )
        
        return jsonify({
            "success": True,
            "statistics": statistics
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting document statistics: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@documents_bp.route('/api/recruitment/documents/bulk-verify', methods=['PUT'])
def bulk_verify_documents():
    """Bulk verify multiple documents."""
    try:
        data = request.get_json()
        document_ids = data.get('document_ids', [])
        verified_by = data.get('verified_by')
        verification_status = data.get('verification_status')
        notes = data.get('notes')
        
        if not document_ids or not verified_by or not verification_status:
            return jsonify({"success": False, "message": "document_ids, verified_by, and verification_status are required"}), 400
        
        results = []
        
        for doc_id in document_ids:
            try:
                document = CandidateDocument.get_document_by_id(db.session, doc_id)
                if not document:
                    results.append({"document_id": doc_id, "success": False, "message": "Document not found"})
                    continue
                
                success, error = document.verify_document(db.session, verified_by, verification_status, notes)
                
                results.append({
                    "document_id": doc_id,
                    "success": success,
                    "message": error if not success else "Verification updated successfully"
                })
                
            except Exception as e:
                results.append({
                    "document_id": doc_id,
                    "success": False,
                    "message": str(e)
                })
        
        return jsonify({
            "success": True,
            "message": "Bulk verification completed",
            "results": results
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error bulk verifying documents: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@documents_bp.route('/api/recruitment/documents/azure-blob-integration', methods=['POST'])
def azure_blob_integration():
    """Handle Azure Blob Storage integration."""
    try:
        data = request.get_json()
        document_id = data.get('document_id')
        blob_url = data.get('blob_url')
        container_name = data.get('container_name')
        blob_name = data.get('blob_name')
        
        if not all([document_id, blob_url, container_name, blob_name]):
            return jsonify({"success": False, "message": "Missing required Azure Blob parameters"}), 400
        
        document = CandidateDocument.get_document_by_id(db.session, document_id)
        if not document:
            return jsonify({"success": False, "message": "Document not found"}), 404
        
        # Update document with Azure Blob details
        document.storage_provider = 'AZURE_BLOB'
        document.storage_bucket = container_name
        document.storage_key = blob_name
        document.storage_url = blob_url
        document.updated_at = datetime.now()
        
        db.session.commit()
        
        return jsonify({
            "success": True,
            "message": "Document linked with Azure Blob Storage successfully",
            "document": document.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error with Azure Blob integration: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500
