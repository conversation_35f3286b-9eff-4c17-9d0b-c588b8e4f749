from flask import Blueprint, request, jsonify, current_app
from application.database import db
from application.Models.recruitment.interview import Interview, InterviewFeedback
from datetime import datetime, date, timedelta
import json

interviews_bp = Blueprint('interviews', __name__)


@interviews_bp.route('/api/recruitment/interviews', methods=['POST'])
def create_interview():
    """Schedule a new interview."""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['application_id', 'candidate_id', 'interview_type', 'scheduled_date', 'primary_interviewer', 'created_by']
        for field in required_fields:
            if field not in data:
                return jsonify({"success": False, "message": f"Missing required field: {field}"}), 400
        
        # Convert scheduled_date string to datetime
        if isinstance(data['scheduled_date'], str):
            data['scheduled_date'] = datetime.strptime(data['scheduled_date'], '%Y-%m-%dT%H:%M:%S')
        
        interview, error = Interview.create_interview(db.session, **data)
        if error:
            return jsonify({"success": False, "message": error}), 400
        
        db.session.commit()
        
        return jsonify({
            "success": True,
            "message": "Interview scheduled successfully",
            "interview": interview.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error creating interview: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@interviews_bp.route('/api/recruitment/interviews', methods=['GET'])
def get_interviews():
    """Get interviews with filters."""
    try:
        application_id = request.args.get('application_id')
        candidate_id = request.args.get('candidate_id')
        interviewer_id = request.args.get('interviewer_id')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        status = request.args.get('status')
        
        if application_id:
            interviews = Interview.get_interviews_by_application(db.session, application_id)
        elif candidate_id:
            interviews = Interview.get_interviews_by_candidate(db.session, candidate_id)
        elif interviewer_id:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d') if date_from else None
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d') if date_to else None
            interviews = Interview.get_interviews_by_interviewer(db.session, interviewer_id, date_from_obj, date_to_obj)
        else:
            # Get all interviews with optional filters
            query = db.session.query(Interview)
            
            if date_from:
                query = query.filter(Interview.scheduled_date >= datetime.strptime(date_from, '%Y-%m-%d'))
            
            if date_to:
                query = query.filter(Interview.scheduled_date <= datetime.strptime(date_to, '%Y-%m-%d'))
            
            if status:
                status_list = status.split(',')
                query = query.filter(Interview.status.in_(status_list))
            
            interviews = query.order_by(Interview.scheduled_date).all()
        
        return jsonify({
            "success": True,
            "interviews": [interview.to_dict() for interview in interviews]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting interviews: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@interviews_bp.route('/api/recruitment/interviews/<interview_id>', methods=['GET'])
def get_interview(interview_id):
    """Get a specific interview."""
    try:
        interview = Interview.get_interview_by_id(db.session, interview_id)
        if not interview:
            return jsonify({"success": False, "message": "Interview not found"}), 404
        
        return jsonify({
            "success": True,
            "interview": interview.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting interview: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@interviews_bp.route('/api/recruitment/interviews/<interview_id>', methods=['PUT'])
def update_interview(interview_id):
    """Update an interview."""
    try:
        data = request.get_json()
        
        interview = Interview.get_interview_by_id(db.session, interview_id)
        if not interview:
            return jsonify({"success": False, "message": "Interview not found"}), 404
        
        # Convert scheduled_date if provided
        if 'scheduled_date' in data and isinstance(data['scheduled_date'], str):
            data['scheduled_date'] = datetime.strptime(data['scheduled_date'], '%Y-%m-%dT%H:%M:%S')
        
        # Update interview fields
        updatable_fields = [
            'interview_type', 'interview_title', 'interview_description', 'scheduled_date',
            'duration_minutes', 'timezone', 'location', 'meeting_platform', 'meeting_url',
            'meeting_id', 'meeting_password', 'dial_in_number', 'primary_interviewer'
        ]
        
        for field in updatable_fields:
            if field in data:
                setattr(interview, field, data[field])
        
        # Handle JSON fields
        json_fields = ['interview_panel', 'questions', 'evaluation_criteria']
        for field in json_fields:
            if field in data:
                if isinstance(data[field], list):
                    setattr(interview, field, json.dumps(data[field]))
                else:
                    setattr(interview, field, data[field])
        
        interview.updated_at = datetime.now()
        db.session.commit()
        
        return jsonify({
            "success": True,
            "message": "Interview updated successfully",
            "interview": interview.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error updating interview: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@interviews_bp.route('/api/recruitment/interviews/<interview_id>/reschedule', methods=['PUT'])
def reschedule_interview(interview_id):
    """Reschedule an interview."""
    try:
        data = request.get_json()
        new_date = data.get('new_date')
        reason = data.get('reason')
        
        if not new_date:
            return jsonify({"success": False, "message": "new_date is required"}), 400
        
        interview = Interview.get_interview_by_id(db.session, interview_id)
        if not interview:
            return jsonify({"success": False, "message": "Interview not found"}), 404
        
        # Convert date string to datetime
        new_date_obj = datetime.strptime(new_date, '%Y-%m-%dT%H:%M:%S')
        
        success, error = interview.reschedule(db.session, new_date_obj, reason)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Interview rescheduled successfully",
            "interview": interview.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error rescheduling interview: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@interviews_bp.route('/api/recruitment/interviews/<interview_id>/cancel', methods=['PUT'])
def cancel_interview(interview_id):
    """Cancel an interview."""
    try:
        data = request.get_json()
        reason = data.get('reason', 'No reason provided')
        cancelled_by = data.get('cancelled_by', 'COMPANY')
        
        interview = Interview.get_interview_by_id(db.session, interview_id)
        if not interview:
            return jsonify({"success": False, "message": "Interview not found"}), 404
        
        success, error = interview.cancel(db.session, reason, cancelled_by)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Interview cancelled",
            "interview": interview.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error cancelling interview: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@interviews_bp.route('/api/recruitment/interviews/<interview_id>/complete', methods=['PUT'])
def complete_interview(interview_id):
    """Mark interview as completed."""
    try:
        interview = Interview.get_interview_by_id(db.session, interview_id)
        if not interview:
            return jsonify({"success": False, "message": "Interview not found"}), 404
        
        success, error = interview.mark_completed(db.session)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Interview marked as completed",
            "interview": interview.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error completing interview: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@interviews_bp.route('/api/recruitment/interviews/<interview_id>/no-show', methods=['PUT'])
def mark_no_show(interview_id):
    """Mark candidate as no-show."""
    try:
        interview = Interview.get_interview_by_id(db.session, interview_id)
        if not interview:
            return jsonify({"success": False, "message": "Interview not found"}), 404
        
        success, error = interview.mark_no_show(db.session)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Interview marked as no-show",
            "interview": interview.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error marking no-show: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@interviews_bp.route('/api/recruitment/interviews/<interview_id>/feedback', methods=['POST'])
def submit_interview_feedback(interview_id):
    """Submit interview feedback."""
    try:
        data = request.get_json()
        submitted_by = data.get('submitted_by')
        
        if not submitted_by:
            return jsonify({"success": False, "message": "submitted_by is required"}), 400
        
        interview = Interview.get_interview_by_id(db.session, interview_id)
        if not interview:
            return jsonify({"success": False, "message": "Interview not found"}), 404
        
        success, error = interview.submit_feedback(db.session, data, submitted_by)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Interview feedback submitted",
            "interview": interview.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error submitting interview feedback: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@interviews_bp.route('/api/recruitment/interviews/<interview_id>/confirm', methods=['PUT'])
def confirm_interview_attendance(interview_id):
    """Confirm candidate attendance."""
    try:
        interview = Interview.get_interview_by_id(db.session, interview_id)
        if not interview:
            return jsonify({"success": False, "message": "Interview not found"}), 404
        
        interview.confirm_attendance(db.session)
        
        return jsonify({
            "success": True,
            "message": "Interview attendance confirmed",
            "interview": interview.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error confirming attendance: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@interviews_bp.route('/api/recruitment/interviews/upcoming', methods=['GET'])
def get_upcoming_interviews():
    """Get upcoming interviews."""
    try:
        interviewer_id = request.args.get('interviewer_id')
        days_ahead = request.args.get('days_ahead', 7, type=int)
        
        interviews = Interview.get_upcoming_interviews(db.session, interviewer_id, days_ahead)
        
        return jsonify({
            "success": True,
            "interviews": [interview.to_dict() for interview in interviews]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting upcoming interviews: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@interviews_bp.route('/api/recruitment/interviews/statistics', methods=['GET'])
def get_interview_statistics():
    """Get interview statistics."""
    try:
        company_id = request.args.get('company_id')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        # Convert date strings
        date_from_obj = datetime.strptime(date_from, '%Y-%m-%d') if date_from else None
        date_to_obj = datetime.strptime(date_to, '%Y-%m-%d') if date_to else None
        
        statistics = Interview.get_interview_statistics(
            db.session, company_id, date_from_obj, date_to_obj
        )
        
        return jsonify({
            "success": True,
            "statistics": statistics
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting interview statistics: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@interviews_bp.route('/api/recruitment/interviews/calendly-integration', methods=['POST'])
def calendly_integration():
    """Handle Calendly integration for interview scheduling."""
    try:
        data = request.get_json()
        interview_id = data.get('interview_id')
        calendly_event_uri = data.get('calendly_event_uri')
        calendly_event_id = data.get('calendly_event_id')
        
        if not all([interview_id, calendly_event_uri]):
            return jsonify({"success": False, "message": "Missing required Calendly parameters"}), 400
        
        interview = Interview.get_interview_by_id(db.session, interview_id)
        if not interview:
            return jsonify({"success": False, "message": "Interview not found"}), 404
        
        # Update interview with Calendly details
        interview.calendly_event_id = calendly_event_id
        interview.meeting_url = calendly_event_uri
        interview.status = 'SCHEDULED'
        interview.confirmation_received = True
        interview.updated_at = datetime.now()
        
        db.session.commit()
        
        return jsonify({
            "success": True,
            "message": "Interview linked with Calendly successfully",
            "interview": interview.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error with Calendly integration: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@interviews_bp.route('/api/recruitment/interviews/zoom-integration', methods=['POST'])
def zoom_integration():
    """Handle Zoom integration for video interviews."""
    try:
        data = request.get_json()
        interview_id = data.get('interview_id')
        zoom_meeting_id = data.get('zoom_meeting_id')
        zoom_join_url = data.get('zoom_join_url')
        zoom_password = data.get('zoom_password')
        
        if not all([interview_id, zoom_meeting_id, zoom_join_url]):
            return jsonify({"success": False, "message": "Missing required Zoom parameters"}), 400
        
        interview = Interview.get_interview_by_id(db.session, interview_id)
        if not interview:
            return jsonify({"success": False, "message": "Interview not found"}), 404
        
        # Update interview with Zoom details
        interview.meeting_platform = 'ZOOM'
        interview.meeting_id = zoom_meeting_id
        interview.meeting_url = zoom_join_url
        interview.meeting_password = zoom_password
        interview.updated_at = datetime.now()
        
        db.session.commit()
        
        return jsonify({
            "success": True,
            "message": "Interview linked with Zoom successfully",
            "interview": interview.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error with Zoom integration: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@interviews_bp.route('/api/recruitment/interviews/google-meet-integration', methods=['POST'])
def google_meet_integration():
    """Handle Google Meet integration for video interviews."""
    try:
        data = request.get_json()
        interview_id = data.get('interview_id')
        meet_link = data.get('meet_link')
        calendar_event_id = data.get('calendar_event_id')
        
        if not all([interview_id, meet_link]):
            return jsonify({"success": False, "message": "Missing required Google Meet parameters"}), 400
        
        interview = Interview.get_interview_by_id(db.session, interview_id)
        if not interview:
            return jsonify({"success": False, "message": "Interview not found"}), 404
        
        # Update interview with Google Meet details
        interview.meeting_platform = 'GOOGLE_MEET'
        interview.meeting_url = meet_link
        interview.calendar_event_id = calendar_event_id
        interview.updated_at = datetime.now()
        
        db.session.commit()
        
        return jsonify({
            "success": True,
            "message": "Interview linked with Google Meet successfully",
            "interview": interview.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error with Google Meet integration: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500
