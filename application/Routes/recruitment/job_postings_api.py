from flask import Blueprint, request, jsonify, current_app
from application.database import db
from application.Models.recruitment.job_posting import JobPosting, PostingChannel
from datetime import datetime, date
import json

job_postings_bp = Blueprint('job_postings', __name__)


@job_postings_bp.route('/api/recruitment/job-postings', methods=['POST'])
def create_job_posting():
    """Create a new job posting."""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['company_id', 'job_title', 'job_summary', 'created_by']
        for field in required_fields:
            if field not in data:
                return jsonify({"success": False, "message": f"Missing required field: {field}"}), 400
        
        # Convert date strings to date objects
        date_fields = ['posting_date', 'application_deadline', 'target_start_date']
        for field in date_fields:
            if field in data and data[field]:
                if isinstance(data[field], str):
                    data[field] = datetime.strptime(data[field], '%Y-%m-%d').date()
        
        posting, error = JobPosting.create_posting(db.session, **data)
        if error:
            return jsonify({"success": False, "message": error}), 400
        
        db.session.commit()
        
        return jsonify({
            "success": True,
            "message": "Job posting created successfully",
            "posting": posting.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error creating job posting: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@job_postings_bp.route('/api/recruitment/job-postings', methods=['GET'])
def get_job_postings():
    """Get job postings with optional filters."""
    try:
        company_id = request.args.get('company_id')
        status = request.args.get('status')
        is_internal_only = request.args.get('is_internal_only')
        search = request.args.get('search')
        location = request.args.get('location')
        employment_type = request.args.get('employment_type')
        
        if company_id:
            # Get postings for specific company
            status_list = status.split(',') if status else None
            internal_only = is_internal_only.lower() == 'true' if is_internal_only else None
            
            postings = JobPosting.get_postings_by_company(
                db.session, company_id, status_list, internal_only
            )
        else:
            # Public job search
            postings = JobPosting.search_postings(
                db.session, search, location, employment_type
            )
        
        return jsonify({
            "success": True,
            "postings": [posting.to_dict() for posting in postings]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting job postings: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@job_postings_bp.route('/api/recruitment/job-postings/active', methods=['GET'])
def get_active_job_postings():
    """Get active job postings for public viewing."""
    try:
        location = request.args.get('location')
        department = request.args.get('department')
        employment_type = request.args.get('employment_type')
        experience_level = request.args.get('experience_level')
        
        query = db.session.query(JobPosting).filter_by(status='ACTIVE', is_internal_only=False)
        
        if location:
            query = query.filter(JobPosting.location.ilike(f'%{location}%'))
        
        if department:
            query = query.filter_by(department=department)
        
        if employment_type:
            query = query.filter_by(employment_type=employment_type)
        
        if experience_level:
            query = query.filter_by(experience_level=experience_level)
        
        postings = query.order_by(JobPosting.posting_date.desc()).all()
        
        # Increment view count for each posting
        for posting in postings:
            posting.increment_views(db.session)
        
        return jsonify({
            "success": True,
            "postings": [posting.to_dict() for posting in postings]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting active job postings: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@job_postings_bp.route('/api/recruitment/job-postings/<posting_id>', methods=['GET'])
def get_job_posting(posting_id):
    """Get a specific job posting."""
    try:
        posting = JobPosting.get_posting_by_id(db.session, posting_id)
        if not posting:
            return jsonify({"success": False, "message": "Job posting not found"}), 404
        
        # Increment view count
        posting.increment_views(db.session)
        
        return jsonify({
            "success": True,
            "posting": posting.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting job posting: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@job_postings_bp.route('/api/recruitment/job-postings/<posting_id>', methods=['PUT'])
def update_job_posting(posting_id):
    """Update a job posting."""
    try:
        data = request.get_json()
        
        posting = JobPosting.get_posting_by_id(db.session, posting_id)
        if not posting:
            return jsonify({"success": False, "message": "Job posting not found"}), 404
        
        if posting.status not in ['DRAFT', 'PAUSED']:
            return jsonify({"success": False, "message": "Cannot update posting in current status"}), 400
        
        # Convert date strings to date objects
        date_fields = ['posting_date', 'application_deadline', 'target_start_date']
        for field in date_fields:
            if field in data and data[field]:
                if isinstance(data[field], str):
                    data[field] = datetime.strptime(data[field], '%Y-%m-%d').date()
        
        # Update posting fields
        updatable_fields = [
            'job_title', 'job_code', 'department', 'location', 'employment_type',
            'work_arrangement', 'experience_level', 'job_summary', 'responsibilities',
            'required_qualifications', 'preferred_qualifications', 'skills_required',
            'salary_disclosed', 'salary_range_min', 'salary_range_max', 'currency',
            'benefits_summary', 'application_method', 'external_application_url',
            'application_email', 'application_instructions', 'application_deadline',
            'target_start_date', 'is_internal_only', 'is_featured', 'seo_title',
            'seo_description', 'seo_keywords', 'company_description'
        ]
        
        for field in updatable_fields:
            if field in data:
                if field in ['skills_required', 'seo_keywords']:
                    if isinstance(data[field], list):
                        setattr(posting, field, json.dumps(data[field]))
                    else:
                        setattr(posting, field, data[field])
                else:
                    setattr(posting, field, data[field])
        
        posting.updated_at = datetime.now()
        db.session.commit()
        
        return jsonify({
            "success": True,
            "message": "Job posting updated successfully",
            "posting": posting.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error updating job posting: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@job_postings_bp.route('/api/recruitment/job-postings/<posting_id>/publish', methods=['PUT'])
def publish_job_posting(posting_id):
    """Publish a job posting to specified channels."""
    try:
        data = request.get_json() or {}
        channels = data.get('channels', [])
        
        posting = JobPosting.get_posting_by_id(db.session, posting_id)
        if not posting:
            return jsonify({"success": False, "message": "Job posting not found"}), 404
        
        success, error = posting.publish(db.session, channels)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Job posting published successfully",
            "posting": posting.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error publishing job posting: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@job_postings_bp.route('/api/recruitment/job-postings/<posting_id>/pause', methods=['PUT'])
def pause_job_posting(posting_id):
    """Pause a job posting."""
    try:
        posting = JobPosting.get_posting_by_id(db.session, posting_id)
        if not posting:
            return jsonify({"success": False, "message": "Job posting not found"}), 404
        
        success, error = posting.pause(db.session)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Job posting paused successfully",
            "posting": posting.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error pausing job posting: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@job_postings_bp.route('/api/recruitment/job-postings/<posting_id>/close', methods=['PUT'])
def close_job_posting(posting_id):
    """Close a job posting."""
    try:
        data = request.get_json() or {}
        reason = data.get('reason')
        
        posting = JobPosting.get_posting_by_id(db.session, posting_id)
        if not posting:
            return jsonify({"success": False, "message": "Job posting not found"}), 404
        
        success, error = posting.close(db.session, reason)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Job posting closed successfully",
            "posting": posting.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error closing job posting: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@job_postings_bp.route('/api/recruitment/job-postings/<posting_id>/channels', methods=['POST'])
def add_posting_channel(posting_id):
    """Add a distribution channel to a job posting."""
    try:
        data = request.get_json()
        
        required_fields = ['channel_name', 'channel_type']
        for field in required_fields:
            if field not in data:
                return jsonify({"success": False, "message": f"Missing required field: {field}"}), 400
        
        posting = JobPosting.get_posting_by_id(db.session, posting_id)
        if not posting:
            return jsonify({"success": False, "message": "Job posting not found"}), 404
        
        channel = PostingChannel(
            posting_id=posting_id,
            **data
        )
        
        db.session.add(channel)
        db.session.commit()
        
        return jsonify({
            "success": True,
            "message": "Posting channel added successfully",
            "channel": channel.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error adding posting channel: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@job_postings_bp.route('/api/recruitment/job-postings/<posting_id>/channels/<channel_id>/posted', methods=['PUT'])
def mark_channel_posted(posting_id, channel_id):
    """Mark a channel as successfully posted."""
    try:
        data = request.get_json() or {}
        external_id = data.get('external_id')
        posting_url = data.get('posting_url')
        
        channel = db.session.query(PostingChannel).filter_by(
            channel_id=channel_id,
            posting_id=posting_id
        ).first()
        
        if not channel:
            return jsonify({"success": False, "message": "Posting channel not found"}), 404
        
        channel.mark_as_posted(db.session, external_id, posting_url)
        
        return jsonify({
            "success": True,
            "message": "Channel marked as posted",
            "channel": channel.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error marking channel as posted: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@job_postings_bp.route('/api/recruitment/job-postings/<posting_id>/channels/<channel_id>/failed', methods=['PUT'])
def mark_channel_failed(posting_id, channel_id):
    """Mark a channel posting as failed."""
    try:
        data = request.get_json()
        error_message = data.get('error_message', 'Posting failed')
        
        channel = db.session.query(PostingChannel).filter_by(
            channel_id=channel_id,
            posting_id=posting_id
        ).first()
        
        if not channel:
            return jsonify({"success": False, "message": "Posting channel not found"}), 404
        
        channel.mark_as_failed(db.session, error_message)
        
        return jsonify({
            "success": True,
            "message": "Channel marked as failed",
            "channel": channel.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error marking channel as failed: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@job_postings_bp.route('/api/recruitment/job-postings/search', methods=['GET'])
def search_job_postings():
    """Search job postings with advanced filters."""
    try:
        search_term = request.args.get('q', '')
        location = request.args.get('location')
        employment_type = request.args.get('employment_type')
        experience_level = request.args.get('experience_level')
        department = request.args.get('department')
        salary_min = request.args.get('salary_min', type=int)
        salary_max = request.args.get('salary_max', type=int)
        work_arrangement = request.args.get('work_arrangement')
        company_id = request.args.get('company_id')
        
        query = db.session.query(JobPosting).filter_by(status='ACTIVE')
        
        # Apply search filters
        if search_term:
            search_filter = db.or_(
                JobPosting.job_title.ilike(f'%{search_term}%'),
                JobPosting.job_summary.ilike(f'%{search_term}%'),
                JobPosting.skills_required.ilike(f'%{search_term}%'),
                JobPosting.department.ilike(f'%{search_term}%')
            )
            query = query.filter(search_filter)
        
        if location:
            query = query.filter(JobPosting.location.ilike(f'%{location}%'))
        
        if employment_type:
            query = query.filter_by(employment_type=employment_type)
        
        if experience_level:
            query = query.filter_by(experience_level=experience_level)
        
        if department:
            query = query.filter_by(department=department)
        
        if work_arrangement:
            query = query.filter_by(work_arrangement=work_arrangement)
        
        if company_id:
            query = query.filter_by(company_id=company_id)
        
        if salary_min:
            query = query.filter(JobPosting.salary_range_min >= salary_min)
        
        if salary_max:
            query = query.filter(JobPosting.salary_range_max <= salary_max)
        
        # Exclude internal-only postings for public search
        if not company_id:
            query = query.filter_by(is_internal_only=False)
        
        postings = query.order_by(JobPosting.posting_date.desc()).all()
        
        return jsonify({
            "success": True,
            "postings": [posting.to_dict() for posting in postings],
            "total_results": len(postings)
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error searching job postings: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@job_postings_bp.route('/api/recruitment/job-postings/statistics', methods=['GET'])
def get_posting_statistics():
    """Get job posting statistics."""
    try:
        company_id = request.args.get('company_id')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        query = db.session.query(JobPosting)
        
        if company_id:
            query = query.filter_by(company_id=company_id)
        
        if date_from:
            query = query.filter(JobPosting.posting_date >= datetime.strptime(date_from, '%Y-%m-%d').date())
        
        if date_to:
            query = query.filter(JobPosting.posting_date <= datetime.strptime(date_to, '%Y-%m-%d').date())
        
        postings = query.all()
        
        # Calculate statistics
        total_postings = len(postings)
        
        status_breakdown = {}
        department_breakdown = {}
        employment_type_breakdown = {}
        
        total_views = 0
        total_applications = 0
        
        for posting in postings:
            # Status breakdown
            status = posting.status
            status_breakdown[status] = status_breakdown.get(status, 0) + 1
            
            # Department breakdown
            dept = posting.department or 'Unknown'
            department_breakdown[dept] = department_breakdown.get(dept, 0) + 1
            
            # Employment type breakdown
            emp_type = posting.employment_type or 'Unknown'
            employment_type_breakdown[emp_type] = employment_type_breakdown.get(emp_type, 0) + 1
            
            # Aggregate metrics
            total_views += posting.total_views or 0
            total_applications += posting.total_applications or 0
        
        # Calculate conversion rate
        conversion_rate = round(total_applications / total_views * 100, 2) if total_views > 0 else 0
        
        # Active postings
        active_postings = status_breakdown.get('ACTIVE', 0)
        
        return jsonify({
            "success": True,
            "statistics": {
                "total_postings": total_postings,
                "active_postings": active_postings,
                "total_views": total_views,
                "total_applications": total_applications,
                "conversion_rate": conversion_rate,
                "status_breakdown": status_breakdown,
                "department_breakdown": department_breakdown,
                "employment_type_breakdown": employment_type_breakdown
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting posting statistics: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@job_postings_bp.route('/api/recruitment/job-postings/linkedin-integration', methods=['POST'])
def linkedin_integration():
    """Handle LinkedIn job posting integration."""
    try:
        data = request.get_json()
        posting_id = data.get('posting_id')
        linkedin_company_id = data.get('linkedin_company_id')
        access_token = data.get('access_token')
        
        if not all([posting_id, linkedin_company_id, access_token]):
            return jsonify({"success": False, "message": "Missing required LinkedIn integration parameters"}), 400
        
        posting = JobPosting.get_posting_by_id(db.session, posting_id)
        if not posting:
            return jsonify({"success": False, "message": "Job posting not found"}), 404
        
        # TODO: Implement actual LinkedIn API integration
        # This is a placeholder for LinkedIn job posting
        
        # Create LinkedIn channel record
        linkedin_channel = PostingChannel(
            posting_id=posting_id,
            channel_name='LINKEDIN',
            channel_type='SOCIAL_MEDIA',
            status='POSTED',
            external_posting_id=f"linkedin_{posting_id}",
            posting_url=f"https://linkedin.com/jobs/view/{posting_id}",
            posted_date=datetime.now()
        )
        
        db.session.add(linkedin_channel)
        db.session.commit()
        
        return jsonify({
            "success": True,
            "message": "Job posted to LinkedIn successfully",
            "channel": linkedin_channel.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error with LinkedIn integration: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500
