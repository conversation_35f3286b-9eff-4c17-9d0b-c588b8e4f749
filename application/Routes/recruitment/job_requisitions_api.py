from flask import Blueprint, request, jsonify, current_app
from application.database import db
from application.Models.recruitment.job_requisition import JobRequisition, RequisitionApproval
from datetime import datetime, date
import json

job_requisitions_bp = Blueprint('job_requisitions', __name__)


@job_requisitions_bp.route('/api/recruitment/requisitions', methods=['POST'])
def create_requisition():
    """Create a new job requisition."""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['company_id', 'job_title', 'department', 'reporting_manager', 'business_justification', 'requested_by']
        for field in required_fields:
            if field not in data:
                return jsonify({"success": False, "message": f"Missing required field: {field}"}), 400
        
        # Convert date strings to date objects
        date_fields = ['target_start_date', 'approval_deadline', 'posting_start_date', 'application_deadline', 'target_hire_date']
        for field in date_fields:
            if field in data and data[field]:
                if isinstance(data[field], str):
                    data[field] = datetime.strptime(data[field], '%Y-%m-%d').date()
        
        requisition, error = JobRequisition.create_requisition(db.session, **data)
        if error:
            return jsonify({"success": False, "message": error}), 400
        
        db.session.commit()
        
        return jsonify({
            "success": True,
            "message": "Job requisition created successfully",
            "requisition": requisition.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error creating job requisition: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@job_requisitions_bp.route('/api/recruitment/requisitions', methods=['GET'])
def get_requisitions():
    """Get job requisitions for a company."""
    try:
        company_id = request.args.get('company_id')
        status = request.args.get('status')
        department = request.args.get('department')
        
        if not company_id:
            return jsonify({"success": False, "message": "company_id is required"}), 400
        
        status_list = status.split(',') if status else None
        requisitions = JobRequisition.get_requisitions_by_company(
            db.session, company_id, status_list, department
        )
        
        return jsonify({
            "success": True,
            "requisitions": [req.to_dict() for req in requisitions]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting job requisitions: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@job_requisitions_bp.route('/api/recruitment/requisitions/<requisition_id>', methods=['GET'])
def get_requisition(requisition_id):
    """Get a specific job requisition."""
    try:
        requisition = JobRequisition.get_requisition_by_id(db.session, requisition_id)
        if not requisition:
            return jsonify({"success": False, "message": "Job requisition not found"}), 404
        
        return jsonify({
            "success": True,
            "requisition": requisition.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting job requisition: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@job_requisitions_bp.route('/api/recruitment/requisitions/<requisition_id>', methods=['PUT'])
def update_requisition(requisition_id):
    """Update a job requisition."""
    try:
        data = request.get_json()
        
        requisition = JobRequisition.get_requisition_by_id(db.session, requisition_id)
        if not requisition:
            return jsonify({"success": False, "message": "Job requisition not found"}), 404
        
        if requisition.status not in ['DRAFT', 'REJECTED']:
            return jsonify({"success": False, "message": "Cannot update requisition in current status"}), 400
        
        # Convert date strings to date objects
        date_fields = ['target_start_date', 'approval_deadline', 'posting_start_date', 'application_deadline', 'target_hire_date']
        for field in date_fields:
            if field in data and data[field]:
                if isinstance(data[field], str):
                    data[field] = datetime.strptime(data[field], '%Y-%m-%d').date()
        
        # Update requisition fields
        updatable_fields = [
            'job_title', 'department', 'reporting_manager', 'position_type', 'employment_type',
            'location', 'remote_work_option', 'number_of_positions', 'replacement_for',
            'urgency_level', 'target_start_date', 'salary_range_min', 'salary_range_max',
            'currency', 'benefits_package', 'business_justification', 'role_responsibilities',
            'required_qualifications', 'preferred_qualifications', 'success_criteria',
            'recruitment_type', 'sourcing_strategy', 'assessment_requirements',
            'interview_process', 'approval_deadline', 'posting_start_date',
            'application_deadline', 'target_hire_date'
        ]
        
        for field in updatable_fields:
            if field in data:
                if field in ['benefits_package', 'sourcing_strategy', 'assessment_requirements', 'interview_process']:
                    if isinstance(data[field], (dict, list)):
                        setattr(requisition, field, json.dumps(data[field]))
                    else:
                        setattr(requisition, field, data[field])
                else:
                    setattr(requisition, field, data[field])
        
        requisition.updated_at = datetime.now()
        db.session.commit()
        
        return jsonify({
            "success": True,
            "message": "Job requisition updated successfully",
            "requisition": requisition.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error updating job requisition: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@job_requisitions_bp.route('/api/recruitment/requisitions/<requisition_id>/submit', methods=['PUT'])
def submit_requisition(requisition_id):
    """Submit job requisition for approval."""
    try:
        requisition = JobRequisition.get_requisition_by_id(db.session, requisition_id)
        if not requisition:
            return jsonify({"success": False, "message": "Job requisition not found"}), 404
        
        success, error = requisition.submit_for_approval(db.session)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Job requisition submitted for approval",
            "requisition": requisition.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error submitting job requisition: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@job_requisitions_bp.route('/api/recruitment/requisitions/<requisition_id>/approve', methods=['PUT'])
def approve_requisition(requisition_id):
    """Approve a job requisition."""
    try:
        data = request.get_json()
        approver_id = data.get('approver_id')
        comments = data.get('comments')
        
        if not approver_id:
            return jsonify({"success": False, "message": "approver_id is required"}), 400
        
        requisition = JobRequisition.get_requisition_by_id(db.session, requisition_id)
        if not requisition:
            return jsonify({"success": False, "message": "Job requisition not found"}), 404
        
        success, error = requisition.approve(db.session, approver_id, comments)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Job requisition approved successfully",
            "requisition": requisition.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error approving job requisition: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@job_requisitions_bp.route('/api/recruitment/requisitions/<requisition_id>/reject', methods=['PUT'])
def reject_requisition(requisition_id):
    """Reject a job requisition."""
    try:
        data = request.get_json()
        approver_id = data.get('approver_id')
        rejection_reason = data.get('rejection_reason')
        
        if not approver_id or not rejection_reason:
            return jsonify({"success": False, "message": "approver_id and rejection_reason are required"}), 400
        
        requisition = JobRequisition.get_requisition_by_id(db.session, requisition_id)
        if not requisition:
            return jsonify({"success": False, "message": "Job requisition not found"}), 404
        
        success, error = requisition.reject(db.session, approver_id, rejection_reason)
        if not success:
            return jsonify({"success": False, "message": error}), 400
        
        return jsonify({
            "success": True,
            "message": "Job requisition rejected",
            "requisition": requisition.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error rejecting job requisition: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@job_requisitions_bp.route('/api/recruitment/requisitions/pending-approval', methods=['GET'])
def get_pending_approvals():
    """Get requisitions pending approval for a specific approver."""
    try:
        approver_id = request.args.get('approver_id')
        company_id = request.args.get('company_id')
        
        if not approver_id:
            return jsonify({"success": False, "message": "approver_id is required"}), 400
        
        # Get requisitions where current approver matches
        query = db.session.query(JobRequisition).filter(
            JobRequisition.status == 'PENDING_APPROVAL',
            JobRequisition.current_approver == approver_id
        )
        
        if company_id:
            query = query.filter_by(company_id=company_id)
        
        requisitions = query.order_by(JobRequisition.created_at.desc()).all()
        
        return jsonify({
            "success": True,
            "requisitions": [req.to_dict() for req in requisitions]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting pending approvals: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@job_requisitions_bp.route('/api/recruitment/requisitions/overdue', methods=['GET'])
def get_overdue_requisitions():
    """Get overdue requisitions."""
    try:
        company_id = request.args.get('company_id')
        
        if not company_id:
            return jsonify({"success": False, "message": "company_id is required"}), 400
        
        # Get overdue requisitions
        today = date.today()
        requisitions = db.session.query(JobRequisition).filter(
            JobRequisition.company_id == company_id,
            JobRequisition.status == 'PENDING_APPROVAL',
            JobRequisition.approval_deadline < today
        ).order_by(JobRequisition.approval_deadline).all()
        
        return jsonify({
            "success": True,
            "requisitions": [req.to_dict() for req in requisitions]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting overdue requisitions: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@job_requisitions_bp.route('/api/recruitment/requisitions/statistics', methods=['GET'])
def get_requisition_statistics():
    """Get requisition statistics for a company."""
    try:
        company_id = request.args.get('company_id')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        if not company_id:
            return jsonify({"success": False, "message": "company_id is required"}), 400
        
        query = db.session.query(JobRequisition).filter_by(company_id=company_id)
        
        if date_from:
            query = query.filter(JobRequisition.created_at >= datetime.strptime(date_from, '%Y-%m-%d'))
        
        if date_to:
            query = query.filter(JobRequisition.created_at <= datetime.strptime(date_to, '%Y-%m-%d'))
        
        requisitions = query.all()
        
        # Calculate statistics
        total_requisitions = len(requisitions)
        
        status_breakdown = {}
        department_breakdown = {}
        urgency_breakdown = {}
        
        for req in requisitions:
            # Status breakdown
            status = req.status
            status_breakdown[status] = status_breakdown.get(status, 0) + 1
            
            # Department breakdown
            dept = req.department
            department_breakdown[dept] = department_breakdown.get(dept, 0) + 1
            
            # Urgency breakdown
            urgency = req.urgency_level
            urgency_breakdown[urgency] = urgency_breakdown.get(urgency, 0) + 1
        
        # Calculate approval metrics
        approved_count = status_breakdown.get('APPROVED', 0)
        rejected_count = status_breakdown.get('REJECTED', 0)
        pending_count = status_breakdown.get('PENDING_APPROVAL', 0)
        
        approval_rate = round(approved_count / total_requisitions * 100, 2) if total_requisitions > 0 else 0
        
        # Calculate average approval time for approved requisitions
        approved_reqs = [req for req in requisitions if req.status == 'APPROVED']
        avg_approval_days = 0
        
        if approved_reqs:
            total_days = 0
            count = 0
            for req in approved_reqs:
                if req.created_at and req.updated_at:
                    days = (req.updated_at.date() - req.created_at.date()).days
                    total_days += days
                    count += 1
            
            if count > 0:
                avg_approval_days = round(total_days / count, 1)
        
        return jsonify({
            "success": True,
            "statistics": {
                "total_requisitions": total_requisitions,
                "status_breakdown": status_breakdown,
                "department_breakdown": department_breakdown,
                "urgency_breakdown": urgency_breakdown,
                "approval_rate": approval_rate,
                "average_approval_days": avg_approval_days,
                "pending_approvals": pending_count,
                "overdue_count": len([req for req in requisitions if req.is_overdue()])
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting requisition statistics: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@job_requisitions_bp.route('/api/recruitment/requisitions/templates', methods=['GET'])
def get_requisition_templates():
    """Get job requisition templates."""
    try:
        company_id = request.args.get('company_id')
        department = request.args.get('department')
        
        # This would typically fetch from a templates table
        # For now, return some sample templates
        templates = [
            {
                "template_id": "software_engineer",
                "name": "Software Engineer",
                "department": "Engineering",
                "position_type": "FULL_TIME",
                "employment_type": "PERMANENT",
                "role_responsibilities": "Design, develop, and maintain software applications...",
                "required_qualifications": "Bachelor's degree in Computer Science or related field...",
                "preferred_qualifications": "Experience with cloud platforms, agile methodologies...",
                "assessment_requirements": {
                    "technical_test": True,
                    "coding_challenge": True,
                    "system_design": True
                },
                "interview_process": {
                    "stages": [
                        {"name": "Phone Screen", "duration": 30},
                        {"name": "Technical Interview", "duration": 60},
                        {"name": "System Design", "duration": 45},
                        {"name": "Cultural Fit", "duration": 30}
                    ]
                }
            },
            {
                "template_id": "sales_manager",
                "name": "Sales Manager",
                "department": "Sales",
                "position_type": "FULL_TIME",
                "employment_type": "PERMANENT",
                "role_responsibilities": "Lead sales team, develop sales strategies...",
                "required_qualifications": "5+ years of sales experience, proven track record...",
                "preferred_qualifications": "MBA, experience in B2B sales...",
                "assessment_requirements": {
                    "sales_simulation": True,
                    "presentation": True
                },
                "interview_process": {
                    "stages": [
                        {"name": "Initial Screen", "duration": 30},
                        {"name": "Sales Presentation", "duration": 45},
                        {"name": "Panel Interview", "duration": 60}
                    ]
                }
            }
        ]
        
        # Filter by department if specified
        if department:
            templates = [t for t in templates if t['department'].lower() == department.lower()]
        
        return jsonify({
            "success": True,
            "templates": templates
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting requisition templates: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@job_requisitions_bp.route('/api/recruitment/requisitions/<requisition_id>/clone', methods=['POST'])
def clone_requisition(requisition_id):
    """Clone an existing job requisition."""
    try:
        data = request.get_json()
        new_job_title = data.get('job_title')
        requested_by = data.get('requested_by')
        
        if not new_job_title or not requested_by:
            return jsonify({"success": False, "message": "job_title and requested_by are required"}), 400
        
        original_req = JobRequisition.get_requisition_by_id(db.session, requisition_id)
        if not original_req:
            return jsonify({"success": False, "message": "Original requisition not found"}), 404
        
        # Create new requisition with copied data
        clone_data = {
            'company_id': original_req.company_id,
            'job_title': new_job_title,
            'department': original_req.department,
            'reporting_manager': original_req.reporting_manager,
            'position_type': original_req.position_type,
            'employment_type': original_req.employment_type,
            'location': original_req.location,
            'remote_work_option': original_req.remote_work_option,
            'number_of_positions': original_req.number_of_positions,
            'urgency_level': original_req.urgency_level,
            'salary_range_min': original_req.salary_range_min,
            'salary_range_max': original_req.salary_range_max,
            'currency': original_req.currency,
            'benefits_package': original_req.benefits_package,
            'role_responsibilities': original_req.role_responsibilities,
            'required_qualifications': original_req.required_qualifications,
            'preferred_qualifications': original_req.preferred_qualifications,
            'success_criteria': original_req.success_criteria,
            'recruitment_type': original_req.recruitment_type,
            'sourcing_strategy': original_req.sourcing_strategy,
            'assessment_requirements': original_req.assessment_requirements,
            'interview_process': original_req.interview_process,
            'business_justification': f"Cloned from requisition {original_req.requisition_number}",
            'requested_by': requested_by
        }
        
        # Override with any provided data
        clone_data.update({k: v for k, v in data.items() if k not in ['job_title', 'requested_by']})
        
        new_requisition, error = JobRequisition.create_requisition(db.session, **clone_data)
        if error:
            return jsonify({"success": False, "message": error}), 400
        
        db.session.commit()
        
        return jsonify({
            "success": True,
            "message": "Job requisition cloned successfully",
            "requisition": new_requisition.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error cloning job requisition: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500
