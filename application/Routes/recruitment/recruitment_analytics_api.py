from flask import Blueprint, request, jsonify, current_app
from application.database import db
from application.Models.recruitment.job_requisition import JobRequisition
from application.Models.recruitment.job_posting import JobPosting
from application.Models.recruitment.candidate import Candidate
from application.Models.recruitment.job_application import JobApplication
from application.Models.recruitment.interview import Interview
from application.Models.recruitment.candidate_assessment import Candidate<PERSON><PERSON>sm<PERSON>
from datetime import datetime, date, timedelta
from sqlalchemy import func, extract
import json

recruitment_analytics_bp = Blueprint('recruitment_analytics', __name__)


@recruitment_analytics_bp.route('/api/recruitment/analytics/overview', methods=['GET'])
def get_recruitment_overview():
    """Get comprehensive recruitment overview dashboard."""
    try:
        company_id = request.args.get('company_id')
        period = request.args.get('period', 'month')  # week, month, quarter, year
        
        if not company_id:
            return jsonify({"success": False, "message": "company_id is required"}), 400
        
        # Calculate date range based on period
        end_date = date.today()
        if period == 'week':
            start_date = end_date - timedelta(days=7)
        elif period == 'month':
            start_date = end_date - timedelta(days=30)
        elif period == 'quarter':
            start_date = end_date - timedelta(days=90)
        elif period == 'year':
            start_date = end_date - timedelta(days=365)
        else:
            start_date = end_date - timedelta(days=30)
        
        # Get basic counts
        open_positions = db.session.query(JobPosting).filter(
            JobPosting.company_id == company_id,
            JobPosting.status == 'ACTIVE'
        ).count()
        
        active_candidates = db.session.query(Candidate).filter(
            Candidate.company_id == company_id,
            Candidate.status == 'ACTIVE'
        ).count()
        
        total_applications = db.session.query(JobApplication).join(JobPosting).filter(
            JobPosting.company_id == company_id,
            JobApplication.submitted_date >= start_date
        ).count()
        
        interviews_scheduled = db.session.query(Interview).join(JobApplication).join(JobPosting).filter(
            JobPosting.company_id == company_id,
            Interview.scheduled_date >= datetime.combine(start_date, datetime.min.time()),
            Interview.status.in_(['SCHEDULED', 'COMPLETED'])
        ).count()
        
        offers_pending = db.session.query(JobApplication).join(JobPosting).filter(
            JobPosting.company_id == company_id,
            JobApplication.status == 'OFFER'
        ).count()
        
        hires_this_period = db.session.query(JobApplication).join(JobPosting).filter(
            JobPosting.company_id == company_id,
            JobApplication.status == 'HIRED',
            JobApplication.decision_date >= start_date
        ).count()
        
        # Calculate metrics
        hire_rate = round(hires_this_period / total_applications * 100, 2) if total_applications > 0 else 0
        
        # Average time to hire
        hired_applications = db.session.query(JobApplication).join(JobPosting).filter(
            JobPosting.company_id == company_id,
            JobApplication.status == 'HIRED',
            JobApplication.decision_date >= start_date
        ).all()
        
        avg_time_to_hire = 0
        if hired_applications:
            total_days = sum(app.get_days_in_process() for app in hired_applications)
            avg_time_to_hire = round(total_days / len(hired_applications), 1)
        
        # Pipeline health
        pipeline_stages = db.session.query(
            JobApplication.status,
            func.count(JobApplication.application_id).label('count')
        ).join(JobPosting).filter(
            JobPosting.company_id == company_id,
            JobApplication.status.in_(['SUBMITTED', 'SCREENING', 'INTERVIEWING', 'OFFER', 'HIRED'])
        ).group_by(JobApplication.status).all()
        
        pipeline_health = {stage: count for stage, count in pipeline_stages}
        
        return jsonify({
            "success": True,
            "overview": {
                "period": period,
                "start_date": start_date.strftime('%Y-%m-%d'),
                "end_date": end_date.strftime('%Y-%m-%d'),
                "open_positions": open_positions,
                "active_candidates": active_candidates,
                "total_applications": total_applications,
                "interviews_scheduled": interviews_scheduled,
                "offers_pending": offers_pending,
                "hires_this_period": hires_this_period,
                "hire_rate": hire_rate,
                "average_time_to_hire": avg_time_to_hire,
                "pipeline_health": pipeline_health
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting recruitment overview: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@recruitment_analytics_bp.route('/api/recruitment/analytics/funnel', methods=['GET'])
def get_recruitment_funnel():
    """Get recruitment funnel analysis."""
    try:
        company_id = request.args.get('company_id')
        posting_id = request.args.get('posting_id')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        if not company_id:
            return jsonify({"success": False, "message": "company_id is required"}), 400
        
        query = db.session.query(JobApplication).join(JobPosting).filter(
            JobPosting.company_id == company_id
        )
        
        if posting_id:
            query = query.filter(JobApplication.posting_id == posting_id)
        
        if date_from:
            query = query.filter(JobApplication.submitted_date >= datetime.strptime(date_from, '%Y-%m-%d'))
        
        if date_to:
            query = query.filter(JobApplication.submitted_date <= datetime.strptime(date_to, '%Y-%m-%d'))
        
        applications = query.all()
        
        # Calculate funnel metrics
        total_applications = len(applications)
        
        funnel_stages = {
            'applications': total_applications,
            'screening': len([a for a in applications if a.status in ['SCREENING', 'INTERVIEWING', 'OFFER', 'HIRED']]),
            'interviewing': len([a for a in applications if a.status in ['INTERVIEWING', 'OFFER', 'HIRED']]),
            'offer': len([a for a in applications if a.status in ['OFFER', 'HIRED']]),
            'hired': len([a for a in applications if a.status == 'HIRED'])
        }
        
        # Calculate conversion rates
        conversion_rates = {}
        prev_count = total_applications
        
        for stage, count in funnel_stages.items():
            if prev_count > 0:
                conversion_rates[stage] = round(count / prev_count * 100, 2)
            else:
                conversion_rates[stage] = 0
            prev_count = count
        
        # Source analysis
        source_funnel = {}
        for app in applications:
            source = app.application_source or 'UNKNOWN'
            if source not in source_funnel:
                source_funnel[source] = {
                    'applications': 0,
                    'hired': 0,
                    'conversion_rate': 0
                }
            
            source_funnel[source]['applications'] += 1
            if app.status == 'HIRED':
                source_funnel[source]['hired'] += 1
        
        # Calculate source conversion rates
        for source, data in source_funnel.items():
            if data['applications'] > 0:
                data['conversion_rate'] = round(data['hired'] / data['applications'] * 100, 2)
        
        return jsonify({
            "success": True,
            "funnel": {
                "stages": funnel_stages,
                "conversion_rates": conversion_rates,
                "source_analysis": source_funnel,
                "total_applications": total_applications
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting recruitment funnel: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@recruitment_analytics_bp.route('/api/recruitment/analytics/time-to-hire', methods=['GET'])
def get_time_to_hire_analytics():
    """Get time-to-hire analytics."""
    try:
        company_id = request.args.get('company_id')
        department = request.args.get('department')
        position_type = request.args.get('position_type')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        if not company_id:
            return jsonify({"success": False, "message": "company_id is required"}), 400
        
        query = db.session.query(JobApplication).join(JobPosting).filter(
            JobPosting.company_id == company_id,
            JobApplication.status == 'HIRED'
        )
        
        if department:
            query = query.filter(JobPosting.department == department)
        
        if position_type:
            query = query.filter(JobPosting.employment_type == position_type)
        
        if date_from:
            query = query.filter(JobApplication.decision_date >= datetime.strptime(date_from, '%Y-%m-%d'))
        
        if date_to:
            query = query.filter(JobApplication.decision_date <= datetime.strptime(date_to, '%Y-%m-%d'))
        
        hired_applications = query.all()
        
        if not hired_applications:
            return jsonify({
                "success": True,
                "time_to_hire": {
                    "average_days": 0,
                    "median_days": 0,
                    "min_days": 0,
                    "max_days": 0,
                    "by_department": {},
                    "by_position_type": {},
                    "trend_data": []
                }
            }), 200
        
        # Calculate time-to-hire metrics
        days_to_hire = [app.get_days_in_process() for app in hired_applications]
        
        average_days = round(sum(days_to_hire) / len(days_to_hire), 1)
        median_days = sorted(days_to_hire)[len(days_to_hire) // 2]
        min_days = min(days_to_hire)
        max_days = max(days_to_hire)
        
        # By department
        dept_analysis = {}
        for app in hired_applications:
            dept = app.job_posting.department or 'Unknown'
            if dept not in dept_analysis:
                dept_analysis[dept] = []
            dept_analysis[dept].append(app.get_days_in_process())
        
        by_department = {}
        for dept, days_list in dept_analysis.items():
            by_department[dept] = {
                'average_days': round(sum(days_list) / len(days_list), 1),
                'count': len(days_list)
            }
        
        # By position type
        type_analysis = {}
        for app in hired_applications:
            pos_type = app.job_posting.employment_type or 'Unknown'
            if pos_type not in type_analysis:
                type_analysis[pos_type] = []
            type_analysis[pos_type].append(app.get_days_in_process())
        
        by_position_type = {}
        for pos_type, days_list in type_analysis.items():
            by_position_type[pos_type] = {
                'average_days': round(sum(days_list) / len(days_list), 1),
                'count': len(days_list)
            }
        
        # Trend data (monthly)
        trend_data = []
        monthly_data = {}
        
        for app in hired_applications:
            if app.decision_date:
                month_key = app.decision_date.strftime('%Y-%m')
                if month_key not in monthly_data:
                    monthly_data[month_key] = []
                monthly_data[month_key].append(app.get_days_in_process())
        
        for month, days_list in sorted(monthly_data.items()):
            trend_data.append({
                'month': month,
                'average_days': round(sum(days_list) / len(days_list), 1),
                'hires_count': len(days_list)
            })
        
        return jsonify({
            "success": True,
            "time_to_hire": {
                "average_days": average_days,
                "median_days": median_days,
                "min_days": min_days,
                "max_days": max_days,
                "total_hires": len(hired_applications),
                "by_department": by_department,
                "by_position_type": by_position_type,
                "trend_data": trend_data
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting time-to-hire analytics: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@recruitment_analytics_bp.route('/api/recruitment/analytics/source-effectiveness', methods=['GET'])
def get_source_effectiveness():
    """Get recruitment source effectiveness analysis."""
    try:
        company_id = request.args.get('company_id')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        if not company_id:
            return jsonify({"success": False, "message": "company_id is required"}), 400
        
        query = db.session.query(JobApplication).join(JobPosting).filter(
            JobPosting.company_id == company_id
        )
        
        if date_from:
            query = query.filter(JobApplication.submitted_date >= datetime.strptime(date_from, '%Y-%m-%d'))
        
        if date_to:
            query = query.filter(JobApplication.submitted_date <= datetime.strptime(date_to, '%Y-%m-%d'))
        
        applications = query.all()
        
        # Analyze by application source
        source_analysis = {}
        
        for app in applications:
            source = app.application_source or 'UNKNOWN'
            
            if source not in source_analysis:
                source_analysis[source] = {
                    'total_applications': 0,
                    'hired': 0,
                    'rejected': 0,
                    'in_progress': 0,
                    'hire_rate': 0,
                    'avg_time_to_hire': 0,
                    'quality_score': 0
                }
            
            source_analysis[source]['total_applications'] += 1
            
            if app.status == 'HIRED':
                source_analysis[source]['hired'] += 1
            elif app.status == 'REJECTED':
                source_analysis[source]['rejected'] += 1
            else:
                source_analysis[source]['in_progress'] += 1
        
        # Calculate metrics for each source
        for source, data in source_analysis.items():
            if data['total_applications'] > 0:
                data['hire_rate'] = round(data['hired'] / data['total_applications'] * 100, 2)
            
            # Calculate average time to hire for this source
            hired_from_source = [app for app in applications 
                               if (app.application_source or 'UNKNOWN') == source and app.status == 'HIRED']
            
            if hired_from_source:
                total_days = sum(app.get_days_in_process() for app in hired_from_source)
                data['avg_time_to_hire'] = round(total_days / len(hired_from_source), 1)
            
            # Quality score based on hire rate and time to hire
            if data['hire_rate'] > 0 and data['avg_time_to_hire'] > 0:
                # Higher hire rate and lower time to hire = higher quality
                data['quality_score'] = round((data['hire_rate'] / max(data['avg_time_to_hire'], 1)) * 10, 2)
        
        # Analyze by candidate source (from candidate table)
        candidate_source_query = db.session.query(Candidate).filter_by(company_id=company_id)
        
        if date_from:
            candidate_source_query = candidate_source_query.filter(
                Candidate.created_at >= datetime.strptime(date_from, '%Y-%m-%d')
            )
        
        if date_to:
            candidate_source_query = candidate_source_query.filter(
                Candidate.created_at <= datetime.strptime(date_to, '%Y-%m-%d')
            )
        
        candidates = candidate_source_query.all()
        
        candidate_source_analysis = {}
        
        for candidate in candidates:
            source = candidate.source or 'UNKNOWN'
            
            if source not in candidate_source_analysis:
                candidate_source_analysis[source] = {
                    'total_candidates': 0,
                    'active_candidates': 0,
                    'hired_candidates': 0
                }
            
            candidate_source_analysis[source]['total_candidates'] += 1
            
            if candidate.status == 'ACTIVE':
                candidate_source_analysis[source]['active_candidates'] += 1
            
            # Count hired candidates
            hired_apps = db.session.query(JobApplication).filter_by(
                candidate_id=candidate.candidate_id,
                status='HIRED'
            ).count()
            
            if hired_apps > 0:
                candidate_source_analysis[source]['hired_candidates'] += 1
        
        return jsonify({
            "success": True,
            "source_effectiveness": {
                "application_sources": source_analysis,
                "candidate_sources": candidate_source_analysis,
                "summary": {
                    "total_applications": len(applications),
                    "total_candidates": len(candidates),
                    "best_performing_source": max(source_analysis.keys(), 
                                                key=lambda x: source_analysis[x]['quality_score']) if source_analysis else None
                }
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting source effectiveness: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@recruitment_analytics_bp.route('/api/recruitment/analytics/interview-performance', methods=['GET'])
def get_interview_performance():
    """Get interview performance analytics."""
    try:
        company_id = request.args.get('company_id')
        interviewer_id = request.args.get('interviewer_id')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        
        if not company_id:
            return jsonify({"success": False, "message": "company_id is required"}), 400
        
        query = db.session.query(Interview).join(JobApplication).join(JobPosting).filter(
            JobPosting.company_id == company_id
        )
        
        if interviewer_id:
            query = query.filter(
                db.or_(
                    Interview.primary_interviewer == interviewer_id,
                    Interview.interview_panel.like(f'%"{interviewer_id}"%')
                )
            )
        
        if date_from:
            query = query.filter(Interview.scheduled_date >= datetime.strptime(date_from, '%Y-%m-%d'))
        
        if date_to:
            query = query.filter(Interview.scheduled_date <= datetime.strptime(date_to, '%Y-%m-%d'))
        
        interviews = query.all()
        
        # Overall interview metrics
        total_interviews = len(interviews)
        completed_interviews = len([i for i in interviews if i.status == 'COMPLETED'])
        no_shows = len([i for i in interviews if i.attendance_status == 'NO_SHOW'])
        cancelled_interviews = len([i for i in interviews if i.status == 'CANCELLED'])
        
        completion_rate = round(completed_interviews / total_interviews * 100, 2) if total_interviews > 0 else 0
        no_show_rate = round(no_shows / total_interviews * 100, 2) if total_interviews > 0 else 0
        
        # Rating analysis
        rated_interviews = [i for i in interviews if i.overall_rating is not None]
        
        avg_rating = 0
        rating_distribution = {}
        
        if rated_interviews:
            total_rating = sum(float(i.overall_rating) for i in rated_interviews)
            avg_rating = round(total_rating / len(rated_interviews), 2)
            
            # Rating distribution
            for interview in rated_interviews:
                rating_bucket = f"{int(float(interview.overall_rating))}.0-{int(float(interview.overall_rating))}.9"
                rating_distribution[rating_bucket] = rating_distribution.get(rating_bucket, 0) + 1
        
        # Interview type analysis
        type_analysis = {}
        for interview in interviews:
            interview_type = interview.interview_type
            if interview_type not in type_analysis:
                type_analysis[interview_type] = {
                    'count': 0,
                    'completed': 0,
                    'avg_rating': 0,
                    'no_shows': 0
                }
            
            type_analysis[interview_type]['count'] += 1
            
            if interview.status == 'COMPLETED':
                type_analysis[interview_type]['completed'] += 1
            
            if interview.attendance_status == 'NO_SHOW':
                type_analysis[interview_type]['no_shows'] += 1
        
        # Calculate averages for each type
        for interview_type, data in type_analysis.items():
            type_interviews = [i for i in interviews if i.interview_type == interview_type and i.overall_rating]
            if type_interviews:
                total_rating = sum(float(i.overall_rating) for i in type_interviews)
                data['avg_rating'] = round(total_rating / len(type_interviews), 2)
        
        # Interviewer performance (if specific interviewer requested)
        interviewer_stats = None
        if interviewer_id:
            interviewer_interviews = [i for i in interviews if i.primary_interviewer == interviewer_id]
            
            if interviewer_interviews:
                interviewer_completed = len([i for i in interviewer_interviews if i.status == 'COMPLETED'])
                interviewer_rated = [i for i in interviewer_interviews if i.overall_rating]
                
                interviewer_stats = {
                    'total_interviews': len(interviewer_interviews),
                    'completed_interviews': interviewer_completed,
                    'completion_rate': round(interviewer_completed / len(interviewer_interviews) * 100, 2),
                    'avg_rating': round(sum(float(i.overall_rating) for i in interviewer_rated) / len(interviewer_rated), 2) if interviewer_rated else 0,
                    'recommendations': {
                        'hire': len([i for i in interviewer_interviews if i.recommendation == 'HIRE']),
                        'no_hire': len([i for i in interviewer_interviews if i.recommendation == 'NO_HIRE'])
                    }
                }
        
        return jsonify({
            "success": True,
            "interview_performance": {
                "overview": {
                    "total_interviews": total_interviews,
                    "completed_interviews": completed_interviews,
                    "completion_rate": completion_rate,
                    "no_show_rate": no_show_rate,
                    "average_rating": avg_rating
                },
                "rating_distribution": rating_distribution,
                "by_interview_type": type_analysis,
                "interviewer_stats": interviewer_stats
            }
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error getting interview performance: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@recruitment_analytics_bp.route('/api/recruitment/analytics/assessment-performance', methods=['GET'])
def get_assessment_performance():
    """Get assessment performance analytics."""
    try:
        company_id = request.args.get('company_id')
        assessment_type = request.args.get('assessment_type')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')

        if not company_id:
            return jsonify({"success": False, "message": "company_id is required"}), 400

        # Get assessments through applications
        query = db.session.query(CandidateAssessment).join(JobApplication).join(JobPosting).filter(
            JobPosting.company_id == company_id
        )

        if assessment_type:
            query = query.filter(CandidateAssessment.assessment_type == assessment_type)

        if date_from:
            query = query.filter(CandidateAssessment.assigned_date >= datetime.strptime(date_from, '%Y-%m-%d'))

        if date_to:
            query = query.filter(CandidateAssessment.assigned_date <= datetime.strptime(date_to, '%Y-%m-%d'))

        assessments = query.all()

        # Overall assessment metrics
        total_assessments = len(assessments)
        completed_assessments = len([a for a in assessments if a.status == 'COMPLETED'])
        passed_assessments = len([a for a in assessments if a.passed is True])

        completion_rate = round(completed_assessments / total_assessments * 100, 2) if total_assessments > 0 else 0
        pass_rate = round(passed_assessments / completed_assessments * 100, 2) if completed_assessments > 0 else 0

        # Score analysis
        scored_assessments = [a for a in assessments if a.percentage_score is not None]

        avg_score = 0
        score_distribution = {}

        if scored_assessments:
            total_score = sum(float(a.percentage_score) for a in scored_assessments)
            avg_score = round(total_score / len(scored_assessments), 2)

            # Score distribution in 10-point buckets
            for assessment in scored_assessments:
                score = float(assessment.percentage_score)
                bucket = f"{int(score//10)*10}-{int(score//10)*10+9}"
                score_distribution[bucket] = score_distribution.get(bucket, 0) + 1

        # Assessment type analysis
        type_analysis = {}
        for assessment in assessments:
            a_type = assessment.assessment_type
            if a_type not in type_analysis:
                type_analysis[a_type] = {
                    'count': 0,
                    'completed': 0,
                    'passed': 0,
                    'avg_score': 0,
                    'completion_rate': 0,
                    'pass_rate': 0
                }

            type_analysis[a_type]['count'] += 1

            if assessment.status == 'COMPLETED':
                type_analysis[a_type]['completed'] += 1

            if assessment.passed is True:
                type_analysis[a_type]['passed'] += 1

        # Calculate rates for each type
        for a_type, data in type_analysis.items():
            if data['count'] > 0:
                data['completion_rate'] = round(data['completed'] / data['count'] * 100, 2)

            if data['completed'] > 0:
                data['pass_rate'] = round(data['passed'] / data['completed'] * 100, 2)

            # Average score for this type
            type_assessments = [a for a in assessments if a.assessment_type == a_type and a.percentage_score]
            if type_assessments:
                total_score = sum(float(a.percentage_score) for a in type_assessments)
                data['avg_score'] = round(total_score / len(type_assessments), 2)

        # Time analysis
        time_analysis = {}
        timed_assessments = [a for a in assessments if a.time_spent_minutes is not None]

        if timed_assessments:
            times = [a.time_spent_minutes for a in timed_assessments]
            time_analysis = {
                'avg_time_minutes': round(sum(times) / len(times), 1),
                'min_time_minutes': min(times),
                'max_time_minutes': max(times),
                'median_time_minutes': sorted(times)[len(times) // 2]
            }

        return jsonify({
            "success": True,
            "assessment_performance": {
                "overview": {
                    "total_assessments": total_assessments,
                    "completed_assessments": completed_assessments,
                    "completion_rate": completion_rate,
                    "pass_rate": pass_rate,
                    "average_score": avg_score
                },
                "score_distribution": score_distribution,
                "by_assessment_type": type_analysis,
                "time_analysis": time_analysis
            }
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error getting assessment performance: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@recruitment_analytics_bp.route('/api/recruitment/analytics/diversity', methods=['GET'])
def get_diversity_analytics():
    """Get diversity and inclusion analytics."""
    try:
        company_id = request.args.get('company_id')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')

        if not company_id:
            return jsonify({"success": False, "message": "company_id is required"}), 400

        # Get applications with diversity data
        query = db.session.query(JobApplication).join(JobPosting).filter(
            JobPosting.company_id == company_id
        )

        if date_from:
            query = query.filter(JobApplication.submitted_date >= datetime.strptime(date_from, '%Y-%m-%d'))

        if date_to:
            query = query.filter(JobApplication.submitted_date <= datetime.strptime(date_to, '%Y-%m-%d'))

        applications = query.all()

        # Analyze diversity at each stage
        stages = ['SUBMITTED', 'SCREENING', 'INTERVIEWING', 'OFFER', 'HIRED']
        diversity_by_stage = {}

        for stage in stages:
            stage_applications = [a for a in applications if a.status == stage or
                                (stage == 'SUBMITTED' and a.status in stages)]

            diversity_data = []
            for app in stage_applications:
                if app.diversity_data:
                    try:
                        diversity_info = json.loads(app.diversity_data)
                        diversity_data.append(diversity_info)
                    except:
                        continue

            # Analyze gender distribution
            gender_dist = {}
            age_dist = {}
            education_dist = {}

            for data in diversity_data:
                # Gender
                gender = data.get('gender', 'Not Specified')
                gender_dist[gender] = gender_dist.get(gender, 0) + 1

                # Age groups
                age = data.get('age')
                if age:
                    if age < 25:
                        age_group = 'Under 25'
                    elif age < 35:
                        age_group = '25-34'
                    elif age < 45:
                        age_group = '35-44'
                    elif age < 55:
                        age_group = '45-54'
                    else:
                        age_group = '55+'

                    age_dist[age_group] = age_dist.get(age_group, 0) + 1

                # Education
                education = data.get('education', 'Not Specified')
                education_dist[education] = education_dist.get(education, 0) + 1

            diversity_by_stage[stage] = {
                'total_candidates': len(stage_applications),
                'with_diversity_data': len(diversity_data),
                'gender_distribution': gender_dist,
                'age_distribution': age_dist,
                'education_distribution': education_dist
            }

        # Calculate diversity ratios
        diversity_ratios = {}
        if 'SUBMITTED' in diversity_by_stage and 'HIRED' in diversity_by_stage:
            submitted = diversity_by_stage['SUBMITTED']
            hired = diversity_by_stage['HIRED']

            # Gender ratio analysis
            for gender in submitted['gender_distribution']:
                submitted_count = submitted['gender_distribution'][gender]
                hired_count = hired['gender_distribution'].get(gender, 0)

                if submitted_count > 0:
                    ratio = round(hired_count / submitted_count * 100, 2)
                    diversity_ratios[f'{gender}_hire_rate'] = ratio

        return jsonify({
            "success": True,
            "diversity_analytics": {
                "by_stage": diversity_by_stage,
                "diversity_ratios": diversity_ratios,
                "summary": {
                    "total_applications": len(applications),
                    "applications_with_diversity_data": len([a for a in applications if a.diversity_data])
                }
            }
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error getting diversity analytics: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@recruitment_analytics_bp.route('/api/recruitment/analytics/cost-analysis', methods=['GET'])
def get_cost_analysis():
    """Get recruitment cost analysis."""
    try:
        company_id = request.args.get('company_id')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')

        if not company_id:
            return jsonify({"success": False, "message": "company_id is required"}), 400

        # Get job postings with channel costs
        query = db.session.query(JobPosting).filter_by(company_id=company_id)

        if date_from:
            query = query.filter(JobPosting.posting_date >= datetime.strptime(date_from, '%Y-%m-%d').date())

        if date_to:
            query = query.filter(JobPosting.posting_date <= datetime.strptime(date_to, '%Y-%m-%d').date())

        postings = query.all()

        # Calculate costs by channel
        channel_costs = {}
        total_cost = 0
        total_hires = 0

        for posting in postings:
            # Get applications and hires for this posting
            applications = db.session.query(JobApplication).filter_by(posting_id=posting.posting_id).all()
            hires = len([a for a in applications if a.status == 'HIRED'])
            total_hires += hires

            # Get channel costs
            for channel in posting.posting_channels:
                channel_name = channel.channel_name
                channel_cost = float(channel.cost) if channel.cost else 0

                if channel_name not in channel_costs:
                    channel_costs[channel_name] = {
                        'total_cost': 0,
                        'applications': 0,
                        'hires': 0,
                        'cost_per_application': 0,
                        'cost_per_hire': 0
                    }

                channel_costs[channel_name]['total_cost'] += channel_cost
                channel_costs[channel_name]['applications'] += channel.applications
                channel_costs[channel_name]['hires'] += hires  # Approximate attribution
                total_cost += channel_cost

        # Calculate cost metrics for each channel
        for channel, data in channel_costs.items():
            if data['applications'] > 0:
                data['cost_per_application'] = round(data['total_cost'] / data['applications'], 2)

            if data['hires'] > 0:
                data['cost_per_hire'] = round(data['total_cost'] / data['hires'], 2)

        # Overall cost metrics
        overall_cost_per_hire = round(total_cost / total_hires, 2) if total_hires > 0 else 0

        # Department cost analysis
        dept_costs = {}
        for posting in postings:
            dept = posting.department or 'Unknown'
            if dept not in dept_costs:
                dept_costs[dept] = {
                    'postings': 0,
                    'total_cost': 0,
                    'hires': 0
                }

            dept_costs[dept]['postings'] += 1

            # Sum channel costs for this posting
            posting_cost = sum(float(c.cost) if c.cost else 0 for c in posting.posting_channels)
            dept_costs[dept]['total_cost'] += posting_cost

            # Count hires
            applications = db.session.query(JobApplication).filter_by(posting_id=posting.posting_id).all()
            dept_hires = len([a for a in applications if a.status == 'HIRED'])
            dept_costs[dept]['hires'] += dept_hires

        # Calculate department cost per hire
        for dept, data in dept_costs.items():
            if data['hires'] > 0:
                data['cost_per_hire'] = round(data['total_cost'] / data['hires'], 2)
            else:
                data['cost_per_hire'] = 0

        return jsonify({
            "success": True,
            "cost_analysis": {
                "overview": {
                    "total_cost": total_cost,
                    "total_hires": total_hires,
                    "cost_per_hire": overall_cost_per_hire,
                    "total_postings": len(postings)
                },
                "by_channel": channel_costs,
                "by_department": dept_costs
            }
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error getting cost analysis: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500


@recruitment_analytics_bp.route('/api/recruitment/analytics/predictive', methods=['GET'])
def get_predictive_analytics():
    """Get predictive recruitment analytics."""
    try:
        company_id = request.args.get('company_id')

        if not company_id:
            return jsonify({"success": False, "message": "company_id is required"}), 400

        # Get historical data for predictions
        end_date = date.today()
        start_date = end_date - timedelta(days=365)  # Last year of data

        applications = db.session.query(JobApplication).join(JobPosting).filter(
            JobPosting.company_id == company_id,
            JobApplication.submitted_date >= start_date
        ).all()

        # Predict hiring success probability
        success_factors = {}

        for app in applications:
            source = app.application_source or 'UNKNOWN'

            if source not in success_factors:
                success_factors[source] = {
                    'total': 0,
                    'hired': 0,
                    'success_rate': 0
                }

            success_factors[source]['total'] += 1
            if app.status == 'HIRED':
                success_factors[source]['hired'] += 1

        # Calculate success rates
        for source, data in success_factors.items():
            if data['total'] > 0:
                data['success_rate'] = round(data['hired'] / data['total'] * 100, 2)

        # Predict time to fill positions
        open_positions = db.session.query(JobPosting).filter(
            JobPosting.company_id == company_id,
            JobPosting.status == 'ACTIVE'
        ).all()

        position_predictions = []

        for position in open_positions:
            # Calculate average time to hire for similar positions
            similar_positions = db.session.query(JobApplication).join(JobPosting).filter(
                JobPosting.company_id == company_id,
                JobPosting.department == position.department,
                JobPosting.employment_type == position.employment_type,
                JobApplication.status == 'HIRED'
            ).all()

            if similar_positions:
                avg_days = sum(app.get_days_in_process() for app in similar_positions) / len(similar_positions)

                # Calculate days since posting
                days_since_posting = (date.today() - position.posting_date).days if position.posting_date else 0
                estimated_days_remaining = max(0, int(avg_days - days_since_posting))

                position_predictions.append({
                    'posting_id': str(position.posting_id),
                    'job_title': position.job_title,
                    'department': position.department,
                    'days_since_posting': days_since_posting,
                    'estimated_days_remaining': estimated_days_remaining,
                    'predicted_fill_date': (date.today() + timedelta(days=estimated_days_remaining)).strftime('%Y-%m-%d')
                })

        # Hiring demand forecast
        monthly_hires = {}
        for app in applications:
            if app.status == 'HIRED' and app.decision_date:
                month_key = app.decision_date.strftime('%Y-%m')
                monthly_hires[month_key] = monthly_hires.get(month_key, 0) + 1

        # Simple trend analysis for next 3 months
        recent_months = sorted(monthly_hires.keys())[-6:]  # Last 6 months
        if recent_months:
            avg_monthly_hires = sum(monthly_hires[month] for month in recent_months) / len(recent_months)

            forecast = []
            for i in range(1, 4):  # Next 3 months
                forecast_date = date.today() + timedelta(days=30*i)
                forecast.append({
                    'month': forecast_date.strftime('%Y-%m'),
                    'predicted_hires': int(avg_monthly_hires),
                    'confidence': 'MEDIUM'  # Simple confidence level
                })
        else:
            forecast = []

        return jsonify({
            "success": True,
            "predictive_analytics": {
                "success_probability_by_source": success_factors,
                "position_fill_predictions": position_predictions,
                "hiring_demand_forecast": forecast,
                "model_info": {
                    "data_period": f"{start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}",
                    "total_applications_analyzed": len(applications),
                    "prediction_accuracy": "ESTIMATED"  # Would be calculated with actual ML model
                }
            }
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error getting predictive analytics: {e}")
        return jsonify({"success": False, "message": "Internal server error"}), 500
