"""
Business-Focused Shift Scheduling API
Provides user-friendly endpoints for creating and managing shift templates using business language.
"""

from flask import Blueprint, request, jsonify, g, current_app
from application.decorators.role_required import roles_required
from application.decorators.token_required import token_required
from application.Models.employees.shift_template import ShiftTemplate
from application.Models.employees.shift_schedule import ShiftSchedule
from application.Models.company import Company
from datetime import datetime, date
import uuid

business_scheduling_bp = Blueprint('business_scheduling', __name__)


@business_scheduling_bp.route('/api/scheduling/create-structured-template', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def create_structured_template():
    """Create a shift template using structured business requirements with validation."""
    try:
        data = request.get_json()
        current_app.logger.info(f"Creating structured template - Request data: {data}")

        company_id = data.get('company_id')
        current_app.logger.info(f"Company ID: {company_id}")

        if not company_id:
            current_app.logger.warning("Company ID is missing from request")
            return jsonify({"message": "Company ID is required"}), 400

        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        current_app.logger.info(f"Database name for company {company_id}: {database_name}")
        if not database_name:
            current_app.logger.error(f"Company with ID {company_id} not found")
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404

        # Validate required fields
        required_fields = ['name', 'pattern_type', 'business_requirements']
        current_app.logger.info(f"Validating required fields: {required_fields}")
        for field in required_fields:
            if not data.get(field):
                current_app.logger.warning(f"Missing required field: {field}")
                return jsonify({"message": f"{field.replace('_', ' ').title()} is required"}), 400

        # Validate pattern type
        pattern_type = data['pattern_type']
        current_app.logger.info(f"Pattern type: {pattern_type}")
        if pattern_type not in ['weekly', 'monthly', 'annual']:
            current_app.logger.error(f"Invalid pattern type: {pattern_type}")
            return jsonify({"message": "pattern_type must be 'weekly', 'monthly', or 'annual'"}), 400

        # Import the processor
        from application.Services.business_requirements_processor import BusinessRequirementsProcessor

        # Apply smart defaults to business requirements
        current_app.logger.info(f"Original business requirements: {data['business_requirements']}")
        enhanced_business_req = BusinessRequirementsProcessor.apply_smart_defaults(
            data['business_requirements']
        )
        current_app.logger.info(f"Enhanced business requirements: {enhanced_business_req}")

        # Validate structured business requirements
        current_app.logger.info("Starting business requirements validation")
        is_valid, error_message = BusinessRequirementsProcessor.validate_schema(enhanced_business_req)
        current_app.logger.info(f"Validation result - Valid: {is_valid}, Error: {error_message}")
        if not is_valid:
            current_app.logger.error(f"Business requirements validation failed: {error_message}")
            return jsonify({
                "message": "Invalid business requirements structure",
                "error": error_message,
                "help": "Please check the business requirements format and try again"
            }), 400

        # Convert business requirements to calculation data
        try:
            calculation_data = BusinessRequirementsProcessor.convert_to_calculation_data(
                enhanced_business_req, pattern_type
            )
        except Exception as e:
            return jsonify({
                "message": "Error processing business requirements",
                "error": str(e)
            }), 400

        # Get staffing summary for response
        staffing_summary = BusinessRequirementsProcessor.get_staffing_summary(enhanced_business_req)

        # Prepare template data
        template_data = {
            'company_id': company_id,
            'name': data['name'],
            'description': data.get('description', f"Structured template: {data['name']}"),
            'pattern_type': pattern_type,
            'business_requirements': enhanced_business_req,
            'coverage_requirements': calculation_data.get('coverage_requirements', {}),
            'rotation_cycle': calculation_data.get('cycle_length', 7),
            'pattern_data': calculation_data,  # Store calculation data in pattern_data for compatibility
            'is_active': True,  # Explicitly set to ensure template is active
            'created_by': g.user.get('user_id')
        }

        # Connect to the database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            try:
                # Validate that all referenced shifts exist
                from application.Models.employees.shift import Shift
                shift_validation_errors = []

                for shift in enhanced_business_req.get('shifts', []):
                    shift_id = shift.get('shift_id')
                    if shift_id:
                        try:
                            # Validate UUID format first
                            uuid.UUID(shift_id)
                            existing_shift = Shift.get_shift_by_id(session, shift_id)
                            if not existing_shift:
                                shift_validation_errors.append(f"Shift with ID {shift_id} not found")
                            elif existing_shift.company_id != company_id:
                                shift_validation_errors.append(f"Shift {shift_id} does not belong to this company")
                        except ValueError:
                            shift_validation_errors.append(f"Invalid shift ID format: {shift_id}")

                if shift_validation_errors:
                    return jsonify({
                        "message": "Invalid shift references in business requirements",
                        "errors": shift_validation_errors
                    }), 400

                template = ShiftTemplate.create_template(session, **template_data)

                if template:
                    return jsonify({
                        "message": "Structured shift template created successfully",
                        "template": {
                            "template_id": str(template.template_id),
                            "name": template.name,
                            "description": template.description,
                            "pattern_type": template.pattern_type,
                            "business_requirements": template.business_requirements,
                            "staffing_summary": staffing_summary,
                            "created_at": template.created_at.strftime('%Y-%m-%d %H:%M:%S')
                        }
                    }), 201
                else:
                    return jsonify({"message": "Failed to create structured template"}), 400

            except Exception as e:
                return jsonify({
                    "message": "Error creating structured template",
                    "error": str(e)
                }), 500

    except Exception as e:
        return jsonify({"message": f"Error creating structured template: {str(e)}"}), 500


@business_scheduling_bp.route('/api/scheduling/validate-requirements', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def validate_business_requirements():
    """Validate business requirements structure without creating a template."""
    try:
        data = request.get_json()
        
        if not data.get('business_requirements'):
            return jsonify({"message": "business_requirements is required"}), 400

        # Import the processor
        from application.Services.business_requirements_processor import BusinessRequirementsProcessor

        # Apply smart defaults
        enhanced_business_req = BusinessRequirementsProcessor.apply_smart_defaults(
            data['business_requirements']
        )

        # Validate the structure
        is_valid, error_message = BusinessRequirementsProcessor.validate_schema(enhanced_business_req)
        
        if is_valid:
            # Get staffing summary
            staffing_summary = BusinessRequirementsProcessor.get_staffing_summary(enhanced_business_req)
            
            return jsonify({
                "valid": True,
                "message": "Business requirements are valid",
                "enhanced_requirements": enhanced_business_req,
                "staffing_summary": staffing_summary
            }), 200
        else:
            return jsonify({
                "valid": False,
                "message": "Invalid business requirements",
                "error": error_message,
                "enhanced_requirements": enhanced_business_req
            }), 400

    except Exception as e:
        return jsonify({
            "valid": False,
            "message": f"Error validating business requirements: {str(e)}"
        }), 500


@business_scheduling_bp.route('/api/scheduling/template-analysis/<template_id>', methods=['GET'])
@token_required
def get_template_analysis(template_id):
    """Get detailed analysis of a shift template's business requirements."""
    try:
        company_id = request.args.get('company_id')
        
        if not company_id:
            return jsonify({"message": "Company ID is required"}), 400

        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404

        # Connect to the database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            template = ShiftTemplate.get_by_id(session, template_id)
            
            if not template:
                return jsonify({"message": "Template not found"}), 404

            # Verify template belongs to the company
            if template.company_id != company_id:
                return jsonify({"message": "Unauthorized access to this template"}), 403

            # Get comprehensive analysis with enriched shift details
            from application.Services.business_requirements_processor import BusinessRequirementsProcessor

            enriched_business_req = None
            if template.business_requirements:
                enriched_business_req = BusinessRequirementsProcessor.enrich_business_requirements_with_shift_details(
                    session, template.business_requirements, company_id
                )

            analysis = {
                "template_info": {
                    "template_id": str(template.template_id),
                    "name": template.name,
                    "description": template.description,
                    "pattern_type": template.pattern_type,
                    "created_at": template.created_at.strftime('%Y-%m-%d %H:%M:%S')
                },
                "business_requirements": enriched_business_req or template.business_requirements,
                "staffing_summary": template.get_staffing_summary(),
                "daily_staff_totals": template.get_total_staff_needed_per_day(),
                "work_rules_summary": template.get_work_rules_summary(),
                "weekly_coverage_hours": template.calculate_weekly_coverage_hours(),
                "is_24_7_coverage": template.is_24_7_coverage(),
                "validation_status": template.validate_business_requirements_structure()
            }

            return jsonify({
                "message": "Template analysis retrieved successfully",
                "analysis": analysis
            }), 200

    except Exception as e:
        return jsonify({"message": f"Error getting template analysis: {str(e)}"}), 500


@business_scheduling_bp.route('/api/scheduling/templates/business-friendly', methods=['GET'])
@token_required
def get_business_friendly_templates():
    """Get all templates with business-friendly summaries."""
    try:
        company_id = request.args.get('company_id')
        active_only = request.args.get('active_only', 'true').lower() == 'true'
        
        if not company_id:
            return jsonify({"message": "Company ID is required"}), 400

        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404

        # Connect to the database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            current_app.logger.info(f"Looking for templates in database: {database_name} for company: {company_id}, active_only: {active_only}")
            templates = ShiftTemplate.get_by_company(session, company_id, active_only)
            current_app.logger.info(f"Found {len(templates)} templates")

            business_friendly_templates = []
            for template in templates:
                template_summary = {
                    "template_id": str(template.template_id),
                    "name": template.name,
                    "description": template.description,
                    "pattern_type": template.pattern_type,
                    "has_business_requirements": template.business_requirements is not None,
                    "created_at": template.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    "is_active": template.is_active
                }
                
                # Add business summaries if available
                if template.business_requirements:
                    template_summary.update({
                        "staffing_summary": template.get_staffing_summary(),
                        "work_rules_summary": template.get_work_rules_summary(),
                        "weekly_coverage_hours": template.calculate_weekly_coverage_hours(),
                        "is_24_7_coverage": template.is_24_7_coverage()
                    })
                
                business_friendly_templates.append(template_summary)

            return jsonify({
                "message": "Business-friendly templates retrieved successfully",
                "templates": business_friendly_templates,
                "count": len(business_friendly_templates)
            }), 200

    except Exception as e:
        return jsonify({"message": f"Error getting business-friendly templates: {str(e)}"}), 500


@business_scheduling_bp.route('/api/scheduling/available-shifts', methods=['GET'])
@token_required
def get_available_shifts():
    """Get available shifts for creating business templates."""
    try:
        company_id = request.args.get('company_id')

        if not company_id:
            return jsonify({"message": "Company ID is required"}), 400

        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404

        # Connect to the database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            from application.Models.employees.shift import Shift

            shifts = Shift.get_all_shifts(session, company_id)
            active_shifts = [shift for shift in shifts if shift.is_active]

            shifts_data = []
            for shift in active_shifts:
                shifts_data.append({
                    "shift_id": str(shift.shift_id),
                    "name": shift.name,
                    "description": shift.description,
                    "start_time": shift.start_time.strftime('%H:%M') if shift.start_time else None,
                    "end_time": shift.end_time.strftime('%H:%M') if shift.end_time else None,
                    "is_night_shift": shift.is_night_shift,
                    "working_days": shift.working_days,
                    "break_duration": shift.break_duration
                })

            return jsonify({
                "message": "Available shifts retrieved successfully",
                "shifts": shifts_data,
                "count": len(shifts_data)
            }), 200

    except Exception as e:
        return jsonify({"message": f"Error getting available shifts: {str(e)}"}), 500


@business_scheduling_bp.route('/api/scheduling/examples/business-requirements', methods=['GET'])
@token_required
def get_business_requirements_examples():
    """Get example business requirements for different industries."""

    examples = {
        "hospital_icu": {
            "name": "Hospital ICU 24/7 Coverage",
            "pattern_type": "weekly",
            "business_requirements": {
                "schedule_pattern": {
                    "type": "weekly",
                    "cycle_length": 7,
                    "description": "Continuous 24/7 patient care coverage"
                },
                "shifts": [
                    {
                        "shift_id": "REPLACE_WITH_ACTUAL_DAY_SHIFT_ID",
                        "staffing": {"minimum_staff": 6, "preferred_staff": 8, "maximum_staff": 10},
                        "days_of_week": [1, 2, 3, 4, 5, 6, 7],
                        "roles_required": [
                            {"role": "RN", "minimum": 4, "preferred": 5},
                            {"role": "LPN", "minimum": 2, "preferred": 3}
                        ]
                    },
                    {
                        "shift_id": "REPLACE_WITH_ACTUAL_NIGHT_SHIFT_ID",
                        "staffing": {"minimum_staff": 4, "preferred_staff": 5, "maximum_staff": 7},
                        "days_of_week": [1, 2, 3, 4, 5, 6, 7],
                        "roles_required": [
                            {"role": "RN", "minimum": 3, "preferred": 4},
                            {"role": "LPN", "minimum": 1, "preferred": 1}
                        ]
                    }
                ],
                "work_rules": {
                    "max_consecutive_days": 4,
                    "min_rest_days": 2,
                    "max_hours_per_week": 48,
                    "overtime_allowed": True,
                    "weekend_requirements": "at_least_one_off"
                },
                "coverage_rules": {
                    "minimum_coverage_percentage": 95,
                    "allow_understaffing": False,
                    "emergency_coverage_plan": "mandatory_overtime"
                }
            }
        },
        "office_customer_service": {
            "name": "Customer Service Team",
            "pattern_type": "weekly",
            "business_requirements": {
                "schedule_pattern": {"type": "weekly", "cycle_length": 7},
                "shifts": [
                    {
                        "shift_id": "REPLACE_WITH_ACTUAL_BUSINESS_HOURS_SHIFT_ID",
                        "staffing": {"minimum_staff": 8, "preferred_staff": 12, "maximum_staff": 15},
                        "days_of_week": [1, 2, 3, 4, 5],
                        "roles_required": [
                            {"role": "Customer Service Rep", "minimum": 6, "preferred": 8},
                            {"role": "Team Lead", "minimum": 1, "preferred": 2}
                        ]
                    }
                ],
                "work_rules": {
                    "max_consecutive_days": 5,
                    "min_rest_days": 2,
                    "max_hours_per_week": 40,
                    "overtime_allowed": False,
                    "weekend_requirements": "both_off"
                }
            }
        },
        "manufacturing_3_shift": {
            "name": "Manufacturing 3-Shift Rotation",
            "pattern_type": "weekly",
            "business_requirements": {
                "schedule_pattern": {"type": "weekly", "cycle_length": 21},
                "shifts": [
                    {
                        "shift_id": "REPLACE_WITH_ACTUAL_MORNING_SHIFT_ID",
                        "staffing": {"minimum_staff": 15, "preferred_staff": 18, "maximum_staff": 20},
                        "days_of_week": [1, 2, 3, 4, 5]
                    },
                    {
                        "shift_id": "REPLACE_WITH_ACTUAL_AFTERNOON_SHIFT_ID",
                        "staffing": {"minimum_staff": 12, "preferred_staff": 15, "maximum_staff": 18},
                        "days_of_week": [1, 2, 3, 4, 5]
                    },
                    {
                        "shift_id": "REPLACE_WITH_ACTUAL_NIGHT_SHIFT_ID",
                        "staffing": {"minimum_staff": 8, "preferred_staff": 10, "maximum_staff": 12},
                        "days_of_week": [1, 2, 3, 4, 5]
                    }
                ],
                "work_rules": {
                    "max_consecutive_days": 5,
                    "min_rest_days": 2,
                    "max_hours_per_week": 40,
                    "overtime_allowed": True,
                    "weekend_requirements": "both_off"
                }
            }
        }
    }

    return jsonify({
        "message": "Business requirements examples retrieved successfully",
        "examples": examples,
        "usage": {
            "description": "Use these examples as templates for creating your own business requirements",
            "instructions": [
                "1. First get available shifts: GET /api/scheduling/available-shifts?company_id=xxx",
                "2. Replace REPLACE_WITH_ACTUAL_*_SHIFT_ID with actual shift IDs from step 1",
                "3. Validate requirements: POST /api/scheduling/validate-requirements",
                "4. Create template: POST /api/scheduling/create-structured-template"
            ],
            "endpoint": "POST /api/scheduling/create-structured-template",
            "validation_endpoint": "POST /api/scheduling/validate-requirements",
            "shifts_endpoint": "GET /api/scheduling/available-shifts"
        }
    }), 200


@business_scheduling_bp.route('/api/scheduling/generate-schedule', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def generate_schedule():
    """Generate a schedule framework from template. Use /assign-schedule to assign employees."""
    try:
        data = request.get_json()
        current_app.logger.info(f"Generating schedule - Request data: {data}")

        # Validate required fields
        required_fields = ['company_id', 'template_id', 'start_date', 'end_date', 'schedule_name']
        for field in required_fields:
            if not data.get(field):
                return jsonify({"message": f"{field} is required"}), 400

        company_id = data['company_id']
        template_id = data['template_id']
        start_date = data['start_date']
        end_date = data['end_date']
        schedule_name = data['schedule_name']

        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404

        # Connect to the database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Verify template exists
            template = ShiftTemplate.get_by_id(session, template_id)
            if not template:
                return jsonify({"message": f"Template with ID {template_id} not found"}), 404

            # Create schedule record
            schedule_data = {
                'company_id': company_id,
                'template_id': template_id,
                'schedule_name': schedule_name,
                'start_date': start_date,
                'end_date': end_date,
                'status': 'draft',
                'generated_by': g.user.get('user_id')
            }

            schedule = ShiftSchedule.create_schedule(session, **schedule_data)

            if schedule:
                return jsonify({
                    "message": "Schedule generated successfully",
                    "schedule": {
                        "schedule_id": str(schedule.schedule_id),
                        "schedule_name": schedule.schedule_name,
                        "template_id": str(schedule.template_id),
                        "start_date": schedule.start_date.strftime('%Y-%m-%d'),
                        "end_date": schedule.end_date.strftime('%Y-%m-%d'),
                        "status": schedule.status,
                        "created_at": schedule.created_at.strftime('%Y-%m-%d %H:%M:%S')
                    }
                }), 201
            else:
                return jsonify({"message": "Failed to generate schedule"}), 400

    except Exception as e:
        current_app.logger.error(f"Error generating schedule: {str(e)}")
        return jsonify({"message": f"Error generating schedule: {str(e)}"}), 500


@business_scheduling_bp.route('/api/scheduling/schedules', methods=['GET'])
@token_required
def get_schedules():
    """Get all schedules for a company with filtering."""
    try:
        company_id = request.args.get('company_id')
        department_id = request.args.get('department_id')
        status = request.args.get('status')
        active_only = request.args.get('active_only', 'false').lower() == 'true'

        if not company_id:
            return jsonify({"message": "Company ID is required"}), 400

        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404

        # Connect to the database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            if department_id:
                schedules = ShiftSchedule.get_by_department(session, company_id, department_id, status)
            elif active_only:
                schedules = ShiftSchedule.get_active_schedules(session, company_id)
            else:
                schedules = ShiftSchedule.get_by_company(session, company_id, status)

            schedules_data = []
            for schedule in schedules:
                schedule_dict = schedule.to_dict()

                # Add template info if available
                if schedule.template_id:
                    template = ShiftTemplate.get_template_by_id(session, schedule.template_id)
                    if template:
                        schedule_dict['template_name'] = template.name
                        schedule_dict['template_description'] = template.description

                schedules_data.append(schedule_dict)

            return jsonify({
                "message": "Schedules retrieved successfully",
                "schedules": schedules_data,
                "count": len(schedules_data)
            }), 200

    except Exception as e:
        current_app.logger.error(f"Error getting schedules: {str(e)}")
        return jsonify({"message": f"Error getting schedules: {str(e)}"}), 500


@business_scheduling_bp.route('/api/scheduling/schedules/<schedule_id>', methods=['GET'])
@token_required
def get_schedule_details(schedule_id):
    """Get detailed information about a specific schedule."""
    try:
        company_id = request.args.get('company_id')

        if not company_id:
            return jsonify({"message": "Company ID is required"}), 400

        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404

        # Connect to the database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            schedule = ShiftSchedule.get_by_id(session, schedule_id)

            if not schedule:
                return jsonify({"message": f"Schedule with ID {schedule_id} not found"}), 404

            # Verify schedule belongs to the company
            if schedule.company_id != company_id:
                return jsonify({"message": "Unauthorized access to this schedule"}), 403

            schedule_dict = schedule.to_dict()

            # Add template details
            if schedule.template_id:
                template = ShiftTemplate.get_template_by_id(session, schedule.template_id)
                if template:
                    schedule_dict['template'] = {
                        "template_id": str(template.template_id),
                        "name": template.name,
                        "description": template.description,
                        "pattern_type": template.pattern_type,
                        "business_requirements": template.business_requirements
                    }

            return jsonify({
                "message": "Schedule details retrieved successfully",
                "schedule": schedule_dict
            }), 200

    except Exception as e:
        current_app.logger.error(f"Error getting schedule details: {str(e)}")
        return jsonify({"message": f"Error getting schedule details: {str(e)}"}), 500


@business_scheduling_bp.route('/api/scheduling/debug/templates', methods=['GET'])
@token_required
def debug_templates():
    """Debug endpoint to see raw template data."""
    try:
        company_id = request.args.get('company_id')

        if not company_id:
            return jsonify({"message": "Company ID is required"}), 400

        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404

        current_app.logger.info(f"Debug: Using database {database_name} for company {company_id}")

        # Connect to the database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Get ALL templates regardless of active status
            all_templates = session.query(ShiftTemplate).all()
            current_app.logger.info(f"Debug: Found {len(all_templates)} total templates")

            # Get templates for this company
            company_templates = session.query(ShiftTemplate).filter_by(company_id=company_id).all()
            current_app.logger.info(f"Debug: Found {len(company_templates)} templates for company {company_id}")

            # Get active templates for this company
            active_templates = session.query(ShiftTemplate).filter_by(company_id=company_id, is_active=True).all()
            current_app.logger.info(f"Debug: Found {len(active_templates)} active templates for company {company_id}")

            debug_data = {
                "database_name": database_name,
                "company_id": company_id,
                "total_templates_in_db": len(all_templates),
                "templates_for_company": len(company_templates),
                "active_templates_for_company": len(active_templates),
                "all_templates": [],
                "company_templates": [],
                "active_templates": []
            }

            # Add details for all templates
            for template in all_templates:
                debug_data["all_templates"].append({
                    "template_id": str(template.template_id),
                    "name": template.name,
                    "company_id": template.company_id,
                    "is_active": template.is_active,
                    "created_at": template.created_at.strftime('%Y-%m-%d %H:%M:%S') if template.created_at else None
                })

            # Add details for company templates
            for template in company_templates:
                debug_data["company_templates"].append({
                    "template_id": str(template.template_id),
                    "name": template.name,
                    "company_id": template.company_id,
                    "is_active": template.is_active,
                    "created_at": template.created_at.strftime('%Y-%m-%d %H:%M:%S') if template.created_at else None
                })

            # Add details for active templates
            for template in active_templates:
                debug_data["active_templates"].append({
                    "template_id": str(template.template_id),
                    "name": template.name,
                    "company_id": template.company_id,
                    "is_active": template.is_active,
                    "created_at": template.created_at.strftime('%Y-%m-%d %H:%M:%S') if template.created_at else None
                })

            return jsonify(debug_data), 200

    except Exception as e:
        current_app.logger.error(f"Debug error: {str(e)}")
        return jsonify({"message": f"Debug error: {str(e)}"}), 500


@business_scheduling_bp.route('/api/scheduling/assign-schedule', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def assign_schedule():
    """Assign a schedule to employees, departments, or both."""
    try:
        data = request.get_json()
        current_app.logger.info(f"Assigning schedule - Request data: {data}")

        # Validate required fields
        required_fields = ['company_id', 'schedule_id', 'assignment_type', 'target_ids']
        for field in required_fields:
            if not data.get(field):
                return jsonify({"message": f"{field} is required"}), 400

        company_id = data['company_id']
        schedule_id = data['schedule_id']
        assignment_type = data['assignment_type']  # 'employee', 'department', 'mixed'
        target_ids = data['target_ids']  # List of employee_ids or department_ids
        assignment_strategy = data.get('assignment_strategy', 'automatic')  # 'automatic', 'manual'

        # Validate assignment_type
        valid_types = ['employee', 'department', 'mixed']
        if assignment_type not in valid_types:
            return jsonify({"message": f"assignment_type must be one of: {valid_types}"}), 400

        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404

        # Connect to the database
        from app import db_connection
        with db_connection.get_session(database_name) as session:
            # Verify schedule exists
            schedule = ShiftSchedule.get_by_id(session, schedule_id)
            if not schedule:
                return jsonify({"message": f"Schedule with ID {schedule_id} not found"}), 404

            # Verify schedule belongs to the company
            if schedule.company_id != company_id:
                return jsonify({"message": "Unauthorized access to this schedule"}), 403

            # Get the template to understand requirements
            template = ShiftTemplate.get_by_id(session, schedule.template_id)
            if not template:
                return jsonify({"message": "Template not found for this schedule"}), 404

            assignments_created = []
            errors = []

            # Process assignments based on type
            if assignment_type == 'employee':
                assignments_created, errors = _assign_to_employees(
                    session, schedule, template, target_ids, assignment_strategy
                )
            elif assignment_type == 'department':
                assignments_created, errors = _assign_to_departments(
                    session, schedule, template, target_ids, assignment_strategy
                )
            elif assignment_type == 'mixed':
                # Handle mixed assignments (employees and departments)
                employee_ids = [tid for tid in target_ids if tid.get('type') == 'employee']
                department_ids = [tid for tid in target_ids if tid.get('type') == 'department']

                emp_assignments, emp_errors = _assign_to_employees(
                    session, schedule, template, [e['id'] for e in employee_ids], assignment_strategy
                )
                dept_assignments, dept_errors = _assign_to_departments(
                    session, schedule, template, [d['id'] for d in department_ids], assignment_strategy
                )

                assignments_created = emp_assignments + dept_assignments
                errors = emp_errors + dept_errors

            return jsonify({
                "message": "Schedule assignment completed",
                "schedule_id": schedule_id,
                "assignments_created": len(assignments_created),
                "assignments": assignments_created,
                "errors": errors,
                "summary": {
                    "total_assignments": len(assignments_created),
                    "failed_assignments": len(errors),
                    "assignment_type": assignment_type,
                    "assignment_strategy": assignment_strategy
                }
            }), 201

    except Exception as e:
        current_app.logger.error(f"Error assigning schedule: {str(e)}")
        return jsonify({"message": f"Error assigning schedule: {str(e)}"}), 500


def _assign_to_employees(session, schedule, template, employee_ids, strategy):
    """Assign schedule to specific employees."""
    assignments = []
    errors = []

    try:
        from application.Models.employees.employee import Employee
        from application.Models.employees.employee_shift import EmployeeShift
        from application.Models.employees.shift import Shift

        # Get shift information from template
        business_req = template.business_requirements
        if not business_req or 'shifts' not in business_req:
            errors.append("Template has no shift requirements")
            return assignments, errors

        # For now, use the first shift in the template
        # TODO: Handle multiple shifts per template
        shift_config = business_req['shifts'][0]
        shift_id = shift_config.get('shift_id')

        if not shift_id:
            errors.append("No shift_id found in template")
            return assignments, errors

        # Verify shift exists
        shift = Shift.get_shift_by_id(session, shift_id)
        if not shift:
            errors.append(f"Shift {shift_id} not found")
            return assignments, errors

        # Create assignments for each employee
        for employee_id in employee_ids:
            try:
                # Verify employee exists
                employee = Employee.get_employee_by_id(session, employee_id)
                if not employee:
                    errors.append(f"Employee {employee_id} not found")
                    continue

                # Create assignment
                assignment_data = {
                    'employee_id': employee_id,
                    'shift_id': shift_id,
                    'effective_start_date': schedule.start_date,
                    'effective_end_date': schedule.end_date,
                    'created_by': schedule.generated_by
                }

                assignment = EmployeeShift.create_assignment(session, **assignment_data)
                if assignment:
                    assignments.append({
                        "assignment_id": str(assignment.assignment_id),
                        "employee_id": employee_id,
                        "shift_id": shift_id,
                        "start_date": schedule.start_date.strftime('%Y-%m-%d'),
                        "end_date": schedule.end_date.strftime('%Y-%m-%d')
                    })
                else:
                    errors.append(f"Failed to create assignment for employee {employee_id}")

            except Exception as e:
                errors.append(f"Error assigning employee {employee_id}: {str(e)}")

        return assignments, errors

    except Exception as e:
        errors.append(f"Error in employee assignment: {str(e)}")
        return assignments, errors


def _assign_to_departments(session, schedule, template, department_ids, strategy):
    """Assign schedule to all employees in specified departments."""
    assignments = []
    errors = []

    try:
        from application.Models.employees.employee import Employee

        # Get all employees from specified departments
        all_employee_ids = []
        for dept_id in department_ids:
            try:
                dept_employees = Employee.get_by_department(session, dept_id)
                dept_employee_ids = [str(emp.employee_id) for emp in dept_employees]
                all_employee_ids.extend(dept_employee_ids)
                current_app.logger.info(f"Found {len(dept_employee_ids)} employees in department {dept_id}")
            except Exception as e:
                errors.append(f"Error getting employees for department {dept_id}: {str(e)}")

        if not all_employee_ids:
            errors.append("No employees found in specified departments")
            return assignments, errors

        # Remove duplicates
        unique_employee_ids = list(set(all_employee_ids))
        current_app.logger.info(f"Assigning schedule to {len(unique_employee_ids)} unique employees")

        # Use employee assignment logic
        return _assign_to_employees(session, schedule, template, unique_employee_ids, strategy)

    except Exception as e:
        errors.append(f"Error in department assignment: {str(e)}")
        return assignments, errors
