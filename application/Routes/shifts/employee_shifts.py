from flask import Blueprint, request, jsonify, current_app as app
import uuid
from application.Models.employees import Shift, EmployeeShift, Employee
from application.Models.company import Company
from application.Models.Msg import Msg
import jsons
from datetime import datetime, date, time
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.Helpers.helper_methods import HelperMethods

employee_shifts_bp = Blueprint('employee_shifts', __name__)

@employee_shifts_bp.route('/api/employee-shifts', methods=['GET'])
@token_required
def get_employee_shifts():
    """Get shift assignments with pagination and filtering."""
    company_id = request.args.get('company_id')
    employee_id = request.args.get('employee_id')
    shift_id = request.args.get('shift_id')
    active_only = request.args.get('active_only', default='true').lower() == 'true'
    current_date_str = request.args.get('current_date')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Get pagination parameters
    page = request.args.get('page', default=1, type=int)
    per_page = request.args.get('per_page', default=10, type=int)

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Determine which query to run based on provided parameters
        if employee_id:
            # Get shifts for a specific employee
            assignments = EmployeeShift.get_employee_shifts(
                session,
                employee_id,
                active_only=active_only,
                current_date=current_date_str if current_date_str else None
            )
        elif shift_id:
            # Get employees for a specific shift
            assignments = EmployeeShift.get_shift_employees(
                session,
                shift_id,
                active_only=active_only,
                current_date=current_date_str if current_date_str else None
            )
        else:
            # Get all assignments for the company
            assignments = session.query(EmployeeShift).all()
            if not assignments:
                return jsonify({"message": "No assignments found for the company"}), 400

        # Calculate total count
        total_count = len(assignments)

        # Apply pagination
        start_idx = (page - 1) * per_page
        end_idx = start_idx + per_page
        assignments = assignments[start_idx:end_idx]

        # Convert to dictionaries
        assignments_data = []
        for assignment in assignments:
            assignment_dict = assignment.to_dict()

            # Enrich with employee and shift details
            if employee_id:
                # If we're filtering by employee, add shift details
                shift = Shift.get_shift_by_id(session, assignment.shift_id)
                if shift:
                    assignment_dict['shift'] = shift.to_dict()
            elif shift_id:
                # If we're filtering by shift, add employee details
                employee = Employee.get_employee_by_id(session, assignment.employee_id)
                if employee:
                    assignment_dict['employee'] = employee.to_dict()

            assignments_data.append(assignment_dict)

        # Prepare pagination metadata
        pagination = {
            "total_count": total_count,
            "page": page,
            "per_page": per_page,
            "pages": (total_count + per_page - 1) // per_page if per_page > 0 else 0,  # Ceiling division
            "has_next": page < ((total_count + per_page - 1) // per_page) if per_page > 0 else False,
            "has_prev": page > 1
        }

        return jsons.dump(Msg.success()
                         .add("assignments", assignments_data)
                         .add("pagination", pagination))

@employee_shifts_bp.route('/api/employee-shifts/<assignment_id>', methods=['GET'])
@token_required
def get_employee_shift(assignment_id):
    """Get a specific shift assignment by ID."""
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        assignment = EmployeeShift.get_assignment_by_id(session, assignment_id)

        if not assignment:
            return jsonify({"message": f"Assignment with ID {assignment_id} not found"}), 404

        # Enrich with employee and shift details
        assignment_dict = assignment.to_dict()

        employee = Employee.get_employee_by_id(session, assignment.employee_id)
        if employee:
            assignment_dict['employee'] = employee.to_dict()

        shift = Shift.get_shift_by_id(session, assignment.shift_id)
        if shift:
            assignment_dict['shift'] = shift.to_dict()

        return jsons.dump(Msg.success().add("assignment", assignment_dict))

@employee_shifts_bp.route('/api/employee-shifts', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def create_employee_shift():
    """Assign a shift to an employee."""
    data = request.get_json()
    company_id = data.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Validate required fields
    required_fields = ['employee_id', 'shift_id', 'effective_start_date']
    for field in required_fields:
        if not data.get(field):
            return jsonify({"message": f"{field.replace('_', ' ').title()} is required"}), 400

    # Prepare assignment data
    assignment_data = {
        'employee_id': data.get('employee_id'),
        'shift_id': data.get('shift_id'),
        'effective_start_date': data.get('effective_start_date'),
        'effective_end_date': data.get('effective_end_date'),
        'custom_start_time': data.get('custom_start_time'),
        'custom_end_time': data.get('custom_end_time'),
        'custom_break_duration': data.get('custom_break_duration'),
        'custom_working_days': data.get('custom_working_days'),
        'is_active': data.get('is_active', True),
        'created_by': data.get('created_by')  # This should be the user ID from the token
    }

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Verify employee exists
        employee = Employee.get_employee_by_id(session, assignment_data['employee_id'])
        if not employee:
            return jsonify({"message": f"Employee with ID {assignment_data['employee_id']} not found"}), 404

        # Verify shift exists and belongs to the company
        shift = Shift.get_shift_by_id(session, assignment_data['shift_id'])
        if not shift:
            return jsonify({"message": f"Shift with ID {assignment_data['shift_id']} not found"}), 404

        if shift.company_id != company_id:
            return jsonify({"message": "Unauthorized access to this shift"}), 403

        # Check for overlapping assignments
        existing_assignments = EmployeeShift.get_employee_shifts(
            session,
            assignment_data['employee_id'],
            active_only=True
        )

        for existing in existing_assignments:
            # Check if the new assignment overlaps with an existing one
            new_start = datetime.strptime(assignment_data['effective_start_date'], '%Y-%m-%d').date() if isinstance(assignment_data['effective_start_date'], str) else assignment_data['effective_start_date']
            new_end = datetime.strptime(assignment_data['effective_end_date'], '%Y-%m-%d').date() if assignment_data['effective_end_date'] and isinstance(assignment_data['effective_end_date'], str) else assignment_data['effective_end_date']

            existing_start = existing.effective_start_date
            existing_end = existing.effective_end_date

            # Check for overlap - fixed logic
            # Two date ranges overlap if: start1 <= end2 AND start2 <= end1
            overlap_exists = False
            if new_end is None:  # New assignment has no end date (ongoing)
                overlap_exists = existing_end is None or existing_end >= new_start
            elif existing_end is None:  # Existing assignment has no end date (ongoing)
                overlap_exists = new_end >= existing_start
            else:  # Both have end dates
                overlap_exists = new_start <= existing_end and existing_start <= new_end

            if overlap_exists:
                return jsonify({
                    "message": "Employee already has an active shift assignment during this period",
                    "conflicting_assignment": existing.to_dict()
                }), 400

        # Create the assignment
        assignment = EmployeeShift.create_assignment(session, **assignment_data)

        if not assignment:
            return jsonify({"message": "Failed to create shift assignment"}), 500

        # Enrich with employee and shift details for response
        assignment_dict = assignment.to_dict()
        assignment_dict['employee'] = employee.to_dict()
        assignment_dict['shift'] = shift.to_dict()

        return jsons.dump(Msg.success().add("assignment", assignment_dict))


@employee_shifts_bp.route('/api/employee-shifts/bulk', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def create_bulk_employee_shifts():
    """Assign a shift to multiple employees at once."""
    data = request.get_json()
    company_id = data.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Validate required fields
    required_fields = ['employee_ids', 'shift_id', 'effective_start_date']
    for field in required_fields:
        if not data.get(field):
            return jsonify({"message": f"{field.replace('_', ' ').title()} is required"}), 400

    employee_ids = data.get('employee_ids')
    if not isinstance(employee_ids, list) or len(employee_ids) == 0:
        return jsonify({"message": "Employee IDs must be a non-empty list"}), 400

    # Validate maximum bulk size (prevent abuse)
    max_bulk_size = 100  # Configurable limit
    if len(employee_ids) > max_bulk_size:
        return jsonify({"message": f"Cannot assign shifts to more than {max_bulk_size} employees at once"}), 400

    # Prepare common assignment data
    common_data = {
        'shift_id': data.get('shift_id'),
        'effective_start_date': data.get('effective_start_date'),
        'effective_end_date': data.get('effective_end_date'),
        'custom_start_time': data.get('custom_start_time'),
        'custom_end_time': data.get('custom_end_time'),
        'custom_break_duration': data.get('custom_break_duration'),
        'custom_working_days': data.get('custom_working_days'),
        'is_active': data.get('is_active', True),
        'created_by': data.get('created_by')  # This should be the user ID from the token
    }

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Verify shift exists and belongs to the company
        shift = Shift.get_shift_by_id(session, common_data['shift_id'])
        if not shift:
            return jsonify({"message": f"Shift with ID {common_data['shift_id']} not found"}), 404

        if shift.company_id != company_id:
            return jsonify({"message": "Unauthorized access to this shift"}), 403

        # Validate all employees exist and collect validation results
        validation_results = []
        valid_employees = []

        for employee_id in employee_ids:
            employee = Employee.get_employee_by_id(session, employee_id)
            if not employee:
                validation_results.append({
                    "employee_id": employee_id,
                    "status": "error",
                    "message": "Employee not found"
                })
                continue

            # Check for overlapping assignments
            existing_assignments = EmployeeShift.get_active_assignments_for_employee(
                session, employee_id, common_data['effective_start_date']
            )

            has_overlap = False
            for existing in existing_assignments:
                existing_start = existing.effective_start_date
                existing_end = existing.effective_end_date
                new_start = datetime.strptime(common_data['effective_start_date'], '%Y-%m-%d').date()
                new_end = datetime.strptime(common_data['effective_end_date'], '%Y-%m-%d').date() if common_data.get('effective_end_date') else None

                # Check for overlap
                if (not new_end or (existing_end and new_end >= existing_start)) and \
                   (not existing_end or (new_end and existing_end >= new_start)):
                    validation_results.append({
                        "employee_id": employee_id,
                        "status": "error",
                        "message": "Employee already has an active shift assignment during this period",
                        "conflicting_assignment_id": str(existing.assignment_id)
                    })
                    has_overlap = True
                    break

            if not has_overlap:
                valid_employees.append({
                    "employee_id": employee_id,
                    "employee": employee
                })

        # If no valid employees, return validation errors
        if not valid_employees:
            return jsonify({
                "message": "No valid employees for shift assignment",
                "validation_results": validation_results
            }), 400

        # Create assignments for valid employees
        successful_assignments = []
        failed_assignments = []

        for employee_data in valid_employees:
            try:
                assignment_data = common_data.copy()
                assignment_data['employee_id'] = employee_data['employee_id']

                assignment = EmployeeShift.create_assignment(session, **assignment_data)

                if assignment:
                    # Enrich with employee and shift details for response
                    assignment_dict = assignment.to_dict()
                    assignment_dict['employee'] = employee_data['employee'].to_dict()
                    assignment_dict['shift'] = shift.to_dict()

                    successful_assignments.append(assignment_dict)
                    validation_results.append({
                        "employee_id": employee_data['employee_id'],
                        "status": "success",
                        "message": "Shift assigned successfully",
                        "assignment_id": str(assignment.assignment_id)
                    })
                else:
                    failed_assignments.append(employee_data['employee_id'])
                    validation_results.append({
                        "employee_id": employee_data['employee_id'],
                        "status": "error",
                        "message": "Failed to create shift assignment"
                    })

            except Exception as e:
                failed_assignments.append(employee_data['employee_id'])
                validation_results.append({
                    "employee_id": employee_data['employee_id'],
                    "status": "error",
                    "message": f"Error creating assignment: {str(e)}"
                })

        # Prepare response
        response_data = {
            "message": f"Bulk shift assignment completed. {len(successful_assignments)} successful, {len(failed_assignments)} failed.",
            "summary": {
                "total_requested": len(employee_ids),
                "successful": len(successful_assignments),
                "failed": len(failed_assignments),
                "validation_errors": len([r for r in validation_results if r['status'] == 'error'])
            },
            "assignments": successful_assignments,
            "validation_results": validation_results
        }

        # Return appropriate status code
        if len(successful_assignments) == 0:
            return jsonify(response_data), 400
        elif len(failed_assignments) > 0:
            return jsonify(response_data), 207  # Multi-Status (partial success)
        else:
            return jsonify(response_data), 201  # All successful


@employee_shifts_bp.route('/api/employee-shifts/<assignment_id>', methods=['PUT'])
@token_required
@roles_required('admin', 'hr')
def update_employee_shift(assignment_id):
    """Update a shift assignment."""
    data = request.get_json()
    company_id = data.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if assignment exists
        assignment = EmployeeShift.get_assignment_by_id(session, assignment_id)
        if not assignment:
            return jsonify({"message": f"Assignment with ID {assignment_id} not found"}), 404

        # Verify shift belongs to the company
        shift = Shift.get_shift_by_id(session, assignment.shift_id)
        if not shift or shift.company_id != company_id:
            return jsonify({"message": "Unauthorized access to this assignment"}), 403

        # Prepare update data
        update_data = {}
        for field in ['effective_start_date', 'effective_end_date', 'custom_start_time',
                     'custom_end_time', 'custom_break_duration', 'custom_working_days', 'is_active']:
            if field in data:
                update_data[field] = data[field]

        # Update the assignment
        updated_assignment = EmployeeShift.update_assignment(session, assignment_id, **update_data)

        if not updated_assignment:
            return jsonify({"message": "Failed to update shift assignment"}), 500

        # Enrich with employee and shift details for response
        assignment_dict = updated_assignment.to_dict()

        employee = Employee.get_employee_by_id(session, updated_assignment.employee_id)
        if employee:
            assignment_dict['employee'] = employee.to_dict()

        shift = Shift.get_shift_by_id(session, updated_assignment.shift_id)
        if shift:
            assignment_dict['shift'] = shift.to_dict()

        return jsons.dump(Msg.success().add("assignment", assignment_dict))

@employee_shifts_bp.route('/api/employee-shifts/<assignment_id>', methods=['DELETE'])
@token_required
@roles_required('admin', 'hr')
def delete_employee_shift(assignment_id):
    """Delete a shift assignment."""
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if assignment exists
        assignment = EmployeeShift.get_assignment_by_id(session, assignment_id)
        if not assignment:
            return jsonify({"message": f"Assignment with ID {assignment_id} not found"}), 404

        # Verify shift belongs to the company
        shift = Shift.get_shift_by_id(session, assignment.shift_id)
        if not shift or shift.company_id != company_id:
            return jsonify({"message": "Unauthorized access to this assignment"}), 403

        # Delete the assignment
        success = EmployeeShift.delete_assignment(session, assignment_id)

        if not success:
            return jsonify({"message": "Failed to delete shift assignment"}), 500

        return jsons.dump(Msg.success().add("message", "Shift assignment deleted successfully"))
