from flask import Blueprint, request, jsonify, current_app as app
import uuid
from application.Models.employees import <PERSON><PERSON>, EmployeeShift, Employee
from application.Models.company import Company
from application.Models.Msg import Msg
import jsons
from datetime import datetime, date, time
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.Helpers.helper_methods import HelperMethods

shifts_bp = Blueprint('shifts', __name__)

# Shift CRUD operations
@shifts_bp.route('/api/shifts', methods=['GET'])
@token_required
def get_shifts():
    """Get shifts for a company with pagination and filtering."""
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"success": False, "message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"success": False, "message": f"Company with ID {company_id} not found"}), 404

    # Get pagination parameters
    page = request.args.get('page', default=1, type=int)
    per_page = request.args.get('per_page', default=10, type=int)

    # Get filter parameters
    is_night_shift = request.args.get('is_night_shift', type=lambda v: v.lower() == 'true' if v else None)
    is_flexible = request.args.get('is_flexible', type=lambda v: v.lower() == 'true' if v else None)
    search_term = request.args.get('search', type=str)

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Get all shifts for the company
        shifts = Shift.get_all_shifts(session, company_id)

        # Apply filters if provided
        if is_night_shift is not None:
            shifts = [shift for shift in shifts if shift.is_night_shift == is_night_shift]

        if is_flexible is not None:
            shifts = [shift for shift in shifts if shift.is_flexible == is_flexible]

        if search_term:
            search_term = search_term.lower()
            shifts = [shift for shift in shifts if search_term in shift.name.lower() or
                     (shift.description and search_term in shift.description.lower())]

        # Calculate total count
        total_count = len(shifts)

        # Apply pagination
        start_idx = (page - 1) * per_page
        end_idx = start_idx + per_page
        shifts = shifts[start_idx:end_idx]

        # Convert to dictionaries
        shifts_data = [shift.to_dict() for shift in shifts]

        # Prepare pagination metadata
        pagination = {
            "total_count": total_count,
            "page": page,
            "per_page": per_page,
            "pages": (total_count + per_page - 1) // per_page if per_page > 0 else 0,  # Ceiling division
            "has_next": page < ((total_count + per_page - 1) // per_page) if per_page > 0 else False,
            "has_prev": page > 1
        }

        return jsons.dump(Msg.success()
                         .add("shifts", shifts_data)
                         .add("pagination", pagination))

@shifts_bp.route('/api/shifts/<shift_id>', methods=['GET'])
@token_required
def get_shift(shift_id):
    """Get a specific shift by ID."""
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        shift = Shift.get_shift_by_id(session, shift_id)

        if not shift:
            return jsonify({"success": False, "message": f"Shift with ID {shift_id} not found"}), 404

        # Check if shift belongs to the company
        if shift.company_id != company_id:
            return jsonify({"success": False, "message": "Unauthorized access to this shift"}), 403

        return jsons.dump(Msg.success().add("shift", shift.to_dict()))

@shifts_bp.route('/api/shifts', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def create_shift():
    """Create a new shift."""
    data = request.get_json()
    company_id = data.get('company_id')

    if not company_id:
        return jsonify({"success": False, "message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"success": False, "message": f"Company with ID {company_id} not found"}), 404

    # Validate required fields
    required_fields = ['name', 'start_time', 'end_time']
    for field in required_fields:
        if not data.get(field):
            return jsonify({"success": False, "message": f"{field.replace('_', ' ').title()} is required"}), 400

    # Prepare shift data
    shift_data = {
        'name': data.get('name'),
        'description': data.get('description'),
        'start_time': data.get('start_time'),
        'end_time': data.get('end_time'),
        'grace_period_late': data.get('grace_period_late', 15),
        'grace_period_early': data.get('grace_period_early', 15),
        'break_duration': data.get('break_duration', 60),
        'break_start_time': data.get('break_start_time'),
        'is_night_shift': data.get('is_night_shift', False),
        'is_flexible': data.get('is_flexible', False),
        'working_days': data.get('working_days', '1,2,3,4,5'),  # Default to Mon-Fri
        'company_id': company_id
    }

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if a shift with the same name already exists for this company
        existing_shifts = Shift.get_all_shifts(session, company_id)
        for existing_shift in existing_shifts:
            if existing_shift.name.lower() == shift_data['name'].lower():
                return jsonify({
                    "success": False,
                    "message": f"A shift with the name '{shift_data['name']}' already exists"
                }), 409  # 409 Conflict

        # Create the shift
        shift = Shift.create_shift(session, **shift_data)

        if not shift:
            return jsonify({"success": False, "message": "Failed to create shift"}), 500

        return jsons.dump(Msg.success().add("shift", shift.to_dict()))

@shifts_bp.route('/api/shifts/<shift_id>', methods=['PATCH'])
@token_required
@roles_required('admin', 'hr')
def update_shift(shift_id):
    """Update an existing shift."""
    data = request.get_json()
    company_id = data.get('company_id')

    if not company_id:
        return jsonify({"success": False, "message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"success": False, "message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if shift exists
        shift = Shift.get_shift_by_id(session, shift_id)
        if not shift:
            return jsonify({"success": False, "message": f"Shift with ID {shift_id} not found"}), 404

        # Check if shift belongs to the company
        if shift.company_id != company_id:
            return jsonify({"success": False, "message": "Unauthorized access to this shift"}), 403

        # Prepare update data
        update_data = {}
        for field in ['name', 'description', 'start_time', 'end_time', 'grace_period_late',
                     'grace_period_early', 'break_duration', 'break_start_time',
                     'is_night_shift', 'is_flexible', 'working_days']:
            if field in data:
                update_data[field] = data[field]

        # If name is being updated, check for duplicates
        if 'name' in update_data and update_data['name'] != shift.name:
            existing_shifts = Shift.get_all_shifts(session, company_id)
            for existing_shift in existing_shifts:
                if (existing_shift.name.lower() == update_data['name'].lower() and
                    str(existing_shift.shift_id) != str(shift_id)):
                    return jsonify({
                        "success": False,
                        "message": f"A shift with the name '{update_data['name']}' already exists"
                    }), 409  # 409 Conflict

        # Update the shift
        updated_shift = Shift.update_shift(session, shift_id, **update_data)

        if not updated_shift:
            return jsonify({"success": False, "message": "Failed to update shift"}), 500

        return jsons.dump(Msg.success().add("shift", updated_shift.to_dict()))

@shifts_bp.route('/api/shifts/<shift_id>', methods=['DELETE'])
@token_required
@roles_required('admin', 'hr')
def delete_shift(shift_id):
    """Delete a shift."""
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"success": False, "message": "Company ID is required"}), 400

    # Get the database name for the company
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        return jsonify({"success": False, "message": f"Company with ID {company_id} not found"}), 404

    # Connect to the database
    from app import db_connection
    with db_connection.get_session(database_name) as session:
        # Check if shift exists
        shift = Shift.get_shift_by_id(session, shift_id)
        if not shift:
            return jsonify({"success": False, "message": f"Shift with ID {shift_id} not found"}), 404

        # Check if shift belongs to the company
        if shift.company_id != company_id:
            return jsonify({"success": False, "message": "Unauthorized access to this shift"}), 403

        # Check if shift is assigned to any employees
        employee_shifts = EmployeeShift.get_shift_employees(session, shift_id)
        if employee_shifts:
            return jsonify({
                "success": False,
                "message": "Cannot delete shift that is assigned to employees. Remove all assignments first.",
                "assigned_employees_count": len(employee_shifts)
            }), 409  # Using 409 Conflict for resource in use

        # Delete the shift
        success = Shift.delete_shift(session, shift_id)

        if not success:
            return jsonify({"success": False, "message": "Failed to delete shift"}), 500

        return jsons.dump(Msg.success().add("message", "Shift deleted successfully"))
