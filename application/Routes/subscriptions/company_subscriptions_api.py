from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from application.Models.company_subscription import CompanySubscription
from application.Models.subscription_plan import SubscriptionPlan
from application.Models.feature_access import FeatureAccess
from application.Services.subscription_service import SubscriptionService
from application.Models.Msg import Msg
import jsons
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required

company_subscriptions_api = Blueprint('company_subscriptions_api', __name__)

def get_user_context():
    """Helper function to extract user and company context."""
    user_info = getattr(g, 'user', None)
    if not user_info:
        return None, None, "Authentication required"

    user_id = user_info.get('user_id')
    if not user_id:
        return None, None, "User context required"

    # For company users, company_id is in the token
    company_id = user_info.get('company_id')
    if company_id:
        return company_id, user_id, None

    # For central users (HR/admin), get company_id from request
    company_id = request.args.get('company_id') or (request.json.get('company_id') if request.json else None)
    if not company_id:
        return None, None, "Company ID is required"

    return company_id, user_id, None


@company_subscriptions_api.route('/api/subscriptions', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def create_subscription():
    """
    Create a new subscription for a company.
    
    Request Body:
    - company_id: string (required for admin/hr users)
    - plan_id: string (required) - Subscription plan ID
    - trial_days: integer (optional, default: 14) - Trial period in days
    """
    try:
        data = request.get_json()
        
        # Get user context
        company_id, user_id, error_msg = get_user_context()
        if error_msg:
            return jsonify({"message": error_msg}), 401

        # For admin/hr users, allow specifying company_id in request
        if g.user.get('role') in ['admin', 'hr'] and data.get('company_id'):
            company_id = data['company_id']

        # Validate required fields
        plan_id = data.get('plan_id')
        if not plan_id:
            return jsonify({"message": "Plan ID is required"}), 400

        # Validate plan exists
        plan = SubscriptionPlan.get_plan_by_id(plan_id)
        if not plan:
            return jsonify({"message": "Subscription plan not found"}), 404

        if not plan.is_active:
            return jsonify({"message": "Selected plan is not active"}), 400

        # Check if company already has a subscription
        existing_subscription = CompanySubscription.get_subscription_by_company(company_id)
        if existing_subscription:
            return jsonify({"message": "Company already has a subscription"}), 400

        # Create subscription
        trial_days = data.get('trial_days', 14)
        subscription, error = SubscriptionService.create_company_subscription(
            company_id=company_id,
            plan_id=plan_id,
            trial_days=trial_days
        )

        if not subscription:
            return jsonify({"message": f"Failed to create subscription: {error}"}), 500

        return jsonify({
            "message": "Subscription created successfully",
            "subscription": subscription.to_dict(include_company=True, include_plan=True)
        }), 201

    except Exception as e:
        app.logger.error(f"Error creating subscription: {e}")
        return jsonify({"message": "Internal server error"}), 500


@company_subscriptions_api.route('/api/subscriptions/current', methods=['GET'])
@token_required
def get_current_subscription():
    """Get the current subscription for the authenticated user's company."""
    try:
        # Get user context
        company_id, user_id, error_msg = get_user_context()
        if error_msg:
            return jsonify({"message": error_msg}), 401

        subscription = CompanySubscription.get_subscription_by_company(company_id)
        if not subscription:
            return jsonify({"message": "No subscription found for this company"}), 404

        return jsonify({
            "message": "Subscription retrieved successfully",
            "subscription": subscription.to_dict(include_company=True, include_plan=True)
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting current subscription: {e}")
        return jsonify({"message": "Internal server error"}), 500


@company_subscriptions_api.route('/api/subscriptions/<subscription_id>', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_subscription(subscription_id):
    """Get a specific subscription by ID (admin/hr only)."""
    try:
        subscription = CompanySubscription.get_subscription_by_id(subscription_id)
        if not subscription:
            return jsonify({"message": "Subscription not found"}), 404

        return jsonify({
            "message": "Subscription retrieved successfully",
            "subscription": subscription.to_dict(include_company=True, include_plan=True)
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting subscription: {e}")
        return jsonify({"message": "Internal server error"}), 500


@company_subscriptions_api.route('/api/subscriptions', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_all_subscriptions():
    """
    Get all subscriptions with filtering options (admin/hr only).
    
    Query Parameters:
    - status: string (optional) - Filter by subscription status
    - plan_id: string (optional) - Filter by plan ID
    - page: integer (optional, default: 1) - Page number
    - limit: integer (optional, default: 20) - Items per page
    """
    try:
        # Get query parameters
        status = request.args.get('status')
        plan_id = request.args.get('plan_id')
        page = request.args.get('page', 1, type=int)
        limit = min(request.args.get('limit', 20, type=int), 100)
        offset = (page - 1) * limit

        # Build query
        query = CompanySubscription.query
        
        if status:
            query = query.filter_by(status=status)
        
        if plan_id:
            query = query.filter_by(plan_id=plan_id)

        # Get total count
        total_count = query.count()

        # Apply pagination
        subscriptions = query.order_by(CompanySubscription.created_at.desc()).offset(offset).limit(limit).all()

        return jsonify({
            "message": "Subscriptions retrieved successfully",
            "subscriptions": [sub.to_dict(include_company=True, include_plan=True) for sub in subscriptions],
            "pagination": {
                "page": page,
                "limit": limit,
                "total": total_count,
                "pages": (total_count + limit - 1) // limit
            }
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting subscriptions: {e}")
        return jsonify({"message": "Internal server error"}), 500


@company_subscriptions_api.route('/api/subscriptions/current/upgrade', methods=['POST'])
@token_required
def upgrade_subscription():
    """
    Upgrade the current subscription to a new plan.
    
    Request Body:
    - new_plan_id: string (required) - New plan ID to upgrade to
    """
    try:
        data = request.get_json()
        
        # Get user context
        company_id, user_id, error_msg = get_user_context()
        if error_msg:
            return jsonify({"message": error_msg}), 401

        new_plan_id = data.get('new_plan_id')
        if not new_plan_id:
            return jsonify({"message": "New plan ID is required"}), 400

        # Validate new plan
        new_plan = SubscriptionPlan.get_plan_by_id(new_plan_id)
        if not new_plan:
            return jsonify({"message": "New subscription plan not found"}), 404

        if not new_plan.is_active:
            return jsonify({"message": "Selected plan is not active"}), 400

        # Upgrade subscription
        success, error = SubscriptionService.upgrade_subscription(company_id, new_plan_id)
        if not success:
            return jsonify({"message": f"Failed to upgrade subscription: {error}"}), 400

        # Get updated subscription
        subscription = CompanySubscription.get_subscription_by_company(company_id)

        return jsonify({
            "message": "Subscription upgraded successfully",
            "subscription": subscription.to_dict(include_company=True, include_plan=True)
        }), 200

    except Exception as e:
        app.logger.error(f"Error upgrading subscription: {e}")
        return jsonify({"message": "Internal server error"}), 500


@company_subscriptions_api.route('/api/subscriptions/current/cancel', methods=['POST'])
@token_required
def cancel_subscription():
    """
    Cancel the current subscription.
    
    Request Body:
    - reason: string (optional) - Cancellation reason
    """
    try:
        data = request.get_json() or {}
        
        # Get user context
        company_id, user_id, error_msg = get_user_context()
        if error_msg:
            return jsonify({"message": error_msg}), 401

        reason = data.get('reason')

        # Cancel subscription
        success, error = SubscriptionService.cancel_subscription(company_id, reason)
        if not success:
            return jsonify({"message": f"Failed to cancel subscription: {error}"}), 400

        return jsonify({
            "message": "Subscription cancelled successfully"
        }), 200

    except Exception as e:
        app.logger.error(f"Error cancelling subscription: {e}")
        return jsonify({"message": "Internal server error"}), 500


@company_subscriptions_api.route('/api/subscriptions/current/employee-count', methods=['PUT'])
@token_required
@roles_required('admin', 'hr')
def update_employee_count():
    """
    Update the employee count for the current subscription.
    This will recalculate billing amounts.
    """
    try:
        # Get user context
        company_id, user_id, error_msg = get_user_context()
        if error_msg:
            return jsonify({"message": error_msg}), 401

        # Update employee count
        success, error = SubscriptionService.update_subscription_employee_count(company_id)
        if not success:
            return jsonify({"message": f"Failed to update employee count: {error}"}), 400

        # Get updated subscription
        subscription = CompanySubscription.get_subscription_by_company(company_id)

        return jsonify({
            "message": "Employee count updated successfully",
            "subscription": subscription.to_dict(include_plan=True)
        }), 200

    except Exception as e:
        app.logger.error(f"Error updating employee count: {e}")
        return jsonify({"message": "Internal server error"}), 500


@company_subscriptions_api.route('/api/subscriptions/current/features', methods=['GET'])
@token_required
def get_subscription_features():
    """Get feature access for the current subscription."""
    try:
        # Get user context
        company_id, user_id, error_msg = get_user_context()
        if error_msg:
            return jsonify({"message": error_msg}), 401

        # Get subscription
        subscription = CompanySubscription.get_subscription_by_company(company_id)
        if not subscription:
            return jsonify({"message": "No subscription found for this company"}), 404

        # Get feature access records
        features = FeatureAccess.get_company_features(company_id)
        
        # Get plan features as well
        plan_features = subscription.plan.features if subscription.plan else {}

        return jsonify({
            "message": "Features retrieved successfully",
            "subscription_status": subscription.status,
            "plan_name": subscription.plan.name if subscription.plan else None,
            "plan_features": plan_features,
            "feature_access": [feature.to_dict() for feature in features]
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting subscription features: {e}")
        return jsonify({"message": "Internal server error"}), 500


@company_subscriptions_api.route('/api/subscriptions/<subscription_id>/activate', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def activate_subscription(subscription_id):
    """Activate a trial subscription (admin/hr only)."""
    try:
        subscription = CompanySubscription.get_subscription_by_id(subscription_id)
        if not subscription:
            return jsonify({"message": "Subscription not found"}), 404

        success, error = subscription.activate_subscription()
        if not success:
            return jsonify({"message": f"Failed to activate subscription: {error}"}), 400

        return jsonify({
            "message": "Subscription activated successfully",
            "subscription": subscription.to_dict(include_company=True, include_plan=True)
        }), 200

    except Exception as e:
        app.logger.error(f"Error activating subscription: {e}")
        return jsonify({"message": "Internal server error"}), 500


@company_subscriptions_api.route('/api/subscriptions/<subscription_id>/suspend', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def suspend_subscription(subscription_id):
    """
    Suspend a subscription (admin/hr only).
    
    Request Body:
    - reason: string (optional) - Suspension reason
    """
    try:
        data = request.get_json() or {}
        reason = data.get('reason')

        subscription = CompanySubscription.get_subscription_by_id(subscription_id)
        if not subscription:
            return jsonify({"message": "Subscription not found"}), 404

        success, error = subscription.suspend_subscription(reason)
        if not success:
            return jsonify({"message": f"Failed to suspend subscription: {error}"}), 400

        return jsonify({
            "message": "Subscription suspended successfully",
            "subscription": subscription.to_dict(include_company=True, include_plan=True)
        }), 200

    except Exception as e:
        app.logger.error(f"Error suspending subscription: {e}")
        return jsonify({"message": "Internal server error"}), 500
