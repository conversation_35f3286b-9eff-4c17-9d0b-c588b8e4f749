from flask import Blueprint, request, jsonify, current_app as app, g
from datetime import datetime, date, timedelta
from application.Models.company_subscription import CompanySubscription
from application.Models.subscription_invoice import SubscriptionInvoice
from application.Models.subscription_payment import SubscriptionPayment
from application.Models.subscription_plan import SubscriptionPlan
from application.Models.feature_access import FeatureAccess
from application.Services.subscription_service import SubscriptionService
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from sqlalchemy import func, extract

subscription_analytics_api = Blueprint('subscription_analytics_api', __name__)

@subscription_analytics_api.route('/api/analytics/overview', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_analytics_overview():
    """
    Get comprehensive subscription analytics overview.
    
    Query Parameters:
    - start_date: string (optional) - Start date for revenue analytics (ISO format)
    - end_date: string (optional) - End date for revenue analytics (ISO format)
    """
    try:
        # Parse date filters
        start_date = None
        end_date = None
        
        if request.args.get('start_date'):
            try:
                start_date = datetime.fromisoformat(request.args.get('start_date').replace('Z', '+00:00')).date()
            except ValueError:
                return jsonify({"message": "Invalid start_date format. Use ISO format."}), 400

        if request.args.get('end_date'):
            try:
                end_date = datetime.fromisoformat(request.args.get('end_date').replace('Z', '+00:00')).date()
            except ValueError:
                return jsonify({"message": "Invalid end_date format. Use ISO format."}), 400

        # Get subscription analytics
        analytics, error = SubscriptionService.get_subscription_analytics(
            start_date=start_date,
            end_date=end_date
        )

        if not analytics:
            return jsonify({"message": f"Failed to get analytics: {error}"}), 500

        # Get additional metrics
        # Subscription growth over time
        growth_data = get_subscription_growth_data()
        
        # Plan distribution
        plan_distribution = get_plan_distribution()
        
        # Feature usage summary
        feature_usage = FeatureAccess.get_feature_usage_summary()
        
        # Recent activity
        recent_activity = get_recent_activity()

        return jsonify({
            "message": "Analytics overview retrieved successfully",
            "overview": analytics,
            "growth_data": growth_data,
            "plan_distribution": plan_distribution,
            "feature_usage": feature_usage,
            "recent_activity": recent_activity,
            "period": {
                "start_date": start_date.strftime('%Y-%m-%d') if start_date else None,
                "end_date": end_date.strftime('%Y-%m-%d') if end_date else None
            }
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting analytics overview: {e}")
        return jsonify({"message": "Internal server error"}), 500


@subscription_analytics_api.route('/api/analytics/revenue', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_revenue_analytics():
    """
    Get detailed revenue analytics.
    
    Query Parameters:
    - period: string (optional, default: 'monthly') - 'daily', 'weekly', 'monthly', 'yearly'
    - start_date: string (optional) - Start date (ISO format)
    - end_date: string (optional) - End date (ISO format)
    """
    try:
        period = request.args.get('period', 'monthly')
        valid_periods = ['daily', 'weekly', 'monthly', 'yearly']
        
        if period not in valid_periods:
            return jsonify({"message": f"Period must be one of: {', '.join(valid_periods)}"}), 400

        # Parse date filters
        start_date = None
        end_date = None
        
        if request.args.get('start_date'):
            try:
                start_date = datetime.fromisoformat(request.args.get('start_date').replace('Z', '+00:00')).date()
            except ValueError:
                return jsonify({"message": "Invalid start_date format. Use ISO format."}), 400

        if request.args.get('end_date'):
            try:
                end_date = datetime.fromisoformat(request.args.get('end_date').replace('Z', '+00:00')).date()
            except ValueError:
                return jsonify({"message": "Invalid end_date format. Use ISO format."}), 400

        # Default to last 12 months if no dates provided
        if not start_date:
            start_date = date.today() - timedelta(days=365)
        if not end_date:
            end_date = date.today()

        # Get revenue data by period
        revenue_data = get_revenue_by_period(period, start_date, end_date)
        
        # Get payment method breakdown
        payment_summary = SubscriptionPayment.get_payment_summary(start_date, end_date)
        
        # Get revenue summary
        revenue_summary = SubscriptionInvoice.get_revenue_summary(start_date, end_date)

        return jsonify({
            "message": "Revenue analytics retrieved successfully",
            "revenue_by_period": revenue_data,
            "payment_summary": payment_summary,
            "revenue_summary": revenue_summary,
            "period": period,
            "date_range": {
                "start_date": start_date.strftime('%Y-%m-%d'),
                "end_date": end_date.strftime('%Y-%m-%d')
            }
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting revenue analytics: {e}")
        return jsonify({"message": "Internal server error"}), 500


@subscription_analytics_api.route('/api/analytics/subscriptions', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_subscription_analytics():
    """Get detailed subscription analytics."""
    try:
        # Get subscription counts by status
        status_counts = {}
        for status in ['TRIAL', 'ACTIVE', 'SUSPENDED', 'CANCELLED']:
            count = CompanySubscription.query.filter_by(status=status).count()
            status_counts[status.lower()] = count

        # Get trial conversion rate
        total_trials = CompanySubscription.query.filter_by(status='TRIAL').count()
        converted_trials = CompanySubscription.query.filter(
            CompanySubscription.status == 'ACTIVE',
            CompanySubscription.trial_end_date.isnot(None)
        ).count()
        
        conversion_rate = (converted_trials / total_trials * 100) if total_trials > 0 else 0

        # Get churn rate (cancelled in last 30 days vs active at start of period)
        thirty_days_ago = date.today() - timedelta(days=30)
        recent_cancellations = CompanySubscription.query.filter(
            CompanySubscription.status == 'CANCELLED',
            CompanySubscription.updated_at >= thirty_days_ago
        ).count()
        
        active_thirty_days_ago = CompanySubscription.query.filter(
            CompanySubscription.status.in_(['ACTIVE', 'TRIAL']),
            CompanySubscription.created_at <= thirty_days_ago
        ).count()
        
        churn_rate = (recent_cancellations / active_thirty_days_ago * 100) if active_thirty_days_ago > 0 else 0

        # Get average subscription duration
        avg_duration = get_average_subscription_duration()

        # Get subscriptions expiring soon
        expiring_soon = CompanySubscription.get_expiring_subscriptions(days_ahead=30)

        return jsonify({
            "message": "Subscription analytics retrieved successfully",
            "status_counts": status_counts,
            "metrics": {
                "trial_conversion_rate": round(conversion_rate, 2),
                "churn_rate": round(churn_rate, 2),
                "average_duration_days": avg_duration,
                "expiring_soon_count": len(expiring_soon)
            },
            "expiring_soon": [sub.to_dict(include_company=True, include_plan=True) for sub in expiring_soon]
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting subscription analytics: {e}")
        return jsonify({"message": "Internal server error"}), 500


@subscription_analytics_api.route('/api/analytics/plans', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_plan_analytics():
    """Get analytics for subscription plans."""
    try:
        plans = SubscriptionPlan.get_all_plans()
        plan_analytics = []

        for plan in plans:
            # Get subscription count for this plan
            subscription_count = CompanySubscription.query.filter_by(plan_id=plan.plan_id).count()
            active_count = CompanySubscription.query.filter_by(
                plan_id=plan.plan_id,
                status='ACTIVE'
            ).count()
            
            # Calculate revenue for this plan
            plan_revenue = 0
            plan_subscriptions = CompanySubscription.query.filter_by(plan_id=plan.plan_id).all()
            for sub in plan_subscriptions:
                if sub.status in ['ACTIVE', 'TRIAL']:
                    plan_revenue += float(sub.amount_due)

            plan_analytics.append({
                "plan": plan.to_dict(),
                "metrics": {
                    "total_subscriptions": subscription_count,
                    "active_subscriptions": active_count,
                    "monthly_revenue": plan_revenue,
                    "average_employees_per_subscription": get_avg_employees_for_plan(plan.plan_id)
                }
            })

        return jsonify({
            "message": "Plan analytics retrieved successfully",
            "plan_analytics": plan_analytics
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting plan analytics: {e}")
        return jsonify({"message": "Internal server error"}), 500


@subscription_analytics_api.route('/api/analytics/features', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_feature_analytics():
    """Get feature usage analytics."""
    try:
        # Get feature usage summary
        feature_summary = FeatureAccess.get_feature_usage_summary()
        
        # Get most used features
        most_used_features = []
        for feature_name, stats in feature_summary.items():
            most_used_features.append({
                "feature_name": feature_name,
                "total_usage": stats['total_usage'],
                "enabled_companies": stats['enabled_companies'],
                "total_companies": stats['total_companies'],
                "adoption_rate": (stats['enabled_companies'] / stats['total_companies'] * 100) if stats['total_companies'] > 0 else 0
            })
        
        # Sort by usage
        most_used_features.sort(key=lambda x: x['total_usage'], reverse=True)

        return jsonify({
            "message": "Feature analytics retrieved successfully",
            "feature_summary": feature_summary,
            "most_used_features": most_used_features[:10]  # Top 10
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting feature analytics: {e}")
        return jsonify({"message": "Internal server error"}), 500


def get_subscription_growth_data():
    """Get subscription growth data over time."""
    try:
        # Get monthly subscription counts for the last 12 months
        growth_data = []
        for i in range(12):
            month_date = date.today().replace(day=1) - timedelta(days=30 * i)
            
            # Count subscriptions created up to this month
            count = CompanySubscription.query.filter(
                CompanySubscription.created_at <= month_date
            ).count()
            
            growth_data.append({
                "month": month_date.strftime('%Y-%m'),
                "total_subscriptions": count
            })
        
        return list(reversed(growth_data))
    except Exception as e:
        app.logger.error(f"Error getting growth data: {e}")
        return []


def get_plan_distribution():
    """Get distribution of subscriptions across plans."""
    try:
        from sqlalchemy import func
        
        distribution = CompanySubscription.query.join(SubscriptionPlan).with_entities(
            SubscriptionPlan.name,
            func.count(CompanySubscription.subscription_id).label('count')
        ).group_by(SubscriptionPlan.name).all()
        
        return [{"plan_name": name, "count": count} for name, count in distribution]
    except Exception as e:
        app.logger.error(f"Error getting plan distribution: {e}")
        return []


def get_recent_activity():
    """Get recent subscription-related activity."""
    try:
        # Get recent subscriptions
        recent_subscriptions = CompanySubscription.query.order_by(
            CompanySubscription.created_at.desc()
        ).limit(5).all()
        
        # Get recent payments
        recent_payments = SubscriptionPayment.query.filter_by(
            status='COMPLETED'
        ).order_by(SubscriptionPayment.payment_date.desc()).limit(5).all()
        
        activity = []
        
        for sub in recent_subscriptions:
            activity.append({
                "type": "subscription_created",
                "date": sub.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                "description": f"New subscription created for {sub.company.company_name if sub.company else 'Unknown Company'}",
                "details": sub.to_dict(include_company=True, include_plan=True)
            })
        
        for payment in recent_payments:
            activity.append({
                "type": "payment_received",
                "date": payment.payment_date.strftime('%Y-%m-%d %H:%M:%S'),
                "description": f"Payment of ${payment.amount} received",
                "details": payment.to_dict()
            })
        
        # Sort by date
        activity.sort(key=lambda x: x['date'], reverse=True)
        
        return activity[:10]  # Return top 10 recent activities
    except Exception as e:
        app.logger.error(f"Error getting recent activity: {e}")
        return []


def get_revenue_by_period(period, start_date, end_date):
    """Get revenue data grouped by period."""
    try:
        if period == 'daily':
            date_format = '%Y-%m-%d'
            date_trunc = func.date(SubscriptionInvoice.paid_date)
        elif period == 'weekly':
            date_format = '%Y-W%U'
            date_trunc = func.date_trunc('week', SubscriptionInvoice.paid_date)
        elif period == 'monthly':
            date_format = '%Y-%m'
            date_trunc = func.date_trunc('month', SubscriptionInvoice.paid_date)
        else:  # yearly
            date_format = '%Y'
            date_trunc = func.date_trunc('year', SubscriptionInvoice.paid_date)

        revenue_data = SubscriptionInvoice.query.filter(
            SubscriptionInvoice.status == 'PAID',
            SubscriptionInvoice.paid_date >= start_date,
            SubscriptionInvoice.paid_date <= end_date
        ).with_entities(
            date_trunc.label('period'),
            func.sum(SubscriptionInvoice.total_amount).label('revenue'),
            func.count(SubscriptionInvoice.invoice_id).label('invoice_count')
        ).group_by(date_trunc).order_by(date_trunc).all()

        return [
            {
                "period": period_date.strftime(date_format) if hasattr(period_date, 'strftime') else str(period_date),
                "revenue": float(revenue),
                "invoice_count": invoice_count
            }
            for period_date, revenue, invoice_count in revenue_data
        ]
    except Exception as e:
        app.logger.error(f"Error getting revenue by period: {e}")
        return []


def get_average_subscription_duration():
    """Calculate average subscription duration in days."""
    try:
        cancelled_subscriptions = CompanySubscription.query.filter_by(status='CANCELLED').all()
        
        if not cancelled_subscriptions:
            return 0
        
        total_days = 0
        for sub in cancelled_subscriptions:
            duration = (sub.updated_at.date() - sub.created_at.date()).days
            total_days += duration
        
        return total_days / len(cancelled_subscriptions)
    except Exception as e:
        app.logger.error(f"Error calculating average duration: {e}")
        return 0


def get_avg_employees_for_plan(plan_id):
    """Get average number of employees for subscriptions on a specific plan."""
    try:
        subscriptions = CompanySubscription.query.filter_by(plan_id=plan_id).all()
        
        if not subscriptions:
            return 0
        
        total_employees = sum(sub.employee_count for sub in subscriptions)
        return total_employees / len(subscriptions)
    except Exception as e:
        app.logger.error(f"Error calculating average employees: {e}")
        return 0
