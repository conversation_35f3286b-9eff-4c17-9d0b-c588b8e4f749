from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from datetime import datetime, date
from decimal import Decimal
from application.Models.subscription_invoice import SubscriptionInvoice
from application.Models.subscription_payment import SubscriptionPayment
from application.Models.company_subscription import CompanySubscription
from application.Services.subscription_service import SubscriptionService
from application.Models.Msg import Msg
import jsons
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required

subscription_invoices_api = Blueprint('subscription_invoices_api', __name__)

def get_user_context():
    """Helper function to extract user and company context."""
    user_info = getattr(g, 'user', None)
    if not user_info:
        return None, None, "Authentication required"

    user_id = user_info.get('user_id')
    if not user_id:
        return None, None, "User context required"

    # For company users, company_id is in the token
    company_id = user_info.get('company_id')
    if company_id:
        return company_id, user_id, None

    # For central users (HR/admin), get company_id from request
    company_id = request.args.get('company_id') or (request.json.get('company_id') if request.json else None)
    if not company_id:
        return None, None, "Company ID is required"

    return company_id, user_id, None


@subscription_invoices_api.route('/api/invoices', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def create_invoice():
    """
    Create a new invoice for a subscription.
    
    Request Body:
    - subscription_id: string (required) - Subscription ID
    - billing_period_start: string (optional) - Start date (ISO format)
    - billing_period_end: string (optional) - End date (ISO format)
    - tax_rate: number (optional, default: 0.18) - Tax rate
    - due_days: integer (optional, default: 30) - Days until due
    """
    try:
        data = request.get_json()
        
        subscription_id = data.get('subscription_id')
        if not subscription_id:
            return jsonify({"message": "Subscription ID is required"}), 400

        # Validate subscription exists
        subscription = CompanySubscription.get_subscription_by_id(subscription_id)
        if not subscription:
            return jsonify({"message": "Subscription not found"}), 404

        # Parse dates if provided
        billing_period_start = None
        billing_period_end = None
        
        if data.get('billing_period_start'):
            try:
                billing_period_start = datetime.fromisoformat(data['billing_period_start'].replace('Z', '+00:00')).date()
            except ValueError:
                return jsonify({"message": "Invalid billing_period_start format. Use ISO format."}), 400

        if data.get('billing_period_end'):
            try:
                billing_period_end = datetime.fromisoformat(data['billing_period_end'].replace('Z', '+00:00')).date()
            except ValueError:
                return jsonify({"message": "Invalid billing_period_end format. Use ISO format."}), 400

        # Generate invoice
        invoice, error = SubscriptionService.generate_subscription_invoice(
            subscription_id=subscription_id,
            billing_period_start=billing_period_start,
            billing_period_end=billing_period_end
        )

        if not invoice:
            return jsonify({"message": f"Failed to create invoice: {error}"}), 500

        return jsonify({
            "message": "Invoice created successfully",
            "invoice": invoice.to_dict(include_subscription=True)
        }), 201

    except Exception as e:
        app.logger.error(f"Error creating invoice: {e}")
        return jsonify({"message": "Internal server error"}), 500


@subscription_invoices_api.route('/api/invoices', methods=['GET'])
@token_required
def get_invoices():
    """
    Get invoices with filtering options.
    
    Query Parameters:
    - company_id: string (optional for admin/hr, ignored for company users)
    - status: string (optional) - Filter by invoice status
    - start_date: string (optional) - Filter by creation date (ISO format)
    - end_date: string (optional) - Filter by creation date (ISO format)
    - page: integer (optional, default: 1) - Page number
    - limit: integer (optional, default: 20) - Items per page
    """
    try:
        # Get user context
        company_id, user_id, error_msg = get_user_context()
        if error_msg:
            return jsonify({"message": error_msg}), 401

        # For admin/hr users, allow filtering by company_id
        if g.user.get('role') in ['admin', 'hr'] and request.args.get('company_id'):
            company_id = request.args.get('company_id')

        # Get query parameters
        status = request.args.get('status')
        page = request.args.get('page', 1, type=int)
        limit = min(request.args.get('limit', 20, type=int), 100)
        offset = (page - 1) * limit

        # Parse date filters
        start_date = None
        end_date = None
        
        if request.args.get('start_date'):
            try:
                start_date = datetime.fromisoformat(request.args.get('start_date').replace('Z', '+00:00'))
            except ValueError:
                return jsonify({"message": "Invalid start_date format. Use ISO format."}), 400

        if request.args.get('end_date'):
            try:
                end_date = datetime.fromisoformat(request.args.get('end_date').replace('Z', '+00:00'))
            except ValueError:
                return jsonify({"message": "Invalid end_date format. Use ISO format."}), 400

        # Build query
        query = SubscriptionInvoice.query.join(CompanySubscription)
        
        # Filter by company if specified
        if company_id:
            query = query.filter(CompanySubscription.company_id == company_id)
        
        if status:
            query = query.filter(SubscriptionInvoice.status == status)
        
        if start_date:
            query = query.filter(SubscriptionInvoice.created_at >= start_date)
        
        if end_date:
            query = query.filter(SubscriptionInvoice.created_at <= end_date)

        # Get total count
        total_count = query.count()

        # Apply pagination
        invoices = query.order_by(SubscriptionInvoice.created_at.desc()).offset(offset).limit(limit).all()

        return jsonify({
            "message": "Invoices retrieved successfully",
            "invoices": [invoice.to_dict(include_subscription=True) for invoice in invoices],
            "pagination": {
                "page": page,
                "limit": limit,
                "total": total_count,
                "pages": (total_count + limit - 1) // limit
            }
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting invoices: {e}")
        return jsonify({"message": "Internal server error"}), 500


@subscription_invoices_api.route('/api/invoices/<invoice_id>', methods=['GET'])
@token_required
def get_invoice(invoice_id):
    """Get a specific invoice by ID."""
    try:
        invoice = SubscriptionInvoice.get_invoice_by_id(invoice_id)
        if not invoice:
            return jsonify({"message": "Invoice not found"}), 404

        # Check access permissions
        company_id, user_id, error_msg = get_user_context()
        if error_msg:
            return jsonify({"message": error_msg}), 401

        # Company users can only see their own invoices
        if g.user.get('role') not in ['admin', 'hr']:
            if invoice.subscription.company_id != company_id:
                return jsonify({"message": "Access denied"}), 403

        return jsonify({
            "message": "Invoice retrieved successfully",
            "invoice": invoice.to_dict(include_subscription=True, include_payments=True)
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting invoice: {e}")
        return jsonify({"message": "Internal server error"}), 500


@subscription_invoices_api.route('/api/invoices/<invoice_id>/pay', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def record_payment():
    """
    Record a manual payment for an invoice.
    
    Request Body:
    - amount: number (required) - Payment amount
    - payment_method: string (required) - Payment method
    - payment_reference: string (optional) - Payment reference
    - payment_date: string (optional) - Payment date (ISO format, defaults to now)
    - notes: string (optional) - Payment notes
    """
    try:
        data = request.get_json()
        invoice_id = request.view_args['invoice_id']
        
        # Validate required fields
        amount = data.get('amount')
        payment_method = data.get('payment_method')
        
        if not amount or amount <= 0:
            return jsonify({"message": "Valid payment amount is required"}), 400
        
        if not payment_method:
            return jsonify({"message": "Payment method is required"}), 400

        # Parse payment date if provided
        payment_date = None
        if data.get('payment_date'):
            try:
                payment_date = datetime.fromisoformat(data['payment_date'].replace('Z', '+00:00'))
            except ValueError:
                return jsonify({"message": "Invalid payment_date format. Use ISO format."}), 400

        # Get user ID for recording
        user_id = g.user.get('user_id')

        # Process payment
        payment, error = SubscriptionService.process_manual_payment(
            invoice_id=invoice_id,
            amount=amount,
            payment_method=payment_method,
            payment_reference=data.get('payment_reference'),
            recorded_by=user_id
        )

        if not payment:
            return jsonify({"message": f"Failed to record payment: {error}"}), 400

        # Get updated invoice
        invoice = SubscriptionInvoice.get_invoice_by_id(invoice_id)

        return jsonify({
            "message": "Payment recorded successfully",
            "payment": payment.to_dict(),
            "invoice": invoice.to_dict(include_payments=True)
        }), 201

    except Exception as e:
        app.logger.error(f"Error recording payment: {e}")
        return jsonify({"message": "Internal server error"}), 500


@subscription_invoices_api.route('/api/invoices/<invoice_id>/cancel', methods=['POST'])
@token_required
@roles_required('admin', 'hr')
def cancel_invoice(invoice_id):
    """
    Cancel an invoice.
    
    Request Body:
    - reason: string (optional) - Cancellation reason
    """
    try:
        data = request.get_json() or {}
        reason = data.get('reason')

        invoice = SubscriptionInvoice.get_invoice_by_id(invoice_id)
        if not invoice:
            return jsonify({"message": "Invoice not found"}), 404

        success, error = invoice.cancel_invoice(reason)
        if not success:
            return jsonify({"message": f"Failed to cancel invoice: {error}"}), 400

        return jsonify({
            "message": "Invoice cancelled successfully",
            "invoice": invoice.to_dict()
        }), 200

    except Exception as e:
        app.logger.error(f"Error cancelling invoice: {e}")
        return jsonify({"message": "Internal server error"}), 500


@subscription_invoices_api.route('/api/invoices/overdue', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_overdue_invoices():
    """Get all overdue invoices (admin/hr only)."""
    try:
        overdue_invoices = SubscriptionInvoice.get_overdue_invoices()
        
        return jsonify({
            "message": "Overdue invoices retrieved successfully",
            "invoices": [invoice.to_dict(include_subscription=True) for invoice in overdue_invoices],
            "count": len(overdue_invoices)
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting overdue invoices: {e}")
        return jsonify({"message": "Internal server error"}), 500


@subscription_invoices_api.route('/api/invoices/revenue-summary', methods=['GET'])
@token_required
@roles_required('admin', 'hr')
def get_revenue_summary():
    """
    Get revenue summary for a date range (admin/hr only).
    
    Query Parameters:
    - start_date: string (optional) - Start date (ISO format)
    - end_date: string (optional) - End date (ISO format)
    """
    try:
        # Parse date filters
        start_date = None
        end_date = None
        
        if request.args.get('start_date'):
            try:
                start_date = datetime.fromisoformat(request.args.get('start_date').replace('Z', '+00:00')).date()
            except ValueError:
                return jsonify({"message": "Invalid start_date format. Use ISO format."}), 400

        if request.args.get('end_date'):
            try:
                end_date = datetime.fromisoformat(request.args.get('end_date').replace('Z', '+00:00')).date()
            except ValueError:
                return jsonify({"message": "Invalid end_date format. Use ISO format."}), 400

        revenue_summary = SubscriptionInvoice.get_revenue_summary(start_date, end_date)
        
        return jsonify({
            "message": "Revenue summary retrieved successfully",
            "summary": revenue_summary,
            "period": {
                "start_date": start_date.strftime('%Y-%m-%d') if start_date else None,
                "end_date": end_date.strftime('%Y-%m-%d') if end_date else None
            }
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting revenue summary: {e}")
        return jsonify({"message": "Internal server error"}), 500


@subscription_invoices_api.route('/api/invoices/current-company', methods=['GET'])
@token_required
def get_company_invoices():
    """Get invoices for the current user's company."""
    try:
        # Get user context
        company_id, user_id, error_msg = get_user_context()
        if error_msg:
            return jsonify({"message": error_msg}), 401

        # Get subscription for the company
        subscription = CompanySubscription.get_subscription_by_company(company_id)
        if not subscription:
            return jsonify({"message": "No subscription found for this company"}), 404

        # Get invoices for the subscription
        invoices = SubscriptionInvoice.get_invoices_by_subscription(subscription.subscription_id)

        return jsonify({
            "message": "Company invoices retrieved successfully",
            "invoices": [invoice.to_dict(include_payments=True) for invoice in invoices],
            "subscription": subscription.to_dict(include_plan=True)
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting company invoices: {e}")
        return jsonify({"message": "Internal server error"}), 500
