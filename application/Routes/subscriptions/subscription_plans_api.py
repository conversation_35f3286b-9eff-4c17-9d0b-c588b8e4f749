from flask import Blueprint, request, jsonify, current_app as app, g
import uuid
from decimal import Decimal
from application.Models.subscription_plan import SubscriptionPlan
from application.Models.Msg import Msg
import jsons
from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required

subscription_plans_api = Blueprint('subscription_plans_api', __name__)

@subscription_plans_api.route('/api/subscription-plans', methods=['GET'])
def get_subscription_plans():
    """
    Get all subscription plans.
    
    Query Parameters:
    - active_only: boolean (default: true) - Only return active plans
    - include_features: boolean (default: true) - Include feature details
    """
    try:
        active_only = request.args.get('active_only', 'true').lower() == 'true'
        include_features = request.args.get('include_features', 'true').lower() == 'true'
        
        if active_only:
            plans = SubscriptionPlan.get_active_plans()
        else:
            plans = SubscriptionPlan.get_all_plans()
        
        plans_data = [plan.to_dict(include_features=include_features) for plan in plans]
        
        return jsonify({
            "message": "Subscription plans retrieved successfully",
            "plans": plans_data,
            "count": len(plans_data)
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting subscription plans: {e}")
        return jsonify({"message": "Internal server error"}), 500


@subscription_plans_api.route('/api/subscription-plans/<plan_id>', methods=['GET'])
def get_subscription_plan(plan_id):
    """Get a specific subscription plan by ID."""
    try:
        plan = SubscriptionPlan.get_plan_by_id(plan_id)
        if not plan:
            return jsonify({"message": "Subscription plan not found"}), 404
        
        return jsonify({
            "message": "Subscription plan retrieved successfully",
            "plan": plan.to_dict()
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting subscription plan: {e}")
        return jsonify({"message": "Internal server error"}), 500


@subscription_plans_api.route('/api/subscription-plans', methods=['POST'])
@token_required
@roles_required('super-admin')
def create_subscription_plan():
    """
    Create a new subscription plan.
    
    Request Body:
    - name: string (required) - Plan name
    - description: string (optional) - Plan description
    - price_per_employee: number (required) - Price per employee
    - flat_price: number (optional, default: 0.0) - Flat price for the plan
    - billing_cycle: string (optional, default: MONTHLY) - MONTHLY, QUARTERLY, YEARLY
    - max_employees: integer (optional) - Maximum employees (null for unlimited)
    - features: object (required) - Feature configuration
    - sort_order: integer (optional, default: 0) - Display order
    """
    try:
        data = request.get_json()
        
        # Accept 'price' as alias for 'price_per_employee'
        if 'price' in data and 'price_per_employee' not in data:
            data['price_per_employee'] = data['price']
        
        # Validate required fields
        required_fields = ['name', 'price_per_employee', 'features']
        for field in required_fields:
            if not data.get(field):
                return jsonify({"message": f"{field} is required"}), 400
        
        # Validate price
        try:
            price = Decimal(str(data['price_per_employee']))
            if price < 0:
                return jsonify({"message": "Price per employee must be non-negative"}), 400
            data['price_per_employee'] = price
        except (ValueError, TypeError):
            return jsonify({"message": "Invalid price format"}), 400
        
        # Accept 'flat_price' (optional, default 0.0)
        if 'flat_price' in data:
            try:
                flat_price = Decimal(str(data['flat_price']))
                if flat_price < 0:
                    return jsonify({"message": "Flat price must be non-negative"}), 400
                data['flat_price'] = flat_price
            except (ValueError, TypeError):
                return jsonify({"message": "Invalid flat price format"}), 400
        else:
            data['flat_price'] = Decimal('0.0')
        
        # Validate billing cycle
        valid_cycles = ['MONTHLY', 'QUARTERLY', 'YEARLY']
        billing_cycle = data.get('billing_cycle', 'MONTHLY').upper()
        if billing_cycle not in valid_cycles:
            return jsonify({"message": f"Billing cycle must be one of: {', '.join(valid_cycles)}"}), 400
        data['billing_cycle'] = billing_cycle
        
        # Validate max_employees
        if 'max_employees' in data and data['max_employees'] is not None:
            try:
                max_employees = int(data['max_employees'])
                if max_employees <= 0:
                    return jsonify({"message": "Max employees must be positive"}), 400
                data['max_employees'] = max_employees
            except (ValueError, TypeError):
                return jsonify({"message": "Invalid max employees format"}), 400
        
        # Create plan
        plan, error = SubscriptionPlan.create_plan(**data)
        if not plan:
            return jsonify({"message": f"Failed to create plan: {error}"}), 500
        
        return jsonify({
            "message": "Subscription plan created successfully",
            "plan": plan.to_dict()
        }), 201

    except Exception as e:
        app.logger.error(f"Error creating subscription plan: {e}")
        return jsonify({"message": "Internal server error"}), 500


@subscription_plans_api.route('/api/subscription-plans/<plan_id>', methods=['PATCH'])
@token_required
@roles_required('super-admin')
def update_subscription_plan(plan_id):
    """
    Update a subscription plan.
    
    Request Body: Same as create_subscription_plan, all fields optional.
    """
    try:
        data = request.get_json()
        
        # Accept 'price' as alias for 'price_per_employee'
        if 'price' in data and 'price_per_employee' not in data:
            data['price_per_employee'] = data['price']
        
        # Validate price if provided
        if 'price_per_employee' in data:
            try:
                price = Decimal(str(data['price_per_employee']))
                if price < 0:
                    return jsonify({"message": "Price per employee must be non-negative"}), 400
                data['price_per_employee'] = price
            except (ValueError, TypeError):
                return jsonify({"message": "Invalid price format"}), 400
        
        # Accept 'flat_price' if provided
        if 'flat_price' in data:
            try:
                flat_price = Decimal(str(data['flat_price']))
                if flat_price < 0:
                    return jsonify({"message": "Flat price must be non-negative"}), 400
                data['flat_price'] = flat_price
            except (ValueError, TypeError):
                return jsonify({"message": "Invalid flat price format"}), 400
        
        # Validate billing cycle if provided
        if 'billing_cycle' in data:
            valid_cycles = ['MONTHLY', 'QUARTERLY', 'YEARLY']
            billing_cycle = data['billing_cycle'].upper()
            if billing_cycle not in valid_cycles:
                return jsonify({"message": f"Billing cycle must be one of: {', '.join(valid_cycles)}"}), 400
            data['billing_cycle'] = billing_cycle
        
        # Validate max_employees if provided
        if 'max_employees' in data and data['max_employees'] is not None:
            try:
                max_employees = int(data['max_employees'])
                if max_employees <= 0:
                    return jsonify({"message": "Max employees must be positive"}), 400
                data['max_employees'] = max_employees
            except (ValueError, TypeError):
                return jsonify({"message": "Invalid max employees format"}), 400
        
        # Update plan
        plan, error = SubscriptionPlan.update_plan(plan_id, **data)
        if not plan:
            return jsonify({"message": f"Failed to update plan: {error}"}), 500
        
        return jsonify({
            "message": "Subscription plan updated successfully",
            "plan": plan.to_dict()
        }), 200

    except Exception as e:
        app.logger.error(f"Error updating subscription plan: {e}")
        return jsonify({"message": "Internal server error"}), 500


@subscription_plans_api.route('/api/subscription-plans/<plan_id>', methods=['DELETE'])
@token_required
@roles_required('super-admin')
def delete_subscription_plan(plan_id):
    """Delete (deactivate) a subscription plan."""
    try:
        success, error = SubscriptionPlan.delete_plan(plan_id)
        if not success:
            return jsonify({"message": f"Failed to delete plan: {error}"}), 400
        
        return jsonify({
            "message": "Subscription plan deactivated successfully"
        }), 200

    except Exception as e:
        app.logger.error(f"Error deleting subscription plan: {e}")
        return jsonify({"message": "Internal server error"}), 500


@subscription_plans_api.route('/api/subscription-plans/<plan_id>/calculate', methods=['POST'])
def calculate_plan_price():
    """
    Calculate price for a plan with given parameters.
    Description:
    This endpoint calculates the total price for a subscription plan based on the number of employees and the billing cycle.
    It checks if the employee count exceeds the plan's maximum limit and returns an error if it does.
    The calculation considers the plan's price per employee and any flat price associated with the plan.
    
    Request Body:
    - employee_count: integer (required) - Number of employees
    - billing_cycle: string (optional) - Override plan's default billing cycle
    """
    try:
        data = request.get_json()
        plan_id = request.view_args['plan_id']
        
        # Validate employee count
        employee_count = data.get('employee_count')
        if not employee_count or employee_count <= 0:
            return jsonify({"message": "Valid employee count is required"}), 400
        
        # Get plan
        plan = SubscriptionPlan.get_plan_by_id(plan_id)
        if not plan:
            return jsonify({"message": "Subscription plan not found"}), 404
        
        # Check if employee count exceeds plan limit
        if plan.max_employees and employee_count > plan.max_employees:
            return jsonify({
                "message": f"Employee count ({employee_count}) exceeds plan limit ({plan.max_employees})",
                "error_code": "EMPLOYEE_LIMIT_EXCEEDED"
            }), 400
        
        # Calculate price
        billing_cycle = data.get('billing_cycle', plan.billing_cycle)
        total_price = plan.calculate_price(employee_count, billing_cycle)
        
        return jsonify({
            "message": "Price calculated successfully",
            "calculation": {
                "plan_name": plan.name,
                "employee_count": employee_count,
                "price_per_employee": float(plan.price_per_employee),
                "billing_cycle": billing_cycle,
                "total_price": total_price,
                "currency": "USD"  # This could be configurable
            }
        }), 200

    except Exception as e:
        app.logger.error(f"Error calculating plan price: {e}")
        return jsonify({"message": "Internal server error"}), 500


@subscription_plans_api.route('/api/subscription-plans/seed-defaults', methods=['POST'])
@token_required
@roles_required('admin')
def seed_default_plans():
    """Seed default subscription plans.
    This endpoint is typically used during initial setup or testing.
    It creates a set of predefined subscription plans with common features.
    The plans are created with default values and can be modified later.
    The endpoint returns a success message or an error if the seeding fails.
    The seeding process is idempotent, meaning it can be called multiple times without creating duplicate plans.
    """
    try:
        success, error = SubscriptionPlan.seed_default_plans()
        if not success:
            return jsonify({"message": f"Failed to seed default plans: {error}"}), 500
        
        return jsonify({
            "message": "Default subscription plans seeded successfully"
        }), 200

    except Exception as e:
        app.logger.error(f"Error seeding default plans: {e}")
        return jsonify({"message": "Internal server error"}), 500


@subscription_plans_api.route('/api/subscription-plans/compare', methods=['POST'])
def compare_subscription_plans():
    """
    Compare multiple subscription plans.
    
    Request Body:
    - plan_ids: array of strings (required) - Plan IDs to compare
    - employee_count: integer (required) - Number of employees for price calculation
    """
    try:
        data = request.get_json()
        
        plan_ids = data.get('plan_ids', [])
        employee_count = data.get('employee_count')
        
        if not plan_ids:
            return jsonify({"message": "Plan IDs are required"}), 400
        
        if not employee_count or employee_count <= 0:
            return jsonify({"message": "Valid employee count is required"}), 400
        
        comparisons = []
        
        for plan_id in plan_ids:
            plan = SubscriptionPlan.get_plan_by_id(plan_id)
            if plan:
                comparison = plan.to_dict()
                comparison['calculated_price'] = plan.calculate_price(employee_count)
                comparison['can_accommodate'] = (
                    plan.max_employees is None or employee_count <= plan.max_employees
                )
                comparisons.append(comparison)
        
        return jsonify({
            "message": "Plans compared successfully",
            "employee_count": employee_count,
            "comparisons": comparisons
        }), 200

    except Exception as e:
        app.logger.error(f"Error comparing subscription plans: {e}")
        return jsonify({"message": "Internal server error"}), 500
