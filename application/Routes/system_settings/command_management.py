"""
API endpoints for managing machine commands and troubleshooting device synchronization issues.
"""

from flask import Blueprint, request, jsonify, current_app as app
from datetime import datetime
import json

from application.decorators.token_required import token_required
from application.decorators.role_required import roles_required
from application.Models.MachineCommand import (
    MachineCommand, 
    select_machine_command_by_id,
    find_pending_command,
    update_machine_command_o
)
from application.Models.company import CompanyDevice, Company
from application.Models.Device import get_device_by_serial_num
from application.web_socket.WebSocketPool import ws_device

command_management = Blueprint('command_management', __name__)


@command_management.route('/api/commands/failed', methods=['GET'])
@token_required
@roles_required('super-admin')
def get_failed_commands():
    """
    Get all failed commands for a device or company.

    Query Parameters:
    - device_sn: Device serial number (optional)
    - company_id: Company ID (required if device_sn not provided)
    - employee_id: Filter by employee enrollid (optional)
    - include_content: Include full command content (default: false)

    Returns:
    - List of failed commands with comprehensive details
    """
    device_sn = request.args.get('device_sn')
    company_id = request.args.get('company_id')
    employee_id = request.args.get('employee_id', type=int)
    include_content = request.args.get('include_content', 'false').lower() == 'true'

    if not device_sn and not company_id:
        return jsonify({"message": "Either device_sn or company_id is required"}), 400

    from app import db_connection

    try:
        failed_commands = []
        devices_checked = []

        if device_sn:
            # Get failed commands for specific device
            database_name = CompanyDevice.get_database_name_by_sn(device_sn)
            if not database_name:
                return jsonify({"message": f"Device {device_sn} not found"}), 404

            devices_to_check = [{'device_sn': device_sn, 'database_name': database_name}]

        elif company_id:
            # Get the database name for the company (following standard pattern)
            database_name = Company.get_database_given_company_id(company_id)
            if not database_name:
                return jsonify({"message": f"Company with ID {company_id} not found"}), 404

            # Get all devices for this company
            devices = CompanyDevice.get_company_devices(company_id)
            if not devices:
                return jsonify({
                    "status": "success",
                    "message": "No devices found for this company",
                    "failed_commands": [],
                    "count": 0
                }), 200

            devices_to_check = [{'device_sn': d.device_sn, 'database_name': database_name} for d in devices]

        # Query each device for failed commands
        for device_info in devices_to_check:
            device_sn_current = device_info['device_sn']
            database_name = device_info['database_name']

            try:
                with db_connection.get_session(database_name) as session:
                    query = session.query(MachineCommand).filter(
                        MachineCommand.serial == device_sn_current,
                        MachineCommand.status == 0,
                        MachineCommand.err_count >= 15
                    )

                    if employee_id:
                        query = query.filter(MachineCommand.content.like(f'%"enrollid":{employee_id}%'))

                    commands = query.order_by(MachineCommand.gmt_crate.desc()).all()

                    devices_checked.append({
                        'device_sn': device_sn_current,
                        'failed_count': len(commands)
                    })

                    for cmd in commands:
                        # Parse command content to extract employee info
                        employee_name = None
                        enrollid = None
                        try:
                            content_json = json.loads(cmd.content)
                            enrollid = content_json.get('enrollid')
                            employee_name = content_json.get('name')
                        except:
                            pass

                        # Calculate time since last attempt
                        time_since_failure = None
                        if cmd.run_time:
                            time_since_failure = (datetime.now() - cmd.run_time).total_seconds()

                        # Check device connectivity
                        device_connected = ws_device.get(cmd.serial) is not None

                        command_data = {
                            'id': cmd.id,
                            'serial': cmd.serial,
                            'name': cmd.name,
                            'status': cmd.status,
                            'status_text': 'Pending' if cmd.status == 0 else 'Completed',
                            'send_status': cmd.send_status,
                            'send_status_text': 'Sent' if cmd.send_status == 1 else 'Not Sent',
                            'err_count': cmd.err_count,
                            'run_time': cmd.run_time.isoformat() if cmd.run_time else None,
                            'created': cmd.gmt_crate.isoformat() if cmd.gmt_crate else None,
                            'modified': cmd.gmt_modified.isoformat() if cmd.gmt_modified else None,
                            'time_since_failure_seconds': int(time_since_failure) if time_since_failure else None,
                            'device_connected': device_connected,
                            'employee_id': enrollid,
                            'employee_name': employee_name,
                            'content_preview': cmd.content[:100] + '...' if len(cmd.content) > 100 else cmd.content
                        }

                        if include_content:
                            command_data['content'] = cmd.content

                        failed_commands.append(command_data)

            except Exception as e:
                app.logger.error(f"Error querying device {device_sn_current}: {str(e)}")
                # Continue with other devices even if one fails
                continue

        return jsonify({
            "status": "success",
            "failed_commands": failed_commands,
            "count": len(failed_commands),
            "devices_checked": devices_checked,
            "summary": {
                "total_failed": len(failed_commands),
                "devices_with_failures": len([d for d in devices_checked if d['failed_count'] > 0]),
                "total_devices_checked": len(devices_checked)
            }
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting failed commands: {str(e)}")
        import traceback
        app.logger.error(traceback.format_exc())
        return jsonify({"message": "Internal server error", "error": str(e)}), 500


@command_management.route('/api/commands/reset', methods=['POST'])
@token_required
@roles_required('super-admin')
def reset_command():
    """
    Reset a failed command to retry it.

    Request Body:
    {
        "command_id": 12,
        "company_id": "uuid",  // required for proper database lookup
        "device_sn": "AYTI10087992"  // optional, for validation
    }

    Returns:
    - Success message with updated command details
    """
    data = request.get_json()

    if not data:
        return jsonify({"message": "Request body is required"}), 400

    if 'command_id' not in data:
        return jsonify({"message": "command_id is required"}), 400

    if 'company_id' not in data:
        return jsonify({"message": "company_id is required"}), 400

    command_id = data['command_id']
    company_id = data['company_id']
    device_sn = data.get('device_sn')

    from app import db_connection

    try:
        # Get the database name for the company (following standard pattern)
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404

        with db_connection.get_session(database_name) as session:
            command = select_machine_command_by_id(session, command_id)

            if not command:
                return jsonify({"message": f"Command {command_id} not found"}), 404

            # Validate device_sn if provided
            if device_sn and command.serial != device_sn:
                return jsonify({
                    "message": f"Command {command_id} belongs to device {command.serial}, not {device_sn}"
                }), 400

            # Check if command is actually failed
            if command.err_count < 15:
                return jsonify({
                    "message": f"Command {command_id} is not failed (err_count={command.err_count})",
                    "note": "Only commands with err_count >= 15 need to be reset"
                }), 400

            # Check if command is already completed
            if command.status == 1:
                return jsonify({
                    "message": f"Command {command_id} is already completed",
                    "note": "No need to reset completed commands"
                }), 400

            # Check device connectivity
            device_connected = ws_device.get(command.serial) is not None

            # Reset the command
            old_err_count = command.err_count
            old_status = command.status
            old_send_status = command.send_status

            command.status = 0
            command.send_status = 0
            command.err_count = 0
            command.run_time = datetime.now()
            command.gmt_modified = datetime.now()
            session.commit()

            app.logger.info(f"Reset command {command_id} for device {command.serial} (err_count: {old_err_count} -> 0)")

            return jsonify({
                "status": "success",
                "message": f"Command {command_id} has been reset and will be retried automatically",
                "command": {
                    'id': command.id,
                    'serial': command.serial,
                    'name': command.name,
                    'status': command.status,
                    'send_status': command.send_status,
                    'err_count': command.err_count,
                    'previous_err_count': old_err_count,
                    'device_connected': device_connected
                },
                "warning": "Device is currently offline - command will be sent when device reconnects" if not device_connected else None
            }), 200

    except Exception as e:
        app.logger.error(f"Error resetting command: {str(e)}")
        import traceback
        app.logger.error(traceback.format_exc())
        return jsonify({"message": "Internal server error", "error": str(e)}), 500


@command_management.route('/api/commands/retry-all', methods=['POST'])
@token_required
@roles_required('super-admin')
def retry_all_failed_commands():
    """
    Reset all failed commands for a device, company, or specific employee.

    Request Body:
    {
        "company_id": "uuid",  // required
        "device_sn": "AYTI10087992",  // optional - if not provided, resets for all company devices
        "employee_id": 5  // optional - filter by specific employee
    }

    Returns:
    - Count of commands reset with details
    """
    data = request.get_json()

    if not data:
        return jsonify({"message": "Request body is required"}), 400

    if 'company_id' not in data:
        return jsonify({"message": "company_id is required"}), 400

    company_id = data['company_id']
    device_sn = data.get('device_sn')
    employee_id = data.get('employee_id')

    from app import db_connection

    try:
        # Get the database name for the company (following standard pattern)
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404

        # Determine which devices to process
        if device_sn:
            # Validate device belongs to company
            device = CompanyDevice.query.filter_by(
                device_sn=device_sn,
                company_id=company_id
            ).first()

            if not device:
                return jsonify({
                    "message": f"Device {device_sn} not found or doesn't belong to company {company_id}"
                }), 404

            devices_to_process = [device_sn]
        else:
            # Get all devices for the company
            devices = CompanyDevice.get_company_devices(company_id)
            if not devices:
                return jsonify({
                    "status": "success",
                    "message": "No devices found for this company",
                    "count": 0
                }), 200

            devices_to_process = [d.device_sn for d in devices]

        with db_connection.get_session(database_name) as session:
            reset_count = 0
            command_details = []
            devices_processed = {}

            for device_sn_current in devices_to_process:
                # Find all failed commands for this device
                query = session.query(MachineCommand).filter(
                    MachineCommand.serial == device_sn_current,
                    MachineCommand.status == 0,
                    MachineCommand.err_count >= 15
                )

                if employee_id:
                    query = query.filter(MachineCommand.content.like(f'%"enrollid":{employee_id}%'))

                failed_commands = query.all()

                device_reset_count = 0

                # Reset all commands for this device
                for cmd in failed_commands:
                    old_err_count = cmd.err_count

                    # Parse employee info from content
                    employee_name = None
                    enrollid = None
                    try:
                        content_json = json.loads(cmd.content)
                        enrollid = content_json.get('enrollid')
                        employee_name = content_json.get('name')
                    except:
                        pass

                    cmd.status = 0
                    cmd.send_status = 0
                    cmd.err_count = 0
                    cmd.run_time = datetime.now()
                    cmd.gmt_modified = datetime.now()
                    reset_count += 1
                    device_reset_count += 1

                    command_details.append({
                        'id': cmd.id,
                        'device_sn': cmd.serial,
                        'name': cmd.name,
                        'employee_id': enrollid,
                        'employee_name': employee_name,
                        'previous_err_count': old_err_count
                    })

                devices_processed[device_sn_current] = device_reset_count

            if reset_count == 0:
                return jsonify({
                    "status": "success",
                    "message": "No failed commands found",
                    "count": 0,
                    "devices_processed": devices_processed
                }), 200

            session.commit()

            app.logger.info(f"Reset {reset_count} failed commands for company {company_id}")

            return jsonify({
                "status": "success",
                "message": f"Reset {reset_count} failed command(s) across {len(devices_processed)} device(s)",
                "count": reset_count,
                "devices_processed": devices_processed,
                "commands": command_details
            }), 200

    except Exception as e:
        app.logger.error(f"Error resetting commands: {str(e)}")
        import traceback
        app.logger.error(traceback.format_exc())
        return jsonify({"message": "Internal server error", "error": str(e)}), 500


@command_management.route('/api/commands/<int:command_id>', methods=['GET'])
@token_required
def get_command_details(command_id):
    """
    Get detailed information about a specific command.

    Query Parameters:
    - company_id: Company ID (required for efficient lookup)

    Returns:
    - Full command details including content and employee information
    """
    company_id = request.args.get('company_id')

    if not company_id:
        return jsonify({"message": "company_id is required"}), 400

    from app import db_connection

    try:
        # Get the database name for the company (following standard pattern)
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404

        with db_connection.get_session(database_name) as session:
            command = select_machine_command_by_id(session, command_id)

            if not command:
                return jsonify({"message": f"Command {command_id} not found"}), 404

            # Check device connectivity
            device_status = ws_device.get(command.serial)
            is_connected = device_status is not None

            # Parse command content to extract employee info
            employee_name = None
            enrollid = None
            command_type = None
            try:
                content_json = json.loads(command.content)
                enrollid = content_json.get('enrollid')
                employee_name = content_json.get('name')
                command_type = content_json.get('cmd')
            except:
                pass

            # Calculate time metrics
            time_since_creation = None
            time_since_last_attempt = None
            if command.gmt_crate:
                time_since_creation = (datetime.now() - command.gmt_crate).total_seconds()
            if command.run_time:
                time_since_last_attempt = (datetime.now() - command.run_time).total_seconds()

            return jsonify({
                "status": "success",
                "command": {
                    'id': command.id,
                    'serial': command.serial,
                    'name': command.name,
                    'command_type': command_type,
                    'status': command.status,
                    'status_text': 'Completed' if command.status == 1 else 'Pending',
                    'send_status': command.send_status,
                    'send_status_text': 'Sent' if command.send_status == 1 else 'Not Sent',
                    'err_count': command.err_count,
                    'is_failed': command.err_count >= 15,
                    'run_time': command.run_time.isoformat() if command.run_time else None,
                    'created': command.gmt_crate.isoformat() if command.gmt_crate else None,
                    'modified': command.gmt_modified.isoformat() if command.gmt_modified else None,
                    'time_since_creation_seconds': int(time_since_creation) if time_since_creation else None,
                    'time_since_last_attempt_seconds': int(time_since_last_attempt) if time_since_last_attempt else None,
                    'content': command.content,
                    'device_connected': is_connected,
                    'employee_id': enrollid,
                    'employee_name': employee_name
                }
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting command details: {str(e)}")
        import traceback
        app.logger.error(traceback.format_exc())
        return jsonify({"message": "Internal server error", "error": str(e)}), 500


@command_management.route('/api/commands/statistics', methods=['GET'])
@token_required
def get_command_statistics():
    """
    Get command statistics for a company or device.

    Query Parameters:
    - company_id: Company ID (required)
    - device_sn: Device serial number (optional - for device-specific stats)

    Returns:
    - Comprehensive statistics about command queue status
    """
    company_id = request.args.get('company_id')
    device_sn = request.args.get('device_sn')

    if not company_id:
        return jsonify({"message": "company_id is required"}), 400

    from app import db_connection

    try:
        # Get the database name for the company
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return jsonify({"message": f"Company with ID {company_id} not found"}), 404

        # Determine which devices to analyze
        if device_sn:
            # Validate device belongs to company
            device = CompanyDevice.query.filter_by(
                device_sn=device_sn,
                company_id=company_id
            ).first()

            if not device:
                return jsonify({
                    "message": f"Device {device_sn} not found or doesn't belong to company {company_id}"
                }), 404

            devices_to_analyze = [device_sn]
        else:
            # Get all devices for the company
            devices = CompanyDevice.get_company_devices(company_id)
            if not devices:
                return jsonify({
                    "status": "success",
                    "message": "No devices found for this company",
                    "statistics": {}
                }), 200

            devices_to_analyze = [d.device_sn for d in devices]

        with db_connection.get_session(database_name) as session:
            overall_stats = {
                'total_commands': 0,
                'completed': 0,
                'pending': 0,
                'failed': 0,
                'not_sent': 0,
                'sent_waiting': 0,
                'devices_online': 0,
                'devices_offline': 0
            }

            device_stats = []

            for device_sn_current in devices_to_analyze:
                # Check device connectivity
                is_connected = ws_device.get(device_sn_current) is not None

                if is_connected:
                    overall_stats['devices_online'] += 1
                else:
                    overall_stats['devices_offline'] += 1

                # Get all commands for this device
                all_commands = session.query(MachineCommand).filter(
                    MachineCommand.serial == device_sn_current
                ).all()

                device_stat = {
                    'device_sn': device_sn_current,
                    'connected': is_connected,
                    'total_commands': len(all_commands),
                    'completed': 0,
                    'pending': 0,
                    'failed': 0,
                    'not_sent': 0,
                    'sent_waiting': 0,
                    'oldest_pending': None,
                    'newest_failed': None
                }

                oldest_pending_time = None
                newest_failed_time = None

                for cmd in all_commands:
                    overall_stats['total_commands'] += 1

                    if cmd.status == 1:
                        # Completed
                        overall_stats['completed'] += 1
                        device_stat['completed'] += 1
                    else:
                        # Pending
                        if cmd.err_count >= 15:
                            # Failed
                            overall_stats['failed'] += 1
                            device_stat['failed'] += 1

                            if newest_failed_time is None or (cmd.run_time and cmd.run_time > newest_failed_time):
                                newest_failed_time = cmd.run_time
                        else:
                            # Still retrying
                            overall_stats['pending'] += 1
                            device_stat['pending'] += 1

                            if cmd.send_status == 0:
                                overall_stats['not_sent'] += 1
                                device_stat['not_sent'] += 1
                            else:
                                overall_stats['sent_waiting'] += 1
                                device_stat['sent_waiting'] += 1

                            if oldest_pending_time is None or (cmd.gmt_crate and cmd.gmt_crate < oldest_pending_time):
                                oldest_pending_time = cmd.gmt_crate

                device_stat['oldest_pending'] = oldest_pending_time.isoformat() if oldest_pending_time else None
                device_stat['newest_failed'] = newest_failed_time.isoformat() if newest_failed_time else None

                device_stats.append(device_stat)

            # Calculate success rate
            success_rate = 0
            if overall_stats['total_commands'] > 0:
                success_rate = (overall_stats['completed'] / overall_stats['total_commands']) * 100

            return jsonify({
                "status": "success",
                "statistics": {
                    'overall': {
                        **overall_stats,
                        'success_rate_percent': round(success_rate, 2),
                        'failure_rate_percent': round((overall_stats['failed'] / overall_stats['total_commands'] * 100) if overall_stats['total_commands'] > 0 else 0, 2)
                    },
                    'by_device': device_stats
                }
            }), 200

    except Exception as e:
        app.logger.error(f"Error getting command statistics: {str(e)}")
        import traceback
        app.logger.error(traceback.format_exc())
        return jsonify({"message": "Internal server error", "error": str(e)}), 500

