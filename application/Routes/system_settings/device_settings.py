from flask import Blueprint, request, jsonify, current_app as app
import os
import json
from application.Models.MachineCommand import MachineCommand
from application.Models.company import CompanyDevice
import jsons
import json
from application.Models.Msg import Msg

device_settings_bp = Blueprint('device_settings', __name__)


@device_settings_bp.route('/initSystem', methods=['GET']) #todo 2024年1月8日18:06:22 没有测试
def init_system():
    """Initialize the system.
    Description:
        This function initializes the system.
        It will delete all users and all logs. However the settings will stay intact."""
    device_sn = request.args.get('deviceSn')

    app.logger.info(f"Initializing system for device: {device_sn}")
    # 创建消息
    message = '{"cmd": "enabledevice"}'
    message2 = '{"cmd": "settime", "cloudtime": "2020-12-23 13:49:30"}'
    s4 = '{"cmd": "settime", "cloudtime": "2016-03-25 13:49:30"}'
    s2 = '{"cmd": "setdevinfo", "deviceid": 1, "language": 0, "volume": 0, "screensaver": 0, "verifymode": 0, "sleep": 0,"userfpnum": 3, "loghint": 1000, "reverifytime": 0}'
    s5 = '{"cmd": "enableuser", "enrollid": 1, "enflag": 0}'
    s6 = '{"cmd": "getusername", "enrollid": 1}'
    message = '{"cmd": "initsys"}'
    # get the database_name given the device_sn
    try:
        database_name = CompanyDevice.get_database_name_by_sn(device_sn)
        app.logger.info(f"Database name: {database_name}")
    except Exception as e:
        app.logger.error(f"Error getting database name: {str(e)}")
        return jsons.dump(Msg.fail())
    from app import db_connection
    
    # Connect to the database
    with db_connection.get_session(database_name) as session:
  
        try:
            machine_command = MachineCommand(content=message, name="initsys", status=0, send_status=0, err_count=0, serial=device_sn)
            app.logger.info(f"Machine command that has been executed: {machine_command}")
        except Exception as e:
            app.logger.error(f"Error initializing system: {str(e)}")
            return jsons.dump(Msg.fail())
        try:
            # execute the command
            inserted = machine_command.insert_machine_command(session, machine_command)
            app.logger.info(f"Machine command inserted: {inserted}")
            message = f"""
            System initialized successfully for device {device_sn}.
            """
            return jsonify({"message": message}), 200
        except Exception as e:
            app.logger.error(f"Error initializing system: {str(e)}")
            return jsons.dump(Msg.fail())