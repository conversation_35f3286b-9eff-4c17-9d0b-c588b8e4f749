
from flask_wtf import F<PERSON>kForm
from wtforms import <PERSON>Field, SubmitField, IntegerField, DecimalField, EmailField, SelectField
from wtforms.validators import <PERSON>Required,  EqualTo, Length, Email, Optional
from wtforms.validators import ValidationError
from flask import flash



def validate_phone(form, field):
    # Ensure that the field contains only digits and is exactly 10 characters long
    if not field.data.isdigit() or len(field.data) != 10:
        flash('Phone number must be exactly 10 digits long and contain only numbers.', 'danger')
        raise ValidationError('Phone number must be exactly 10 digits long and contain only numbers.')


class SignupForm(FlaskForm):
    username = <PERSON><PERSON><PERSON>('Username', validators=[DataRequired()])
    first_name = <PERSON><PERSON>ield('First Name')
    last_name = <PERSON><PERSON><PERSON>('Last Name', validators=[DataRequired()])
    email = EmailField('Email', validators=[DataRequired(), Email()])
    password = String<PERSON>ield('Password', validators=[DataRequired(), Length(min=6, max=20)])
    confirm_password = StringField('Confirm Password', validators=[DataRequired(), EqualTo('password')])
    phone_number = String<PERSON>ield('Phone Number', validators=[DataRequired(), validate_phone])
    submit = SubmitField('Sign Up')