from flask import Blueprint, request, jsonify, current_app as app
import os
from dotenv import load_dotenv
from application.Helpers.helper_methods import HelperMethods
from application.Models.Msg import Msg
import jsons

load_dotenv()

utility_bp = Blueprint('utility', __name__)

@utility_bp.route('/sendEmail', methods=['POST'])
def send_email():
    """Send an email.
    Description:
        This function sends an email.
        payload: {
            receiver_email: str: The email address of the recipient.
            subject: str: The subject of the email.
            html_message: str: The HTML content of the email.
        }
        Returns:
            JSON: A JSON response.
    """
    try:
        data = request.get_json()
        app.logger.info(f"Received JSON: {data}")

        if not data:
            app.logger.warning("Invalid JSON data received.")
            return jsons.dump(Msg.fail())
        
        receiver_email = data.get('receiver_email')
        subject = data.get('subject')
        html_message = data.get('html_message')
        