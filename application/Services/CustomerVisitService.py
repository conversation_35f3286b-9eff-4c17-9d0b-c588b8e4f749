"""Customer visit service for processing customer biometric records and visit tracking."""

from flask import current_app as app
from application.Models.customers.customer import Customer
from application.Models.customers.customer_visit import CustomerVisit
from application.Models.customers.promotion_audit_log import PromotionAuditLog
from datetime import datetime, date
import uuid


class CustomerVisitService:
    """Service for processing customer visits from biometric device records.
    
    This service handles:
    - Creating customer visit records from biometric data
    - Duplicate detection
    - Loyalty program integration
    - Audit logging
    """
    
    @staticmethod
    def process_customer_record(session, customer_id, record):
        """Process a biometric device record for a customer.
        
        This is the main entry point called by UnifiedRecordHandler.
        
        Args:
            session: Database session
            customer_id: UUID of the customer
            record: Records object from biometric device
            
        Returns:
            CustomerVisit object or None if failed
        """
        try:
            # Convert customer_id to UUID if string
            if isinstance(customer_id, str):
                customer_id = uuid.UUID(customer_id)
            
            # Verify customer exists
            customer = Customer.get_customer_by_id(session, customer_id)
            if not customer:
                app.logger.error(f"Customer not found: {customer_id}")
                return None
            
            # Check if customer is active
            if customer.status != 'active':
                app.logger.warning(f"Customer {customer_id} is not active (status: {customer.status})")
                # Still create visit but log the warning
            
            # Extract visit details from record
            visit_date = record.records_time.date() if hasattr(record.records_time, 'date') else date.today()
            visit_time = record.records_time if isinstance(record.records_time, datetime) else datetime.now()
            
            # Check for duplicate visits (within 5 minutes)
            duplicate = CustomerVisit.check_duplicate_visit(
                session=session,
                customer_id=customer_id,
                visit_date=visit_date,
                visit_time=visit_time,
                tolerance_minutes=5
            )
            
            if duplicate:
                app.logger.info(f"Duplicate visit detected for customer {customer_id}, skipping")
                return duplicate
            
            # Create visit record
            visit = CustomerVisit.create_visit(
                session=session,
                customer_id=customer_id,
                visit_date=visit_date,
                visit_time=visit_time,
                source='biometric',
                source_record_id=str(record.id) if hasattr(record, 'id') else None,
                device_serial_num=record.device_serial_num if hasattr(record, 'device_serial_num') else None,
                visit_type='regular',
                is_loyalty_visit=True,  # Default: count toward loyalty
                reward_redeemed=False
            )
            
            if not visit:
                app.logger.error(f"Failed to create visit for customer {customer_id}")
                return None
            
            app.logger.info(f"Created customer visit: {visit.visit_id} for customer {customer_id}")
            
            # Log the visit in audit trail
            PromotionAuditLog.log_visit_recorded(
                session=session,
                customer_id=customer_id,
                visit_id=visit.visit_id,
                visit_data=visit.to_dict(),
                performed_by=None  # Automatic from device
            )
            
            # Process loyalty program if applicable
            if visit.is_loyalty_visit:
                from application.Services.LoyaltyService import LoyaltyService
                LoyaltyService.process_visit(session, customer_id, visit.visit_id)
            
            return visit
        
        except Exception as e:
            app.logger.error(f"Error processing customer record: {e}", exc_info=True)
            return None
    
    @staticmethod
    def create_manual_visit(session, customer_id, visit_date=None, visit_time=None, 
                           visit_type='regular', notes=None, created_by=None):
        """Create a manual customer visit (not from biometric device).
        
        Args:
            session: Database session
            customer_id: UUID of the customer
            visit_date: Date of visit (defaults to today)
            visit_time: Time of visit (defaults to now)
            visit_type: Type of visit (regular, complimentary, reward_redemption)
            notes: Optional notes
            created_by: User ID who created the visit
            
        Returns:
            CustomerVisit object or None if failed
        """
        try:
            if isinstance(customer_id, str):
                customer_id = uuid.UUID(customer_id)
            
            # Verify customer exists
            customer = Customer.get_customer_by_id(session, customer_id)
            if not customer:
                app.logger.error(f"Customer not found: {customer_id}")
                return None
            
            if not visit_date:
                visit_date = date.today()
            
            if not visit_time:
                visit_time = datetime.now()
            
            # Create visit
            visit = CustomerVisit.create_visit(
                session=session,
                customer_id=customer_id,
                visit_date=visit_date,
                visit_time=visit_time,
                source='manual',
                visit_type=visit_type,
                is_loyalty_visit=(visit_type == 'regular'),  # Only regular visits count toward loyalty
                notes=notes
            )
            
            if not visit:
                app.logger.error(f"Failed to create manual visit for customer {customer_id}")
                return None
            
            app.logger.info(f"Created manual visit: {visit.visit_id} for customer {customer_id}")
            
            # Log the visit
            PromotionAuditLog.log_visit_recorded(
                session=session,
                customer_id=customer_id,
                visit_id=visit.visit_id,
                visit_data=visit.to_dict(),
                performed_by=created_by
            )
            
            # Process loyalty if applicable
            if visit.is_loyalty_visit:
                from application.Services.LoyaltyService import LoyaltyService
                LoyaltyService.process_visit(session, customer_id, visit.visit_id)
            
            return visit
        
        except Exception as e:
            app.logger.error(f"Error creating manual visit: {e}", exc_info=True)
            return None
    
    @staticmethod
    def get_customer_visit_history(session, customer_id, start_date=None, end_date=None, limit=None):
        """Get visit history for a customer.
        
        Args:
            session: Database session
            customer_id: UUID of the customer
            start_date: Optional start date filter
            end_date: Optional end date filter
            limit: Optional limit on number of results
            
        Returns:
            List of CustomerVisit objects
        """
        try:
            visits = CustomerVisit.get_visits_by_customer(
                session=session,
                customer_id=customer_id,
                start_date=start_date,
                end_date=end_date,
                limit=limit
            )
            return visits
        except Exception as e:
            app.logger.error(f"Error getting customer visit history: {e}")
            return []
    
    @staticmethod
    def get_visit_statistics(session, customer_id, start_date=None, end_date=None):
        """Get visit statistics for a customer.
        
        Args:
            session: Database session
            customer_id: UUID of the customer
            start_date: Optional start date filter
            end_date: Optional end date filter
            
        Returns:
            dict: Statistics including total visits, loyalty visits, etc.
        """
        try:
            if isinstance(customer_id, str):
                customer_id = uuid.UUID(customer_id)
            
            # Get all visits
            all_visits = CustomerVisit.get_visits_by_customer(
                session=session,
                customer_id=customer_id,
                start_date=start_date,
                end_date=end_date
            )
            
            # Calculate statistics
            total_visits = len(all_visits)
            loyalty_visits = sum(1 for v in all_visits if v.is_loyalty_visit)
            reward_visits = sum(1 for v in all_visits if v.reward_redeemed)
            
            # Count by source
            biometric_visits = sum(1 for v in all_visits if v.source == 'biometric')
            manual_visits = sum(1 for v in all_visits if v.source == 'manual')
            
            # Count by type
            regular_visits = sum(1 for v in all_visits if v.visit_type == 'regular')
            complimentary_visits = sum(1 for v in all_visits if v.visit_type == 'complimentary')
            
            return {
                'customer_id': str(customer_id),
                'total_visits': total_visits,
                'loyalty_visits': loyalty_visits,
                'reward_visits': reward_visits,
                'biometric_visits': biometric_visits,
                'manual_visits': manual_visits,
                'regular_visits': regular_visits,
                'complimentary_visits': complimentary_visits,
                'start_date': start_date.strftime('%Y-%m-%d') if start_date else None,
                'end_date': end_date.strftime('%Y-%m-%d') if end_date else None
            }
        
        except Exception as e:
            app.logger.error(f"Error getting visit statistics: {e}")
            return {
                'error': str(e)
            }
    
    @staticmethod
    def get_daily_visit_summary(session, visit_date=None):
        """Get summary of all customer visits for a specific date.
        
        Args:
            session: Database session
            visit_date: Date to summarize (defaults to today)
            
        Returns:
            dict: Summary with total visits, unique customers, etc.
        """
        try:
            if not visit_date:
                visit_date = date.today()
            
            visits = CustomerVisit.get_visits_by_date(session, visit_date)
            
            unique_customers = set(v.customer_id for v in visits)
            
            return {
                'date': visit_date.strftime('%Y-%m-%d'),
                'total_visits': len(visits),
                'unique_customers': len(unique_customers),
                'loyalty_visits': sum(1 for v in visits if v.is_loyalty_visit),
                'reward_redemptions': sum(1 for v in visits if v.reward_redeemed)
            }
        
        except Exception as e:
            app.logger.error(f"Error getting daily visit summary: {e}")
            return {
                'error': str(e)
            }

