import json
from datetime import datetime
from application.Models.MachineCommand import MachineCommand
from application.Models.company import CompanyDevice
from application.Models.Person import Person
from application.Models.EnrollInfo import EnrollInfo
from application.utils.db_connection import DatabaseConnection
from flask import current_app as app


def create_sync_command_for_device(session, enroll_id, name, roll_id, backupnum, signatures, target_device_sn, company_id):
    """Creates and enqueues a MachineCommand to synchronize user biometric data to a target device.

    Args:
        session: The database session (tenant database).
        enroll_id (int): The ID of the person.
        name (str): The name of the person.
        roll_id (int): The role ID of the person.
        backupnum (int): The backup number indicating the type of biometric data.
        signatures (str): The biometric signature data (e.g., base64 image, fingerprint template).
        target_device_sn (str): The serial number of the target device.
        company_id (str): The ID of the company.

    Returns:
        MachineCommand or None: The created MachineCommand object if successful, None otherwise.
    """
    try:
        # Construct the JSON message for 'setuserinfo' based on the protocol
        message_data = {
            "cmd": "setuserinfo",
            "enrollid": enroll_id,
            "name": name,
            "admin": roll_id,
            "backupnum": backupnum,
            "record": signatures,
        }

        message = json.dumps(message_data)

        # Create the MachineCommand
        machine_command = MachineCommand(
            name="setuserinfo",
            status=0,  # 0 for pending, 1 for sent, 2 for acknowledged
            send_status=0,  # 0 for pending, 1 for success, 2 for failure
            err_count=0,
            serial=target_device_sn,
            gmt_crate=datetime.now(),
            gmt_modified=datetime.now(),
            content=message
        )

        session.add(machine_command)
        session.commit()
        app.logger.info(f"Created sync command for enroll_id {enroll_id} (backupnum {backupnum}) to device {target_device_sn} for company {company_id}")
        return machine_command

    except Exception as e:
        app.logger.error(f"Error creating sync command for enroll_id {enroll_id} (backupnum {backupnum}) to device {target_device_sn}: {str(e)}")
        session.rollback()
        return None

def sync_biometric_data_to_devices(db_session, person_id, company_id, exclude_device_sn=None):
    """Synchronizes all relevant biometric data for a given person to all devices of a company,
    excluding a specified device if necessary. This function is fault-tolerant.

    Args:
        db_session: The database session to use (for the tenant database).
        person_id (int): The ID of the person whose biometric data needs to be synced.
        company_id (str): The ID of the company to which the devices belong.
        exclude_device_sn (str, optional): A device serial number to exclude from synchronization.
                                           Useful when data originates from one device and shouldn't be sent back to it.
    """
    app.logger.info(f"Attempting biometric data sync for person {person_id} in company {company_id}")

    try:
        # 1. Get all EnrollInfo records for the person (focus on biometric types)
        person_enroll_infos = db_session.query(EnrollInfo).filter_by(enroll_id=person_id).all()
        
        # Define all biometric backupnums based on the protocol documentation
        # 0-9: fingerprint, 10: password, 11: rfid card, 20-27: static face, 50: photo (dynamic face)
        biometric_backupnums = list(range(0, 10)) + list(range(20, 28)) + [10, 11, 50]
        biometric_enroll_infos = [ei for ei in person_enroll_infos if ei.backupnum in biometric_backupnums]

        if not biometric_enroll_infos:
            app.logger.info(f"No relevant biometric data found for person {person_id}. Skipping sync.")
            return

        # 2. Get the person's details
        person = db_session.query(Person).filter_by(id=person_id).first()
        if not person:
            app.logger.warning(f"Person with ID {person_id} not found in tenant DB. Cannot sync biometric data.")
            return

        # 3. Get all devices for the company (from central DB context)
        # This operation must use the central_db to get all company devices.
        # We explicitly open a new central_session here to ensure isolation and proper rollback/commit for central_db ops.
        from application.database import central_db # Import central_db here to avoid circular dependencies if app is importing this service
        with DatabaseConnection().get_session(db_name=None, is_central=True) as central_session:
            company_devices = central_session.query(CompanyDevice).filter_by(company_id=company_id).all()

        if not company_devices:
            app.logger.info(f"No devices found for company {company_id} in central DB. Skipping sync.")
            return

        # 4. Iterate through devices and create sync commands
        for device in company_devices:
            if device.device_sn == exclude_device_sn:
                app.logger.info(f"Excluding originating device {exclude_device_sn} from sync for person {person_id}.")
                continue

            app.logger.info(f"Preparing to send biometric data for person {person_id} to device {device.device_sn}")

            for enroll_info in biometric_enroll_infos:
                # Call the fault-tolerant command creation function
                create_sync_command_for_device(
                    db_session,  # Use the tenant session for MachineCommand creation
                    person.id,
                    person.name,
                    person.roll_id,
                    enroll_info.backupnum,
                    enroll_info.signatures,
                    device.device_sn,
                    company_id
                )

        app.logger.info(f"Successfully initiated biometric sync commands for person {person_id} to multiple devices in company {company_id}.")

    except Exception as e:
        # Catch any exceptions during the overall synchronization process
        app.logger.error(f"Overall error in sync_biometric_data_to_devices for person {person_id}, company {company_id}: {str(e)}")
        # No rollback of db_session here, as it belongs to the calling context.
        # Individual create_sync_command_for_device calls handle their own rollbacks.
