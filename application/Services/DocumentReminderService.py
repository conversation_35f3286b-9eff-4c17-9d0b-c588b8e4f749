import os
from datetime import datetime, date, timedelta
from flask import current_app as app
from application.database import db
from application.utils.db_connection import DatabaseConnection
from application.Models.company import Company
from application.Models.documents.document_reminder import DocumentReminder
from application.Models.documents.company_document import CompanyDocument

class DocumentReminderService:
    """Service for managing document expiry reminders and notifications"""

    def __init__(self):
        """Initialize reminder service"""
        self.email_enabled = os.getenv('EMAIL_NOTIFICATIONS_ENABLED', 'true').lower() == 'true'
        self.whatsapp_enabled = os.getenv('WHATSAPP_NOTIFICATIONS_ENABLED', 'false').lower() == 'true'
        self.default_reminder_days = [30, 14, 7, 1]  # Days before expiry to send reminders

    def process_pending_reminders(self):
        """Process all pending reminders across all companies"""
        try:
            # Get all companies
            companies = Company.get_companies()

            total_processed = 0
            total_sent = 0

            for company in companies:
                company_id = company.company_id
                database_name = company.database_name

                if not database_name:
                    continue

                try:
                    with DatabaseConnection.get_session(database_name) as session:
                        # Process reminders for this company
                        processed, sent = self._process_company_reminders(session, company_id)
                        total_processed += processed
                        total_sent += sent

                except Exception as e:
                    app.logger.error(f"Error processing reminders for company {company_id}: {e}")
                    continue

            app.logger.info(f"Processed {total_processed} reminders, sent {total_sent} notifications")
            return total_processed, total_sent

        except Exception as e:
            app.logger.error(f"Error in process_pending_reminders: {e}")
            return 0, 0

    def _process_company_reminders(self, session, company_id):
        """Process reminders for a specific company"""
        try:
            # Get pending reminders
            pending_reminders = DocumentReminder.get_pending_reminders(session, company_id)

            processed = 0
            sent = 0

            for reminder in pending_reminders:
                try:
                    # Send notification
                    if self._send_reminder_notification(reminder, session):
                        # Mark as sent
                        DocumentReminder.mark_reminder_sent(session, reminder.reminder_id)
                        sent += 1

                    processed += 1

                except Exception as e:
                    app.logger.error(f"Error processing reminder {reminder.reminder_id}: {e}")
                    continue

            return processed, sent

        except Exception as e:
            app.logger.error(f"Error processing company reminders for {company_id}: {e}")
            return 0, 0

    def _send_reminder_notification(self, reminder, session):
        """Send reminder notification via configured channels"""
        try:
            # Get document details
            document = reminder.document
            if not document:
                app.logger.warning(f"Document not found for reminder {reminder.reminder_id}")
                return False

            # Get employee details if available
            employee_name = "Unknown Employee"
            if document.employee:
                employee_name = f"{document.employee.first_name} {document.employee.last_name}"

            # Prepare notification content
            notification_data = {
                'document_name': document.document_name,
                'employee_name': employee_name,
                'expiry_date': document.expiry_date.strftime('%Y-%m-%d') if document.expiry_date else 'N/A',
                'days_until_expiry': reminder.days_before_expiry,
                'document_category': document.document_category
            }

            # Send via configured channels
            email_sent = False
            whatsapp_sent = False

            if reminder.reminder_type in ['EMAIL', 'BOTH'] and self.email_enabled:
                email_sent = self._send_email_reminder(notification_data, reminder)
                app.logger.info(f"Email reminder sent for document {document.document_id}")

            if reminder.reminder_type in ['WHATSAPP', 'BOTH'] and self.whatsapp_enabled:
                whatsapp_sent = self._send_whatsapp_reminder(notification_data, reminder)
                app.logger.info(f"WhatsApp reminder sent for document {document.document_id}")

            return email_sent or whatsapp_sent

        except Exception as e:
            app.logger.error(f"Error sending reminder notification: {e}")
            return False

    def _send_email_reminder(self, notification_data, reminder):
        """Send email reminder to HR users"""
        try:
            # Get HR users for the company
            hr_users = self._get_company_hr_users(reminder.company_id)

            if not hr_users:
                app.logger.warning(f"No HR users found for company {reminder.company_id}")
                return False

            subject = f"Document Expiry Alert: {notification_data['document_name']}"

            body = f"""
            Document Expiry Reminder

            Document: {notification_data['document_name']}
            Employee: {notification_data['employee_name']}
            Category: {notification_data['document_category']}
            Expires: {notification_data['expiry_date']}
            Days remaining: {notification_data['days_until_expiry']}

            Please take necessary action to renew or update this document.

            This is an automated notification from the HRMS Document Management System.
            """

            # Send to all HR users
            sent_count = 0
            for hr_user in hr_users:
                try:
                    self._send_email(hr_user.get('email'), subject, body)
                    sent_count += 1
                except Exception as e:
                    app.logger.error(f"Failed to send email to {hr_user.get('email')}: {e}")

            return sent_count > 0

        except Exception as e:
            app.logger.error(f"Error sending email reminder: {e}")
            return False

    def _send_whatsapp_reminder(self, notification_data, reminder):
        """Send WhatsApp reminder to HR users"""
        try:
            # Get HR users for the company
            hr_users = self._get_company_hr_users(reminder.company_id)

            if not hr_users:
                app.logger.warning(f"No HR users found for company {reminder.company_id}")
                return False

            message = f"""
            📄 *Document Expiry Alert*

            *Document:* {notification_data['document_name']}
            *Employee:* {notification_data['employee_name']}
            *Category:* {notification_data['document_category']}
            *Expires:* {notification_data['expiry_date']}
            *Days remaining:* {notification_data['days_until_expiry']}

            Please take necessary action.
            """

            # Send to all HR users with phone numbers
            sent_count = 0
            for hr_user in hr_users:
                phone = hr_user.get('phone_number') or hr_user.get('phone')
                if phone:
                    try:
                        self._send_whatsapp(phone, message)
                        sent_count += 1
                    except Exception as e:
                        app.logger.error(f"Failed to send WhatsApp to {phone}: {e}")

            return sent_count > 0

        except Exception as e:
            app.logger.error(f"Error sending WhatsApp reminder: {e}")
            return False

    def _get_company_hr_users(self, company_id):
        """Get HR users for a company"""
        try:
            # This would integrate with your existing user/role system
            # For now, return a placeholder - you'll need to implement this
            # based on your existing user management system

            # Example implementation:
            # from application.Models.user import User
            # return User.get_users_by_role_and_company(company_id, 'hr')

            app.logger.warning("_get_company_hr_users not implemented - returning empty list")
            return []

        except Exception as e:
            app.logger.error(f"Error getting HR users for company {company_id}: {e}")
            return []

    def _send_email(self, email, subject, body):
        """Send email using your existing email service"""
        try:
            # Integrate with your existing email service
            # Example:
            # from application.services.email.email_service import EmailService
            # email_service = EmailService()
            # email_service.send_email(email, subject, body)

            app.logger.info(f"Email would be sent to {email}: {subject}")
            # For now, just log - you'll integrate with your email service

        except Exception as e:
            raise Exception(f"Failed to send email: {e}")

    def _send_whatsapp(self, phone, message):
        """Send WhatsApp message using your preferred service"""
        try:
            # Integrate with WhatsApp service (Twilio, 360Dialog, etc.)
            # Example:
            # from application.services.whatsapp.whatsapp_service import WhatsAppService
            # whatsapp_service = WhatsAppService()
            # whatsapp_service.send_message(phone, message)

            app.logger.info(f"WhatsApp would be sent to {phone}")
            # For now, just log - you'll integrate with your WhatsApp service

        except Exception as e:
            raise Exception(f"Failed to send WhatsApp: {e}")

    def create_reminders_for_document(self, document_id, company_id, expiry_date=None):
        """Create reminders for a document (called when document is uploaded/updated)"""
        try:
            database_name = Company.get_database_given_company_id(company_id)
            if not database_name:
                return False

            with DatabaseConnection.get_session(database_name) as session:
                # If no expiry date, no reminders needed
                if not expiry_date:
                    return True

                # Create reminders
                DocumentReminder.create_reminders_for_document(session, document_id, company_id)
                return True

        except Exception as e:
            app.logger.error(f"Error creating reminders for document {document_id}: {e}")
            return False

    def reschedule_document_reminders(self, document_id, company_id, new_expiry_date=None):
        """Reschedule reminders when document expiry date changes"""
        try:
            database_name = Company.get_database_given_company_id(company_id)
            if not database_name:
                return False

            with DatabaseConnection.get_session(database_name) as session:
                DocumentReminder.reschedule_reminders_for_document(
                    session, document_id, company_id, new_expiry_date
                )
                return True

        except Exception as e:
            app.logger.error(f"Error rescheduling reminders for document {document_id}: {e}")
            return False

    def get_reminder_statistics(self, company_id):
        """Get reminder statistics for a company"""
        try:
            database_name = Company.get_database_given_company_id(company_id)
            if not database_name:
                return {}

            with DatabaseConnection.get_session(database_name) as session:
                return DocumentReminder.get_reminder_statistics(session, company_id)

        except Exception as e:
            app.logger.error(f"Error getting reminder statistics for company {company_id}: {e}")
            return {}

    def cleanup_old_reminders(self, company_id, days_old=90):
        """Clean up old sent reminders"""
        try:
            database_name = Company.get_database_given_company_id(company_id)
            if not database_name:
                return 0

            with DatabaseConnection.get_session(database_name) as session:
                return DocumentReminder.cleanup_old_reminders(session, company_id, days_old)

        except Exception as e:
            app.logger.error(f"Error cleaning up old reminders for company {company_id}: {e}")
            return 0

    def get_overdue_reminders(self, company_id):
        """Get overdue reminders that haven't been sent"""
        try:
            database_name = Company.get_database_given_company_id(company_id)
            if not database_name:
                return []

            with DatabaseConnection.get_session(database_name) as session:
                reminders = DocumentReminder.get_overdue_reminders(session, company_id)
                return [reminder.to_dict() for reminder in reminders]

        except Exception as e:
            app.logger.error(f"Error getting overdue reminders for company {company_id}: {e}")
            return []

    def send_test_reminder(self, company_id, reminder_type='EMAIL'):
        """Send a test reminder to verify configuration"""
        try:
            # Get HR users for the company
            hr_users = self._get_company_hr_users(company_id)

            if not hr_users:
                return {"success": False, "message": "No HR users found"}

            test_data = {
                'document_name': 'Test Document',
                'employee_name': 'Test Employee',
                'expiry_date': (date.today() + timedelta(days=7)).strftime('%Y-%m-%d'),
                'days_until_expiry': 7,
                'document_category': 'TEST'
            }

            sent_count = 0

            if reminder_type in ['EMAIL', 'BOTH'] and self.email_enabled:
                # Send test email to first HR user
                hr_user = hr_users[0]
                if hr_user.get('email'):
                    self._send_email(
                        hr_user['email'],
                        "Test Document Expiry Reminder",
                        f"This is a test notification.\n\n{test_data}"
                    )
                    sent_count += 1

            if reminder_type in ['WHATSAPP', 'BOTH'] and self.whatsapp_enabled:
                # Send test WhatsApp to first HR user
                hr_user = hr_users[0]
                phone = hr_user.get('phone_number') or hr_user.get('phone')
                if phone:
                    self._send_whatsapp(
                        phone,
                        f"🧪 *Test Notification*\n\n{test_data}"
                    )
                    sent_count += 1

            return {
                "success": True,
                "message": f"Test reminder sent to {sent_count} recipient(s)",
                "sent_count": sent_count
            }

        except Exception as e:
            app.logger.error(f"Error sending test reminder: {e}")
            return {"success": False, "message": str(e)}
