import boto3
import os
import logging
from botocore.client import Config
from typing import Dict, Optional, BinaryIO, Union
import uuid
from datetime import datetime
from flask import current_app as app
from dotenv import load_dotenv
from application.Models.company import Company
import re

# Load environment variables with logging
logger = logging.getLogger(__name__)
logger.info("Loading environment variables from .env file...")
load_dotenv()
logger.info("Environment variables loaded successfully")

class DocumentStorageService:
    """Unified storage service for document operations across all providers"""

    def __init__(self, provider_config: Optional[Dict] = None):
        """Initialize with provider configuration"""
        logger.info("Initializing DocumentStorageService...")
        self.provider_config = provider_config or self._get_default_config()
        self.provider = self.provider_config.get('provider', 'LOCAL')
        logger.info(f"Using storage provider: {self.provider}")
        logger.info(f"Provider config keys: {list(self.provider_config.keys())}")
        self.client = self._create_client()
        logger.info("DocumentStorageService initialized successfully")

    def _get_default_config(self) -> Dict:
        """Get default configuration from environment"""
        logger.info("Getting default configuration from environment variables...")

        # Log environment variable status (without exposing secrets)
        storage_provider = os.getenv('STORAGE_PROVIDER', 'LOCAL')
        s3_endpoint = os.getenv('S3_ENDPOINT', 'https://s3.contabo.com')
        s3_access_key = os.getenv('S3_ACCESS_KEY')
        s3_secret_key = os.getenv('S3_SECRET_KEY')
        s3_region = os.getenv('S3_REGION', 'us-east-1')

        logger.info(f"STORAGE_PROVIDER: {storage_provider}")
        logger.info(f"S3_ENDPOINT: {s3_endpoint}")
        logger.info(f"S3_REGION: {s3_region}")
        logger.info(f"S3_ACCESS_KEY present: {s3_access_key is not None}")
        logger.info(f"S3_SECRET_KEY present: {s3_secret_key is not None}")

        # Log Azure and Local config
        azure_account_name = os.getenv('AZURE_ACCOUNT_NAME')
        azure_account_key = os.getenv('AZURE_ACCOUNT_KEY')
        local_base_path = os.getenv('LOCAL_STORAGE_PATH', '/tmp/hr_documents')

        logger.info(f"AZURE_ACCOUNT_NAME present: {azure_account_name is not None}")
        logger.info(f"AZURE_ACCOUNT_KEY present: {azure_account_key is not None}")
        logger.info(f"LOCAL_STORAGE_PATH: {local_base_path}")

        config = {
            'provider': storage_provider,
            's3_endpoint': s3_endpoint,
            's3_access_key': s3_access_key,
            's3_secret_key': s3_secret_key,
            's3_region': s3_region,
            'azure_account_name': azure_account_name,
            'azure_account_key': azure_account_key,
            'azure_container_prefix': os.getenv('AZURE_CONTAINER_PREFIX', 'company-docs'),
            'local_base_path': local_base_path
        }

        logger.info(f"Final configuration: provider={config['provider']}")
        return config

    def _create_client(self):
        """Create appropriate client based on provider"""
        try:
            logger.info(f"Creating client for provider: {self.provider}")

            if self.provider == 'S3':
                logger.info("Creating S3 client...")
                logger.info(f"S3 Endpoint: {self.provider_config['s3_endpoint']}")
                logger.info(f"S3 Region: {self.provider_config['s3_region']}")
                logger.info(f"S3 Access Key present: {self.provider_config['s3_access_key'] is not None}")
                logger.info(f"S3 Secret Key present: {self.provider_config['s3_secret_key'] is not None}")

                # Check for None values before creating client
                if self.provider_config['s3_access_key'] is None:
                    logger.error("S3_ACCESS_KEY is None - environment variable not loaded!")
                    raise ValueError("S3_ACCESS_KEY environment variable is not set")
                if self.provider_config['s3_secret_key'] is None:
                    logger.error("S3_SECRET_KEY is None - environment variable not loaded!")
                    raise ValueError("S3_SECRET_KEY environment variable is not set")

                client = boto3.client(
                    's3',
                    endpoint_url=self.provider_config['s3_endpoint'],
                    aws_access_key_id=self.provider_config['s3_access_key'],
                    aws_secret_access_key=self.provider_config['s3_secret_key'],
                    region_name=self.provider_config['s3_region'],
                    config=Config(signature_version='s3v4')
                )
                logger.info("S3 client created successfully")
                return client
            elif self.provider == 'AZURE_BLOB':
                from azure.storage.blob import BlobServiceClient
                account_url = f"https://{self.provider_config['azure_account_name']}.blob.core.windows.net"
                return BlobServiceClient(
                    account_url=account_url,
                    credential=self.provider_config['azure_account_key']
                )
            return None  # LOCAL provider
        except Exception as e:
            app.logger.error(f"Error creating storage client: {e}")
            return None

    def upload_document(self,
                       file_obj: BinaryIO,
                       filename: str,
                       company_id: str,
                       metadata: Optional[Dict] = None,
                       content_type: Optional[str] = None) -> Dict[str, Union[str, int]]:
        """
        Upload document and return storage info
        Works with any configured provider
        """
        logger.info(f"Starting document upload: {filename} for company: {company_id}")
        logger.info(f"Provider: {self.provider}, Content-Type: {content_type}")
        logger.info(f"Metadata keys: {list(metadata.keys()) if metadata else 'None'}")

        try:
            # Generate unique file key
            file_extension = os.path.splitext(filename)[1] if '.' in filename else ''
            unique_filename = f"{uuid.uuid4()}{file_extension}"
            file_key = f"documents/{unique_filename}"
            logger.info(f"Generated unique filename: {unique_filename}")
            logger.info(f"File key: {file_key}")

            # Get file size
            file_obj.seek(0, 2)  # Seek to end
            file_size = file_obj.tell()
            file_obj.seek(0)  # Reset to beginning
            logger.info(f"File size: {file_size} bytes")

            # Determine bucket/container name
            bucket_name = self._get_bucket_name(company_id)
            logger.info(f"Bucket/Container name: {bucket_name}")

            # Upload based on provider
            logger.info(f"Starting upload to {self.provider} provider...")
            if self.provider == 'S3':
                logger.info("Calling _upload_s3 method")
                result = self._upload_s3(file_obj, bucket_name, file_key, metadata, content_type)
                logger.info("S3 upload completed")
            elif self.provider == 'AZURE_BLOB':
                logger.info("Calling _upload_azure method")
                result = self._upload_azure(file_obj, bucket_name, file_key, metadata, content_type)
                logger.info("Azure upload completed")
            else:  # LOCAL
                logger.info("Calling _upload_local method")
                result = self._upload_local(file_obj, filename, company_id, unique_filename)
                logger.info("Local upload completed")

            logger.info(f"Upload result: {result}")

            # Add common metadata
            result.update({
                'original_filename': filename,
                'file_size_bytes': file_size,
                'uploaded_at': datetime.now().isoformat()
            })

            app.logger.info(f"Successfully uploaded document: {filename} for company {company_id}")
            return result

        except Exception as e:
            app.logger.error(f"Error uploading document {filename}: {e}")
            raise Exception(f"Failed to upload document: {str(e)}")

    def download_document(self, storage_info: Dict) -> BinaryIO:
        """Download document using stored info"""
        try:
            provider = storage_info['storage_provider']
            bucket = storage_info['storage_bucket']
            key = storage_info['storage_key']

            if provider == 'S3':
                return self._download_s3(bucket, key)
            elif provider == 'AZURE_BLOB':
                return self._download_azure(bucket, key)
            else:  # LOCAL
                return self._download_local(bucket, key)

        except Exception as e:
            app.logger.error(f"Error downloading document: {e}")
            raise Exception(f"Failed to download document: {str(e)}")

    def generate_download_url(self, storage_info: Dict, expiry_seconds: int = 3600) -> str:
        """Generate secure download URL"""
        try:
            provider = storage_info['storage_provider']
            bucket = storage_info['storage_bucket']
            key = storage_info['storage_key']

            if provider == 'S3':
                return self._generate_s3_download_url(bucket, key, expiry_seconds)
            elif provider == 'AZURE_BLOB':
                return self._generate_azure_download_url(bucket, key, expiry_seconds)
            else:  # LOCAL
                return self._generate_local_download_url(bucket, key)

        except Exception as e:
            app.logger.error(f"Error generating download URL: {e}")
            raise Exception(f"Failed to generate download URL: {str(e)}")

    def delete_document(self, storage_info: Dict) -> bool:
        """Delete document from storage"""
        try:
            provider = storage_info['storage_provider']
            bucket = storage_info['storage_bucket']
            key = storage_info['storage_key']

            if provider == 'S3':
                self.client.delete_object(Bucket=bucket, Key=key)
            elif provider == 'AZURE_BLOB':
                blob_client = self.client.get_blob_client(container=bucket, blob=key)
                blob_client.delete_blob()
            else:  # LOCAL
                self._delete_local(bucket, key)

            app.logger.info(f"Successfully deleted document: {key}")
            return True

        except Exception as e:
            app.logger.error(f"Error deleting document: {e}")
            return False

    def get_storage_usage(self, company_id: str) -> Dict[str, Union[int, float]]:
        """Get storage usage for company"""
        try:
            bucket_name = self._get_bucket_name(company_id)

            if self.provider == 'S3':
                return self._get_s3_usage(bucket_name)
            elif self.provider == 'AZURE_BLOB':
                return self._get_azure_usage(bucket_name)
            else:  # LOCAL
                return self._get_local_usage(company_id)

        except Exception as e:
            app.logger.error(f"Error getting storage usage for company {company_id}: {e}")
            return {'total_bytes': 0, 'total_mb': 0.0, 'object_count': 0}

    def _get_bucket_name(self, company_id: str) -> str:
        """Generate bucket/container name for company"""
        company = Company.get_company_by_id(company_id)
        company_name = company.company_name
        # Sanitize company_name to be lowercase, replace spaces with hyphens, and remove any other special characters
        company_name = re.sub(r'[^a-z0-9-]', '', company_name.lower().replace(" ", "-"))
        bucket_name = f"{company_name}-{company_id}"
        logger.info(f"Generated bucket name: {bucket_name} for company_id: {company_id}")
        return bucket_name

    def _upload_s3(self, file_obj: BinaryIO, bucket: str, key: str,
                   metadata: Optional[Dict], content_type: Optional[str]) -> Dict[str, str]:
        """Upload to S3-compatible storage"""
        extra_args = {}
        if metadata:
            extra_args['Metadata'] = metadata
        if content_type:
            extra_args['ContentType'] = content_type

        # Ensure bucket exists
        self._ensure_s3_bucket(bucket)

        self.client.upload_fileobj(file_obj, bucket, key, ExtraArgs=extra_args)

        return {
            'storage_provider': 'S3',
            'storage_bucket': bucket,
            'storage_key': key,
            'storage_url': self._generate_s3_download_url(bucket, key)
        }

    def _download_s3(self, bucket: str, key: str) -> BinaryIO:
        """Download from S3-compatible storage"""
        response = self.client.get_object(Bucket=bucket, Key=key)
        return response['Body']

    def _generate_s3_download_url(self, bucket: str, key: str, expiry: int = 3600) -> str:
        """Generate S3 pre-signed URL"""
        return self.client.generate_presigned_url(
            'get_object',
            Params={'Bucket': bucket, 'Key': key},
            ExpiresIn=expiry
        )

    def _ensure_s3_bucket(self, bucket: str):
        """Ensure S3 bucket exists"""
        try:
            self.client.head_bucket(Bucket=bucket)
        except:
            # Bucket doesn't exist, create it
            self.client.create_bucket(Bucket=bucket)

    def _get_s3_usage(self, bucket: str) -> Dict[str, Union[int, float]]:
        """Get S3 storage usage"""
        try:
            response = self.client.list_objects_v2(Bucket=bucket)
            objects = response.get('Contents', [])
            total_size = sum(obj['Size'] for obj in objects)

            return {
                'total_bytes': total_size,
                'total_mb': round(total_size / (1024 * 1024), 2),
                'object_count': len(objects)
            }
        except Exception:
            return {'total_bytes': 0, 'total_mb': 0.0, 'object_count': 0}

    def _upload_azure(self, file_obj: BinaryIO, container: str, blob_name: str,
                      metadata: Optional[Dict], content_type: Optional[str]) -> Dict[str, str]:
        """Upload to Azure Blob Storage"""
        blob_client = self.client.get_blob_client(container=container, blob=blob_name)

        upload_options = {}
        if content_type:
            upload_options['content_type'] = content_type
        if metadata:
            upload_options['metadata'] = metadata

        blob_client.upload_blob(file_obj, overwrite=True, **upload_options)

        return {
            'storage_provider': 'AZURE_BLOB',
            'storage_bucket': container,
            'storage_key': blob_name,
            'storage_url': blob_client.url
        }

    def _download_azure(self, container: str, blob_name: str) -> BinaryIO:
        """Download from Azure Blob Storage"""
        blob_client = self.client.get_blob_client(container=container, blob=blob_name)
        return blob_client.download_blob().readall()

    def _generate_azure_download_url(self, container: str, blob_name: str, expiry: int = 3600) -> str:
        """Generate Azure SAS URL"""
        blob_client = self.client.get_blob_client(container=container, blob=blob_name)
        # For now, return the direct URL (implement SAS token later if needed)
        return blob_client.url

    def _get_azure_usage(self, container: str) -> Dict[str, Union[int, float]]:
        """Get Azure storage usage"""
        try:
            container_client = self.client.get_container_client(container)
            blobs = list(container_client.list_blobs())
            total_size = sum(blob.size for blob in blobs)

            return {
                'total_bytes': total_size,
                'total_mb': round(total_size / (1024 * 1024), 2),
                'object_count': len(blobs)
            }
        except Exception:
            return {'total_bytes': 0, 'total_mb': 0.0, 'object_count': 0}

    def _upload_local(self, file_obj: BinaryIO, original_filename: str,
                      company_id: str, unique_filename: str) -> Dict[str, str]:
        """Upload to local filesystem"""
        import os
        from pathlib import Path

        base_path = Path(self.provider_config['local_base_path'])
        company_path = base_path / f"company-{company_id}" / "documents"
        company_path.mkdir(parents=True, exist_ok=True)

        file_path = company_path / unique_filename
        with open(file_path, 'wb') as f:
            f.write(file_obj.read())

        return {
            'storage_provider': 'LOCAL',
            'storage_bucket': f"company-{company_id}",
            'storage_key': f"documents/{unique_filename}",
            'storage_url': f"/local-files/{company_id}/{unique_filename}"
        }

    def _download_local(self, bucket: str, key: str) -> BinaryIO:
        """Download from local filesystem"""
        import os
        from pathlib import Path

        base_path = Path(self.provider_config['local_base_path'])
        file_path = base_path / bucket / key

        if not file_path.exists():
            raise FileNotFoundError(f"Local file not found: {file_path}")

        return open(file_path, 'rb')

    def _generate_local_download_url(self, bucket: str, key: str) -> str:
        """Generate local download URL"""
        return f"/api/documents/download/local/{bucket}/{key}"

    def _delete_local(self, bucket: str, key: str):
        """Delete local file"""
        import os
        from pathlib import Path

        base_path = Path(self.provider_config['local_base_path'])
        file_path = base_path / bucket / key

        if file_path.exists():
            file_path.unlink()

    def _get_local_usage(self, company_id: str) -> Dict[str, Union[int, float]]:
        """Get local storage usage"""
        import os
        from pathlib import Path

        try:
            base_path = Path(self.provider_config['local_base_path'])
            company_path = base_path / f"company-{company_id}" / "documents"

            if not company_path.exists():
                return {'total_bytes': 0, 'total_mb': 0.0, 'object_count': 0}

            total_size = 0
            file_count = 0

            for file_path in company_path.rglob('*'):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
                    file_count += 1

            return {
                'total_bytes': total_size,
                'total_mb': round(total_size / (1024 * 1024), 2),
                'object_count': file_count
            }
        except Exception:
            return {'total_bytes': 0, 'total_mb': 0.0, 'object_count': 0}

