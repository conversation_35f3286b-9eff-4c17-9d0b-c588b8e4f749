"""
Employee Validation Service

This service provides validation and safety checks for employee operations,
specifically to prevent orphaned employee records during device synchronization.

Created: 2025-10-01
Purpose: Prevent the creation of 33,371 orphaned employee records issue from recurring
"""

from flask import current_app as app
import uuid
from typing import Optional, Tuple, Dict, Any


class EmployeeValidationService:
    """Service for validating employee operations and preventing orphaned records."""
    
    @staticmethod
    def validate_employee_creation(session, employee_id: uuid.UUID, person_id: int = None) -> Tuple[bool, str]:
        """
        Validate that an employee can be safely created.
        
        Args:
            session: Database session
            employee_id: UUID of the employee to create
            person_id: Optional person ID to verify linkage
            
        Returns:
            Tuple of (is_valid, error_message)
            - (True, "") if employee can be created
            - (False, "error message") if employee should not be created
        """
        from application.Models.employees.employee import Employee
        from application.Models.Person import Person
        
        # Check 1: Verify employee doesn't already exist
        try:
            existing_employee = session.query(Employee).filter_by(employee_id=employee_id).first()
            if existing_employee:
                return False, f"Employee with employee_id {employee_id} already exists"
        except Exception as e:
            app.logger.error(f"Error checking employee existence: {e}")
            return False, f"Database error checking employee existence: {str(e)}"
        
        # Check 2: Verify employee_id is linked to a person record
        try:
            person_with_employee_id = session.query(Person).filter_by(employee_id=str(employee_id)).first()
            if not person_with_employee_id:
                return False, f"No person record found with employee_id {employee_id}. Cannot create orphaned employee."
        except Exception as e:
            app.logger.error(f"Error checking person linkage: {e}")
            return False, f"Database error checking person linkage: {str(e)}"
        
        # Check 3: If person_id provided, verify it matches
        if person_id is not None:
            if person_with_employee_id.id != person_id:
                return False, f"Person ID mismatch: expected {person_id}, found {person_with_employee_id.id}"
        
        # All checks passed
        return True, ""
    
    @staticmethod
    def safe_get_employee_by_id(session, employee_id: uuid.UUID) -> Optional[Any]:
        """
        Safely get an employee by ID with proper error handling.
        
        This method distinguishes between "employee not found" and "database error".
        
        Args:
            session: Database session
            employee_id: UUID of the employee
            
        Returns:
            Employee object if found, None if not found
            
        Raises:
            Exception: If there's a database error (not just "not found")
        """
        from application.Models.employees.employee import Employee
        
        try:
            employee = session.query(Employee).filter_by(employee_id=employee_id).first()
            return employee
        except Exception as e:
            app.logger.error(f"Database error getting employee {employee_id}: {e}")
            raise  # Re-raise to let caller handle it
    
    @staticmethod
    def verify_employee_person_linkage(session, employee_id: uuid.UUID) -> Tuple[bool, Optional[Any]]:
        """
        Verify that an employee is properly linked to a person record.
        
        Args:
            session: Database session
            employee_id: UUID of the employee
            
        Returns:
            Tuple of (is_linked, person_object)
            - (True, person) if employee is linked to a person
            - (False, None) if employee is orphaned
        """
        from application.Models.Person import Person
        
        try:
            person = session.query(Person).filter_by(employee_id=str(employee_id)).first()
            if person:
                return True, person
            else:
                app.logger.warning(f"Orphaned employee detected: employee_id {employee_id} has no person record")
                return False, None
        except Exception as e:
            app.logger.error(f"Error checking employee-person linkage: {e}")
            raise
    
    @staticmethod
    def get_orphaned_employees_count(session) -> int:
        """
        Get count of orphaned employees (employees not linked to any person).
        
        Args:
            session: Database session
            
        Returns:
            Count of orphaned employees
        """
        from application.Models.employees.employee import Employee
        from application.Models.Person import Person
        
        try:
            # Query employees that don't have a matching person record
            orphaned_count = session.query(Employee).outerjoin(
                Person,
                Employee.employee_id == Person.employee_id
            ).filter(
                Person.employee_id.is_(None)
            ).count()
            
            return orphaned_count
        except Exception as e:
            app.logger.error(f"Error counting orphaned employees: {e}")
            return -1  # Return -1 to indicate error
    
    @staticmethod
    def create_employee_safely(session, employee_data: Dict[str, Any], person_id: int = None) -> Tuple[bool, Optional[Any], str]:
        """
        Safely create an employee with validation checks.
        
        Args:
            session: Database session
            employee_data: Dictionary with employee data (must include employee_id)
            person_id: Optional person ID to verify linkage
            
        Returns:
            Tuple of (success, employee_object, message)
            - (True, employee, "success message") if created
            - (False, None, "error message") if failed
        """
        from application.Models.employees.employee import Employee
        
        if 'employee_id' not in employee_data:
            return False, None, "employee_id is required in employee_data"
        
        employee_id = employee_data['employee_id']
        if isinstance(employee_id, str):
            try:
                employee_id = uuid.UUID(employee_id)
            except ValueError:
                return False, None, f"Invalid UUID format: {employee_id}"
        
        # Validate before creating
        is_valid, error_msg = EmployeeValidationService.validate_employee_creation(
            session, employee_id, person_id
        )
        
        if not is_valid:
            app.logger.warning(f"Employee creation validation failed: {error_msg}")
            return False, None, error_msg
        
        # Validation passed - create employee
        try:
            employee = Employee.create_employee(session, **employee_data)
            if employee:
                app.logger.info(f"✅ Employee created successfully: {employee_id}")
                return True, employee, "Employee created successfully"
            else:
                return False, None, "Employee.create_employee() returned None"
        except Exception as e:
            app.logger.error(f"Error creating employee: {e}")
            return False, None, f"Error creating employee: {str(e)}"
    
    @staticmethod
    def log_employee_sync_stats(session):
        """
        Log statistics about employee-person synchronization.
        
        Args:
            session: Database session
        """
        from application.Models.employees.employee import Employee
        from application.Models.Person import Person
        
        try:
            total_employees = session.query(Employee).count()
            total_persons_with_employee_id = session.query(Person).filter(
                Person.employee_id.isnot(None)
            ).count()
            orphaned_employees = EmployeeValidationService.get_orphaned_employees_count(session)
            
            app.logger.info(f"[EMPLOYEE_SYNC_STATS] Total employees: {total_employees}")
            app.logger.info(f"[EMPLOYEE_SYNC_STATS] Persons with employee_id: {total_persons_with_employee_id}")
            app.logger.info(f"[EMPLOYEE_SYNC_STATS] Orphaned employees: {orphaned_employees}")
            
            if orphaned_employees > 0:
                app.logger.warning(f"⚠️ WARNING: {orphaned_employees} orphaned employees detected!")
        except Exception as e:
            app.logger.error(f"Error logging employee sync stats: {e}")

