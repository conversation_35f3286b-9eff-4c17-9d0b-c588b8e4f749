"""Loyalty service for managing customer loyalty program operations."""

from flask import current_app as app
from application.Models.customers.customer import Customer
from application.Models.customers.customer_visit import CustomerVisit
from application.Models.customers.promotion_rule import PromotionRule
from application.Models.customers.customer_loyalty_balance import CustomerLoyaltyBalance
from application.Models.customers.reward_redemption import RewardRedemption
from application.Models.customers.promotion_audit_log import PromotionAuditLog
from datetime import datetime, date
import uuid


class LoyaltyService:
    """Service for managing loyalty program operations.
    
    This service handles:
    - Processing visits and updating loyalty balances
    - Checking reward eligibility
    - Redeeming rewards
    - Managing promotion rules
    """
    
    @staticmethod
    def process_visit(session, customer_id, visit_id):
        """Process a customer visit for loyalty program.
        
        This method:
        1. Gets all applicable promotion rules for the customer
        2. Updates loyalty balances
        3. Awards rewards if thresholds are met
        
        Args:
            session: Database session
            customer_id: UUID of the customer
            visit_id: UUID of the visit
            
        Returns:
            dict: Result with rewards_earned count and updated balances
        """
        try:
            if isinstance(customer_id, str):
                customer_id = uuid.UUID(customer_id)
            
            # Get customer
            customer = Customer.get_customer_by_id(session, customer_id)
            if not customer:
                app.logger.error(f"Customer not found: {customer_id}")
                return {'success': False, 'error': 'Customer not found'}
            
            # Get applicable promotion rules
            rules = PromotionRule.get_active_rules_for_customer(
                session=session,
                customer_segment=customer.customer_segment,
                current_date=date.today()
            )
            
            if not rules:
                app.logger.info(f"No applicable promotion rules for customer {customer_id}")
                return {'success': True, 'rewards_earned': 0, 'balances': []}
            
            rewards_earned = 0
            updated_balances = []
            
            # Process each applicable rule
            for rule in rules:
                # Only process visit_count rules for now
                if rule.rule_type != 'visit_count':
                    continue
                
                # Get or create loyalty balance
                balance = CustomerLoyaltyBalance.get_or_create_balance(
                    session=session,
                    customer_id=customer_id,
                    rule_id=rule.rule_id,
                    target_count=rule.trigger_value,
                    period_days=rule.trigger_period_days
                )
                
                if not balance:
                    app.logger.error(f"Failed to get/create balance for customer {customer_id}, rule {rule.rule_id}")
                    continue
                
                # Check if period has expired (for time-based rules)
                if rule.trigger_period_days and balance.is_period_expired():
                    app.logger.info(f"Period expired for balance {balance.balance_id}, resetting")
                    balance.reset_period(session, rule.trigger_period_days)
                
                # Store old balance for audit log
                old_balance_dict = balance.to_dict()
                
                # Increment count and check for reward
                reward_earned = balance.increment_count(session, increment=1)
                
                # Store new balance for audit log
                new_balance_dict = balance.to_dict()
                
                if reward_earned:
                    rewards_earned += 1
                    app.logger.info(f"Customer {customer_id} earned reward for rule {rule.rule_id}")
                    
                    # Log reward earned
                    PromotionAuditLog.log_reward_earned(
                        session=session,
                        customer_id=customer_id,
                        balance_id=balance.balance_id,
                        rule_id=rule.rule_id,
                        old_balance=old_balance_dict,
                        new_balance=new_balance_dict,
                        performed_by=None  # Automatic
                    )
                else:
                    # Log balance update
                    PromotionAuditLog.log_balance_updated(
                        session=session,
                        customer_id=customer_id,
                        balance_id=balance.balance_id,
                        old_balance=old_balance_dict,
                        new_balance=new_balance_dict,
                        performed_by=None
                    )
                
                updated_balances.append(balance.to_dict())
            
            app.logger.info(f"Processed visit {visit_id} for customer {customer_id}: {rewards_earned} rewards earned")
            
            return {
                'success': True,
                'rewards_earned': rewards_earned,
                'balances': updated_balances
            }
        
        except Exception as e:
            app.logger.error(f"Error processing visit for loyalty: {e}", exc_info=True)
            return {'success': False, 'error': str(e)}
    
    @staticmethod
    def check_reward_eligibility(session, customer_id, rule_id=None):
        """Check if a customer has available rewards.
        
        Args:
            session: Database session
            customer_id: UUID of the customer
            rule_id: Optional specific rule to check (None = check all)
            
        Returns:
            dict: Eligibility information with available rewards
        """
        try:
            if isinstance(customer_id, str):
                customer_id = uuid.UUID(customer_id)
            
            if rule_id:
                # Check specific rule
                if isinstance(rule_id, str):
                    rule_id = uuid.UUID(rule_id)
                
                balance = CustomerLoyaltyBalance.get_balance_by_customer_and_rule(
                    session=session,
                    customer_id=customer_id,
                    rule_id=rule_id
                )
                
                if not balance:
                    return {
                        'eligible': False,
                        'available_rewards': 0,
                        'rule_id': str(rule_id)
                    }
                
                return {
                    'eligible': balance.rewards_available > 0,
                    'available_rewards': balance.rewards_available,
                    'rule_id': str(rule_id),
                    'balance': balance.to_dict()
                }
            else:
                # Check all rules
                balances = CustomerLoyaltyBalance.get_balances_by_customer(
                    session=session,
                    customer_id=customer_id,
                    active_only=True
                )
                
                total_rewards = sum(b.rewards_available for b in balances)
                
                return {
                    'eligible': total_rewards > 0,
                    'total_available_rewards': total_rewards,
                    'balances': [b.to_dict() for b in balances if b.rewards_available > 0]
                }
        
        except Exception as e:
            app.logger.error(f"Error checking reward eligibility: {e}")
            return {'error': str(e)}
    
    @staticmethod
    def redeem_reward(session, customer_id, rule_id, redeemed_by=None, visit_id=None, 
                     redemption_method='manual', notes=None):
        """Redeem a customer's reward.
        
        Args:
            session: Database session
            customer_id: UUID of the customer
            rule_id: UUID of the promotion rule
            redeemed_by: User ID who processed the redemption
            visit_id: Optional visit_id if redeeming for a specific visit
            redemption_method: Method of redemption (manual, automatic, self_service)
            notes: Optional notes
            
        Returns:
            RewardRedemption object or None if failed
        """
        try:
            if isinstance(customer_id, str):
                customer_id = uuid.UUID(customer_id)
            if isinstance(rule_id, str):
                rule_id = uuid.UUID(rule_id)
            
            # Get customer
            customer = Customer.get_customer_by_id(session, customer_id)
            if not customer:
                app.logger.error(f"Customer not found: {customer_id}")
                return None
            
            # Get rule
            rule = PromotionRule.get_rule_by_id(session, rule_id)
            if not rule:
                app.logger.error(f"Promotion rule not found: {rule_id}")
                return None
            
            # Get balance
            balance = CustomerLoyaltyBalance.get_balance_by_customer_and_rule(
                session=session,
                customer_id=customer_id,
                rule_id=rule_id
            )
            
            if not balance:
                app.logger.error(f"No loyalty balance found for customer {customer_id}, rule {rule_id}")
                return None
            
            # Check if rewards available
            if balance.rewards_available <= 0:
                app.logger.warning(f"No rewards available for customer {customer_id}, rule {rule_id}")
                return None
            
            # Check max redemptions limit
            if rule.max_redemptions_per_customer:
                redemption_count = RewardRedemption.count_redemptions_by_customer(
                    session=session,
                    customer_id=customer_id,
                    rule_id=rule_id
                )
                
                if redemption_count >= rule.max_redemptions_per_customer:
                    app.logger.warning(f"Customer {customer_id} has reached max redemptions for rule {rule_id}")
                    return None
            
            # Create redemption record
            redemption = RewardRedemption.create_redemption(
                session=session,
                customer_id=customer_id,
                rule_id=rule_id,
                balance_id=balance.balance_id,
                visit_id=visit_id,
                redemption_date=date.today(),
                redemption_time=datetime.now(),
                reward_type=rule.reward_type,
                reward_value=rule.reward_value,
                reward_description=rule.reward_description,
                status='completed',
                redeemed_by=redeemed_by,
                redemption_method=redemption_method,
                notes=notes
            )
            
            if not redemption:
                app.logger.error(f"Failed to create redemption record")
                return None
            
            # Update balance
            balance.redeem_reward(session)
            
            # Log redemption
            PromotionAuditLog.log_reward_redeemed(
                session=session,
                customer_id=customer_id,
                redemption_id=redemption.redemption_id,
                redemption_data=redemption.to_dict(),
                performed_by=redeemed_by
            )
            
            app.logger.info(f"Redeemed reward for customer {customer_id}: {redemption.redemption_id}")
            
            return redemption
        
        except Exception as e:
            app.logger.error(f"Error redeeming reward: {e}", exc_info=True)
            return None
    
    @staticmethod
    def get_customer_loyalty_summary(session, customer_id):
        """Get a comprehensive loyalty summary for a customer.
        
        Args:
            session: Database session
            customer_id: UUID of the customer
            
        Returns:
            dict: Comprehensive loyalty information
        """
        try:
            if isinstance(customer_id, str):
                customer_id = uuid.UUID(customer_id)
            
            # Get customer
            customer = Customer.get_customer_by_id(session, customer_id)
            if not customer:
                return {'error': 'Customer not found'}
            
            # Get all balances
            balances = CustomerLoyaltyBalance.get_balances_by_customer(
                session=session,
                customer_id=customer_id,
                active_only=True
            )
            
            # Get redemption history
            redemptions = RewardRedemption.get_redemptions_by_customer(
                session=session,
                customer_id=customer_id,
                status='completed'
            )
            
            # Calculate totals
            total_rewards_available = sum(b.rewards_available for b in balances)
            total_rewards_earned = sum(b.rewards_earned for b in balances)
            total_rewards_redeemed = len(redemptions)
            
            return {
                'customer_id': str(customer_id),
                'customer_name': f"{customer.first_name} {customer.last_name}",
                'customer_segment': customer.customer_segment,
                'total_rewards_available': total_rewards_available,
                'total_rewards_earned': total_rewards_earned,
                'total_rewards_redeemed': total_rewards_redeemed,
                'active_balances': [b.to_dict() for b in balances],
                'recent_redemptions': [r.to_dict() for r in redemptions[:5]]  # Last 5
            }
        
        except Exception as e:
            app.logger.error(f"Error getting loyalty summary: {e}")
            return {'error': str(e)}
    
    @staticmethod
    def get_loyalty_analytics(session, start_date=None, end_date=None):
        """Get loyalty program analytics.
        
        Args:
            session: Database session
            start_date: Optional start date filter
            end_date: Optional end date filter
            
        Returns:
            dict: Analytics data
        """
        try:
            if not start_date:
                start_date = date.today().replace(day=1)  # First day of current month
            if not end_date:
                end_date = date.today()
            
            # Count total redemptions
            redemptions = session.query(RewardRedemption).filter(
                RewardRedemption.redemption_date >= start_date,
                RewardRedemption.redemption_date <= end_date,
                RewardRedemption.status == 'completed'
            ).all()
            
            # Count unique customers with rewards
            customers_with_rewards = session.query(CustomerLoyaltyBalance).filter(
                CustomerLoyaltyBalance.rewards_available > 0
            ).count()
            
            # Count total rewards available
            balances = session.query(CustomerLoyaltyBalance).filter(
                CustomerLoyaltyBalance.is_active == True
            ).all()
            
            total_rewards_available = sum(b.rewards_available for b in balances)
            total_rewards_earned = sum(b.rewards_earned for b in balances)
            
            return {
                'period': {
                    'start_date': start_date.strftime('%Y-%m-%d'),
                    'end_date': end_date.strftime('%Y-%m-%d')
                },
                'redemptions': {
                    'total': len(redemptions),
                    'by_reward_type': {}
                },
                'customers_with_rewards': customers_with_rewards,
                'total_rewards_available': total_rewards_available,
                'total_rewards_earned': total_rewards_earned,
                'total_rewards_redeemed': len(redemptions)
            }
        
        except Exception as e:
            app.logger.error(f"Error getting loyalty analytics: {e}")
            return {'error': str(e)}

