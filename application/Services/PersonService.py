from datetime import datetime
from abc import ABC, abstractmethod

from application.Models.MachineCommand import MachineCommand
from application.web_socket.WebSocketPool import WebSocketPool
from application.Models.EnrollInfo import EnrollInfo
import asyncio
from flask import current_app
class PersonService(ABC):
    @abstractmethod
    def updateByPrimaryKeySelective(self, record):
        pass

    @abstractmethod
    def updateByPrimaryKey(self, record):
        pass

    @abstractmethod
    def insertSelective(self, person):
        pass

    @abstractmethod
    def insert(self, person):
        pass

    @abstractmethod
    def deleteByPrimaryKey(self, id):
        pass

    @abstractmethod
    def selectByPrimaryKey(self, id):
        pass

    @abstractmethod
    def selectAll(self):
        pass


class PersonServiceImpl():
    def __init__(self, person, enroll_info, machine_command):
        self.person = person
        self.enroll_info = enroll_info
        self.machine_command = machine_command

    def updateByPrimaryKeySelective(self, record):
        return self.person.updateByPrimaryKeySelective(record)

    # Similar methods for other abstract methods...


    # class PersonService:
    #     def __init__(self, machine_command):
    #         self.machine_command = machine_command

    def set_user_to_device(self, session, enroll_id, name, backupnum, admin, records, device_sn):
            if backupnum != -1:
                machine_command = MachineCommand(name="setuserinfo", status=0, send_status=0, err_count=0,serial=device_sn)
                current_app.logger.info(f"machine_command: {machine_command}")
                # machine_command.name = "setuserinfo"
                # machine_command.status = 0
                # machine_command.send_status = 0
                # machine_command.err_count = 0
                # machine_command.serial = device_sn
                # machine_command.gmt_create = datetime.now()
                # machine_command.gmt_modified = datetime.now()

                if self.is_number(records):
                    machine_command.content = f'{{"cmd":"setuserinfo","enrollid":{enroll_id},"name":"{name}","backupnum":{backupnum},"admin":{admin},"record":{records}}}'
                else:
                    machine_command.content = f'{{"cmd":"setuserinfo","enrollid":{enroll_id},"name":"{name}","backupnum":{backupnum},"admin":{admin},"record":"{records}"}}'

                if backupnum == 11 or backupnum == 10:
                    if self.is_number(records):
                        machine_command.content = f'{{"cmd":"setuserinfo","enrollid":{enroll_id},"name":"{name}","backupnum":{backupnum},"admin":{admin},"record":{records}}}'
                        current_app.logger.info(f"content: {machine_command.content}")
                        current_app.logger.info(f"is_number: {self.is_number(records)}")
                    else:
                        machine_command.content = f'{{"cmd":"setuserinfo","enrollid":{enroll_id},"name":"{name}","backupnum":{backupnum},"admin":{admin},"record":“{records}"}}'
                        current_app.logger.info(f"content: {machine_command.content}")
                        current_app.logger.info(f"is_number: {self.is_number(records)}")


                try:
                    my_command = self.machine_command.insert(session, machine_command)
                    current_app.logger.info(f"Command for inserting user to device: {my_command}")
                except Exception as e:
                    current_app.logger.error(f"Error in inserting user to device: {e}")

            else:
                sb = f'{{"cmd":"setusername","count":1,"record":[{{"enrollid":{enroll_id},"name":"{name}"}}]}}'
                print(f'下发用户姓名{sb}')
                current_app.logger.info(f"sb command: {sb}")

                machine_command = MachineCommand()
                machine_command.name = "setusername"
                machine_command.status = 0
                machine_command.send_status = 0
                machine_command.err_count = 0
                machine_command.serial = device_sn
                machine_command.gmt_create = datetime.now()
                machine_command.gmt_modified = datetime.now()
                machine_command.content = sb
                current_app.logger.info(f"machine_command: {machine_command}")
                current_app.logger.info(f"machine_command content: {machine_command.content}")
                current_app.logger.info(f"machine_command name: {machine_command.name}")
                current_app.logger.info(f"machine_command status: {machine_command.status}")
                current_app.logger.info(f"machine_command send_status: {machine_command.send_status}")
                current_app.logger.info(f"machine_command err_count: {machine_command.err_count}")
                current_app.logger.info(f"machine_command serial: {machine_command.serial}")

                inserted = self.machine_command.insert(session, machine_command)
                current_app.logger.info(f"Inserted command: {inserted}")

    def set_username_to_device(self, session, device_sn, database_name=None):
        """Send all persons to a device with proper session context.

        Args:
            session: Database session for the specific company database
            device_sn: Device serial number
            database_name: Optional database name for logging context
        """
        current_app.logger.info(f"[DEVICE_SYNC] Starting bulk username sync to device {device_sn}")
        if database_name:
            current_app.logger.info(f"[DEVICE_SYNC] Using database context: {database_name}")

        # Use session-based query instead of broken Person.query.all()
        from application.Models.Person import Person
        persons = session.query(Person).all()

        current_app.logger.info(f"[DEVICE_SYNC] Retrieved {len(persons)} persons from database")

        # Log each person being included in the sync
        for person in persons:
            person_type = "employee" if person.employee_id else ("customer" if person.customer_id else "unknown")
            current_app.logger.info(f"[DEVICE_SYNC] Including person: ID={person.id}, name='{person.name}', type={person_type}")

        sb = []
        for j in range(len(persons)):
            if j == len(persons) - 1 or len(persons) == 1:
                sb.append(f'{{"enrollid":{persons[j].id},"name":"{persons[j].name}"}}')
            else:
                sb.append(f'{{"enrollid":{persons[j].id},"name":"{persons[j].name}"}},')

        sb_str = f'{{"cmd":"setusername","count":{len(persons)},"record":[{"".join(sb)}]}}'
        current_app.logger.info(f"[DEVICE_SYNC] Generated command for {len(persons)} persons: {sb_str[:200]}...")

        machine_command = MachineCommand()
        machine_command.name = "setusername"
        machine_command.status = 0
        machine_command.send_status = 0
        machine_command.err_count = 0
        machine_command.serial = device_sn
        machine_command.gmt_create = datetime.now()
        machine_command.gmt_modified = datetime.now()
        machine_command.content = sb_str

        inserted = self.machine_command.insert(session, machine_command)
        current_app.logger.info(f"[DEVICE_SYNC] Bulk username sync command created: {inserted}")

    def set_username_to_device_legacy(self, device_sn):
        """Legacy method for backward compatibility - DO NOT USE for new code.

        This method uses the old broken pattern and should be replaced with
        the session-based set_username_to_device method.
        """
        current_app.logger.warning(f"[DEVICE_SYNC] ⚠️ Using legacy set_username_to_device method for device {device_sn}")
        current_app.logger.warning(f"[DEVICE_SYNC] ⚠️ This method may cause session context issues - please update caller to use session-based method")

        persons = self.person.select_all()

        print(len(persons))

        sb = []
        for j in range(len(persons)):
            if j == len(persons) - 1 or len(persons) == 1:
                sb.append(f'{{"enrollid":{persons[j].id},"name":"{persons[j].name}"}}')
            else:
                sb.append(f'{{"enrollid":{persons[j].id},"name":"{persons[j].name}"}},')

        sb_str = f'{{"cmd":"setusername","count":{len(persons)},"record":[{"".join(sb)}]}}'
        print(f'下发用户姓名{sb_str}')

        machine_command = MachineCommand()
        machine_command.name = "setusername"
        machine_command.status = 0
        machine_command.send_status = 0
        machine_command.err_count = 0
        machine_command.serial = device_sn
        machine_command.gmt_create = datetime.now()
        machine_command.gmt_modified = datetime.now()
        machine_command.content = sb_str

        self.machine_command.insert(machine_command)
    def setUserToDevice2(self, device_sn):
        user_infos = self.enroll_info.users_to_send_device()

        print(len(user_infos))
        for user_info in user_infos:
            enroll_id = user_info.enroll_id
            name = user_info.name
            backupnum = user_info.backupnum
            admin = user_info.admin
            record = user_info.record

            machine_command = MachineCommand()
            machine_command.name = "setuserinfo"
            machine_command.status = 0
            machine_command.send_status = 0
            machine_command.err_count = 0
            machine_command.serial = device_sn
            machine_command.gmt_create = datetime.now()
            machine_command.gmt_modified = datetime.now()
            if self.is_number(record):
                machine_command.content = f'{{"cmd":"setuserinfo","enrollid":{enroll_id},"name":"{name}","backupnum":{backupnum},"admin":{admin},"record":{record}}}'
            else:
                machine_command.content = f'{{"cmd":"setuserinfo","enrollid":{enroll_id},"name":"{name}","backupnum":{backupnum},"admin":{admin},"record":"{record}"}}'

            if backupnum == 11 or backupnum == 10:

                if self.is_number(record):
                    machine_command.content = f'{{"cmd":"setuserinfo","enrollid":{enroll_id},"name":"{name}","backupnum":{backupnum},"admin":{admin},"record":{record}}}'
                else:
                    machine_command.content = f'{{"cmd":"setuserinfo","enrollid":{enroll_id},"name":"{name}","backupnum":{backupnum},"admin":{admin},"record":“{record}”}}'


            self.machine_command.insert(machine_command)

    def setUserToDevice2_with_session(self, session, device_sn, database_name=None):
        """Send all users with biometric data to a device with proper session context.

        Args:
            session: Database session for the specific company database
            device_sn: Device serial number
            database_name: Optional database name for logging context
        """
        current_app.logger.info(f"[DEVICE_SYNC] Starting bulk biometric sync to device {device_sn}")
        if database_name:
            current_app.logger.info(f"[DEVICE_SYNC] Using database context: {database_name}")

        # Get persons and enroll_infos using session-based queries
        from application.Models.Person import Person
        from application.Models.EnrollInfo import EnrollInfo

        persons = session.query(Person).all()
        enroll_infos = session.query(EnrollInfo).all()

        current_app.logger.info(f"[DEVICE_SYNC] Retrieved {len(persons)} persons and {len(enroll_infos)} enroll_infos")

        user_infos = []
        for person in persons:
            for enroll_info in enroll_infos:
                if person.id == enroll_info.enroll_id:
                    user_info = type('UserInfo', (), {
                        'enroll_id': person.id,
                        'name': person.name,
                        'backupnum': enroll_info.backupnum,
                        'admin': person.roll_id,
                        'record': enroll_info.signatures
                    })()
                    user_infos.append(user_info)

        current_app.logger.info(f"[DEVICE_SYNC] Found {len(user_infos)} user biometric records to sync")

        for user_info in user_infos:
            enroll_id = user_info.enroll_id
            name = user_info.name
            backupnum = user_info.backupnum
            admin = user_info.admin
            record = user_info.record

            current_app.logger.info(f"[DEVICE_SYNC] Syncing biometric: person_id={enroll_id}, name='{name}', backupnum={backupnum}")

            machine_command = MachineCommand()
            machine_command.name = "setuserinfo"
            machine_command.status = 0
            machine_command.send_status = 0
            machine_command.err_count = 0
            machine_command.serial = device_sn
            machine_command.gmt_create = datetime.now()
            machine_command.gmt_modified = datetime.now()

            if self.is_number(record):
                machine_command.content = f'{{"cmd":"setuserinfo","enrollid":{enroll_id},"name":"{name}","backupnum":{backupnum},"admin":{admin},"record":{record}}}'
            else:
                machine_command.content = f'{{"cmd":"setuserinfo","enrollid":{enroll_id},"name":"{name}","backupnum":{backupnum},"admin":{admin},"record":"{record}"}}'

            if backupnum == 11 or backupnum == 10:
                if self.is_number(record):
                    machine_command.content = f'{{"cmd":"setuserinfo","enrollid":{enroll_id},"name":"{name}","backupnum":{backupnum},"admin":{admin},"record":{record}}}'
                else:
                    machine_command.content = f'{{"cmd":"setuserinfo","enrollid":{enroll_id},"name":"{name}","backupnum":{backupnum},"admin":{admin},"record":"{record}"}}'

            inserted = self.machine_command.insert(session, machine_command)
            current_app.logger.info(f"[DEVICE_SYNC] Created biometric sync command: {inserted}")

    def is_number(self,s):
        try:
            float(s)
            return True
        except ValueError:
            return False
    def get_signature(self, enroll_id, device_sn, backupnum):
        asyncio.sleep(0.4)
        message = f'{{"cmd":"getuserinfo","enrollid":{enroll_id},"backupnum":0}}'
        message1 = f'{{"cmd":"getuserinfo","enrollid":{enroll_id},"backupnum":{backupnum}}}'
        device_status = WebSocketPool.get_device_status(device_sn)
        print(f'socket connection {WebSocketPool.get_device_socket_by_sn(device_sn)}')
        if device_status.status == 1:
            device_status.status = 0
            self.update_device(device_sn, device_status)
            if device_status.websocket is not None:
                device_status.websocket.send(message1)
        else:
            asyncio.sleep(0.4)
            device_status.status = 0
            self.update_device(device_sn, device_status)
            if device_status.websocket is not None:
                asyncio.run(WebSocketPool.send_message_to_device_status(device_sn, message))

    @staticmethod
    def get_signature2(enrolls, device_sn):
        for enroll in enrolls:
            message1 = f'{{"cmd":"getuserinfo","enrollid":{enroll.enroll_id},"backupnum":{enroll.backupnum}}}'
            machine_command = MachineCommand(content=message1, name='getuserinfo', status=0, send_status=0, err_count=0, serial=device_sn)
            machine_command.insert(machine_command)  # Insert machine_command to the database


    def delete_user_info_from_device(self, enroll_id, device_sn):
        backupnum = 13
        message = f'{{"cmd":"deleteuser","enrollid":{enroll_id},"backupnum":{backupnum}}}'

        machine_command = MachineCommand()
        machine_command.content = message
        machine_command.name = "deleteuser"
        machine_command.status = 0
        machine_command.send_status = 0
        machine_command.err_count = 0
        machine_command.serial = device_sn
        machine_command.gmt_create = datetime.now()
        machine_command.gmt_modified = datetime.now()

        self.machine_command.insert(machine_command)
        self.person.delete_by_primary_key(enroll_id)
        self.enroll_info.delete_by_enroll_id(enroll_id)

    def update_device(self, sn, device_status):
        if WebSocketPool.get_device_status(sn) is not None:
            WebSocketPool.remove_device_status(sn)
            WebSocketPool.add_device_and_status(sn, device_status)
        else:
            WebSocketPool.add_device_and_status(sn, device_status)