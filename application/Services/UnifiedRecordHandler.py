"""Unified record handler for routing biometric device records to appropriate services.

This service acts as the main router that determines whether a biometric record
is for an employee (attendance) or a customer (visit), and delegates to the
appropriate service for processing.
"""

from flask import current_app as app
from application.Models.Person import Person
from application.Models.Records import Record


class UnifiedRecordHandler:
    """Service for routing biometric device records to employee or customer handlers.
    
    This is the KEY integration point that maintains separation between
    employee attendance and customer visit tracking.
    """
    
    @staticmethod
    def handle_device_record(session, record):
        """Main entry point for processing biometric device records.

        This method:
        1. Identifies whether the record is for an employee or customer
        2. Routes to the appropriate service
        3. Returns the result

        Args:
            session: Database session
            record: Records object from biometric device

        Returns:
            dict: Result with keys:
                - success: bool
                - person_type: 'employee' or 'customer'
                - entity_id: employee_id or customer_id
                - result: Processing result from the appropriate service
                - message: Status message
        """
        try:
            app.logger.info(f"[UNIFIED_HANDLER] Starting record processing for enroll_id: {record.enroll_id}")
            app.logger.info(f"[UNIFIED_HANDLER] Record details - Time: {record.records_time}, Device: {record.device_serial_num}")

            # Step 1: Identify person type
            app.logger.info(f"[UNIFIED_HANDLER] Step 1: Calling Person.get_person_type() for enroll_id: {record.enroll_id}")
            person_type, linked_id = Person.get_person_type(session, record.enroll_id)

            app.logger.info(f"[UNIFIED_HANDLER] Person.get_person_type() returned: person_type='{person_type}', linked_id='{linked_id}'")

            if not person_type:
                app.logger.warning(f"[UNIFIED_HANDLER] ❌ Record from unknown person (enroll_id: {record.enroll_id})")
                return {
                    'success': False,
                    'person_type': None,
                    'entity_id': None,
                    'result': None,
                    'message': f'Unknown person with enroll_id: {record.enroll_id}'
                }

            app.logger.info(f"[UNIFIED_HANDLER] ✅ Person identified as {person_type} with ID: {linked_id}")
            app.logger.info(f"[UNIFIED_HANDLER] Step 2: Routing to {person_type} handler")

            # Step 2: Route to appropriate handler
            if person_type == 'employee':
                app.logger.info(f"[UNIFIED_HANDLER] 🔄 Routing to EMPLOYEE handler for employee_id: {linked_id}")
                result = UnifiedRecordHandler._handle_employee_record(session, linked_id, record)

                app.logger.info(f"[UNIFIED_HANDLER] Employee handler returned: {type(result).__name__ if result else 'None'}")

                return {
                    'success': result is not None and result.get('success', False),
                    'person_type': 'employee',
                    'entity_id': linked_id,
                    'result': result,
                    'message': result.get('message', 'Employee attendance processed') if result else 'Failed to process employee record'
                }

            elif person_type == 'customer':
                app.logger.info(f"[UNIFIED_HANDLER] 🔄 Routing to CUSTOMER handler for customer_id: {linked_id}")
                result = UnifiedRecordHandler._handle_customer_record(session, linked_id, record)

                app.logger.info(f"[UNIFIED_HANDLER] Customer handler returned: {type(result).__name__ if result else 'None'}")

                return {
                    'success': result is not None,
                    'person_type': 'customer',
                    'entity_id': linked_id,
                    'result': result,
                    'message': 'Customer visit record processed' if result else 'Failed to process customer record'
                }

            else:
                app.logger.error(f"[UNIFIED_HANDLER] ❌ Unexpected person_type: {person_type}")
                return {
                    'success': False,
                    'person_type': person_type,
                    'entity_id': linked_id,
                    'result': None,
                    'message': f'Unexpected person type: {person_type}'
                }

        except Exception as e:
            app.logger.error(f"[UNIFIED_HANDLER] ❌ EXCEPTION in handle_device_record: {e}", exc_info=True)
            return {
                'success': False,
                'person_type': None,
                'entity_id': None,
                'result': None,
                'message': f'Error processing record: {str(e)}'
            }
    
    @staticmethod
    def _handle_employee_record(session, employee_id, record):
        """Handle a biometric record for an employee (attendance).

        This delegates to the EXISTING attendance processing logic.
        NO CHANGES to existing employee attendance functionality.

        Args:
            session: Database session
            employee_id: Employee ID (not used - method determines employee from record)
            record: Records object

        Returns:
            dict: Result from Attendance.process_attendance_from_record()
        """
        try:
            app.logger.info(f"[EMPLOYEE_HANDLER] Starting employee attendance processing")
            app.logger.info(f"[EMPLOYEE_HANDLER] Employee ID: {employee_id}, Enroll ID: {record.enroll_id}")

            from application.Models.employees.attendance import Attendance

            # CRITICAL FIX: The existing method is process_attendance_from_record(),
            # NOT create_or_update_from_device_record()
            # It expects a DICTIONARY, not a Record object
            app.logger.info(f"[EMPLOYEE_HANDLER] Converting Record object to dictionary for Attendance.process_attendance_from_record()")

            # Convert Record object to dictionary format expected by the existing method
            record_dict = {
                'enroll_id': record.enroll_id,
                'records_time': record.records_time.strftime('%Y-%m-%d %H:%M:%S') if hasattr(record.records_time, 'strftime') else str(record.records_time),
                'device_serial_num': record.device_serial_num,
                'id': record.id
            }

            app.logger.info(f"[EMPLOYEE_HANDLER] Record dict: {record_dict}")
            app.logger.info(f"[EMPLOYEE_HANDLER] Calling Attendance.process_attendance_from_record()")

            # Use the EXISTING attendance processing method with correct signature
            result = Attendance.process_attendance_from_record(
                session=session,
                record=record_dict
            )

            app.logger.info(f"[EMPLOYEE_HANDLER] Attendance.process_attendance_from_record() returned: {result}")

            if result and result.get('success'):
                app.logger.info(f"[EMPLOYEE_HANDLER] ✅ Employee attendance processed successfully: {result.get('message')}")
            else:
                app.logger.warning(f"[EMPLOYEE_HANDLER] ⚠️ Employee attendance processing issue: {result.get('message') if result else 'No result returned'}")

            return result

        except AttributeError as ae:
            app.logger.error(f"[EMPLOYEE_HANDLER] ❌ AttributeError (method not found?): {ae}", exc_info=True)
            return {'success': False, 'message': f'Method error: {str(ae)}'}
        except Exception as e:
            app.logger.error(f"[EMPLOYEE_HANDLER] ❌ EXCEPTION in employee record handling: {e}", exc_info=True)
            return {'success': False, 'message': f'Error: {str(e)}'}
    
    @staticmethod
    def _handle_customer_record(session, customer_id, record):
        """Handle a biometric record for a customer (visit).
        
        This delegates to the NEW customer visit service.
        
        Args:
            session: Database session
            customer_id: Customer ID (UUID)
            record: Records object
            
        Returns:
            CustomerVisit object or None
        """
        try:
            from application.Services.CustomerVisitService import CustomerVisitService
            
            # Delegate to customer visit service
            visit = CustomerVisitService.process_customer_record(
                session=session,
                customer_id=customer_id,
                record=record
            )
            
            if visit:
                app.logger.info(f"Customer visit processed: {visit.visit_id}")
            else:
                app.logger.warning(f"Failed to process customer visit for {customer_id}")
            
            return visit
        
        except Exception as e:
            app.logger.error(f"Error handling customer record: {e}", exc_info=True)
            return None
    
    @staticmethod
    def process_batch_records(session, records):
        """Process multiple biometric records in batch.
        
        Args:
            session: Database session
            records: List of Records objects
            
        Returns:
            dict: Summary with keys:
                - total: Total records processed
                - successful: Number of successful processes
                - failed: Number of failed processes
                - employee_count: Number of employee records
                - customer_count: Number of customer records
                - results: List of individual results
        """
        try:
            summary = {
                'total': len(records),
                'successful': 0,
                'failed': 0,
                'employee_count': 0,
                'customer_count': 0,
                'results': []
            }
            
            for record in records:
                result = UnifiedRecordHandler.handle_device_record(session, record)
                
                if result['success']:
                    summary['successful'] += 1
                    
                    if result['person_type'] == 'employee':
                        summary['employee_count'] += 1
                    elif result['person_type'] == 'customer':
                        summary['customer_count'] += 1
                else:
                    summary['failed'] += 1
                
                summary['results'].append(result)
            
            app.logger.info(f"Batch processing complete: {summary['successful']}/{summary['total']} successful "
                          f"({summary['employee_count']} employees, {summary['customer_count']} customers)")
            
            return summary
        
        except Exception as e:
            app.logger.error(f"Error processing batch records: {e}", exc_info=True)
            return {
                'total': len(records),
                'successful': 0,
                'failed': len(records),
                'employee_count': 0,
                'customer_count': 0,
                'results': [],
                'error': str(e)
            }
    
    @staticmethod
    def get_processing_stats(session, start_date=None, end_date=None):
        """Get statistics on record processing.
        
        Args:
            session: Database session
            start_date: Optional start date filter
            end_date: Optional end date filter
            
        Returns:
            dict: Statistics with employee and customer counts
        """
        try:
            from application.Models.employees.attendance import Attendance
            from application.Models.customers.customer_visit import CustomerVisit
            from datetime import date
            
            if not start_date:
                start_date = date.today()
            if not end_date:
                end_date = date.today()
            
            # Count employee attendance records
            employee_count = session.query(Attendance).filter(
                Attendance.date >= start_date,
                Attendance.date <= end_date
            ).count()
            
            # Count customer visit records
            customer_count = session.query(CustomerVisit).filter(
                CustomerVisit.visit_date >= start_date,
                CustomerVisit.visit_date <= end_date
            ).count()
            
            return {
                'start_date': start_date.strftime('%Y-%m-%d'),
                'end_date': end_date.strftime('%Y-%m-%d'),
                'employee_records': employee_count,
                'customer_records': customer_count,
                'total_records': employee_count + customer_count
            }
        
        except Exception as e:
            app.logger.error(f"Error getting processing stats: {e}")
            return {
                'error': str(e)
            }

