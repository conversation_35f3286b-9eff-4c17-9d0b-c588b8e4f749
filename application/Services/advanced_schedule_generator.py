from datetime import datetime, date, timedelta
from flask import current_app as app
from application.Models.employees.shift_template import ShiftTemplate
from application.Models.employees.shift_schedule import ShiftSchedule
from application.Models.employees.schedule_assignment import ScheduleAssignment
from application.Models.employees.employee import Employee
from application.Models.employees.shift import Shift
import uuid


class AdvancedScheduleGenerator:
    """Service for generating complex shift schedules using rotation templates for any industry."""

    def __init__(self, session):
        self.session = session

    def generate_department_schedule(self, department_id, template_id, start_date, duration_weeks, 
                                   employee_groups=None, generated_by=None):
        """
        Generate a complete schedule for a department using a rotation template.
        
        Args:
            department_id: UUID of the department
            template_id: UUID of the shift template to use
            start_date: Start date for the schedule (date object or string)
            duration_weeks: Number of weeks to generate
            employee_groups: Optional list of employee groups for rotation
            generated_by: UUID of user generating the schedule
            
        Returns:
            dict: Result with success status and schedule details
        """
        try:
            # Get the template
            template = ShiftTemplate.get_by_id(self.session, template_id)
            if not template:
                return {"success": False, "message": "Template not found"}

            # Validate template pattern
            is_valid, validation_message = template.validate_pattern_data()
            if not is_valid:
                return {"success": False, "message": f"Invalid template: {validation_message}"}

            # Convert start_date if it's a string
            if isinstance(start_date, str):
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()

            # Calculate end date
            end_date = start_date + timedelta(weeks=duration_weeks) - timedelta(days=1)

            # Get department employees if groups not provided
            if not employee_groups:
                employee_groups = self._get_default_employee_groups(department_id, template)

            if not employee_groups:
                return {"success": False, "message": "No employees found for department"}

            # Create schedule instance
            schedule_name = f"Schedule - {start_date.strftime('%B %Y')}"
            schedule = ShiftSchedule.create_schedule(self.session, 
                company_id=template.company_id,
                department_id=department_id,
                template_id=template_id,
                schedule_name=schedule_name,
                start_date=start_date,
                end_date=end_date,
                status='draft',
                generated_by=generated_by
            )

            if not schedule:
                return {"success": False, "message": "Failed to create schedule"}

            # Generate assignments for each employee group
            all_assignments = []
            issues = []

            for group_index, employee_group in enumerate(employee_groups):
                try:
                    group_assignments = self._generate_group_rotation(
                        employee_group, template, start_date, duration_weeks, 
                        group_index, schedule.schedule_id
                    )
                    all_assignments.extend(group_assignments)
                except Exception as e:
                    issues.append(f"Error generating assignments for group {group_index}: {str(e)}")

            # Create all assignments in bulk
            if all_assignments:
                created_assignments = ScheduleAssignment.bulk_create(self.session, all_assignments)
                if not created_assignments:
                    issues.append("Failed to create schedule assignments")

            # Validate staffing coverage
            validation_result = self._validate_schedule_coverage(schedule.schedule_id, department_id)
            
            # Update schedule status based on validation
            if validation_result["valid"] and not issues:
                schedule.update_schedule(self.session, schedule.schedule_id, 
                    status='draft_ready_for_review',
                    coverage_analysis=validation_result["coverage_analysis"]
                )
            else:
                all_issues = issues + validation_result.get("issues", [])
                schedule.update_schedule(self.session, schedule.schedule_id,
                    status='draft_needs_attention',
                    issues=all_issues,
                    coverage_analysis=validation_result["coverage_analysis"]
                )

            return {
                "success": True,
                "schedule_id": str(schedule.schedule_id),
                "schedule": schedule.to_dict(),
                "assignments_created": len(all_assignments),
                "validation": validation_result,
                "issues": issues
            }

        except Exception as e:
            app.logger.error(f"Error generating department schedule: {e}")
            return {"success": False, "message": f"Generation failed: {str(e)}"}

    def _get_default_employee_groups(self, department_id, template):
        """Get default employee groups for a department based on template requirements."""
        try:
            # Get all active employees in the department
            # This would need to be implemented based on your Employee model
            # For now, returning empty list - would need actual department employee query
            employees = []  # Employee.get_by_department(self.session, department_id, active_only=True)
            
            if not employees:
                return []

            # Group employees based on template requirements
            group_size = template.employee_group_size or 1
            employee_groups = []
            
            for i in range(0, len(employees), group_size):
                group = employees[i:i + group_size]
                employee_groups.append([emp.employee_id for emp in group])

            return employee_groups

        except Exception as e:
            app.logger.error(f"Error getting default employee groups: {e}")
            return []

    def _generate_group_rotation(self, employee_group, template, start_date, duration_weeks,
                                group_offset, schedule_id):
        """Generate rotation pattern for a specific group of employees."""
        try:
            # Use business requirements if available, otherwise fall back to legacy pattern_data
            if template.business_requirements:
                return self._generate_from_business_requirements(
                    employee_group, template, start_date, duration_weeks, group_offset, schedule_id
                )
            else:
                return self._generate_from_legacy_pattern(
                    employee_group, template, start_date, duration_weeks, group_offset, schedule_id
                )

        except Exception as e:
            app.logger.error(f"Error generating group rotation: {e}")
            return []

    def _generate_from_business_requirements(self, employee_group, template, start_date, duration_weeks,
                                           group_offset, schedule_id):
        """Generate assignments using business requirements with shift_id references."""
        try:
            business_req = template.business_requirements
            shifts_config = business_req.get('shifts', [])

            assignments = []
            current_date = start_date

            for week in range(duration_weeks):
                # For each day of the week
                for day_index in range(7):  # 0=Monday, 6=Sunday
                    assignment_date = current_date + timedelta(days=day_index)
                    day_of_week = assignment_date.weekday() + 1  # Convert to 1=Monday, 7=Sunday

                    # Find shifts that should run on this day
                    for shift_config in shifts_config:
                        days_of_week = shift_config.get('days_of_week', [1, 2, 3, 4, 5, 6, 7])

                        if day_of_week in days_of_week:
                            shift_id = shift_config['shift_id']

                            # Create assignment for each employee in the group
                            for employee_id in employee_group:
                                assignments.append({
                                    "schedule_id": schedule_id,
                                    "employee_id": employee_id,
                                    "date": assignment_date,
                                    "shift_id": shift_id,
                                    "assignment_type": "scheduled",
                                    "status": "assigned"
                                })

                current_date += timedelta(weeks=1)

            return assignments

        except Exception as e:
            app.logger.error(f"Error generating from business requirements: {e}")
            return []

    def _generate_from_legacy_pattern(self, employee_group, template, start_date, duration_weeks,
                                    group_offset, schedule_id):
        """Generate assignments using legacy pattern_data (for backward compatibility)."""
        try:
            pattern_data = template.pattern_data
            cycle_length = pattern_data['cycle_length']
            pattern = pattern_data['pattern']

            assignments = []
            current_date = start_date

            for week in range(duration_weeks):
                # Calculate which week in the rotation cycle for this group
                cycle_week = (week + group_offset) % cycle_length

                if cycle_week < len(pattern):
                    week_pattern = pattern[cycle_week]
                    shifts = week_pattern.get('shifts', [])

                    # Generate assignments for each day of the week
                    for day_index, shift_type in enumerate(shifts):
                        assignment_date = current_date + timedelta(days=day_index)

                        if shift_type != "off":
                            # Get the actual shift ID for this shift type
                            shift = self._get_shift_by_type(shift_type, template.company_id)

                            if shift:
                                # Create assignment for each employee in the group
                                for employee_id in employee_group:
                                    assignments.append({
                                        "schedule_id": schedule_id,
                                        "employee_id": employee_id,
                                        "date": assignment_date,
                                        "shift_id": shift.shift_id,
                                        "assignment_type": "scheduled",
                                        "status": "assigned"
                                    })

                current_date += timedelta(weeks=1)

            return assignments

        except Exception as e:
            app.logger.error(f"Error generating from legacy pattern: {e}")
            return []

    def _get_shift_by_type(self, shift_type, company_id):
        """Get a shift by its type (day, evening, night) for a company."""
        try:
            # This would need to be implemented based on your Shift model
            # For now, returning None - would need actual shift lookup by name/type
            shift = self.session.query(Shift).filter_by(
                company_id=company_id,
                name=shift_type.title() + " Shift",  # "Day Shift", "Evening Shift", etc.
                is_active=True
            ).first()
            
            return shift

        except Exception as e:
            app.logger.error(f"Error getting shift by type {shift_type}: {e}")
            return None

    def _validate_schedule_coverage(self, schedule_id, department_id):
        """Validate that the generated schedule meets staffing requirements."""
        try:
            # Get all assignments for this schedule
            assignments = ScheduleAssignment.get_by_schedule(self.session, schedule_id)
            
            # Group assignments by date and shift
            coverage_by_date_shift = {}
            for assignment in assignments:
                date_key = assignment.date.strftime('%Y-%m-%d')
                shift_key = str(assignment.shift_id)
                
                if date_key not in coverage_by_date_shift:
                    coverage_by_date_shift[date_key] = {}
                
                if shift_key not in coverage_by_date_shift[date_key]:
                    coverage_by_date_shift[date_key][shift_key] = []
                
                coverage_by_date_shift[date_key][shift_key].append(assignment)

            # Get staffing requirements for the department
            # This would need to be implemented based on your requirements model
            staffing_requirements = self._get_staffing_requirements(department_id)
            
            issues = []
            coverage_stats = {
                "total_shifts": 0,
                "adequately_staffed": 0,
                "understaffed": 0,
                "overstaffed": 0
            }

            # Validate each date/shift combination
            for date_key, shifts in coverage_by_date_shift.items():
                for shift_key, shift_assignments in shifts.items():
                    coverage_stats["total_shifts"] += 1
                    assigned_count = len(shift_assignments)
                    
                    # Get required count for this shift type
                    required_count = self._get_required_staff_count(shift_key, staffing_requirements)
                    
                    if assigned_count < required_count:
                        coverage_stats["understaffed"] += 1
                        issues.append({
                            "date": date_key,
                            "shift_id": shift_key,
                            "type": "understaffed",
                            "required": required_count,
                            "assigned": assigned_count,
                            "shortage": required_count - assigned_count
                        })
                    elif assigned_count > required_count * 1.2:  # 20% overstaffing threshold
                        coverage_stats["overstaffed"] += 1
                        issues.append({
                            "date": date_key,
                            "shift_id": shift_key,
                            "type": "overstaffed",
                            "required": required_count,
                            "assigned": assigned_count,
                            "excess": assigned_count - required_count
                        })
                    else:
                        coverage_stats["adequately_staffed"] += 1

            # Calculate coverage percentage
            if coverage_stats["total_shifts"] > 0:
                coverage_percentage = (coverage_stats["adequately_staffed"] / coverage_stats["total_shifts"]) * 100
            else:
                coverage_percentage = 0

            return {
                "valid": len(issues) == 0,
                "issues": issues,
                "coverage_analysis": {
                    "coverage_percentage": round(coverage_percentage, 2),
                    "stats": coverage_stats,
                    "total_assignments": len(assignments)
                }
            }

        except Exception as e:
            app.logger.error(f"Error validating schedule coverage: {e}")
            return {
                "valid": False,
                "issues": [f"Validation error: {str(e)}"],
                "coverage_analysis": {}
            }

    def _get_staffing_requirements(self, department_id):
        """Get staffing requirements for a department."""
        # This would be implemented based on your staffing requirements model
        # For now, returning default requirements
        return {
            "day_shift": {"minimum": 5, "optimal": 7},
            "evening_shift": {"minimum": 4, "optimal": 6},
            "night_shift": {"minimum": 3, "optimal": 4}
        }

    def _get_required_staff_count(self, shift_id, staffing_requirements):
        """Get required staff count for a specific shift."""
        # This would map shift_id to shift type and return requirement
        # For now, returning default minimum
        return 3

    def regenerate_schedule(self, schedule_id, **kwargs):
        """Regenerate a schedule with new parameters."""
        try:
            schedule = ShiftSchedule.get_by_id(self.session, schedule_id)
            if not schedule:
                return {"success": False, "message": "Schedule not found"}

            if not schedule.is_editable():
                return {"success": False, "message": "Schedule cannot be regenerated"}

            # Delete existing assignments
            existing_assignments = ScheduleAssignment.get_by_schedule(self.session, schedule_id)
            for assignment in existing_assignments:
                self.session.delete(assignment)

            # Regenerate with new parameters
            duration_weeks = schedule.get_duration_weeks()
            result = self.generate_department_schedule(
                department_id=schedule.department_id,
                template_id=schedule.template_id,
                start_date=schedule.start_date,
                duration_weeks=duration_weeks,
                **kwargs
            )

            return result

        except Exception as e:
            app.logger.error(f"Error regenerating schedule: {e}")
            return {"success": False, "message": f"Regeneration failed: {str(e)}"}

    def get_schedule_preview(self, template_id, start_date, duration_weeks, employee_count=10):
        """Generate a preview of what a schedule would look like without saving."""
        try:
            template = ShiftTemplate.get_by_id(self.session, template_id)
            if not template:
                return {"success": False, "message": "Template not found"}

            # Create mock employee groups
            mock_groups = []
            group_size = template.employee_group_size or 1
            for i in range(0, employee_count, group_size):
                group = [f"employee_{j}" for j in range(i, min(i + group_size, employee_count))]
                mock_groups.append(group)

            # Generate preview assignments (without saving)
            preview_assignments = []
            for group_index, employee_group in enumerate(mock_groups):
                group_assignments = self._generate_group_rotation(
                    employee_group, template, start_date, duration_weeks, 
                    group_index, "preview"
                )
                preview_assignments.extend(group_assignments)

            return {
                "success": True,
                "template": template.to_dict(),
                "preview_assignments": preview_assignments,
                "total_assignments": len(preview_assignments),
                "employee_groups": len(mock_groups)
            }

        except Exception as e:
            app.logger.error(f"Error generating schedule preview: {e}")
            return {"success": False, "message": f"Preview failed: {str(e)}"}


class AutoScheduleService:
    """Service for automatic monthly schedule generation."""

    def __init__(self, session):
        self.session = session

    def generate_monthly_schedules(self, target_month=None):
        """
        Automatically generate schedules for all departments with auto-generation enabled.
        This would typically run as a scheduled job on the 15th of each month.
        """
        try:
            if not target_month:
                # Generate for next month
                today = date.today()
                if today.month == 12:
                    target_month = date(today.year + 1, 1, 1)
                else:
                    target_month = date(today.year, today.month + 1, 1)

            # Get all departments with auto-generation enabled
            departments = self._get_auto_schedule_departments()

            results = {
                "target_month": target_month.strftime('%Y-%m'),
                "departments_processed": 0,
                "successful_generations": 0,
                "failed_generations": 0,
                "results": []
            }

            generator = AdvancedScheduleGenerator(self.session)

            for department in departments:
                try:
                    # Calculate schedule duration (4-5 weeks depending on month)
                    month_end = self._get_month_end(target_month)
                    duration_weeks = ((month_end - target_month).days + 1) // 7 + 1

                    # Generate schedule for this department
                    result = generator.generate_department_schedule(
                        department_id=department["department_id"],
                        template_id=department["template_id"],
                        start_date=target_month,
                        duration_weeks=duration_weeks,
                        generated_by=None  # System generated
                    )

                    results["departments_processed"] += 1

                    if result["success"]:
                        results["successful_generations"] += 1

                        # Send notification to HR for review
                        self._notify_hr_for_review(department, result["schedule"])
                    else:
                        results["failed_generations"] += 1

                        # Send notification about failure
                        self._notify_hr_of_failure(department, result["message"])

                    results["results"].append({
                        "department": department,
                        "success": result["success"],
                        "schedule_id": result.get("schedule_id"),
                        "message": result.get("message", "Generated successfully")
                    })

                except Exception as e:
                    results["failed_generations"] += 1
                    results["results"].append({
                        "department": department,
                        "success": False,
                        "message": f"Exception: {str(e)}"
                    })
                    app.logger.error(f"Error generating schedule for department {department['department_id']}: {e}")

            app.logger.info(f"Auto-generation completed: {results['successful_generations']}/{results['departments_processed']} successful")
            return results

        except Exception as e:
            app.logger.error(f"Error in auto schedule generation: {e}")
            return {"success": False, "message": str(e)}

    def _get_auto_schedule_departments(self):
        """Get departments that have auto-generation enabled."""
        # This would query your department configuration
        # For now, returning empty list - would need actual department config query
        return []

    def _get_month_end(self, month_start):
        """Get the last day of the month."""
        if month_start.month == 12:
            next_month = date(month_start.year + 1, 1, 1)
        else:
            next_month = date(month_start.year, month_start.month + 1, 1)

        return next_month - timedelta(days=1)

    def _notify_hr_for_review(self, department, schedule):
        """Send notification to HR that a schedule is ready for review."""
        # This would integrate with your notification system
        app.logger.info(f"Schedule ready for review: {schedule['schedule_name']} for department {department['name']}")

    def _notify_hr_of_failure(self, department, error_message):
        """Send notification to HR about schedule generation failure."""
        # This would integrate with your notification system
        app.logger.error(f"Schedule generation failed for department {department['name']}: {error_message}")
