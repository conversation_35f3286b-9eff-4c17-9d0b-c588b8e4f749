import hashlib
import json
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from flask import current_app as app
from application.Models.ai.ai_insight_response import AIInsightResponse

class AICacheService:
    """Service for caching AI insights to reduce API calls and improve performance"""
    
    @classmethod
    def generate_cache_key(cls, prompt: str, company_id: str, insight_type: str, 
                          additional_params: Optional[Dict[str, Any]] = None) -> str:
        """
        Generate a unique cache key for an AI insight request.
        
        Args:
            prompt: The AI prompt
            company_id: Company identifier
            insight_type: Type of insight being generated
            additional_params: Additional parameters that affect the result
            
        Returns:
            Unique cache key string
        """
        try:
            # Create a dictionary of all parameters that affect the result
            cache_data = {
                'prompt': prompt,
                'company_id': company_id,
                'insight_type': insight_type
            }
            
            # Add additional parameters if provided
            if additional_params:
                cache_data.update(additional_params)
            
            # Convert to JSON string for consistent hashing
            cache_string = json.dumps(cache_data, sort_keys=True)
            
            # Generate MD5 hash
            cache_key = hashlib.md5(cache_string.encode()).hexdigest()
            
            # Add prefix for easy identification
            return f"ai_insight_{insight_type}_{cache_key}"
            
        except Exception as e:
            app.logger.error(f"Error generating cache key: {e}")
            # Fallback to simple key
            return f"ai_insight_{insight_type}_{company_id}_{hash(prompt) % 1000000}"

    @classmethod
    def get_cached_insight(cls, session, cache_key: str) -> Optional[AIInsightResponse]:
        """
        Retrieve a cached insight if it exists and is still valid.
        
        Args:
            session: Database session
            cache_key: The cache key to look up
            
        Returns:
            Cached insight response or None if not found/expired
        """
        try:
            insight = AIInsightResponse.get_cached_insight(session, cache_key)
            
            if insight and not insight.is_expired():
                app.logger.info(f"Cache hit for key: {cache_key}")
                return insight
            elif insight and insight.is_expired():
                app.logger.info(f"Cache expired for key: {cache_key}")
                # Mark as archived instead of deleting
                insight.is_archived = True
                session.commit()
            
            return None
            
        except Exception as e:
            app.logger.error(f"Error retrieving cached insight: {e}")
            return None

    @classmethod
    def cache_insight(cls, session, insight_response: AIInsightResponse, 
                     cache_key: str, ttl_hours: int = 24) -> bool:
        """
        Cache an AI insight response.
        
        Args:
            session: Database session
            insight_response: The insight response to cache
            cache_key: Cache key for the insight
            ttl_hours: Time to live in hours
            
        Returns:
            Boolean indicating if caching was successful
        """
        try:
            # Set cache properties
            insight_response.cache_key = cache_key
            insight_response.is_cached = True
            insight_response.expires_at = datetime.utcnow() + timedelta(hours=ttl_hours)
            
            session.commit()
            app.logger.info(f"Cached insight with key: {cache_key}, TTL: {ttl_hours} hours")
            return True
            
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error caching insight: {e}")
            return False

    @classmethod
    def invalidate_cache(cls, session, pattern: str = None, company_id: str = None, 
                        insight_type: str = None) -> int:
        """
        Invalidate cached insights based on pattern or criteria.
        
        Args:
            session: Database session
            pattern: Cache key pattern to match
            company_id: Company ID to invalidate cache for
            insight_type: Insight type to invalidate
            
        Returns:
            Number of cache entries invalidated
        """
        try:
            query = session.query(AIInsightResponse).filter(
                AIInsightResponse.is_cached == True,
                AIInsightResponse.is_archived == False
            )
            
            # Filter by pattern if provided
            if pattern:
                query = query.filter(AIInsightResponse.cache_key.like(f"%{pattern}%"))
            
            # Filter by company if provided
            if company_id:
                from application.Models.ai.ai_insight_request import AIInsightRequest
                query = query.join(AIInsightRequest).filter(
                    AIInsightRequest.company_id == company_id
                )
            
            # Filter by insight type if provided
            if insight_type:
                query = query.filter(AIInsightResponse.insight_type == insight_type)
            
            # Get insights to invalidate
            insights_to_invalidate = query.all()
            
            # Mark as archived (soft delete)
            count = 0
            for insight in insights_to_invalidate:
                insight.is_archived = True
                insight.expires_at = datetime.utcnow()  # Expire immediately
                count += 1
            
            session.commit()
            app.logger.info(f"Invalidated {count} cached insights")
            return count
            
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error invalidating cache: {e}")
            return 0

    @classmethod
    def cleanup_expired_cache(cls, session) -> int:
        """
        Clean up expired cache entries.
        
        Args:
            session: Database session
            
        Returns:
            Number of expired entries cleaned up
        """
        try:
            # Find expired cached insights
            expired_insights = session.query(AIInsightResponse).filter(
                AIInsightResponse.is_cached == True,
                AIInsightResponse.expires_at < datetime.utcnow(),
                AIInsightResponse.is_archived == False
            ).all()
            
            # Mark as archived
            count = 0
            for insight in expired_insights:
                insight.is_archived = True
                count += 1
            
            session.commit()
            app.logger.info(f"Cleaned up {count} expired cache entries")
            return count
            
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error cleaning up expired cache: {e}")
            return 0

    @classmethod
    def get_cache_stats(cls, session, company_id: str = None) -> Dict[str, Any]:
        """
        Get cache statistics.
        
        Args:
            session: Database session
            company_id: Optional company ID to filter stats
            
        Returns:
            Dictionary with cache statistics
        """
        try:
            query = session.query(AIInsightResponse).filter(
                AIInsightResponse.is_cached == True
            )
            
            if company_id:
                from application.Models.ai.ai_insight_request import AIInsightRequest
                query = query.join(AIInsightRequest).filter(
                    AIInsightRequest.company_id == company_id
                )
            
            all_cached = query.all()
            
            # Calculate statistics
            total_cached = len(all_cached)
            active_cached = len([i for i in all_cached if not i.is_archived and not i.is_expired()])
            expired_cached = len([i for i in all_cached if i.is_expired() and not i.is_archived])
            archived_cached = len([i for i in all_cached if i.is_archived])
            
            # Cache hit rate (based on view count vs creation)
            total_views = sum([i.view_count for i in all_cached])
            cache_hit_rate = (total_views / max(total_cached, 1)) if total_cached > 0 else 0
            
            # Most popular insight types
            insight_types = {}
            for insight in all_cached:
                insight_type = insight.insight_type
                if insight_type not in insight_types:
                    insight_types[insight_type] = 0
                insight_types[insight_type] += 1
            
            return {
                'total_cached_insights': total_cached,
                'active_cached_insights': active_cached,
                'expired_cached_insights': expired_cached,
                'archived_cached_insights': archived_cached,
                'cache_hit_rate': round(cache_hit_rate, 2),
                'insight_types_distribution': insight_types,
                'cache_efficiency': round((active_cached / max(total_cached, 1)) * 100, 2)
            }
            
        except Exception as e:
            app.logger.error(f"Error getting cache stats: {e}")
            return {}

    @classmethod
    def should_cache_insight(cls, insight_type: str, company_config: Dict[str, Any] = None) -> bool:
        """
        Determine if an insight should be cached based on type and configuration.
        
        Args:
            insight_type: Type of insight
            company_config: Company configuration
            
        Returns:
            Boolean indicating if insight should be cached
        """
        try:
            # Check if caching is enabled in company config
            if company_config and not company_config.get('enable_caching', True):
                return False
            
            # Define cacheable insight types
            cacheable_types = [
                'daily_summary',
                'weekly_summary',
                'monthly_summary',
                'annual_summary',
                'custom_summary',
                'trend_analysis',
                'department_analysis',
                'risk_assessment',
                'recommendations'
            ]
            
            # Don't cache real-time or highly dynamic insights
            non_cacheable_types = [
                'real_time_alert',
                'urgent_notification',
                'live_dashboard'
            ]
            
            if insight_type in non_cacheable_types:
                return False
            
            if insight_type in cacheable_types:
                return True
            
            # Default to caching for unknown types
            return True
            
        except Exception as e:
            app.logger.error(f"Error determining cache eligibility: {e}")
            return True  # Default to caching

    @classmethod
    def get_cache_ttl(cls, insight_type: str, company_config: Dict[str, Any] = None) -> int:
        """
        Get appropriate TTL (time to live) for an insight type.
        
        Args:
            insight_type: Type of insight
            company_config: Company configuration
            
        Returns:
            TTL in hours
        """
        try:
            # Default TTL from company config
            default_ttl = 24
            if company_config:
                default_ttl = company_config.get('cache_duration_hours', 24)
            
            # Type-specific TTL rules
            ttl_rules = {
                'daily_summary': 24,      # Cache for 1 day
                'weekly_summary': 168,    # Cache for 1 week
                'monthly_summary': 720,   # Cache for 1 month
                'trend_analysis': 72,     # Cache for 3 days
                'risk_assessment': 48,    # Cache for 2 days
                'executive_summary': 168, # Cache for 1 week
                'announcement': 12,       # Cache for 12 hours
                'policy_reminder': 72     # Cache for 3 days
            }
            
            return ttl_rules.get(insight_type, default_ttl)
            
        except Exception as e:
            app.logger.error(f"Error getting cache TTL: {e}")
            return 24  # Default to 24 hours
