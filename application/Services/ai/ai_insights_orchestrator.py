import time
import json
from typing import Dict, Any, Optional, List
from datetime import datetime
from flask import current_app as app

from application.Services.ai.ai_service_factory import AIServiceFactory
from application.Services.ai.prompt_builder import PromptBuilder
from application.Services.ai.ai_cache_service import AICacheService
from application.Models.ai.ai_provider import AIProvider
from application.Models.ai.ai_insight_template import AIIns<PERSON>Template
from application.Models.ai.ai_insight_request import AIInsightRequest
from application.Models.ai.ai_insight_response import AIInsightResponse
from application.Models.ai.ai_company_config import AICompanyConfig

class AIInsightsOrchestrator:
    """Main orchestrator for AI insights generation and management"""
    
    def __init__(self, session, company_id: str):
        """
        Initialize the orchestrator for a specific company.
        
        Args:
            session: Database session
            company_id: Company identifier
        """
        self.session = session
        self.company_id = company_id
        self.company_config = AICompanyConfig.get_config_by_company(session, company_id)
        
    def generate_attendance_insights(self, data: Dict[str, Any], 
                                   insight_type: str = 'daily_summary',
                                   user_id: Optional[str] = None,
                                   force_regenerate: bool = False) -> Dict[str, Any]:
        """
        Generate attendance-related AI insights.
        
        Args:
            data: Attendance data and context
            insight_type: Type of insight to generate
            user_id: User requesting the insight
            force_regenerate: Force regeneration even if cached version exists
            
        Returns:
            Dictionary containing the generated insight and metadata
        """
        try:
            # Check if this insight type is enabled for the company
            if not self.company_config.is_insight_type_enabled(insight_type):
                return {
                    'success': False,
                    'error': f'Insight type "{insight_type}" is not enabled for this company'
                }
            
            # Build the prompt
            company_context = self.company_config.get_context_for_prompts()
            prompt = PromptBuilder.build_attendance_prompt(data, company_context, insight_type)
            
            # Check cache first (unless force regenerate)
            if not force_regenerate and self.company_config.enable_caching:
                cache_key = AICacheService.generate_cache_key(
                    prompt, self.company_id, insight_type, 
                    {'data_hash': hash(str(data))}
                )
                
                cached_insight = AICacheService.get_cached_insight(self.session, cache_key)
                if cached_insight:
                    # Record view and return cached result
                    cached_insight.record_view(self.session, user_id)
                    return {
                        'success': True,
                        'insight': cached_insight.to_dict(),
                        'cached': True,
                        'cache_key': cache_key
                    }
            
            # Generate new insight
            return self._generate_new_insight(
                prompt=prompt,
                insight_type=insight_type,
                category='attendance',
                user_id=user_id,
                data=data
            )
            
        except Exception as e:
            app.logger.error(f"Error generating attendance insights: {e}")
            return {
                'success': False,
                'error': f'Error generating attendance insights: {str(e)}'
            }

    def generate_smart_announcements(self, data: Dict[str, Any],
                                   announcement_type: str = 'weekly_summary',
                                   user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate smart announcements based on data insights.
        
        Args:
            data: Data for announcement generation
            announcement_type: Type of announcement to generate
            user_id: User requesting the announcement
            
        Returns:
            Dictionary containing the generated announcement
        """
        try:
            # Check if smart announcements are enabled
            if not self.company_config.enable_smart_announcements:
                return {
                    'success': False,
                    'error': 'Smart announcements are not enabled for this company'
                }
            
            # Build the prompt
            company_context = self.company_config.get_context_for_prompts()
            prompt = PromptBuilder.build_announcement_prompt(data, company_context, announcement_type)
            
            # Generate announcement
            return self._generate_new_insight(
                prompt=prompt,
                insight_type=announcement_type,
                category='announcements',
                user_id=user_id,
                data=data
            )
            
        except Exception as e:
            app.logger.error(f"Error generating smart announcements: {e}")
            return {
                'success': False,
                'error': f'Error generating smart announcements: {str(e)}'
            }

    def generate_executive_summary(self, data: Dict[str, Any],
                                 executive_type: str = 'monthly_summary',
                                 user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate executive-level insights and summaries.
        
        Args:
            data: Executive data and metrics
            executive_type: Type of executive insight to generate
            user_id: User requesting the insight
            
        Returns:
            Dictionary containing the generated executive insight
        """
        try:
            # Check if executive summaries are enabled
            if not self.company_config.enable_executive_summaries:
                return {
                    'success': False,
                    'error': 'Executive summaries are not enabled for this company'
                }
            
            # Build the prompt
            company_context = self.company_config.get_context_for_prompts()
            prompt = PromptBuilder.build_executive_prompt(data, company_context, executive_type)
            
            # Generate executive insight
            return self._generate_new_insight(
                prompt=prompt,
                insight_type=executive_type,
                category='executive',
                user_id=user_id,
                data=data,
                priority='high'  # Executive insights are high priority
            )
            
        except Exception as e:
            app.logger.error(f"Error generating executive summary: {e}")
            return {
                'success': False,
                'error': f'Error generating executive summary: {str(e)}'
            }

    def generate_custom_insight(self, prompt: str, insight_type: str,
                              category: str = 'custom', user_id: Optional[str] = None,
                              template_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate a custom insight with a user-provided prompt.
        
        Args:
            prompt: Custom prompt for insight generation
            insight_type: Type identifier for the insight
            category: Category of the insight
            user_id: User requesting the insight
            template_id: Optional template ID if using a custom template
            
        Returns:
            Dictionary containing the generated insight
        """
        try:
            # Add company context to custom prompt
            company_context = self.company_config.get_context_for_prompts()
            if company_context:
                prompt = f"{company_context}\n\n{prompt}"
            
            # Generate custom insight
            return self._generate_new_insight(
                prompt=prompt,
                insight_type=insight_type,
                category=category,
                user_id=user_id,
                template_id=template_id
            )
            
        except Exception as e:
            app.logger.error(f"Error generating custom insight: {e}")
            return {
                'success': False,
                'error': f'Error generating custom insight: {str(e)}'
            }

    def _generate_new_insight(self, prompt: str, insight_type: str, category: str,
                            user_id: Optional[str] = None, data: Optional[Dict[str, Any]] = None,
                            template_id: Optional[str] = None, priority: str = 'medium') -> Dict[str, Any]:
        """
        Generate a new AI insight (internal method).
        
        Args:
            prompt: The prompt to send to AI
            insight_type: Type of insight
            category: Category of insight
            user_id: User requesting the insight
            data: Optional data context
            template_id: Optional template ID
            priority: Priority level for the insight
            
        Returns:
            Dictionary containing the generated insight and metadata
        """
        try:
            # Get AI service and provider
            ai_service = AIServiceFactory.get_default_service(self.session)
            if not ai_service:
                return {
                    'success': False,
                    'error': 'No AI service available'
                }

            # Get the actual provider from database to get provider_id
            from application.Models.ai.ai_provider import AIProvider
            provider = AIProvider.get_default_provider(self.session, 'text_generation')
            if not provider:
                return {
                    'success': False,
                    'error': 'No AI provider found in database'
                }

            # Check rate limits
            rate_limit_check = ai_service.check_rate_limits(self.company_id)
            if not rate_limit_check.get('can_proceed', True):
                return {
                    'success': False,
                    'error': 'Rate limit exceeded',
                    'rate_limit_info': rate_limit_check
                }

            # Create request record
            request_record = AIInsightRequest.create_request(
                self.session,
                company_id=self.company_id,
                user_id=user_id,
                module=category,
                insight_type=insight_type,
                provider_id=str(provider.provider_id),
                template_id=template_id,
                prompt_used=prompt,
                input_data=json.dumps(data) if data else None,
                context_data=json.dumps(self.company_config.to_dict()),
                max_tokens=self.company_config.max_tokens_per_request,
                temperature=0.7,
                status='pending'
            )
            
            if not request_record:
                return {
                    'success': False,
                    'error': 'Failed to create request record'
                }
            
            # Generate insight using AI service
            start_time = time.time()
            ai_response = ai_service.generate_insight(
                prompt=prompt,
                max_tokens=self.company_config.max_tokens_per_request,
                temperature=0.7
            )
            processing_time = time.time() - start_time
            
            # Update request record with response info
            if ai_response.get('success'):
                AIInsightRequest.update_request_status(
                    self.session,
                    request_record.request_id,
                    'completed',
                    tokens_used=ai_response.get('tokens_used', 0),
                    estimated_cost=ai_response.get('cost', 0.0)
                )
                
                # Extract structured data from response
                content = ai_response.get('content', '')
                key_metrics = ai_service.extract_key_metrics(content)
                recommendations = ai_service.extract_recommendations(content)
                alerts = ai_service.extract_alerts(content)
                determined_priority = ai_service.determine_priority(content, alerts, recommendations)
                
                # Create insight response record
                response_record = AIInsightResponse.create_response(
                    self.session,
                    request_id=request_record.request_id,
                    insight_content=content,
                    insight_summary=content[:200] + '...' if len(content) > 200 else content,
                    insight_type=insight_type,
                    confidence_score=0.8,  # Could be enhanced with actual confidence scoring
                    category=category,
                    priority=determined_priority,
                    key_metrics=json.dumps(key_metrics),
                    recommendations=json.dumps(recommendations),
                    alerts=json.dumps(alerts)
                )
                
                # Cache the insight if appropriate
                if (self.company_config.enable_caching and 
                    AICacheService.should_cache_insight(insight_type, self.company_config.to_dict())):
                    
                    cache_key = AICacheService.generate_cache_key(
                        prompt, self.company_id, insight_type,
                        {'data_hash': hash(str(data)) if data else 0}
                    )
                    
                    ttl_hours = AICacheService.get_cache_ttl(insight_type, self.company_config.to_dict())
                    AICacheService.cache_insight(self.session, response_record, cache_key, ttl_hours)
                
                # Record view for the requesting user
                if user_id:
                    response_record.record_view(self.session, user_id)
                
                return {
                    'success': True,
                    'insight': response_record.to_dict(),
                    'cached': False,
                    'processing_time': processing_time,
                    'tokens_used': ai_response.get('tokens_used', 0),
                    'cost': ai_response.get('cost', 0.0)
                }
                
            else:
                # Update request record with error
                AIInsightRequest.update_request_status(
                    self.session,
                    request_record.request_id,
                    'failed',
                    error_message=ai_response.get('error', 'Unknown error')
                )
                
                return {
                    'success': False,
                    'error': ai_response.get('error', 'AI service failed to generate insight'),
                    'processing_time': processing_time
                }
                
        except Exception as e:
            app.logger.error(f"Error in _generate_new_insight: {e}")
            return {
                'success': False,
                'error': f'Internal error generating insight: {str(e)}'
            }

    def get_recent_insights(self, category: Optional[str] = None, 
                          limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get recent insights for the company.
        
        Args:
            category: Optional category filter
            limit: Maximum number of insights to return
            
        Returns:
            List of recent insights
        """
        try:
            insights = AIInsightResponse.get_active_insights(
                self.session, 
                category=category, 
                company_id=self.company_id, 
                limit=limit
            )
            
            return [insight.to_dict(include_content=False) for insight in insights]
            
        except Exception as e:
            app.logger.error(f"Error getting recent insights: {e}")
            return []

    def get_insight_by_id(self, response_id: str, user_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Get a specific insight by ID.
        
        Args:
            response_id: The insight response ID
            user_id: User requesting the insight (for view tracking)
            
        Returns:
            Insight dictionary or None if not found
        """
        try:
            insight = self.session.query(AIInsightResponse).filter_by(
                response_id=response_id,
                is_active=True
            ).first()
            
            if insight:
                # Record view
                if user_id:
                    insight.record_view(self.session, user_id)
                
                return insight.to_dict()
            
            return None
            
        except Exception as e:
            app.logger.error(f"Error getting insight by ID: {e}")
            return None
