from typing import Dict, Any, Optional
from flask import current_app as app
from application.Services.ai.base_ai_service import BaseAIService
from application.Services.ai.gemini_service import GeminiAIService
from application.Models.ai.ai_provider import AIProvider

class AIServiceFactory:
    """Factory class for creating AI service instances"""
    
    # Registry of available AI service implementations
    _service_registry = {
        'gemini': GeminiAIService,
        'google': GeminiAIService,  # Alias for gemini
        # Future providers can be added here:
        # 'openai': OpenAIService,
        # 'claude': ClaudeAIService,
        # 'azure': AzureOpenAIService,
    }

    @classmethod
    def get_service(cls, provider_name: str, provider_config: Optional[Dict[str, Any]] = None) -> Optional[BaseAIService]:
        """
        Get an AI service instance for the specified provider.
        
        Args:
            provider_name: Name of the AI provider (e.g., 'gemini', 'openai')
            provider_config: Optional provider configuration dictionary
            
        Returns:
            AI service instance or None if provider not found
        """
        try:
            # Normalize provider name
            provider_name = provider_name.lower().strip()
            
            # Check if provider is registered
            if provider_name not in cls._service_registry:
                app.logger.error(f"AI provider '{provider_name}' not found in registry")
                return None
            
            # Get the service class
            service_class = cls._service_registry[provider_name]
            
            # Use provided config or create default
            if not provider_config:
                provider_config = cls._get_default_config(provider_name)
            
            # Create and return service instance
            service = service_class(provider_config)
            app.logger.info(f"Created AI service for provider: {provider_name}")
            return service
            
        except Exception as e:
            app.logger.error(f"Error creating AI service for provider '{provider_name}': {e}")
            return None

    @classmethod
    def get_service_from_db(cls, session, provider_id: str) -> Optional[BaseAIService]:
        """
        Get an AI service instance from database provider configuration.
        
        Args:
            session: Database session
            provider_id: UUID of the AI provider in database
            
        Returns:
            AI service instance or None if provider not found
        """
        try:
            # Get provider from database
            provider = session.query(AIProvider).filter_by(
                provider_id=provider_id,
                is_active=True
            ).first()
            
            if not provider:
                app.logger.error(f"AI provider with ID '{provider_id}' not found or inactive")
                return None
            
            # Convert provider model to config dictionary
            provider_config = cls._provider_model_to_config(provider)
            
            # Create service instance
            return cls.get_service(provider.name, provider_config)
            
        except Exception as e:
            app.logger.error(f"Error creating AI service from database provider '{provider_id}': {e}")
            return None

    @classmethod
    def get_default_service(cls, session, provider_type: str = 'text_generation') -> Optional[BaseAIService]:
        """
        Get the default AI service for a specific provider type.
        
        Args:
            session: Database session
            provider_type: Type of AI provider (e.g., 'text_generation')
            
        Returns:
            Default AI service instance or None if not found
        """
        try:
            # Get default provider from database
            provider = AIProvider.get_default_provider(session, provider_type)
            
            if not provider:
                app.logger.warning(f"No default AI provider found for type '{provider_type}', using fallback")
                # Fallback to Gemini with default config
                return cls.get_service('gemini')
            
            # Create service from database provider
            return cls.get_service_from_db(session, str(provider.provider_id))
            
        except Exception as e:
            app.logger.error(f"Error getting default AI service: {e}")
            # Fallback to Gemini
            return cls.get_service('gemini')

    @classmethod
    def register_provider(cls, provider_name: str, service_class: type):
        """
        Register a new AI provider service class.
        
        Args:
            provider_name: Name of the provider
            service_class: Service class that implements BaseAIService
        """
        try:
            if not issubclass(service_class, BaseAIService):
                raise ValueError(f"Service class must inherit from BaseAIService")
            
            provider_name = provider_name.lower().strip()
            cls._service_registry[provider_name] = service_class
            app.logger.info(f"Registered AI provider: {provider_name}")
            
        except Exception as e:
            app.logger.error(f"Error registering AI provider '{provider_name}': {e}")

    @classmethod
    def get_available_providers(cls) -> list:
        """Get list of available AI providers."""
        return list(cls._service_registry.keys())

    @classmethod
    def _get_default_config(cls, provider_name: str) -> Dict[str, Any]:
        """Get default configuration for a provider."""
        default_configs = {
            'gemini': {
                'name': 'gemini',
                'display_name': 'Google Gemini Flash 2.0',
                'provider_type': 'text_generation',
                'model_name': 'gemini-2.0-flash-exp',
                'max_tokens': 8192,
                'supports_streaming': False,
                'supports_function_calling': True,
                'supports_vision': True,
                'requests_per_minute': 60,
                'requests_per_day': 1500,
                'cost_per_1k_tokens': 0.0,  # Currently free
                'api_endpoint': 'https://generativelanguage.googleapis.com/v1beta',
                'api_version': 'v1beta'
            }
        }
        
        return default_configs.get(provider_name, {
            'name': provider_name,
            'display_name': provider_name.title(),
            'provider_type': 'text_generation',
            'model_name': 'unknown',
            'max_tokens': 2048,
            'requests_per_minute': 60,
            'requests_per_day': 1000,
            'cost_per_1k_tokens': 0.01
        })

    @classmethod
    def _provider_model_to_config(cls, provider: AIProvider) -> Dict[str, Any]:
        """Convert AIProvider model to configuration dictionary."""
        try:
            config = {
                'name': provider.name,
                'display_name': provider.display_name,
                'provider_type': provider.provider_type,
                'model_name': provider.model_name,
                'api_endpoint': provider.api_endpoint,
                'api_version': provider.api_version,
                'max_tokens': provider.max_tokens,
                'supports_streaming': provider.supports_streaming,
                'supports_function_calling': provider.supports_function_calling,
                'supports_vision': provider.supports_vision,
                'requests_per_minute': provider.requests_per_minute,
                'requests_per_day': provider.requests_per_day,
                'cost_per_1k_tokens': float(provider.cost_per_1k_tokens) if provider.cost_per_1k_tokens else 0.0
            }
            
            # Add custom configuration if available
            if provider.configuration:
                try:
                    import json
                    custom_config = json.loads(provider.configuration)
                    config.update(custom_config)
                except json.JSONDecodeError:
                    app.logger.warning(f"Invalid JSON in provider configuration for {provider.name}")
            
            return config
            
        except Exception as e:
            app.logger.error(f"Error converting provider model to config: {e}")
            return cls._get_default_config(provider.name)

    @classmethod
    def test_provider(cls, provider_name: str, provider_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Test an AI provider with a simple request.
        
        Args:
            provider_name: Name of the provider to test
            provider_config: Optional provider configuration
            
        Returns:
            Dictionary with test results
        """
        try:
            # Get service instance
            service = cls.get_service(provider_name, provider_config)
            if not service:
                return {
                    'success': False,
                    'error': f"Could not create service for provider '{provider_name}'"
                }
            
            # Test with a simple prompt
            test_prompt = "Generate a brief test response to confirm the AI service is working correctly."
            
            result = service.generate_insight(
                prompt=test_prompt,
                max_tokens=100,
                temperature=0.5
            )
            
            if result.get('success'):
                return {
                    'success': True,
                    'message': f"Provider '{provider_name}' is working correctly",
                    'test_response': result.get('content', '')[:100] + '...',
                    'tokens_used': result.get('tokens_used', 0),
                    'processing_time': result.get('processing_time', 0)
                }
            else:
                return {
                    'success': False,
                    'error': f"Test failed for provider '{provider_name}': {result.get('error', 'Unknown error')}"
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f"Error testing provider '{provider_name}': {str(e)}"
            }
