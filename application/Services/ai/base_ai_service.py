from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from flask import current_app as app

class BaseAIService(ABC):
    """Abstract base class for AI service implementations"""
    
    def __init__(self, provider_config: Dict[str, Any]):
        """
        Initialize the AI service with provider configuration.
        
        Args:
            provider_config: Dictionary containing provider-specific configuration
        """
        self.provider_config = provider_config
        self.provider_name = provider_config.get('name', 'unknown')
        self.model_name = provider_config.get('model_name', 'unknown')
        self.api_endpoint = provider_config.get('api_endpoint')
        self.max_tokens = provider_config.get('max_tokens', 2048)
        self.requests_per_minute = provider_config.get('requests_per_minute', 60)
        self.requests_per_day = provider_config.get('requests_per_day', 1500)
        self.cost_per_1k_tokens = provider_config.get('cost_per_1k_tokens', 0.0)

    @abstractmethod
    def generate_insight(self, prompt: str, system_prompt: Optional[str] = None, 
                        max_tokens: Optional[int] = None, temperature: float = 0.7,
                        **kwargs) -> Dict[str, Any]:
        """
        Generate an AI insight based on the provided prompt.
        
        Args:
            prompt: The main prompt for the AI
            system_prompt: Optional system prompt for context
            max_tokens: Maximum tokens to generate
            temperature: AI creativity level (0.0 to 1.0)
            **kwargs: Additional provider-specific parameters
            
        Returns:
            Dictionary containing:
            - success: Boolean indicating if the request was successful
            - content: The generated insight content
            - tokens_used: Number of tokens consumed
            - cost: Estimated cost of the request
            - error: Error message if unsuccessful
            - metadata: Additional response metadata
        """
        pass

    @abstractmethod
    def validate_response(self, response: Dict[str, Any]) -> bool:
        """
        Validate the AI response for quality and completeness.
        
        Args:
            response: The AI response to validate
            
        Returns:
            Boolean indicating if the response is valid
        """
        pass

    @abstractmethod
    def calculate_cost(self, tokens_used: int) -> float:
        """
        Calculate the cost of an AI request based on tokens used.
        
        Args:
            tokens_used: Number of tokens consumed
            
        Returns:
            Estimated cost in USD
        """
        pass

    @abstractmethod
    def check_rate_limits(self, company_id: str) -> Dict[str, Any]:
        """
        Check if the company has exceeded rate limits.
        
        Args:
            company_id: The company ID to check limits for
            
        Returns:
            Dictionary containing:
            - can_proceed: Boolean indicating if request can proceed
            - daily_remaining: Remaining daily requests
            - minute_remaining: Remaining requests this minute
            - reset_time: When limits reset
        """
        pass

    def prepare_prompt(self, base_prompt: str, context: str = "", 
                      company_config: Optional[Dict[str, Any]] = None) -> str:
        """
        Prepare the final prompt by adding context and company-specific information.
        
        Args:
            base_prompt: The base prompt template
            context: Additional context to include
            company_config: Company-specific configuration
            
        Returns:
            The final prepared prompt
        """
        try:
            # Start with base prompt
            final_prompt = base_prompt
            
            # Add company context if available
            if company_config:
                company_context = self._build_company_context(company_config)
                if company_context:
                    final_prompt = f"{company_context}\n\n{final_prompt}"
            
            # Add additional context
            if context:
                final_prompt = f"{final_prompt}\n\nAdditional Context: {context}"
            
            return final_prompt
            
        except Exception as e:
            app.logger.error(f"Error preparing prompt: {e}")
            return base_prompt

    def _build_company_context(self, company_config: Dict[str, Any]) -> str:
        """Build company context string for prompts."""
        context_parts = []
        
        # Add company size and industry
        if company_config.get('company_size'):
            context_parts.append(f"Company size: {company_config['company_size']}")
        
        if company_config.get('industry'):
            context_parts.append(f"Industry: {company_config['industry']}")
        
        if company_config.get('business_model'):
            context_parts.append(f"Business model: {company_config['business_model']}")
        
        if company_config.get('primary_location'):
            context_parts.append(f"Location: {company_config['primary_location']}")
        
        # Add custom context
        if company_config.get('custom_context'):
            context_parts.append(company_config['custom_context'])
        
        if context_parts:
            return f"Company Context: {'. '.join(context_parts)}"
        
        return ""

    def extract_key_metrics(self, insight_content: str) -> Dict[str, Any]:
        """
        Extract key metrics from the insight content.
        This is a basic implementation that can be overridden by specific providers.
        
        Args:
            insight_content: The AI-generated insight content
            
        Returns:
            Dictionary of extracted metrics
        """
        metrics = {}
        
        try:
            # Look for percentage patterns
            import re
            percentages = re.findall(r'(\d+(?:\.\d+)?)\s*%', insight_content)
            if percentages:
                metrics['percentages_mentioned'] = [float(p) for p in percentages]
            
            # Look for currency amounts
            currency_amounts = re.findall(r'\$(\d+(?:,\d{3})*(?:\.\d{2})?)', insight_content)
            if currency_amounts:
                metrics['currency_amounts'] = [float(a.replace(',', '')) for a in currency_amounts]
            
            # Look for time periods
            time_periods = re.findall(r'(\d+)\s+(day|week|month|year)s?', insight_content.lower())
            if time_periods:
                metrics['time_periods'] = [{'value': int(t[0]), 'unit': t[1]} for t in time_periods]
            
        except Exception as e:
            app.logger.error(f"Error extracting metrics: {e}")
        
        return metrics

    def extract_recommendations(self, insight_content: str) -> list:
        """
        Extract actionable recommendations from the insight content.
        
        Args:
            insight_content: The AI-generated insight content
            
        Returns:
            List of extracted recommendations
        """
        recommendations = []
        
        try:
            # Look for common recommendation patterns
            import re
            
            # Pattern 1: "Recommend:" or "Recommendation:"
            recommend_pattern = r'(?:recommend|suggestion?)s?:?\s*(.+?)(?:\n|$)'
            matches = re.findall(recommend_pattern, insight_content, re.IGNORECASE | re.MULTILINE)
            recommendations.extend([m.strip() for m in matches if m.strip()])
            
            # Pattern 2: Numbered recommendations
            numbered_pattern = r'\d+\.\s*(.+?)(?:\n|$)'
            matches = re.findall(numbered_pattern, insight_content, re.MULTILINE)
            recommendations.extend([m.strip() for m in matches if m.strip() and len(m) > 10])
            
            # Pattern 3: Bullet points
            bullet_pattern = r'[•\-\*]\s*(.+?)(?:\n|$)'
            matches = re.findall(bullet_pattern, insight_content, re.MULTILINE)
            recommendations.extend([m.strip() for m in matches if m.strip() and len(m) > 10])
            
            # Remove duplicates while preserving order
            seen = set()
            unique_recommendations = []
            for rec in recommendations:
                if rec not in seen:
                    seen.add(rec)
                    unique_recommendations.append(rec)
            
            return unique_recommendations[:5]  # Limit to top 5 recommendations
            
        except Exception as e:
            app.logger.error(f"Error extracting recommendations: {e}")
            return []

    def extract_alerts(self, insight_content: str) -> list:
        """
        Extract alerts or warnings from the insight content.
        
        Args:
            insight_content: The AI-generated insight content
            
        Returns:
            List of extracted alerts
        """
        alerts = []
        
        try:
            import re
            
            # Look for alert keywords
            alert_keywords = [
                'warning', 'alert', 'urgent', 'critical', 'risk', 'concern',
                'issue', 'problem', 'attention', 'caution', 'danger'
            ]
            
            for keyword in alert_keywords:
                pattern = rf'({keyword}[^.!?]*[.!?])'
                matches = re.findall(pattern, insight_content, re.IGNORECASE)
                alerts.extend([m.strip() for m in matches])
            
            # Remove duplicates
            return list(set(alerts))[:3]  # Limit to top 3 alerts
            
        except Exception as e:
            app.logger.error(f"Error extracting alerts: {e}")
            return []

    def determine_priority(self, insight_content: str, alerts: list, 
                          recommendations: list) -> str:
        """
        Determine the priority level of the insight.
        
        Args:
            insight_content: The AI-generated insight content
            alerts: List of extracted alerts
            recommendations: List of extracted recommendations
            
        Returns:
            Priority level: 'low', 'medium', 'high', or 'urgent'
        """
        try:
            # Check for urgent keywords
            urgent_keywords = ['urgent', 'critical', 'immediate', 'emergency', 'crisis']
            high_keywords = ['important', 'significant', 'major', 'serious', 'risk']
            
            content_lower = insight_content.lower()
            
            # Urgent priority
            if any(keyword in content_lower for keyword in urgent_keywords):
                return 'urgent'
            
            # High priority
            if (any(keyword in content_lower for keyword in high_keywords) or 
                len(alerts) > 2 or 
                len(recommendations) > 3):
                return 'high'
            
            # Medium priority
            if len(alerts) > 0 or len(recommendations) > 1:
                return 'medium'
            
            # Default to low priority
            return 'low'
            
        except Exception as e:
            app.logger.error(f"Error determining priority: {e}")
            return 'medium'
