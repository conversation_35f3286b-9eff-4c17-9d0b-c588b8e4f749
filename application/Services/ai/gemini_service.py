import os
import time
import json
import requests
from typing import Dict, Any, Optional
from flask import current_app as app
from datetime import datetime, timedelta
from application.Services.ai.base_ai_service import BaseAIService

class GeminiAIService(BaseAIService):
    """Google Gemini AI service implementation"""
    
    def __init__(self, provider_config: Dict[str, Any]):
        super().__init__(provider_config)
        self.api_key = os.getenv('GEMINI_API_KEY')
        self.base_url = "https://generativelanguage.googleapis.com/v1beta"
        self.model_name = provider_config.get('model_name', 'gemini-2.0-flash-exp')
        
        # Rate limiting tracking (in-memory for now, could be moved to Redis)
        self._rate_limit_tracker = {}

    def generate_insight(self, prompt: str, system_prompt: Optional[str] = None, 
                        max_tokens: Optional[int] = None, temperature: float = 0.7,
                        **kwargs) -> Dict[str, Any]:
        """Generate an AI insight using Google Gemini."""
        start_time = time.time()
        
        try:
            if not self.api_key:
                return {
                    'success': False,
                    'error': 'Gemini API key not configured',
                    'content': '',
                    'tokens_used': 0,
                    'cost': 0.0
                }

            # Prepare the request
            url = f"{self.base_url}/models/{self.model_name}:generateContent"
            
            # Build the content parts
            parts = []
            
            # Add system prompt if provided
            if system_prompt:
                parts.append({"text": f"System: {system_prompt}\n\n"})
            
            # Add main prompt
            parts.append({"text": prompt})
            
            # Prepare request payload
            payload = {
                "contents": [{
                    "parts": parts
                }],
                "generationConfig": {
                    "temperature": temperature,
                    "maxOutputTokens": max_tokens or self.max_tokens,
                    "topP": 0.8,
                    "topK": 10
                }
            }
            
            headers = {
                "Content-Type": "application/json"
            }
            
            # Add API key to URL
            url_with_key = f"{url}?key={self.api_key}"
            
            # Make the request
            response = requests.post(url_with_key, headers=headers, json=payload, timeout=30)
            
            processing_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                
                # Extract content from Gemini response
                content = ""
                tokens_used = 0
                
                if 'candidates' in result and len(result['candidates']) > 0:
                    candidate = result['candidates'][0]
                    if 'content' in candidate and 'parts' in candidate['content']:
                        content = candidate['content']['parts'][0].get('text', '')
                
                # Estimate tokens (Gemini doesn't always return token count)
                if 'usageMetadata' in result:
                    tokens_used = result['usageMetadata'].get('totalTokenCount', 0)
                else:
                    # Rough estimation: 1 token ≈ 4 characters
                    tokens_used = len(content) // 4
                
                # Calculate cost
                cost = self.calculate_cost(tokens_used)
                
                # Validate response
                if not self.validate_response({'content': content, 'tokens_used': tokens_used}):
                    return {
                        'success': False,
                        'error': 'Generated content failed validation',
                        'content': content,
                        'tokens_used': tokens_used,
                        'cost': cost,
                        'processing_time': processing_time
                    }
                
                return {
                    'success': True,
                    'content': content,
                    'tokens_used': tokens_used,
                    'cost': cost,
                    'processing_time': processing_time,
                    'metadata': {
                        'model': self.model_name,
                        'temperature': temperature,
                        'max_tokens': max_tokens or self.max_tokens,
                        'raw_response': result
                    }
                }
            
            else:
                error_msg = f"Gemini API error: {response.status_code} - {response.text}"
                app.logger.error(error_msg)
                
                return {
                    'success': False,
                    'error': error_msg,
                    'content': '',
                    'tokens_used': 0,
                    'cost': 0.0,
                    'processing_time': processing_time
                }
                
        except requests.exceptions.Timeout:
            return {
                'success': False,
                'error': 'Request timeout - Gemini API took too long to respond',
                'content': '',
                'tokens_used': 0,
                'cost': 0.0,
                'processing_time': time.time() - start_time
            }
            
        except Exception as e:
            error_msg = f"Error calling Gemini API: {str(e)}"
            app.logger.error(error_msg)
            
            return {
                'success': False,
                'error': error_msg,
                'content': '',
                'tokens_used': 0,
                'cost': 0.0,
                'processing_time': time.time() - start_time
            }

    def validate_response(self, response: Dict[str, Any]) -> bool:
        """Validate the Gemini response for quality and completeness."""
        try:
            content = response.get('content', '')
            tokens_used = response.get('tokens_used', 0)
            
            # Basic validation checks
            if not content or len(content.strip()) < 10:
                app.logger.warning("Gemini response too short or empty")
                return False
            
            if tokens_used == 0:
                app.logger.warning("Gemini response has no token usage")
                # Don't fail validation for this, just log it
            
            # Check for common error patterns in content
            error_patterns = [
                "I cannot", "I'm unable", "I don't have access",
                "I cannot provide", "I'm not able to",
                "Sorry, I cannot", "I apologize, but I cannot"
            ]

            content_lower = content.lower()
            for pattern in error_patterns:
                if pattern.lower() in content_lower:
                    app.logger.warning(f"Gemini response contains error pattern: {pattern}")
                    return False

            # Check for "Error:" but be more specific to avoid false positives
            if content_lower.startswith("error:") or " error:" in content_lower[:100]:
                app.logger.warning(f"Gemini response contains error pattern: Error:")
                app.logger.debug(f"Response content preview: {content[:200]}...")
                return False
            
            # Check for minimum content quality
            if len(content.split()) < 5:  # At least 5 words
                app.logger.warning("Gemini response too short")
                return False
            
            return True
            
        except Exception as e:
            app.logger.error(f"Error validating Gemini response: {e}")
            return False

    def calculate_cost(self, tokens_used: int) -> float:
        """Calculate the cost of a Gemini request based on tokens used."""
        try:
            # Gemini Flash 2.0 is currently free, but we'll track for future pricing
            cost_per_1k = float(self.cost_per_1k_tokens)
            return (tokens_used / 1000) * cost_per_1k
        except Exception as e:
            app.logger.error(f"Error calculating Gemini cost: {e}")
            return 0.0

    def check_rate_limits(self, company_id: str) -> Dict[str, Any]:
        """Check if the company has exceeded Gemini rate limits."""
        try:
            current_time = datetime.now()
            
            # Initialize tracking for company if not exists
            if company_id not in self._rate_limit_tracker:
                self._rate_limit_tracker[company_id] = {
                    'daily_requests': 0,
                    'daily_reset': current_time.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1),
                    'minute_requests': 0,
                    'minute_reset': current_time.replace(second=0, microsecond=0) + timedelta(minutes=1)
                }
            
            tracker = self._rate_limit_tracker[company_id]
            
            # Reset daily counter if needed
            if current_time >= tracker['daily_reset']:
                tracker['daily_requests'] = 0
                tracker['daily_reset'] = current_time.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
            
            # Reset minute counter if needed
            if current_time >= tracker['minute_reset']:
                tracker['minute_requests'] = 0
                tracker['minute_reset'] = current_time.replace(second=0, microsecond=0) + timedelta(minutes=1)
            
            # Check limits
            daily_remaining = max(0, self.requests_per_day - tracker['daily_requests'])
            minute_remaining = max(0, self.requests_per_minute - tracker['minute_requests'])
            
            can_proceed = daily_remaining > 0 and minute_remaining > 0
            
            # Increment counters if proceeding
            if can_proceed:
                tracker['daily_requests'] += 1
                tracker['minute_requests'] += 1
            
            return {
                'can_proceed': can_proceed,
                'daily_remaining': daily_remaining,
                'minute_remaining': minute_remaining,
                'daily_reset': tracker['daily_reset'].isoformat(),
                'minute_reset': tracker['minute_reset'].isoformat()
            }
            
        except Exception as e:
            app.logger.error(f"Error checking Gemini rate limits: {e}")
            # Default to allowing the request if there's an error
            return {
                'can_proceed': True,
                'daily_remaining': self.requests_per_day,
                'minute_remaining': self.requests_per_minute,
                'daily_reset': (datetime.now() + timedelta(days=1)).isoformat(),
                'minute_reset': (datetime.now() + timedelta(minutes=1)).isoformat()
            }

    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the Gemini model."""
        return {
            'provider': 'google',
            'model_name': self.model_name,
            'max_tokens': self.max_tokens,
            'supports_streaming': False,
            'supports_function_calling': True,
            'supports_vision': True,
            'cost_per_1k_tokens': float(self.cost_per_1k_tokens),
            'requests_per_minute': self.requests_per_minute,
            'requests_per_day': self.requests_per_day
        }
