from typing import Dict, Any, Optional, List
from datetime import datetime, timed<PERSON>ta
from flask import current_app as app
import json

class PromptBuilder:
    """Service for building dynamic AI prompts based on data and context"""
    
    # Default prompt templates for different insight types
    DEFAULT_TEMPLATES = {
        'attendance': {
            'daily_summary': """
You are a senior HR analytics consultant providing a comprehensive workforce attendance analysis for {company_name} on {date}.

**ATTENDANCE DATA:**
{attendance_data}

**COMPANY CONTEXT:**
{company_context}

**ANALYSIS REQUIREMENTS:**
**IMPORTANT**: For attendance calculations, employees who arrive late are considered ATTENDED (present). The attendance rate includes both on-time and late employees. Only completely absent employees should be excluded from attendance rate calculations.

Provide a detailed, executive-level attendance intelligence report structured as follows:

**1. EXECUTIVE SUMMARY**
- Concise overview of attendance performance against organizational benchmarks
- Key performance indicators and critical metrics analysis
- Overall workforce availability assessment

**2. QUANTITATIVE ANALYSIS**
- Detailed breakdown of attendance statistics with percentage calculations
- Department-wise performance comparison and variance analysis
- Trend identification and statistical significance assessment
- Benchmark comparison against industry standards (90-95% attendance rate)

**3. OPERATIONAL IMPACT ASSESSMENT**
- Productivity implications of current attendance levels
- Resource allocation and workload distribution analysis
- Customer service and operational continuity considerations
- Financial impact estimation: Research current market salary rates for specific positions in the company's country and calculate realistic productivity loss in local currency

**4. RISK ANALYSIS & EARLY WARNING INDICATORS**
- Identification of attendance patterns indicating potential issues
- Assessment of chronic absenteeism risks
- Compliance and policy adherence evaluation
- Predictive indicators for future attendance challenges

**5. STRATEGIC RECOMMENDATIONS**
- Immediate tactical interventions (next 24-48 hours)
- Short-term corrective measures (1-2 weeks)
- Long-term strategic initiatives (1-3 months)
- Resource requirements and implementation timeline
- Success metrics and monitoring framework

**6. DEPARTMENTAL INSIGHTS**
- Individual department performance analysis
- Cross-departmental comparison and best practices identification
- Targeted recommendations for underperforming areas

**FORMATTING REQUIREMENTS:**
- Use professional business language appropriate for {target_audience}
- Include specific data points and percentages
- Maintain {tone} tone throughout
- Provide {detail_level} level of analysis
- Use bullet points and structured formatting for clarity
- Include actionable next steps with clear ownership and timelines

**CONTEXT CONSIDERATIONS:**
- Consider company size, industry, and operational requirements
- Factor in seasonal patterns, business cycles, and external factors
- Align recommendations with organizational culture and capabilities
""",
            'trend_analysis': """
Analyze the following attendance trends for {company_name} over the period {date_range}:

{trend_data}

Company Context: {company_context}

Please provide a comprehensive trend analysis including:
1. Key attendance patterns and trends identified
2. Comparison with previous periods
3. Seasonal or cyclical patterns observed
4. Correlation with business events or external factors
5. Predictive insights for future attendance patterns
6. Strategic recommendations for workforce planning

Focus on actionable insights that can drive business decisions.
""",
            'risk_assessment': """
Evaluate the following attendance data for potential risks at {company_name}:

{attendance_data}

Company Context: {company_context}

Please assess and identify:
1. Employees or departments at risk of chronic absenteeism
2. Patterns that may indicate underlying issues (burnout, disengagement, etc.)
3. Compliance risks related to attendance policies
4. Financial impact of attendance issues
5. Early warning indicators to monitor
6. Preventive measures and intervention strategies

Prioritize risks by severity and provide specific action plans.
""",
            'recommendations': """
Based on the following attendance analysis for {company_name}:

{analysis_data}

Company Context: {company_context}

Generate specific, actionable recommendations including:
1. Immediate actions to address current issues
2. Short-term improvements (1-3 months)
3. Long-term strategic initiatives (3-12 months)
4. Policy adjustments or new policies needed
5. Technology or process improvements
6. Expected ROI and success metrics for each recommendation

Ensure recommendations are practical and aligned with the company's size and industry.
""",
            'weekly_summary': """
Provide a comprehensive weekly attendance intelligence report for {company_name} covering the period {date_range}.

**WEEKLY ATTENDANCE DATA:**
{attendance_data}

**COMPANY CONTEXT:**
{company_context}

**ANALYSIS REQUIREMENTS:**
**IMPORTANT**: For attendance calculations, employees who arrive late are considered ATTENDED (present). The attendance rate includes both on-time and late employees. Only completely absent employees should be excluded from attendance rate calculations.

Generate a strategic weekly attendance analysis structured as follows:

**1. WEEKLY PERFORMANCE OVERVIEW**
- Summary of attendance performance across the week
- Key metrics and performance indicators analysis
- Comparison with previous weeks and monthly targets
- Overall workforce availability and productivity assessment

**2. OPERATIONAL INSIGHTS**
- Daily attendance patterns and trends within the week
- Peak performance days and challenging periods identification
- Department-wise performance comparison and variance analysis
- Impact on operational efficiency and service delivery

**3. FINANCIAL IMPACT ANALYSIS**
- Calculate productivity impact based on attendance levels
- Research current market salary rates for positions in the company's country
- Estimate financial implications of attendance patterns in local currency
- Cost-benefit analysis of attendance improvement initiatives

**4. PATTERN RECOGNITION**
- Weekly attendance trends and behavioral patterns
- Identification of recurring issues or positive trends
- Correlation with business activities, deadlines, or external factors
- Early warning indicators for potential attendance challenges

**5. STRATEGIC RECOMMENDATIONS**
- Immediate actions for the upcoming week
- Short-term improvements for the next 2-4 weeks
- Process optimizations and policy adjustments
- Resource allocation and workforce planning recommendations

**6. DEPARTMENTAL PERFORMANCE**
- Individual department analysis and benchmarking
- Best practices identification and knowledge sharing opportunities
- Targeted interventions for underperforming areas

**FORMATTING REQUIREMENTS:**
- Use professional business language appropriate for {target_audience}
- Include specific data points, percentages, and trend analysis
- Maintain {tone} tone throughout the analysis
- Provide {detail_level} level of insights
- Structure content with clear headings and actionable recommendations
- Focus on operational excellence and continuous improvement
""",
            'monthly_summary': """
Generate a comprehensive monthly attendance intelligence report for {company_name} covering {date_range}.

**MONTHLY ATTENDANCE DATA:**
{attendance_data}

**COMPANY CONTEXT:**
{company_context}

**ANALYSIS REQUIREMENTS:**
**IMPORTANT**: For attendance calculations, employees who arrive late are considered ATTENDED (present). The attendance rate includes both on-time and late employees. Only completely absent employees should be excluded from attendance rate calculations.

Develop a strategic monthly attendance analysis structured as follows:

**1. MONTHLY PERFORMANCE EXECUTIVE SUMMARY**
- Overall attendance performance assessment for the month
- Key achievements and areas of concern identification
- Comparison with previous months and annual targets
- Strategic implications for workforce management

**2. COMPREHENSIVE TREND ANALYSIS**
- Monthly attendance patterns and seasonal variations
- Week-over-week progression and performance consistency
- Department-wise trends and comparative analysis
- Correlation with business cycles, holidays, and external factors

**3. FINANCIAL AND PRODUCTIVITY IMPACT**
- Monthly productivity analysis based on attendance data
- Research and apply current market salary rates for the company's country
- Calculate financial impact of attendance patterns in local currency
- ROI analysis of attendance management initiatives
- Budget implications and cost optimization opportunities

**4. STRATEGIC WORKFORCE INSIGHTS**
- Employee engagement indicators derived from attendance patterns
- Risk assessment for chronic absenteeism and turnover
- Workforce planning implications and capacity analysis
- Performance correlation with attendance consistency

**5. DEPARTMENTAL DEEP DIVE**
- Comprehensive department-by-department performance analysis
- Best practices identification and cross-departmental learning
- Resource allocation optimization recommendations
- Targeted improvement strategies for underperforming areas

**6. FORWARD-LOOKING RECOMMENDATIONS**
- Strategic initiatives for the next month
- Medium-term improvements (2-3 months)
- Policy enhancements and process optimizations
- Technology and infrastructure recommendations
- Success metrics and monitoring framework

**FORMATTING REQUIREMENTS:**
- Executive-level business language appropriate for {target_audience}
- Include comprehensive data analysis with statistical insights
- Maintain {tone} tone with strategic focus
- Provide {detail_level} analysis with actionable intelligence
- Use structured formatting with executive summary and detailed sections
- Include predictive insights and strategic planning recommendations
""",
            'annual_summary': """
Develop a comprehensive annual attendance intelligence report for {company_name} covering the year {date_range}.

**ANNUAL ATTENDANCE DATA:**
{attendance_data}

**COMPANY CONTEXT:**
{company_context}

**ANALYSIS REQUIREMENTS:**
Create a strategic annual attendance analysis structured as follows:

**1. ANNUAL PERFORMANCE EXECUTIVE OVERVIEW**
- Year-end attendance performance assessment and achievements
- Annual targets achievement and variance analysis
- Strategic workforce availability and reliability metrics
- Overall organizational attendance maturity evaluation

**2. COMPREHENSIVE ANNUAL TRENDS**
- Year-over-year attendance progression and patterns
- Seasonal variations and cyclical trend identification
- Monthly and quarterly performance analysis
- Long-term workforce behavior and engagement trends

**3. STRATEGIC FINANCIAL ANALYSIS**
- Annual productivity impact assessment based on attendance data
- Research current market salary rates for positions in the company's country
- Calculate comprehensive financial impact in local currency
- ROI analysis of attendance management investments
- Budget planning and cost optimization recommendations for next year

**4. ORGANIZATIONAL INSIGHTS**
- Employee retention correlation with attendance patterns
- Workforce engagement and satisfaction indicators
- Department performance benchmarking and best practices
- Organizational culture impact on attendance behavior

**5. STRATEGIC PLANNING INTELLIGENCE**
- Workforce planning recommendations for the upcoming year
- Capacity planning and resource allocation strategies
- Risk mitigation strategies for attendance challenges
- Technology and process improvement roadmap

**6. ANNUAL RECOMMENDATIONS AND ROADMAP**
- Strategic initiatives for the next 12 months
- Policy framework enhancements and governance improvements
- Investment priorities and resource requirements
- Success metrics and KPI framework for next year
- Competitive benchmarking and industry positioning

**FORMATTING REQUIREMENTS:**
- Executive and board-level business language appropriate for {target_audience}
- Include comprehensive statistical analysis and trend insights
- Maintain {tone} tone with strategic and forward-looking focus
- Provide {detail_level} analysis with strategic intelligence
- Structure as executive report with summary and detailed analysis sections
- Include predictive modeling and strategic planning recommendations
""",
            'custom_summary': """
Generate a comprehensive attendance intelligence report for {company_name} covering the custom period {date_range}.

**CUSTOM PERIOD ATTENDANCE DATA:**
{attendance_data}

**COMPANY CONTEXT:**
{company_context}

**ANALYSIS REQUIREMENTS:**
Develop a tailored attendance analysis structured as follows:

**1. CUSTOM PERIOD PERFORMANCE OVERVIEW**
- Attendance performance assessment for the specified period
- Key metrics and performance indicators analysis
- Context-appropriate benchmarking and comparison
- Period-specific insights and observations

**2. PERIOD-SPECIFIC TREND ANALYSIS**
- Attendance patterns and trends within the custom timeframe
- Daily, weekly, or monthly progression as appropriate
- Department-wise performance and comparative analysis
- Correlation with business events, projects, or external factors

**3. FINANCIAL AND OPERATIONAL IMPACT**
- Productivity analysis based on attendance levels for the period
- Research current market salary rates for positions in the company's country
- Calculate financial implications in local currency
- Operational efficiency and service delivery impact assessment

**4. CONTEXTUAL INSIGHTS**
- Period-specific workforce behavior and engagement patterns
- Risk identification and opportunity assessment
- Performance correlation with business objectives
- External factor influence analysis

**5. TARGETED RECOMMENDATIONS**
- Period-appropriate immediate actions
- Short and medium-term improvement strategies
- Process and policy optimization recommendations
- Resource allocation and planning suggestions

**FORMATTING REQUIREMENTS:**
- Professional business language appropriate for {target_audience}
- Include relevant data analysis and insights for the custom period
- Maintain {tone} tone throughout the analysis
- Provide {detail_level} level of insights
- Structure content appropriately for the period length and context
- Focus on actionable recommendations and strategic insights
"""
        },
        'announcements': {
            'weekly_summary': """
Create a professional company announcement based on this weekly attendance summary:

{weekly_data}

Company Context: {company_context}

Generate an engaging announcement that:
1. Highlights positive attendance achievements
2. Recognizes top-performing teams or individuals
3. Addresses any concerns in a constructive manner
4. Motivates continued good attendance
5. Includes any relevant reminders or updates

Target audience: {target_audience}
Tone: {tone}
Keep it under 200 words and maintain a positive, encouraging tone.
""",
            'policy_reminder': """
Create a targeted policy reminder announcement based on this attendance data:

{attendance_issues}

Company Context: {company_context}

Generate a professional reminder that:
1. Addresses specific attendance issues without naming individuals
2. Clearly restates relevant policies
3. Explains the importance of good attendance
4. Offers support resources if available
5. Sets clear expectations going forward

Tone should be firm but supportive, emphasizing team success.
""",
            'celebration': """
Create a celebratory announcement based on these positive attendance achievements:

{achievement_data}

Company Context: {company_context}

Generate an enthusiastic announcement that:
1. Celebrates the specific achievements
2. Recognizes individuals or teams (if appropriate)
3. Highlights the positive impact on the company
4. Encourages continued excellence
5. Builds team morale and engagement

Make it inspiring and motivational while maintaining professionalism.
"""
        },
        'executive': {
            'monthly_summary': """
You are a senior workforce analytics consultant preparing a C-level executive briefing on attendance performance for {company_name} covering {period}.

**WORKFORCE DATA:**
{executive_data}

**ORGANIZATIONAL CONTEXT:**
{company_context}

**EXECUTIVE BRIEFING STRUCTURE:**

**I. EXECUTIVE SUMMARY**
- Strategic workforce availability overview
- Critical performance indicators vs. organizational targets
- Key business impact assessment
- Priority action items requiring executive attention

**II. FINANCIAL & OPERATIONAL IMPACT**
- Productivity metrics and efficiency analysis
- Revenue impact of attendance patterns
- Cost implications of absenteeism (replacement costs, overtime, productivity loss)
- Operational risk assessment and mitigation strategies

**III. STRATEGIC WORKFORCE ANALYSIS**
- Attendance trends and predictive modeling
- Department-wise performance benchmarking
- Industry comparison and competitive positioning
- Workforce engagement correlation analysis

**IV. BUSINESS CONTINUITY & RISK MANAGEMENT**
- Critical role coverage and succession planning implications
- Operational resilience assessment
- Compliance and regulatory considerations
- Reputation and client service impact evaluation

**V. STRATEGIC RECOMMENDATIONS & INVESTMENT PRIORITIES**
- High-impact initiatives with ROI projections
- Resource allocation recommendations
- Technology and process improvement opportunities
- Change management and cultural transformation strategies

**VI. IMPLEMENTATION ROADMAP**
- 30-60-90 day action plan with measurable outcomes
- Budget requirements and resource allocation
- Success metrics and KPI framework
- Risk mitigation and contingency planning

**DELIVERABLE REQUIREMENTS:**
- Executive-level language with strategic focus
- Quantified business impact and ROI analysis
- Clear decision points and investment recommendations
- Actionable insights with ownership and timelines
- Data-driven conclusions with supporting evidence
""",
            'strategic_insights': """
Analyze the following workforce data for strategic insights at {company_name}:

{strategic_data}

Company Context: {company_context}

Provide strategic analysis including:
1. Workforce efficiency and optimization opportunities
2. Talent retention and engagement indicators
3. Operational excellence metrics and improvements
4. Competitive advantages and market positioning
5. Investment priorities for workforce management
6. Risk mitigation strategies
7. Long-term workforce planning recommendations

Focus on strategic value creation and competitive advantage.
"""
        }
    }

    @classmethod
    def build_attendance_prompt(cls, data: Dict[str, Any], company_context: str, 
                               insight_type: str = 'daily_summary', 
                               custom_template: Optional[str] = None) -> str:
        """
        Build an attendance-specific prompt.
        
        Args:
            data: Attendance data and metrics
            company_context: Company-specific context
            insight_type: Type of insight to generate
            custom_template: Optional custom template to use
            
        Returns:
            Formatted prompt string
        """
        try:
            # Use custom template or default
            template = custom_template or cls.DEFAULT_TEMPLATES['attendance'].get(insight_type, 
                cls.DEFAULT_TEMPLATES['attendance']['daily_summary'])
            
            # Prepare template variables
            period_info = data.get('period_info', {})

            variables = {
                'company_name': data.get('company_name', 'Your Company'),
                'date': data.get('date', period_info.get('start_date', datetime.now().strftime('%Y-%m-%d'))),
                'date_range': period_info.get('description', data.get('date_range', 'recent period')),
                'company_context': company_context or 'Medium-sized organization',
                'attendance_data': cls._format_attendance_data(data.get('attendance_data', {}), data.get('company_info', {})),
                'trend_data': cls._format_trend_data(data.get('trend_data', {})),
                'analysis_data': cls._format_analysis_data(data.get('analysis_data', {})),
                'target_audience': data.get('target_audience', 'management'),
                'tone': data.get('tone', 'professional'),
                'detail_level': data.get('detail_level', 'medium')
            }
            
            # Format the template
            return template.format(**variables)
            
        except Exception as e:
            app.logger.error(f"Error building attendance prompt: {e}")
            return f"Analyze the attendance data for {data.get('company_name', 'the company')} and provide insights."

    @classmethod
    def build_announcement_prompt(cls, data: Dict[str, Any], company_context: str,
                                 announcement_type: str = 'weekly_summary',
                                 custom_template: Optional[str] = None) -> str:
        """
        Build an announcement-specific prompt.
        
        Args:
            data: Announcement data and context
            company_context: Company-specific context
            announcement_type: Type of announcement to generate
            custom_template: Optional custom template to use
            
        Returns:
            Formatted prompt string
        """
        try:
            # Use custom template or default
            template = custom_template or cls.DEFAULT_TEMPLATES['announcements'].get(announcement_type,
                cls.DEFAULT_TEMPLATES['announcements']['weekly_summary'])
            
            # Prepare template variables
            variables = {
                'company_name': data.get('company_name', 'Your Company'),
                'company_context': company_context or 'Medium-sized organization',
                'weekly_data': cls._format_weekly_data(data.get('weekly_data', {})),
                'attendance_issues': cls._format_attendance_issues(data.get('attendance_issues', {})),
                'achievement_data': cls._format_achievement_data(data.get('achievement_data', {})),
                'target_audience': data.get('target_audience', 'all employees'),
                'tone': data.get('tone', 'professional')
            }
            
            # Format the template
            return template.format(**variables)
            
        except Exception as e:
            app.logger.error(f"Error building announcement prompt: {e}")
            return f"Create a professional announcement for {data.get('company_name', 'the company')}."

    @classmethod
    def build_executive_prompt(cls, data: Dict[str, Any], company_context: str,
                              executive_type: str = 'monthly_summary',
                              custom_template: Optional[str] = None) -> str:
        """
        Build an executive-level prompt.
        
        Args:
            data: Executive data and metrics
            company_context: Company-specific context
            executive_type: Type of executive insight to generate
            custom_template: Optional custom template to use
            
        Returns:
            Formatted prompt string
        """
        try:
            # Use custom template or default
            template = custom_template or cls.DEFAULT_TEMPLATES['executive'].get(executive_type,
                cls.DEFAULT_TEMPLATES['executive']['monthly_summary'])
            
            # Prepare template variables
            variables = {
                'company_name': data.get('company_name', 'Your Company'),
                'period': data.get('period', 'this month'),
                'company_context': company_context or 'Medium-sized organization',
                'executive_data': cls._format_executive_data(data.get('executive_data', {})),
                'strategic_data': cls._format_strategic_data(data.get('strategic_data', {}))
            }
            
            # Format the template
            return template.format(**variables)
            
        except Exception as e:
            app.logger.error(f"Error building executive prompt: {e}")
            return f"Provide executive insights for {data.get('company_name', 'the company')}."

    @classmethod
    def build_custom_prompt(cls, template: str, variables: Dict[str, Any]) -> str:
        """
        Build a custom prompt from template and variables.
        
        Args:
            template: Custom prompt template with placeholders
            variables: Dictionary of variables to substitute
            
        Returns:
            Formatted prompt string
        """
        try:
            return template.format(**variables)
        except Exception as e:
            app.logger.error(f"Error building custom prompt: {e}")
            return template

    @classmethod
    def _format_attendance_data(cls, data: Dict[str, Any], company_info: Dict[str, Any] = None) -> str:
        """Format attendance data for prompt inclusion with enhanced professional context."""
        if not data:
            return "No attendance data available."

        formatted = []

        if 'summary' in data:
            summary = data['summary']
            total_employees = summary.get('total_employees', 0)
            present_count = summary.get('present_count', 0)
            absent_count = summary.get('absent_count', 0)
            late_count = summary.get('late_count', 0)
            on_leave_count = summary.get('on_leave_count', 0)
            attendance_rate = summary.get('attendance_percentage', 0)

            # Calculate total attended (present + late) for clarity
            total_attended = present_count + (late_count if isinstance(late_count, int) else 0)

            formatted.append("**WORKFORCE ATTENDANCE METRICS:**")
            formatted.append(f"• Total Workforce: {total_employees} employees")
            formatted.append(f"• Total Attended (entire period): {total_attended} employee-days ({attendance_rate:.1f}%)")
            formatted.append(f"  - On Time: {present_count} employee-days")
            formatted.append(f"  - Late Arrivals: {late_count} employee-days")
            formatted.append(f"• Total Absent (entire period): {absent_count} employee-days ({100-attendance_rate:.1f}%)")
            formatted.append(f"• On Leave (entire period): {on_leave_count}")

            # Add critical clarification for AI interpretation
            formatted.append(f"• **IMPORTANT**: Late arrivals are counted as ATTENDED for attendance rate calculation")
            formatted.append(f"• Note: These are cumulative totals across the entire reporting period, not daily figures")

            # Add performance assessment
            if attendance_rate >= 95:
                performance = "EXCELLENT (Above Industry Standard)"
            elif attendance_rate >= 90:
                performance = "GOOD (Meeting Industry Standard)"
            elif attendance_rate >= 80:
                performance = "BELOW STANDARD (Requires Attention)"
            else:
                performance = "CRITICAL (Immediate Action Required)"

            formatted.append(f"• Performance Rating: {performance}")

            # Add capacity impact
            if total_employees > 0:
                capacity_impact = (absent_count / total_employees) * 100
                formatted.append(f"• Operational Capacity Impact: {capacity_impact:.1f}% reduction")

        if 'departments' in data:
            formatted.append("\n**DEPARTMENTAL PERFORMANCE ANALYSIS:**")

            # Sort departments by attendance rate for better analysis
            departments = sorted(data['departments'],
                               key=lambda x: x.get('attendance_percentage', 0), reverse=True)

            for i, dept in enumerate(departments):
                dept_name = dept.get('department_name', 'Unknown Department')
                dept_rate = dept.get('attendance_percentage', 0)
                total_emp = dept.get('total_employees', 0)
                present = dept.get('present_count', 0)
                absent = dept.get('absent_count', 0)

                rank_indicator = "🟢" if dept_rate >= 90 else "🟡" if dept_rate >= 80 else "🔴"

                formatted.append(f"{rank_indicator} {dept_name}:")
                formatted.append(f"  - Attendance Rate: {dept_rate:.1f}% ({present}/{total_emp} present)")
                formatted.append(f"  - Absent: {absent} employees")

                if i == 0 and len(departments) > 1:
                    formatted.append(f"  - Status: TOP PERFORMER")
                elif i == len(departments) - 1 and len(departments) > 1:
                    formatted.append(f"  - Status: NEEDS IMPROVEMENT")

        # Add financial impact analysis if company info is available
        if company_info and isinstance(company_info, dict):
            formatted.append("\n**FINANCIAL IMPACT ANALYSIS:**")
            formatted.append(cls._format_financial_impact(data, company_info))

        return "\n".join(formatted)

    @classmethod
    def _format_trend_data(cls, data: Dict[str, Any]) -> str:
        """Format trend data for prompt inclusion."""
        if not data:
            return "No trend data available."
        
        formatted = []
        
        if 'trends' in data:
            formatted.append("Attendance Trends:")
            for trend in data['trends']:
                formatted.append(f"- {trend.get('description', 'Trend data')}")
        
        return "\n".join(formatted)

    @classmethod
    def _format_analysis_data(cls, data: Dict[str, Any]) -> str:
        """Format analysis data for prompt inclusion."""
        if not data:
            return "No analysis data available."
        
        return json.dumps(data, indent=2)

    @classmethod
    def _format_weekly_data(cls, data: Dict[str, Any]) -> str:
        """Format weekly data for announcements."""
        if not data:
            return "No weekly data available."
        
        formatted = []
        formatted.append(f"Week of {data.get('week_start', 'N/A')}:")
        formatted.append(f"- Overall attendance: {data.get('attendance_rate', 'N/A')}%")
        formatted.append(f"- Top performing team: {data.get('top_team', 'N/A')}")
        
        return "\n".join(formatted)

    @classmethod
    def _format_attendance_issues(cls, data: Dict[str, Any]) -> str:
        """Format attendance issues for policy reminders."""
        if not data:
            return "No specific issues identified."
        
        formatted = []
        if 'issues' in data:
            for issue in data['issues']:
                formatted.append(f"- {issue}")
        
        return "\n".join(formatted)

    @classmethod
    def _format_financial_impact(cls, attendance_data: Dict[str, Any], company_info: Dict[str, Any]) -> str:
        """Format financial impact context for AI to research and calculate."""
        formatted = []

        country = company_info.get('country') or {}
        country_name = country.get('name') if country else 'Unknown'
        currency = country.get('currency') if country else 'USD'

        # If no country data, use default assumptions
        if not country_name or country_name == 'Unknown':
            country_name = 'International'
            currency = 'USD'

        # Collect employee positions and departments for AI analysis
        absent_employees = []
        if 'employee_details' in attendance_data and 'absent' in attendance_data['employee_details']:
            for emp in attendance_data['employee_details']['absent']:
                position = emp.get('position') or emp.get('department_name', 'Unknown')
                absent_employees.append({
                    'name': emp.get('full_name', 'Unknown'),
                    'position': position,
                    'department': emp.get('department_name', 'Unknown')
                })

        # Provide context for AI to research and calculate financial impact
        formatted.append(f"**FINANCIAL IMPACT ANALYSIS CONTEXT:**")
        formatted.append(f"• Company Location: {country_name}")
        formatted.append(f"• Local Currency: {currency}")
        formatted.append(f"• Absent Employees and Positions:")

        for emp in absent_employees:
            formatted.append(f"  - {emp['name']}: {emp['position']} ({emp['department']} department)")

        # Get summary data for proper calculation context
        summary = attendance_data.get('summary', {})
        total_employees = summary.get('total_employees', 0)
        absent_count = summary.get('absent_count', 0)
        present_count = summary.get('present_count', 0)

        formatted.append(f"\n**INSTRUCTIONS FOR AI FINANCIAL ANALYSIS:**")
        formatted.append(f"IMPORTANT: The absent count ({absent_count}) represents TOTAL EMPLOYEE-DAYS absent across the entire reporting period.")
        formatted.append(f"This is NOT the number of employees absent per day.")
        formatted.append(f"Company has {total_employees} total employees.")
        formatted.append(f"Total present employee-days: {present_count}")
        formatted.append(f"")
        formatted.append(f"CALCULATION METHOD:")
        formatted.append(f"1. Research realistic daily salary rates for positions in {country_name}")
        formatted.append(f"2. Calculate: Total absent employee-days × average daily salary = TOTAL PERIOD LOSS")
        formatted.append(f"3. This gives you the cumulative financial impact for the entire reporting period")
        formatted.append(f"4. Convert to USD equivalent using current exchange rates")
        formatted.append(f"5. Consider local economic conditions and industry standards in {country_name}")
        formatted.append(f"")
        formatted.append(f"DO NOT multiply by additional days/weeks/months - the absent count is already cumulative.")

        return "\n".join(formatted)

    @classmethod
    def _format_achievement_data(cls, data: Dict[str, Any]) -> str:
        """Format achievement data for celebrations."""
        if not data:
            return "No achievements to celebrate."

        formatted = []
        if 'achievements' in data:
            for achievement in data['achievements']:
                formatted.append(f"- {achievement}")

        return "\n".join(formatted)

    @classmethod
    def _format_executive_data(cls, data: Dict[str, Any]) -> str:
        """Format executive data for strategic insights."""
        if not data:
            return "No executive data available."
        
        return json.dumps(data, indent=2)

    @classmethod
    def _format_strategic_data(cls, data: Dict[str, Any]) -> str:
        """Format strategic data for executive insights."""
        if not data:
            return "No strategic data available."
        
        return json.dumps(data, indent=2)
