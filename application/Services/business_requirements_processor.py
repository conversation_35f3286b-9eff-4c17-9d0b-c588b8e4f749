"""
Business Requirements Processor Service
Handles validation, conversion, and processing of structured business requirements for shift scheduling.
"""

import json
from datetime import datetime, time
from typing import Dict, List, Tuple, Any, Optional
from flask import current_app as app


class BusinessRequirementsProcessor:
    """Service class for processing structured business requirements."""
    
    # Valid pattern types
    VALID_PATTERN_TYPES = ['weekly', 'monthly', 'annual']
    
    # Valid weekend requirements
    VALID_WEEKEND_REQUIREMENTS = ['at_least_one_off', 'both_off', 'flexible']
    
    # Valid emergency coverage plans
    VALID_EMERGENCY_PLANS = ['call_in_overtime', 'mandatory_overtime', 'redistribute_workload', 'reduce_service']

    @staticmethod
    def validate_schema(business_requirements: Dict) -> Tuple[bool, str]:
        """
        Validate structured business requirements against expected schema.
        
        Args:
            business_requirements: The business requirements dictionary
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            # Check required top-level fields
            required_fields = ['schedule_pattern', 'shifts', 'work_rules']
            for field in required_fields:
                if field not in business_requirements:
                    return False, f"Missing required field: {field}"
            
            # Validate schedule pattern
            schedule_pattern = business_requirements['schedule_pattern']
            if 'type' not in schedule_pattern:
                return False, "schedule_pattern must include 'type'"
            
            if schedule_pattern['type'] not in BusinessRequirementsProcessor.VALID_PATTERN_TYPES:
                return False, f"schedule_pattern.type must be one of: {BusinessRequirementsProcessor.VALID_PATTERN_TYPES}"
            
            # Validate shifts
            shifts = business_requirements['shifts']
            if not isinstance(shifts, list) or len(shifts) == 0:
                return False, "At least one shift is required"
            
            for i, shift in enumerate(shifts):
                is_valid, error = BusinessRequirementsProcessor._validate_shift(shift, i)
                if not is_valid:
                    return False, error
            
            # Validate work rules
            work_rules = business_requirements['work_rules']
            is_valid, error = BusinessRequirementsProcessor._validate_work_rules(work_rules)
            if not is_valid:
                return False, error
            
            return True, "Valid business requirements"
            
        except Exception as e:
            app.logger.error(f"Error validating business requirements: {e}")
            return False, f"Validation error: {str(e)}"

    @staticmethod
    def _validate_shift(shift: Dict, index: int) -> Tuple[bool, str]:
        """Validate individual shift configuration."""
        # Check if shift_id is provided (preferred approach)
        has_shift_id = 'shift_id' in shift and shift['shift_id']

        if has_shift_id:
            # When shift_id is provided, only staffing is required
            # shift_name, time_range, break_duration are optional overrides
            required_fields = ['staffing']
        else:
            # Legacy approach: require all fields for inline shift definition
            required_fields = ['shift_name', 'time_range', 'staffing']

        for field in required_fields:
            if field not in shift:
                return False, f"Shift {index + 1}: Missing required field '{field}'"

        # Validate time range if provided (for overrides or inline definitions)
        if 'time_range' in shift:
            time_range = shift['time_range']
            if 'start_time' not in time_range or 'end_time' not in time_range:
                return False, f"Shift {index + 1}: time_range must include start_time and end_time"

            # Validate time format and logic
            try:
                start_time = datetime.strptime(time_range['start_time'], '%H:%M').time()
                end_time = datetime.strptime(time_range['end_time'], '%H:%M').time()

                # Check for logical time consistency (for same-day shifts)
                # Note: Night shifts (end_time < start_time) are allowed for overnight shifts
                if start_time == end_time:
                    return False, f"Shift {index + 1}: Start time and end time cannot be the same"

            except ValueError:
                return False, f"Shift {index + 1}: Invalid time format. Use HH:MM format"

        # Validate break_duration if provided (override)
        if 'break_duration' in shift:
            try:
                break_duration = int(shift['break_duration'])
                if break_duration < 0:
                    return False, f"Shift {index + 1}: break_duration cannot be negative"
                if break_duration > 480:  # 8 hours max
                    return False, f"Shift {index + 1}: break_duration cannot exceed 480 minutes"
            except (ValueError, TypeError):
                return False, f"Shift {index + 1}: break_duration must be a valid number"

        # Validate break_start_time if provided (override)
        if 'break_start_time' in shift:
            try:
                datetime.strptime(shift['break_start_time'], '%H:%M')
            except ValueError:
                return False, f"Shift {index + 1}: Invalid break_start_time format. Use HH:MM format"
        
        # Validate staffing
        staffing = shift['staffing']
        required_staffing_fields = ['minimum_staff', 'preferred_staff']
        for field in required_staffing_fields:
            if field not in staffing:
                return False, f"Shift {index + 1}: Missing staffing field '{field}'"
            
            if not isinstance(staffing[field], int) or staffing[field] < 1:
                return False, f"Shift {index + 1}: {field} must be a positive integer"
        
        # Validate staffing logic
        if staffing['minimum_staff'] > staffing['preferred_staff']:
            return False, f"Shift {index + 1}: minimum_staff cannot exceed preferred_staff"

        if 'maximum_staff' in staffing:
            if staffing['maximum_staff'] < staffing['minimum_staff']:
                return False, f"Shift {index + 1}: maximum_staff cannot be less than minimum_staff"
            if staffing['maximum_staff'] < staffing['preferred_staff']:
                return False, f"Shift {index + 1}: maximum_staff cannot be less than preferred_staff"
        
        # Validate days of week
        if 'days_of_week' in shift:
            days = shift['days_of_week']
            if not isinstance(days, list) or not all(isinstance(d, int) and 1 <= d <= 7 for d in days):
                return False, f"Shift {index + 1}: days_of_week must be a list of integers 1-7 (Monday=1, Sunday=7)"
        
        return True, "Valid shift"

    @staticmethod
    def _validate_work_rules(work_rules: Dict) -> Tuple[bool, str]:
        """Validate work rules configuration."""
        # Validate max consecutive days
        if 'max_consecutive_days' in work_rules:
            max_days = work_rules['max_consecutive_days']
            if not isinstance(max_days, int) or max_days < 1:
                return False, "max_consecutive_days must be a positive integer"
        
        # Validate min rest days
        if 'min_rest_days' in work_rules:
            min_rest = work_rules['min_rest_days']
            if not isinstance(min_rest, int) or min_rest < 0:
                return False, "min_rest_days must be a non-negative integer"
        
        # Validate weekend requirements
        if 'weekend_requirements' in work_rules:
            weekend_req = work_rules['weekend_requirements']
            app.logger.info(f"Validating weekend_requirements: '{weekend_req}' against valid options: {BusinessRequirementsProcessor.VALID_WEEKEND_REQUIREMENTS}")
            if weekend_req not in BusinessRequirementsProcessor.VALID_WEEKEND_REQUIREMENTS:
                app.logger.error(f"Invalid weekend_requirements: '{weekend_req}'. Valid options: {BusinessRequirementsProcessor.VALID_WEEKEND_REQUIREMENTS}")
                return False, f"weekend_requirements must be one of: {BusinessRequirementsProcessor.VALID_WEEKEND_REQUIREMENTS}"
        
        return True, "Valid work rules"

    @staticmethod
    def convert_to_calculation_data(business_requirements: Dict, pattern_type: str) -> Dict:
        """
        Convert structured business requirements to calculation-ready format.
        
        Args:
            business_requirements: Validated business requirements
            pattern_type: The pattern type (weekly, monthly, annual)
            
        Returns:
            Dictionary with calculation-ready data
        """
        try:
            calculation_data = {
                "pattern_type": pattern_type,
                "cycle_length": business_requirements['schedule_pattern'].get('cycle_length', 
                    BusinessRequirementsProcessor._get_default_cycle_length(pattern_type)),
                "shifts_config": [],
                "daily_requirements": {},
                "constraints": {},
                "coverage_matrix": {}
            }
            
            # Process shifts - now requires shift_id
            for shift in business_requirements['shifts']:
                if not shift.get('shift_id'):
                    raise ValueError(f"shift_id is required for all shifts in business requirements")

                shift_config = {
                    "shift_id": shift['shift_id'],  # Now required
                    "min_staff": shift['staffing']['minimum_staff'],
                    "preferred_staff": shift['staffing']['preferred_staff'],
                    "max_staff": shift['staffing'].get('maximum_staff', shift['staffing']['preferred_staff'] + 2),
                    "days_of_week": shift.get('days_of_week', [1, 2, 3, 4, 5, 6, 7]),
                    "roles_required": shift.get('roles_required', [])
                }
                calculation_data["shifts_config"].append(shift_config)
            
            # Build daily requirements matrix
            calculation_data["daily_requirements"] = BusinessRequirementsProcessor._build_daily_requirements(
                business_requirements['shifts']
            )
            
            # Process constraints
            work_rules = business_requirements.get('work_rules', {})
            calculation_data["constraints"] = {
                "max_consecutive_days": work_rules.get('max_consecutive_days', 5),
                "min_rest_days": work_rules.get('min_rest_days', 1),
                "max_hours_per_week": work_rules.get('max_hours_per_week', 40),
                "max_hours_per_day": work_rules.get('max_hours_per_day', 8),
                "overtime_allowed": work_rules.get('overtime_allowed', False),
                "weekend_requirements": work_rules.get('weekend_requirements', 'flexible')
            }
            
            # Add coverage rules
            coverage_rules = business_requirements.get('coverage_rules', {})
            calculation_data["coverage_requirements"] = {
                "minimum_coverage_percentage": coverage_rules.get('minimum_coverage_percentage', 90),
                "allow_understaffing": coverage_rules.get('allow_understaffing', False),
                "emergency_coverage_plan": coverage_rules.get('emergency_coverage_plan', 'call_in_overtime')
            }
            
            return calculation_data
            
        except Exception as e:
            app.logger.error(f"Error converting business requirements to calculation data: {e}")
            raise

    @staticmethod
    def _get_default_cycle_length(pattern_type: str) -> int:
        """Get default cycle length based on pattern type."""
        defaults = {
            'weekly': 7,
            'monthly': 30,
            'annual': 365
        }
        return defaults.get(pattern_type, 7)

    @staticmethod
    def _build_daily_requirements(shifts: List[Dict]) -> Dict:
        """Build daily staffing requirements matrix."""
        daily_requirements = {}

        # Initialize all days
        for day in range(1, 8):  # Monday=1 to Sunday=7
            daily_requirements[day] = {}

        # Process each shift
        for shift in shifts:
            shift_id = shift['shift_id']  # Use shift_id as key instead of shift_name
            days_of_week = shift.get('days_of_week', [1, 2, 3, 4, 5, 6, 7])
            staffing = shift['staffing']

            for day in days_of_week:
                daily_requirements[day][shift_id] = {
                    "min_staff": staffing['minimum_staff'],
                    "preferred_staff": staffing['preferred_staff'],
                    "max_staff": staffing.get('maximum_staff', staffing['preferred_staff'] + 2),
                    "shift_id": shift_id
                }

        return daily_requirements

    @staticmethod
    def apply_smart_defaults(business_requirements: Dict) -> Dict:
        """Apply smart defaults to incomplete business requirements."""
        # Create a copy to avoid modifying the original
        enhanced_requirements = business_requirements.copy()
        
        # Apply schedule pattern defaults
        if 'schedule_pattern' not in enhanced_requirements:
            enhanced_requirements['schedule_pattern'] = {'type': 'weekly'}
        
        schedule_pattern = enhanced_requirements['schedule_pattern']
        if 'cycle_length' not in schedule_pattern:
            schedule_pattern['cycle_length'] = BusinessRequirementsProcessor._get_default_cycle_length(
                schedule_pattern.get('type', 'weekly')
            )
        
        # Apply shift defaults
        for shift in enhanced_requirements.get('shifts', []):
            if 'days_of_week' not in shift:
                shift['days_of_week'] = [1, 2, 3, 4, 5, 6, 7]  # All days by default
            
            staffing = shift.get('staffing', {})
            if 'maximum_staff' not in staffing and 'preferred_staff' in staffing:
                staffing['maximum_staff'] = staffing['preferred_staff'] + 2
        
        # Apply work rules defaults
        if 'work_rules' not in enhanced_requirements:
            enhanced_requirements['work_rules'] = {}
        
        work_rules = enhanced_requirements['work_rules']
        work_rules_defaults = {
            'max_consecutive_days': 5,
            'min_rest_days': 1,
            'max_hours_per_week': 40,
            'overtime_allowed': False,
            'weekend_requirements': 'flexible'
        }
        
        for key, default_value in work_rules_defaults.items():
            if key not in work_rules:
                work_rules[key] = default_value
        
        # Apply employee preferences defaults
        if 'employee_preferences' not in enhanced_requirements:
            enhanced_requirements['employee_preferences'] = {}
        
        emp_prefs = enhanced_requirements['employee_preferences']
        emp_prefs_defaults = {
            'fair_rotation': True,
            'seniority_priority': False,
            'consider_employee_requests': True
        }
        
        for key, default_value in emp_prefs_defaults.items():
            if key not in emp_prefs:
                emp_prefs[key] = default_value
        
        # Apply coverage rules defaults
        if 'coverage_rules' not in enhanced_requirements:
            enhanced_requirements['coverage_rules'] = {}
        
        coverage_rules = enhanced_requirements['coverage_rules']
        coverage_defaults = {
            'minimum_coverage_percentage': 90,
            'allow_understaffing': False,
            'emergency_coverage_plan': 'call_in_overtime'
        }
        
        for key, default_value in coverage_defaults.items():
            if key not in coverage_rules:
                coverage_rules[key] = default_value
        
        return enhanced_requirements

    @staticmethod
    def get_staffing_summary(business_requirements: Dict) -> Dict:
        """Generate a summary of staffing requirements."""
        summary = {
            "total_shifts": len(business_requirements.get('shifts', [])),
            "daily_totals": {},
            "weekly_totals": {},
            "shift_details": []
        }
        
        # Calculate daily and weekly totals
        for day in range(1, 8):
            summary["daily_totals"][day] = {
                "min_staff": 0,
                "preferred_staff": 0,
                "max_staff": 0
            }
        
        for shift in business_requirements.get('shifts', []):
            days_of_week = shift.get('days_of_week', [1, 2, 3, 4, 5, 6, 7])
            staffing = shift['staffing']

            shift_detail = {
                "shift_id": shift['shift_id'],
                "staffing": staffing,
                "days_count": len(days_of_week)
            }
            summary["shift_details"].append(shift_detail)
            
            # Add to daily totals
            for day in days_of_week:
                summary["daily_totals"][day]["min_staff"] += staffing['minimum_staff']
                summary["daily_totals"][day]["preferred_staff"] += staffing['preferred_staff']
                summary["daily_totals"][day]["max_staff"] += staffing.get('maximum_staff', staffing['preferred_staff'] + 2)
        
        # Calculate weekly totals
        summary["weekly_totals"] = {
            "min_staff_hours": sum(day["min_staff"] for day in summary["daily_totals"].values()) * 8,  # Assuming 8-hour shifts
            "preferred_staff_hours": sum(day["preferred_staff"] for day in summary["daily_totals"].values()) * 8,
            "max_staff_hours": sum(day["max_staff"] for day in summary["daily_totals"].values()) * 8
        }
        
        return summary

    @staticmethod
    def get_shift_details_by_ids(session, shift_ids, company_id):
        """Get shift details for a list of shift IDs."""
        from application.Models.employees.shift import Shift

        shift_details = {}
        for shift_id in shift_ids:
            shift = Shift.get_shift_by_id(session, shift_id)
            if shift and shift.company_id == company_id:
                shift_details[shift_id] = {
                    "shift_id": str(shift.shift_id),
                    "name": shift.name,
                    "description": shift.description,
                    "start_time": shift.start_time.strftime('%H:%M') if shift.start_time else None,
                    "end_time": shift.end_time.strftime('%H:%M') if shift.end_time else None,
                    "is_night_shift": shift.is_night_shift,
                    "working_days": shift.working_days
                }

        return shift_details

    @staticmethod
    def enrich_business_requirements_with_shift_details(session, business_requirements, company_id):
        """Enrich business requirements with actual shift details from the database."""
        if not business_requirements or 'shifts' not in business_requirements:
            return business_requirements

        # Get all shift IDs
        shift_ids = [shift['shift_id'] for shift in business_requirements['shifts'] if shift.get('shift_id')]

        # Get shift details
        shift_details = BusinessRequirementsProcessor.get_shift_details_by_ids(session, shift_ids, company_id)

        # Enrich the business requirements
        enriched_requirements = business_requirements.copy()
        for shift in enriched_requirements['shifts']:
            shift_id = shift.get('shift_id')
            if shift_id and shift_id in shift_details:
                shift['shift_details'] = shift_details[shift_id]

        return enriched_requirements
