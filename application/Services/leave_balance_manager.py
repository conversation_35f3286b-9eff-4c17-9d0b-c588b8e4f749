from datetime import datetime, date, timedelta
from decimal import Decimal
from flask import current_app as app
from sqlalchemy import and_, or_, func
from application.Models.employees import Employee, LeaveType, LeavePolicy, LeaveBalance, LeaveRequest
from application.Models.employees.leave_audit_log import LeaveAuditLog
from application.Models.country import Country
import uuid

class LeaveBalanceManager:
    """
    Comprehensive leave balance management service.
    Handles edge cases, data validation, and automatic healing.
    """
    
    @classmethod
    def auto_initialize_missing_balances(cls, session, company_id, year=None, employee_id=None):
        """
        Automatically detect and create missing leave balances.
        
        Args:
            session: Database session (tenant database)
            company_id: Company ID for audit logging
            year: Year to initialize balances for (defaults to current year)
            employee_id: Specific employee ID (optional, if None processes all employees)
        """
        if year is None:
            year = datetime.now().year
            
        try:
            # Get all active employees or specific employee
            if employee_id:
                employees = session.query(Employee).filter(
                    Employee.employee_id == employee_id,
                    Employee.status == 'active'
                ).all()
            else:
                employees = session.query(Employee).filter(Employee.status == 'active').all()
            
            if not employees:
                return {"success": False, "message": "No active employees found"}
            
            # Get all leave types
            leave_types = session.query(LeaveType).all()
            if not leave_types:
                return {"success": False, "message": "No leave types found"}
            
            # Get Rwanda country ID for policy lookup
            with app.app_context():
                rwanda = Country.get_country_by_code('RW')
                if not rwanda:
                    return {"success": False, "message": "Rwanda country not found"}
                rwanda_id = rwanda.country_id
            
            created_balances = []
            skipped_employees = []
            
            for employee in employees:
                # Skip employees without hire_date
                if not employee.hire_date:
                    skipped_employees.append({
                        "employee_id": str(employee.employee_id),
                        "name": f"{employee.first_name} {employee.last_name}",
                        "reason": "No hire_date set"
                    })
                    continue
                
                for leave_type in leave_types:
                    # Check if balance already exists
                    existing_balance = session.query(LeaveBalance).filter(
                        LeaveBalance.employee_id == employee.employee_id,
                        LeaveBalance.leave_type_id == leave_type.leave_type_id,
                        LeaveBalance.year == year
                    ).first()
                    
                    if existing_balance:
                        continue  # Balance already exists
                    
                    # Get leave policy for this leave type (considering gender)
                    policy = LeavePolicy.get_policy_for_leave_type_and_country(
                        session, leave_type.leave_type_id, rwanda_id,
                        employee.gender if hasattr(employee, 'gender') else None
                    )
                    
                    if not policy:
                        continue  # No policy found for this leave type
                    
                    # Check eligibility based on service period
                    service_days = cls._calculate_service_days(employee.hire_date, date(year, 12, 31))
                    if service_days < policy.min_service_days:
                        continue  # Employee not eligible yet
                    
                    # Calculate prorated leave if hired mid-year
                    total_days = cls._calculate_prorated_leave(
                        employee.hire_date, year, policy.days_allowed, policy.is_prorated
                    )
                    
                    # Create leave balance
                    balance_data = {
                        'employee_id': employee.employee_id,
                        'leave_type_id': leave_type.leave_type_id,
                        'year': year,
                        'total_days': total_days,
                        'used_days': Decimal('0'),
                        'pending_days': Decimal('0'),
                        'carried_over_days': Decimal('0')
                    }
                    
                    balance = LeaveBalance.create_balance(session, **balance_data)
                    if balance:
                        created_balances.append({
                            "employee_id": str(employee.employee_id),
                            "employee_name": f"{employee.first_name} {employee.last_name}",
                            "leave_type": leave_type.name,
                            "total_days": float(total_days),
                            "year": year
                        })
                        
                        # Log audit trail
                        LeaveAuditLog.log_action(
                            session=session,
                            employee_id=employee.employee_id,
                            action_type='balance_auto_created',
                            entity_type='leave_balance',
                            entity_id=balance.balance_id,
                            new_value=balance.to_dict(),
                            reason=f'Auto-initialized missing balance for {year}',
                            performed_by='system'
                        )
            
            return {
                "success": True,
                "message": f"Successfully created {len(created_balances)} leave balances",
                "created_balances": created_balances,
                "skipped_employees": skipped_employees
            }
            
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error auto-initializing leave balances: {e}")
            return {"success": False, "message": f"Error: {str(e)}"}
    
    @classmethod
    def validate_and_fix_balances(cls, session, company_id, employee_id=None):
        """
        Detect and fix balance inconsistencies.
        
        Args:
            session: Database session
            company_id: Company ID for audit logging
            employee_id: Specific employee ID (optional)
        """
        try:
            # Get balances to validate
            query = session.query(LeaveBalance)
            if employee_id:
                query = query.filter(LeaveBalance.employee_id == employee_id)
            
            balances = query.all()
            fixed_issues = []
            
            for balance in balances:
                issues_found = []
                old_value = balance.to_dict()
                
                # Calculate available days (not stored as attribute in database)
                calculated_available = balance.total_days - balance.used_days - balance.pending_days
                # Note: available_days is calculated in to_dict(), not stored as database field
                
                # Check for negative values
                if balance.used_days < 0:
                    issues_found.append("negative_used_days")
                    balance.used_days = Decimal('0')
                
                if balance.pending_days < 0:
                    issues_found.append("negative_pending_days")
                    balance.pending_days = Decimal('0')

                # Recalculate available days after fixing negative values
                calculated_available = balance.total_days - balance.used_days - balance.pending_days
                if calculated_available < 0:
                    issues_found.append("negative_available_days")
                    # Adjust total_days to make available_days = 0
                    balance.total_days = balance.used_days + balance.pending_days
                
                if issues_found:
                    session.commit()
                    
                    fixed_issues.append({
                        "balance_id": str(balance.balance_id),
                        "employee_id": str(balance.employee_id),
                        "leave_type_id": str(balance.leave_type_id),
                        "year": balance.year,
                        "issues_fixed": issues_found,
                        "old_value": old_value,
                        "new_value": balance.to_dict()
                    })
                    
                    # Log audit trail
                    LeaveAuditLog.log_action(
                        session=session,
                        employee_id=balance.employee_id,
                        action_type='balance_auto_fixed',
                        entity_type='leave_balance',
                        entity_id=balance.balance_id,
                        old_value=old_value,
                        new_value=balance.to_dict(),
                        reason=f'Auto-fixed issues: {", ".join(issues_found)}',
                        performed_by='system'
                    )
            
            return {
                "success": True,
                "message": f"Validated and fixed {len(fixed_issues)} balance issues",
                "fixed_issues": fixed_issues
            }
            
        except Exception as e:
            session.rollback()
            app.logger.error(f"Error validating/fixing balances: {e}")
            return {"success": False, "message": f"Error: {str(e)}"}
    
    @classmethod
    def handle_hire_date_change(cls, session, employee_id, old_hire_date, new_hire_date, performed_by='system'):
        """
        Comprehensive hire date change handling:
        1. If old_hire_date was None and new_hire_date is set: Create missing balances
        2. If hire_date changed: Recalculate existing balances
        3. If new_hire_date is None: Remove balances

        Args:
            session: Database session
            employee_id: Employee ID
            old_hire_date: Previous hire date (can be None)
            new_hire_date: New hire date (can be None)
            performed_by: User ID who made the change
        """
        try:
            current_year = datetime.now().year
            total_actions = []

            # Scenario 1: Employee got hire_date for the first time (old was None)
            if old_hire_date is None and new_hire_date is not None:
                app.logger.info(f"Employee {employee_id} got hire_date for first time: {new_hire_date}")

                # Auto-initialize missing balances
                init_result = cls.auto_initialize_missing_balances(
                    session,
                    company_id=None,  # Will be logged in audit with employee context
                    year=current_year,
                    employee_id=employee_id
                )

                if init_result["success"]:
                    total_actions.extend(init_result.get("created_balances", []))

                    # Log the auto-initialization
                    LeaveAuditLog.log_action(
                        session=session,
                        employee_id=employee_id,
                        action_type='balances_auto_created_hire_date',
                        entity_type='leave_balance',
                        new_value={
                            "trigger": "hire_date_set_first_time",
                            "old_hire_date": None,
                            "new_hire_date": new_hire_date.strftime('%Y-%m-%d') if new_hire_date else None,
                            "balances_created": len(init_result.get("created_balances", []))
                        },
                        reason=f'Auto-created leave balances when hire_date set to {new_hire_date}',
                        performed_by=performed_by
                    )

                return {
                    "success": True,
                    "message": f"Created {len(total_actions)} leave balances for new hire_date",
                    "created_balances": total_actions,
                    "action_type": "auto_initialize"
                }

            # Scenario 2: Hire date changed (both old and new are not None)
            elif old_hire_date is not None and new_hire_date is not None:
                app.logger.info(f"Employee {employee_id} hire_date changed: {old_hire_date} → {new_hire_date}")

                # Get existing balances to recalculate
                balances = session.query(LeaveBalance).filter(
                    LeaveBalance.employee_id == employee_id,
                    LeaveBalance.year == current_year
                ).all()

                recalculated_balances = []

                for balance in balances:
                    old_value = balance.to_dict()

                    # Get leave policy
                    policy = session.query(LeavePolicy).filter(
                        LeavePolicy.leave_type_id == balance.leave_type_id
                    ).first()

                    if not policy:
                        continue

                    # Check new eligibility
                    service_days = cls._calculate_service_days(new_hire_date, date(current_year, 12, 31))
                    if service_days < policy.min_service_days:
                        # Employee no longer eligible - remove balance
                        session.delete(balance)
                        app.logger.info(f"Removed balance {balance.balance_id} - employee no longer eligible")
                        continue

                    # Recalculate prorated leave
                    new_total_days = cls._calculate_prorated_leave(
                        new_hire_date, current_year, policy.days_allowed, policy.is_prorated
                    )

                    # Update balance while preserving used/pending days
                    old_total = balance.total_days
                    balance.total_days = new_total_days

                    # Calculate available days (not stored as database field)
                    calculated_available = new_total_days - balance.used_days - balance.pending_days

                    # Ensure available days don't go negative by adjusting total_days
                    if calculated_available < 0:
                        balance.total_days = balance.used_days + balance.pending_days

                    session.commit()

                    # Calculate final available days for reporting
                    final_available = balance.total_days - balance.used_days - balance.pending_days

                    recalculated_balances.append({
                        "balance_id": str(balance.balance_id),
                        "leave_type_id": str(balance.leave_type_id),
                        "old_total_days": float(old_total),
                        "new_total_days": float(balance.total_days),
                        "available_days": float(final_available)
                    })

                    # Log audit trail
                    LeaveAuditLog.log_action(
                        session=session,
                        employee_id=employee_id,
                        action_type='balance_recalculated_hire_date',
                        entity_type='leave_balance',
                        entity_id=balance.balance_id,
                        old_value=old_value,
                        new_value=balance.to_dict(),
                        reason=f'Hire date changed from {old_hire_date} to {new_hire_date}',
                        performed_by=performed_by
                    )

                return {
                    "success": True,
                    "message": f"Recalculated {len(recalculated_balances)} leave balances",
                    "recalculated_balances": recalculated_balances,
                    "action_type": "recalculate"
                }

            # Scenario 3: Hire date removed (new is None)
            elif old_hire_date is not None and new_hire_date is None:
                app.logger.info(f"Employee {employee_id} hire_date removed")

                # Remove all leave balances
                balances = session.query(LeaveBalance).filter(
                    LeaveBalance.employee_id == employee_id,
                    LeaveBalance.year == current_year
                ).all()

                removed_balances = []
                for balance in balances:
                    removed_balances.append({
                        "balance_id": str(balance.balance_id),
                        "leave_type_id": str(balance.leave_type_id),
                        "total_days": float(balance.total_days)
                    })

                    # Log removal
                    LeaveAuditLog.log_action(
                        session=session,
                        employee_id=employee_id,
                        action_type='balance_removed_hire_date_cleared',
                        entity_type='leave_balance',
                        entity_id=balance.balance_id,
                        old_value=balance.to_dict(),
                        reason=f'Hire date removed (was {old_hire_date})',
                        performed_by=performed_by
                    )

                    session.delete(balance)

                session.commit()

                return {
                    "success": True,
                    "message": f"Removed {len(removed_balances)} leave balances",
                    "removed_balances": removed_balances,
                    "action_type": "remove"
                }

            # Scenario 4: Both None (no change needed)
            else:
                return {
                    "success": True,
                    "message": "No hire date change detected",
                    "action_type": "no_change"
                }

        except Exception as e:
            session.rollback()
            app.logger.error(f"Error handling hire date change: {e}")
            return {"success": False, "message": f"Error: {str(e)}"}

    @classmethod
    def _calculate_service_days(cls, hire_date, end_date):
        """Calculate number of service days between hire date and end date."""
        if not hire_date or not end_date:
            return 0

        if hire_date > end_date:
            return 0

        return (end_date - hire_date).days

    @classmethod
    def _calculate_prorated_leave(cls, hire_date, year, annual_days, is_prorated):
        """
        Calculate prorated leave for employees hired mid-year.

        Args:
            hire_date: Employee hire date
            year: Year to calculate for
            annual_days: Full year leave entitlement
            is_prorated: Whether to prorate based on hire date
        """
        if not hire_date or not is_prorated:
            return Decimal(str(annual_days))

        # If hired before the year started, give full entitlement
        year_start = date(year, 1, 1)
        if hire_date <= year_start:
            return Decimal(str(annual_days))

        # If hired after the year ended, give zero
        year_end = date(year, 12, 31)
        if hire_date > year_end:
            return Decimal('0')

        # Calculate prorated amount based on remaining months
        months_remaining = 12 - hire_date.month + 1
        if hire_date.day > 15:  # If hired after 15th, don't count that month
            months_remaining -= 1

        prorated_days = (Decimal(str(annual_days)) * Decimal(str(months_remaining))) / Decimal('12')
        return prorated_days.quantize(Decimal('0.5'))  # Round to nearest 0.5
