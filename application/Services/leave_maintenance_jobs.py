"""
Leave Management Maintenance Jobs
Scheduled jobs for maintaining leave balance data integrity and compliance.
"""

import schedule
import time
import threading
from datetime import datetime, timedelta
from flask import current_app as app
from application.Models.company import Company
from application.Services.leave_balance_manager import LeaveBalanceManager
from application.Models.employees.leave_audit_log import LeaveAuditLog
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LeaveMaintenanceJobs:
    """
    Scheduled maintenance jobs for leave management system.
    Handles data validation, cleanup, and compliance checks.
    """
    
    def __init__(self):
        self.is_running = False
        self.scheduler_thread = None
    
    def start_scheduler(self):
        """Start the maintenance job scheduler."""
        if self.is_running:
            logger.warning("Maintenance scheduler is already running")
            return
        
        self.is_running = True
        
        # Schedule jobs
        schedule.every().day.at("02:00").do(self.daily_balance_validation)
        schedule.every().sunday.at("03:00").do(self.weekly_data_cleanup)
        schedule.every().month.do(self.monthly_compliance_check)
        schedule.every().day.at("01:00").do(self.daily_auto_heal_balances)
        
        # Start scheduler in a separate thread
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        logger.info("Leave maintenance scheduler started")
    
    def stop_scheduler(self):
        """Stop the maintenance job scheduler."""
        self.is_running = False
        schedule.clear()
        logger.info("Leave maintenance scheduler stopped")
    
    def _run_scheduler(self):
        """Run the scheduler loop."""
        while self.is_running:
            schedule.run_pending()
            time.sleep(60)  # Check every minute
    
    def daily_balance_validation(self):
        """
        Daily job to validate leave balances across all companies.
        Checks for inconsistencies and fixes minor issues.
        """
        logger.info("Starting daily balance validation")
        
        try:
            with app.app_context():
                companies = Company.get_all_companies()
                
                total_issues_found = 0
                total_issues_fixed = 0
                
                for company in companies:
                    try:
                        database_name = company.database_name
                        
                        from app import db_connection
                        with db_connection.get_session(database_name) as session:
                            # Validate and fix balance inconsistencies
                            result = LeaveBalanceManager.validate_and_fix_balances(
                                session, company.company_id
                            )
                            
                            if result["success"]:
                                issues_fixed = len(result.get("fixed_issues", []))
                                total_issues_fixed += issues_fixed
                                
                                if issues_fixed > 0:
                                    logger.info(f"Fixed {issues_fixed} balance issues for company {company.company_name}")
                            else:
                                logger.error(f"Error validating balances for company {company.company_name}: {result['message']}")
                    
                    except Exception as e:
                        logger.error(f"Error processing company {company.company_name}: {e}")
                        continue
                
                logger.info(f"Daily validation completed. Fixed {total_issues_fixed} issues across {len(companies)} companies")
        
        except Exception as e:
            logger.error(f"Error in daily balance validation: {e}")
    
    def daily_auto_heal_balances(self):
        """
        Daily job to automatically create missing leave balances.
        Focuses on employees who recently got hire dates set.
        """
        logger.info("Starting daily auto-heal balances")
        
        try:
            with app.app_context():
                companies = Company.get_all_companies()
                current_year = datetime.now().year
                
                total_balances_created = 0
                
                for company in companies:
                    try:
                        database_name = company.database_name
                        
                        from app import db_connection
                        with db_connection.get_session(database_name) as session:
                            # Auto-initialize missing balances
                            result = LeaveBalanceManager.auto_initialize_missing_balances(
                                session, company.company_id, current_year
                            )
                            
                            if result["success"]:
                                balances_created = len(result.get("created_balances", []))
                                total_balances_created += balances_created
                                
                                if balances_created > 0:
                                    logger.info(f"Created {balances_created} missing balances for company {company.company_name}")
                            else:
                                logger.error(f"Error auto-healing balances for company {company.company_name}: {result['message']}")
                    
                    except Exception as e:
                        logger.error(f"Error processing company {company.company_name}: {e}")
                        continue
                
                logger.info(f"Auto-heal completed. Created {total_balances_created} balances across {len(companies)} companies")
        
        except Exception as e:
            logger.error(f"Error in daily auto-heal balances: {e}")
    
    def weekly_data_cleanup(self):
        """
        Weekly job for data cleanup and maintenance.
        Removes old audit logs, detects duplicates, and optimizes data.
        """
        logger.info("Starting weekly data cleanup")
        
        try:
            with app.app_context():
                companies = Company.get_all_companies()
                
                total_duplicates_removed = 0
                total_old_logs_cleaned = 0
                
                for company in companies:
                    try:
                        database_name = company.database_name
                        
                        from app import db_connection
                        with db_connection.get_session(database_name) as session:
                            # Remove duplicate balances
                            dup_result = LeaveBalanceManager.remove_duplicate_balances(session)
                            if dup_result["success"]:
                                duplicates_removed = len(dup_result.get("removed_balances", []))
                                total_duplicates_removed += duplicates_removed
                                
                                if duplicates_removed > 0:
                                    logger.info(f"Removed {duplicates_removed} duplicate balances for company {company.company_name}")
                            
                            # Clean old audit logs (older than 2 years)
                            cutoff_date = datetime.now() - timedelta(days=730)
                            old_logs = session.query(LeaveAuditLog).filter(
                                LeaveAuditLog.created_at < cutoff_date
                            ).all()
                            
                            logs_to_delete = len(old_logs)
                            if logs_to_delete > 0:
                                for log in old_logs:
                                    session.delete(log)
                                session.commit()
                                total_old_logs_cleaned += logs_to_delete
                                logger.info(f"Cleaned {logs_to_delete} old audit logs for company {company.company_name}")
                    
                    except Exception as e:
                        logger.error(f"Error cleaning data for company {company.company_name}: {e}")
                        continue
                
                logger.info(f"Weekly cleanup completed. Removed {total_duplicates_removed} duplicates and {total_old_logs_cleaned} old logs")
        
        except Exception as e:
            logger.error(f"Error in weekly data cleanup: {e}")
    
    def monthly_compliance_check(self):
        """
        Monthly job to check Rwanda labor law compliance.
        Verifies leave policies, carryover limits, and accrual calculations.
        """
        logger.info("Starting monthly compliance check")
        
        try:
            with app.app_context():
                companies = Company.get_all_companies()
                
                compliance_issues = []
                
                for company in companies:
                    try:
                        database_name = company.database_name
                        
                        from app import db_connection
                        with db_connection.get_session(database_name) as session:
                            company_issues = self._check_company_compliance(session, company)
                            if company_issues:
                                compliance_issues.extend(company_issues)
                    
                    except Exception as e:
                        logger.error(f"Error checking compliance for company {company.company_name}: {e}")
                        continue
                
                if compliance_issues:
                    logger.warning(f"Found {len(compliance_issues)} compliance issues across companies")
                    # Here you could send alerts to administrators
                else:
                    logger.info("Monthly compliance check completed - no issues found")
        
        except Exception as e:
            logger.error(f"Error in monthly compliance check: {e}")
    
    def _check_company_compliance(self, session, company):
        """Check compliance issues for a specific company."""
        issues = []
        
        try:
            from application.Models.employees import LeaveBalance, LeavePolicy, Employee
            from datetime import date
            
            current_year = datetime.now().year
            
            # Check for excessive carryover (Rwanda law: max 6 days annual leave)
            excessive_carryover = session.query(LeaveBalance).join(
                LeavePolicy, LeaveBalance.leave_type_id == LeavePolicy.leave_type_id
            ).filter(
                LeaveBalance.year == current_year,
                LeaveBalance.carried_over_days > 6,
                LeavePolicy.max_carryover == 6  # Annual leave policy
            ).all()
            
            for balance in excessive_carryover:
                issues.append({
                    "company_id": company.company_id,
                    "company_name": company.company_name,
                    "issue_type": "excessive_carryover",
                    "employee_id": str(balance.employee_id),
                    "carried_over_days": float(balance.carried_over_days),
                    "max_allowed": 6
                })
            
            # Check for employees with negative balances
            negative_balances = session.query(LeaveBalance).filter(
                LeaveBalance.year == current_year,
                LeaveBalance.available_days < 0
            ).all()
            
            for balance in negative_balances:
                issues.append({
                    "company_id": company.company_id,
                    "company_name": company.company_name,
                    "issue_type": "negative_balance",
                    "employee_id": str(balance.employee_id),
                    "available_days": float(balance.available_days)
                })
            
            # Check for employees without leave balances who should have them
            employees_without_balances = session.query(Employee).filter(
                Employee.status == 'active',
                Employee.hire_date.isnot(None),
                Employee.hire_date <= date(current_year, 12, 31),
                ~Employee.employee_id.in_(
                    session.query(LeaveBalance.employee_id).filter(
                        LeaveBalance.year == current_year
                    )
                )
            ).all()
            
            for employee in employees_without_balances:
                issues.append({
                    "company_id": company.company_id,
                    "company_name": company.company_name,
                    "issue_type": "missing_balances",
                    "employee_id": str(employee.employee_id),
                    "employee_name": f"{employee.first_name} {employee.last_name}",
                    "hire_date": employee.hire_date.strftime('%Y-%m-%d')
                })
        
        except Exception as e:
            logger.error(f"Error checking compliance for company {company.company_name}: {e}")
        
        return issues
    
    def run_manual_maintenance(self, company_id=None, actions=None):
        """
        Run maintenance jobs manually for testing or emergency fixes.
        
        Args:
            company_id: Specific company ID (optional)
            actions: List of actions to run (optional, defaults to all)
        """
        if actions is None:
            actions = ['validate', 'auto_heal', 'cleanup', 'compliance']
        
        logger.info(f"Running manual maintenance with actions: {actions}")
        
        try:
            if 'validate' in actions:
                self.daily_balance_validation()
            
            if 'auto_heal' in actions:
                self.daily_auto_heal_balances()
            
            if 'cleanup' in actions:
                self.weekly_data_cleanup()
            
            if 'compliance' in actions:
                self.monthly_compliance_check()
            
            logger.info("Manual maintenance completed successfully")
        
        except Exception as e:
            logger.error(f"Error in manual maintenance: {e}")

# Global instance for the maintenance scheduler
maintenance_scheduler = LeaveMaintenanceJobs()

def start_leave_maintenance():
    """Start the leave maintenance scheduler."""
    maintenance_scheduler.start_scheduler()

def stop_leave_maintenance():
    """Stop the leave maintenance scheduler."""
    maintenance_scheduler.stop_scheduler()
