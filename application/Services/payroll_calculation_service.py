from flask import current_app as app
from application.Models import PayrollPolicy, TaxBracket, DeductionPolicy
from application.Models.employees import EmployeeSalary, EmployeeAllowance
from application.Models.company import Company
from application.Models.calculation_rule import CalculationRule
from datetime import datetime, date
import json
from decimal import Decimal, ROUND_HALF_UP, getcontext
from enum import Enum

# Set global decimal context for financial calculations
getcontext().prec = 28  # High precision
getcontext().rounding = ROUND_HALF_UP  # Standard financial rounding

class PayrollCalculationScenario(Enum):
    """Enumeration of different payroll calculation scenarios"""
    BASIC_PLUS_ALLOWANCES = "basic_plus_allowances"  # Basic + individual allowances known
    GROSS_PLUS_ALLOWANCES = "gross_plus_allowances"  # Gross + individual allowances known
    NET_PLUS_ALLOWANCES = "net_plus_allowances"      # Net + individual allowances known
    TOTAL_COST_BREAKDOWN = "total_cost_breakdown"    # Total staff cost known, need to work backwards


def round_currency(amount, places=2):
    """Round currency to specified decimal places using financial rounding"""
    if isinstance(amount, (int, float)):
        amount = Decimal(str(amount))
    elif not isinstance(amount, Decimal):
        amount = Decimal(str(amount))
    return amount.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)


def ensure_decimal(value):
    """Ensure a value is a Decimal for financial calculations"""
    if isinstance(value, Decimal):
        return value
    elif isinstance(value, (int, float)):
        return Decimal(str(value))
    elif isinstance(value, str):
        return Decimal(value)
    else:
        return Decimal('0')


class PayrollCalculationService:
    """Service for calculating payroll based on Rwanda tax laws and company policies."""

    def __init__(self, company_id):
        self.company_id = company_id
        self.company = Company.get_company_by_id(company_id)
        if not self.company:
            raise ValueError(f"Company {company_id} not found")
        self.country_id = self.company.country_id
        self.calculation_rules = {}  # Cache for calculation rules

    def calculate_employee_payroll(self, session, employee_id, pay_period_start, pay_period_end):
        """Calculate complete payroll for an employee for a specific pay period using Decimal arithmetic."""
        try:
            # Get employee salary
            salary = EmployeeSalary.get_current_salary(session, employee_id, pay_period_start)
            if not salary:
                raise ValueError(f"No active salary found for employee {employee_id}")

            # Calculate gross pay using Decimal
            gross_pay = self._calculate_gross_pay(session, salary, pay_period_start, pay_period_end)
            basic_salary = ensure_decimal(salary.basic_salary)

            # Calculate PAYE tax
            tax_calculation = self._calculate_paye_tax(salary.employee_type_id, gross_pay, pay_period_start)

            # Calculate deductions using Decimal
            deductions_calculation = self._calculate_deductions(
                session, employee_id, salary.employee_type_id, gross_pay,
                basic_salary, pay_period_start, tax_calculation['total_tax']
            )

            # Calculate net pay using Decimal
            total_tax = ensure_decimal(tax_calculation['total_tax'])
            total_employee_deductions = Decimal('0')
            for d in deductions_calculation['deductions'].values():
                total_employee_deductions += ensure_decimal(d['employee_amount'])

            net_pay = round_currency(gross_pay - total_tax - total_employee_deductions)

            # Get allowances breakdown using Decimal
            allowances = EmployeeAllowance.get_active_allowances(session, employee_id, pay_period_start)
            allowances_breakdown = {}
            for allowance in allowances:
                allowances_breakdown[allowance.allowance_type.code] = {
                    'name': allowance.allowance_type.name,
                    'amount': ensure_decimal(allowance.amount),
                    'is_taxable': allowance.allowance_type.is_taxable,
                    'is_pensionable': allowance.allowance_type.is_pensionable
                }

            return {
                'employee_id': employee_id,
                'employee_type_id': salary.employee_type_id,
                'basic_salary': basic_salary,
                'allowances': allowances_breakdown,
                'gross_pay': gross_pay,
                'tax_calculations': tax_calculation,
                'employee_deductions': deductions_calculation['deductions'],
                'employer_contributions': deductions_calculation['employer_contributions'],
                'net_pay': net_pay,
                'calculation_date': datetime.now().isoformat()
            }

        except Exception as e:
            app.logger.error(f"Error calculating payroll for employee {employee_id}: {e}")
            raise

    def calculate_payroll_dynamic(self, session, employee_id, scenario_type, known_values, pay_period_start, pay_period_end):
        """
        Calculate payroll based on different scenarios and known values.

        Args:
            session: Database session
            employee_id: Employee ID
            scenario_type: PayrollCalculationScenario enum value
            known_values: Dictionary of known values (varies by scenario)
            pay_period_start: Start date of pay period
            pay_period_end: End date of pay period

        Returns:
            Complete payroll calculation result
        """
        try:
            if scenario_type == PayrollCalculationScenario.BASIC_PLUS_ALLOWANCES.value:
                return self._calculate_forward_from_basic(session, employee_id, known_values, pay_period_start, pay_period_end)
            elif scenario_type == PayrollCalculationScenario.GROSS_PLUS_ALLOWANCES.value:
                return self._calculate_from_gross(session, employee_id, known_values, pay_period_start, pay_period_end)
            elif scenario_type == PayrollCalculationScenario.NET_PLUS_ALLOWANCES.value:
                return self._calculate_backward_from_net(session, employee_id, known_values, pay_period_start, pay_period_end)
            elif scenario_type == PayrollCalculationScenario.TOTAL_COST_BREAKDOWN.value:
                return self._calculate_backward_from_total_cost(session, employee_id, known_values, pay_period_start, pay_period_end)
            else:
                raise ValueError(f"Unknown calculation scenario: {scenario_type}")

        except Exception as e:
            app.logger.error(f"Error in dynamic payroll calculation for employee {employee_id}: {e}")
            raise

    def _calculate_forward_from_basic(self, session, employee_id, known_values, pay_period_start, pay_period_end):
        """Calculate payroll forward from basic salary and allowances."""
        # Get employee salary record
        salary = EmployeeSalary.get_current_salary(session, employee_id, pay_period_start)
        if not salary:
            raise ValueError(f"No active salary found for employee {employee_id}")

        # Use known basic salary
        basic_salary = ensure_decimal(known_values.get('basic_salary', salary.basic_salary))

        # Calculate gross pay with known allowances
        gross_pay = basic_salary
        allowances_breakdown = {}

        for allowance_code, amount in known_values.items():
            if allowance_code != 'basic_salary':
                allowance_amount = ensure_decimal(amount)
                gross_pay += allowance_amount
                allowances_breakdown[allowance_code] = {
                    'name': allowance_code.title(),
                    'amount': allowance_amount,
                    'is_taxable': True,  # Assume taxable unless specified
                    'is_pensionable': True
                }

        # Continue with standard calculation
        return self._complete_payroll_calculation(
            session, employee_id, salary.employee_type_id, basic_salary,
            gross_pay, allowances_breakdown, pay_period_start
        )

    def _calculate_from_gross(self, session, employee_id, known_values, pay_period_start, pay_period_end):
        """Calculate payroll from known gross salary and allowances."""
        # Get employee salary record
        salary = EmployeeSalary.get_current_salary(session, employee_id, pay_period_start)
        if not salary:
            raise ValueError(f"No active salary found for employee {employee_id}")

        # Use known gross salary
        gross_pay = ensure_decimal(known_values.get('gross_salary'))

        # Calculate basic salary by subtracting known allowances from gross
        total_allowances = Decimal('0')
        allowances_breakdown = {}

        for allowance_code, amount in known_values.items():
            if allowance_code != 'gross_salary':
                allowance_amount = ensure_decimal(amount)
                total_allowances += allowance_amount
                allowances_breakdown[allowance_code] = {
                    'name': allowance_code.title(),
                    'amount': allowance_amount,
                    'is_taxable': True,  # Assume taxable unless specified
                    'is_pensionable': True
                }

        basic_salary = gross_pay - total_allowances

        # Continue with standard calculation
        return self._complete_payroll_calculation(
            session, employee_id, salary.employee_type_id, basic_salary,
            gross_pay, allowances_breakdown, pay_period_start
        )

    def _calculate_backward_from_net(self, session, employee_id, known_values, pay_period_start, pay_period_end):
        """Calculate payroll backward from net salary using binary search."""
        target_net = ensure_decimal(known_values['net_salary'])
        known_allowances = {k: ensure_decimal(v) for k, v in known_values.items() if k != 'net_salary'}

        def calculate_net_for_basic(basic_salary_guess):
            temp_values = {'basic_salary': basic_salary_guess, **known_allowances}
            result = self._calculate_forward_from_basic(session, employee_id, temp_values, pay_period_start, pay_period_end)
            return ensure_decimal(result['net_pay'])

        # Binary search to find correct basic salary
        basic_salary = self._binary_search_for_target_decimal(
            calculate_net_for_basic,
            target_net,
            Decimal('10000'),
            Decimal('5000000')
        )

        # Calculate final result with found basic salary
        final_values = {'basic_salary': basic_salary, **known_allowances}
        return self._calculate_forward_from_basic(session, employee_id, final_values, pay_period_start, pay_period_end)

    def _calculate_backward_from_total_cost(self, session, employee_id, known_values, pay_period_start, pay_period_end):
        """Calculate payroll backward from total staff cost."""
        target_total_cost = ensure_decimal(known_values['total_staff_cost'])
        known_allowances = known_values.get('allowances', {})
        known_allowances = {k: ensure_decimal(v) for k, v in known_allowances.items()}

        def calculate_total_cost_for_basic(basic_salary_guess):
            temp_values = {'basic_salary': basic_salary_guess, **known_allowances}
            result = self._calculate_forward_from_basic(session, employee_id, temp_values, pay_period_start, pay_period_end)

            # Total cost = gross + employer contributions
            total_employer_contributions = Decimal('0')
            for contrib in result['employer_contributions'].values():
                total_employer_contributions += ensure_decimal(contrib['employer_amount'])

            return ensure_decimal(result['gross_pay']) + total_employer_contributions

        basic_salary = self._binary_search_for_target_decimal(
            calculate_total_cost_for_basic,
            target_total_cost,
            Decimal('10000'),
            Decimal('5000000')
        )

        final_values = {'basic_salary': basic_salary, **known_allowances}
        return self._calculate_forward_from_basic(session, employee_id, final_values, pay_period_start, pay_period_end)

    def _binary_search_for_target_decimal(self, calculation_func, target_value, min_value, max_value, tolerance=None):
        """Binary search using Decimal precision for financial calculations."""
        if tolerance is None:
            tolerance = Decimal('0.01')

        target = ensure_decimal(target_value)
        min_val = ensure_decimal(min_value)
        max_val = ensure_decimal(max_value)

        max_iterations = 100  # Prevent infinite loops
        iteration = 0

        while max_val - min_val > tolerance and iteration < max_iterations:
            mid_val = (min_val + max_val) / Decimal('2')
            result = calculation_func(mid_val)

            if abs(result - target) <= tolerance:
                return round_currency(mid_val)
            elif result < target:
                min_val = mid_val
            else:
                max_val = mid_val

            iteration += 1

        return round_currency((min_val + max_val) / Decimal('2'))

    def _calculate_gross_pay(self, session, salary, pay_period_start, pay_period_end):
        """Calculate gross pay including basic salary and allowances using Decimal arithmetic."""
        basic_salary = ensure_decimal(salary.basic_salary)

        # Get taxable allowances
        taxable_allowances = self._get_taxable_allowances_total_decimal(session, salary.employee_id, pay_period_start)

        gross_pay = basic_salary + taxable_allowances

        app.logger.debug(f"Gross pay calculation: Basic={basic_salary}, Allowances={taxable_allowances}, Total={gross_pay}")
        return round_currency(gross_pay)

    def _calculate_paye_tax(self, employee_type_id, gross_pay, calculation_date):
        """Calculate PAYE tax based on Rwanda tax brackets using Decimal arithmetic."""
        try:
            gross_pay = ensure_decimal(gross_pay)

            # Get PAYE policy for employee type
            paye_policy = PayrollPolicy.get_active_policy(
                policy_type_code='PAYE',
                country_id=self.country_id,
                employee_type_id=employee_type_id,
                date=calculation_date
            )

            if not paye_policy:
                app.logger.warning(f"No PAYE policy found for employee type {employee_type_id}")
                return {'total_tax': Decimal('0'), 'brackets': [], 'policy_version': None}

            # Get tax brackets
            tax_brackets = TaxBracket.get_brackets_for_policy(paye_policy.policy_id)

            total_tax = Decimal('0')
            brackets_breakdown = []

            for bracket in tax_brackets:
                bracket_min = ensure_decimal(bracket.min_amount)
                bracket_max = ensure_decimal(bracket.max_amount) if bracket.max_amount else None
                bracket_rate = ensure_decimal(bracket.tax_rate)

                if gross_pay > bracket_min:
                    # Calculate taxable amount in this bracket
                    if bracket_max:
                        taxable_in_bracket = min(gross_pay, bracket_max) - bracket_min
                    else:
                        taxable_in_bracket = gross_pay - bracket_min

                    if taxable_in_bracket > Decimal('0'):
                        bracket_tax = round_currency(taxable_in_bracket * bracket_rate)
                        total_tax += bracket_tax

                        brackets_breakdown.append({
                            'bracket_order': bracket.bracket_order,
                            'min_amount': bracket_min,
                            'max_amount': bracket_max,
                            'tax_rate': bracket_rate,
                            'taxable_amount': taxable_in_bracket,
                            'tax_amount': bracket_tax
                        })

            return {
                'total_tax': round_currency(total_tax),
                'brackets': brackets_breakdown,
                'policy_version': paye_policy.version_number,
                'policy_id': str(paye_policy.policy_id)
            }

        except Exception as e:
            app.logger.error(f"Error calculating PAYE tax: {e}")
            raise

    def _calculate_deductions(self, session, employee_id, employee_type_id, gross_pay, basic_salary, calculation_date, tax_amount):
        """Calculate all deductions using configurable rules and Decimal arithmetic."""
        try:
            # Ensure all inputs are Decimal
            gross_pay = ensure_decimal(gross_pay)
            basic_salary = ensure_decimal(basic_salary)
            tax_amount = ensure_decimal(tax_amount)

            # Get transport allowance for proper calculation bases
            allowances = EmployeeAllowance.get_active_allowances(session, employee_id, calculation_date)
            transport_allowance = Decimal('0')
            for allowance in allowances:
                if allowance.allowance_type and allowance.allowance_type.code == 'TRANSPORT':
                    transport_allowance = ensure_decimal(allowance.amount)
                    break

            # Get all active deduction policies
            deduction_policies = DeductionPolicy.get_active_policies_for_country(
                country_id=self.country_id,
                employee_type_id=employee_type_id,
                date=calculation_date
            )

            employee_deductions = {}
            employer_contributions = {}

            # Calculate net salary before CBHI (for CBHI calculation)
            total_employee_deductions_before_cbhi = Decimal('0')

            # First pass: calculate all deductions except CBHI
            for policy in deduction_policies:
                deduction_name = policy.deduction_type.name
                deduction_code = policy.deduction_type.code

                # Skip CBHI for first pass
                if deduction_code == 'CBHI':
                    continue

                # Get calculation base using configurable rules
                calculation_base = self._get_calculation_base_dynamic(
                    gross_pay, deduction_code, basic_salary, transport_allowance,
                    None, calculation_date
                )

                # Calculate employee contribution
                employee_amount = Decimal('0')
                if policy.employee_rate and policy.deduction_type.has_employee_contribution:
                    employee_rate = ensure_decimal(policy.employee_rate)
                    employee_amount = round_currency(calculation_base * employee_rate)
                    total_employee_deductions_before_cbhi += employee_amount

                # Calculate employer contribution
                employer_amount = Decimal('0')
                if policy.employer_rate and policy.deduction_type.has_employer_contribution:
                    employer_rate = ensure_decimal(policy.employer_rate)
                    employer_amount = round_currency(calculation_base * employer_rate)

                if employee_amount > Decimal('0'):
                    employee_deductions[deduction_code] = {
                        'name': deduction_name,
                        'calculation_base': calculation_base,
                        'rate': ensure_decimal(policy.employee_rate),
                        'employee_amount': employee_amount,
                        'policy_version': policy.version_number
                    }

                if employer_amount > Decimal('0'):
                    employer_contributions[deduction_code] = {
                        'name': deduction_name,
                        'calculation_base': calculation_base,
                        'rate': ensure_decimal(policy.employer_rate),
                        'employer_amount': employer_amount,
                        'policy_version': policy.version_number
                    }

            # Second pass: calculate CBHI based on net salary
            net_salary_before_cbhi = gross_pay - tax_amount - total_employee_deductions_before_cbhi

            for policy in deduction_policies:
                deduction_code = policy.deduction_type.code

                if deduction_code == 'CBHI':
                    deduction_name = policy.deduction_type.name
                    calculation_base = self._get_calculation_base_dynamic(
                        gross_pay, deduction_code, basic_salary, transport_allowance,
                        net_salary_before_cbhi, calculation_date
                    )

                    # Calculate employee contribution (CBHI is typically employee-only)
                    employee_amount = Decimal('0')
                    if policy.employee_rate and policy.deduction_type.has_employee_contribution:
                        employee_rate = ensure_decimal(policy.employee_rate)
                        employee_amount = round_currency(calculation_base * employee_rate)

                    if employee_amount > Decimal('0'):
                        employee_deductions[deduction_code] = {
                            'name': deduction_name,
                            'calculation_base': calculation_base,
                            'rate': ensure_decimal(policy.employee_rate),
                            'employee_amount': employee_amount,
                            'policy_version': policy.version_number
                        }

            # Add loan deductions
            loan_deductions, total_loan_amount = self._calculate_loan_deductions(
                session, employee_id, calculation_date
            )

            # Merge loan deductions with other deductions
            employee_deductions.update(loan_deductions)

            return {
                'deductions': employee_deductions,
                'employer_contributions': employer_contributions,
                'total_loan_deductions': total_loan_amount
            }

        except Exception as e:
            app.logger.error(f"Error calculating deductions: {e}")
            raise

    def _get_calculation_base_dynamic(self, gross_pay, deduction_code, basic_salary=None,
                                    transport_allowance=None, net_salary_before_cbhi=None, calculation_date=None):
        """Get calculation base using configurable rules from database."""
        # Ensure all inputs are Decimal
        gross_pay = ensure_decimal(gross_pay)
        basic_salary = ensure_decimal(basic_salary) if basic_salary else Decimal('0')
        transport_allowance = ensure_decimal(transport_allowance) if transport_allowance else Decimal('0')
        net_salary_before_cbhi = ensure_decimal(net_salary_before_cbhi) if net_salary_before_cbhi else Decimal('0')

        # Try to get calculation rule from database
        calc_rule = CalculationRule.get_active_rule(
            country_id=self.country_id,
            calculation_type='DEDUCTION',
            type_code=deduction_code,
            date=calculation_date
        )

        if calc_rule:
            calculation_base_type = calc_rule.calculation_base
        else:
            # Fallback to deduction type's default or hardcoded rules for backward compatibility
            from application.Models.deduction_type import DeductionType
            deduction_type = DeductionType.get_by_code(self.country_id, deduction_code)
            if deduction_type:
                calculation_base_type = deduction_type.calculation_base
            else:
                # Final fallback - use legacy hardcoded rules
                if deduction_code in ['PENSION']:
                    calculation_base_type = 'GROSS_SALARY'
                elif deduction_code in ['MATERNITY', 'OCC_HAZARD']:
                    calculation_base_type = 'GROSS_MINUS_TRANSPORT'
                elif deduction_code == 'CBHI':
                    calculation_base_type = 'NET_BEFORE_CBHI'
                else:
                    calculation_base_type = 'GROSS_SALARY'

        # Apply the calculation base
        return self._apply_calculation_base(
            calculation_base_type, gross_pay, basic_salary,
            transport_allowance, net_salary_before_cbhi
        )

    def _apply_calculation_base(self, calculation_base_type, gross_pay, basic_salary,
                              transport_allowance, net_salary_before_cbhi):
        """Apply the calculation base type to get the actual calculation amount."""
        if calculation_base_type == 'GROSS_SALARY':
            # For pension: full gross salary including transport
            return gross_pay
        elif calculation_base_type == 'GROSS_MINUS_TRANSPORT':
            # For maternity and occupational hazard: gross - transport allowance
            return gross_pay - transport_allowance
        elif calculation_base_type in ['NET_SALARY', 'NET_BEFORE_CBHI']:
            # For CBHI: net salary after PAYE and other deductions
            return net_salary_before_cbhi if net_salary_before_cbhi else gross_pay
        elif calculation_base_type == 'BASIC_SALARY':
            return basic_salary if basic_salary else gross_pay
        else:
            return gross_pay

    def _get_calculation_base(self, gross_pay, calculation_base_type, basic_salary=None, transport_allowance=0, net_salary_before_cbhi=None):
        """Legacy method - kept for backward compatibility."""
        # Convert to Decimal and use new method
        gross_pay = ensure_decimal(gross_pay)
        basic_salary = ensure_decimal(basic_salary) if basic_salary else Decimal('0')
        transport_allowance = ensure_decimal(transport_allowance)
        net_salary_before_cbhi = ensure_decimal(net_salary_before_cbhi) if net_salary_before_cbhi else Decimal('0')

        return self._apply_calculation_base(
            calculation_base_type, gross_pay, basic_salary,
            transport_allowance, net_salary_before_cbhi
        )

    def _get_taxable_allowances_total_decimal(self, session, employee_id, date):
        """Get total taxable allowances as Decimal."""
        return EmployeeAllowance.get_taxable_allowances_total_decimal(session, employee_id, date)

    def _complete_payroll_calculation(self, session, employee_id, employee_type_id, basic_salary,
                                    gross_pay, allowances_breakdown, calculation_date):
        """Complete the payroll calculation with tax and deductions."""
        # Calculate PAYE tax
        tax_calculation = self._calculate_paye_tax(employee_type_id, gross_pay, calculation_date)

        # Calculate deductions
        deductions_calculation = self._calculate_deductions(
            session, employee_id, employee_type_id, gross_pay,
            basic_salary, calculation_date, tax_calculation['total_tax']
        )

        # Calculate net pay
        total_tax = ensure_decimal(tax_calculation['total_tax'])
        total_employee_deductions = Decimal('0')
        for d in deductions_calculation['deductions'].values():
            total_employee_deductions += ensure_decimal(d['employee_amount'])

        net_pay = round_currency(gross_pay - total_tax - total_employee_deductions)

        return {
            'employee_id': employee_id,
            'employee_type_id': employee_type_id,
            'basic_salary': basic_salary,
            'allowances': allowances_breakdown,
            'gross_pay': gross_pay,
            'tax_calculations': tax_calculation,
            'employee_deductions': deductions_calculation['deductions'],
            'employer_contributions': deductions_calculation['employer_contributions'],
            'net_pay': net_pay,
            'calculation_date': datetime.now().isoformat()
        }

    def decimal_to_float_for_json(self, calculation_result):
        """Convert Decimal values to float only for JSON serialization."""
        def convert_value(value):
            if isinstance(value, Decimal):
                return float(value)
            elif isinstance(value, dict):
                return {k: convert_value(v) for k, v in value.items()}
            elif isinstance(value, list):
                return [convert_value(item) for item in value]
            return value

        return convert_value(calculation_result)

    def _calculate_loan_deductions(self, session, employee_id, calculation_date):
        """Calculate loan deductions for payroll."""
        try:
            from application.Models.employees.employee_loan import EmployeeLoan
            from application.Models.employees.loan_repayment_schedule import LoanRepaymentSchedule

            # Get active loans for this employee
            active_loans = EmployeeLoan.get_loans_for_payroll_deduction(
                session, employee_id, calculation_date
            )

            loan_deductions = {}
            total_loan_amount = Decimal('0')

            for loan in active_loans:
                # Get current month's repayment
                repayment = loan.get_current_repayment(session, calculation_date)

                if repayment and repayment.status == 'PENDING':
                    deduction_amount = ensure_decimal(repayment.amount)
                    total_loan_amount += deduction_amount

                    loan_deductions[f'LOAN_{loan.loan_number}'] = {
                        'name': f'{loan.loan_type.name} - {loan.loan_number}',
                        'loan_id': str(loan.loan_id),
                        'loan_number': loan.loan_number,
                        'installment_number': repayment.installment_number,
                        'employee_amount': deduction_amount,
                        'outstanding_balance': ensure_decimal(loan.outstanding_balance),
                        'due_date': repayment.due_date.strftime('%Y-%m-%d'),
                        'schedule_id': str(repayment.schedule_id)
                    }

            app.logger.debug(f"Calculated loan deductions for employee {employee_id}: {total_loan_amount}")
            return loan_deductions, total_loan_amount

        except Exception as e:
            app.logger.error(f"Error calculating loan deductions: {e}")
            return {}, Decimal('0')

    def process_loan_deductions(self, session, employee_id, payslip_id, loan_deductions, payroll_date):
        """Process loan deductions and update loan records."""
        try:
            from application.Models.employees.employee_loan import EmployeeLoan
            from application.Models.employees.loan_repayment_schedule import LoanRepaymentSchedule
            from application.Models.employees.loan_transaction import LoanTransaction

            for deduction_key, deduction_data in loan_deductions.items():
                if not deduction_key.startswith('LOAN_'):
                    continue

                loan_id = deduction_data['loan_id']
                schedule_id = deduction_data['schedule_id']
                amount = ensure_decimal(deduction_data['employee_amount'])

                # Get loan and repayment schedule
                loan = session.query(EmployeeLoan).filter_by(loan_id=loan_id).first()
                repayment = session.query(LoanRepaymentSchedule).filter_by(schedule_id=schedule_id).first()

                if loan and repayment:
                    # Record the payment
                    success, error = loan.make_payment(
                        session=session,
                        amount=amount,
                        payment_date=payroll_date,
                        payslip_id=payslip_id
                    )

                    if success:
                        app.logger.info(f"Processed loan deduction: {loan.loan_number} - {amount}")
                    else:
                        app.logger.error(f"Failed to process loan deduction: {error}")

        except Exception as e:
            app.logger.error(f"Error processing loan deductions: {e}")

    def calculate_payroll_summary(self, session, payroll_calculations):
        """Calculate summary totals for a payroll run."""
        summary = {
            'total_employees': len(payroll_calculations),
            'total_basic_salary': 0,
            'total_allowances': 0,
            'total_gross_pay': 0,
            'total_tax': 0,
            'total_employee_deductions': 0,
            'total_employer_contributions': 0,
            'total_net_pay': 0,
            'deduction_breakdown': {}
        }

        for calc in payroll_calculations:
            summary['total_basic_salary'] += calc['basic_salary']
            summary['total_gross_pay'] += calc['gross_pay']
            summary['total_tax'] += calc['tax_calculations']['total_tax']
            summary['total_net_pay'] += calc['net_pay']

            # Sum allowances
            for allowance in calc['allowances'].values():
                summary['total_allowances'] += allowance['amount']

            # Sum employee deductions
            for deduction in calc['employee_deductions'].values():
                summary['total_employee_deductions'] += deduction['employee_amount']
                
                # Breakdown by deduction type
                deduction_name = deduction['name']
                if deduction_name not in summary['deduction_breakdown']:
                    summary['deduction_breakdown'][deduction_name] = {
                        'employee_total': 0,
                        'employer_total': 0
                    }
                summary['deduction_breakdown'][deduction_name]['employee_total'] += deduction['employee_amount']

            # Sum employer contributions
            for contribution in calc['employer_contributions'].values():
                summary['total_employer_contributions'] += contribution['employer_amount']
                
                # Breakdown by contribution type
                contribution_name = contribution['name']
                if contribution_name not in summary['deduction_breakdown']:
                    summary['deduction_breakdown'][contribution_name] = {
                        'employee_total': 0,
                        'employer_total': 0
                    }
                summary['deduction_breakdown'][contribution_name]['employer_total'] += contribution['employer_amount']

        return summary

    @classmethod
    def preview_calculation(cls, company_id, employee_id, session, calculation_date=None):
        """Preview payroll calculation for an employee without saving."""
        if calculation_date is None:
            calculation_date = date.today()

        service = cls(company_id)
        return service.calculate_employee_payroll(
            session=session,
            employee_id=employee_id,
            pay_period_start=calculation_date,
            pay_period_end=calculation_date
        )
