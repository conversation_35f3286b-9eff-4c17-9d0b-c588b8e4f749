"""
Rwanda Leave Setup Service

Automatically sets up Rwanda-specific leave policies when a company is created.
This service ensures all Rwanda companies get proper leave compliance from day one.
"""

from flask import current_app as app
from application.Models.employees import LeaveType, LeavePolicy
from application.Models.country import Country
from application.Models.company import Company

class RwandaLeaveSetupService:
    """Service to automatically set up Rwanda leave policies for new companies."""
    
    @classmethod
    def setup_rwanda_leave_policies(cls, company_id, session=None):
        """
        Set up Rwanda-specific leave policies for a company.
        
        Args:
            company_id (str): The UUID of the company to set up policies for
            session: Database session (optional, will create if not provided)
            
        Returns:
            dict: Result with success status and details
        """
        
        # Rwanda Leave Types and Policies based on Labor Law
        leave_configurations = [
            {
                'name': 'Annual Leave',
                'code': 'ANNUAL',
                'description': 'Annual vacation leave as per Rwanda Labor Law',
                'is_paid': True,
                'requires_approval': True,
                'requires_documentation': False,
                'days_allowed': 18,  # 18 working days per year
                'accrual_period': 'yearly',
                'accrual_rate': 1.5,  # 1.5 days per month
                'max_carryover': 6,   # Maximum 6 days can be carried over
                'min_service_days': 365,  # Must work 1 year to be eligible
                'is_prorated': True,
                'gender_specific': None
            },
            {
                'name': 'Sick Leave',
                'code': 'SICK',
                'description': 'Medical leave for illness or injury',
                'is_paid': True,
                'requires_approval': False,  # Usually doesn't require pre-approval
                'requires_documentation': True,  # Medical certificate required
                'days_allowed': 30,  # 30 days per year
                'accrual_period': 'yearly',
                'accrual_rate': 2.5,  # 2.5 days per month
                'max_carryover': 0,   # Usually doesn't carry over
                'min_service_days': 0,  # Available immediately
                'is_prorated': True,
                'gender_specific': None
            },
            {
                'name': 'Maternity Leave',
                'code': 'MATERNITY',
                'description': 'Maternity leave for female employees',
                'is_paid': True,
                'requires_approval': True,
                'requires_documentation': True,  # Medical certificate required
                'days_allowed': 98,  # 14 weeks (98 days)
                'accrual_period': 'none',  # Not accrued, available when needed
                'accrual_rate': None,
                'max_carryover': 0,
                'min_service_days': 180,  # 6 months service required
                'is_prorated': False,
                'gender_specific': 'female'
            },
            {
                'name': 'Paternity Leave',
                'code': 'PATERNITY',
                'description': 'Paternity leave for male employees',
                'is_paid': True,
                'requires_approval': True,
                'requires_documentation': True,  # Birth certificate required
                'days_allowed': 7,  # 1 week
                'accrual_period': 'none',
                'accrual_rate': None,
                'max_carryover': 0,
                'min_service_days': 180,  # 6 months service required
                'is_prorated': False,
                'gender_specific': 'male'
            },
            # Circumstantial Leave Types (Rwanda Labor Law Article 59)
            {
                'name': 'Circumstantial Leave - Marriage',
                'code': 'CIRCUMSTANTIAL_MARRIAGE',
                'description': 'Leave for employee civil marriage (Article 59a)',
                'is_paid': True,
                'requires_approval': False,  # Automatic grant per law
                'requires_documentation': True,  # Marriage certificate required
                'days_allowed': 2,  # 2 working days
                'accrual_period': 'none',  # Granted when event occurs
                'accrual_rate': None,
                'max_carryover': 0,
                'min_service_days': 0,  # Immediate eligibility - circumstantial
                'is_prorated': False,
                'gender_specific': None
            },
            {
                'name': 'Circumstantial Leave - Spouse Death',
                'code': 'CIRCUMSTANTIAL_SPOUSE_DEATH',
                'description': 'Leave for death of spouse (Article 59c)',
                'is_paid': True,
                'requires_approval': False,  # Automatic grant per law
                'requires_documentation': True,  # Death certificate required
                'days_allowed': 7,  # 7 working days
                'accrual_period': 'none',
                'accrual_rate': None,
                'max_carryover': 0,
                'min_service_days': 0,  # Immediate eligibility - circumstantial
                'is_prorated': False,
                'gender_specific': None
            },
            {
                'name': 'Circumstantial Leave - Spouse Death with Infant',
                'code': 'CIRCUMSTANTIAL_SPOUSE_DEATH_INFANT',
                'description': 'Additional leave when spouse dies leaving infant <3 months (Article 59b)',
                'is_paid': True,
                'requires_approval': False,  # Automatic grant per law
                'requires_documentation': True,  # Death certificate + birth certificate required
                'days_allowed': 30,  # 1 month additional
                'accrual_period': 'none',
                'accrual_rate': None,
                'max_carryover': 0,
                'min_service_days': 0,  # Immediate eligibility - circumstantial
                'is_prorated': False,
                'gender_specific': None
            },
            {
                'name': 'Circumstantial Leave - Child Death',
                'code': 'CIRCUMSTANTIAL_CHILD_DEATH',
                'description': 'Leave for death of child or adoptive child (Article 59d)',
                'is_paid': True,
                'requires_approval': False,  # Automatic grant per law
                'requires_documentation': True,  # Death certificate required
                'days_allowed': 5,  # 5 working days
                'accrual_period': 'none',
                'accrual_rate': None,
                'max_carryover': 0,
                'min_service_days': 0,  # Immediate eligibility - circumstantial
                'is_prorated': False,
                'gender_specific': None
            },
            {
                'name': 'Circumstantial Leave - Parent Death',
                'code': 'CIRCUMSTANTIAL_PARENT_DEATH',
                'description': 'Leave for death of father, mother, father-in-law or mother-in-law (Article 59e)',
                'is_paid': True,
                'requires_approval': False,  # Automatic grant per law
                'requires_documentation': True,  # Death certificate required
                'days_allowed': 4,  # 4 working days
                'accrual_period': 'none',
                'accrual_rate': None,
                'max_carryover': 0,
                'min_service_days': 0,  # Immediate eligibility - circumstantial
                'is_prorated': False,
                'gender_specific': None
            },
            {
                'name': 'Circumstantial Leave - Sibling Death',
                'code': 'CIRCUMSTANTIAL_SIBLING_DEATH',
                'description': 'Leave for death of brother or sister (Article 59f)',
                'is_paid': True,
                'requires_approval': False,  # Automatic grant per law
                'requires_documentation': True,  # Death certificate required
                'days_allowed': 4,  # 4 working days
                'accrual_period': 'none',
                'accrual_rate': None,
                'max_carryover': 0,
                'min_service_days': 0,  # Immediate eligibility - circumstantial
                'is_prorated': False,
                'gender_specific': None
            },
            {
                'name': 'Circumstantial Leave - Grandparent Death',
                'code': 'CIRCUMSTANTIAL_GRANDPARENT_DEATH',
                'description': 'Leave for death of grandfather or grandmother (Article 59g)',
                'is_paid': True,
                'requires_approval': False,  # Automatic grant per law
                'requires_documentation': True,  # Death certificate required
                'days_allowed': 3,  # 3 working days
                'accrual_period': 'none',
                'accrual_rate': None,
                'max_carryover': 0,
                'min_service_days': 0,  # Immediate eligibility - circumstantial
                'is_prorated': False,
                'gender_specific': None
            },
            {
                'name': 'Circumstantial Leave - Transfer',
                'code': 'CIRCUMSTANTIAL_TRANSFER',
                'description': 'Leave for transfer over 30km from usual workplace (Article 59h)',
                'is_paid': True,
                'requires_approval': False,  # Automatic grant per law
                'requires_documentation': True,  # Transfer documentation required
                'days_allowed': 3,  # 3 working days
                'accrual_period': 'none',
                'accrual_rate': None,
                'max_carryover': 0,
                'min_service_days': 0,  # Immediate eligibility - circumstantial
                'is_prorated': False,
                'gender_specific': None
            }
        ]

        try:
            # Get Rwanda country ID
            rwanda = Country.get_country_by_code('RW')
            if not rwanda:
                return {
                    "success": False,
                    "message": "Rwanda not found in countries table. Please add Rwanda first.",
                    "created_policies": []
                }

            rwanda_id = rwanda.country_id
            app.logger.info(f"Setting up Rwanda leave policies for company {company_id}")

            # Get company database
            database_name = Company.get_database_given_company_id(company_id)
            if not database_name:
                return {
                    "success": False,
                    "message": f"Company with ID {company_id} not found",
                    "created_policies": []
                }

            # Use provided session or create new one
            if session:
                return cls._create_policies_in_session(session, leave_configurations, rwanda_id)
            else:
                from app import db_connection
                with db_connection.get_session(database_name) as session:
                    return cls._create_policies_in_session(session, leave_configurations, rwanda_id)

        except Exception as e:
            app.logger.error(f"Error setting up Rwanda leave policies: {e}")
            return {
                "success": False,
                "message": f"Error: {str(e)}",
                "created_policies": []
            }

    @classmethod
    def _create_policies_in_session(cls, session, leave_configurations, rwanda_id):
        """Create leave policies within a database session."""
        created_policies = []
        skipped_policies = []

        for config in leave_configurations:
            try:
                # Create leave type
                leave_type_data = {
                    'name': config['name'],
                    'code': config['code'],
                    'description': config['description'],
                    'is_paid': config['is_paid'],
                    'requires_approval': config['requires_approval'],
                    'requires_documentation': config['requires_documentation']
                }

                # Check if leave type already exists
                existing_type = session.query(LeaveType).filter_by(code=config['code']).first()
                if existing_type:
                    app.logger.info(f"Leave type '{config['name']}' already exists, skipping...")
                    leave_type = existing_type
                else:
                    leave_type = LeaveType.create_leave_type(session, **leave_type_data)
                    if not leave_type:
                        app.logger.error(f"Failed to create leave type: {config['name']}")
                        continue
                    app.logger.info(f"Created leave type: {config['name']}")

                # Create leave policy
                policy_data = {
                    'leave_type_id': leave_type.leave_type_id,
                    'country_id': rwanda_id,
                    'days_allowed': config['days_allowed'],
                    'accrual_period': config['accrual_period'],
                    'accrual_rate': config['accrual_rate'],
                    'max_carryover': config['max_carryover'],
                    'min_service_days': config['min_service_days'],
                    'is_prorated': config['is_prorated'],
                    'gender_specific': config['gender_specific']
                }

                # Check if policy already exists
                existing_policy = LeavePolicy.get_policy_for_leave_type_and_country(
                    session,
                    leave_type.leave_type_id,
                    rwanda_id,
                    config['gender_specific']
                )

                if existing_policy:
                    app.logger.info(f"Leave policy for '{config['name']}' already exists, skipping...")
                    skipped_policies.append({
                        'leave_type': config['name'],
                        'days_allowed': config['days_allowed'],
                        'reason': 'already_exists'
                    })
                    continue

                policy = LeavePolicy.create_policy(session, **policy_data)
                if not policy:
                    app.logger.error(f"Failed to create leave policy: {config['name']}")
                    continue

                created_policies.append({
                    'leave_type': config['name'],
                    'days_allowed': config['days_allowed'],
                    'gender_specific': config['gender_specific']
                })
                app.logger.info(f"Created leave policy: {config['name']} - {config['days_allowed']} days")

            except Exception as e:
                app.logger.error(f"Error creating {config['name']}: {str(e)}")
                continue

        return {
            "success": True,
            "message": f"Successfully processed Rwanda leave setup. Created {len(created_policies)} new policies, skipped {len(skipped_policies)} existing policies.",
            "created_policies": created_policies,
            "skipped_policies": skipped_policies
        }

    @classmethod
    def should_setup_rwanda_policies(cls, country_code):
        """
        Check if Rwanda leave policies should be set up for this country.
        
        Args:
            country_code (str): Country code (e.g., 'RW')
            
        Returns:
            bool: True if Rwanda policies should be set up
        """
        return country_code and country_code.upper() == 'RW'
