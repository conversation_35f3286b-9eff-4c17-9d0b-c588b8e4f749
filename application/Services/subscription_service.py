from flask import current_app
from datetime import datetime, date, timedelta
from decimal import Decimal
from application.Models.subscription_plan import SubscriptionPlan
from application.Models.company_subscription import CompanySubscription
from application.Models.subscription_invoice import SubscriptionInvoice
from application.Models.subscription_payment import SubscriptionPayment
from application.Models.feature_access import FeatureAccess
from application.Models.company import Company
from application.utils.db_connection import DatabaseConnection

class SubscriptionService:
    """Service class for subscription-related business logic."""

    @staticmethod
    def count_active_employees(company_id):
        """Count active employees for a company."""
        try:
            company = Company.get_company_by_id(company_id)
            if not company:
                return 0

            database_name = company.database_name
            db_connection = DatabaseConnection()
            
            with db_connection.get_session(database_name) as session:
                from application.Models.employees.employee import Employee
                count = session.query(Employee).filter_by(status='active').count()
                return count
        except Exception as e:
            current_app.logger.error(f"Error counting active employees: {e}")
            return 0

    @staticmethod
    def create_company_subscription(company_id, plan_id, trial_days=14):
        """Create a new subscription for a company."""
        try:
            # Check if company already has a subscription
            existing_subscription = CompanySubscription.get_subscription_by_company(company_id)
            if existing_subscription:
                return None, "Company already has a subscription"

            # Count current employees
            employee_count = SubscriptionService.count_active_employees(company_id)

            # Create subscription
            subscription, error = CompanySubscription.create_subscription(
                company_id=company_id,
                plan_id=plan_id,
                trial_days=trial_days,
                employee_count=employee_count
            )

            if not subscription:
                return None, error

            # Sync feature access with plan
            FeatureAccess.sync_features_with_plan(company_id)

            current_app.logger.info(f"Created subscription for company {company_id}")
            return subscription, None
        except Exception as e:
            current_app.logger.error(f"Error creating company subscription: {e}")
            return None, str(e)

    @staticmethod
    def update_subscription_employee_count(company_id):
        """Update subscription employee count and recalculate billing."""
        try:
            subscription = CompanySubscription.get_subscription_by_company(company_id)
            if not subscription:
                return False, "No subscription found for company"

            # Count current active employees
            new_count = SubscriptionService.count_active_employees(company_id)
            
            # Update subscription
            success, error = subscription.update_employee_count(new_count)
            if not success:
                return False, error

            current_app.logger.info(f"Updated employee count to {new_count} for company {company_id}")
            return True, None
        except Exception as e:
            current_app.logger.error(f"Error updating subscription employee count: {e}")
            return False, str(e)

    @staticmethod
    def generate_subscription_invoice(subscription_id, billing_period_start=None, billing_period_end=None):
        """Generate an invoice for a subscription."""
        try:
            subscription = CompanySubscription.get_subscription_by_id(subscription_id)
            if not subscription:
                return None, "Subscription not found"

            if not subscription.plan:
                return None, "No plan associated with subscription"

            # Use current period if dates not provided
            if not billing_period_start:
                billing_period_start = subscription.current_period_start
            if not billing_period_end:
                billing_period_end = subscription.current_period_end

            # Calculate subtotal based on employee count and plan
            employee_count = subscription.employee_count
            subtotal = Decimal(str(subscription.plan.calculate_price(employee_count)))

            # Create invoice
            invoice, error = SubscriptionInvoice.create_invoice(
                subscription_id=subscription_id,
                billing_period_start=billing_period_start,
                billing_period_end=billing_period_end,
                employee_count=employee_count,
                subtotal=subtotal,
                tax_rate=0.18  # Default 18% tax rate - can be configurable
            )

            if not invoice:
                return None, error

            current_app.logger.info(f"Generated invoice {invoice.invoice_number} for subscription {subscription_id}")
            return invoice, None
        except Exception as e:
            current_app.logger.error(f"Error generating subscription invoice: {e}")
            return None, str(e)

    @staticmethod
    def process_manual_payment(invoice_id, amount, payment_method, payment_reference=None, recorded_by=None):
        """Process a manual payment for an invoice."""
        try:
            # Create payment record
            payment, error = SubscriptionPayment.create_payment(
                invoice_id=invoice_id,
                amount=amount,
                payment_method=payment_method,
                payment_reference=payment_reference,
                recorded_by=recorded_by
            )

            if not payment:
                return None, error

            current_app.logger.info(f"Processed manual payment of {amount} for invoice {invoice_id}")
            return payment, None
        except Exception as e:
            current_app.logger.error(f"Error processing manual payment: {e}")
            return None, str(e)

    @staticmethod
    def check_and_update_subscription_status():
        """Check and update subscription statuses (for scheduled tasks)."""
        try:
            updated_count = 0
            
            # Get all active subscriptions
            active_subscriptions = CompanySubscription.get_subscriptions_by_status('ACTIVE')
            trial_subscriptions = CompanySubscription.get_subscriptions_by_status('TRIAL')
            
            all_subscriptions = active_subscriptions + trial_subscriptions
            
            for subscription in all_subscriptions:
                # Check if subscription has expired
                if subscription.is_expired():
                    # Check for unpaid invoices
                    unpaid_invoices = SubscriptionInvoice.get_invoices_by_subscription(
                        subscription.subscription_id
                    )
                    unpaid_invoices = [inv for inv in unpaid_invoices if inv.status in ['PENDING', 'OVERDUE']]
                    
                    if unpaid_invoices:
                        # Suspend subscription due to non-payment
                        subscription.suspend_subscription("Subscription expired with unpaid invoices")
                        updated_count += 1
                    else:
                        # Auto-renew if enabled
                        if subscription.auto_renew:
                            success, error = subscription.renew_subscription()
                            if success:
                                # Generate invoice for new period
                                SubscriptionService.generate_subscription_invoice(subscription.subscription_id)
                                updated_count += 1
                        else:
                            # Cancel subscription
                            subscription.cancel_subscription("Subscription expired and auto-renew disabled")
                            updated_count += 1

                # Check trial expiration
                elif subscription.is_trial() and subscription.days_until_trial_end() == 0:
                    # Trial has ended, activate subscription
                    subscription.activate_subscription()
                    # Generate first invoice
                    SubscriptionService.generate_subscription_invoice(subscription.subscription_id)
                    updated_count += 1

            current_app.logger.info(f"Updated {updated_count} subscription statuses")
            return updated_count, None
        except Exception as e:
            current_app.logger.error(f"Error checking subscription statuses: {e}")
            return 0, str(e)

    @staticmethod
    def get_subscription_analytics(company_id=None, start_date=None, end_date=None):
        """Get subscription analytics."""
        try:
            analytics = {
                'subscriptions': {
                    'total': 0,
                    'active': 0,
                    'trial': 0,
                    'suspended': 0,
                    'cancelled': 0
                },
                'revenue': {
                    'total': 0,
                    'monthly_recurring': 0,
                    'average_per_customer': 0
                },
                'employees': {
                    'total_billable': 0,
                    'average_per_company': 0
                }
            }

            # Get subscription counts
            if company_id:
                subscription = CompanySubscription.get_subscription_by_company(company_id)
                if subscription:
                    analytics['subscriptions']['total'] = 1
                    analytics['subscriptions'][subscription.status.lower()] = 1
                    analytics['employees']['total_billable'] = subscription.employee_count
            else:
                all_subscriptions = CompanySubscription.query.all()
                analytics['subscriptions']['total'] = len(all_subscriptions)
                
                for subscription in all_subscriptions:
                    status_key = subscription.status.lower()
                    if status_key in analytics['subscriptions']:
                        analytics['subscriptions'][status_key] += 1
                    analytics['employees']['total_billable'] += subscription.employee_count

                if analytics['subscriptions']['total'] > 0:
                    analytics['employees']['average_per_company'] = (
                        analytics['employees']['total_billable'] / analytics['subscriptions']['total']
                    )

            # Get revenue analytics
            revenue_summary = SubscriptionInvoice.get_revenue_summary(start_date, end_date)
            analytics['revenue'].update(revenue_summary)

            # Calculate monthly recurring revenue (MRR)
            active_subscriptions = CompanySubscription.get_subscriptions_by_status('ACTIVE')
            mrr = sum(float(sub.amount_due) for sub in active_subscriptions if sub.plan and sub.plan.billing_cycle == 'MONTHLY')
            analytics['revenue']['monthly_recurring'] = mrr

            if analytics['subscriptions']['active'] > 0:
                analytics['revenue']['average_per_customer'] = mrr / analytics['subscriptions']['active']

            return analytics, None
        except Exception as e:
            current_app.logger.error(f"Error getting subscription analytics: {e}")
            return None, str(e)

    @staticmethod
    def upgrade_subscription(company_id, new_plan_id):
        """Upgrade a company's subscription plan."""
        try:
            subscription = CompanySubscription.get_subscription_by_company(company_id)
            if not subscription:
                return False, "No subscription found for company"

            # Change plan
            success, error = subscription.change_plan(new_plan_id)
            if not success:
                return False, error

            # Sync feature access with new plan
            FeatureAccess.sync_features_with_plan(company_id)

            # Generate prorated invoice if needed
            # (This is a simplified version - in production you might want more complex proration logic)
            SubscriptionService.generate_subscription_invoice(subscription.subscription_id)

            current_app.logger.info(f"Upgraded subscription for company {company_id} to plan {new_plan_id}")
            return True, None
        except Exception as e:
            current_app.logger.error(f"Error upgrading subscription: {e}")
            return False, str(e)

    @staticmethod
    def cancel_subscription(company_id, reason=None):
        """Cancel a company's subscription."""
        try:
            subscription = CompanySubscription.get_subscription_by_company(company_id)
            if not subscription:
                return False, "No subscription found for company"

            # Cancel subscription
            success, error = subscription.cancel_subscription(reason)
            if not success:
                return False, error

            # Disable all features
            features = FeatureAccess.get_company_features(company_id)
            for feature in features:
                feature.is_enabled = False

            from application.database import central_db as db
            db.session.commit()

            current_app.logger.info(f"Cancelled subscription for company {company_id}")
            return True, None
        except Exception as e:
            current_app.logger.error(f"Error cancelling subscription: {e}")
            return False, str(e)
