from functools import wraps
from flask import request, current_app
from flask_restx import fields

# Store for all documented endpoints
documented_endpoints = {}

def document_api(namespace, name, description=None, expect=None, responses=None, security=None, params=None, examples=None):
    """
    Decorator to document API endpoints.

    Args:
        namespace: The namespace to add this endpoint to
        name: The name of the endpoint
        description: The description of the endpoint
        expect: The expected input model
        responses: A dictionary of response codes to (description, model) tuples
        security: Security requirements
        params: URL parameters
        examples: Request and response examples
    """
    def decorator(func):
        endpoint_key = func.__name__

        # Store documentation info for this endpoint
        documented_endpoints[endpoint_key] = {
            'namespace': namespace,
            'name': name,
            'description': description or func.__doc__,
            'expect': expect,
            'responses': responses or {},
            'security': security,
            'params': params or [],
            'examples': examples or {},
            'func': func,
            'methods': getattr(func, 'methods', ['GET']),
            'url': None  # Will be set when the route is registered
        }

        @wraps(func)
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)

        return wrapper

    return decorator

def register_documented_endpoints(app):
    """
    Register all documented endpoints with the API.
    This should be called after all routes are registered.
    """
    from application.api_docs import api

    # Get all registered routes
    for rule in app.url_map.iter_rules():
        endpoint = rule.endpoint

        # Skip static routes and other non-API routes
        if endpoint.startswith('static') or '.' not in endpoint:
            continue

        # Extract blueprint and function name
        blueprint_name, func_name = endpoint.split('.', 1)

        # Check if this endpoint is documented
        if func_name in documented_endpoints:
            doc_info = documented_endpoints[func_name]

            # Store the URL for this endpoint
            doc_info['url'] = str(rule)

            # Create a resource for this endpoint
            namespace = doc_info['namespace']

            # Define the resource class dynamically
            resource_name = f"{func_name.title().replace('_', '')}Resource"
            resource_methods = {}

            # Add methods to the resource
            for method in doc_info['methods']:
                method_lower = method.lower()

                # Define the method function
                def create_method(m):
                    @wraps(doc_info['func'])
                    def method_func(self, *args, **kwargs):
                        # This is just a placeholder for documentation
                        # The actual implementation is in the blueprint
                        pass

                    # Add documentation
                    if doc_info['description']:
                        method_func.__doc__ = doc_info['description']

                    # Add expect
                    if doc_info['expect'] and m.lower() in ['post', 'put', 'patch']:
                        namespace.expect(doc_info['expect'])(method_func)

                    # Add responses
                    for code, (desc, model) in doc_info['responses'].items():
                        namespace.response(code, desc, model)(method_func)

                    # Add security
                    if doc_info['security']:
                        namespace.doc(security=doc_info['security'])(method_func)

                    # Add examples
                    if doc_info['examples'].get(m.lower()):
                        namespace.doc(examples=doc_info['examples'][m.lower()])(method_func)

                    return method_func

                # Add the method to the resource
                resource_methods[method_lower] = create_method(method)

            # Create the resource class
            from flask_restx import Resource
            resource_class = type(resource_name, (Resource,), resource_methods)

            # Extract URL parameters
            url_params = []
            for segment in rule.rule.split('/'):
                if segment and segment.startswith('<') and segment.endswith('>'):
                    param_name = segment[1:-1]
                    if ':' in param_name:
                        param_type, param_name = param_name.split(':', 1)
                    url_params.append(param_name)

            # Add URL parameters to the resource
            for param in url_params:
                # Check if this parameter is documented
                param_doc = next((p for p in doc_info['params'] if p['name'] == param), None)
                if param_doc:
                    namespace.param(param, param_doc.get('description', ''),
                                   _in=param_doc.get('_in', 'path'),
                                   type=param_doc.get('type', 'string'))(resource_class)
                else:
                    namespace.param(param, f'The {param} parameter')(resource_class)

            # Register the resource with the namespace
            endpoint_url = doc_info['url']

            # Remove blueprint prefix from URL
            for bp in app.blueprints.values():
                if bp.name == blueprint_name and bp.url_prefix:
                    if endpoint_url.startswith(bp.url_prefix):
                        endpoint_url = endpoint_url[len(bp.url_prefix):]

            # Register the resource
            namespace.add_resource(resource_class, endpoint_url)

def generate_code_samples(language, endpoint_info, base_url):
    """
    Generate code samples for a specific language and endpoint.

    Args:
        language: The programming language (python, javascript, java, etc.)
        endpoint_info: Information about the endpoint
        base_url: The base URL for the API

    Returns:
        A string containing the code sample
    """
    url = endpoint_info['url']
    method = endpoint_info['methods'][0].lower()

    # Replace URL parameters with placeholders
    for param in endpoint_info.get('params', []):
        if param.get('_in') == 'path':
            url = url.replace(f"<{param['name']}>", f"{{{param['name']}}}")

    # Generate code based on language
    if language == 'python':
        return _generate_python_sample(method, url, endpoint_info, base_url)
    elif language == 'javascript':
        return _generate_javascript_sample(method, url, endpoint_info, base_url)
    elif language == 'curl':
        return _generate_curl_sample(method, url, endpoint_info, base_url)
    elif language == 'java':
        return _generate_java_sample(method, url, endpoint_info, base_url)
    elif language == 'csharp':
        return _generate_csharp_sample(method, url, endpoint_info, base_url)
    else:
        return f"Code samples for {language} are not available yet."

def _generate_python_sample(method, url, endpoint_info, base_url):
    """Generate Python code sample"""
    code = f"""import requests

url = "{base_url}{url}"
"""

    # Add headers
    if endpoint_info.get('security'):
        code += """headers = {
    "Authorization": "Bearer YOUR_ACCESS_TOKEN"
}
"""
    else:
        code += """headers = {
    "Content-Type": "application/json"
}
"""

    # Add request body for POST/PUT/PATCH
    if method in ['post', 'put', 'patch'] and endpoint_info.get('expect'):
        code += """
payload = {
    # Add your request data here
}

"""

    # Add the request
    if method == 'get':
        if endpoint_info.get('security'):
            code += f"response = requests.get(url, headers=headers)\n"
        else:
            code += f"response = requests.get(url)\n"
    elif method in ['post', 'put', 'patch']:
        code += f"response = requests.{method}(url, json=payload, headers=headers)\n"
    elif method == 'delete':
        code += f"response = requests.delete(url, headers=headers)\n"

    # Add response handling
    code += """
# Check if the request was successful
if response.status_code == 200:
    data = response.json()
    print(data)
else:
    print(f"Error: {response.status_code}")
    print(response.text)
"""

    return code

def _generate_javascript_sample(method, url, endpoint_info, base_url):
    """Generate JavaScript code sample"""
    code = f"""// Using fetch API
const url = "{base_url}{url}";
"""

    # Add headers
    if endpoint_info.get('security'):
        code += """const headers = {
    "Authorization": "Bearer YOUR_ACCESS_TOKEN",
    "Content-Type": "application/json"
};
"""
    else:
        code += """const headers = {
    "Content-Type": "application/json"
};
"""

    # Add request options
    if method in ['post', 'put', 'patch'] and endpoint_info.get('expect'):
        code += """
const payload = {
    // Add your request data here
};

const options = {
    method: "{}".toUpperCase(),
    headers: headers,
    body: JSON.stringify(payload)
};
""".format(method)
    else:
        code += """
const options = {
    method: "{}".toUpperCase(),
    headers: headers
};
""".format(method)

    # Add the request
    code += """
fetch(url, options)
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log(data);
    })
    .catch(error => {
        console.error('Error:', error);
    });
"""

    return code

def _generate_curl_sample(method, url, endpoint_info, base_url):
    """Generate curl command sample"""
    code = f"curl -X {method.upper()} \"{base_url}{url}\""

    # Add headers
    if endpoint_info.get('security'):
        code += " \\\n  -H \"Authorization: Bearer YOUR_ACCESS_TOKEN\""

    code += " \\\n  -H \"Content-Type: application/json\""

    # Add request body for POST/PUT/PATCH
    if method in ['post', 'put', 'patch'] and endpoint_info.get('expect'):
        code += " \\\n  -d '{\n    \"key\": \"value\"\n    // Add your request data here\n  }'"

    return code

def _generate_java_sample(method, url, endpoint_info, base_url):
    """Generate Java code sample"""
    code = """import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.http.HttpResponse.BodyHandlers;

public class ApiExample {
    public static void main(String[] args) {
        try {
            HttpClient client = HttpClient.newHttpClient();
"""

    # Build the request
    code += f"            URI uri = URI.create(\"{base_url}{url}\");\n"

    if method in ['post', 'put', 'patch'] and endpoint_info.get('expect'):
        code += """
            String requestBody = "{"
                + "    \\"key\\": \\"value\\""
                + "    // Add your request data here"
                + "}";

            HttpRequest request = HttpRequest.newBuilder()
                .uri(uri)
                .header("Content-Type", "application/json")
"""
        if endpoint_info.get('security'):
            code += '                .header("Authorization", "Bearer YOUR_ACCESS_TOKEN")\n'

        code += f"                .{method}(HttpRequest.BodyPublishers.ofString(requestBody))\n"
        code += "                .build();\n"
    else:
        code += """
            HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
                .uri(uri)
                .header("Content-Type", "application/json")
"""
        if endpoint_info.get('security'):
            code += '                .header("Authorization", "Bearer YOUR_ACCESS_TOKEN")\n'

        if method == 'get':
            code += "                .GET();\n"
        elif method == 'delete':
            code += "                .DELETE();\n"

        code += "            HttpRequest request = requestBuilder.build();\n"

    # Send the request and handle the response
    code += """
            HttpResponse<String> response = client.send(request, BodyHandlers.ofString());

            System.out.println("Status code: " + response.statusCode());
            System.out.println("Response body: " + response.body());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
"""

    return code

def _generate_csharp_sample(method, url, endpoint_info, base_url):
    """Generate C# code sample"""
    code = """using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

class Program
{
    static async Task Main(string[] args)
    {
        using (HttpClient client = new HttpClient())
        {
"""

    # Add headers
    if endpoint_info.get('security'):
        code += """            client.DefaultRequestHeaders.Authorization =
                new AuthenticationHeaderValue("Bearer", "YOUR_ACCESS_TOKEN");
"""

    code += f"            string url = \"{base_url}{url}\";\n"

    # Add request body for POST/PUT/PATCH
    if method in ['post', 'put', 'patch'] and endpoint_info.get('expect'):
        code += """
            // Create request body
            var requestData = new
            {
                // Add your request data here
                key = "value"
            };

            string jsonContent = JsonConvert.SerializeObject(requestData);
            var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");
"""

    # Add the request
    if method == 'get':
        code += """
            try
            {
                HttpResponseMessage response = await client.GetAsync(url);
                response.EnsureSuccessStatusCode();
                string responseBody = await response.Content.ReadAsStringAsync();
                Console.WriteLine(responseBody);
            }
            catch (HttpRequestException e)
            {
                Console.WriteLine($"Error: {e.Message}");
            }
"""
    elif method == 'post':
        code += """
            try
            {
                HttpResponseMessage response = await client.PostAsync(url, content);
                response.EnsureSuccessStatusCode();
                string responseBody = await response.Content.ReadAsStringAsync();
                Console.WriteLine(responseBody);
            }
            catch (HttpRequestException e)
            {
                Console.WriteLine($"Error: {e.Message}");
            }
"""
    elif method == 'put':
        code += """
            try
            {
                HttpResponseMessage response = await client.PutAsync(url, content);
                response.EnsureSuccessStatusCode();
                string responseBody = await response.Content.ReadAsStringAsync();
                Console.WriteLine(responseBody);
            }
            catch (HttpRequestException e)
            {
                Console.WriteLine($"Error: {e.Message}");
            }
"""
    elif method == 'delete':
        code += """
            try
            {
                HttpResponseMessage response = await client.DeleteAsync(url);
                response.EnsureSuccessStatusCode();
                string responseBody = await response.Content.ReadAsStringAsync();
                Console.WriteLine(responseBody);
            }
            catch (HttpRequestException e)
            {
                Console.WriteLine($"Error: {e.Message}");
            }
"""

    code += """        }
    }
}
"""

    return code
