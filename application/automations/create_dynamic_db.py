import psycopg2
from sqlalchemy import create_engine
from sqlalchemy.exc import ProgrammingError, SQLAlchemyError
from flask import app
from application.database import db as DynamicBase
import os
from dotenv import load_dotenv

load_dotenv()

def create_postgres_db(database_name):
    """Create a new PostgreSQL database."""
    from app import app
    # Database connection parameters (from environment variables or default values)
    db_host = os.getenv('DB_HOST')
    db_user = os.getenv('DB_USER')
    db_password = os.getenv('DB_PASSWORD')
    db_port = "5432"  # Default PostgreSQL port

    # Connect to the default database (e.g., 'postgres' for PostgreSQL)
    try:
        # Connect to the postgres database to create a new database
        conn = psycopg2.connect(
            host=db_host,
            user=db_user,
            password=db_password,
            port=int(db_port),
            dbname='postgres'  # Connect to the 'postgres' system database to create a new database
        )
        conn.autocommit = True  # Required for CREATE DATABASE
        cursor = conn.cursor()

        # Check if database exists first to avoid errors
        cursor.execute("SELECT 1 FROM pg_catalog.pg_database WHERE datname = %s", (database_name,))
        exists = cursor.fetchone()

        if not exists:
            # Create the new database with proper quoting for PostgreSQL
            # Use double quotes for identifiers in PostgreSQL
            cursor.execute(f'CREATE DATABASE "{database_name}"')
            app.logger.info(f"Database '{database_name}' created successfully.")
        else:
            app.logger.info(f"Database '{database_name}' already exists.")

    except psycopg2.Error as e:
        app.logger.error(f"Error while creating database '{database_name}': {e}")
    except Exception as e:
        app.logger.error(f"An error occurred while creating database {database_name}: {str(e)}")
    finally:
        # Clean up the connection and cursor
        if 'cursor' in locals() and cursor:
            cursor.close()
        if 'conn' in locals() and conn:
            conn.close()

def create_database(database_name):
    """Create a new PostgreSQL database and tables for a new company."""
    from app import app
    try:
        create_postgres_db(database_name)
        app.logger.info(f"Database '{database_name}' created successfully.")

        # Get database password and other connection parameters
        pwd = os.getenv('DB_PASSWORD')
        db_user = os.getenv('DB_USER')
        db_host = os.getenv('DB_HOST')
        if not pwd:
            app.logger.error("Database password not found in environment variables.")
            raise ValueError("Database password not found in environment variables.")

        # Connect to the newly created PostgreSQL database and create tables
        # Use URL encoding for special characters in the database name
        from urllib.parse import quote_plus
        encoded_db_name = quote_plus(database_name)
        engine = create_engine(f'postgresql+psycopg2://{db_user}:{pwd}@{db_host}/{encoded_db_name}')
        DynamicBase.metadata.create_all(engine)
        app.logger.info(f"Tables created successfully in database '{database_name}'.")

    except (ProgrammingError, SQLAlchemyError) as e:
        app.logger.error(f"Failure with SQLAlchemy: {str(e)}")
    except Exception as e:
        app.logger.error(f"Something went wrong with error message: {str(e)}")