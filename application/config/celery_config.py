"""
Celery Configuration for KaziSync HRMS Email System
Handles background task processing for email sending and other async operations
"""

import os
from celery import Celery
from kombu import Queue

class CeleryConfig:
    """Celery configuration for email and background tasks"""
    
    # Redis configuration
    REDIS_URL = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
    
    # Celery broker settings
    broker_url = REDIS_URL
    result_backend = REDIS_URL
    
    # Task settings
    task_serializer = 'json'
    accept_content = ['json']
    result_serializer = 'json'
    timezone = 'UTC'
    enable_utc = True
    
    # Task routing
    task_routes = {
        'application.tasks.email_tasks.send_email_async': {'queue': 'email_high'},
        'application.tasks.email_tasks.send_otp_email': {'queue': 'email_high'},
        'application.tasks.email_tasks.send_password_reset_email': {'queue': 'email_high'},
        'application.tasks.email_tasks.send_notification_email': {'queue': 'email_normal'},
        'application.tasks.email_tasks.send_bulk_emails': {'queue': 'email_low'},
        'application.tasks.email_tasks.cleanup_expired_tokens': {'queue': 'maintenance'},
        'application.tasks.email_tasks.process_email_analytics': {'queue': 'analytics'},
    }
    
    # Queue definitions
    task_default_queue = 'default'
    task_queues = (
        Queue('email_high', routing_key='email_high'),
        Queue('email_normal', routing_key='email_normal'),
        Queue('email_low', routing_key='email_low'),
        Queue('maintenance', routing_key='maintenance'),
        Queue('analytics', routing_key='analytics'),
        Queue('default', routing_key='default'),
    )
    
    # Worker settings
    worker_prefetch_multiplier = 1
    task_acks_late = True
    worker_max_tasks_per_child = 1000
    
    # Retry settings
    task_default_retry_delay = 60  # 1 minute
    task_max_retries = 3
    
    # Email-specific settings
    email_task_soft_time_limit = 300  # 5 minutes
    email_task_time_limit = 600  # 10 minutes
    
    # Beat schedule for periodic tasks
    beat_schedule = {
        'cleanup-expired-otp-tokens': {
            'task': 'application.tasks.email_tasks.cleanup_expired_tokens',
            'schedule': 300.0,  # Every 5 minutes
            'options': {'queue': 'maintenance'}
        },
        'cleanup-expired-password-reset-tokens': {
            'task': 'application.tasks.email_tasks.cleanup_expired_password_reset_tokens',
            'schedule': 3600.0,  # Every hour
            'options': {'queue': 'maintenance'}
        },
        'process-email-analytics': {
            'task': 'application.tasks.email_tasks.process_email_analytics',
            'schedule': 1800.0,  # Every 30 minutes
            'options': {'queue': 'analytics'}
        },
        'email-delivery-status-check': {
            'task': 'application.tasks.email_tasks.check_email_delivery_status',
            'schedule': 900.0,  # Every 15 minutes
            'options': {'queue': 'maintenance'}
        },
    }


def make_celery(app):
    """Create and configure Celery instance"""
    celery = Celery(
        app.import_name,
        backend=CeleryConfig.result_backend,
        broker=CeleryConfig.broker_url
    )
    
    # Update configuration
    celery.conf.update(
        task_serializer=CeleryConfig.task_serializer,
        accept_content=CeleryConfig.accept_content,
        result_serializer=CeleryConfig.result_serializer,
        timezone=CeleryConfig.timezone,
        enable_utc=CeleryConfig.enable_utc,
        task_routes=CeleryConfig.task_routes,
        task_default_queue=CeleryConfig.task_default_queue,
        task_queues=CeleryConfig.task_queues,
        worker_prefetch_multiplier=CeleryConfig.worker_prefetch_multiplier,
        task_acks_late=CeleryConfig.task_acks_late,
        worker_max_tasks_per_child=CeleryConfig.worker_max_tasks_per_child,
        task_default_retry_delay=CeleryConfig.task_default_retry_delay,
        task_max_retries=CeleryConfig.task_max_retries,
        beat_schedule=CeleryConfig.beat_schedule,
    )
    
    class ContextTask(celery.Task):
        """Make celery tasks work with Flask app context"""
        def __call__(self, *args, **kwargs):
            with app.app_context():
                return self.run(*args, **kwargs)
    
    celery.Task = ContextTask
    return celery


# Redis connection settings
REDIS_CONFIG = {
    'host': os.getenv('REDIS_HOST', 'localhost'),
    'port': int(os.getenv('REDIS_PORT', 6379)),
    'db': int(os.getenv('REDIS_DB', 0)),
    'password': os.getenv('REDIS_PASSWORD', None),
    'socket_timeout': 30,
    'socket_connect_timeout': 30,
    'retry_on_timeout': True,
    'health_check_interval': 30,
}

# Email queue priorities
EMAIL_QUEUE_PRIORITIES = {
    'high': 9,    # OTP, password reset, security alerts
    'normal': 5,  # Leave requests, payroll notifications
    'low': 1,     # Announcements, bulk emails
}

# Task retry configuration
TASK_RETRY_CONFIG = {
    'email_send': {
        'max_retries': 3,
        'countdown': 60,  # Wait 1 minute before retry
        'backoff': True,  # Exponential backoff
    },
    'otp_send': {
        'max_retries': 2,
        'countdown': 30,  # Wait 30 seconds before retry
        'backoff': False,
    },
    'bulk_email': {
        'max_retries': 5,
        'countdown': 300,  # Wait 5 minutes before retry
        'backoff': True,
    }
}

# Monitoring and logging
CELERY_MONITORING = {
    'log_level': os.getenv('CELERY_LOG_LEVEL', 'INFO'),
    'log_file': os.getenv('CELERY_LOG_FILE', 'logs/celery.log'),
    'enable_task_logging': True,
    'enable_error_emails': False,  # We'll handle errors through our own system
}
