"""
Email Configuration for KaziSync HRMS
Handles email settings for multi-tenant environment with cPanel SMTP
"""

import os
from typing import Dict, Any

class EmailConfig:
    """Email configuration management for multi-tenant HRMS"""
    
    # Default email settings for cPanel SMTP
    DEFAULT_MAIL_SERVER = os.getenv('MAIL_SERVER', 'mail.kazisync.com')
    DEFAULT_MAIL_PORT = int(os.getenv('MAIL_PORT', 587))
    DEFAULT_MAIL_USE_TLS = os.getenv('MAIL_USE_TLS', 'True').lower() == 'true'
    DEFAULT_MAIL_USE_SSL = os.getenv('MAIL_USE_SSL', 'False').lower() == 'true'
    DEFAULT_MAIL_USERNAME = os.getenv('MAIL_USERNAME', '<EMAIL>')
    DEFAULT_MAIL_PASSWORD = os.getenv('MAIL_PASSWORD', '')
    DEFAULT_MAIL_DEFAULT_SENDER = os.getenv('MAIL_DEFAULT_SENDER', 'KaziSync HRMS <<EMAIL>>')
    
    # Email sending settings
    MAIL_MAX_EMAILS = int(os.getenv('MAIL_MAX_EMAILS', 100))
    MAIL_SUPPRESS_SEND = os.getenv('MAIL_SUPPRESS_SEND', 'False').lower() == 'true'
    MAIL_ASCII_ATTACHMENTS = os.getenv('MAIL_ASCII_ATTACHMENTS', 'False').lower() == 'true'
    
    # Email template settings
    EMAIL_TEMPLATE_FOLDER = 'templates/emails'
    EMAIL_STATIC_FOLDER = 'static/email'
    
    # OTP settings
    OTP_EXPIRY_MINUTES = int(os.getenv('OTP_EXPIRY_MINUTES', 5))
    OTP_LENGTH = int(os.getenv('OTP_LENGTH', 6))
    OTP_MAX_ATTEMPTS = int(os.getenv('OTP_MAX_ATTEMPTS', 3))
    OTP_RATE_LIMIT_MINUTES = int(os.getenv('OTP_RATE_LIMIT_MINUTES', 60))
    
    # Password reset settings
    PASSWORD_RESET_EXPIRY_HOURS = int(os.getenv('PASSWORD_RESET_EXPIRY_HOURS', 24))
    EMAIL_VERIFICATION_EXPIRY_HOURS = int(os.getenv('EMAIL_VERIFICATION_EXPIRY_HOURS', 72))
    
    # Email queue settings
    EMAIL_QUEUE_HIGH_PRIORITY = 'email_high'
    EMAIL_QUEUE_NORMAL_PRIORITY = 'email_normal'
    EMAIL_QUEUE_LOW_PRIORITY = 'email_low'
    
    # Rate limiting
    EMAIL_RATE_LIMIT_PER_HOUR = int(os.getenv('EMAIL_RATE_LIMIT_PER_HOUR', 100))
    EMAIL_RATE_LIMIT_PER_DAY = int(os.getenv('EMAIL_RATE_LIMIT_PER_DAY', 1000))
    
    @classmethod
    def get_mail_config(cls) -> Dict[str, Any]:
        """Get Flask-Mail configuration"""
        return {
            'MAIL_SERVER': cls.DEFAULT_MAIL_SERVER,
            'MAIL_PORT': cls.DEFAULT_MAIL_PORT,
            'MAIL_USE_TLS': cls.DEFAULT_MAIL_USE_TLS,
            'MAIL_USE_SSL': cls.DEFAULT_MAIL_USE_SSL,
            'MAIL_USERNAME': cls.DEFAULT_MAIL_USERNAME,
            'MAIL_PASSWORD': cls.DEFAULT_MAIL_PASSWORD,
            'MAIL_DEFAULT_SENDER': cls.DEFAULT_MAIL_DEFAULT_SENDER,
            'MAIL_MAX_EMAILS': cls.MAIL_MAX_EMAILS,
            'MAIL_SUPPRESS_SEND': cls.MAIL_SUPPRESS_SEND,
            'MAIL_ASCII_ATTACHMENTS': cls.MAIL_ASCII_ATTACHMENTS,
        }
    
    @classmethod
    def get_company_mail_config(cls, company_id: str) -> Dict[str, Any]:
        """Get company-specific email configuration if available"""
        # For now, return default config
        # In the future, this can be extended to support per-company SMTP settings
        return cls.get_mail_config()
    
    @classmethod
    def is_email_enabled(cls) -> bool:
        """Check if email sending is enabled"""
        return not cls.MAIL_SUPPRESS_SEND and bool(cls.DEFAULT_MAIL_PASSWORD)


class NotificationConfig:
    """Configuration for email notifications"""
    
    # Notification types
    NOTIFICATION_TYPES = {
        'auth': {
            'otp_code': {'priority': 'high', 'template': 'auth/otp_code.html'},
            'password_reset': {'priority': 'high', 'template': 'auth/password_reset.html'},
            'email_verification': {'priority': 'high', 'template': 'auth/email_verification.html'},
            'security_alert': {'priority': 'high', 'template': 'auth/security_alert.html'},
            'account_locked': {'priority': 'high', 'template': 'auth/account_locked.html'},
        },
        'leave': {
            'request_submitted': {'priority': 'normal', 'template': 'leave/request_submitted.html'},
            'request_approved': {'priority': 'normal', 'template': 'leave/request_approved.html'},
            'request_rejected': {'priority': 'normal', 'template': 'leave/request_rejected.html'},
            'balance_updated': {'priority': 'low', 'template': 'leave/balance_updated.html'},
            'approval_reminder': {'priority': 'normal', 'template': 'leave/approval_reminder.html'},
        },
        'payroll': {
            'payslip_ready': {'priority': 'normal', 'template': 'payroll/payslip_ready.html'},
            'salary_updated': {'priority': 'normal', 'template': 'payroll/salary_updated.html'},
            'payroll_processed': {'priority': 'low', 'template': 'payroll/payroll_processed.html'},
        },
        'recruitment': {
            'application_received': {'priority': 'low', 'template': 'recruitment/application_received.html'},
            'interview_scheduled': {'priority': 'normal', 'template': 'recruitment/interview_scheduled.html'},
            'offer_letter': {'priority': 'high', 'template': 'recruitment/offer_letter.html'},
            'application_rejected': {'priority': 'normal', 'template': 'recruitment/application_rejected.html'},
        },
        'onboarding': {
            'welcome_email': {'priority': 'high', 'template': 'onboarding/welcome_email.html'},
            'task_assigned': {'priority': 'normal', 'template': 'onboarding/task_assigned.html'},
            'document_required': {'priority': 'normal', 'template': 'onboarding/document_required.html'},
        },
        'system': {
            'announcement': {'priority': 'normal', 'template': 'system/announcement.html'},
            'maintenance_notice': {'priority': 'high', 'template': 'system/maintenance_notice.html'},
            'policy_update': {'priority': 'normal', 'template': 'system/policy_update.html'},
        }
    }
    
    # Default notification preferences
    DEFAULT_PREFERENCES = {
        'auth': True,  # Always enabled for security
        'leave': True,
        'payroll': True,
        'recruitment': True,
        'onboarding': True,
        'system': True,
    }
    
    @classmethod
    def get_notification_config(cls, notification_type: str, notification_subtype: str) -> Dict[str, Any]:
        """Get configuration for a specific notification"""
        return cls.NOTIFICATION_TYPES.get(notification_type, {}).get(notification_subtype, {})
    
    @classmethod
    def get_template_path(cls, notification_type: str, notification_subtype: str) -> str:
        """Get template path for a notification"""
        config = cls.get_notification_config(notification_type, notification_subtype)
        return config.get('template', 'system/default.html')
    
    @classmethod
    def get_priority(cls, notification_type: str, notification_subtype: str) -> str:
        """Get priority for a notification"""
        config = cls.get_notification_config(notification_type, notification_subtype)
        return config.get('priority', 'normal')
