"""
Leave Setup Configuration

Configuration settings for automatic leave policy setup when companies are created.
"""

import os
from typing import Dict, List

class LeaveSetupConfig:
    """Configuration for automatic leave policy setup."""
    
    # Enable/disable automatic leave setup
    AUTO_SETUP_ENABLED = os.getenv('AUTO_LEAVE_SETUP_ENABLED', 'true').lower() == 'true'
    
    # Country-specific setup configurations
    COUNTRY_SETUPS = {
        'RW': {
            'enabled': True,
            'service_class': 'RwandaLeaveSetupService',
            'description': 'Rwanda Labor Law compliant leave policies'
        },
        # Future countries can be added here
        # 'UG': {
        #     'enabled': True,
        #     'service_class': 'UgandaLeaveSetupService',
        #     'description': 'Uganda Labor Law compliant leave policies'
        # },
        # 'KE': {
        #     'enabled': True,
        #     'service_class': 'KenyaLeaveSetupService',
        #     'description': 'Kenya Labor Law compliant leave policies'
        # }
    }
    
    # Default country for companies without specified country
    DEFAULT_COUNTRY_SETUP = 'RW'  # Default to Rwanda setup
    
    @classmethod
    def is_auto_setup_enabled(cls) -> bool:
        """Check if automatic leave setup is enabled globally."""
        return cls.AUTO_SETUP_ENABLED
    
    @classmethod
    def get_country_setup_config(cls, country_code: str) -> Dict:
        """
        Get setup configuration for a specific country.
        
        Args:
            country_code (str): Country code (e.g., 'RW', 'UG')
            
        Returns:
            dict: Setup configuration or None if not supported
        """
        if not country_code:
            country_code = cls.DEFAULT_COUNTRY_SETUP
            
        return cls.COUNTRY_SETUPS.get(country_code.upper())
    
    @classmethod
    def should_setup_for_country(cls, country_code: str) -> bool:
        """
        Check if automatic setup should be performed for a country.
        
        Args:
            country_code (str): Country code
            
        Returns:
            bool: True if setup should be performed
        """
        if not cls.is_auto_setup_enabled():
            return False
            
        config = cls.get_country_setup_config(country_code)
        return config and config.get('enabled', False)
    
    @classmethod
    def get_supported_countries(cls) -> List[str]:
        """Get list of countries with automatic leave setup support."""
        return [
            country for country, config in cls.COUNTRY_SETUPS.items()
            if config.get('enabled', False)
        ]
