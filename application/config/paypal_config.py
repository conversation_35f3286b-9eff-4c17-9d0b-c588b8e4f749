import os
from flask import current_app

class PayPalConfig:
    """PayPal configuration management."""
    
    @staticmethod
    def get_paypal_config():
        """Get PayPal configuration from environment variables."""
        config = {
            # PayPal API Configuration
            'PAYPAL_MODE': os.getenv('PAYPAL_MODE', 'sandbox'),  # 'sandbox' or 'live'
            'PAYPAL_CLIENT_ID': os.getenv('PAYPAL_CLIENT_ID'),
            'PAYPAL_CLIENT_SECRET': os.getenv('PAYPAL_CLIENT_SECRET'),
            
            # PayPal API URLs
            'PAYPAL_BASE_URL': PayPalConfig.get_base_url(),
            
            # Webhook Configuration
            'PAYPAL_WEBHOOK_ID': os.getenv('PAYPAL_WEBHOOK_ID'),
            'PAYPAL_WEBHOOK_URL': os.getenv('PAYPAL_WEBHOOK_URL'),
            
            # Application URLs
            'PAYPAL_RETURN_URL': os.getenv('PAYPAL_RETURN_URL', 'http://localhost:3000/subscription/success'),
            'PAYPAL_CANCEL_URL': os.getenv('PAYPAL_CANCEL_URL', 'http://localhost:3000/subscription/cancel'),
            
            # Currency and Locale
            'PAYPAL_CURRENCY': os.getenv('PAYPAL_CURRENCY', 'USD'),
            'PAYPAL_LOCALE': os.getenv('PAYPAL_LOCALE', 'en_US'),
            
            # Billing Configuration
            'PAYPAL_BRAND_NAME': os.getenv('PAYPAL_BRAND_NAME', 'KaziSync HRMS'),
            'PAYPAL_LOGO_URL': os.getenv('PAYPAL_LOGO_URL'),
            
            # Retry Configuration
            'PAYPAL_MAX_RETRIES': int(os.getenv('PAYPAL_MAX_RETRIES', '3')),
            'PAYPAL_RETRY_DELAY': int(os.getenv('PAYPAL_RETRY_DELAY', '300')),  # seconds
            
            # Timeout Configuration
            'PAYPAL_TIMEOUT': int(os.getenv('PAYPAL_TIMEOUT', '30')),  # seconds
        }
        
        # Validate required configuration
        required_fields = ['PAYPAL_CLIENT_ID', 'PAYPAL_CLIENT_SECRET']
        missing_fields = [field for field in required_fields if not config.get(field)]
        
        #if missing_fields:
            #raise ValueError(f"Missing required PayPal configuration: {', '.join(missing_fields)}")
        
        return config
    
    @staticmethod
    def get_base_url():
        """Get PayPal base URL based on mode."""
        mode = os.getenv('PAYPAL_MODE', 'sandbox')
        if mode == 'live':
            return 'https://api.paypal.com'
        else:
            return 'https://api.sandbox.paypal.com'
    
    @staticmethod
    def get_web_url():
        """Get PayPal web URL for redirects."""
        mode = os.getenv('PAYPAL_MODE', 'sandbox')
        if mode == 'live':
            return 'https://www.paypal.com'
        else:
            return 'https://www.sandbox.paypal.com'
    
    @staticmethod
    def is_sandbox():
        """Check if running in sandbox mode."""
        return os.getenv('PAYPAL_MODE', 'sandbox') == 'sandbox'
    
    @staticmethod
    def is_production():
        """Check if running in production mode."""
        return os.getenv('PAYPAL_MODE', 'sandbox') == 'live'
    
    @staticmethod
    def validate_webhook_signature():
        """Check if webhook signature validation is enabled."""
        return os.getenv('PAYPAL_VALIDATE_WEBHOOK', 'true').lower() == 'true'
    
    @staticmethod
    def get_sdk_config():
        """Get configuration for PayPal JavaScript SDK."""
        config = PayPalConfig.get_paypal_config()
        
        return {
            'client_id': config['PAYPAL_CLIENT_ID'],
            'currency': config['PAYPAL_CURRENCY'],
            'locale': config['PAYPAL_LOCALE'],
            'intent': 'subscription',  # For subscription payments
            'vault': True,  # Enable vaulting for recurring payments
            'disable_funding': [],  # Allow all funding sources
            'enable_funding': ['card', 'paylater'],  # Explicitly enable cards and pay later
        }
    
    @staticmethod
    def get_billing_agreement_config():
        """Get configuration for billing agreements."""
        config = PayPalConfig.get_paypal_config()
        
        return {
            'name': f"{config['PAYPAL_BRAND_NAME']} Subscription",
            'description': 'Monthly subscription billing for HRMS services',
            'start_date': None,  # Will be set when creating agreement
            'payer': {
                'payment_method': 'paypal'
            },
            'plan': {
                'type': 'INFINITE',  # Ongoing subscription
                'frequency_interval': '1',
                'frequency': 'MONTH',
                'cycles': '0',  # Infinite cycles
                'amount': {
                    'currency': config['PAYPAL_CURRENCY'],
                    'value': '0.00'  # Will be set dynamically
                }
            }
        }
    
    @staticmethod
    def get_webhook_events():
        """Get list of webhook events to subscribe to."""
        return [
            'BILLING.SUBSCRIPTION.ACTIVATED',
            'BILLING.SUBSCRIPTION.CANCELLED',
            'BILLING.SUBSCRIPTION.CREATED',
            'BILLING.SUBSCRIPTION.SUSPENDED',
            'BILLING.SUBSCRIPTION.UPDATED',
            'PAYMENT.SALE.COMPLETED',
            'PAYMENT.SALE.DENIED',
            'PAYMENT.SALE.PENDING',
            'PAYMENT.SALE.REFUNDED',
            'PAYMENT.SALE.REVERSED',
            'BILLING.SUBSCRIPTION.PAYMENT.FAILED',
            'BILLING.SUBSCRIPTION.RE-ACTIVATED',
        ]
    
    @staticmethod
    def get_error_codes():
        """Get PayPal error code mappings."""
        return {
            'INSTRUMENT_DECLINED': 'Payment method declined',
            'PAYER_ACCOUNT_LOCKED_OR_CLOSED': 'PayPal account locked or closed',
            'PAYER_ACCOUNT_RESTRICTED': 'PayPal account restricted',
            'PAYER_CANNOT_PAY': 'Payer cannot complete payment',
            'TRANSACTION_REFUSED': 'Transaction refused',
            'INTERNAL_SERVICE_ERROR': 'PayPal internal error',
            'INVALID_ACCOUNT_STATUS': 'Invalid account status',
            'CREDIT_CARD_REFUSED': 'Credit card refused',
            'EXPIRED_CREDIT_CARD': 'Credit card expired',
            'INSUFFICIENT_FUNDS': 'Insufficient funds',
            'DUPLICATE_TRANSACTION': 'Duplicate transaction',
            'TRANSACTION_LIMIT_EXCEEDED': 'Transaction limit exceeded',
        }
    
    @staticmethod
    def log_config_status():
        """Log PayPal configuration status (without sensitive data)."""
        try:
            config = PayPalConfig.get_paypal_config()
            current_app.logger.info(f"PayPal Configuration:")
            current_app.logger.info(f"  Mode: {config['PAYPAL_MODE']}")
            current_app.logger.info(f"  Base URL: {config['PAYPAL_BASE_URL']}")
            current_app.logger.info(f"  Currency: {config['PAYPAL_CURRENCY']}")
            current_app.logger.info(f"  Brand Name: {config['PAYPAL_BRAND_NAME']}")
            current_app.logger.info(f"  Client ID: {'*' * (len(config['PAYPAL_CLIENT_ID']) - 4) + config['PAYPAL_CLIENT_ID'][-4:]}")
            current_app.logger.info(f"  Webhook URL: {config.get('PAYPAL_WEBHOOK_URL', 'Not configured')}")
        except Exception as e:
            current_app.logger.error(f"Error logging PayPal config: {e}")

# Environment variable template for .env file
PAYPAL_ENV_TEMPLATE = """
# PayPal Configuration
PAYPAL_MODE=sandbox
PAYPAL_CLIENT_ID=your_paypal_client_id_here
PAYPAL_CLIENT_SECRET=your_paypal_client_secret_here
PAYPAL_WEBHOOK_ID=your_webhook_id_here
PAYPAL_WEBHOOK_URL=https://yourdomain.com/api/paypal/webhook
PAYPAL_RETURN_URL=https://yourdomain.com/subscription/success
PAYPAL_CANCEL_URL=https://yourdomain.com/subscription/cancel
PAYPAL_CURRENCY=USD
PAYPAL_BRAND_NAME=KaziSync HRMS
PAYPAL_LOGO_URL=https://yourdomain.com/logo.png
PAYPAL_MAX_RETRIES=3
PAYPAL_RETRY_DELAY=300
PAYPAL_TIMEOUT=30
PAYPAL_VALIDATE_WEBHOOK=true
"""
