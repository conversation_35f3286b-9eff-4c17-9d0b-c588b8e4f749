from functools import wraps
from flask import request, jsonify, g, current_app as app
from application.Helpers.apitoken import APIToken
from application.Models.apiclient.api_client import APIClient

def api_client_required(f):
    """
    Decorator to validate API client tokens and automatically extract company_id.
    
    This decorator:
    1. Validates the Bearer token in Authorization header
    2. Decodes the JWT to extract client_id and company_id
    3. Verifies the API client is still active
    4. Makes company_id and client info available via Flask's g object
    5. Handles all authentication errors with standardized responses
    
    Usage:
        @api_client_required
        def my_endpoint():
            company_id = g.api_client_company_id  # Automatically available
            client_info = g.api_client  # Full client info
    """
    @wraps(f)
    def decorated(*args, **kwargs):
        try:
            # Extract token from Authorization header
            token = None
            if 'Authorization' in request.headers:
                auth_header = request.headers['Authorization']
                if auth_header.startswith('Bearer '):
                    token = auth_header.split(" ")[1]
            
            if not token:
                return jsonify({
                    "success": False,
                    "error": {
                        "code": "MISSING_API_TOKEN",
                        "message": "API access token is required",
                        "details": "Include your API token in the Authorization header: 'Bearer YOUR_TOKEN'"
                    }
                }), 401
            
            # Decode the JWT token
            decoded_payload = APIToken.decode(token)
            if not decoded_payload:
                return jsonify({
                    "success": False,
                    "error": {
                        "code": "INVALID_API_TOKEN",
                        "message": "API token is invalid or expired",
                        "details": "Please obtain a new token using your client credentials"
                    }
                }), 401
            
            # Extract required fields from token
            client_id = decoded_payload.get('client_id')
            company_id = decoded_payload.get('company_id')
            
            if not client_id or not company_id:
                return jsonify({
                    "success": False,
                    "error": {
                        "code": "MALFORMED_API_TOKEN",
                        "message": "API token is missing required information",
                        "details": "Token must contain valid client_id and company_id"
                    }
                }), 401
            
            # Verify the API client is still active (security check)
            from application.database import central_db
            api_client = central_db.session.query(APIClient).filter_by(
                client_id=client_id,
                is_active=True
            ).first()

            if not api_client:
                return jsonify({
                    "success": False,
                    "error": {
                        "code": "API_CLIENT_INACTIVE",
                        "message": "API client is inactive or not found",
                        "details": "Your API client may have been deactivated. Contact support."
                    }
                }), 401

            # Update last used timestamp
            api_client.update_last_used()
            
            # Store client information in Flask's g object for use in the endpoint
            g.api_client_company_id = company_id
            g.api_client_id = client_id
            g.api_client = {
                'client_id': client_id,
                'company_id': company_id,
                'name': decoded_payload.get('name'),
                'token_payload': decoded_payload
            }
            
            # Log API access for monitoring
            app.logger.info(f"[API_CLIENT] Access granted - Client: {client_id}, Company: {company_id}, Endpoint: {request.endpoint}")
            
            return f(*args, **kwargs)
            
        except Exception as e:
            app.logger.error(f"[API_CLIENT] Authentication error: {str(e)}")
            return jsonify({
                "success": False,
                "error": {
                    "code": "AUTHENTICATION_ERROR",
                    "message": "An error occurred during authentication",
                    "details": "Please try again or contact support if the issue persists"
                }
            }), 500
    
    return decorated
