from functools import wraps
from flask import jsonify, g, current_app as app
from datetime import datetime, timedelta
from typing import Dict, Any
import threading

# In-memory rate limiting storage (for production, use Redis)
# Structure: {client_id: {'count': int, 'reset_time': datetime, 'requests': [timestamps]}}
rate_limit_storage: Dict[str, Dict[str, Any]] = {}
storage_lock = threading.Lock()

def cleanup_rate_limit_storage():
    """Clean up expired rate limit entries"""
    current_time = datetime.utcnow()
    with storage_lock:
        expired_clients = []
        for client_id, data in rate_limit_storage.items():
            if current_time > data.get('reset_time', current_time):
                expired_clients.append(client_id)
        
        for client_id in expired_clients:
            del rate_limit_storage[client_id]

def api_rate_limit(requests_per_hour: int = 1000, requests_per_minute: int = 100):
    """
    Rate limiting decorator for API clients.
    
    Args:
        requests_per_hour: Maximum requests per hour per client
        requests_per_minute: Maximum requests per minute per client (burst protection)
    
    Usage:
        @api_client_required
        @api_rate_limit(requests_per_hour=500, requests_per_minute=50)
        def my_endpoint():
            pass
    """
    def decorator(f):
        @wraps(f)
        def decorated(*args, **kwargs):
            # This decorator should be used after @api_client_required
            client_id = getattr(g, 'api_client_id', None)
            if not client_id:
                app.logger.error("[RATE_LIMIT] No client_id found in context. Use @api_client_required first.")
                return jsonify({
                    "success": False,
                    "error": {
                        "code": "AUTHENTICATION_REQUIRED",
                        "message": "Authentication required for rate limiting"
                    }
                }), 401
            
            cleanup_rate_limit_storage()
            current_time = datetime.utcnow()
            
            with storage_lock:
                # Initialize client data if not exists
                if client_id not in rate_limit_storage:
                    rate_limit_storage[client_id] = {
                        'hourly_count': 0,
                        'hourly_reset_time': current_time + timedelta(hours=1),
                        'minute_requests': [],
                        'minute_reset_time': current_time + timedelta(minutes=1)
                    }
                
                client_data = rate_limit_storage[client_id]
                
                # Check hourly limit
                if current_time > client_data['hourly_reset_time']:
                    # Reset hourly counter
                    client_data['hourly_count'] = 0
                    client_data['hourly_reset_time'] = current_time + timedelta(hours=1)
                
                if client_data['hourly_count'] >= requests_per_hour:
                    reset_time = client_data['hourly_reset_time']
                    minutes_until_reset = int((reset_time - current_time).total_seconds() / 60)
                    
                    return jsonify({
                        "success": False,
                        "error": {
                            "code": "RATE_LIMIT_EXCEEDED_HOURLY",
                            "message": f"Hourly rate limit of {requests_per_hour} requests exceeded",
                            "details": f"Rate limit resets in {minutes_until_reset} minutes",
                            "retry_after": minutes_until_reset * 60  # seconds
                        }
                    }), 429
                
                # Check per-minute limit (burst protection)
                # Clean old minute requests
                minute_ago = current_time - timedelta(minutes=1)
                client_data['minute_requests'] = [
                    req_time for req_time in client_data['minute_requests'] 
                    if req_time > minute_ago
                ]
                
                if len(client_data['minute_requests']) >= requests_per_minute:
                    return jsonify({
                        "success": False,
                        "error": {
                            "code": "RATE_LIMIT_EXCEEDED_MINUTE",
                            "message": f"Per-minute rate limit of {requests_per_minute} requests exceeded",
                            "details": "Please slow down your request rate",
                            "retry_after": 60  # seconds
                        }
                    }), 429
                
                # Record this request
                client_data['hourly_count'] += 1
                client_data['minute_requests'].append(current_time)
            
            # Add rate limit headers to response
            response = f(*args, **kwargs)
            
            # If response is a tuple (response, status_code), handle it
            if isinstance(response, tuple):
                response_obj, status_code = response[0], response[1]
            else:
                response_obj, status_code = response, 200
            
            # Add rate limit headers
            if hasattr(response_obj, 'headers'):
                remaining_hourly = max(0, requests_per_hour - client_data['hourly_count'])
                remaining_minute = max(0, requests_per_minute - len(client_data['minute_requests']))
                
                response_obj.headers['X-RateLimit-Limit-Hour'] = str(requests_per_hour)
                response_obj.headers['X-RateLimit-Remaining-Hour'] = str(remaining_hourly)
                response_obj.headers['X-RateLimit-Limit-Minute'] = str(requests_per_minute)
                response_obj.headers['X-RateLimit-Remaining-Minute'] = str(remaining_minute)
                response_obj.headers['X-RateLimit-Reset'] = str(int(client_data['hourly_reset_time'].timestamp()))
            
            return response if isinstance(response, tuple) else (response_obj, status_code)
        
        return decorated
    return decorator
