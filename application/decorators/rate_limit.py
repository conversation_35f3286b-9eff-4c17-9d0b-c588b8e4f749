"""Rate limiting decorator for API endpoints."""

from functools import wraps
from flask import request, jsonify, g, current_app as app
from datetime import datetime, timedelta
import json

# In-memory rate limit store (in production, use Redis)
rate_limit_store = {}

def rate_limit(max_requests=5, window_minutes=60, per_user=True, per_ip=False):
    """
    Rate limiting decorator.
    
    Args:
        max_requests: Maximum number of requests allowed
        window_minutes: Time window in minutes
        per_user: Apply limit per authenticated user
        per_ip: Apply limit per IP address
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Determine the key for rate limiting
            key_parts = [f.__name__]
            
            if per_user:
                user_id = getattr(g, 'user', {}).get('user_id')
                if user_id:
                    key_parts.append(f"user:{user_id}")
                else:
                    return jsonify({"error": "Authentication required for rate limiting"}), 401
            
            if per_ip:
                ip = request.remote_addr
                key_parts.append(f"ip:{ip}")
            
            rate_key = ":".join(key_parts)
            
            # Clean up old entries
            now = datetime.utcnow()
            cutoff = now - timedelta(minutes=window_minutes)
            
            if rate_key in rate_limit_store:
                # Remove old requests outside the window
                rate_limit_store[rate_key] = [
                    timestamp for timestamp in rate_limit_store[rate_key] 
                    if timestamp > cutoff
                ]
            else:
                rate_limit_store[rate_key] = []
            
            # Check if limit exceeded
            if len(rate_limit_store[rate_key]) >= max_requests:
                app.logger.warning(f"[RATE_LIMIT] Limit exceeded for {rate_key} - {len(rate_limit_store[rate_key])} requests in {window_minutes} minutes")
                return jsonify({
                    "error": "Rate limit exceeded",
                    "message": f"Maximum {max_requests} requests per {window_minutes} minutes allowed",
                    "retry_after": window_minutes * 60  # seconds
                }), 429
            
            # Add current request timestamp
            rate_limit_store[rate_key].append(now)
            
            # Log rate limit usage
            app.logger.info(f"[RATE_LIMIT] {rate_key}: {len(rate_limit_store[rate_key])}/{max_requests} requests used")
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator
