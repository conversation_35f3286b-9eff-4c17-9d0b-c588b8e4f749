from functools import wraps
from flask import request, jsonify, g

def roles_required(*roles):
    def decorator(f):
        @wraps(f)
        def wrapper(*args, **kwargs):
            user = getattr(g, 'user', None)
            if not user:
                return jsonify({'message': 'User not found in context. Use @token_required first.'}), 401

            if user.get('role') not in roles:
                return jsonify({'message': 'You do not have permission to access this resource.'}), 403

            return f(*args, **kwargs)
        return wrapper
    return decorator
