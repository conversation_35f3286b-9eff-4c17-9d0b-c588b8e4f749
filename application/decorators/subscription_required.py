from functools import wraps
from flask import jsonify, g, current_app
from application.Models.feature_access import FeatureAccess
from application.Models.company_subscription import CompanySubscription

def subscription_required(feature_name=None, increment_usage=False):
    """
    Decorator to check if a company has access to a specific feature.
    
    Args:
        feature_name (str): Name of the feature to check access for
        increment_usage (bool): Whether to increment usage count when feature is used
    
    Returns:
        Function decorator that checks subscription and feature access
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # Get user context from token
                user_info = getattr(g, 'user', None)
                if not user_info:
                    return jsonify({"message": "Authentication required"}), 401

                # Get company_id from user context or request
                company_id = user_info.get('company_id')
                if not company_id:
                    # For central users (HR), get company_id from request
                    from flask import request
                    company_id = request.args.get('company_id') or (
                        request.json.get('company_id') if request.json else None
                    )

                if not company_id:
                    return jsonify({"message": "Company ID is required"}), 400

                # Check if company has an active subscription
                subscription = CompanySubscription.get_subscription_by_company(company_id)
                if not subscription:
                    return jsonify({
                        "message": "No subscription found. Please contact support to set up a subscription.",
                        "error_code": "NO_SUBSCRIPTION"
                    }), 403

                if not subscription.is_active():
                    status_messages = {
                        'TRIAL': "Your trial period has expired. Please upgrade to continue using this service.",
                        'SUSPENDED': "Your subscription has been suspended. Please contact support or update your payment method.",
                        'CANCELLED': "Your subscription has been cancelled. Please contact support to reactivate."
                    }
                    message = status_messages.get(subscription.status, "Your subscription is not active.")
                    
                    return jsonify({
                        "message": message,
                        "error_code": "SUBSCRIPTION_INACTIVE",
                        "subscription_status": subscription.status,
                        "subscription_details": subscription.to_dict()
                    }), 403

                # Check specific feature access if feature_name is provided
                if feature_name:
                    has_access = FeatureAccess.check_feature_access(company_id, feature_name)
                    if not has_access:
                        return jsonify({
                            "message": f"Your subscription plan does not include access to '{feature_name}'. Please upgrade your plan.",
                            "error_code": "FEATURE_NOT_AVAILABLE",
                            "feature_name": feature_name,
                            "current_plan": subscription.plan.name if subscription.plan else "Unknown"
                        }), 403

                    # Increment usage if requested
                    if increment_usage:
                        success, error = FeatureAccess.increment_usage(company_id, feature_name)
                        if not success:
                            return jsonify({
                                "message": f"Feature usage limit exceeded for '{feature_name}'. {error}",
                                "error_code": "USAGE_LIMIT_EXCEEDED",
                                "feature_name": feature_name
                            }), 403

                # Add subscription info to g for use in the endpoint
                g.subscription = subscription
                g.company_id = company_id

                return f(*args, **kwargs)

            except Exception as e:
                current_app.logger.error(f"Error in subscription_required decorator: {e}")
                return jsonify({
                    "message": "An error occurred while checking subscription access",
                    "error_code": "SUBSCRIPTION_CHECK_ERROR"
                }), 500

        return decorated_function
    return decorator


def feature_usage_tracking(feature_name):
    """
    Decorator to track feature usage without blocking access.
    Useful for analytics and monitoring.
    
    Args:
        feature_name (str): Name of the feature to track usage for
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # Get user context
                user_info = getattr(g, 'user', None)
                if user_info:
                    company_id = user_info.get('company_id')
                    if not company_id:
                        from flask import request
                        company_id = request.args.get('company_id') or (
                            request.json.get('company_id') if request.json else None
                        )

                    if company_id:
                        # Track usage (don't block if it fails)
                        FeatureAccess.increment_usage(company_id, feature_name)

                return f(*args, **kwargs)

            except Exception as e:
                current_app.logger.error(f"Error in feature_usage_tracking decorator: {e}")
                # Don't block the request if tracking fails
                return f(*args, **kwargs)

        return decorated_function
    return decorator


def check_employee_limit():
    """
    Decorator to check if adding a new employee would exceed the subscription limit.
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # Get user context
                user_info = getattr(g, 'user', None)
                if not user_info:
                    return jsonify({"message": "Authentication required"}), 401

                company_id = user_info.get('company_id')
                if not company_id:
                    from flask import request
                    company_id = request.args.get('company_id') or (
                        request.json.get('company_id') if request.json else None
                    )

                if not company_id:
                    return jsonify({"message": "Company ID is required"}), 400

                # Get subscription
                subscription = CompanySubscription.get_subscription_by_company(company_id)
                if not subscription or not subscription.is_active():
                    return jsonify({
                        "message": "Active subscription required to add employees",
                        "error_code": "SUBSCRIPTION_REQUIRED"
                    }), 403

                # Check employee limit
                if subscription.plan and subscription.plan.max_employees:
                    current_count = subscription.employee_count
                    if current_count >= subscription.plan.max_employees:
                        return jsonify({
                            "message": f"Employee limit reached. Your plan allows up to {subscription.plan.max_employees} employees. Please upgrade your plan to add more employees.",
                            "error_code": "EMPLOYEE_LIMIT_EXCEEDED",
                            "current_count": current_count,
                            "max_allowed": subscription.plan.max_employees,
                            "plan_name": subscription.plan.name
                        }), 403

                return f(*args, **kwargs)

            except Exception as e:
                current_app.logger.error(f"Error in check_employee_limit decorator: {e}")
                return jsonify({
                    "message": "An error occurred while checking employee limit",
                    "error_code": "EMPLOYEE_LIMIT_CHECK_ERROR"
                }), 500

        return decorated_function
    return decorator


def subscription_status_check():
    """
    Decorator to add subscription status information to the response.
    Useful for dashboard endpoints that need to show subscription info.
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # Execute the original function
                result = f(*args, **kwargs)

                # Get user context
                user_info = getattr(g, 'user', None)
                if user_info:
                    company_id = user_info.get('company_id')
                    if not company_id:
                        from flask import request
                        company_id = request.args.get('company_id') or (
                            request.json.get('company_id') if request.json else None
                        )

                    if company_id:
                        subscription = CompanySubscription.get_subscription_by_company(company_id)
                        if subscription:
                            # Add subscription info to response if it's a JSON response
                            if hasattr(result, 'json') and result.json:
                                result.json['subscription_status'] = {
                                    'status': subscription.status,
                                    'plan_name': subscription.plan.name if subscription.plan else None,
                                    'employee_count': subscription.employee_count,
                                    'days_until_renewal': subscription.days_until_renewal(),
                                    'is_trial': subscription.is_trial(),
                                    'trial_days_remaining': subscription.days_until_trial_end() if subscription.is_trial() else None
                                }

                return result

            except Exception as e:
                current_app.logger.error(f"Error in subscription_status_check decorator: {e}")
                # Return original result if status check fails
                return f(*args, **kwargs)

        return decorated_function
    return decorator
