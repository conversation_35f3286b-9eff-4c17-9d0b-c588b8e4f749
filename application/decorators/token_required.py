from functools import wraps
from flask import request, jsonify, g
from application.Models.refreshtoken import RefreshToken

def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None

        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            if auth_header.startswith('Bearer '):
                token = auth_header.split(" ")[1]

        if not token:
            return jsonify({'message': 'Access token is missing'}), 401

        decoded = RefreshToken.decode_token(token)
        if not decoded:
            return jsonify({'message': 'Token is invalid or expired'}), 401

        g.user = decoded  # store token info globally for this request
        return f(*args, **kwargs)

    return decorated
