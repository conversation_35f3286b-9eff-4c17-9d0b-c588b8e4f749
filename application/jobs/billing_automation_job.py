import schedule
import time
import threading
from datetime import datetime, date
from flask import current_app
from application.services.billing.subscription_billing_service import SubscriptionBillingService
from application.Models.company_subscription import CompanySubscription

class BillingAutomationJob:
    """Automated billing job for subscription management."""
    
    def __init__(self):
        self.billing_service = SubscriptionBillingService()
        self.is_running = False
        self.thread = None
        self.stop_event = threading.Event()
    
    def start(self):
        """Start the billing automation job."""
        if self.is_running:
            current_app.logger.warning("Billing automation job is already running")
            return
        
        self.is_running = True
        self.stop_event.clear()
        
        # Schedule jobs
        self.schedule_jobs()
        
        # Start the scheduler thread
        self.thread = threading.Thread(target=self.run_scheduler, daemon=True)
        self.thread.start()
        
        current_app.logger.info("Billing automation job started")
    
    def stop(self):
        """Stop the billing automation job."""
        if not self.is_running:
            return
        
        self.is_running = False
        self.stop_event.set()
        
        if self.thread and self.thread.is_alive():
            self.thread.join(timeout=5)
        
        # Clear all scheduled jobs
        schedule.clear()
        
        current_app.logger.info("Billing automation job stopped")
    
    def schedule_jobs(self):
        """Schedule all billing-related jobs."""
        # Daily job to process due subscriptions (runs at 2 AM)
        schedule.every().day.at("02:00").do(self.process_due_subscriptions)
        
        # Daily job to update employee counts (runs at 1 AM)
        schedule.every().day.at("01:00").do(self.update_employee_counts)
        
        # Weekly job to process subscriptions with employee changes (runs on Mondays at 3 AM)
        schedule.every().monday.at("03:00").do(self.process_employee_changes)
        
        # Monthly job to generate billing reports (runs on 1st of month at 4 AM)
        schedule.every().month.do(self.generate_monthly_reports)
        
        # Hourly job to retry failed payments (during business hours)
        for hour in range(9, 18):  # 9 AM to 5 PM
            schedule.every().day.at(f"{hour:02d}:00").do(self.retry_failed_payments)
        
        current_app.logger.info("Billing automation jobs scheduled")
    
    def run_scheduler(self):
        """Run the job scheduler."""
        while self.is_running and not self.stop_event.is_set():
            try:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
            except Exception as e:
                current_app.logger.error(f"Error in billing scheduler: {e}")
                time.sleep(300)  # Wait 5 minutes before retrying
    
    def process_due_subscriptions(self):
        """Process all subscriptions that are due for billing."""
        try:
            current_app.logger.info("Starting daily billing process")
            
            with current_app.app_context():
                results = self.billing_service.process_all_due_subscriptions()
                
                current_app.logger.info(
                    f"Daily billing completed: {results['successful']} successful, "
                    f"{results['failed']} failed out of {results['total']} total"
                )
                
                # Log errors for failed billings
                for error in results['errors']:
                    current_app.logger.error(
                        f"Billing failed for subscription {error.get('subscription_id', 'unknown')}: "
                        f"{error.get('error', 'Unknown error')}"
                    )
                
                return results
                
        except Exception as e:
            current_app.logger.error(f"Error in daily billing process: {e}")
            return None
    
    def update_employee_counts(self):
        """Update employee counts for all active subscriptions."""
        try:
            current_app.logger.info("Starting employee count update")
            
            with current_app.app_context():
                results = self.billing_service.process_employee_count_changes()
                
                current_app.logger.info(
                    f"Employee count update completed: {results['processed']} updated "
                    f"out of {results['total']} total"
                )
                
                return results
                
        except Exception as e:
            current_app.logger.error(f"Error updating employee counts: {e}")
            return None
    
    def process_employee_changes(self):
        """Process billing for subscriptions with employee count changes."""
        try:
            current_app.logger.info("Processing subscriptions with employee changes")
            
            with current_app.app_context():
                # Get subscriptions with employee count changes
                changed_subscriptions = CompanySubscription.get_subscriptions_with_employee_changes()
                
                results = {
                    'total': len(changed_subscriptions),
                    'processed': 0,
                    'failed': 0,
                    'errors': []
                }
                
                for subscription in changed_subscriptions:
                    try:
                        # Process immediate billing for employee count changes
                        success, error = self.billing_service.process_subscription_billing(subscription)
                        
                        if success:
                            results['processed'] += 1
                        else:
                            results['failed'] += 1
                            results['errors'].append({
                                'subscription_id': str(subscription.subscription_id),
                                'error': error
                            })
                            
                    except Exception as e:
                        results['failed'] += 1
                        results['errors'].append({
                            'subscription_id': str(subscription.subscription_id),
                            'error': str(e)
                        })
                
                current_app.logger.info(
                    f"Employee change billing completed: {results['processed']} successful, "
                    f"{results['failed']} failed out of {results['total']} total"
                )
                
                return results
                
        except Exception as e:
            current_app.logger.error(f"Error processing employee changes: {e}")
            return None
    
    def retry_failed_payments(self):
        """Retry failed payments during business hours."""
        try:
            current_app.logger.info("Retrying failed payments")
            
            with current_app.app_context():
                # Get subscriptions with failed billing attempts
                failed_subscriptions = CompanySubscription.query.filter(
                    CompanySubscription.auto_billing_enabled == True,
                    CompanySubscription.billing_retry_count > 0,
                    CompanySubscription.billing_retry_count < 3,
                    CompanySubscription.status.in_(['TRIAL', 'ACTIVE'])
                ).all()
                
                results = {
                    'total': len(failed_subscriptions),
                    'successful': 0,
                    'failed': 0,
                    'errors': []
                }
                
                for subscription in failed_subscriptions:
                    try:
                        # Only retry if last attempt was more than 4 hours ago
                        if (subscription.last_billing_attempt and 
                            (datetime.now() - subscription.last_billing_attempt).total_seconds() < 14400):
                            continue
                        
                        success, error = self.billing_service.process_subscription_billing(subscription)
                        
                        if success:
                            results['successful'] += 1
                            current_app.logger.info(f"Retry successful for subscription {subscription.subscription_id}")
                        else:
                            results['failed'] += 1
                            results['errors'].append({
                                'subscription_id': str(subscription.subscription_id),
                                'error': error
                            })
                            
                    except Exception as e:
                        results['failed'] += 1
                        results['errors'].append({
                            'subscription_id': str(subscription.subscription_id),
                            'error': str(e)
                        })
                
                if results['total'] > 0:
                    current_app.logger.info(
                        f"Payment retry completed: {results['successful']} successful, "
                        f"{results['failed']} failed out of {results['total']} total"
                    )
                
                return results
                
        except Exception as e:
            current_app.logger.error(f"Error retrying failed payments: {e}")
            return None
    
    def generate_monthly_reports(self):
        """Generate monthly billing reports."""
        try:
            current_app.logger.info("Generating monthly billing reports")
            
            with current_app.app_context():
                # This would generate reports for the previous month
                # Implementation depends on your reporting requirements
                
                from datetime import datetime, timedelta
                from calendar import monthrange
                
                # Get previous month date range
                today = date.today()
                if today.month == 1:
                    prev_month = 12
                    prev_year = today.year - 1
                else:
                    prev_month = today.month - 1
                    prev_year = today.year
                
                # Get all billing activity for previous month
                start_date = date(prev_year, prev_month, 1)
                end_date = date(prev_year, prev_month, monthrange(prev_year, prev_month)[1])
                
                # Query billing data (this would be expanded based on your needs)
                from application.Models.subscription_payment import SubscriptionPayment
                
                payments = SubscriptionPayment.query.filter(
                    SubscriptionPayment.payment_date >= start_date,
                    SubscriptionPayment.payment_date <= end_date,
                    SubscriptionPayment.status == 'COMPLETED'
                ).all()
                
                total_revenue = sum(float(payment.amount) for payment in payments)
                total_transactions = len(payments)
                
                current_app.logger.info(
                    f"Monthly report generated: {total_transactions} transactions, "
                    f"${total_revenue:.2f} total revenue for {prev_month}/{prev_year}"
                )
                
                return {
                    'month': prev_month,
                    'year': prev_year,
                    'total_revenue': total_revenue,
                    'total_transactions': total_transactions,
                    'payments': [payment.to_dict() for payment in payments]
                }
                
        except Exception as e:
            current_app.logger.error(f"Error generating monthly reports: {e}")
            return None
    
    def get_status(self):
        """Get the current status of the billing automation job."""
        return {
            'is_running': self.is_running,
            'thread_alive': self.thread.is_alive() if self.thread else False,
            'scheduled_jobs': len(schedule.jobs),
            'next_run': str(schedule.next_run()) if schedule.jobs else None
        }

# Global instance
billing_automation_job = BillingAutomationJob()
