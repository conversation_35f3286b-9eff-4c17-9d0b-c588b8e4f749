import os
import json
import zipfile
import tempfile
import shutil
from flask import Blueprint, request, jsonify, send_file, current_app as app

sdk_generator = Blueprint('sdk_generator', __name__)

# Define supported languages
SUPPORTED_LANGUAGES = {
    'python': {
        'name': 'Python',
        'extension': '.py',
        'template_dir': 'python_sdk_template'
    },
    'javascript': {
        'name': 'JavaScript',
        'extension': '.js',
        'template_dir': 'javascript_sdk_template'
    },
    'java': {
        'name': 'Java',
        'extension': '.java',
        'template_dir': 'java_sdk_template'
    },
    'csharp': {
        'name': 'C#',
        'extension': '.cs',
        'template_dir': 'csharp_sdk_template'
    },
    'php': {
        'name': 'PHP',
        'extension': '.php',
        'template_dir': 'php_sdk_template'
    }
}

@sdk_generator.route('/api/sdk/languages', methods=['GET'])
def get_supported_languages():
    """Get a list of supported SDK languages"""
    languages = []
    for key, value in SUPPORTED_LANGUAGES.items():
        languages.append({
            'id': key,
            'name': value['name']
        })
    return jsonify({'languages': languages})

@sdk_generator.route('/api/sdk/generate/<language>', methods=['GET'])
def generate_sdk(language):
    """Generate an SDK for the specified language"""
    if language not in SUPPORTED_LANGUAGES:
        return jsonify({
            'success': False,
            'message': f'Language {language} is not supported. Supported languages are: {", ".join(SUPPORTED_LANGUAGES.keys())}'
        }), 400

    # Get the base URL from the request or use a default
    base_url = request.args.get('base_url', request.host_url.rstrip('/'))

    try:
        # Create a temporary directory for the SDK
        with tempfile.TemporaryDirectory() as temp_dir:
            sdk_dir = os.path.join(temp_dir, f'attendance_api_sdk_{language}')
            os.makedirs(sdk_dir, exist_ok=True)

            # Generate the SDK files based on the language
            generate_sdk_files(sdk_dir, language, base_url)

            # Create a zip file
            zip_path = os.path.join(temp_dir, f'attendance_api_sdk_{language}.zip')
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, _, files in os.walk(sdk_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, temp_dir)
                        zipf.write(file_path, arcname)

            # Send the zip file
            return send_file(
                zip_path,
                mimetype='application/zip',
                as_attachment=True,
                download_name=f'attendance_api_sdk_{language}.zip'
            )
    except Exception as e:
        app.logger.error(f"Error generating SDK: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Failed to generate SDK',
            'error': str(e)
        }), 500

def generate_sdk_files(sdk_dir, language, base_url):
    """Generate SDK files for the specified language"""
    lang_info = SUPPORTED_LANGUAGES[language]

    # Check if template directory exists
    template_dir = os.path.join(app.root_path, 'sdk_templates', lang_info['template_dir'])
    if os.path.exists(template_dir):
        # Copy template files
        for item in os.listdir(template_dir):
            s = os.path.join(template_dir, item)
            d = os.path.join(sdk_dir, item)
            if os.path.isdir(s):
                shutil.copytree(s, d)
            else:
                shutil.copy2(s, d)
    else:
        # Generate basic SDK files if template doesn't exist
        generate_basic_sdk(sdk_dir, language, base_url)

def generate_basic_sdk(sdk_dir, language, base_url):
    """Generate basic SDK files for the specified language"""
    lang_info = SUPPORTED_LANGUAGES[language]

    # Get OpenAPI specification
    from application.api_docs import api
    spec = api.__schema__

    # Add base URL to the specification
    spec['servers'] = [{'url': base_url}]

    # Save OpenAPI specification
    with open(os.path.join(sdk_dir, 'openapi.json'), 'w') as f:
        json.dump(spec, f, indent=2)

    # Generate README
    with open(os.path.join(sdk_dir, 'README.md'), 'w') as f:
        f.write(f"""# Attendance API SDK for {lang_info['name']}

This SDK provides a client for the Attendance API.

## Installation

[Installation instructions for {lang_info['name']}]

## Usage

```{language}
// Example code for using the SDK
```

## API Documentation

For full API documentation, visit: {base_url}/api/docs

## License

MIT
""")

    # Generate basic client based on language
    if language == 'python':
        generate_python_sdk(sdk_dir, base_url)
    elif language == 'javascript':
        generate_javascript_sdk(sdk_dir, base_url)
    elif language == 'java':
        generate_java_sdk(sdk_dir, base_url)
    elif language == 'csharp':
        generate_csharp_sdk(sdk_dir, base_url)
    elif language == 'php':
        generate_php_sdk(sdk_dir, base_url)

def generate_python_sdk(sdk_dir, base_url):
    """Generate Python SDK files"""
    # Create package structure
    os.makedirs(os.path.join(sdk_dir, 'attendance_api'), exist_ok=True)

    # Create __init__.py
    with open(os.path.join(sdk_dir, 'attendance_api', '__init__.py'), 'w') as f:
        f.write("from .client import AttendanceApiClient\n\n__all__ = ['AttendanceApiClient']\n")

    # Create client.py
    client_code = """import requests
import json

class AttendanceApiClient:
    def __init__(self, base_url='BASE_URL_PLACEHOLDER', api_key=None):
        self.base_url = base_url
        self.api_key = api_key
        self.session = requests.Session()
        if api_key:
            self.session.headers.update({'Authorization': f'Bearer {api_key}'})

    def login(self, username, password):
        \"\"\"Login a user and get authentication tokens\"\"\"
        url = f'{self.base_url}/login'
        response = self.session.post(url, json={'username': username, 'password': password})
        data = response.json()
        if response.status_code == 200 and data.get('authenticated'):
            self.session.headers.update({'Authorization': f'Bearer {data[\"access_token\"]}'})
            return data
        raise Exception(f'Login failed: {data.get(\"message\", \"Unknown error\")}')

    def get_user(self, user_id):
        \"\"\"Get a user by ID\"\"\"
        url = f'{self.base_url}/get_user/{user_id}'
        response = self.session.get(url)
        return response.json()

    def get_companies(self):
        \"\"\"Get all companies\"\"\"
        url = f'{self.base_url}/get_companies'
        response = self.session.get(url)
        return response.json()

    def add_company(self, company_data):
        \"\"\"Add a new company\"\"\"
        url = f'{self.base_url}/add_company'
        response = self.session.post(url, json=company_data)
        return response.json()

    def get_employees(self, company_id):
        \"\"\"Get all employees for a company\"\"\"
        url = f'{self.base_url}/api/employees'
        response = self.session.get(url, params={'company_id': company_id})
        return response.json()

    def create_employee(self, employee_data):
        \"\"\"Create a new employee\"\"\"
        url = f'{self.base_url}/api/employees'
        response = self.session.post(url, json=employee_data)
        return response.json()
"""

    # Replace the placeholder with the actual base URL
    client_code = client_code.replace('BASE_URL_PLACEHOLDER', base_url)

    # Write the client code to file
    with open(os.path.join(sdk_dir, 'attendance_api', 'client.py'), 'w') as f:
        f.write(client_code)

    # Create setup.py
    with open(os.path.join(sdk_dir, 'setup.py'), 'w') as f:
        f.write("""from setuptools import setup, find_packages

setup(
    name="attendance-api-client",
    version="1.0.0",
    packages=find_packages(),
    install_requires=[
        "requests>=2.25.1",
    ],
    author="Your Name",
    author_email="<EMAIL>",
    description="A client library for the Attendance API",
    keywords="attendance, api, client",
    url="https://example.com",
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
    ],
)
""")

def generate_javascript_sdk(sdk_dir, base_url):
    """Generate JavaScript SDK files"""
    # Create package.json
    with open(os.path.join(sdk_dir, 'package.json'), 'w') as f:
        f.write("""{
  "name": "attendance-api-client",
  "version": "1.0.0",
  "description": "A client library for the Attendance API",
  "main": "index.js",
  "scripts": {
    "test": "echo \\"Error: no test specified\\" && exit 1"
  },
  "keywords": [
    "attendance",
    "api",
    "client"
  ],
  "author": "Your Name",
  "license": "MIT",
  "dependencies": {
    "axios": "^0.21.1"
  }
}
""")

    # Create index.js
    js_code = """const axios = require('axios');

class AttendanceApiClient {
  constructor(baseUrl = 'BASE_URL_PLACEHOLDER', apiKey = null) {
    this.baseUrl = baseUrl;
    this.client = axios.create({
      baseURL: baseUrl,
      headers: apiKey ? { Authorization: `Bearer ${apiKey}` } : {}
    });
  }

  async login(username, password) {
    try {
      const response = await this.client.post('/login', { username, password });
      const data = response.data;
      if (data.authenticated && data.access_token) {
        this.client.defaults.headers.common['Authorization'] = `Bearer ${data.access_token}`;
      }
      return data;
    } catch (error) {
      throw new Error(`Login failed: ${error.response?.data?.message || error.message}`);
    }
  }

  async getUser(userId) {
    try {
      const response = await this.client.get(`/get_user/${userId}`);
      return response.data;
    } catch (error) {
      throw new Error(`Failed to get user: ${error.response?.data?.message || error.message}`);
    }
  }

  async getCompanies() {
    try {
      const response = await this.client.get('/get_companies');
      return response.data;
    } catch (error) {
      throw new Error(`Failed to get companies: ${error.response?.data?.message || error.message}`);
    }
  }

  async addCompany(companyData) {
    try {
      const response = await this.client.post('/add_company', companyData);
      return response.data;
    } catch (error) {
      throw new Error(`Failed to add company: ${error.response?.data?.message || error.message}`);
    }
  }

  async getEmployees(companyId) {
    try {
      const response = await this.client.get('/api/employees', { params: { company_id: companyId } });
      return response.data;
    } catch (error) {
      throw new Error(`Failed to get employees: ${error.response?.data?.message || error.message}`);
    }
  }

  async createEmployee(employeeData) {
    try {
      const response = await this.client.post('/api/employees', employeeData);
      return response.data;
    } catch (error) {
      throw new Error(`Failed to create employee: ${error.response?.data?.message || error.message}`);
    }
  }
}

module.exports = AttendanceApiClient;
"""

    # Replace the placeholder with the actual base URL
    js_code = js_code.replace('BASE_URL_PLACEHOLDER', base_url)

    # Write the JavaScript code to file
    with open(os.path.join(sdk_dir, 'index.js'), 'w') as f:
        f.write(js_code)

def generate_java_sdk(sdk_dir, base_url):
    """Generate Java SDK files"""
    # Create directory structure
    os.makedirs(os.path.join(sdk_dir, 'src', 'main', 'java', 'com', 'attendance', 'api'), exist_ok=True)

    # Create pom.xml
    with open(os.path.join(sdk_dir, 'pom.xml'), 'w') as f:
        f.write("""<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.attendance</groupId>
    <artifactId>attendance-api-client</artifactId>
    <version>1.0-SNAPSHOT</version>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.9.1</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.9</version>
        </dependency>
    </dependencies>
</project>
""")

    # Create AttendanceApiClient.java
    java_code = """package com.attendance.api;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import okhttp3.*;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class AttendanceApiClient {
    private final String baseUrl;
    private final OkHttpClient client;
    private final Gson gson;
    private String accessToken;

    public AttendanceApiClient(String baseUrl, String apiKey) {
        this.baseUrl = baseUrl != null ? baseUrl : "BASE_URL_PLACEHOLDER";
        this.client = new OkHttpClient();
        this.gson = new Gson();
        this.accessToken = apiKey;
    }

    public AttendanceApiClient() {
        this("BASE_URL_PLACEHOLDER", null);
    }

    public JsonObject login(String username, String password) throws IOException {
        JsonObject requestBody = new JsonObject();
        requestBody.addProperty("username", username);
        requestBody.addProperty("password", password);

        Request request = new Request.Builder()
                .url(baseUrl + "/login")
                .post(RequestBody.create(requestBody.toString(), MediaType.parse("application/json")))
                .build();

        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body().string();
            JsonObject jsonResponse = gson.fromJson(responseBody, JsonObject.class);

            if (response.isSuccessful() && jsonResponse.get("authenticated").getAsBoolean()) {
                accessToken = jsonResponse.get("access_token").getAsString();
            }

            return jsonResponse;
        }
    }

    public JsonObject getUser(String userId) throws IOException {
        Request request = new Request.Builder()
                .url(baseUrl + "/get_user/" + userId)
                .header("Authorization", "Bearer " + accessToken)
                .build();

        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body().string();
            return gson.fromJson(responseBody, JsonObject.class);
        }
    }

    public JsonObject getCompanies() throws IOException {
        Request request = new Request.Builder()
                .url(baseUrl + "/get_companies")
                .header("Authorization", "Bearer " + accessToken)
                .build();

        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body().string();
            return gson.fromJson(responseBody, JsonObject.class);
        }
    }

    public JsonObject addCompany(JsonObject companyData) throws IOException {
        Request request = new Request.Builder()
                .url(baseUrl + "/add_company")
                .header("Authorization", "Bearer " + accessToken)
                .post(RequestBody.create(companyData.toString(), MediaType.parse("application/json")))
                .build();

        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body().string();
            return gson.fromJson(responseBody, JsonObject.class);
        }
    }

    public JsonObject getEmployees(String companyId) throws IOException {
        HttpUrl url = HttpUrl.parse(baseUrl + "/api/employees")
                .newBuilder()
                .addQueryParameter("company_id", companyId)
                .build();

        Request request = new Request.Builder()
                .url(url)
                .header("Authorization", "Bearer " + accessToken)
                .build();

        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body().string();
            return gson.fromJson(responseBody, JsonObject.class);
        }
    }

    public JsonObject createEmployee(JsonObject employeeData) throws IOException {
        Request request = new Request.Builder()
                .url(baseUrl + "/api/employees")
                .header("Authorization", "Bearer " + accessToken)
                .post(RequestBody.create(employeeData.toString(), MediaType.parse("application/json")))
                .build();

        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body().string();
            return gson.fromJson(responseBody, JsonObject.class);
        }
    }
}
"""

    # Replace the placeholder with the actual base URL
    java_code = java_code.replace('BASE_URL_PLACEHOLDER', base_url)

    # Write the Java code to file
    with open(os.path.join(sdk_dir, 'src', 'main', 'java', 'com', 'attendance', 'api', 'AttendanceApiClient.java'), 'w') as f:
        f.write(java_code)

def generate_csharp_sdk(sdk_dir, base_url):
    """Generate C# SDK files"""
    # Create directory structure
    os.makedirs(os.path.join(sdk_dir, 'AttendanceApi'), exist_ok=True)

    # Create AttendanceApi.csproj
    with open(os.path.join(sdk_dir, 'AttendanceApi', 'AttendanceApi.csproj'), 'w') as f:
        f.write("""<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
    <PackageId>AttendanceApi</PackageId>
    <Version>1.0.0</Version>
    <Authors>Your Name</Authors>
    <Company>Your Company</Company>
    <Description>A client library for the Attendance API</Description>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
  </ItemGroup>

</Project>
""")

    # Create AttendanceApiClient.cs
    csharp_code = """using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace AttendanceApi
{
    public class AttendanceApiClient
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;

        public AttendanceApiClient(string baseUrl = "BASE_URL_PLACEHOLDER", string apiKey = null)
        {
            _baseUrl = baseUrl;
            _httpClient = new HttpClient();

            if (!string.IsNullOrEmpty(apiKey))
            {
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", apiKey);
            }
        }

        public async Task<JObject> LoginAsync(string username, string password)
        {
            var content = new JObject
            {
                {"username", username},
                {"password", password}
            };

            var response = await _httpClient.PostAsync($"{_baseUrl}/login",
                new StringContent(content.ToString(), Encoding.UTF8, "application/json"));

            var responseContent = await response.Content.ReadAsStringAsync();
            var result = JObject.Parse(responseContent);

            if (response.IsSuccessStatusCode && result["authenticated"]?.Value<bool>() == true)
            {
                var token = result["access_token"]?.Value<string>();
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
            }

            return result;
        }

        public async Task<JObject> GetUserAsync(string userId)
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/get_user/{userId}");
            var content = await response.Content.ReadAsStringAsync();
            return JObject.Parse(content);
        }

        public async Task<JObject> GetCompaniesAsync()
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/get_companies");
            var content = await response.Content.ReadAsStringAsync();
            return JObject.Parse(content);
        }

        public async Task<JObject> AddCompanyAsync(JObject companyData)
        {
            var response = await _httpClient.PostAsync($"{_baseUrl}/add_company",
                new StringContent(companyData.ToString(), Encoding.UTF8, "application/json"));
            var content = await response.Content.ReadAsStringAsync();
            return JObject.Parse(content);
        }

        public async Task<JObject> GetEmployeesAsync(string companyId)
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/api/employees?company_id={companyId}");
            var content = await response.Content.ReadAsStringAsync();
            return JObject.Parse(content);
        }

        public async Task<JObject> CreateEmployeeAsync(JObject employeeData)
        {
            var response = await _httpClient.PostAsync($"{_baseUrl}/api/employees",
                new StringContent(employeeData.ToString(), Encoding.UTF8, "application/json"));
            var content = await response.Content.ReadAsStringAsync();
            return JObject.Parse(content);
        }
    }
}
"""

    # Replace the placeholder with the actual base URL
    csharp_code = csharp_code.replace('BASE_URL_PLACEHOLDER', base_url)

    # Write the C# code to file
    with open(os.path.join(sdk_dir, 'AttendanceApi', 'AttendanceApiClient.cs'), 'w') as f:
        f.write(csharp_code)

def generate_php_sdk(sdk_dir, base_url):
    """Generate PHP SDK files"""
    # Create composer.json
    with open(os.path.join(sdk_dir, 'composer.json'), 'w') as f:
        f.write("""{
    "name": "attendance/api-client",
    "description": "A client library for the Attendance API",
    "type": "library",
    "license": "MIT",
    "authors": [
        {
            "name": "Your Name",
            "email": "<EMAIL>"
        }
    ],
    "require": {
        "php": ">=7.2",
        "guzzlehttp/guzzle": "^7.0"
    },
    "autoload": {
        "psr-4": {
            "Attendance\\Api\\": "src/"
        }
    }
}
""")

    # Create directory structure
    os.makedirs(os.path.join(sdk_dir, 'src'), exist_ok=True)

    # Create Client.php
    php_code = """<?php

namespace Attendance\\Api;

use GuzzleHttp\\Client as GuzzleClient;
use GuzzleHttp\\Exception\\GuzzleException;

class Client
{
    private $client;
    private $baseUrl;
    private $accessToken;

    public function __construct(string $baseUrl = 'BASE_URL_PLACEHOLDER', string $apiKey = null)
    {
        $this->baseUrl = $baseUrl;
        $this->accessToken = $apiKey;

        $headers = [];
        if ($apiKey) {
            $headers['Authorization'] = 'Bearer ' . $apiKey;
        }

        $this->client = new GuzzleClient([
            'base_uri' => $baseUrl,
            'headers' => $headers
        ]);
    }

    public function login(string $username, string $password)
    {
        try {
            $response = $this->client->post('/login', [
                'json' => [
                    'username' => $username,
                    'password' => $password
                ]
            ]);

            $data = json_decode($response->getBody(), true);

            if (isset($data['authenticated']) && $data['authenticated'] && isset($data['access_token'])) {
                $this->accessToken = $data['access_token'];
                $this->client = new GuzzleClient([
                    'base_uri' => $this->baseUrl,
                    'headers' => [
                        'Authorization' => 'Bearer ' . $this->accessToken
                    ]
                ]);
            }

            return $data;
        } catch (GuzzleException $e) {
            return [
                'authenticated' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    public function getUser(string $userId)
    {
        try {
            $response = $this->client->get('/get_user/' . $userId);
            return json_decode($response->getBody(), true);
        } catch (GuzzleException $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    public function getCompanies()
    {
        try {
            $response = $this->client->get('/get_companies');
            return json_decode($response->getBody(), true);
        } catch (GuzzleException $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    public function addCompany(array $companyData)
    {
        try {
            $response = $this->client->post('/add_company', [
                'json' => $companyData
            ]);
            return json_decode($response->getBody(), true);
        } catch (GuzzleException $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    public function getEmployees(string $companyId)
    {
        try {
            $response = $this->client->get('/api/employees', [
                'query' => ['company_id' => $companyId]
            ]);
            return json_decode($response->getBody(), true);
        } catch (GuzzleException $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    public function createEmployee(array $employeeData)
    {
        try {
            $response = $this->client->post('/api/employees', [
                'json' => $employeeData
            ]);
            return json_decode($response->getBody(), true);
        } catch (GuzzleException $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
}
"""

    # Replace the placeholder with the actual base URL
    php_code = php_code.replace('BASE_URL_PLACEHOLDER', base_url)

    # Write the PHP code to file
    with open(os.path.join(sdk_dir, 'src', 'Client.php'), 'w') as f:
        f.write(php_code)
