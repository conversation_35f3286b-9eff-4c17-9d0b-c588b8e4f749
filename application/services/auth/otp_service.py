"""
OTP Service for KaziSync HRMS
Handles email-based OTP generation, validation, and management
"""

import logging
import secrets
import string
from typing import Optional, Dict, Any, <PERSON><PERSON>
from datetime import datetime, timedelta
from flask import current_app
import pyotp
from sqlalchemy.orm import Session

from application.config.email_config import EmailConfig
from application.utils.db_connection import DatabaseConnection

logger = logging.getLogger(__name__)


class OTPService:
    """Email-based OTP service for secure authentication"""
    
    def __init__(self):
        self.config = EmailConfig()
        
    def generate_otp_code(self, length: int = None) -> str:
        """Generate a random OTP code"""
        length = length or self.config.OTP_LENGTH
        
        # Generate random digits
        digits = string.digits
        otp_code = ''.join(secrets.choice(digits) for _ in range(length))
        
        return otp_code
    
    def create_otp_session(self, session: Session, user_id: str, email: str, 
                          purpose: str = 'login') -> Tuple[str, str]:
        """Create OTP session and return session_id and OTP code"""
        try:
            from application.Models.auth.otp_token import OTPToken
            
            # Generate OTP code and session ID
            otp_code = self.generate_otp_code()
            session_id = secrets.token_urlsafe(32)
            
            # Calculate expiry time
            expiry_time = datetime.utcnow() + timedelta(minutes=self.config.OTP_EXPIRY_MINUTES)
            
            # Create OTP token record
            otp_token = OTPToken(
                session_id=session_id,
                user_id=user_id,
                email=email,
                otp_code=otp_code,
                purpose=purpose,
                expires_at=expiry_time,
                attempts=0,
                is_used=False
            )
            
            session.add(otp_token)
            session.commit()
            
            logger.info(f"OTP session created for user {user_id}, purpose: {purpose}")
            return session_id, otp_code
            
        except Exception as e:
            session.rollback()
            logger.error(f"Error creating OTP session: {str(e)}")
            raise
    
    def validate_otp(self, session: Session, session_id: str, provided_otp: str) -> Tuple[bool, str, Dict[str, Any]]:
        """Validate OTP code and return result"""
        try:
            from application.Models.auth.otp_token import OTPToken
            
            # Find OTP token
            otp_token = session.query(OTPToken).filter_by(
                session_id=session_id,
                is_used=False
            ).first()
            
            if not otp_token:
                return False, "Invalid or expired OTP session", {}
            
            # Check if expired
            if datetime.utcnow() > otp_token.expires_at:
                otp_token.is_used = True
                session.commit()
                return False, "OTP has expired", {}
            
            # Check attempt limit
            if otp_token.attempts >= self.config.OTP_MAX_ATTEMPTS:
                otp_token.is_used = True
                session.commit()
                return False, "Too many failed attempts", {}
            
            # Increment attempts
            otp_token.attempts += 1
            
            # Validate OTP code
            if otp_token.otp_code != provided_otp:
                session.commit()
                return False, "Invalid OTP code", {}
            
            # Mark as used
            otp_token.is_used = True
            otp_token.used_at = datetime.utcnow()
            session.commit()
            
            # Return success with user info
            user_info = {
                'user_id': otp_token.user_id,
                'email': otp_token.email,
                'purpose': otp_token.purpose
            }
            
            logger.info(f"OTP validated successfully for user {otp_token.user_id}")
            return True, "OTP validated successfully", user_info
            
        except Exception as e:
            session.rollback()
            logger.error(f"Error validating OTP: {str(e)}")
            return False, "Error validating OTP", {}
    
    def check_rate_limit(self, session: Session, user_id: str = None, email: str = None) -> Tuple[bool, str]:
        """Check if user has exceeded OTP request rate limit"""
        try:
            from application.Models.auth.otp_token import OTPToken
            
            # Check rate limit window
            rate_limit_window = datetime.utcnow() - timedelta(minutes=self.config.OTP_RATE_LIMIT_MINUTES)
            
            # Build query
            query = session.query(OTPToken).filter(
                OTPToken.created_at >= rate_limit_window
            )
            
            if user_id:
                query = query.filter(OTPToken.user_id == user_id)
            elif email:
                query = query.filter(OTPToken.email == email)
            else:
                return True, "No identifier provided for rate limiting"
            
            recent_requests = query.count()
            
            # Check if exceeded limit
            max_requests = 5  # Max 5 OTP requests per hour
            if recent_requests >= max_requests:
                return False, f"Rate limit exceeded. Try again in {self.config.OTP_RATE_LIMIT_MINUTES} minutes"
            
            return True, "Rate limit OK"
            
        except Exception as e:
            logger.error(f"Error checking rate limit: {str(e)}")
            return True, "Rate limit check failed, allowing request"
    
    def cleanup_expired_tokens(self, session: Session) -> int:
        """Clean up expired OTP tokens"""
        try:
            from application.Models.auth.otp_token import OTPToken
            
            # Delete expired tokens
            expired_tokens = session.query(OTPToken).filter(
                OTPToken.expires_at < datetime.utcnow()
            )
            
            count = expired_tokens.count()
            expired_tokens.delete()
            session.commit()
            
            logger.info(f"Cleaned up {count} expired OTP tokens")
            return count
            
        except Exception as e:
            session.rollback()
            logger.error(f"Error cleaning up expired tokens: {str(e)}")
            return 0
    
    def get_otp_statistics(self, session: Session, user_id: str = None) -> Dict[str, Any]:
        """Get OTP usage statistics"""
        try:
            from application.Models.auth.otp_token import OTPToken
            
            # Base query
            query = session.query(OTPToken)
            if user_id:
                query = query.filter(OTPToken.user_id == user_id)
            
            # Get statistics
            total_requests = query.count()
            successful_validations = query.filter(OTPToken.is_used == True).count()
            failed_attempts = query.filter(
                OTPToken.attempts > 0,
                OTPToken.is_used == False
            ).count()
            
            # Recent activity (last 24 hours)
            recent_window = datetime.utcnow() - timedelta(hours=24)
            recent_requests = query.filter(OTPToken.created_at >= recent_window).count()
            
            return {
                'total_requests': total_requests,
                'successful_validations': successful_validations,
                'failed_attempts': failed_attempts,
                'recent_requests_24h': recent_requests,
                'success_rate': (successful_validations / total_requests * 100) if total_requests > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"Error getting OTP statistics: {str(e)}")
            return {}
    
    def send_otp_email(self, user_id: str, email: str, user_name: str, 
                      purpose: str = 'login', company_id: str = None) -> Tuple[bool, str, str]:
        """Generate OTP and send via email"""
        try:
            # Get database session
            database_name = 'central_db'  # OTP tokens stored in central DB
            if company_id:
                from application.Models.company import Company
                database_name = Company.get_database_given_company_id(company_id) or 'central_db'
            
            with DatabaseConnection.get_session(database_name) as session:
                # Check rate limit
                rate_limit_ok, rate_limit_msg = self.check_rate_limit(session, user_id, email)
                if not rate_limit_ok:
                    return False, rate_limit_msg, ""
                
                # Create OTP session
                session_id, otp_code = self.create_otp_session(session, user_id, email, purpose)
                
                # Send email via notification system
                from application.services.email.email_service import EmailService
                email_service = EmailService()
                
                context = {
                    'user_name': user_name,
                    'otp_code': otp_code,
                    'purpose': purpose,
                    'expiry_minutes': self.config.OTP_EXPIRY_MINUTES,
                    'subject': f'Your KaziSync Access Code - {otp_code}'
                }
                
                success = email_service.send_notification_email(
                    notification_type='auth',
                    notification_subtype='otp_code',
                    recipients=[email],
                    context=context,
                    company_id=company_id
                )
                
                if success:
                    return True, "OTP sent successfully", session_id
                else:
                    return False, "Failed to send OTP email", ""
                    
        except Exception as e:
            logger.error(f"Error sending OTP email: {str(e)}")
            return False, "Error sending OTP email", ""
