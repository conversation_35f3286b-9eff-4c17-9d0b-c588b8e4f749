from datetime import datetime, date, timedelta
from decimal import Decimal
from flask import current_app
from application.services.paypal.paypal_service import PayPalService
from application.Models.company_subscription import CompanySubscription
from application.Models.subscription_invoice import SubscriptionInvoice
from application.Models.subscription_payment import SubscriptionPayment
from application.Models.paypal_billing_agreement import PayPalBillingAgreement
from application.utils.db_connection import DatabaseConnection

class SubscriptionBillingService:
    """Service for automated subscription billing."""
    
    def __init__(self):
        self.paypal_service = PayPalService()
        self.db_connection = DatabaseConnection()
    
    def get_active_employee_count(self, company_id):
        """Get current active employee count for a company."""
        try:
            from application.Models.company import Company
            from application.Models.employees.employee import Employee
            
            # Get company database name
            database_name = Company.get_database_given_company_id(company_id)
            if not database_name:
                current_app.logger.error(f"No database found for company {company_id}")
                return 0
            
            # Connect to company database and count active employees
            with self.db_connection.get_session(database_name) as session:
                employee_count = session.query(Employee).filter(
                    Employee.status == 'ACTIVE'
                ).count()
                
                current_app.logger.debug(f"Company {company_id} has {employee_count} active employees")
                return employee_count
                
        except Exception as e:
            current_app.logger.error(f"Error getting employee count for company {company_id}: {e}")
            return 0
    
    def calculate_monthly_charge(self, subscription):
        """Calculate monthly charge based on current employee count."""
        try:
            if not subscription.plan:
                return Decimal('0.00')
            
            # Get current employee count
            current_employees = self.get_active_employee_count(subscription.company_id)
            
            # Update subscription with current count
            subscription.employee_count = current_employees
            
            # Calculate amount based on plan
            amount = subscription.plan.calculate_price(current_employees)
            
            current_app.logger.info(
                f"Calculated charge for subscription {subscription.subscription_id}: "
                f"{current_employees} employees × ${subscription.plan.price_per_employee} = ${amount}"
            )
            
            return Decimal(str(amount))
            
        except Exception as e:
            current_app.logger.error(f"Error calculating monthly charge: {e}")
            return Decimal('0.00')
    
    def create_subscription_invoice(self, subscription, amount, description=None):
        """Create an invoice for subscription billing."""
        try:
            if not description:
                description = f"{subscription.plan.name} Plan - {subscription.employee_count} employees"
            
            invoice, error = SubscriptionInvoice.create_invoice(
                subscription_id=subscription.subscription_id,
                amount=amount,
                description=description,
                due_date=date.today() + timedelta(days=7)  # 7 days to pay
            )
            
            if error:
                current_app.logger.error(f"Error creating invoice: {error}")
                return None, error
            
            current_app.logger.info(f"Created invoice {invoice.invoice_number} for ${amount}")
            return invoice, None
            
        except Exception as e:
            current_app.logger.error(f"Error creating subscription invoice: {e}")
            return None, str(e)
    
    def process_paypal_payment(self, subscription, amount, invoice):
        """Process payment via PayPal billing agreement."""
        try:
            if not subscription.paypal_agreement_id:
                return None, "No PayPal billing agreement found"
            
            # Charge the billing agreement
            description = f"Invoice {invoice.invoice_number} - {subscription.plan.name}"
            payment_response, error = self.paypal_service.charge_billing_agreement(
                agreement_id=subscription.paypal_agreement_id,
                amount=amount,
                description=description,
                invoice_number=invoice.invoice_number
            )
            
            if error:
                current_app.logger.error(f"PayPal payment failed: {error}")
                return None, error
            
            # Create payment record
            payment, payment_error = SubscriptionPayment.create_payment(
                invoice_id=invoice.invoice_id,
                amount=amount,
                payment_method='PAYPAL',
                payment_reference=payment_response.get('id'),
                transaction_id=payment_response.get('id'),
                processor_response=str(payment_response)
            )
            
            if payment_error:
                current_app.logger.error(f"Error creating payment record: {payment_error}")
                return None, payment_error
            
            current_app.logger.info(f"Successfully processed PayPal payment of ${amount}")
            return payment, None
            
        except Exception as e:
            current_app.logger.error(f"Error processing PayPal payment: {e}")
            return None, str(e)
    
    def process_subscription_billing(self, subscription):
        """Process billing for a single subscription."""
        try:
            current_app.logger.info(f"Processing billing for subscription {subscription.subscription_id}")
            
            # Calculate amount to charge
            amount = self.calculate_monthly_charge(subscription)
            
            if amount <= 0:
                current_app.logger.info(f"No charge needed for subscription {subscription.subscription_id}")
                subscription.record_billing_attempt(success=True, amount=0)
                return True, "No charge needed"
            
            # Create invoice
            invoice, invoice_error = self.create_subscription_invoice(subscription, amount)
            if invoice_error:
                subscription.record_billing_attempt(success=False, error_message=invoice_error)
                return False, invoice_error
            
            # Process payment
            payment, payment_error = self.process_paypal_payment(subscription, amount, invoice)
            if payment_error:
                subscription.record_billing_attempt(success=False, error_message=payment_error)
                return False, payment_error
            
            # Record successful billing
            subscription.record_billing_attempt(success=True, amount=amount)
            
            # Activate subscription if it was in trial
            if subscription.status == 'TRIAL':
                subscription.activate_subscription()
            
            current_app.logger.info(f"Successfully billed subscription {subscription.subscription_id} for ${amount}")
            return True, None
            
        except Exception as e:
            error_msg = f"Error processing subscription billing: {e}"
            current_app.logger.error(error_msg)
            subscription.record_billing_attempt(success=False, error_message=str(e))
            return False, error_msg
    
    def process_all_due_subscriptions(self):
        """Process billing for all subscriptions that are due."""
        try:
            # Get subscriptions that need billing
            due_subscriptions = CompanySubscription.get_subscriptions_for_billing()
            
            current_app.logger.info(f"Found {len(due_subscriptions)} subscriptions due for billing")
            
            results = {
                'total': len(due_subscriptions),
                'successful': 0,
                'failed': 0,
                'errors': []
            }
            
            for subscription in due_subscriptions:
                success, error = self.process_subscription_billing(subscription)
                
                if success:
                    results['successful'] += 1
                else:
                    results['failed'] += 1
                    results['errors'].append({
                        'subscription_id': str(subscription.subscription_id),
                        'company_id': subscription.company_id,
                        'error': error
                    })
            
            current_app.logger.info(
                f"Billing completed: {results['successful']} successful, "
                f"{results['failed']} failed out of {results['total']} total"
            )
            
            return results
            
        except Exception as e:
            current_app.logger.error(f"Error processing all due subscriptions: {e}")
            return {
                'total': 0,
                'successful': 0,
                'failed': 0,
                'errors': [{'error': str(e)}]
            }
    
    def process_employee_count_changes(self):
        """Process billing for subscriptions where employee count has changed."""
        try:
            # Get subscriptions with employee count changes
            changed_subscriptions = CompanySubscription.get_subscriptions_with_employee_changes()
            
            current_app.logger.info(f"Found {len(changed_subscriptions)} subscriptions with employee count changes")
            
            results = {
                'total': len(changed_subscriptions),
                'processed': 0,
                'errors': []
            }
            
            for subscription in changed_subscriptions:
                try:
                    # Update employee count and amount due
                    current_employees = self.get_active_employee_count(subscription.company_id)
                    success, error = subscription.update_employee_count(current_employees)
                    
                    if success:
                        results['processed'] += 1
                        current_app.logger.info(
                            f"Updated employee count for subscription {subscription.subscription_id}: "
                            f"{subscription.last_billed_employee_count} → {current_employees}"
                        )
                    else:
                        results['errors'].append({
                            'subscription_id': str(subscription.subscription_id),
                            'error': error
                        })
                        
                except Exception as e:
                    results['errors'].append({
                        'subscription_id': str(subscription.subscription_id),
                        'error': str(e)
                    })
            
            return results
            
        except Exception as e:
            current_app.logger.error(f"Error processing employee count changes: {e}")
            return {
                'total': 0,
                'processed': 0,
                'errors': [{'error': str(e)}]
            }
    
    def setup_paypal_billing_for_subscription(self, subscription, return_url=None, cancel_url=None):
        """Set up PayPal billing agreement for a subscription."""
        try:
            # Create billing agreement with PayPal
            agreement_response, error = self.paypal_service.create_billing_agreement(
                company_id=subscription.company_id,
                plan_name=subscription.plan.name,
                description=f"{subscription.plan.name} subscription billing",
                return_url=return_url,
                cancel_url=cancel_url
            )
            
            if error:
                return None, error
            
            # Extract approval URL for user to approve the agreement
            approval_url = None
            for link in agreement_response.get('links', []):
                if link.get('rel') == 'approval_url':
                    approval_url = link.get('href')
                    break
            
            if not approval_url:
                return None, "No approval URL found in PayPal response"
            
            current_app.logger.info(f"Created PayPal billing agreement for subscription {subscription.subscription_id}")
            
            return {
                'agreement_id': agreement_response.get('id'),
                'approval_url': approval_url,
                'agreement_data': agreement_response
            }, None
            
        except Exception as e:
            current_app.logger.error(f"Error setting up PayPal billing: {e}")
            return None, str(e)
