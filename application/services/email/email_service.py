"""
Core Email Service for KaziSync HRMS
Handles email sending, template rendering, and delivery tracking
"""

import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from flask import current_app, render_template
from flask_mail import Mail, Message
from email_validator import validate_email, EmailNotValidError
import bleach
from premailer import transform

from application.config.email_config import EmailConfig, NotificationConfig
from .template_manager import TemplateManager

logger = logging.getLogger(__name__)


class EmailService:
    """Core email service for sending emails with templates and tracking"""
    
    def __init__(self, mail_instance: Mail = None):
        self.mail = mail_instance
        self.template_manager = TemplateManager()
        self.config = EmailConfig()
        
    def initialize_app(self, app, mail_instance: Mail):
        """Initialize email service with Flask app and Mail instance"""
        self.mail = mail_instance
        
        # Configure Flask-Mail
        mail_config = self.config.get_mail_config()
        for key, value in mail_config.items():
            app.config[key] = value
            
        logger.info("Email service initialized successfully")
    
    def validate_email_address(self, email: str) -> bool:
        """Validate email address format"""
        try:
            validate_email(email)
            return True
        except EmailNotValidError:
            return False
    
    def sanitize_content(self, content: str) -> str:
        """Sanitize email content to prevent XSS"""
        allowed_tags = [
            'p', 'br', 'strong', 'b', 'em', 'i', 'u', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
            'ul', 'ol', 'li', 'a', 'img', 'div', 'span', 'table', 'tr', 'td', 'th', 'thead', 'tbody'
        ]
        allowed_attributes = {
            'a': ['href', 'title'],
            'img': ['src', 'alt', 'width', 'height'],
            'div': ['style', 'class'],
            'span': ['style', 'class'],
            'p': ['style', 'class'],
            'table': ['style', 'class', 'width', 'cellpadding', 'cellspacing'],
            'tr': ['style', 'class'],
            'td': ['style', 'class', 'colspan', 'rowspan'],
            'th': ['style', 'class', 'colspan', 'rowspan'],
        }
        
        return bleach.clean(content, tags=allowed_tags, attributes=allowed_attributes)
    
    def render_email_template(self, template_path: str, context: Dict[str, Any], 
                            company_id: str = None) -> tuple[str, str]:
        """Render email template with context data"""
        try:
            # Get company-specific template if available
            html_content = self.template_manager.render_template(
                template_path, context, company_id
            )
            
            # Inline CSS for better email client compatibility
            html_content = transform(html_content)
            
            # Generate plain text version
            text_content = self.template_manager.html_to_text(html_content)
            
            return html_content, text_content
            
        except Exception as e:
            logger.error(f"Error rendering email template {template_path}: {str(e)}")
            raise
    
    def create_message(self, 
                      recipients: Union[str, List[str]], 
                      subject: str,
                      html_body: str = None,
                      text_body: str = None,
                      sender: str = None,
                      cc: List[str] = None,
                      bcc: List[str] = None,
                      attachments: List[Dict] = None) -> Message:
        """Create email message object"""
        
        # Ensure recipients is a list
        if isinstance(recipients, str):
            recipients = [recipients]
        
        # Validate all email addresses
        valid_recipients = []
        for email in recipients:
            if self.validate_email_address(email):
                valid_recipients.append(email)
            else:
                logger.warning(f"Invalid email address: {email}")
        
        if not valid_recipients:
            raise ValueError("No valid recipients provided")
        
        # Create message
        msg = Message(
            subject=subject,
            recipients=valid_recipients,
            sender=sender or self.config.DEFAULT_MAIL_DEFAULT_SENDER,
            cc=cc or [],
            bcc=bcc or []
        )
        
        # Set body content
        if html_body:
            msg.html = self.sanitize_content(html_body)
        if text_body:
            msg.body = text_body
        
        # Add attachments if provided
        if attachments:
            for attachment in attachments:
                msg.attach(
                    filename=attachment.get('filename'),
                    content_type=attachment.get('content_type'),
                    data=attachment.get('data')
                )
        
        return msg
    
    def send_email_sync(self, message: Message) -> bool:
        """Send email synchronously"""
        try:
            if not self.mail:
                logger.error("Mail instance not initialized")
                return False
            
            if self.config.MAIL_SUPPRESS_SEND:
                logger.info(f"Email sending suppressed: {message.subject}")
                return True
            
            self.mail.send(message)
            logger.info(f"Email sent successfully: {message.subject} to {message.recipients}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending email: {str(e)}")
            return False
    
    def send_templated_email(self,
                           recipients: Union[str, List[str]],
                           subject: str,
                           template_path: str,
                           context: Dict[str, Any],
                           company_id: str = None,
                           sender: str = None,
                           priority: str = 'normal',
                           send_async: bool = True) -> bool:
        """Send email using template with context data"""
        
        try:
            # Render template
            html_content, text_content = self.render_email_template(
                template_path, context, company_id
            )
            
            # Create message
            message = self.create_message(
                recipients=recipients,
                subject=subject,
                html_body=html_content,
                text_body=text_content,
                sender=sender
            )
            
            if send_async:
                # Send via Celery task
                from application.tasks.email_tasks import send_email_async
                task = send_email_async.apply_async(
                    args=[message.as_string()],
                    queue=f'email_{priority}'
                )
                logger.info(f"Email queued for async sending: {task.id}")
                return True
            else:
                # Send synchronously
                return self.send_email_sync(message)
                
        except Exception as e:
            logger.error(f"Error sending templated email: {str(e)}")
            return False
    
    def send_notification_email(self,
                              notification_type: str,
                              notification_subtype: str,
                              recipients: Union[str, List[str]],
                              context: Dict[str, Any],
                              company_id: str = None) -> bool:
        """Send notification email based on type and subtype"""
        
        try:
            # Get notification configuration
            template_path = NotificationConfig.get_template_path(
                notification_type, notification_subtype
            )
            priority = NotificationConfig.get_priority(
                notification_type, notification_subtype
            )
            
            # Generate subject from context or use default
            subject = context.get('subject', f'KaziSync HRMS - {notification_subtype.replace("_", " ").title()}')
            
            return self.send_templated_email(
                recipients=recipients,
                subject=subject,
                template_path=template_path,
                context=context,
                company_id=company_id,
                priority=priority,
                send_async=True
            )
            
        except Exception as e:
            logger.error(f"Error sending notification email: {str(e)}")
            return False
    
    def get_email_status(self) -> Dict[str, Any]:
        """Get email service status and statistics"""
        return {
            'service_enabled': self.config.is_email_enabled(),
            'mail_server': self.config.DEFAULT_MAIL_SERVER,
            'mail_port': self.config.DEFAULT_MAIL_PORT,
            'default_sender': self.config.DEFAULT_MAIL_DEFAULT_SENDER,
            'suppress_send': self.config.MAIL_SUPPRESS_SEND,
            'template_folder': self.config.EMAIL_TEMPLATE_FOLDER,
        }
