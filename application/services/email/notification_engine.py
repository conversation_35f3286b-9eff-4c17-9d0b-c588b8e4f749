"""
Notification Engine for KaziSync HRMS
Handles event-driven email notifications across all modules
"""

import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from flask import current_app

from application.config.email_config import NotificationConfig
from .email_service import EmailService

logger = logging.getLogger(__name__)


class NotificationEngine:
    """Event-driven notification engine for HRMS modules"""
    
    def __init__(self, email_service: EmailService = None):
        self.email_service = email_service or EmailService()
        self.config = NotificationConfig()
        
    def send_leave_notification(self, notification_type: str, context: Dict[str, Any]) -> bool:
        """Send leave management notifications"""
        try:
            notification_map = {
                'request_submitted': {
                    'recipients': context.get('approver_emails', []),
                    'subject': f"Leave Request Submitted - {context.get('employee_name', 'Employee')}",
                    'context': {
                        **context,
                        'notification_title': 'New Leave Request',
                        'action_required': True,
                        'action_url': context.get('approval_url', ''),
                        'action_text': 'Review Request'
                    }
                },
                'request_approved': {
                    'recipients': [context.get('employee_email')],
                    'subject': f"Leave Request Approved - {context.get('leave_type', 'Leave')}",
                    'context': {
                        **context,
                        'notification_title': 'Leave Request Approved',
                        'status': 'approved',
                        'status_color': '#10b981'
                    }
                },
                'request_rejected': {
                    'recipients': [context.get('employee_email')],
                    'subject': f"Leave Request Rejected - {context.get('leave_type', 'Leave')}",
                    'context': {
                        **context,
                        'notification_title': 'Leave Request Rejected',
                        'status': 'rejected',
                        'status_color': '#ef4444'
                    }
                },
                'balance_updated': {
                    'recipients': [context.get('employee_email')],
                    'subject': f"Leave Balance Updated - {context.get('leave_type', 'Leave')}",
                    'context': {
                        **context,
                        'notification_title': 'Leave Balance Updated'
                    }
                },
                'approval_reminder': {
                    'recipients': context.get('approver_emails', []),
                    'subject': f"Reminder: Pending Leave Request - {context.get('employee_name', 'Employee')}",
                    'context': {
                        **context,
                        'notification_title': 'Pending Leave Request Reminder',
                        'is_reminder': True,
                        'action_required': True
                    }
                }
            }
            
            notification_data = notification_map.get(notification_type)
            if not notification_data:
                logger.warning(f"Unknown leave notification type: {notification_type}")
                return False
            
            return self.email_service.send_notification_email(
                notification_type='leave',
                notification_subtype=notification_type,
                recipients=notification_data['recipients'],
                context={
                    'subject': notification_data['subject'],
                    **notification_data['context']
                },
                company_id=context.get('company_id')
            )
            
        except Exception as e:
            logger.error(f"Error sending leave notification: {str(e)}")
            return False
    
    def send_payroll_notification(self, notification_type: str, context: Dict[str, Any]) -> bool:
        """Send payroll notifications"""
        try:
            notification_map = {
                'payslip_ready': {
                    'recipients': [context.get('employee_email')],
                    'subject': f"Your Payslip is Ready - {context.get('pay_period', 'Current Period')}",
                    'context': {
                        **context,
                        'notification_title': 'Payslip Available',
                        'action_url': context.get('payslip_url', ''),
                        'action_text': 'View Payslip'
                    }
                },
                'salary_updated': {
                    'recipients': [context.get('employee_email')],
                    'subject': f"Salary Information Updated - {context.get('employee_name', 'Employee')}",
                    'context': {
                        **context,
                        'notification_title': 'Salary Updated',
                        'effective_date': context.get('effective_date', datetime.now().strftime('%Y-%m-%d'))
                    }
                },
                'payroll_processed': {
                    'recipients': context.get('hr_emails', []),
                    'subject': f"Payroll Processing Complete - {context.get('pay_period', 'Current Period')}",
                    'context': {
                        **context,
                        'notification_title': 'Payroll Processing Complete',
                        'total_employees': context.get('total_employees', 0),
                        'total_amount': context.get('total_amount', 0)
                    }
                }
            }
            
            notification_data = notification_map.get(notification_type)
            if not notification_data:
                logger.warning(f"Unknown payroll notification type: {notification_type}")
                return False
            
            return self.email_service.send_notification_email(
                notification_type='payroll',
                notification_subtype=notification_type,
                recipients=notification_data['recipients'],
                context={
                    'subject': notification_data['subject'],
                    **notification_data['context']
                },
                company_id=context.get('company_id')
            )
            
        except Exception as e:
            logger.error(f"Error sending payroll notification: {str(e)}")
            return False
    
    def send_recruitment_notification(self, notification_type: str, context: Dict[str, Any]) -> bool:
        """Send recruitment notifications"""
        try:
            notification_map = {
                'application_received': {
                    'recipients': [context.get('candidate_email')],
                    'subject': f"Application Received - {context.get('job_title', 'Position')}",
                    'context': {
                        **context,
                        'notification_title': 'Application Received',
                        'next_steps': context.get('next_steps', 'We will review your application and contact you soon.')
                    }
                },
                'interview_scheduled': {
                    'recipients': [context.get('candidate_email')],
                    'subject': f"Interview Scheduled - {context.get('job_title', 'Position')}",
                    'context': {
                        **context,
                        'notification_title': 'Interview Scheduled',
                        'interview_date': context.get('interview_date'),
                        'interview_time': context.get('interview_time'),
                        'interview_location': context.get('interview_location'),
                        'interviewer_name': context.get('interviewer_name'),
                        'action_required': True
                    }
                },
                'offer_letter': {
                    'recipients': [context.get('candidate_email')],
                    'subject': f"Job Offer - {context.get('job_title', 'Position')}",
                    'context': {
                        **context,
                        'notification_title': 'Job Offer',
                        'offer_details': context.get('offer_details', {}),
                        'response_deadline': context.get('response_deadline'),
                        'action_required': True
                    }
                },
                'application_rejected': {
                    'recipients': [context.get('candidate_email')],
                    'subject': f"Application Update - {context.get('job_title', 'Position')}",
                    'context': {
                        **context,
                        'notification_title': 'Application Update',
                        'feedback': context.get('feedback', 'Thank you for your interest in our company.')
                    }
                }
            }
            
            notification_data = notification_map.get(notification_type)
            if not notification_data:
                logger.warning(f"Unknown recruitment notification type: {notification_type}")
                return False
            
            return self.email_service.send_notification_email(
                notification_type='recruitment',
                notification_subtype=notification_type,
                recipients=notification_data['recipients'],
                context={
                    'subject': notification_data['subject'],
                    **notification_data['context']
                },
                company_id=context.get('company_id')
            )
            
        except Exception as e:
            logger.error(f"Error sending recruitment notification: {str(e)}")
            return False
    
    def send_onboarding_notification(self, notification_type: str, context: Dict[str, Any]) -> bool:
        """Send onboarding notifications"""
        try:
            notification_map = {
                'welcome_email': {
                    'recipients': [context.get('employee_email')],
                    'subject': f"Welcome to {context.get('company_name', 'KaziSync')}!",
                    'context': {
                        **context,
                        'notification_title': 'Welcome to the Team!',
                        'start_date': context.get('start_date'),
                        'first_day_instructions': context.get('first_day_instructions', ''),
                        'hr_contact': context.get('hr_contact', {})
                    }
                },
                'task_assigned': {
                    'recipients': [context.get('employee_email')],
                    'subject': f"Onboarding Task Assigned - {context.get('task_title', 'Task')}",
                    'context': {
                        **context,
                        'notification_title': 'New Onboarding Task',
                        'task_description': context.get('task_description', ''),
                        'due_date': context.get('due_date'),
                        'action_required': True
                    }
                },
                'document_required': {
                    'recipients': [context.get('employee_email')],
                    'subject': f"Document Required - {context.get('document_name', 'Document')}",
                    'context': {
                        **context,
                        'notification_title': 'Document Required',
                        'document_list': context.get('document_list', []),
                        'submission_deadline': context.get('submission_deadline'),
                        'action_required': True
                    }
                }
            }
            
            notification_data = notification_map.get(notification_type)
            if not notification_data:
                logger.warning(f"Unknown onboarding notification type: {notification_type}")
                return False
            
            return self.email_service.send_notification_email(
                notification_type='onboarding',
                notification_subtype=notification_type,
                recipients=notification_data['recipients'],
                context={
                    'subject': notification_data['subject'],
                    **notification_data['context']
                },
                company_id=context.get('company_id')
            )
            
        except Exception as e:
            logger.error(f"Error sending onboarding notification: {str(e)}")
            return False
    
    def send_system_notification(self, notification_type: str, context: Dict[str, Any]) -> bool:
        """Send system notifications"""
        try:
            notification_map = {
                'announcement': {
                    'recipients': context.get('recipient_emails', []),
                    'subject': context.get('announcement_title', 'Company Announcement'),
                    'context': {
                        **context,
                        'notification_title': 'Company Announcement',
                        'announcement_content': context.get('announcement_content', ''),
                        'announcement_date': context.get('announcement_date', datetime.now().strftime('%Y-%m-%d'))
                    }
                },
                'maintenance_notice': {
                    'recipients': context.get('recipient_emails', []),
                    'subject': 'System Maintenance Notice',
                    'context': {
                        **context,
                        'notification_title': 'System Maintenance Notice',
                        'maintenance_start': context.get('maintenance_start'),
                        'maintenance_end': context.get('maintenance_end'),
                        'affected_services': context.get('affected_services', [])
                    }
                },
                'policy_update': {
                    'recipients': context.get('recipient_emails', []),
                    'subject': f"Policy Update - {context.get('policy_name', 'Company Policy')}",
                    'context': {
                        **context,
                        'notification_title': 'Policy Update',
                        'policy_changes': context.get('policy_changes', ''),
                        'effective_date': context.get('effective_date')
                    }
                }
            }
            
            notification_data = notification_map.get(notification_type)
            if not notification_data:
                logger.warning(f"Unknown system notification type: {notification_type}")
                return False
            
            return self.email_service.send_notification_email(
                notification_type='system',
                notification_subtype=notification_type,
                recipients=notification_data['recipients'],
                context={
                    'subject': notification_data['subject'],
                    **notification_data['context']
                },
                company_id=context.get('company_id')
            )
            
        except Exception as e:
            logger.error(f"Error sending system notification: {str(e)}")
            return False
