"""
Email Template Manager for KaziSync HRMS
Handles template rendering, customization, and multi-tenant support
"""

import os
import logging
from typing import Dict, Any, Optional
from datetime import datetime
from flask import render_template_string, current_app
from jinja2 import Environment, FileSystemLoader, TemplateNotFound
import re
from html import unescape

logger = logging.getLogger(__name__)


class TemplateManager:
    """Manages email templates with multi-tenant support and customization"""
    
    def __init__(self):
        self.template_cache = {}
        self.company_templates = {}
        
    def get_template_path(self, template_name: str, company_id: str = None) -> str:
        """Get the full path to a template file"""
        base_path = os.path.join(os.getcwd(), 'templates', 'emails')

        # Check for company-specific template first
        if company_id:
            company_template_path = os.path.join(base_path, 'companies', company_id, template_name)
            if os.path.exists(company_template_path):
                return company_template_path

        # Fall back to default template
        default_template_path = os.path.join(base_path, template_name)
        return default_template_path
    
    def load_template_content(self, template_path: str) -> str:
        """Load template content from file"""
        try:
            if template_path in self.template_cache:
                return self.template_cache[template_path]
            
            with open(template_path, 'r', encoding='utf-8') as file:
                content = file.read()
                self.template_cache[template_path] = content
                return content
                
        except FileNotFoundError:
            logger.error(f"Template file not found: {template_path}")
            return self.get_fallback_template()
        except Exception as e:
            logger.error(f"Error loading template {template_path}: {str(e)}")
            return self.get_fallback_template()
    
    def get_fallback_template(self) -> str:
        """Get a basic fallback template when the requested template is not found"""
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{{ subject or 'KaziSync HRMS Notification' }}</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="background-color: #1e40af; color: white; padding: 20px; text-align: center;">
                    <h1 style="margin: 0;">KaziSync HRMS</h1>
                </div>
                <div style="padding: 30px; background-color: #f9f9f9;">
                    <h2>{{ subject or 'Notification' }}</h2>
                    <p>{{ message or 'This is a notification from KaziSync HRMS.' }}</p>
                    {% if content %}
                        {{ content | safe }}
                    {% endif %}
                </div>
                <div style="padding: 20px; text-align: center; color: #666; font-size: 12px;">
                    <p>This email was sent from KaziSync HRMS</p>
                    <p>© {{ current_year }} KaziSync. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
        """
    
    def get_base_context(self, company_id: str = None) -> Dict[str, Any]:
        """Get base context variables available to all templates"""
        context = {
            'current_year': datetime.now().year,
            'current_date': datetime.now().strftime('%Y-%m-%d'),
            'current_datetime': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'app_name': 'KaziSync HRMS',
            'support_email': '<EMAIL>',
            'website_url': 'https://kazisync.com',
        }
        
        # Add company-specific context if available
        if company_id:
            company_context = self.get_company_context(company_id)
            context.update(company_context)
        
        return context
    
    def get_company_context(self, company_id: str) -> Dict[str, Any]:
        """Get company-specific context variables"""
        # This would typically fetch from database
        # For now, return default values
        return {
            'company_name': 'Your Company',
            'company_logo_url': '',
            'company_primary_color': '#1e40af',
            'company_secondary_color': '#64748b',
            'company_address': '',
            'company_phone': '',
            'company_email': '<EMAIL>',
        }
    
    def render_template(self, template_name: str, context: Dict[str, Any], 
                       company_id: str = None) -> str:
        """Render email template with context data"""
        try:
            # Get template path
            template_path = self.get_template_path(template_name, company_id)
            
            # Load template content
            template_content = self.load_template_content(template_path)
            
            # Prepare context
            full_context = self.get_base_context(company_id)
            full_context.update(context)
            
            # Render template
            rendered_content = render_template_string(template_content, **full_context)
            
            return rendered_content
            
        except Exception as e:
            logger.error(f"Error rendering template {template_name}: {str(e)}")
            # Return fallback template with error context
            fallback_context = self.get_base_context(company_id)
            fallback_context.update({
                'subject': 'KaziSync HRMS Notification',
                'message': 'There was an issue rendering the email template.',
                'content': context.get('message', '')
            })
            return render_template_string(self.get_fallback_template(), **fallback_context)
    
    def html_to_text(self, html_content: str) -> str:
        """Convert HTML email content to plain text"""
        try:
            # Remove HTML tags
            text = re.sub(r'<[^>]+>', '', html_content)
            
            # Decode HTML entities
            text = unescape(text)
            
            # Clean up whitespace
            text = re.sub(r'\n\s*\n', '\n\n', text)
            text = re.sub(r' +', ' ', text)
            text = text.strip()
            
            return text
            
        except Exception as e:
            logger.error(f"Error converting HTML to text: {str(e)}")
            return "This email contains HTML content. Please view in an HTML-capable email client."
    
    def validate_template(self, template_content: str) -> tuple[bool, list]:
        """Validate template syntax and required variables"""
        errors = []
        
        try:
            # Try to render with minimal context
            test_context = self.get_base_context()
            render_template_string(template_content, **test_context)
            
        except Exception as e:
            errors.append(f"Template syntax error: {str(e)}")
        
        # Check for required elements
        required_elements = ['<html', '<body', '</html>', '</body>']
        for element in required_elements:
            if element not in template_content.lower():
                errors.append(f"Missing required HTML element: {element}")
        
        return len(errors) == 0, errors
    
    def get_available_templates(self, company_id: str = None) -> Dict[str, Any]:
        """Get list of available email templates"""
        templates = {}
        base_path = current_app.config.get('EMAIL_TEMPLATE_FOLDER', 'templates/emails')
        
        try:
            # Scan default templates
            for root, dirs, files in os.walk(base_path):
                for file in files:
                    if file.endswith('.html'):
                        rel_path = os.path.relpath(os.path.join(root, file), base_path)
                        template_name = rel_path.replace('\\', '/')  # Normalize path separators
                        templates[template_name] = {
                            'name': template_name,
                            'path': os.path.join(root, file),
                            'type': 'default',
                            'size': os.path.getsize(os.path.join(root, file)),
                            'modified': datetime.fromtimestamp(
                                os.path.getmtime(os.path.join(root, file))
                            ).isoformat()
                        }
            
            # Scan company-specific templates if company_id provided
            if company_id:
                company_path = os.path.join(base_path, 'companies', company_id)
                if os.path.exists(company_path):
                    for root, dirs, files in os.walk(company_path):
                        for file in files:
                            if file.endswith('.html'):
                                rel_path = os.path.relpath(os.path.join(root, file), company_path)
                                template_name = rel_path.replace('\\', '/')
                                templates[template_name] = {
                                    'name': template_name,
                                    'path': os.path.join(root, file),
                                    'type': 'company',
                                    'company_id': company_id,
                                    'size': os.path.getsize(os.path.join(root, file)),
                                    'modified': datetime.fromtimestamp(
                                        os.path.getmtime(os.path.join(root, file))
                                    ).isoformat()
                                }
            
        except Exception as e:
            logger.error(f"Error scanning templates: {str(e)}")
        
        return templates
    
    def clear_template_cache(self):
        """Clear the template cache"""
        self.template_cache.clear()
        logger.info("Template cache cleared")
