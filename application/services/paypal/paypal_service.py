import requests
import json
import base64
from datetime import datetime, timedelta
from decimal import Decimal
from flask import current_app
from application.config.paypal_config import PayPalConfig

class PayPalService:
    """Service for PayPal API operations."""
    
    def __init__(self):
        self.config = PayPalConfig.get_paypal_config()
        self.base_url = self.config['PAYPAL_BASE_URL']
        self.client_id = self.config['PAYPAL_CLIENT_ID']
        self.client_secret = self.config['PAYPAL_CLIENT_SECRET']
        self.access_token = None
        self.token_expires_at = None
    
    def get_access_token(self):
        """Get or refresh PayPal access token."""
        try:
            # Check if current token is still valid
            if (self.access_token and self.token_expires_at and 
                datetime.now() < self.token_expires_at - timedelta(minutes=5)):
                return self.access_token
            
            # Get new access token
            url = f"{self.base_url}/v1/oauth2/token"
            
            # Encode credentials
            credentials = f"{self.client_id}:{self.client_secret}"
            encoded_credentials = base64.b64encode(credentials.encode()).decode()
            
            headers = {
                'Accept': 'application/json',
                'Accept-Language': 'en_US',
                'Authorization': f'Basic {encoded_credentials}',
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            data = 'grant_type=client_credentials'
            
            response = requests.post(url, headers=headers, data=data, timeout=self.config['PAYPAL_TIMEOUT'])
            response.raise_for_status()
            
            token_data = response.json()
            self.access_token = token_data['access_token']
            expires_in = token_data.get('expires_in', 3600)
            self.token_expires_at = datetime.now() + timedelta(seconds=expires_in)
            
            current_app.logger.info("Successfully obtained PayPal access token")
            return self.access_token
            
        except Exception as e:
            current_app.logger.error(f"Error getting PayPal access token: {e}")
            raise Exception(f"Failed to authenticate with PayPal: {str(e)}")
    
    def make_request(self, method, endpoint, data=None, params=None):
        """Make authenticated request to PayPal API."""
        try:
            token = self.get_access_token()
            url = f"{self.base_url}{endpoint}"
            
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {token}',
                'Accept': 'application/json',
                'PayPal-Request-Id': str(datetime.now().timestamp())  # Idempotency
            }
            
            kwargs = {
                'headers': headers,
                'timeout': self.config['PAYPAL_TIMEOUT']
            }
            
            if data:
                kwargs['data'] = json.dumps(data)
            if params:
                kwargs['params'] = params
            
            response = requests.request(method, url, **kwargs)
            
            # Log request for debugging (without sensitive data)
            current_app.logger.debug(f"PayPal API {method} {endpoint}: {response.status_code}")
            
            if response.status_code >= 400:
                error_data = response.json() if response.content else {}
                current_app.logger.error(f"PayPal API error: {response.status_code} - {error_data}")
                raise Exception(f"PayPal API error: {response.status_code} - {error_data}")
            
            return response.json() if response.content else {}
            
        except requests.exceptions.RequestException as e:
            current_app.logger.error(f"PayPal API request failed: {e}")
            raise Exception(f"PayPal API request failed: {str(e)}")
    
    def create_billing_agreement(self, company_id, plan_name, description, return_url=None, cancel_url=None):
        """Create a billing agreement for recurring payments."""
        try:
            # Prepare billing agreement data
            agreement_data = {
                "name": f"{plan_name} Subscription",
                "description": description,
                "start_date": (datetime.now() + timedelta(minutes=1)).isoformat() + "Z",
                "payer": {
                    "payment_method": "paypal"
                },
                "plan": {
                    "id": "P-5ML4271244454362WXNWU5NQ",  # This would be a real plan ID
                    "state": "ACTIVE",
                    "name": plan_name,
                    "description": description,
                    "type": "INFINITE",
                    "payment_definitions": [{
                        "name": "Monthly Payment",
                        "type": "REGULAR",
                        "frequency": "MONTH",
                        "frequency_interval": "1",
                        "amount": {
                            "currency": self.config['PAYPAL_CURRENCY'],
                            "value": "0.01"  # Minimal amount, will be updated with actual charges
                        },
                        "cycles": "0"  # Infinite
                    }],
                    "merchant_preferences": {
                        "setup_fee": {
                            "currency": self.config['PAYPAL_CURRENCY'],
                            "value": "0.00"
                        },
                        "return_url": return_url or self.config['PAYPAL_RETURN_URL'],
                        "cancel_url": cancel_url or self.config['PAYPAL_CANCEL_URL'],
                        "auto_bill_amount": "YES",
                        "initial_fail_amount_action": "CONTINUE",
                        "max_fail_attempts": "3"
                    }
                }
            }
            
            # Create billing agreement
            response = self.make_request('POST', '/v1/payments/billing-agreements', agreement_data)
            
            current_app.logger.info(f"Created billing agreement for company {company_id}")
            return response, None
            
        except Exception as e:
            current_app.logger.error(f"Error creating billing agreement: {e}")
            return None, str(e)
    
    def execute_billing_agreement(self, token):
        """Execute (activate) a billing agreement after user approval."""
        try:
            response = self.make_request('POST', f'/v1/payments/billing-agreements/{token}/agreement-execute')
            
            current_app.logger.info(f"Executed billing agreement {token}")
            return response, None
            
        except Exception as e:
            current_app.logger.error(f"Error executing billing agreement: {e}")
            return None, str(e)
    
    def get_billing_agreement(self, agreement_id):
        """Get billing agreement details."""
        try:
            response = self.make_request('GET', f'/v1/payments/billing-agreements/{agreement_id}')
            return response, None
            
        except Exception as e:
            current_app.logger.error(f"Error getting billing agreement: {e}")
            return None, str(e)
    
    def charge_billing_agreement(self, agreement_id, amount, description, invoice_number=None):
        """Charge a billing agreement (reference transaction)."""
        try:
            charge_data = {
                "amount": {
                    "total": str(amount),
                    "currency": self.config['PAYPAL_CURRENCY']
                },
                "description": description,
                "invoice_number": invoice_number,
                "payment_options": {
                    "allowed_payment_method": "UNRESTRICTED"
                }
            }
            
            response = self.make_request(
                'POST', 
                f'/v1/payments/billing-agreements/{agreement_id}/bill-balance',
                charge_data
            )
            
            current_app.logger.info(f"Charged ${amount} to billing agreement {agreement_id}")
            return response, None
            
        except Exception as e:
            current_app.logger.error(f"Error charging billing agreement: {e}")
            return None, str(e)
    
    def cancel_billing_agreement(self, agreement_id, reason):
        """Cancel a billing agreement."""
        try:
            cancel_data = {
                "note": reason
            }
            
            response = self.make_request(
                'POST',
                f'/v1/payments/billing-agreements/{agreement_id}/cancel',
                cancel_data
            )
            
            current_app.logger.info(f"Cancelled billing agreement {agreement_id}")
            return response, None
            
        except Exception as e:
            current_app.logger.error(f"Error cancelling billing agreement: {e}")
            return None, str(e)
    
    def suspend_billing_agreement(self, agreement_id, reason):
        """Suspend a billing agreement."""
        try:
            suspend_data = {
                "note": reason
            }
            
            response = self.make_request(
                'POST',
                f'/v1/payments/billing-agreements/{agreement_id}/suspend',
                suspend_data
            )
            
            current_app.logger.info(f"Suspended billing agreement {agreement_id}")
            return response, None
            
        except Exception as e:
            current_app.logger.error(f"Error suspending billing agreement: {e}")
            return None, str(e)
    
    def reactivate_billing_agreement(self, agreement_id, reason):
        """Reactivate a suspended billing agreement."""
        try:
            reactivate_data = {
                "note": reason
            }
            
            response = self.make_request(
                'POST',
                f'/v1/payments/billing-agreements/{agreement_id}/re-activate',
                reactivate_data
            )
            
            current_app.logger.info(f"Reactivated billing agreement {agreement_id}")
            return response, None
            
        except Exception as e:
            current_app.logger.error(f"Error reactivating billing agreement: {e}")
            return None, str(e)

    def verify_webhook_signature(self, headers, body, webhook_id):
        """Verify PayPal webhook signature."""
        try:
            if not self.config.get('PAYPAL_VALIDATE_WEBHOOK', True):
                return True  # Skip validation if disabled

            verification_data = {
                "auth_algo": headers.get('PAYPAL-AUTH-ALGO'),
                "cert_id": headers.get('PAYPAL-CERT-ID'),
                "transmission_id": headers.get('PAYPAL-TRANSMISSION-ID'),
                "transmission_sig": headers.get('PAYPAL-TRANSMISSION-SIG'),
                "transmission_time": headers.get('PAYPAL-TRANSMISSION-TIME'),
                "webhook_id": webhook_id,
                "webhook_event": json.loads(body) if isinstance(body, str) else body
            }

            response = self.make_request(
                'POST',
                '/v1/notifications/verify-webhook-signature',
                verification_data
            )

            return response.get('verification_status') == 'SUCCESS'

        except Exception as e:
            current_app.logger.error(f"Error verifying webhook signature: {e}")
            return False

    def get_payment_details(self, payment_id):
        """Get payment details by payment ID."""
        try:
            response = self.make_request('GET', f'/v1/payments/payment/{payment_id}')
            return response, None

        except Exception as e:
            current_app.logger.error(f"Error getting payment details: {e}")
            return None, str(e)

    def refund_payment(self, sale_id, amount=None, reason=None):
        """Refund a payment."""
        try:
            refund_data = {}

            if amount:
                refund_data["amount"] = {
                    "total": str(amount),
                    "currency": self.config['PAYPAL_CURRENCY']
                }

            if reason:
                refund_data["description"] = reason

            response = self.make_request(
                'POST',
                f'/v1/payments/sale/{sale_id}/refund',
                refund_data
            )

            current_app.logger.info(f"Refunded payment {sale_id}")
            return response, None

        except Exception as e:
            current_app.logger.error(f"Error refunding payment: {e}")
            return None, str(e)

    def create_webhook(self, url, events=None):
        """Create a webhook endpoint."""
        try:
            if not events:
                events = PayPalConfig.get_webhook_events()

            webhook_data = {
                "url": url,
                "event_types": [{"name": event} for event in events]
            }

            response = self.make_request('POST', '/v1/notifications/webhooks', webhook_data)

            current_app.logger.info(f"Created webhook for URL {url}")
            return response, None

        except Exception as e:
            current_app.logger.error(f"Error creating webhook: {e}")
            return None, str(e)

    def get_webhook_events(self, webhook_id):
        """Get webhook event types."""
        try:
            response = self.make_request('GET', f'/v1/notifications/webhooks/{webhook_id}')
            return response, None

        except Exception as e:
            current_app.logger.error(f"Error getting webhook events: {e}")
            return None, str(e)

    def parse_error_response(self, error_response):
        """Parse PayPal error response and return user-friendly message."""
        try:
            if isinstance(error_response, str):
                return error_response

            error_codes = PayPalConfig.get_error_codes()

            if isinstance(error_response, dict):
                # Handle different error response formats
                if 'details' in error_response:
                    details = error_response['details']
                    if isinstance(details, list) and details:
                        issue = details[0].get('issue', '')
                        return error_codes.get(issue, f"PayPal error: {issue}")

                if 'error' in error_response:
                    error = error_response['error']
                    return error_codes.get(error, f"PayPal error: {error}")

                if 'message' in error_response:
                    return error_response['message']

            return "An error occurred while processing your payment"

        except Exception as e:
            current_app.logger.error(f"Error parsing PayPal error response: {e}")
            return "An unexpected error occurred"

    def health_check(self):
        """Check PayPal API connectivity."""
        try:
            # Simple API call to check connectivity
            token = self.get_access_token()
            if token:
                current_app.logger.info("PayPal API health check passed")
                return True, "PayPal API is accessible"
            else:
                return False, "Failed to get access token"

        except Exception as e:
            current_app.logger.error(f"PayPal API health check failed: {e}")
            return False, str(e)
