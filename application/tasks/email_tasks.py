"""
Email Tasks for KaziSync HRMS
Celery tasks for background email processing
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from celery import current_task
from flask import current_app
from flask_mail import Message
import json

from application.config.celery_config import TASK_RETRY_CONFIG
from application.utils.db_connection import DatabaseConnection

logger = logging.getLogger(__name__)

# This will be set when the app is created
celery = None


def get_celery_app():
    """Get the Celery app instance"""
    global celery
    if celery is None:
        from application.config.celery_config import make_celery
        from flask import current_app
        celery = make_celery(current_app)
    return celery


@get_celery_app().task(bind=True, name='application.tasks.email_tasks.send_email_async')
def send_email_async(self, message_data: str, priority: str = 'normal'):
    """
    Send email asynchronously via Celery
    
    Args:
        message_data: Serialized email message
        priority: Email priority (high, normal, low)
    """
    try:
        from application.services.email.email_service import EmailService
        from flask_mail import Mail
        
        # Initialize email service
        mail = Mail(current_app)
        email_service = EmailService(mail)
        
        # Recreate message from serialized data
        message = Message()
        message_dict = json.loads(message_data)
        
        message.subject = message_dict.get('subject', '')
        message.recipients = message_dict.get('recipients', [])
        message.sender = message_dict.get('sender', '')
        message.body = message_dict.get('body', '')
        message.html = message_dict.get('html', '')
        message.cc = message_dict.get('cc', [])
        message.bcc = message_dict.get('bcc', [])
        
        # Send email
        success = email_service.send_email_sync(message)
        
        if success:
            logger.info(f"Email sent successfully: {message.subject}")
            return {'status': 'success', 'message': 'Email sent successfully'}
        else:
            raise Exception("Failed to send email")
            
    except Exception as exc:
        logger.error(f"Error sending email: {str(exc)}")
        
        # Retry logic based on priority
        retry_config = TASK_RETRY_CONFIG.get('email_send', {})
        max_retries = retry_config.get('max_retries', 3)
        countdown = retry_config.get('countdown', 60)
        
        if self.request.retries < max_retries:
            logger.info(f"Retrying email send (attempt {self.request.retries + 1}/{max_retries})")
            raise self.retry(exc=exc, countdown=countdown, max_retries=max_retries)
        else:
            logger.error(f"Email send failed after {max_retries} attempts")
            return {'status': 'failed', 'error': str(exc)}


@get_celery_app().task(bind=True, name='application.tasks.email_tasks.send_otp_email')
def send_otp_email(self, user_id: str, email: str, user_name: str, 
                   purpose: str = 'login', company_id: str = None):
    """
    Generate and send OTP email
    
    Args:
        user_id: User identifier
        email: User email address
        user_name: User display name
        purpose: OTP purpose (login, password_reset, etc.)
        company_id: Company identifier for multi-tenant support
    """
    try:
        from application.services.auth.otp_service import OTPService
        
        otp_service = OTPService()
        success, message, session_id = otp_service.send_otp_email(
            user_id=user_id,
            email=email,
            user_name=user_name,
            purpose=purpose,
            company_id=company_id
        )
        
        if success:
            logger.info(f"OTP email sent successfully to {email}")
            return {
                'status': 'success',
                'message': message,
                'session_id': session_id
            }
        else:
            raise Exception(message)
            
    except Exception as exc:
        logger.error(f"Error sending OTP email: {str(exc)}")
        
        # Retry with shorter delay for OTP emails
        retry_config = TASK_RETRY_CONFIG.get('otp_send', {})
        max_retries = retry_config.get('max_retries', 2)
        countdown = retry_config.get('countdown', 30)
        
        if self.request.retries < max_retries:
            logger.info(f"Retrying OTP email send (attempt {self.request.retries + 1}/{max_retries})")
            raise self.retry(exc=exc, countdown=countdown, max_retries=max_retries)
        else:
            logger.error(f"OTP email send failed after {max_retries} attempts")
            return {'status': 'failed', 'error': str(exc)}


@get_celery_app().task(name='application.tasks.email_tasks.send_notification_email')
def send_notification_email(notification_type: str, notification_subtype: str,
                          recipients: List[str], context: Dict[str, Any],
                          company_id: str = None):
    """
    Send notification email based on type and subtype
    
    Args:
        notification_type: Type of notification (auth, leave, payroll, etc.)
        notification_subtype: Subtype of notification
        recipients: List of email addresses
        context: Template context data
        company_id: Company identifier
    """
    try:
        from application.services.email.email_service import EmailService
        from flask_mail import Mail
        
        # Initialize email service
        mail = Mail(current_app)
        email_service = EmailService(mail)
        
        # Send notification email
        success = email_service.send_notification_email(
            notification_type=notification_type,
            notification_subtype=notification_subtype,
            recipients=recipients,
            context=context,
            company_id=company_id
        )
        
        if success:
            logger.info(f"Notification email sent: {notification_type}.{notification_subtype}")
            return {'status': 'success', 'message': 'Notification sent successfully'}
        else:
            raise Exception("Failed to send notification email")
            
    except Exception as exc:
        logger.error(f"Error sending notification email: {str(exc)}")
        return {'status': 'failed', 'error': str(exc)}


@get_celery_app().task(name='application.tasks.email_tasks.send_bulk_emails')
def send_bulk_emails(email_list: List[Dict[str, Any]], batch_size: int = 10):
    """
    Send bulk emails in batches
    
    Args:
        email_list: List of email data dictionaries
        batch_size: Number of emails to send per batch
    """
    try:
        from application.services.email.email_service import EmailService
        from flask_mail import Mail
        
        # Initialize email service
        mail = Mail(current_app)
        email_service = EmailService(mail)
        
        total_emails = len(email_list)
        sent_count = 0
        failed_count = 0
        
        # Process emails in batches
        for i in range(0, total_emails, batch_size):
            batch = email_list[i:i + batch_size]
            
            for email_data in batch:
                try:
                    success = email_service.send_templated_email(
                        recipients=email_data['recipients'],
                        subject=email_data['subject'],
                        template_path=email_data['template_path'],
                        context=email_data['context'],
                        company_id=email_data.get('company_id'),
                        send_async=False  # Already in async context
                    )
                    
                    if success:
                        sent_count += 1
                    else:
                        failed_count += 1
                        
                except Exception as e:
                    logger.error(f"Error sending bulk email: {str(e)}")
                    failed_count += 1
            
            # Update task progress
            if current_task:
                current_task.update_state(
                    state='PROGRESS',
                    meta={
                        'current': i + len(batch),
                        'total': total_emails,
                        'sent': sent_count,
                        'failed': failed_count
                    }
                )
        
        logger.info(f"Bulk email completed: {sent_count} sent, {failed_count} failed")
        return {
            'status': 'completed',
            'total': total_emails,
            'sent': sent_count,
            'failed': failed_count
        }
        
    except Exception as exc:
        logger.error(f"Error in bulk email task: {str(exc)}")
        return {'status': 'failed', 'error': str(exc)}


@get_celery_app().task(name='application.tasks.email_tasks.cleanup_expired_tokens')
def cleanup_expired_tokens():
    """Clean up expired OTP and other authentication tokens"""
    try:
        from application.Models.auth.otp_token import OTPToken
        
        # Clean up OTP tokens from central database
        with DatabaseConnection.get_session('central_db') as session:
            cleaned_count = OTPToken.cleanup_expired(session)
            session.commit()
            
        logger.info(f"Cleaned up {cleaned_count} expired OTP tokens")
        return {'status': 'success', 'cleaned_count': cleaned_count}
        
    except Exception as exc:
        logger.error(f"Error cleaning up expired tokens: {str(exc)}")
        return {'status': 'failed', 'error': str(exc)}


@get_celery_app().task(name='application.tasks.email_tasks.cleanup_expired_password_reset_tokens')
def cleanup_expired_password_reset_tokens():
    """Clean up expired password reset tokens"""
    try:
        # This will be implemented when password reset tokens are created
        logger.info("Password reset token cleanup - not implemented yet")
        return {'status': 'success', 'cleaned_count': 0}
        
    except Exception as exc:
        logger.error(f"Error cleaning up password reset tokens: {str(exc)}")
        return {'status': 'failed', 'error': str(exc)}


@get_celery_app().task(name='application.tasks.email_tasks.process_email_analytics')
def process_email_analytics():
    """Process email analytics and generate reports"""
    try:
        # This will be implemented when email analytics are created
        logger.info("Email analytics processing - not implemented yet")
        return {'status': 'success', 'processed_count': 0}
        
    except Exception as exc:
        logger.error(f"Error processing email analytics: {str(exc)}")
        return {'status': 'failed', 'error': str(exc)}


@get_celery_app().task(name='application.tasks.email_tasks.check_email_delivery_status')
def check_email_delivery_status():
    """Check email delivery status and update records"""
    try:
        # This will be implemented when email logging is created
        logger.info("Email delivery status check - not implemented yet")
        return {'status': 'success', 'checked_count': 0}
        
    except Exception as exc:
        logger.error(f"Error checking email delivery status: {str(exc)}")
        return {'status': 'failed', 'error': str(exc)}
