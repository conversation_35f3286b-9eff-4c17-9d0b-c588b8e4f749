<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Sample Code Generator</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/default.min.css">
    <style>
        body {
            padding-top: 2rem;
            padding-bottom: 2rem;
        }
        .code-container {
            margin-top: 2rem;
            position: relative;
        }
        pre {
            border-radius: 0.25rem;
            padding: 1rem;
            background-color: #f8f9fa;
        }
        .copy-btn {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            z-index: 10;
        }
        .language-selector {
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">API Sample Code Generator</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="endpoint-select" class="form-label">Select Endpoint</label>
                    <select class="form-select" id="endpoint-select">
                        <option value="">-- Select an endpoint --</option>
                        <option value="login">Login</option>
                        <option value="register_user">Register User</option>
                        <option value="get_user">Get User</option>
                        <option value="logout">Logout</option>
                    </select>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="language-select" class="form-label">Select Language</label>
                    <select class="form-select" id="language-select">
                        <option value="python">Python</option>
                        <option value="javascript">JavaScript</option>
                        <option value="curl">cURL</option>
                        <option value="java">Java</option>
                        <option value="csharp">C#</option>
                    </select>
                </div>
            </div>
        </div>
        
        <div class="mb-3">
            <label for="base-url" class="form-label">Base URL</label>
            <input type="text" class="form-control" id="base-url" value="">
        </div>
        
        <button id="generate-btn" class="btn btn-primary">Generate Sample Code</button>
        
        <div class="code-container d-none">
            <button class="btn btn-sm btn-outline-secondary copy-btn">Copy</button>
            <pre><code id="code-output"></code></pre>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Set default base URL to current origin
            document.getElementById('base-url').value = window.location.origin;
            
            const generateBtn = document.getElementById('generate-btn');
            const endpointSelect = document.getElementById('endpoint-select');
            const languageSelect = document.getElementById('language-select');
            const baseUrlInput = document.getElementById('base-url');
            const codeOutput = document.getElementById('code-output');
            const codeContainer = document.querySelector('.code-container');
            const copyBtn = document.querySelector('.copy-btn');
            
            generateBtn.addEventListener('click', function() {
                const endpoint = endpointSelect.value;
                const language = languageSelect.value;
                const baseUrl = baseUrlInput.value;
                
                if (!endpoint) {
                    alert('Please select an endpoint');
                    return;
                }
                
                // Make API request to get sample code
                fetch(`/api/sdk/sample/${language}/${endpoint}?base_url=${encodeURIComponent(baseUrl)}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Failed to generate sample code');
                        }
                        return response.json();
                    })
                    .then(data => {
                        codeOutput.textContent = data.code;
                        codeOutput.className = `language-${language === 'csharp' ? 'cs' : language}`;
                        hljs.highlightElement(codeOutput);
                        codeContainer.classList.remove('d-none');
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Failed to generate sample code: ' + error.message);
                    });
            });
            
            copyBtn.addEventListener('click', function() {
                const code = codeOutput.textContent;
                navigator.clipboard.writeText(code)
                    .then(() => {
                        const originalText = copyBtn.textContent;
                        copyBtn.textContent = 'Copied!';
                        setTimeout(() => {
                            copyBtn.textContent = originalText;
                        }, 2000);
                    })
                    .catch(err => {
                        console.error('Failed to copy code: ', err);
                    });
            });
        });
    </script>
</body>
</html>
