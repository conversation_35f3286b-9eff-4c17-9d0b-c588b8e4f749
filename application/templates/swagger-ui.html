<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <!-- Swagger UI CSS -->
    <link rel="stylesheet" href="https://unpkg.com/swagger-ui-dist@4.5.0/swagger-ui.css" />
    <!-- Custom CSS for enhanced styling -->
    <style>
        :root {
            --primary-color: #4a6cf7;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --info-color: #17a2b8;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --body-bg: #f5f7ff;
            --card-bg: #ffffff;
            --header-bg: #ffffff;
            --footer-bg: #ffffff;
            --border-color: #e9ecef;
            --text-color: #333333;
            --text-muted: #6c757d;
            --link-color: #4a6cf7;
            --link-hover-color: #3a56d4;
            --code-bg: #f8f9fa;
            --code-color: #e83e8c;
            --font-family: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: var(--font-family);
            background-color: var(--body-bg);
            color: var(--text-color);
        }

        .swagger-ui {
            font-family: var(--font-family) !important;
        }

        /* Header styling */
        .topbar {
            background-color: var(--header-bg);
            padding: 15px 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .topbar-wrapper {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .topbar-wrapper img {
            height: 40px;
        }

        .custom-header {
            background-color: var(--header-bg);
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .custom-header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .custom-header h1 {
            color: var(--primary-color);
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }

        .custom-header p {
            color: var(--text-muted);
            margin: 10px 0 0;
            font-size: 16px;
            line-height: 1.5;
        }

        /* Swagger UI overrides */
        .swagger-ui .info {
            margin: 30px 0;
        }

        .swagger-ui .info .title {
            color: var(--primary-color);
            font-size: 36px;
            font-weight: 600;
        }

        .swagger-ui .info .description {
            font-size: 16px;
            line-height: 1.6;
        }

        .swagger-ui .opblock-tag {
            font-size: 20px;
            font-weight: 600;
            margin: 20px 0 10px;
            padding: 15px;
            background-color: var(--card-bg);
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            border-left: 4px solid var(--primary-color);
        }

        .swagger-ui .opblock {
            margin: 0 0 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            overflow: hidden;
            border: 1px solid var(--border-color);
        }

        .swagger-ui .opblock .opblock-summary {
            padding: 10px 20px;
        }

        .swagger-ui .opblock .opblock-summary-method {
            border-radius: 4px;
            font-weight: 600;
            min-width: 80px;
            text-align: center;
        }

        .swagger-ui .opblock-tag-section {
            margin-bottom: 30px;
        }

        /* Method colors */
        .swagger-ui .opblock-get {
            background-color: rgba(97, 175, 254, 0.1);
            border-color: #61affe;
        }

        .swagger-ui .opblock-post {
            background-color: rgba(73, 204, 144, 0.1);
            border-color: #49cc90;
        }

        .swagger-ui .opblock-put {
            background-color: rgba(252, 161, 48, 0.1);
            border-color: #fca130;
        }

        .swagger-ui .opblock-delete {
            background-color: rgba(249, 62, 62, 0.1);
            border-color: #f93e3e;
        }

        .swagger-ui .opblock-patch {
            background-color: rgba(80, 227, 194, 0.1);
            border-color: #50e3c2;
        }

        /* Button styling */
        .swagger-ui .btn {
            border-radius: 4px;
            font-weight: 600;
            transition: all 0.2s ease;
        }

        .swagger-ui .btn.execute {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .swagger-ui .btn.execute:hover {
            background-color: var(--link-hover-color);
            border-color: var(--link-hover-color);
        }

        /* Response styling */
        .swagger-ui .responses-table {
            border-radius: 5px;
            overflow: hidden;
        }

        .swagger-ui .response-col_status {
            font-weight: 600;
        }

        .swagger-ui .response-col_description__inner div.markdown {
            font-size: 14px;
            line-height: 1.5;
        }

        /* Code blocks */
        .swagger-ui .highlight-code {
            background-color: var(--code-bg);
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }

        .swagger-ui .model-box {
            background-color: var(--code-bg);
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }

        /* Authentication section */
        .swagger-ui .auth-wrapper {
            display: flex;
            justify-content: flex-end;
            margin: 0 0 20px;
        }

        .swagger-ui .auth-container {
            padding: 15px;
            border-radius: 5px;
            background-color: var(--card-bg);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            border: 1px solid var(--border-color);
        }

        .swagger-ui .auth-btn-wrapper {
            display: flex;
            justify-content: flex-end;
        }

        /* Footer */
        .custom-footer {
            background-color: var(--footer-bg);
            padding: 20px;
            margin-top: 40px;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
            text-align: center;
        }

        .custom-footer p {
            color: var(--text-muted);
            margin: 0;
            font-size: 14px;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .custom-header h1 {
                font-size: 24px;
            }

            .swagger-ui .info .title {
                font-size: 28px;
            }

            .swagger-ui .opblock-tag {
                font-size: 18px;
            }
        }

        /* Authentication flow visualization */
        .auth-flow {
            background-color: var(--card-bg);
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .auth-flow h3 {
            color: var(--primary-color);
            margin-top: 0;
        }

        .auth-flow-steps {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 20px;
        }

        .auth-flow-step {
            flex: 1;
            min-width: 200px;
            background-color: var(--light-color);
            border-radius: 5px;
            padding: 15px;
            position: relative;
        }

        .auth-flow-step:not(:last-child)::after {
            content: "→";
            position: absolute;
            right: -15px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 20px;
            color: var(--secondary-color);
        }

        .auth-flow-step h4 {
            margin-top: 0;
            color: var(--primary-color);
        }

        .auth-flow-step p {
            margin-bottom: 0;
            font-size: 14px;
        }

        /* Code examples */
        .code-example {
            background-color: var(--code-bg);
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            overflow-x: auto;
        }

        .code-example pre {
            margin: 0;
            font-family: 'Courier New', monospace;
        }

        /* Language tabs */
        .language-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }

        .language-tab {
            padding: 5px 10px;
            border-radius: 5px 5px 0 0;
            background-color: var(--light-color);
            cursor: pointer;
            border: 1px solid var(--border-color);
            border-bottom: none;
        }

        .language-tab.active {
            background-color: var(--primary-color);
            color: white;
        }

        /* Try it out section */
        .swagger-ui .try-out {
            margin-top: 10px;
        }

        .swagger-ui .try-out__btn {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 600;
        }

        .swagger-ui .try-out__btn:hover {
            background-color: var(--link-hover-color);
        }
    </style>
</head>
<body>
    <!-- Custom Header -->
    <div class="custom-header">
        <div class="custom-header-content">
            <h1>{{ title }} Documentation</h1>
            <p>Interactive API documentation for the {{ title }}</p>
        </div>
    </div>

    <!-- Authentication Flow Visualization -->
    <div class="custom-header-content auth-flow">
        <h3>Authentication Flow</h3>
        <p>Follow these steps to authenticate with the API:</p>
        <div class="auth-flow-steps">
            <div class="auth-flow-step">
                <h4>Step 1: Login</h4>
                <p>Use the <code>/users/login</code> endpoint to obtain access and refresh tokens.</p>
            </div>
            <div class="auth-flow-step">
                <h4>Step 2: Use Token</h4>
                <p>Include the access token in the Authorization header for authenticated requests.</p>
            </div>
            <div class="auth-flow-step">
                <h4>Step 3: Refresh Token</h4>
                <p>When the access token expires, use the refresh token to get a new one.</p>
            </div>
        </div>
    </div>

    <!-- Swagger UI Container -->
    <div id="swagger-ui"></div>

    <!-- Custom Footer -->
    <div class="custom-footer">
        <p>&copy; {{ year }} {{ title }}. All rights reserved.</p>
    </div>

    <!-- Swagger UI JS -->
    <script src="https://unpkg.com/swagger-ui-dist@4.5.0/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@4.5.0/swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {
            // Get the current year for the footer
            document.querySelector('.custom-footer p').innerHTML = 
                document.querySelector('.custom-footer p').innerHTML.replace('{{ year }}', new Date().getFullYear());

            // Initialize Swagger UI
            const ui = SwaggerUIBundle({
                url: "{{ specs_url }}",
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "BaseLayout",
                docExpansion: "list",
                defaultModelsExpandDepth: 1,
                defaultModelExpandDepth: 1,
                defaultModelRendering: "example",
                displayRequestDuration: true,
                filter: true,
                showExtensions: true,
                showCommonExtensions: true,
                syntaxHighlight: {
                    activate: true,
                    theme: "agate"
                },
                tryItOutEnabled: true
            });

            // Add syntax highlighting to code examples
            setTimeout(function() {
                const codeBlocks = document.querySelectorAll('.swagger-ui .highlight-code pre');
                codeBlocks.forEach(block => {
                    block.classList.add('language-json');
                    hljs.highlightElement(block);
                });
            }, 1000);

            window.ui = ui;
        }
    </script>
    <!-- Syntax highlighting -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/atom-one-dark.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
</body>
</html>
