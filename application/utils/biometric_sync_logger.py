"""
Biometric Synchronization Logger
Dedicated logging utility for tracking biometric data synchronization operations
"""

import logging
import os
from datetime import datetime
from flask import current_app

class BiometricSyncLogger:
    """Dedicated logger for biometric synchronization operations"""
    
    def __init__(self):
        self.logger = None
        self._setup_logger()
    
    def _setup_logger(self):
        """Setup dedicated logger for biometric sync"""
        self.logger = logging.getLogger('biometric_sync')
        self.logger.setLevel(logging.INFO)
        
        # Avoid duplicate handlers
        if not self.logger.handlers:
            # Create logs directory if it doesn't exist
            log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
            os.makedirs(log_dir, exist_ok=True)
            
            # File handler for biometric sync logs
            log_file = os.path.join(log_dir, 'biometric_sync.log')
            file_handler = logging.FileHandler(log_file)
            file_handler.setLevel(logging.INFO)
            
            # Formatter
            formatter = logging.Formatter(
                '[%(asctime)s] [%(levelname)s] [BIOMETRIC_SYNC] - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            file_handler.setFormatter(formatter)
            
            self.logger.addHandler(file_handler)
    
    def log_employee_distribution_start(self, person_id, person_name, device_count):
        """Log start of employee distribution to devices"""
        self.logger.info(f"EMPLOYEE_DISTRIBUTION_START: Person {person_id} ({person_name}) to {device_count} devices")
    
    def log_name_sync_success(self, person_id, person_name, device_sn):
        """Log successful name synchronization"""
        self.logger.info(f"NAME_SYNC_SUCCESS: Person {person_id} ({person_name}) sent to device {device_sn}")
    
    def log_name_sync_failure(self, person_id, person_name, device_sn, error):
        """Log failed name synchronization"""
        self.logger.error(f"NAME_SYNC_FAILURE: Person {person_id} ({person_name}) to device {device_sn} - Error: {error}")
    
    def log_biometric_scan_start(self, person_id, person_name):
        """Log start of biometric data scanning"""
        self.logger.info(f"BIOMETRIC_SCAN_START: Scanning biometric data for person {person_id} ({person_name})")
    
    def log_biometric_found(self, person_id, backupnum, data_size):
        """Log when biometric data is found"""
        self.logger.info(f"BIOMETRIC_FOUND: Person {person_id} - backupnum {backupnum} - size {data_size} chars")
    
    def log_biometric_sync_success(self, person_id, person_name, backupnum, device_sn):
        """Log successful biometric synchronization"""
        biometric_type = self._get_biometric_type_name(backupnum)
        self.logger.info(f"BIOMETRIC_SYNC_SUCCESS: Person {person_id} ({person_name}) - {biometric_type} (backupnum {backupnum}) sent to device {device_sn}")
    
    def log_biometric_sync_failure(self, person_id, person_name, backupnum, device_sn, error):
        """Log failed biometric synchronization"""
        biometric_type = self._get_biometric_type_name(backupnum)
        self.logger.error(f"BIOMETRIC_SYNC_FAILURE: Person {person_id} ({person_name}) - {biometric_type} (backupnum {backupnum}) to device {device_sn} - Error: {error}")
    
    def log_employee_distribution_summary(self, person_id, person_name, devices_synced, total_devices, biometric_count):
        """Log summary of employee distribution"""
        self.logger.info(f"EMPLOYEE_DISTRIBUTION_SUMMARY: Person {person_id} ({person_name}) - {devices_synced}/{total_devices} devices synced, {biometric_count} biometric templates sent")
    
    def log_biometric_sync_disabled(self, reason):
        """Log when biometric sync is disabled"""
        self.logger.warning(f"BIOMETRIC_SYNC_DISABLED: {reason}")
    
    def _get_biometric_type_name(self, backupnum):
        """Get human-readable biometric type name"""
        if backupnum == 50:
            return "Face Image"
        elif 0 <= backupnum <= 9:
            return f"Fingerprint {backupnum}"
        elif backupnum == 10:
            return "Password"
        elif backupnum == 11:
            return "Card"
        elif 20 <= backupnum <= 27:
            return f"Face Template {backupnum-20}"
        else:
            return f"Unknown Type {backupnum}"

# Global instance
biometric_sync_logger = BiometricSyncLogger()
