from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from contextlib import contextmanager
from dotenv import load_dotenv
import os

load_dotenv()

class DatabaseConnection:
    """Database connection class for handling database sessions."""

    def __init__(self):
        self.db_user = os.getenv('DB_USER')
        self.db_password = os.getenv('DB_PASSWORD')
        self.db_host = os.getenv('DB_HOST')

        if not all([self.db_user, self.db_password, self.db_host]):
            raise ValueError("Database credentials are missing. Please check your environment variables.")

        self.engines = {}  # Cache engines per database

    def get_engine(self, database_name):
        """Get or create an engine for the given database."""
        if database_name not in self.engines:
            # Use URL encoding for special characters in the database name
            from urllib.parse import quote_plus
            encoded_db_name = quote_plus(database_name)
            self.engines[database_name] = create_engine(
                f'postgresql+psycopg2://{self.db_user}:{self.db_password}@{self.db_host}/{encoded_db_name}',
                pool_size=10,           # Adjust based on expected concurrent connections
                max_overflow=20,        # Allow temporary burst connections
                pool_recycle=1800,      # Recycle connections after 30 mins
                pool_pre_ping=True      # Check if the connection is alive
            )
        return self.engines[database_name]

    @contextmanager
    def get_session(self, database_name):
        """Provide a transactional scope around a series of operations."""
        engine = self.get_engine(database_name)
        Session = sessionmaker(bind=engine)
        session = Session()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()
