import hashlib
from flask import current_app as app
from application.Models.company import Company

def encode_company_id_in_username(username, company_id):
    """
    Hide company ID within username structure using steganographic approach.
    
    Args:
        username (str): Original username
        company_id (str): Company ID to encode
        
    Returns:
        str: Username with encoded company ID
    """
    try:
        # Convert company ID to a numeric representation
        hash_object = hashlib.md5(company_id.encode())
        numeric_id = int(hash_object.hexdigest(), 16) % 10000  # Get a 4-digit number
        
        # Convert to a pattern of characters to insert
        # Using a-j for digits 0-9 to make it less obvious
        pattern = ''.join([chr(97 + int(d)) for d in str(numeric_id).zfill(4)])
        
        # Insert pattern characters at specific positions
        username_chars = list(username)
        positions = [min(i * len(username) // 5, len(username) - 1) for i in range(1, 5)]
        
        for i, pos in enumerate(positions):
            username_chars.insert(pos + i, pattern[i])
        
        return ''.join(username_chars)
    except Exception as e:
        app.logger.error(f"Error encoding company ID in username: {str(e)}")
        return username  # Return original username if encoding fails

def extract_company_id_from_username(encoded_username):
    """
    Extract company ID from steganographically encoded username.
    
    Args:
        encoded_username (str): Username with encoded company ID
        
    Returns:
        tuple: (company_id, original_username) or (None, encoded_username) if extraction fails
    """
    try:
        # Try different username lengths to find valid pattern
        for original_len in range(max(len(encoded_username) - 5, 3), len(encoded_username)):
            positions = [min(i * original_len // 5, original_len - 1) for i in range(1, 5)]
            
            # Extract pattern characters
            pattern = ''
            username_chars = list(encoded_username)
            
            # Adjust positions for already removed characters
            adjusted_positions = []
            for i, pos in enumerate(positions):
                adjusted_pos = pos + i
                if adjusted_pos < len(username_chars):
                    adjusted_positions.append(adjusted_pos)
            
            # Skip if we can't extract enough characters
            if len(adjusted_positions) < 4:
                continue
            
            # Extract pattern characters
            for pos in adjusted_positions[:4]:
                pattern += username_chars[pos]
            
            # Convert pattern back to numeric ID
            if len(pattern) == 4:
                try:
                    numeric_id = ''.join([str(ord(c) - 97) for c in pattern])
                    
                    # Look up company by this hash
                    companies = Company.query.all()
                    for company in companies:
                        hash_object = hashlib.md5(company.company_id.encode())
                        company_numeric_id = int(hash_object.hexdigest(), 16) % 10000
                        if str(company_numeric_id).zfill(4) == numeric_id:
                            # Reconstruct original username by removing pattern chars
                            original_username = list(encoded_username)
                            for pos in sorted(adjusted_positions[:4], reverse=True):
                                original_username.pop(pos)
                            return company.company_id, ''.join(original_username)
                except Exception:
                    continue
    except Exception as e:
        app.logger.error(f"Error extracting company ID from username: {str(e)}")
    
    return None, encoded_username
