from flask import Flask
from application.database import central_db
from application.Models.company import Company
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()

app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('SQLALCHEMY_DATABASE_URI')
central_db.init_app(app)

with app.app_context():
    companies = Company.query.all()
    print(f'Found {len(companies)} companies:')
    for company in companies:
        print(f'- {company.company_name} (DB: {company.database_name})')
