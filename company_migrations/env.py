from logging.config import fileConfig
import os
import logging
from sqlalchemy import create_engine
from sqlalchemy import pool
from dotenv import load_dotenv
from alembic import context

# Load environment variables
load_dotenv()

# Import the models for the company databases
import sys
sys.path.append(os.getcwd())
try:
    from application.database import db

    # Biometric and Device Models (tenant-specific)
    from application.Models.EnrollInfo import EnrollInfo
    from application.Models.Device import Device
    from application.Models.Person import Person
    from application.Models.MachineCommand import MachineCommand
    from application.Models.Records import Record
    from application.Models.AccessDay import AccessDay
    from application.Models.AccessWeek import AccessWeek
    from application.Models.LockGroup import LockGroup
    from application.Models.UserLock import UserLock
    from application.Models.DeviceStatus import DeviceStatus

    # Employee Management Models
    from application.Models.employees.employee import Employee
    from application.Models.employees.department import Department
    from application.Models.employees.attendance import Attendance
    from application.Models.employees.company_user import CompanyUser
    from application.Models.employees.shift import Shift
    from application.Models.employees.employee_shift import EmployeeShift

    # Advanced Scheduling Models
    from application.Models.employees.shift_template import ShiftTemplate
    from application.Models.employees.shift_schedule import ShiftSchedule
    from application.Models.employees.schedule_assignment import ScheduleAssignment

    # Leave Management Models
    from application.Models.employees.leave_type import LeaveType
    from application.Models.employees.leave_policy import LeavePolicy
    from application.Models.employees.leave_balance import LeaveBalance
    from application.Models.employees.leave_request import LeaveRequest

    # Payroll Models
    from application.Models.employees.employee_salary import EmployeeSalary
    from application.Models.employees.payroll_run import PayrollRun
    from application.Models.employees.payslip import Payslip
    from application.Models.employees.payroll_deduction import PayrollDeduction
    from application.Models.employees.company_payroll_settings import CompanyPayrollSettings
    from application.Models.employees.allowance_type import AllowanceType
    from application.Models.employees.employee_allowance import EmployeeAllowance

    # Loan Management Models
    from application.Models.employees.loan_type import LoanType
    from application.Models.employees.employee_loan import EmployeeLoan
    from application.Models.employees.loan_repayment_schedule import LoanRepaymentSchedule
    from application.Models.employees.loan_transaction import LoanTransaction

    # Leave Audit and Management Models
    from application.Models.employees.leave_audit_log import LeaveAuditLog

    # Announcement Management Models
    from application.Models.employees.announcement import Announcement
    from application.Models.employees.announcement_read import AnnouncementRead

    # Approval Workflow Models
    from application.Models.approval.approvable_entity import ApprovableEntity
    from application.Models.approval.approval_level import ApprovalLevel
    from application.Models.approval.approval_flow import ApprovalFlow
    from application.Models.approval.approval_workflow import ApprovalWorkflow
    from application.Models.approval.approval_record import ApprovalRecord
    from application.Models.approval.approval_assignment import ApprovalAssignment

    # Onboarding Models
    from application.Models.onboarding.onboarding_workflow import OnboardingWorkflow
    from application.Models.onboarding.onboarding_workflow_task import OnboardingWorkflowTask
    from application.Models.onboarding.onboarding_instance import OnboardingInstance
    from application.Models.onboarding.onboarding_task import OnboardingTask
    from application.Models.onboarding.onboarding_document import OnboardingDocument
    from application.Models.onboarding.onboarding_document_requirement import OnboardingDocumentRequirement
    from application.Models.onboarding.onboarding_asset import OnboardingAsset
    from application.Models.onboarding.onboarding_feedback import OnboardingFeedback

    # Performance Management Models
    from application.Models.performance.performance_review_cycle import PerformanceReviewCycle
    from application.Models.performance.performance_review import PerformanceReview
    from application.Models.performance.performance_goal import PerformanceGoal
    from application.Models.performance.goal_progress_update import GoalProgressUpdate
    from application.Models.performance.performance_feedback import PerformanceFeedback
    from application.Models.performance.performance_rating_scale import PerformanceRatingScale
    from application.Models.performance.development_plan import DevelopmentPlan
    from application.Models.performance.competency_framework import CompetencyFramework

    # Recruitment Models
    from application.Models.recruitment.job_requisition import JobRequisition
    from application.Models.recruitment.job_posting import JobPosting
    from application.Models.recruitment.candidate import Candidate
    from application.Models.recruitment.job_application import JobApplication
    from application.Models.recruitment.interview import Interview
    from application.Models.recruitment.candidate_assessment import CandidateAssessment
    from application.Models.recruitment.candidate_document import CandidateDocument

    # AI Integration Models
    from application.Models.ai.ai_provider import AIProvider
    from application.Models.ai.ai_insight_template import AIInsightTemplate
    from application.Models.ai.ai_insight_request import AIInsightRequest
    from application.Models.ai.ai_insight_response import AIInsightResponse
    from application.Models.ai.ai_company_config import AICompanyConfig

    # Document Management Models
    from application.Models.documents.company_document import CompanyDocument
    from application.Models.documents.document_folder import DocumentFolder
    from application.Models.documents.document_reminder import DocumentReminder

    # Customer Loyalities models
    from application.Models.customers.customer import Customer
    from application.Models.customers.customer_visit import CustomerVisit
    from application.Models.customers.promotion_rule import PromotionRule
    from application.Models.customers.customer_loyalty_balance import CustomerLoyaltyBalance
    from application.Models.customers.reward_redemption import RewardRedemption
    from application.Models.customers.promotion_audit_log import PromotionAuditLog
    from application.Models.customers.services import Service, ServicePrice, CustomerServiceConsumption, StorageLocation, CustomerItemStorage
    
    



    print("Successfully imported all tenant database models")
except ImportError as e:
    print(f"Error importing models: {e}")
    raise

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
try:
    # Import the Flask app to get the properly configured db instance
    from app import app
    from application.database import db

    # Make sure all models are imported so they're registered with the metadata
    # Note: Models are already imported above, but we re-import here to ensure
    # they are registered with the Flask app's database metadata

    # Biometric and Device Models (tenant-specific)
    from application.Models.EnrollInfo import EnrollInfo
    from application.Models.Device import Device
    from application.Models.Person import Person
    from application.Models.MachineCommand import MachineCommand
    from application.Models.Records import Record
    from application.Models.AccessDay import AccessDay
    from application.Models.AccessWeek import AccessWeek
    from application.Models.LockGroup import LockGroup
    from application.Models.UserLock import UserLock
    from application.Models.DeviceStatus import DeviceStatus

    # Employee Management Models
    from application.Models.employees.employee import Employee
    from application.Models.employees.department import Department
    from application.Models.employees.attendance import Attendance
    from application.Models.employees.company_user import CompanyUser
    from application.Models.employees.shift import Shift
    from application.Models.employees.employee_shift import EmployeeShift

    # Advanced Scheduling Models
    from application.Models.employees.shift_template import ShiftTemplate
    from application.Models.employees.shift_schedule import ShiftSchedule
    from application.Models.employees.schedule_assignment import ScheduleAssignment

    # Leave Management Models
    from application.Models.employees.leave_type import LeaveType
    from application.Models.employees.leave_policy import LeavePolicy
    from application.Models.employees.leave_balance import LeaveBalance
    from application.Models.employees.leave_request import LeaveRequest

    # Payroll Models
    from application.Models.employees.employee_salary import EmployeeSalary
    from application.Models.employees.payroll_run import PayrollRun
    from application.Models.employees.payslip import Payslip
    from application.Models.employees.payroll_deduction import PayrollDeduction
    from application.Models.employees.company_payroll_settings import CompanyPayrollSettings
    from application.Models.employees.allowance_type import AllowanceType
    from application.Models.employees.employee_allowance import EmployeeAllowance

    # Loan Management Models
    from application.Models.employees.loan_type import LoanType
    from application.Models.employees.employee_loan import EmployeeLoan
    from application.Models.employees.loan_repayment_schedule import LoanRepaymentSchedule
    from application.Models.employees.loan_transaction import LoanTransaction

    # Leave Audit and Management Models
    from application.Models.employees.leave_audit_log import LeaveAuditLog

    # Announcement Management Models
    from application.Models.employees.announcement import Announcement
    from application.Models.employees.announcement_read import AnnouncementRead

    # Approval Workflow Models
    from application.Models.approval.approvable_entity import ApprovableEntity
    from application.Models.approval.approval_level import ApprovalLevel
    from application.Models.approval.approval_flow import ApprovalFlow
    from application.Models.approval.approval_workflow import ApprovalWorkflow
    from application.Models.approval.approval_record import ApprovalRecord
    from application.Models.approval.approval_assignment import ApprovalAssignment

    # Onboarding Models
    from application.Models.onboarding.onboarding_workflow import OnboardingWorkflow
    from application.Models.onboarding.onboarding_workflow_task import OnboardingWorkflowTask
    from application.Models.onboarding.onboarding_instance import OnboardingInstance
    from application.Models.onboarding.onboarding_task import OnboardingTask
    from application.Models.onboarding.onboarding_document import OnboardingDocument
    from application.Models.onboarding.onboarding_document_requirement import OnboardingDocumentRequirement
    from application.Models.onboarding.onboarding_asset import OnboardingAsset
    from application.Models.onboarding.onboarding_feedback import OnboardingFeedback

    # Performance Management Models
    from application.Models.performance.performance_review_cycle import PerformanceReviewCycle
    from application.Models.performance.performance_review import PerformanceReview
    from application.Models.performance.performance_goal import PerformanceGoal
    from application.Models.performance.goal_progress_update import GoalProgressUpdate
    from application.Models.performance.performance_feedback import PerformanceFeedback
    from application.Models.performance.performance_rating_scale import PerformanceRatingScale
    from application.Models.performance.development_plan import DevelopmentPlan
    from application.Models.performance.competency_framework import CompetencyFramework

    # Recruitment Models
    from application.Models.recruitment.job_requisition import JobRequisition
    from application.Models.recruitment.job_posting import JobPosting
    from application.Models.recruitment.candidate import Candidate
    from application.Models.recruitment.job_application import JobApplication
    from application.Models.recruitment.interview import Interview
    from application.Models.recruitment.candidate_assessment import CandidateAssessment
    from application.Models.recruitment.candidate_document import CandidateDocument

    # AI Integration Models
    from application.Models.ai.ai_provider import AIProvider
    from application.Models.ai.ai_insight_template import AIInsightTemplate
    from application.Models.ai.ai_insight_request import AIInsightRequest
    from application.Models.ai.ai_insight_response import AIInsightResponse
    from application.Models.ai.ai_company_config import AICompanyConfig

    # Set the target metadata to the db metadata
    with app.app_context():
        target_metadata = db.metadata
        print(f"Successfully set target metadata with {len(db.metadata.tables)} tables")
        for table_name in db.metadata.tables.keys():
            print(f"  - Table: {table_name}")
except Exception as e:
    print(f"Error setting target metadata: {e}")
    target_metadata = None

# Helper function to get all company database names
def get_company_database_names():
    """Get a list of all company databases from the central database."""
    try:
        # Import the existing Flask app that already has central_db initialized
        from app import app
        from application.Models.company import Company

        # Get company database names using the app context
        with app.app_context():
            company_dbs = Company.get_database_names()

        if company_dbs:
            logging.info(f"Found {len(company_dbs)} company databases: {', '.join(company_dbs)}")
        else:
            logging.warning("No company databases found. Using test_company_db as fallback.")
            return ['test_company_db']

        return company_dbs
    except Exception as e:
        logging.error(f"Error getting company databases: {str(e)}")
        # Fallback to test database if there's an error
        return ['test_company_db']


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    # Get database connection parameters from environment variables
    db_user = os.getenv('DB_USER')
    db_password = os.getenv('DB_PASSWORD')
    db_host = os.getenv('DB_HOST')

    # Log the database connection parameters (without password)
    print(f"Database connection parameters (offline mode): user={db_user}, host={db_host}")

    # Check if we're generating a revision or applying migrations
    # We can determine this by checking if the script was called with 'revision' in sys.argv
    import sys
    is_generating_revision = any('revision' in arg for arg in sys.argv)

    if is_generating_revision:
        # We're generating a revision - use a representative database for schema comparison
        print("Generating revision - using a representative database for schema comparison")

        # Get all company database names
        company_dbs = get_company_database_names()

        if not company_dbs:
            logging.warning("No company databases found.")
            return

        # Use the first database as a representative for schema comparison
        db_name = company_dbs[0]
        print(f"Using database for schema comparison: {db_name}")

        # For PostgreSQL, we need to properly quote database names with special characters
        from urllib.parse import quote_plus
        encoded_db_name = quote_plus(db_name)
        url = f'postgresql+psycopg2://{db_user}:{db_password}@{db_host}/{encoded_db_name}'

        try:
            context.configure(
                url=url,
                target_metadata=target_metadata,
                literal_binds=True,
                dialect_opts={"paramstyle": "named"},
                include_schemas=True
            )

            with context.begin_transaction():
                context.run_migrations()

            print(f"Successfully generated revision using database: {db_name}")
        except Exception as e:
            print(f"Error generating revision using database {db_name}: {str(e)}")
            raise
    else:
        # We're applying migrations - run for all databases
        # Get all company database names
        company_dbs = get_company_database_names()

        if not company_dbs:
            logging.warning("No company databases found.")
            return

        print(f"Running offline migrations for {len(company_dbs)} company databases: {', '.join(company_dbs)}")

        # Run migrations for each company database
        for db_name in company_dbs:
            print(f"Processing database (offline mode): {db_name}")

            # For PostgreSQL, we need to properly quote database names with special characters
            from urllib.parse import quote_plus
            encoded_db_name = quote_plus(db_name)
            url = f'postgresql+psycopg2://{db_user}:{db_password}@{db_host}/{encoded_db_name}'

            try:
                context.configure(
                    url=url,
                    target_metadata=target_metadata,
                    literal_binds=True,
                    dialect_opts={"paramstyle": "named"},
                    include_schemas=True
                )

                with context.begin_transaction():
                    context.run_migrations()

                print(f"Successfully ran offline migrations for database: {db_name}")
            except Exception as e:
                print(f"Error running offline migrations for database {db_name}: {str(e)}")
                # Continue with other databases even if one fails
                continue


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    # Get database connection parameters from environment variables
    db_user = os.getenv('DB_USER')
    db_password = os.getenv('DB_PASSWORD')
    db_host = os.getenv('DB_HOST')

    # Log the database connection parameters (without password)
    print(f"Database connection parameters: user={db_user}, host={db_host}")

    # Check if we're generating a revision or applying migrations
    # We can determine this by checking if the script was called with 'revision' in sys.argv
    import sys
    is_generating_revision = any('revision' in arg for arg in sys.argv)

    if is_generating_revision:
        # We're generating a revision - use a representative database for schema comparison
        print("Generating revision - using a representative database for schema comparison")

        # Get all company database names
        company_dbs = get_company_database_names()

        if not company_dbs:
            logging.warning("No company databases found.")
            return

        # Use the first database as a representative for schema comparison
        db_name = company_dbs[0]
        print(f"Using database for schema comparison: {db_name}")

        # For PostgreSQL, we need to properly quote database names with special characters
        from urllib.parse import quote_plus
        encoded_db_name = quote_plus(db_name)
        url = f'postgresql+psycopg2://{db_user}:{db_password}@{db_host}/{encoded_db_name}'

        print(f"Database URL (without password): postgresql+psycopg2://{db_user}:****@{db_host}/{encoded_db_name}")

        try:
            # Create a new engine for the representative database
            engine = create_engine(url)

            with engine.connect() as connection:
                # Configure Alembic context with the connection and metadata
                context.configure(
                    connection=connection,
                    target_metadata=target_metadata,
                    include_schemas=True
                )

                # Generate the migration
                with context.begin_transaction():
                    context.run_migrations()

            print(f"Successfully generated revision using database: {db_name}")
        except Exception as e:
            print(f"Error generating revision using database {db_name}: {str(e)}")
            raise
    else:
        # We're applying migrations - run for all databases
        # Get all company database names
        company_dbs = get_company_database_names()

        if not company_dbs:
            logging.warning("No company databases found.")
            return

        print(f"Running migrations for {len(company_dbs)} company databases: {', '.join(company_dbs)}")

        # Run migrations for each company database
        for db_name in company_dbs:
            print(f"Processing database: {db_name}")

            # For PostgreSQL, we need to properly quote database names with special characters
            from urllib.parse import quote_plus
            encoded_db_name = quote_plus(db_name)
            url = f'postgresql+psycopg2://{db_user}:{db_password}@{db_host}/{encoded_db_name}'

            print(f"Database URL (without password): postgresql+psycopg2://{db_user}:****@{db_host}/{encoded_db_name}")

            try:
                # Create a new engine for each database
                engine = create_engine(url)

                with engine.connect() as connection:
                    # Configure Alembic context with the connection and metadata
                    context.configure(
                        connection=connection,
                        target_metadata=target_metadata,
                        include_schemas=True
                    )

                    # Run the migration
                    with context.begin_transaction():
                        context.run_migrations()

                print(f"Successfully ran migrations for database: {db_name}")
            except Exception as e:
                print(f"Error running migrations for database {db_name}: {str(e)}")
                # Continue with other databases even if one fails
                continue


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
