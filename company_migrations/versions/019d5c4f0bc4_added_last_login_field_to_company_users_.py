"""added last_login field to company_users table

Revision ID: 019d5c4f0bc4
Revises: f694b4fa650e
Create Date: 2025-05-17 22:06:38.267799

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '019d5c4f0bc4'
down_revision = 'f694b4fa650e'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    #op.add_column('company_users', sa.Column('last_login', sa.DateTime(), nullable=True))
    # ### end Alembic commands ###
    pass


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    #op.drop_column('company_users', 'last_login')
    # ### end Alembic commands ###
    
    pass
