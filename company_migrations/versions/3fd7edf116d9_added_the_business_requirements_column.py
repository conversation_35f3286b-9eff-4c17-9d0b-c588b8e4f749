"""added the business_requirements column

Revision ID: 3fd7edf116d9
Revises: cff19cda2ce1
Create Date: 2025-08-16 12:22:09.697178

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '3fd7edf116d9'
down_revision = 'cff19cda2ce1'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """
        op.add_column('shift_templates', sa.Column('business_requirements', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('shift_templates', sa.Column('coverage_requirements', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.alter_column('shift_templates', 'pattern_type',
               existing_type=sa.VARCHAR(length=50),
               nullable=True)
    op.alter_column('shift_templates', 'rotation_cycle',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.alter_column('shift_templates', 'pattern_data',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               nullable=True)
    op.alter_column('shift_templates', 'employee_group_size',
               existing_type=sa.INTEGER(),
               nullable=True)
    # ### end Alembic commands ###
    """
    pass


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """
    op.alter_column('shift_templates', 'employee_group_size',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.alter_column('shift_templates', 'pattern_data',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               nullable=False)
    op.alter_column('shift_templates', 'rotation_cycle',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.alter_column('shift_templates', 'pattern_type',
               existing_type=sa.VARCHAR(length=50),
               nullable=False)
    op.drop_column('shift_templates', 'coverage_requirements')
    op.drop_column('shift_templates', 'business_requirements')
    # ### end Alembic commands ###
    """
    pass
