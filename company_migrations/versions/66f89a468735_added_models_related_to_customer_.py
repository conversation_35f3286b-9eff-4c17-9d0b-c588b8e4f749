"""added models related to customer consumptions

Revision ID: 66f89a468735
Revises: dc885c623470
Create Date: 2025-10-07 13:06:12.708037

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '66f89a468735'
down_revision = 'dc885c623470'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('services',
    sa.Column('service_id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('created_by', sa.UUID(), nullable=True),
    sa.PrimaryKeyConstraint('service_id')
    )
    op.create_table('storage_locations',
    sa.Column('location_id', sa.UUID(), nullable=False),
    sa.Column('location_number', sa.String(length=50), nullable=False),
    sa.Column('is_available', sa.Boolean(), nullable=False),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('location_id'),
    sa.UniqueConstraint('location_number')
    )
    op.create_table('customer_item_storage',
    sa.Column('storage_id', sa.UUID(), nullable=False),
    sa.Column('customer_id', sa.UUID(), nullable=False),
    sa.Column('location_id', sa.UUID(), nullable=False),
    sa.Column('visit_id', sa.UUID(), nullable=True),
    sa.Column('items_description', sa.Text(), nullable=False),
    sa.Column('stored_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('retrieved_at', sa.DateTime(), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['customer_id'], ['customers.customer_id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['location_id'], ['storage_locations.location_id'], ondelete='RESTRICT'),
    sa.ForeignKeyConstraint(['visit_id'], ['customer_visits.visit_id'], ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('storage_id')
    )
    op.create_table('service_prices',
    sa.Column('price_id', sa.UUID(), nullable=False),
    sa.Column('service_id', sa.UUID(), nullable=False),
    sa.Column('price_amount', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('currency', sa.String(length=3), nullable=False),
    sa.Column('effective_from', sa.Date(), nullable=False),
    sa.Column('effective_to', sa.Date(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('created_by', sa.UUID(), nullable=True),
    sa.ForeignKeyConstraint(['service_id'], ['services.service_id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('price_id')
    )
    op.create_table('customer_service_consumption',
    sa.Column('consumption_id', sa.UUID(), nullable=False),
    sa.Column('visit_id', sa.UUID(), nullable=True),
    sa.Column('customer_id', sa.UUID(), nullable=False),
    sa.Column('service_id', sa.UUID(), nullable=False),
    sa.Column('price_id', sa.UUID(), nullable=False),
    sa.Column('quantity', sa.Integer(), nullable=False),
    sa.Column('total_amount', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('consumed_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['customer_id'], ['customers.customer_id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['price_id'], ['service_prices.price_id'], ondelete='RESTRICT'),
    sa.ForeignKeyConstraint(['service_id'], ['services.service_id'], ondelete='RESTRICT'),
    sa.ForeignKeyConstraint(['visit_id'], ['customer_visits.visit_id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('consumption_id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('customer_service_consumption')
    op.drop_table('service_prices')
    op.drop_table('customer_item_storage')
    op.drop_table('storage_locations')
    op.drop_table('services')
    # ### end Alembic commands ###
