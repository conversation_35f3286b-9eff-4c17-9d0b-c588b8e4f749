"""added document management tables

Revision ID: 9f4a926a7d03
Revises: 3fd7edf116d9
Create Date: 2025-09-13 20:21:55.260431

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '9f4a926a7d03'
down_revision = '3fd7edf116d9'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """
    op.create_table('document_folders',
    sa.Column('folder_id', sa.UUID(), nullable=False),
    sa.Column('folder_name', sa.String(length=255), nullable=False),
    sa.Column('parent_folder_id', sa.UUID(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('color', sa.String(length=7), nullable=True),
    sa.Column('icon', sa.String(length=50), nullable=True),
    sa.Column('is_private', sa.<PERSON>(), nullable=True),
    sa.Column('allowed_roles', postgresql.ARRAY(sa.Text()), nullable=True),
    sa.Column('created_by', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['parent_folder_id'], ['document_folders.folder_id'], ),
    sa.PrimaryKeyConstraint('folder_id')
    )
    op.create_table('company_documents',
    sa.Column('document_id', sa.UUID(), nullable=False),
    sa.Column('employee_id', sa.UUID(), nullable=True),
    sa.Column('folder_id', sa.UUID(), nullable=True),
    sa.Column('document_name', sa.String(length=255), nullable=False),
    sa.Column('document_description', sa.Text(), nullable=True),
    sa.Column('document_category', sa.String(length=50), nullable=False),
    sa.Column('original_filename', sa.String(length=255), nullable=False),
    sa.Column('file_size_bytes', sa.BigInteger(), nullable=False),
    sa.Column('file_type', sa.String(length=100), nullable=True),
    sa.Column('mime_type', sa.String(length=100), nullable=True),
    sa.Column('storage_provider', sa.String(length=100), nullable=True),
    sa.Column('storage_bucket', sa.String(length=255), nullable=True),
    sa.Column('storage_key', sa.String(length=500), nullable=True),
    sa.Column('storage_url', sa.Text(), nullable=True),
    sa.Column('expiry_date', sa.Date(), nullable=True),
    sa.Column('is_confidential', sa.Boolean(), nullable=True),
    sa.Column('access_level', sa.String(length=50), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('uploaded_by', sa.UUID(), nullable=False),
    sa.Column('uploaded_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('search_tags', postgresql.ARRAY(sa.Text()), nullable=True),
    sa.ForeignKeyConstraint(['employee_id'], ['employees.employee_id'], ),
    sa.ForeignKeyConstraint(['folder_id'], ['document_folders.folder_id'], ),
    sa.PrimaryKeyConstraint('document_id')
    )
    op.create_table('document_reminders',
    sa.Column('reminder_id', sa.UUID(), nullable=False),
    sa.Column('document_id', sa.UUID(), nullable=False),
    sa.Column('reminder_type', sa.String(length=50), nullable=True),
    sa.Column('days_before_expiry', sa.Integer(), nullable=False),
    sa.Column('reminder_date', sa.Date(), nullable=False),
    sa.Column('is_sent', sa.Boolean(), nullable=True),
    sa.Column('sent_at', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['document_id'], ['company_documents.document_id'], ),
    sa.PrimaryKeyConstraint('reminder_id')
    )
    # ### end Alembic commands ###
    """
    pass


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """
    op.drop_table('document_reminders')
    op.drop_table('company_documents')
    op.drop_table('document_folders')
    # ### end Alembic commands ###
    """
    pass
