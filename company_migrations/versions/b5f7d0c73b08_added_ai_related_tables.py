"""added ai related tables

Revision ID: b5f7d0c73b08
Revises: 7dda3cd4da8f
Create Date: 2025-06-29 08:42:35.335021

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'b5f7d0c73b08'
down_revision = '7dda3cd4da8f'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """
    op.create_table('ai_insight_templates',
    sa.Column('template_id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('category', sa.String(length=100), nullable=False),
    sa.Column('insight_type', sa.String(length=100), nullable=False),
    sa.Column('prompt_template', sa.Text(), nullable=False),
    sa.Column('system_prompt', sa.Text(), nullable=True),
    sa.Column('variables', sa.Text(), nullable=True),
    sa.Column('target_audience', sa.String(length=100), nullable=True),
    sa.Column('output_format', sa.String(length=50), nullable=True),
    sa.Column('max_tokens', sa.Integer(), nullable=True),
    sa.Column('temperature', sa.Numeric(precision=3, scale=2), nullable=True),
    sa.Column('usage_count', sa.Integer(), nullable=True),
    sa.Column('average_rating', sa.Numeric(precision=3, scale=2), nullable=True),
    sa.Column('last_used', sa.DateTime(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_default', sa.Boolean(), nullable=True),
    sa.Column('is_system_template', sa.Boolean(), nullable=True),
    sa.Column('created_by', sa.UUID(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('template_id')
    )
    op.create_table('ai_providers',
    sa.Column('provider_id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('display_name', sa.String(length=255), nullable=False),
    sa.Column('provider_type', sa.String(length=50), nullable=False),
    sa.Column('api_endpoint', sa.String(length=500), nullable=True),
    sa.Column('api_version', sa.String(length=50), nullable=True),
    sa.Column('model_name', sa.String(length=100), nullable=False),
    sa.Column('max_tokens', sa.Integer(), nullable=True),
    sa.Column('supports_streaming', sa.Boolean(), nullable=True),
    sa.Column('supports_function_calling', sa.Boolean(), nullable=True),
    sa.Column('supports_vision', sa.Boolean(), nullable=True),
    sa.Column('requests_per_minute', sa.Integer(), nullable=True),
    sa.Column('requests_per_day', sa.Integer(), nullable=True),
    sa.Column('cost_per_1k_tokens', sa.Numeric(precision=10, scale=6), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_default', sa.Boolean(), nullable=True),
    sa.Column('configuration', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('provider_id'),
    sa.UniqueConstraint('name')
    )
    op.create_table('ai_company_configs',
    sa.Column('config_id', sa.UUID(), nullable=False),
    sa.Column('company_id', sa.String(length=36), nullable=False),
    sa.Column('preferred_provider_id', sa.UUID(), nullable=True),
    sa.Column('fallback_provider_id', sa.UUID(), nullable=True),
    sa.Column('company_size', sa.String(length=50), nullable=True),
    sa.Column('industry', sa.String(length=100), nullable=True),
    sa.Column('business_model', sa.String(length=100), nullable=True),
    sa.Column('company_culture', sa.String(length=100), nullable=True),
    sa.Column('primary_location', sa.String(length=100), nullable=True),
    sa.Column('timezone', sa.String(length=50), nullable=True),
    sa.Column('regulatory_requirements', sa.Text(), nullable=True),
    sa.Column('insight_frequency', sa.String(length=50), nullable=True),
    sa.Column('preferred_insight_types', sa.Text(), nullable=True),
    sa.Column('excluded_insight_types', sa.Text(), nullable=True),
    sa.Column('enable_ai_notifications', sa.Boolean(), nullable=True),
    sa.Column('notification_channels', sa.Text(), nullable=True),
    sa.Column('notification_recipients', sa.Text(), nullable=True),
    sa.Column('urgent_alert_threshold', sa.String(length=50), nullable=True),
    sa.Column('preferred_tone', sa.String(length=50), nullable=True),
    sa.Column('preferred_detail_level', sa.String(length=50), nullable=True),
    sa.Column('include_recommendations', sa.Boolean(), nullable=True),
    sa.Column('include_benchmarks', sa.Boolean(), nullable=True),
    sa.Column('include_predictions', sa.Boolean(), nullable=True),
    sa.Column('custom_system_prompt', sa.Text(), nullable=True),
    sa.Column('custom_context', sa.Text(), nullable=True),
    sa.Column('custom_templates', sa.Text(), nullable=True),
    sa.Column('daily_request_limit', sa.Integer(), nullable=True),
    sa.Column('monthly_request_limit', sa.Integer(), nullable=True),
    sa.Column('max_tokens_per_request', sa.Integer(), nullable=True),
    sa.Column('enable_caching', sa.Boolean(), nullable=True),
    sa.Column('cache_duration_hours', sa.Integer(), nullable=True),
    sa.Column('enable_predictive_insights', sa.Boolean(), nullable=True),
    sa.Column('enable_cross_module_insights', sa.Boolean(), nullable=True),
    sa.Column('enable_smart_announcements', sa.Boolean(), nullable=True),
    sa.Column('enable_executive_summaries', sa.Boolean(), nullable=True),
    sa.Column('data_anonymization_level', sa.String(length=50), nullable=True),
    sa.Column('exclude_sensitive_data', sa.Boolean(), nullable=True),
    sa.Column('data_retention_days', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['fallback_provider_id'], ['ai_providers.provider_id'], ),
    sa.ForeignKeyConstraint(['preferred_provider_id'], ['ai_providers.provider_id'], ),
    sa.PrimaryKeyConstraint('config_id'),
    sa.UniqueConstraint('company_id')
    )
    op.create_table('ai_insight_requests',
    sa.Column('request_id', sa.UUID(), nullable=False),
    sa.Column('company_id', sa.String(length=36), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=True),
    sa.Column('module', sa.String(length=100), nullable=False),
    sa.Column('insight_type', sa.String(length=100), nullable=False),
    sa.Column('provider_id', sa.UUID(), nullable=False),
    sa.Column('template_id', sa.UUID(), nullable=True),
    sa.Column('prompt_used', sa.Text(), nullable=False),
    sa.Column('input_data', sa.Text(), nullable=True),
    sa.Column('context_data', sa.Text(), nullable=True),
    sa.Column('max_tokens', sa.Integer(), nullable=True),
    sa.Column('temperature', sa.Numeric(precision=3, scale=2), nullable=True),
    sa.Column('other_parameters', sa.Text(), nullable=True),
    sa.Column('response_id', sa.UUID(), nullable=True),
    sa.Column('request_time', sa.DateTime(), nullable=False),
    sa.Column('response_time', sa.DateTime(), nullable=True),
    sa.Column('processing_duration_ms', sa.Integer(), nullable=True),
    sa.Column('tokens_used', sa.Integer(), nullable=True),
    sa.Column('estimated_cost', sa.Numeric(precision=10, scale=6), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('retry_count', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['provider_id'], ['ai_providers.provider_id'], ),
    sa.ForeignKeyConstraint(['template_id'], ['ai_insight_templates.template_id'], ),
    sa.PrimaryKeyConstraint('request_id')
    )
    op.create_table('ai_insight_responses',
    sa.Column('response_id', sa.UUID(), nullable=False),
    sa.Column('request_id', sa.UUID(), nullable=False),
    sa.Column('insight_content', sa.Text(), nullable=False),
    sa.Column('insight_summary', sa.Text(), nullable=True),
    sa.Column('insight_type', sa.String(length=100), nullable=False),
    sa.Column('confidence_score', sa.Numeric(precision=3, scale=2), nullable=True),
    sa.Column('key_metrics', sa.Text(), nullable=True),
    sa.Column('recommendations', sa.Text(), nullable=True),
    sa.Column('alerts', sa.Text(), nullable=True),
    sa.Column('category', sa.String(length=100), nullable=False),
    sa.Column('priority', sa.String(length=50), nullable=True),
    sa.Column('tags', sa.Text(), nullable=True),
    sa.Column('view_count', sa.Integer(), nullable=True),
    sa.Column('unique_viewers', sa.Text(), nullable=True),
    sa.Column('last_viewed', sa.DateTime(), nullable=True),
    sa.Column('user_rating', sa.Numeric(precision=3, scale=2), nullable=True),
    sa.Column('rating_count', sa.Integer(), nullable=True),
    sa.Column('feedback_comments', sa.Text(), nullable=True),
    sa.Column('actions_taken', sa.Text(), nullable=True),
    sa.Column('action_count', sa.Integer(), nullable=True),
    sa.Column('cache_key', sa.String(length=255), nullable=True),
    sa.Column('expires_at', sa.DateTime(), nullable=True),
    sa.Column('is_cached', sa.Boolean(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_featured', sa.Boolean(), nullable=True),
    sa.Column('is_archived', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['request_id'], ['ai_insight_requests.request_id'], ),
    sa.PrimaryKeyConstraint('response_id')
    )
    # ### end Alembic commands ###
    """
    pass


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """
    op.drop_table('ai_insight_responses')
    op.drop_table('ai_insight_requests')
    op.drop_table('ai_company_configs')
    op.drop_table('ai_providers')
    op.drop_table('ai_insight_templates')
    # ### end Alembic commands ###
    """
    pass
