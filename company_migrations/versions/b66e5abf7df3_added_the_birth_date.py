"""added the birth_date

Revision ID: b66e5abf7df3
Revises: b5f7d0c73b08
Create Date: 2025-07-04 20:32:15.051802

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'b66e5abf7df3'
down_revision = 'b5f7d0c73b08'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """
    op.add_column('employees', sa.Column('date_of_birth', sa.Date(), nullable=True))
    # ### end Alembic commands ###
    """
    pass


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """
    op.drop_column('employees', 'date_of_birth')
    # ### end Alembic commands ###
    """
    pass
