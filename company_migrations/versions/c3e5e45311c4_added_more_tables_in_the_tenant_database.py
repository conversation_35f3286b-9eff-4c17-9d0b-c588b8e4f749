"""added more tables in the tenant database

Revision ID: c3e5e45311c4
Revises: 5ef523eb3741
Create Date: 2025-06-01 13:27:32.074651

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'c3e5e45311c4'
down_revision = '5ef523eb3741'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """
    op.create_table('allowance_types',
    sa.Column('allowance_type_id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('code', sa.String(length=50), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('is_taxable', sa.<PERSON>(), nullable=False),
    sa.Column('is_pensionable', sa.<PERSON>(), nullable=False),
    sa.Column('calculation_type', sa.String(length=50), nullable=False),
    sa.Column('default_amount', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('allowance_type_id'),
    sa.UniqueConstraint('code')
    )
    op.create_table('candidates',
    sa.Column('candidate_id', sa.UUID(), nullable=False),
    sa.Column('company_id', sa.UUID(), nullable=False),
    sa.Column('first_name', sa.String(length=255), nullable=False),
    sa.Column('last_name', sa.String(length=255), nullable=False),
    sa.Column('email', sa.String(length=255), nullable=False),
    sa.Column('phone', sa.String(length=50), nullable=True),
    sa.Column('address_line1', sa.String(length=255), nullable=True),
    sa.Column('address_line2', sa.String(length=255), nullable=True),
    sa.Column('city', sa.String(length=100), nullable=True),
    sa.Column('state_province', sa.String(length=100), nullable=True),
    sa.Column('postal_code', sa.String(length=20), nullable=True),
    sa.Column('country', sa.String(length=100), nullable=True),
    sa.Column('current_job_title', sa.String(length=255), nullable=True),
    sa.Column('current_company', sa.String(length=255), nullable=True),
    sa.Column('current_salary', sa.Numeric(precision=12, scale=2), nullable=True),
    sa.Column('expected_salary', sa.Numeric(precision=12, scale=2), nullable=True),
    sa.Column('currency', sa.String(length=10), nullable=True),
    sa.Column('years_of_experience', sa.Integer(), nullable=True),
    sa.Column('highest_education', sa.String(length=100), nullable=True),
    sa.Column('field_of_study', sa.String(length=255), nullable=True),
    sa.Column('university', sa.String(length=255), nullable=True),
    sa.Column('graduation_year', sa.Integer(), nullable=True),
    sa.Column('skills', sa.Text(), nullable=True),
    sa.Column('certifications', sa.Text(), nullable=True),
    sa.Column('languages', sa.Text(), nullable=True),
    sa.Column('availability_date', sa.Date(), nullable=True),
    sa.Column('notice_period', sa.String(length=100), nullable=True),
    sa.Column('work_authorization', sa.String(length=100), nullable=True),
    sa.Column('willing_to_relocate', sa.Boolean(), nullable=True),
    sa.Column('preferred_locations', sa.Text(), nullable=True),
    sa.Column('remote_work_preference', sa.String(length=100), nullable=True),
    sa.Column('linkedin_url', sa.String(length=500), nullable=True),
    sa.Column('portfolio_url', sa.String(length=500), nullable=True),
    sa.Column('github_url', sa.String(length=500), nullable=True),
    sa.Column('other_profiles', sa.Text(), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('source', sa.String(length=100), nullable=True),
    sa.Column('source_details', sa.String(length=255), nullable=True),
    sa.Column('talent_pool', sa.String(length=255), nullable=True),
    sa.Column('pipeline_stage', sa.String(length=100), nullable=True),
    sa.Column('last_contact_date', sa.Date(), nullable=True),
    sa.Column('next_contact_date', sa.Date(), nullable=True),
    sa.Column('communication_preference', sa.String(length=100), nullable=True),
    sa.Column('best_time_to_contact', sa.String(length=100), nullable=True),
    sa.Column('timezone', sa.String(length=100), nullable=True),
    sa.Column('gdpr_consent', sa.Boolean(), nullable=True),
    sa.Column('marketing_consent', sa.Boolean(), nullable=True),
    sa.Column('data_retention_date', sa.Date(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('tags', sa.Text(), nullable=True),
    sa.Column('referred_by', sa.UUID(), nullable=True),
    sa.Column('referral_bonus_eligible', sa.Boolean(), nullable=True),
    sa.Column('referral_bonus_paid', sa.Boolean(), nullable=True),
    sa.Column('created_by', sa.UUID(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('candidate_id')
    )
    op.create_index(op.f('ix_candidates_company_id'), 'candidates', ['company_id'], unique=False)
    op.create_index(op.f('ix_candidates_email'), 'candidates', ['email'], unique=False)
    op.create_table('company_payroll_settings',
    sa.Column('settings_id', sa.UUID(), nullable=False),
    sa.Column('company_id', sa.String(length=36), nullable=False),
    sa.Column('default_pay_frequency', sa.String(length=20), nullable=False),
    sa.Column('payroll_cutoff_day', sa.Integer(), nullable=False),
    sa.Column('payment_day', sa.Integer(), nullable=False),
    sa.Column('overtime_enabled', sa.Boolean(), nullable=False),
    sa.Column('overtime_rate_multiplier', sa.Numeric(precision=3, scale=2), nullable=False),
    sa.Column('weekend_overtime_multiplier', sa.Numeric(precision=3, scale=2), nullable=False),
    sa.Column('holiday_overtime_multiplier', sa.Numeric(precision=3, scale=2), nullable=False),
    sa.Column('auto_generate_payslips', sa.Boolean(), nullable=False),
    sa.Column('require_approval', sa.Boolean(), nullable=False),
    sa.Column('effective_from', sa.Date(), nullable=False),
    sa.Column('effective_to', sa.Date(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('settings_id')
    )
    op.create_index('idx_company_payroll_settings_active', 'company_payroll_settings', ['company_id', 'effective_from', 'effective_to'], unique=False)
    op.create_table('competency_frameworks',
    sa.Column('framework_id', sa.UUID(), nullable=False),
    sa.Column('company_id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('framework_type', sa.String(length=100), nullable=True),
    sa.Column('applicable_roles', sa.Text(), nullable=True),
    sa.Column('applicable_departments', sa.Text(), nullable=True),
    sa.Column('applicable_levels', sa.Text(), nullable=True),
    sa.Column('proficiency_levels', sa.Text(), nullable=False),
    sa.Column('competency_categories', sa.Text(), nullable=True),
    sa.Column('is_default', sa.Boolean(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('version', sa.String(length=50), nullable=True),
    sa.Column('created_by', sa.UUID(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('framework_id')
    )
    op.create_index(op.f('ix_competency_frameworks_company_id'), 'competency_frameworks', ['company_id'], unique=False)
    op.create_table('job_requisitions',
    sa.Column('requisition_id', sa.UUID(), nullable=False),
    sa.Column('company_id', sa.UUID(), nullable=False),
    sa.Column('requisition_number', sa.String(length=100), nullable=False),
    sa.Column('job_title', sa.String(length=255), nullable=False),
    sa.Column('department', sa.String(length=255), nullable=False),
    sa.Column('reporting_manager', sa.UUID(), nullable=False),
    sa.Column('position_type', sa.String(length=100), nullable=True),
    sa.Column('employment_type', sa.String(length=100), nullable=True),
    sa.Column('location', sa.String(length=255), nullable=True),
    sa.Column('remote_work_option', sa.String(length=100), nullable=True),
    sa.Column('number_of_positions', sa.Integer(), nullable=True),
    sa.Column('replacement_for', sa.UUID(), nullable=True),
    sa.Column('urgency_level', sa.String(length=50), nullable=True),
    sa.Column('target_start_date', sa.Date(), nullable=True),
    sa.Column('budget_approved', sa.Boolean(), nullable=True),
    sa.Column('salary_range_min', sa.Numeric(precision=12, scale=2), nullable=True),
    sa.Column('salary_range_max', sa.Numeric(precision=12, scale=2), nullable=True),
    sa.Column('currency', sa.String(length=10), nullable=True),
    sa.Column('benefits_package', sa.Text(), nullable=True),
    sa.Column('business_justification', sa.Text(), nullable=False),
    sa.Column('role_responsibilities', sa.Text(), nullable=True),
    sa.Column('required_qualifications', sa.Text(), nullable=True),
    sa.Column('preferred_qualifications', sa.Text(), nullable=True),
    sa.Column('success_criteria', sa.Text(), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('current_approver', sa.UUID(), nullable=True),
    sa.Column('approval_level', sa.Integer(), nullable=True),
    sa.Column('recruitment_type', sa.String(length=100), nullable=True),
    sa.Column('sourcing_strategy', sa.Text(), nullable=True),
    sa.Column('assessment_requirements', sa.Text(), nullable=True),
    sa.Column('interview_process', sa.Text(), nullable=True),
    sa.Column('approval_deadline', sa.Date(), nullable=True),
    sa.Column('posting_start_date', sa.Date(), nullable=True),
    sa.Column('application_deadline', sa.Date(), nullable=True),
    sa.Column('target_hire_date', sa.Date(), nullable=True),
    sa.Column('requested_by', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('requisition_id'),
    sa.UniqueConstraint('requisition_number')
    )
    op.create_index(op.f('ix_job_requisitions_company_id'), 'job_requisitions', ['company_id'], unique=False)
    op.create_table('loan_types',
    sa.Column('loan_type_id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('code', sa.String(length=50), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('min_amount', sa.Numeric(precision=15, scale=2), nullable=False),
    sa.Column('max_amount', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('max_salary_multiple', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('interest_rate', sa.Numeric(precision=5, scale=4), nullable=False),
    sa.Column('interest_calculation_method', sa.String(length=20), nullable=False),
    sa.Column('min_term_months', sa.Integer(), nullable=False),
    sa.Column('max_term_months', sa.Integer(), nullable=False),
    sa.Column('max_deduction_percentage', sa.Numeric(precision=5, scale=2), nullable=False),
    sa.Column('allow_early_repayment', sa.Boolean(), nullable=False),
    sa.Column('early_repayment_penalty_rate', sa.Numeric(precision=5, scale=4), nullable=False),
    sa.Column('requires_approval', sa.Boolean(), nullable=False),
    sa.Column('auto_approve_limit', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('approval_levels', sa.Integer(), nullable=False),
    sa.Column('min_employment_months', sa.Integer(), nullable=False),
    sa.Column('max_active_loans', sa.Integer(), nullable=False),
    sa.Column('requires_guarantor', sa.Boolean(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('loan_type_id'),
    sa.UniqueConstraint('code')
    )
    op.create_table('onboarding_workflows',
    sa.Column('workflow_id', sa.UUID(), nullable=False),
    sa.Column('company_id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('workflow_type', sa.String(length=100), nullable=True),
    sa.Column('department', sa.String(length=100), nullable=True),
    sa.Column('position_level', sa.String(length=50), nullable=True),
    sa.Column('duration_days', sa.Integer(), nullable=True),
    sa.Column('auto_start', sa.Boolean(), nullable=True),
    sa.Column('requires_manager_approval', sa.Boolean(), nullable=True),
    sa.Column('requires_hr_approval', sa.Boolean(), nullable=True),
    sa.Column('send_welcome_email', sa.Boolean(), nullable=True),
    sa.Column('create_system_accounts', sa.Boolean(), nullable=True),
    sa.Column('assign_buddy', sa.Boolean(), nullable=True),
    sa.Column('schedule_orientation', sa.Boolean(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_default', sa.Boolean(), nullable=True),
    sa.Column('version', sa.String(length=20), nullable=True),
    sa.Column('created_by', sa.UUID(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('workflow_id')
    )
    op.create_index(op.f('ix_onboarding_workflows_company_id'), 'onboarding_workflows', ['company_id'], unique=False)
    op.create_table('payroll_runs',
    sa.Column('run_id', sa.UUID(), nullable=False),
    sa.Column('company_id', sa.String(length=36), nullable=False),
    sa.Column('pay_period_start', sa.Date(), nullable=False),
    sa.Column('pay_period_end', sa.Date(), nullable=False),
    sa.Column('run_date', sa.Date(), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('total_employees', sa.Integer(), nullable=False),
    sa.Column('total_gross_pay', sa.Numeric(precision=15, scale=2), nullable=False),
    sa.Column('total_tax', sa.Numeric(precision=15, scale=2), nullable=False),
    sa.Column('total_employee_deductions', sa.Numeric(precision=15, scale=2), nullable=False),
    sa.Column('total_employer_contributions', sa.Numeric(precision=15, scale=2), nullable=False),
    sa.Column('total_net_pay', sa.Numeric(precision=15, scale=2), nullable=False),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_by', sa.UUID(), nullable=True),
    sa.Column('approved_by', sa.UUID(), nullable=True),
    sa.Column('approved_at', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('run_id'),
    sa.UniqueConstraint('company_id', 'pay_period_start', 'pay_period_end', name='uq_payroll_run_period')
    )
    op.create_index('idx_payroll_run_company_period', 'payroll_runs', ['company_id', 'pay_period_start', 'pay_period_end'], unique=False)
    op.create_table('performance_rating_scales',
    sa.Column('scale_id', sa.UUID(), nullable=False),
    sa.Column('company_id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('scale_type', sa.String(length=100), nullable=True),
    sa.Column('min_value', sa.Numeric(precision=3, scale=2), nullable=True),
    sa.Column('max_value', sa.Numeric(precision=3, scale=2), nullable=True),
    sa.Column('increment', sa.Numeric(precision=3, scale=2), nullable=True),
    sa.Column('scale_points', sa.Text(), nullable=False),
    sa.Column('is_default', sa.Boolean(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('allow_na', sa.Boolean(), nullable=True),
    sa.Column('require_comments', sa.Boolean(), nullable=True),
    sa.Column('require_comments_for_extremes', sa.Boolean(), nullable=True),
    sa.Column('forced_distribution', sa.Boolean(), nullable=True),
    sa.Column('distribution_guidelines', sa.Text(), nullable=True),
    sa.Column('created_by', sa.UUID(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('scale_id')
    )
    op.create_index(op.f('ix_performance_rating_scales_company_id'), 'performance_rating_scales', ['company_id'], unique=False)
    op.create_table('performance_review_cycles',
    sa.Column('cycle_id', sa.UUID(), nullable=False),
    sa.Column('company_id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('cycle_type', sa.String(length=100), nullable=True),
    sa.Column('start_date', sa.Date(), nullable=False),
    sa.Column('end_date', sa.Date(), nullable=False),
    sa.Column('review_period_start', sa.Date(), nullable=True),
    sa.Column('review_period_end', sa.Date(), nullable=True),
    sa.Column('goal_setting_deadline', sa.Date(), nullable=True),
    sa.Column('self_review_deadline', sa.Date(), nullable=True),
    sa.Column('manager_review_deadline', sa.Date(), nullable=True),
    sa.Column('peer_feedback_deadline', sa.Date(), nullable=True),
    sa.Column('final_review_deadline', sa.Date(), nullable=True),
    sa.Column('requires_goals', sa.Boolean(), nullable=True),
    sa.Column('requires_self_review', sa.Boolean(), nullable=True),
    sa.Column('requires_manager_review', sa.Boolean(), nullable=True),
    sa.Column('requires_peer_feedback', sa.Boolean(), nullable=True),
    sa.Column('requires_subordinate_feedback', sa.Boolean(), nullable=True),
    sa.Column('include_all_employees', sa.Boolean(), nullable=True),
    sa.Column('included_departments', sa.Text(), nullable=True),
    sa.Column('excluded_employees', sa.Text(), nullable=True),
    sa.Column('minimum_tenure_days', sa.Integer(), nullable=True),
    sa.Column('rating_scale_id', sa.UUID(), nullable=True),
    sa.Column('competency_framework_id', sa.UUID(), nullable=True),
    sa.Column('auto_assign_reviewers', sa.Boolean(), nullable=True),
    sa.Column('reminder_frequency_days', sa.Integer(), nullable=True),
    sa.Column('allow_late_submissions', sa.Boolean(), nullable=True),
    sa.Column('requires_calibration', sa.Boolean(), nullable=True),
    sa.Column('calibration_deadline', sa.Date(), nullable=True),
    sa.Column('forced_ranking', sa.Boolean(), nullable=True),
    sa.Column('distribution_guidelines', sa.Text(), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('completion_percentage', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('created_by', sa.UUID(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('activated_at', sa.DateTime(), nullable=True),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('cycle_id')
    )
    op.create_index(op.f('ix_performance_review_cycles_company_id'), 'performance_review_cycles', ['company_id'], unique=False)
    op.create_table('competencies',
    sa.Column('competency_id', sa.UUID(), nullable=False),
    sa.Column('framework_id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('category', sa.String(length=255), nullable=True),
    sa.Column('competency_type', sa.String(length=100), nullable=True),
    sa.Column('is_required', sa.Boolean(), nullable=True),
    sa.Column('weight', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('level_definitions', sa.Text(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('display_order', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['framework_id'], ['competency_frameworks.framework_id'], ),
    sa.PrimaryKeyConstraint('competency_id')
    )
    op.create_table('development_plans',
    sa.Column('plan_id', sa.UUID(), nullable=False),
    sa.Column('employee_id', sa.UUID(), nullable=False),
    sa.Column('cycle_id', sa.UUID(), nullable=True),
    sa.Column('plan_name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('plan_type', sa.String(length=100), nullable=True),
    sa.Column('start_date', sa.Date(), nullable=False),
    sa.Column('target_completion_date', sa.Date(), nullable=False),
    sa.Column('actual_completion_date', sa.Date(), nullable=True),
    sa.Column('primary_focus_area', sa.String(length=255), nullable=True),
    sa.Column('secondary_focus_areas', sa.Text(), nullable=True),
    sa.Column('current_role', sa.String(length=255), nullable=True),
    sa.Column('target_role', sa.String(length=255), nullable=True),
    sa.Column('career_path', sa.String(length=255), nullable=True),
    sa.Column('promotion_timeline', sa.String(length=100), nullable=True),
    sa.Column('skill_gaps_identified', sa.Text(), nullable=True),
    sa.Column('competency_targets', sa.Text(), nullable=True),
    sa.Column('development_objectives', sa.Text(), nullable=True),
    sa.Column('success_criteria', sa.Text(), nullable=True),
    sa.Column('manager_support_needed', sa.Text(), nullable=True),
    sa.Column('mentor_assigned', sa.UUID(), nullable=True),
    sa.Column('budget_allocated', sa.Numeric(precision=10, scale=2), nullable=True),
    sa.Column('time_allocation', sa.String(length=100), nullable=True),
    sa.Column('overall_progress_percentage', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('last_review_date', sa.Date(), nullable=True),
    sa.Column('next_review_date', sa.Date(), nullable=True),
    sa.Column('review_frequency', sa.String(length=50), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('approved_by', sa.UUID(), nullable=True),
    sa.Column('approval_date', sa.DateTime(), nullable=True),
    sa.Column('approval_comments', sa.Text(), nullable=True),
    sa.Column('completion_summary', sa.Text(), nullable=True),
    sa.Column('skills_developed', sa.Text(), nullable=True),
    sa.Column('certifications_earned', sa.Text(), nullable=True),
    sa.Column('promotion_achieved', sa.Boolean(), nullable=True),
    sa.Column('role_change_date', sa.Date(), nullable=True),
    sa.Column('created_by', sa.UUID(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['cycle_id'], ['performance_review_cycles.cycle_id'], ),
    sa.PrimaryKeyConstraint('plan_id')
    )
    op.create_index(op.f('ix_development_plans_employee_id'), 'development_plans', ['employee_id'], unique=False)
    op.create_table('job_postings',
    sa.Column('posting_id', sa.UUID(), nullable=False),
    sa.Column('company_id', sa.UUID(), nullable=False),
    sa.Column('requisition_id', sa.UUID(), nullable=True),
    sa.Column('job_title', sa.String(length=255), nullable=False),
    sa.Column('job_code', sa.String(length=100), nullable=True),
    sa.Column('department', sa.String(length=255), nullable=True),
    sa.Column('location', sa.String(length=255), nullable=True),
    sa.Column('employment_type', sa.String(length=100), nullable=True),
    sa.Column('work_arrangement', sa.String(length=100), nullable=True),
    sa.Column('experience_level', sa.String(length=100), nullable=True),
    sa.Column('job_summary', sa.Text(), nullable=False),
    sa.Column('responsibilities', sa.Text(), nullable=True),
    sa.Column('required_qualifications', sa.Text(), nullable=True),
    sa.Column('preferred_qualifications', sa.Text(), nullable=True),
    sa.Column('skills_required', sa.Text(), nullable=True),
    sa.Column('salary_disclosed', sa.Boolean(), nullable=True),
    sa.Column('salary_range_min', sa.Numeric(precision=12, scale=2), nullable=True),
    sa.Column('salary_range_max', sa.Numeric(precision=12, scale=2), nullable=True),
    sa.Column('currency', sa.String(length=10), nullable=True),
    sa.Column('benefits_summary', sa.Text(), nullable=True),
    sa.Column('application_method', sa.String(length=100), nullable=True),
    sa.Column('external_application_url', sa.String(length=500), nullable=True),
    sa.Column('application_email', sa.String(length=255), nullable=True),
    sa.Column('application_instructions', sa.Text(), nullable=True),
    sa.Column('posting_date', sa.Date(), nullable=True),
    sa.Column('application_deadline', sa.Date(), nullable=True),
    sa.Column('target_start_date', sa.Date(), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('is_internal_only', sa.Boolean(), nullable=True),
    sa.Column('is_featured', sa.Boolean(), nullable=True),
    sa.Column('seo_title', sa.String(length=255), nullable=True),
    sa.Column('seo_description', sa.Text(), nullable=True),
    sa.Column('seo_keywords', sa.Text(), nullable=True),
    sa.Column('company_description', sa.Text(), nullable=True),
    sa.Column('total_applications', sa.Integer(), nullable=True),
    sa.Column('total_views', sa.Integer(), nullable=True),
    sa.Column('created_by', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['requisition_id'], ['job_requisitions.requisition_id'], ),
    sa.PrimaryKeyConstraint('posting_id')
    )
    op.create_index(op.f('ix_job_postings_company_id'), 'job_postings', ['company_id'], unique=False)
    op.create_table('onboarding_document_requirements',
    sa.Column('requirement_id', sa.UUID(), nullable=False),
    sa.Column('workflow_id', sa.UUID(), nullable=False),
    sa.Column('document_name', sa.String(length=255), nullable=False),
    sa.Column('document_code', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('category', sa.String(length=100), nullable=True),
    sa.Column('is_mandatory', sa.Boolean(), nullable=True),
    sa.Column('is_sensitive', sa.Boolean(), nullable=True),
    sa.Column('requires_verification', sa.Boolean(), nullable=True),
    sa.Column('requires_original', sa.Boolean(), nullable=True),
    sa.Column('file_types_allowed', sa.String(length=255), nullable=True),
    sa.Column('max_file_size_mb', sa.Integer(), nullable=True),
    sa.Column('min_file_size_kb', sa.Integer(), nullable=True),
    sa.Column('max_files_count', sa.Integer(), nullable=True),
    sa.Column('validation_rules', sa.Text(), nullable=True),
    sa.Column('expiry_check', sa.Boolean(), nullable=True),
    sa.Column('min_validity_days', sa.Integer(), nullable=True),
    sa.Column('display_order', sa.Integer(), nullable=True),
    sa.Column('help_text', sa.Text(), nullable=True),
    sa.Column('example_url', sa.String(length=500), nullable=True),
    sa.Column('due_days_from_start', sa.Integer(), nullable=True),
    sa.Column('blocks_other_tasks', sa.Boolean(), nullable=True),
    sa.Column('auto_remind', sa.Boolean(), nullable=True),
    sa.Column('reminder_frequency_days', sa.Integer(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['workflow_id'], ['onboarding_workflows.workflow_id'], ),
    sa.PrimaryKeyConstraint('requirement_id')
    )
    op.create_table('onboarding_instances',
    sa.Column('instance_id', sa.UUID(), nullable=False),
    sa.Column('employee_id', sa.UUID(), nullable=False),
    sa.Column('workflow_id', sa.UUID(), nullable=False),
    sa.Column('start_date', sa.Date(), nullable=False),
    sa.Column('expected_completion_date', sa.Date(), nullable=True),
    sa.Column('actual_completion_date', sa.Date(), nullable=True),
    sa.Column('first_day_date', sa.Date(), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('completion_percentage', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('current_phase', sa.String(length=100), nullable=True),
    sa.Column('assigned_hr', sa.UUID(), nullable=True),
    sa.Column('assigned_manager', sa.UUID(), nullable=True),
    sa.Column('assigned_buddy', sa.UUID(), nullable=True),
    sa.Column('total_tasks', sa.Integer(), nullable=True),
    sa.Column('completed_tasks', sa.Integer(), nullable=True),
    sa.Column('overdue_tasks', sa.Integer(), nullable=True),
    sa.Column('total_documents', sa.Integer(), nullable=True),
    sa.Column('submitted_documents', sa.Integer(), nullable=True),
    sa.Column('verified_documents', sa.Integer(), nullable=True),
    sa.Column('employee_feedback', sa.Text(), nullable=True),
    sa.Column('manager_feedback', sa.Text(), nullable=True),
    sa.Column('hr_notes', sa.Text(), nullable=True),
    sa.Column('overall_rating', sa.Integer(), nullable=True),
    sa.Column('welcome_email_sent', sa.Boolean(), nullable=True),
    sa.Column('welcome_email_sent_date', sa.DateTime(), nullable=True),
    sa.Column('system_accounts_created', sa.Boolean(), nullable=True),
    sa.Column('equipment_assigned', sa.Boolean(), nullable=True),
    sa.Column('created_by', sa.UUID(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('completed_by', sa.UUID(), nullable=True),
    sa.ForeignKeyConstraint(['workflow_id'], ['onboarding_workflows.workflow_id'], ),
    sa.PrimaryKeyConstraint('instance_id')
    )
    op.create_index(op.f('ix_onboarding_instances_employee_id'), 'onboarding_instances', ['employee_id'], unique=False)
    op.create_table('onboarding_workflow_tasks',
    sa.Column('task_template_id', sa.UUID(), nullable=False),
    sa.Column('workflow_id', sa.UUID(), nullable=False),
    sa.Column('task_name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('instructions', sa.Text(), nullable=True),
    sa.Column('completion_criteria', sa.Text(), nullable=True),
    sa.Column('assigned_to_role', sa.String(length=100), nullable=False),
    sa.Column('due_days_from_start', sa.Integer(), nullable=True),
    sa.Column('estimated_duration_hours', sa.Integer(), nullable=True),
    sa.Column('is_mandatory', sa.Boolean(), nullable=True),
    sa.Column('is_blocking', sa.Boolean(), nullable=True),
    sa.Column('task_type', sa.String(length=100), nullable=True),
    sa.Column('task_category', sa.String(length=100), nullable=True),
    sa.Column('task_order', sa.Integer(), nullable=True),
    sa.Column('depends_on_tasks', sa.Text(), nullable=True),
    sa.Column('auto_assign', sa.Boolean(), nullable=True),
    sa.Column('send_reminder', sa.Boolean(), nullable=True),
    sa.Column('reminder_days_before', sa.Integer(), nullable=True),
    sa.Column('external_system', sa.String(length=100), nullable=True),
    sa.Column('external_config', sa.Text(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['workflow_id'], ['onboarding_workflows.workflow_id'], ),
    sa.PrimaryKeyConstraint('task_template_id')
    )
    op.create_table('performance_feedback',
    sa.Column('feedback_id', sa.UUID(), nullable=False),
    sa.Column('employee_id', sa.UUID(), nullable=False),
    sa.Column('feedback_provider_id', sa.UUID(), nullable=False),
    sa.Column('cycle_id', sa.UUID(), nullable=True),
    sa.Column('feedback_type', sa.String(length=100), nullable=False),
    sa.Column('feedback_source', sa.String(length=100), nullable=False),
    sa.Column('relationship_to_employee', sa.String(length=100), nullable=True),
    sa.Column('requested_by', sa.UUID(), nullable=True),
    sa.Column('request_date', sa.DateTime(), nullable=True),
    sa.Column('due_date', sa.Date(), nullable=True),
    sa.Column('feedback_period_start', sa.Date(), nullable=True),
    sa.Column('feedback_period_end', sa.Date(), nullable=True),
    sa.Column('overall_rating', sa.Numeric(precision=3, scale=2), nullable=True),
    sa.Column('strengths', sa.Text(), nullable=True),
    sa.Column('areas_for_improvement', sa.Text(), nullable=True),
    sa.Column('specific_examples', sa.Text(), nullable=True),
    sa.Column('recommendations', sa.Text(), nullable=True),
    sa.Column('communication_rating', sa.Numeric(precision=3, scale=2), nullable=True),
    sa.Column('communication_comments', sa.Text(), nullable=True),
    sa.Column('teamwork_rating', sa.Numeric(precision=3, scale=2), nullable=True),
    sa.Column('teamwork_comments', sa.Text(), nullable=True),
    sa.Column('leadership_rating', sa.Numeric(precision=3, scale=2), nullable=True),
    sa.Column('leadership_comments', sa.Text(), nullable=True),
    sa.Column('technical_skills_rating', sa.Numeric(precision=3, scale=2), nullable=True),
    sa.Column('technical_skills_comments', sa.Text(), nullable=True),
    sa.Column('problem_solving_rating', sa.Numeric(precision=3, scale=2), nullable=True),
    sa.Column('problem_solving_comments', sa.Text(), nullable=True),
    sa.Column('initiative_rating', sa.Numeric(precision=3, scale=2), nullable=True),
    sa.Column('initiative_comments', sa.Text(), nullable=True),
    sa.Column('competency_feedback', sa.Text(), nullable=True),
    sa.Column('development_suggestions', sa.Text(), nullable=True),
    sa.Column('career_advice', sa.Text(), nullable=True),
    sa.Column('training_recommendations', sa.Text(), nullable=True),
    sa.Column('mentoring_suggestions', sa.Text(), nullable=True),
    sa.Column('promotion_readiness', sa.String(length=50), nullable=True),
    sa.Column('potential_rating', sa.String(length=50), nullable=True),
    sa.Column('retention_risk', sa.String(length=50), nullable=True),
    sa.Column('custom_questions_responses', sa.Text(), nullable=True),
    sa.Column('feedback_quality', sa.String(length=50), nullable=True),
    sa.Column('is_anonymous', sa.Boolean(), nullable=True),
    sa.Column('is_confidential', sa.Boolean(), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('submission_date', sa.DateTime(), nullable=True),
    sa.Column('review_date', sa.DateTime(), nullable=True),
    sa.Column('shared_with_employee', sa.Boolean(), nullable=True),
    sa.Column('share_date', sa.DateTime(), nullable=True),
    sa.Column('employee_response', sa.Text(), nullable=True),
    sa.Column('employee_response_date', sa.DateTime(), nullable=True),
    sa.Column('employee_acknowledgment', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['cycle_id'], ['performance_review_cycles.cycle_id'], ),
    sa.PrimaryKeyConstraint('feedback_id')
    )
    op.create_index(op.f('ix_performance_feedback_employee_id'), 'performance_feedback', ['employee_id'], unique=False)
    op.create_index(op.f('ix_performance_feedback_feedback_provider_id'), 'performance_feedback', ['feedback_provider_id'], unique=False)
    op.create_table('performance_goals',
    sa.Column('goal_id', sa.UUID(), nullable=False),
    sa.Column('employee_id', sa.UUID(), nullable=False),
    sa.Column('cycle_id', sa.UUID(), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('goal_category', sa.String(length=100), nullable=True),
    sa.Column('goal_type', sa.String(length=100), nullable=True),
    sa.Column('specific_description', sa.Text(), nullable=True),
    sa.Column('measurable_criteria', sa.Text(), nullable=True),
    sa.Column('achievable_rationale', sa.Text(), nullable=True),
    sa.Column('relevant_justification', sa.Text(), nullable=True),
    sa.Column('time_bound_deadline', sa.Date(), nullable=True),
    sa.Column('target_value', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('current_value', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('unit_of_measure', sa.String(length=100), nullable=True),
    sa.Column('completion_percentage', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('weight', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('priority', sa.String(length=50), nullable=True),
    sa.Column('difficulty_level', sa.String(length=50), nullable=True),
    sa.Column('start_date', sa.Date(), nullable=True),
    sa.Column('target_completion_date', sa.Date(), nullable=False),
    sa.Column('actual_completion_date', sa.Date(), nullable=True),
    sa.Column('milestones', sa.Text(), nullable=True),
    sa.Column('next_review_date', sa.Date(), nullable=True),
    sa.Column('review_frequency', sa.String(length=50), nullable=True),
    sa.Column('parent_goal_id', sa.UUID(), nullable=True),
    sa.Column('aligned_company_objective', sa.String(length=255), nullable=True),
    sa.Column('department_alignment', sa.String(length=255), nullable=True),
    sa.Column('required_resources', sa.Text(), nullable=True),
    sa.Column('support_needed', sa.Text(), nullable=True),
    sa.Column('potential_obstacles', sa.Text(), nullable=True),
    sa.Column('success_factors', sa.Text(), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('approval_status', sa.String(length=50), nullable=True),
    sa.Column('approved_by', sa.UUID(), nullable=True),
    sa.Column('approval_date', sa.DateTime(), nullable=True),
    sa.Column('approval_comments', sa.Text(), nullable=True),
    sa.Column('performance_impact', sa.String(length=50), nullable=True),
    sa.Column('business_impact', sa.Text(), nullable=True),
    sa.Column('skill_development_areas', sa.Text(), nullable=True),
    sa.Column('last_update_date', sa.Date(), nullable=True),
    sa.Column('update_frequency', sa.String(length=50), nullable=True),
    sa.Column('progress_notes', sa.Text(), nullable=True),
    sa.Column('created_by', sa.UUID(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('completed_by', sa.UUID(), nullable=True),
    sa.ForeignKeyConstraint(['cycle_id'], ['performance_review_cycles.cycle_id'], ),
    sa.PrimaryKeyConstraint('goal_id')
    )
    op.create_index(op.f('ix_performance_goals_employee_id'), 'performance_goals', ['employee_id'], unique=False)
    op.create_table('performance_reviews',
    sa.Column('review_id', sa.UUID(), nullable=False),
    sa.Column('cycle_id', sa.UUID(), nullable=False),
    sa.Column('employee_id', sa.UUID(), nullable=False),
    sa.Column('reviewer_id', sa.UUID(), nullable=False),
    sa.Column('review_type', sa.String(length=100), nullable=False),
    sa.Column('review_relationship', sa.String(length=100), nullable=True),
    sa.Column('assigned_date', sa.DateTime(), nullable=True),
    sa.Column('due_date', sa.Date(), nullable=True),
    sa.Column('started_date', sa.DateTime(), nullable=True),
    sa.Column('submitted_date', sa.DateTime(), nullable=True),
    sa.Column('completed_date', sa.DateTime(), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('completion_percentage', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('overall_rating', sa.Numeric(precision=3, scale=2), nullable=True),
    sa.Column('overall_rating_justification', sa.Text(), nullable=True),
    sa.Column('job_knowledge_rating', sa.Numeric(precision=3, scale=2), nullable=True),
    sa.Column('job_knowledge_comments', sa.Text(), nullable=True),
    sa.Column('quality_of_work_rating', sa.Numeric(precision=3, scale=2), nullable=True),
    sa.Column('quality_of_work_comments', sa.Text(), nullable=True),
    sa.Column('productivity_rating', sa.Numeric(precision=3, scale=2), nullable=True),
    sa.Column('productivity_comments', sa.Text(), nullable=True),
    sa.Column('communication_rating', sa.Numeric(precision=3, scale=2), nullable=True),
    sa.Column('communication_comments', sa.Text(), nullable=True),
    sa.Column('teamwork_rating', sa.Numeric(precision=3, scale=2), nullable=True),
    sa.Column('teamwork_comments', sa.Text(), nullable=True),
    sa.Column('leadership_rating', sa.Numeric(precision=3, scale=2), nullable=True),
    sa.Column('leadership_comments', sa.Text(), nullable=True),
    sa.Column('initiative_rating', sa.Numeric(precision=3, scale=2), nullable=True),
    sa.Column('initiative_comments', sa.Text(), nullable=True),
    sa.Column('problem_solving_rating', sa.Numeric(precision=3, scale=2), nullable=True),
    sa.Column('problem_solving_comments', sa.Text(), nullable=True),
    sa.Column('goals_achievement_rating', sa.Numeric(precision=3, scale=2), nullable=True),
    sa.Column('goals_achievement_comments', sa.Text(), nullable=True),
    sa.Column('competency_ratings', sa.Text(), nullable=True),
    sa.Column('strengths', sa.Text(), nullable=True),
    sa.Column('areas_for_improvement', sa.Text(), nullable=True),
    sa.Column('achievements', sa.Text(), nullable=True),
    sa.Column('challenges_faced', sa.Text(), nullable=True),
    sa.Column('development_needs', sa.Text(), nullable=True),
    sa.Column('career_aspirations', sa.Text(), nullable=True),
    sa.Column('training_recommendations', sa.Text(), nullable=True),
    sa.Column('mentoring_needs', sa.Text(), nullable=True),
    sa.Column('short_term_goals', sa.Text(), nullable=True),
    sa.Column('long_term_goals', sa.Text(), nullable=True),
    sa.Column('support_needed', sa.Text(), nullable=True),
    sa.Column('promotion_readiness', sa.String(length=50), nullable=True),
    sa.Column('retention_risk', sa.String(length=50), nullable=True),
    sa.Column('succession_potential', sa.String(length=50), nullable=True),
    sa.Column('requires_pip', sa.Boolean(), nullable=True),
    sa.Column('pip_reason', sa.Text(), nullable=True),
    sa.Column('calibrated', sa.Boolean(), nullable=True),
    sa.Column('calibration_notes', sa.Text(), nullable=True),
    sa.Column('calibrated_by', sa.UUID(), nullable=True),
    sa.Column('calibration_date', sa.DateTime(), nullable=True),
    sa.Column('requires_approval', sa.Boolean(), nullable=True),
    sa.Column('approved', sa.Boolean(), nullable=True),
    sa.Column('approved_by', sa.UUID(), nullable=True),
    sa.Column('approval_date', sa.DateTime(), nullable=True),
    sa.Column('approval_comments', sa.Text(), nullable=True),
    sa.Column('employee_acknowledged', sa.Boolean(), nullable=True),
    sa.Column('acknowledgment_date', sa.DateTime(), nullable=True),
    sa.Column('employee_comments', sa.Text(), nullable=True),
    sa.Column('employee_agreement', sa.String(length=50), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('custom_fields', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['cycle_id'], ['performance_review_cycles.cycle_id'], ),
    sa.PrimaryKeyConstraint('review_id')
    )
    op.create_index(op.f('ix_performance_reviews_employee_id'), 'performance_reviews', ['employee_id'], unique=False)
    op.create_index(op.f('ix_performance_reviews_reviewer_id'), 'performance_reviews', ['reviewer_id'], unique=False)
    op.create_table('requisition_approvals',
    sa.Column('approval_id', sa.UUID(), nullable=False),
    sa.Column('requisition_id', sa.UUID(), nullable=False),
    sa.Column('approval_level', sa.Integer(), nullable=False),
    sa.Column('approver_id', sa.UUID(), nullable=False),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('approved_date', sa.DateTime(), nullable=True),
    sa.Column('comments', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['requisition_id'], ['job_requisitions.requisition_id'], ),
    sa.PrimaryKeyConstraint('approval_id')
    )
    op.create_table('development_actions',
    sa.Column('action_id', sa.UUID(), nullable=False),
    sa.Column('plan_id', sa.UUID(), nullable=False),
    sa.Column('action_title', sa.String(length=255), nullable=False),
    sa.Column('action_description', sa.Text(), nullable=True),
    sa.Column('action_type', sa.String(length=100), nullable=False),
    sa.Column('action_category', sa.String(length=100), nullable=True),
    sa.Column('start_date', sa.Date(), nullable=True),
    sa.Column('target_completion_date', sa.Date(), nullable=False),
    sa.Column('actual_completion_date', sa.Date(), nullable=True),
    sa.Column('provider', sa.String(length=255), nullable=True),
    sa.Column('cost', sa.Numeric(precision=10, scale=2), nullable=True),
    sa.Column('time_required', sa.String(length=100), nullable=True),
    sa.Column('prerequisites', sa.Text(), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('progress_percentage', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('completion_evidence', sa.Text(), nullable=True),
    sa.Column('weight', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('priority', sa.String(length=50), nullable=True),
    sa.Column('skills_targeted', sa.Text(), nullable=True),
    sa.Column('competencies_addressed', sa.Text(), nullable=True),
    sa.Column('success_criteria', sa.Text(), nullable=True),
    sa.Column('actual_outcomes', sa.Text(), nullable=True),
    sa.Column('requires_manager_approval', sa.Boolean(), nullable=True),
    sa.Column('manager_approved', sa.Boolean(), nullable=True),
    sa.Column('approval_date', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['plan_id'], ['development_plans.plan_id'], ),
    sa.PrimaryKeyConstraint('action_id')
    )
    op.create_table('employee_allowances',
    sa.Column('employee_allowance_id', sa.UUID(), nullable=False),
    sa.Column('employee_id', sa.UUID(), nullable=False),
    sa.Column('allowance_type_id', sa.UUID(), nullable=False),
    sa.Column('amount', sa.Numeric(precision=15, scale=2), nullable=False),
    sa.Column('percentage', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('effective_from', sa.Date(), nullable=False),
    sa.Column('effective_to', sa.Date(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['allowance_type_id'], ['allowance_types.allowance_type_id'], ),
    sa.ForeignKeyConstraint(['employee_id'], ['employees.employee_id'], ),
    sa.PrimaryKeyConstraint('employee_allowance_id')
    )
    op.create_index('idx_employee_allowance_active', 'employee_allowances', ['employee_id', 'allowance_type_id', 'effective_from', 'effective_to'], unique=False)
    op.create_table('employee_loans',
    sa.Column('loan_id', sa.UUID(), nullable=False),
    sa.Column('employee_id', sa.UUID(), nullable=False),
    sa.Column('loan_type_id', sa.UUID(), nullable=False),
    sa.Column('loan_number', sa.String(length=50), nullable=False),
    sa.Column('principal_amount', sa.Numeric(precision=15, scale=2), nullable=False),
    sa.Column('interest_rate', sa.Numeric(precision=5, scale=4), nullable=False),
    sa.Column('total_interest', sa.Numeric(precision=15, scale=2), nullable=False),
    sa.Column('total_amount', sa.Numeric(precision=15, scale=2), nullable=False),
    sa.Column('term_months', sa.Integer(), nullable=False),
    sa.Column('monthly_deduction', sa.Numeric(precision=15, scale=2), nullable=False),
    sa.Column('amount_disbursed', sa.Numeric(precision=15, scale=2), nullable=False),
    sa.Column('amount_repaid', sa.Numeric(precision=15, scale=2), nullable=False),
    sa.Column('outstanding_balance', sa.Numeric(precision=15, scale=2), nullable=False),
    sa.Column('application_date', sa.Date(), nullable=False),
    sa.Column('approval_date', sa.Date(), nullable=True),
    sa.Column('disbursement_date', sa.Date(), nullable=True),
    sa.Column('first_deduction_date', sa.Date(), nullable=True),
    sa.Column('expected_completion_date', sa.Date(), nullable=True),
    sa.Column('actual_completion_date', sa.Date(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('approved_by', sa.UUID(), nullable=True),
    sa.Column('rejected_reason', sa.Text(), nullable=True),
    sa.Column('purpose', sa.Text(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.CheckConstraint('monthly_deduction > 0', name='chk_positive_monthly_deduction'),
    sa.CheckConstraint('principal_amount > 0', name='chk_positive_principal'),
    sa.CheckConstraint('term_months > 0', name='chk_positive_term'),
    sa.CheckConstraint('total_amount >= principal_amount', name='chk_total_amount'),
    sa.ForeignKeyConstraint(['employee_id'], ['employees.employee_id'], ),
    sa.ForeignKeyConstraint(['loan_type_id'], ['loan_types.loan_type_id'], ),
    sa.PrimaryKeyConstraint('loan_id'),
    sa.UniqueConstraint('loan_number')
    )
    op.create_table('employee_salaries',
    sa.Column('salary_id', sa.UUID(), nullable=False),
    sa.Column('employee_id', sa.UUID(), nullable=False),
    sa.Column('employee_type_id', sa.UUID(), nullable=False),
    sa.Column('basic_salary', sa.Numeric(precision=15, scale=2), nullable=False),
    sa.Column('currency', sa.String(length=3), nullable=False),
    sa.Column('pay_frequency', sa.String(length=20), nullable=False),
    sa.Column('effective_from', sa.Date(), nullable=False),
    sa.Column('effective_to', sa.Date(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('calculation_scenario', sa.String(length=50), nullable=True),
    sa.Column('input_values', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('calculated_values', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['employee_id'], ['employees.employee_id'], ),
    sa.PrimaryKeyConstraint('salary_id')
    )
    op.create_index('idx_employee_salary_active', 'employee_salaries', ['employee_id', 'effective_from', 'effective_to'], unique=False)
    op.create_table('goal_progress_updates',
    sa.Column('update_id', sa.UUID(), nullable=False),
    sa.Column('goal_id', sa.UUID(), nullable=False),
    sa.Column('update_date', sa.Date(), nullable=False),
    sa.Column('update_type', sa.String(length=100), nullable=True),
    sa.Column('previous_value', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('new_value', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('value_change', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('completion_percentage', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('percentage_change', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('milestone_achieved', sa.String(length=255), nullable=True),
    sa.Column('challenges_faced', sa.Text(), nullable=True),
    sa.Column('support_needed', sa.Text(), nullable=True),
    sa.Column('next_steps', sa.Text(), nullable=True),
    sa.Column('evidence_description', sa.Text(), nullable=True),
    sa.Column('attachments', sa.Text(), nullable=True),
    sa.Column('requires_manager_review', sa.Boolean(), nullable=True),
    sa.Column('manager_reviewed', sa.Boolean(), nullable=True),
    sa.Column('manager_comments', sa.Text(), nullable=True),
    sa.Column('reviewed_by', sa.UUID(), nullable=True),
    sa.Column('review_date', sa.DateTime(), nullable=True),
    sa.Column('updated_by', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['goal_id'], ['performance_goals.goal_id'], ),
    sa.PrimaryKeyConstraint('update_id')
    )
    op.create_table('job_applications',
    sa.Column('application_id', sa.UUID(), nullable=False),
    sa.Column('posting_id', sa.UUID(), nullable=False),
    sa.Column('candidate_id', sa.UUID(), nullable=False),
    sa.Column('application_source', sa.String(length=100), nullable=True),
    sa.Column('referral_source', sa.String(length=255), nullable=True),
    sa.Column('cover_letter', sa.Text(), nullable=True),
    sa.Column('screening_responses', sa.Text(), nullable=True),
    sa.Column('custom_responses', sa.Text(), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('current_stage', sa.String(length=100), nullable=True),
    sa.Column('stage_order', sa.Integer(), nullable=True),
    sa.Column('overall_score', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('screening_score', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('interview_score', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('assessment_score', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('submitted_date', sa.DateTime(), nullable=True),
    sa.Column('screening_completed_date', sa.DateTime(), nullable=True),
    sa.Column('first_interview_date', sa.DateTime(), nullable=True),
    sa.Column('final_interview_date', sa.DateTime(), nullable=True),
    sa.Column('offer_date', sa.DateTime(), nullable=True),
    sa.Column('decision_date', sa.DateTime(), nullable=True),
    sa.Column('hiring_decision', sa.String(length=50), nullable=True),
    sa.Column('decision_reason', sa.Text(), nullable=True),
    sa.Column('decision_made_by', sa.UUID(), nullable=True),
    sa.Column('offer_extended', sa.Boolean(), nullable=True),
    sa.Column('offer_accepted', sa.Boolean(), nullable=True),
    sa.Column('offer_declined_reason', sa.Text(), nullable=True),
    sa.Column('last_communication_date', sa.DateTime(), nullable=True),
    sa.Column('next_action_date', sa.Date(), nullable=True),
    sa.Column('next_action_type', sa.String(length=100), nullable=True),
    sa.Column('recruiter_notes', sa.Text(), nullable=True),
    sa.Column('hiring_manager_notes', sa.Text(), nullable=True),
    sa.Column('rejection_feedback', sa.Text(), nullable=True),
    sa.Column('diversity_data', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['candidate_id'], ['candidates.candidate_id'], ),
    sa.ForeignKeyConstraint(['posting_id'], ['job_postings.posting_id'], ),
    sa.PrimaryKeyConstraint('application_id')
    )
    op.create_table('leave_audit_logs',
    sa.Column('audit_id', sa.UUID(), nullable=False),
    sa.Column('employee_id', sa.UUID(), nullable=False),
    sa.Column('action_type', sa.String(length=50), nullable=False),
    sa.Column('entity_type', sa.String(length=50), nullable=False),
    sa.Column('entity_id', sa.UUID(), nullable=True),
    sa.Column('old_value', sa.JSON(), nullable=True),
    sa.Column('new_value', sa.JSON(), nullable=True),
    sa.Column('reason', sa.Text(), nullable=True),
    sa.Column('performed_by', sa.String(length=36), nullable=False),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('user_agent', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['employee_id'], ['employees.employee_id'], ),
    sa.PrimaryKeyConstraint('audit_id')
    )
    op.create_index('idx_audit_action_type', 'leave_audit_logs', ['action_type'], unique=False)
    op.create_index('idx_audit_employee_date', 'leave_audit_logs', ['employee_id', 'created_at'], unique=False)
    op.create_index('idx_audit_entity', 'leave_audit_logs', ['entity_type', 'entity_id'], unique=False)
    op.create_index('idx_audit_performed_by', 'leave_audit_logs', ['performed_by'], unique=False)
    op.create_table('onboarding_assets',
    sa.Column('assignment_id', sa.UUID(), nullable=False),
    sa.Column('instance_id', sa.UUID(), nullable=False),
    sa.Column('asset_type', sa.String(length=100), nullable=False),
    sa.Column('asset_category', sa.String(length=100), nullable=True),
    sa.Column('asset_name', sa.String(length=255), nullable=False),
    sa.Column('asset_description', sa.Text(), nullable=True),
    sa.Column('asset_serial_number', sa.String(length=100), nullable=True),
    sa.Column('asset_model', sa.String(length=100), nullable=True),
    sa.Column('asset_brand', sa.String(length=100), nullable=True),
    sa.Column('asset_tag', sa.String(length=100), nullable=True),
    sa.Column('barcode', sa.String(length=100), nullable=True),
    sa.Column('qr_code', sa.String(length=255), nullable=True),
    sa.Column('purchase_cost', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('current_value', sa.Numeric(precision=15, scale=2), nullable=True),
    sa.Column('depreciation_rate', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('assigned_date', sa.Date(), nullable=True),
    sa.Column('assigned_by', sa.UUID(), nullable=False),
    sa.Column('assignment_reason', sa.String(length=255), nullable=True),
    sa.Column('expected_return_date', sa.Date(), nullable=True),
    sa.Column('condition_at_assignment', sa.String(length=50), nullable=True),
    sa.Column('assignment_notes', sa.Text(), nullable=True),
    sa.Column('assignment_photos', sa.Text(), nullable=True),
    sa.Column('return_date', sa.Date(), nullable=True),
    sa.Column('returned_by', sa.UUID(), nullable=True),
    sa.Column('return_condition', sa.String(length=50), nullable=True),
    sa.Column('return_notes', sa.Text(), nullable=True),
    sa.Column('return_photos', sa.Text(), nullable=True),
    sa.Column('damage_assessment', sa.Text(), nullable=True),
    sa.Column('damage_cost', sa.Numeric(precision=10, scale=2), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('is_returnable', sa.Boolean(), nullable=True),
    sa.Column('requires_training', sa.Boolean(), nullable=True),
    sa.Column('training_completed', sa.Boolean(), nullable=True),
    sa.Column('training_date', sa.Date(), nullable=True),
    sa.Column('current_location', sa.String(length=255), nullable=True),
    sa.Column('assigned_location', sa.String(length=255), nullable=True),
    sa.Column('warranty_expiry_date', sa.Date(), nullable=True),
    sa.Column('last_maintenance_date', sa.Date(), nullable=True),
    sa.Column('next_maintenance_date', sa.Date(), nullable=True),
    sa.Column('maintenance_notes', sa.Text(), nullable=True),
    sa.Column('security_level', sa.String(length=50), nullable=True),
    sa.Column('requires_background_check', sa.Boolean(), nullable=True),
    sa.Column('background_check_completed', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['instance_id'], ['onboarding_instances.instance_id'], ),
    sa.PrimaryKeyConstraint('assignment_id')
    )
    op.create_table('onboarding_documents',
    sa.Column('document_id', sa.UUID(), nullable=False),
    sa.Column('instance_id', sa.UUID(), nullable=False),
    sa.Column('requirement_id', sa.UUID(), nullable=False),
    sa.Column('original_filename', sa.String(length=255), nullable=False),
    sa.Column('stored_filename', sa.String(length=255), nullable=False),
    sa.Column('file_path', sa.String(length=500), nullable=False),
    sa.Column('file_size_bytes', sa.BigInteger(), nullable=True),
    sa.Column('file_type', sa.String(length=50), nullable=True),
    sa.Column('mime_type', sa.String(length=100), nullable=True),
    sa.Column('upload_date', sa.DateTime(), nullable=True),
    sa.Column('uploaded_by', sa.UUID(), nullable=False),
    sa.Column('upload_ip_address', sa.String(length=45), nullable=True),
    sa.Column('upload_user_agent', sa.String(length=500), nullable=True),
    sa.Column('document_title', sa.String(length=255), nullable=True),
    sa.Column('document_description', sa.Text(), nullable=True),
    sa.Column('document_date', sa.Date(), nullable=True),
    sa.Column('expiry_date', sa.Date(), nullable=True),
    sa.Column('document_number', sa.String(length=100), nullable=True),
    sa.Column('issuing_authority', sa.String(length=255), nullable=True),
    sa.Column('verification_status', sa.String(length=50), nullable=True),
    sa.Column('verified_by', sa.UUID(), nullable=True),
    sa.Column('verification_date', sa.DateTime(), nullable=True),
    sa.Column('verification_notes', sa.Text(), nullable=True),
    sa.Column('rejection_reason', sa.Text(), nullable=True),
    sa.Column('is_sensitive', sa.Boolean(), nullable=True),
    sa.Column('access_level', sa.String(length=50), nullable=True),
    sa.Column('retention_period_years', sa.Integer(), nullable=True),
    sa.Column('version', sa.Integer(), nullable=True),
    sa.Column('replaces_document_id', sa.UUID(), nullable=True),
    sa.Column('is_current_version', sa.Boolean(), nullable=True),
    sa.Column('virus_scan_status', sa.String(length=50), nullable=True),
    sa.Column('virus_scan_date', sa.DateTime(), nullable=True),
    sa.Column('ocr_processed', sa.Boolean(), nullable=True),
    sa.Column('ocr_text', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['instance_id'], ['onboarding_instances.instance_id'], ),
    sa.ForeignKeyConstraint(['requirement_id'], ['onboarding_document_requirements.requirement_id'], ),
    sa.PrimaryKeyConstraint('document_id')
    )
    op.create_table('onboarding_feedback',
    sa.Column('feedback_id', sa.UUID(), nullable=False),
    sa.Column('instance_id', sa.UUID(), nullable=False),
    sa.Column('feedback_type', sa.String(length=100), nullable=False),
    sa.Column('feedback_stage', sa.String(length=100), nullable=True),
    sa.Column('feedback_category', sa.String(length=100), nullable=True),
    sa.Column('feedback_text', sa.Text(), nullable=False),
    sa.Column('feedback_title', sa.String(length=255), nullable=True),
    sa.Column('overall_rating', sa.Integer(), nullable=True),
    sa.Column('process_rating', sa.Integer(), nullable=True),
    sa.Column('communication_rating', sa.Integer(), nullable=True),
    sa.Column('support_rating', sa.Integer(), nullable=True),
    sa.Column('training_rating', sa.Integer(), nullable=True),
    sa.Column('what_went_well', sa.Text(), nullable=True),
    sa.Column('what_could_improve', sa.Text(), nullable=True),
    sa.Column('suggestions', sa.Text(), nullable=True),
    sa.Column('would_recommend', sa.Boolean(), nullable=True),
    sa.Column('structured_responses', sa.Text(), nullable=True),
    sa.Column('provided_by', sa.UUID(), nullable=False),
    sa.Column('provider_role', sa.String(length=100), nullable=True),
    sa.Column('is_anonymous', sa.Boolean(), nullable=True),
    sa.Column('feedback_date', sa.DateTime(), nullable=True),
    sa.Column('onboarding_day', sa.Integer(), nullable=True),
    sa.Column('requires_follow_up', sa.Boolean(), nullable=True),
    sa.Column('follow_up_assigned_to', sa.UUID(), nullable=True),
    sa.Column('follow_up_completed', sa.Boolean(), nullable=True),
    sa.Column('follow_up_date', sa.DateTime(), nullable=True),
    sa.Column('follow_up_notes', sa.Text(), nullable=True),
    sa.Column('sentiment_score', sa.Numeric(precision=3, scale=2), nullable=True),
    sa.Column('sentiment_label', sa.String(length=50), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('is_public', sa.Boolean(), nullable=True),
    sa.Column('visibility_level', sa.String(length=50), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['instance_id'], ['onboarding_instances.instance_id'], ),
    sa.PrimaryKeyConstraint('feedback_id')
    )
    op.create_table('onboarding_tasks',
    sa.Column('task_id', sa.UUID(), nullable=False),
    sa.Column('instance_id', sa.UUID(), nullable=False),
    sa.Column('task_template_id', sa.UUID(), nullable=False),
    sa.Column('task_name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('instructions', sa.Text(), nullable=True),
    sa.Column('completion_criteria', sa.Text(), nullable=True),
    sa.Column('assigned_to_role', sa.String(length=100), nullable=False),
    sa.Column('assigned_to_user', sa.UUID(), nullable=True),
    sa.Column('assigned_by', sa.UUID(), nullable=True),
    sa.Column('assigned_date', sa.DateTime(), nullable=True),
    sa.Column('due_date', sa.Date(), nullable=False),
    sa.Column('estimated_duration_hours', sa.Integer(), nullable=True),
    sa.Column('started_date', sa.DateTime(), nullable=True),
    sa.Column('completed_date', sa.DateTime(), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('completion_percentage', sa.Integer(), nullable=True),
    sa.Column('is_mandatory', sa.Boolean(), nullable=True),
    sa.Column('is_blocking', sa.Boolean(), nullable=True),
    sa.Column('task_type', sa.String(length=100), nullable=True),
    sa.Column('task_category', sa.String(length=100), nullable=True),
    sa.Column('task_order', sa.Integer(), nullable=True),
    sa.Column('completed_by', sa.UUID(), nullable=True),
    sa.Column('completion_notes', sa.Text(), nullable=True),
    sa.Column('completion_attachments', sa.Text(), nullable=True),
    sa.Column('verification_required', sa.Boolean(), nullable=True),
    sa.Column('verified_by', sa.UUID(), nullable=True),
    sa.Column('verification_date', sa.DateTime(), nullable=True),
    sa.Column('verification_notes', sa.Text(), nullable=True),
    sa.Column('reminder_sent', sa.Boolean(), nullable=True),
    sa.Column('reminder_sent_date', sa.DateTime(), nullable=True),
    sa.Column('escalation_sent', sa.Boolean(), nullable=True),
    sa.Column('escalation_sent_date', sa.DateTime(), nullable=True),
    sa.Column('external_system', sa.String(length=100), nullable=True),
    sa.Column('external_reference', sa.String(length=255), nullable=True),
    sa.Column('external_status', sa.String(length=100), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['instance_id'], ['onboarding_instances.instance_id'], ),
    sa.ForeignKeyConstraint(['task_template_id'], ['onboarding_workflow_tasks.task_template_id'], ),
    sa.PrimaryKeyConstraint('task_id')
    )
    op.create_table('payslips',
    sa.Column('payslip_id', sa.UUID(), nullable=False),
    sa.Column('run_id', sa.UUID(), nullable=False),
    sa.Column('employee_id', sa.UUID(), nullable=False),
    sa.Column('pay_period_start', sa.Date(), nullable=False),
    sa.Column('pay_period_end', sa.Date(), nullable=False),
    sa.Column('employee_type_id', sa.UUID(), nullable=False),
    sa.Column('basic_salary', sa.Numeric(precision=15, scale=2), nullable=False),
    sa.Column('allowances', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('gross_pay', sa.Numeric(precision=15, scale=2), nullable=False),
    sa.Column('tax_calculations', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('employee_deductions', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('employer_contributions', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('net_pay', sa.Numeric(precision=15, scale=2), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('payment_date', sa.Date(), nullable=True),
    sa.Column('payment_reference', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['employee_id'], ['employees.employee_id'], ),
    sa.ForeignKeyConstraint(['run_id'], ['payroll_runs.run_id'], ),
    sa.PrimaryKeyConstraint('payslip_id'),
    sa.UniqueConstraint('run_id', 'employee_id', name='uq_payslip_run_employee')
    )
    op.create_index('idx_payslip_employee_period', 'payslips', ['employee_id', 'pay_period_start', 'pay_period_end'], unique=False)
    op.create_table('posting_channels',
    sa.Column('channel_id', sa.UUID(), nullable=False),
    sa.Column('posting_id', sa.UUID(), nullable=False),
    sa.Column('channel_name', sa.String(length=255), nullable=False),
    sa.Column('channel_type', sa.String(length=100), nullable=True),
    sa.Column('external_posting_id', sa.String(length=255), nullable=True),
    sa.Column('posting_url', sa.String(length=500), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('posted_date', sa.DateTime(), nullable=True),
    sa.Column('removed_date', sa.DateTime(), nullable=True),
    sa.Column('views', sa.Integer(), nullable=True),
    sa.Column('applications', sa.Integer(), nullable=True),
    sa.Column('cost', sa.Numeric(precision=10, scale=2), nullable=True),
    sa.Column('api_response', sa.Text(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['posting_id'], ['job_postings.posting_id'], ),
    sa.PrimaryKeyConstraint('channel_id')
    )
    op.create_table('application_stage_history',
    sa.Column('stage_history_id', sa.UUID(), nullable=False),
    sa.Column('application_id', sa.UUID(), nullable=False),
    sa.Column('stage_name', sa.String(length=100), nullable=False),
    sa.Column('stage_order', sa.Integer(), nullable=False),
    sa.Column('entered_date', sa.DateTime(), nullable=False),
    sa.Column('completed_date', sa.DateTime(), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['application_id'], ['job_applications.application_id'], ),
    sa.PrimaryKeyConstraint('stage_history_id')
    )
    op.create_table('candidate_assessments',
    sa.Column('assessment_id', sa.UUID(), nullable=False),
    sa.Column('candidate_id', sa.UUID(), nullable=False),
    sa.Column('application_id', sa.UUID(), nullable=True),
    sa.Column('assessment_name', sa.String(length=255), nullable=False),
    sa.Column('assessment_type', sa.String(length=100), nullable=False),
    sa.Column('assessment_category', sa.String(length=100), nullable=True),
    sa.Column('provider', sa.String(length=255), nullable=True),
    sa.Column('platform_url', sa.String(length=500), nullable=True),
    sa.Column('external_assessment_id', sa.String(length=255), nullable=True),
    sa.Column('time_limit_minutes', sa.Integer(), nullable=True),
    sa.Column('max_attempts', sa.Integer(), nullable=True),
    sa.Column('passing_score', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('total_possible_score', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('instructions', sa.Text(), nullable=True),
    sa.Column('questions', sa.Text(), nullable=True),
    sa.Column('skills_tested', sa.Text(), nullable=True),
    sa.Column('assigned_date', sa.DateTime(), nullable=True),
    sa.Column('due_date', sa.DateTime(), nullable=True),
    sa.Column('access_link', sa.String(length=500), nullable=True),
    sa.Column('access_code', sa.String(length=100), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('started_at', sa.DateTime(), nullable=True),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.Column('time_spent_minutes', sa.Integer(), nullable=True),
    sa.Column('score', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('percentage_score', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('passed', sa.Boolean(), nullable=True),
    sa.Column('section_scores', sa.Text(), nullable=True),
    sa.Column('question_responses', sa.Text(), nullable=True),
    sa.Column('skill_scores', sa.Text(), nullable=True),
    sa.Column('strengths', sa.Text(), nullable=True),
    sa.Column('weaknesses', sa.Text(), nullable=True),
    sa.Column('recommendations', sa.Text(), nullable=True),
    sa.Column('detailed_report', sa.Text(), nullable=True),
    sa.Column('report_url', sa.String(length=500), nullable=True),
    sa.Column('proctored', sa.Boolean(), nullable=True),
    sa.Column('proctoring_notes', sa.Text(), nullable=True),
    sa.Column('security_flags', sa.Text(), nullable=True),
    sa.Column('attempt_number', sa.Integer(), nullable=True),
    sa.Column('previous_attempts', sa.Text(), nullable=True),
    sa.Column('created_by', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['application_id'], ['job_applications.application_id'], ),
    sa.ForeignKeyConstraint(['candidate_id'], ['candidates.candidate_id'], ),
    sa.PrimaryKeyConstraint('assessment_id')
    )
    op.create_table('candidate_documents',
    sa.Column('document_id', sa.UUID(), nullable=False),
    sa.Column('candidate_id', sa.UUID(), nullable=False),
    sa.Column('application_id', sa.UUID(), nullable=True),
    sa.Column('document_name', sa.String(length=255), nullable=False),
    sa.Column('document_type', sa.String(length=100), nullable=False),
    sa.Column('document_category', sa.String(length=100), nullable=True),
    sa.Column('original_filename', sa.String(length=255), nullable=False),
    sa.Column('file_path', sa.String(length=500), nullable=False),
    sa.Column('file_size', sa.BigInteger(), nullable=True),
    sa.Column('file_type', sa.String(length=100), nullable=True),
    sa.Column('file_extension', sa.String(length=10), nullable=True),
    sa.Column('storage_provider', sa.String(length=100), nullable=True),
    sa.Column('storage_bucket', sa.String(length=255), nullable=True),
    sa.Column('storage_key', sa.String(length=500), nullable=True),
    sa.Column('storage_url', sa.String(length=500), nullable=True),
    sa.Column('parsed_content', sa.Text(), nullable=True),
    sa.Column('parsed_data', sa.Text(), nullable=True),
    sa.Column('parsing_status', sa.String(length=50), nullable=True),
    sa.Column('parsing_error', sa.Text(), nullable=True),
    sa.Column('is_verified', sa.Boolean(), nullable=True),
    sa.Column('verification_status', sa.String(length=50), nullable=True),
    sa.Column('verification_notes', sa.Text(), nullable=True),
    sa.Column('verified_by', sa.UUID(), nullable=True),
    sa.Column('verified_date', sa.DateTime(), nullable=True),
    sa.Column('version', sa.Integer(), nullable=True),
    sa.Column('is_latest_version', sa.Boolean(), nullable=True),
    sa.Column('previous_version_id', sa.UUID(), nullable=True),
    sa.Column('is_confidential', sa.Boolean(), nullable=True),
    sa.Column('access_level', sa.String(length=50), nullable=True),
    sa.Column('password_protected', sa.Boolean(), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('expiry_date', sa.Date(), nullable=True),
    sa.Column('ai_analysis', sa.Text(), nullable=True),
    sa.Column('skill_extraction', sa.Text(), nullable=True),
    sa.Column('experience_analysis', sa.Text(), nullable=True),
    sa.Column('quality_score', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('gdpr_compliant', sa.Boolean(), nullable=True),
    sa.Column('retention_period_days', sa.Integer(), nullable=True),
    sa.Column('auto_delete_date', sa.Date(), nullable=True),
    sa.Column('uploaded_by', sa.UUID(), nullable=True),
    sa.Column('uploaded_at', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['application_id'], ['job_applications.application_id'], ),
    sa.ForeignKeyConstraint(['candidate_id'], ['candidates.candidate_id'], ),
    sa.PrimaryKeyConstraint('document_id')
    )
    op.create_table('interviews',
    sa.Column('interview_id', sa.UUID(), nullable=False),
    sa.Column('application_id', sa.UUID(), nullable=False),
    sa.Column('candidate_id', sa.UUID(), nullable=False),
    sa.Column('interview_type', sa.String(length=100), nullable=False),
    sa.Column('interview_round', sa.Integer(), nullable=True),
    sa.Column('interview_title', sa.String(length=255), nullable=True),
    sa.Column('interview_description', sa.Text(), nullable=True),
    sa.Column('scheduled_date', sa.DateTime(), nullable=False),
    sa.Column('duration_minutes', sa.Integer(), nullable=True),
    sa.Column('timezone', sa.String(length=100), nullable=True),
    sa.Column('location', sa.String(length=255), nullable=True),
    sa.Column('meeting_platform', sa.String(length=100), nullable=True),
    sa.Column('meeting_url', sa.String(length=500), nullable=True),
    sa.Column('meeting_id', sa.String(length=255), nullable=True),
    sa.Column('meeting_password', sa.String(length=100), nullable=True),
    sa.Column('dial_in_number', sa.String(length=50), nullable=True),
    sa.Column('calendar_event_id', sa.String(length=255), nullable=True),
    sa.Column('calendly_event_id', sa.String(length=255), nullable=True),
    sa.Column('primary_interviewer', sa.UUID(), nullable=False),
    sa.Column('interview_panel', sa.Text(), nullable=True),
    sa.Column('interview_template_id', sa.UUID(), nullable=True),
    sa.Column('questions', sa.Text(), nullable=True),
    sa.Column('evaluation_criteria', sa.Text(), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=True),
    sa.Column('attendance_status', sa.String(length=50), nullable=True),
    sa.Column('overall_rating', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('technical_rating', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('communication_rating', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('cultural_fit_rating', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('problem_solving_rating', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('strengths', sa.Text(), nullable=True),
    sa.Column('weaknesses', sa.Text(), nullable=True),
    sa.Column('detailed_feedback', sa.Text(), nullable=True),
    sa.Column('recommendation', sa.String(length=100), nullable=True),
    sa.Column('next_steps', sa.Text(), nullable=True),
    sa.Column('follow_up_required', sa.Boolean(), nullable=True),
    sa.Column('follow_up_date', sa.Date(), nullable=True),
    sa.Column('recording_url', sa.String(length=500), nullable=True),
    sa.Column('recording_available', sa.Boolean(), nullable=True),
    sa.Column('notes_document_url', sa.String(length=500), nullable=True),
    sa.Column('reminder_sent_candidate', sa.Boolean(), nullable=True),
    sa.Column('reminder_sent_interviewer', sa.Boolean(), nullable=True),
    sa.Column('confirmation_received', sa.Boolean(), nullable=True),
    sa.Column('created_by', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['application_id'], ['job_applications.application_id'], ),
    sa.ForeignKeyConstraint(['candidate_id'], ['candidates.candidate_id'], ),
    sa.PrimaryKeyConstraint('interview_id')
    )
    op.create_table('loan_repayment_schedule',
    sa.Column('schedule_id', sa.UUID(), nullable=False),
    sa.Column('loan_id', sa.UUID(), nullable=False),
    sa.Column('installment_number', sa.Integer(), nullable=False),
    sa.Column('due_date', sa.Date(), nullable=False),
    sa.Column('principal_amount', sa.Numeric(precision=15, scale=2), nullable=False),
    sa.Column('interest_amount', sa.Numeric(precision=15, scale=2), nullable=False),
    sa.Column('amount', sa.Numeric(precision=15, scale=2), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('amount_paid', sa.Numeric(precision=15, scale=2), nullable=False),
    sa.Column('payment_date', sa.Date(), nullable=True),
    sa.Column('days_overdue', sa.Integer(), nullable=False),
    sa.Column('late_fee', sa.Numeric(precision=15, scale=2), nullable=False),
    sa.Column('payslip_id', sa.UUID(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.CheckConstraint('amount > 0', name='chk_positive_amount'),
    sa.CheckConstraint('amount_paid >= 0', name='chk_non_negative_paid'),
    sa.CheckConstraint('days_overdue >= 0', name='chk_non_negative_overdue'),
    sa.ForeignKeyConstraint(['loan_id'], ['employee_loans.loan_id'], ),
    sa.PrimaryKeyConstraint('schedule_id'),
    sa.UniqueConstraint('loan_id', 'installment_number', name='uq_loan_installment')
    )
    op.create_table('payroll_deductions',
    sa.Column('deduction_id', sa.UUID(), nullable=False),
    sa.Column('payslip_id', sa.UUID(), nullable=False),
    sa.Column('deduction_type_id', sa.UUID(), nullable=False),
    sa.Column('deduction_name', sa.String(length=255), nullable=False),
    sa.Column('deduction_code', sa.String(length=50), nullable=False),
    sa.Column('calculation_base', sa.Numeric(precision=15, scale=2), nullable=False),
    sa.Column('employee_rate_applied', sa.Numeric(precision=5, scale=4), nullable=True),
    sa.Column('employer_rate_applied', sa.Numeric(precision=5, scale=4), nullable=True),
    sa.Column('employee_amount', sa.Numeric(precision=15, scale=2), nullable=False),
    sa.Column('employer_amount', sa.Numeric(precision=15, scale=2), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['payslip_id'], ['payslips.payslip_id'], ),
    sa.PrimaryKeyConstraint('deduction_id')
    )
    op.create_index('idx_payroll_deduction_payslip', 'payroll_deductions', ['payslip_id'], unique=False)
    op.create_index('idx_payroll_deduction_type', 'payroll_deductions', ['deduction_type_id'], unique=False)
    op.create_table('interview_feedback',
    sa.Column('feedback_id', sa.UUID(), nullable=False),
    sa.Column('interview_id', sa.UUID(), nullable=False),
    sa.Column('interviewer_id', sa.UUID(), nullable=False),
    sa.Column('overall_rating', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('technical_rating', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('communication_rating', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('cultural_fit_rating', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('problem_solving_rating', sa.Numeric(precision=5, scale=2), nullable=True),
    sa.Column('strengths', sa.Text(), nullable=True),
    sa.Column('weaknesses', sa.Text(), nullable=True),
    sa.Column('detailed_feedback', sa.Text(), nullable=True),
    sa.Column('recommendation', sa.String(length=100), nullable=True),
    sa.Column('submitted_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['interview_id'], ['interviews.interview_id'], ),
    sa.PrimaryKeyConstraint('feedback_id')
    )
    op.create_table('loan_transactions',
    sa.Column('transaction_id', sa.UUID(), nullable=False),
    sa.Column('loan_id', sa.UUID(), nullable=False),
    sa.Column('schedule_id', sa.UUID(), nullable=True),
    sa.Column('payslip_id', sa.UUID(), nullable=True),
    sa.Column('transaction_type', sa.String(length=20), nullable=False),
    sa.Column('amount', sa.Numeric(precision=15, scale=2), nullable=False),
    sa.Column('principal_portion', sa.Numeric(precision=15, scale=2), nullable=False),
    sa.Column('interest_portion', sa.Numeric(precision=15, scale=2), nullable=False),
    sa.Column('late_fee_portion', sa.Numeric(precision=15, scale=2), nullable=False),
    sa.Column('transaction_date', sa.Date(), nullable=False),
    sa.Column('reference_number', sa.String(length=100), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('processed_by', sa.UUID(), nullable=True),
    sa.Column('processing_method', sa.String(length=20), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.CheckConstraint('amount > 0', name='chk_positive_amount'),
    sa.CheckConstraint('interest_portion >= 0', name='chk_non_negative_interest'),
    sa.CheckConstraint('late_fee_portion >= 0', name='chk_non_negative_late_fee'),
    sa.CheckConstraint('principal_portion >= 0', name='chk_non_negative_principal'),
    sa.ForeignKeyConstraint(['loan_id'], ['employee_loans.loan_id'], ),
    sa.ForeignKeyConstraint(['schedule_id'], ['loan_repayment_schedule.schedule_id'], ),
    sa.PrimaryKeyConstraint('transaction_id')
    )
    # ### end Alembic commands ###
    """
    pass


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """
    op.drop_table('loan_transactions')
    op.drop_table('interview_feedback')
    op.drop_index('idx_payroll_deduction_type', table_name='payroll_deductions')
    op.drop_index('idx_payroll_deduction_payslip', table_name='payroll_deductions')
    op.drop_table('payroll_deductions')
    op.drop_table('loan_repayment_schedule')
    op.drop_table('interviews')
    op.drop_table('candidate_documents')
    op.drop_table('candidate_assessments')
    op.drop_table('application_stage_history')
    op.drop_table('posting_channels')
    op.drop_index('idx_payslip_employee_period', table_name='payslips')
    op.drop_table('payslips')
    op.drop_table('onboarding_tasks')
    op.drop_table('onboarding_feedback')
    op.drop_table('onboarding_documents')
    op.drop_table('onboarding_assets')
    op.drop_index('idx_audit_performed_by', table_name='leave_audit_logs')
    op.drop_index('idx_audit_entity', table_name='leave_audit_logs')
    op.drop_index('idx_audit_employee_date', table_name='leave_audit_logs')
    op.drop_index('idx_audit_action_type', table_name='leave_audit_logs')
    op.drop_table('leave_audit_logs')
    op.drop_table('job_applications')
    op.drop_table('goal_progress_updates')
    op.drop_index('idx_employee_salary_active', table_name='employee_salaries')
    op.drop_table('employee_salaries')
    op.drop_table('employee_loans')
    op.drop_index('idx_employee_allowance_active', table_name='employee_allowances')
    op.drop_table('employee_allowances')
    op.drop_table('development_actions')
    op.drop_table('requisition_approvals')
    op.drop_index(op.f('ix_performance_reviews_reviewer_id'), table_name='performance_reviews')
    op.drop_index(op.f('ix_performance_reviews_employee_id'), table_name='performance_reviews')
    op.drop_table('performance_reviews')
    op.drop_index(op.f('ix_performance_goals_employee_id'), table_name='performance_goals')
    op.drop_table('performance_goals')
    op.drop_index(op.f('ix_performance_feedback_feedback_provider_id'), table_name='performance_feedback')
    op.drop_index(op.f('ix_performance_feedback_employee_id'), table_name='performance_feedback')
    op.drop_table('performance_feedback')
    op.drop_table('onboarding_workflow_tasks')
    op.drop_index(op.f('ix_onboarding_instances_employee_id'), table_name='onboarding_instances')
    op.drop_table('onboarding_instances')
    op.drop_table('onboarding_document_requirements')
    op.drop_index(op.f('ix_job_postings_company_id'), table_name='job_postings')
    op.drop_table('job_postings')
    op.drop_index(op.f('ix_development_plans_employee_id'), table_name='development_plans')
    op.drop_table('development_plans')
    op.drop_table('competencies')
    op.drop_index(op.f('ix_performance_review_cycles_company_id'), table_name='performance_review_cycles')
    op.drop_table('performance_review_cycles')
    op.drop_index(op.f('ix_performance_rating_scales_company_id'), table_name='performance_rating_scales')
    op.drop_table('performance_rating_scales')
    op.drop_index('idx_payroll_run_company_period', table_name='payroll_runs')
    op.drop_table('payroll_runs')
    op.drop_index(op.f('ix_onboarding_workflows_company_id'), table_name='onboarding_workflows')
    op.drop_table('onboarding_workflows')
    op.drop_table('loan_types')
    op.drop_index(op.f('ix_job_requisitions_company_id'), table_name='job_requisitions')
    op.drop_table('job_requisitions')
    op.drop_index(op.f('ix_competency_frameworks_company_id'), table_name='competency_frameworks')
    op.drop_table('competency_frameworks')
    op.drop_index('idx_company_payroll_settings_active', table_name='company_payroll_settings')
    op.drop_table('company_payroll_settings')
    op.drop_index(op.f('ix_candidates_email'), table_name='candidates')
    op.drop_index(op.f('ix_candidates_company_id'), table_name='candidates')
    op.drop_table('candidates')
    op.drop_table('allowance_types')
    # ### end Alembic commands ###
    """
    pass