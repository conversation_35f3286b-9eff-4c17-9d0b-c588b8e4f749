"""added shift models

Revision ID: cff19cda2ce1
Revises: b66e5abf7df3
Create Date: 2025-07-17 20:35:58.835246

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'cff19cda2ce1'
down_revision = 'b66e5abf7df3'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """
    op.create_table('shift_templates',
    sa.Column('template_id', sa.UUID(), nullable=False),
    sa.Column('company_id', sa.String(length=36), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('pattern_type', sa.String(length=50), nullable=False),
    sa.Column('rotation_cycle', sa.Integer(), nullable=False),
    sa.Column('pattern_data', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('department_ids', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('employee_group_size', sa.Integer(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('created_by', sa.UUID(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('template_id')
    )
    op.create_table('shift_schedules',
    sa.Column('schedule_id', sa.UUID(), nullable=False),
    sa.Column('company_id', sa.String(length=36), nullable=False),
    sa.Column('department_id', sa.UUID(), nullable=True),
    sa.Column('template_id', sa.UUID(), nullable=True),
    sa.Column('schedule_name', sa.String(length=255), nullable=False),
    sa.Column('start_date', sa.Date(), nullable=False),
    sa.Column('end_date', sa.Date(), nullable=False),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('issues', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('coverage_analysis', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('generated_by', sa.UUID(), nullable=True),
    sa.Column('reviewed_by', sa.UUID(), nullable=True),
    sa.Column('published_by', sa.UUID(), nullable=True),
    sa.Column('published_at', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['template_id'], ['shift_templates.template_id'], ),
    sa.PrimaryKeyConstraint('schedule_id')
    )
    op.create_table('schedule_assignments',
    sa.Column('assignment_id', sa.UUID(), nullable=False),
    sa.Column('schedule_id', sa.UUID(), nullable=False),
    sa.Column('employee_id', sa.UUID(), nullable=False),
    sa.Column('date', sa.Date(), nullable=False),
    sa.Column('shift_id', sa.UUID(), nullable=False),
    sa.Column('assignment_type', sa.String(length=50), nullable=False),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['schedule_id'], ['shift_schedules.schedule_id'], ),
    sa.PrimaryKeyConstraint('assignment_id')
    )
    # ### end Alembic commands ###
    """
    pass


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """
    op.drop_table('schedule_assignments')
    op.drop_table('shift_schedules')
    op.drop_table('shift_templates')
    # ### end Alembic commands ###
    """
    pass
