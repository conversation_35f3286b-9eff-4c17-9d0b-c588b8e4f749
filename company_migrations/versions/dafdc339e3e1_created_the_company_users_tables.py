"""created the company_users tables

Revision ID: dafdc339e3e1
Revises: 0084f2aae995
Create Date: 2025-05-17 12:25:11.485806

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'dafdc339e3e1'
down_revision = '0084f2aae995'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """
    op.create_table('company_users',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('username', sa.String(length=255), nullable=False),
    sa.Column('email', sa.String(length=255), nullable=True),
    sa.Column('first_name', sa.String(length=255), nullable=True),
    sa.Column('last_name', sa.String(length=255), nullable=False),
    sa.Column('password', sa.String(length=255), nullable=False),
    sa.Column('salt', sa.String(length=255), nullable=False),
    sa.Column('role', sa.String(length=50), nullable=False),
    sa.Column('employee_id', sa.UUID(), nullable=True),
    sa.Column('phone_number', sa.String(length=20), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['employee_id'], ['employees.employee_id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('email'),
    sa.UniqueConstraint('username')
    )
    op.create_foreign_key(None, 'attendance', 'employees', ['employee_id'], ['employee_id'])
    op.create_foreign_key(None, 'employees', 'departments', ['department_id'], ['department_id'])
    # ### end Alembic commands ###
    """
    pass


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    """
    op.drop_constraint(None, 'employees', type_='foreignkey')
    op.drop_constraint(None, 'attendance', type_='foreignkey')
    op.drop_table('company_users')
    # ### end Alembic commands ###
    """
    pass
