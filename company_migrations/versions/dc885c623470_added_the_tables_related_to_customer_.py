"""added the tables related to customer loyalities

Revision ID: dc885c623470
Revises: 9f4a926a7d03
Create Date: 2025-10-01 20:29:31.473781

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'dc885c623470'
down_revision = '9f4a926a7d03'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### CORRECTED: Fixed table creation order and circular foreign key dependencies ###

    # STEP 1: Create base tables with no dependencies
    # ================================================

    # Table 1: customers (no dependencies)
    op.create_table('customers',
    sa.Column('customer_id', sa.UUID(), nullable=False),
    sa.Column('first_name', sa.String(length=255), nullable=False),
    sa.Column('last_name', sa.String(length=255), nullable=False),
    sa.Column('email', sa.String(length=255), nullable=True),
    sa.Column('phone_number', sa.String(length=20), nullable=True),
    sa.Column('membership_number', sa.String(length=50), nullable=True),
    sa.Column('customer_segment', sa.String(length=50), nullable=False),
    sa.Column('registration_date', sa.Date(), nullable=False),
    sa.Column('date_of_birth', sa.Date(), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('preferred_contact_method', sa.String(length=50), nullable=True),
    sa.Column('marketing_consent', sa.Boolean(), nullable=False),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('created_by', sa.UUID(), nullable=True),
    sa.PrimaryKeyConstraint('customer_id'),
    sa.UniqueConstraint('email'),
    sa.UniqueConstraint('membership_number'),
    sa.UniqueConstraint('phone_number')
    )

    # Table 2: promotion_rules (no dependencies)
    op.create_table('promotion_rules',
    sa.Column('rule_id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('rule_type', sa.String(length=50), nullable=False),
    sa.Column('trigger_value', sa.Integer(), nullable=False),
    sa.Column('trigger_period_days', sa.Integer(), nullable=True),
    sa.Column('reward_type', sa.String(length=50), nullable=False),
    sa.Column('reward_value', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('reward_description', sa.String(length=255), nullable=True),
    sa.Column('applicable_customer_segments', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('applicable_visit_types', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('valid_from', sa.Date(), nullable=True),
    sa.Column('valid_until', sa.Date(), nullable=True),
    sa.Column('max_redemptions_per_customer', sa.Integer(), nullable=True),
    sa.Column('reward_expiry_days', sa.Integer(), nullable=True),
    sa.Column('priority', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('created_by', sa.UUID(), nullable=True),
    sa.PrimaryKeyConstraint('rule_id')
    )

    # STEP 2: Create customer_loyalty_balances (depends on customers and promotion_rules)
    # ====================================================================================

    op.create_table('customer_loyalty_balances',
    sa.Column('balance_id', sa.UUID(), nullable=False),
    sa.Column('customer_id', sa.UUID(), nullable=False),
    sa.Column('rule_id', sa.UUID(), nullable=False),
    sa.Column('current_count', sa.Integer(), nullable=False),
    sa.Column('target_count', sa.Integer(), nullable=False),
    sa.Column('rewards_earned', sa.Integer(), nullable=False),
    sa.Column('rewards_redeemed', sa.Integer(), nullable=False),
    sa.Column('rewards_available', sa.Integer(), nullable=False),
    sa.Column('rewards_expired', sa.Integer(), nullable=False),
    sa.Column('period_start_date', sa.Date(), nullable=True),
    sa.Column('period_end_date', sa.Date(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('last_activity_date', sa.Date(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['customer_id'], ['customers.customer_id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['rule_id'], ['promotion_rules.rule_id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('balance_id'),
    sa.UniqueConstraint('customer_id', 'rule_id', name='uq_customer_rule')
    )

    # STEP 3: Create customer_visits WITHOUT the circular foreign key to reward_redemptions
    # ======================================================================================

    op.create_table('customer_visits',
    sa.Column('visit_id', sa.UUID(), nullable=False),
    sa.Column('customer_id', sa.UUID(), nullable=False),
    sa.Column('visit_date', sa.Date(), nullable=False),
    sa.Column('visit_time', sa.DateTime(), nullable=False),
    sa.Column('source', sa.String(length=50), nullable=False),
    sa.Column('source_record_id', sa.String(length=255), nullable=True),
    sa.Column('device_serial_num', sa.String(length=255), nullable=True),
    sa.Column('visit_type', sa.String(length=50), nullable=False),
    sa.Column('duration_minutes', sa.Integer(), nullable=True),
    sa.Column('is_loyalty_visit', sa.Boolean(), nullable=False),
    sa.Column('loyalty_points_earned', sa.Integer(), nullable=False),
    sa.Column('reward_redeemed', sa.Boolean(), nullable=False),
    sa.Column('redemption_id', sa.UUID(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['customer_id'], ['customers.customer_id'], ondelete='CASCADE'),
    # NOTE: Foreign key to reward_redemptions will be added AFTER that table is created
    sa.PrimaryKeyConstraint('visit_id')
    )

    # STEP 4: Create reward_redemptions WITHOUT the circular foreign key to customer_visits
    # ======================================================================================

    op.create_table('reward_redemptions',
    sa.Column('redemption_id', sa.UUID(), nullable=False),
    sa.Column('customer_id', sa.UUID(), nullable=False),
    sa.Column('rule_id', sa.UUID(), nullable=False),
    sa.Column('balance_id', sa.UUID(), nullable=True),
    sa.Column('visit_id', sa.UUID(), nullable=True),
    sa.Column('redemption_date', sa.Date(), nullable=False),
    sa.Column('redemption_time', sa.DateTime(), nullable=False),
    sa.Column('reward_type', sa.String(length=50), nullable=False),
    sa.Column('reward_value', sa.Numeric(precision=10, scale=2), nullable=False),
    sa.Column('reward_description', sa.String(length=255), nullable=True),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('redeemed_by', sa.UUID(), nullable=True),
    sa.Column('redemption_method', sa.String(length=50), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['balance_id'], ['customer_loyalty_balances.balance_id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['customer_id'], ['customers.customer_id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['rule_id'], ['promotion_rules.rule_id'], ondelete='CASCADE'),
    # NOTE: Foreign key to customer_visits will be added AFTER both tables exist
    sa.PrimaryKeyConstraint('redemption_id')
    )

    # STEP 5: Add circular foreign key constraints NOW that both tables exist
    # ========================================================================

    # Add FK from customer_visits.redemption_id -> reward_redemptions.redemption_id
    op.create_foreign_key(
        'fk_customer_visits_redemption_id',
        'customer_visits', 'reward_redemptions',
        ['redemption_id'], ['redemption_id'],
        ondelete='SET NULL'
    )

    # Add FK from reward_redemptions.visit_id -> customer_visits.visit_id
    op.create_foreign_key(
        'fk_reward_redemptions_visit_id',
        'reward_redemptions', 'customer_visits',
        ['visit_id'], ['visit_id'],
        ondelete='SET NULL'
    )

    # STEP 6: Create promotion_audit_logs (depends on customers)
    # ===========================================================

    op.create_table('promotion_audit_logs',
    sa.Column('log_id', sa.UUID(), nullable=False),
    sa.Column('customer_id', sa.UUID(), nullable=True),
    sa.Column('action_type', sa.String(length=50), nullable=False),
    sa.Column('entity_type', sa.String(length=50), nullable=False),
    sa.Column('entity_id', sa.UUID(), nullable=True),
    sa.Column('old_value', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('new_value', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('performed_by', sa.UUID(), nullable=True),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('user_agent', sa.String(length=255), nullable=True),
    sa.Column('timestamp', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['customer_id'], ['customers.customer_id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('log_id')
    )

    # STEP 7: Modify person table to add customer_id (depends on customers)
    # ======================================================================

    op.add_column('person', sa.Column('customer_id', sa.UUID(), nullable=True))
    op.create_unique_constraint('uq_person_customer_id', 'person', ['customer_id'])
    op.create_foreign_key(
        'fk_person_customer_id',
        'person', 'customers',
        ['customer_id'], ['customer_id'],
        ondelete='CASCADE'
    )

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### CORRECTED: Reverse order of upgrade, drop circular FKs first ###

    # STEP 1: Drop person table modifications
    op.drop_constraint('fk_person_customer_id', 'person', type_='foreignkey')
    op.drop_constraint('uq_person_customer_id', 'person', type_='unique')
    op.drop_column('person', 'customer_id')

    # STEP 2: Drop promotion_audit_logs
    op.drop_table('promotion_audit_logs')

    # STEP 3: Drop circular foreign key constraints
    op.drop_constraint('fk_reward_redemptions_visit_id', 'reward_redemptions', type_='foreignkey')
    op.drop_constraint('fk_customer_visits_redemption_id', 'customer_visits', type_='foreignkey')

    # STEP 4: Drop tables in reverse dependency order
    op.drop_table('reward_redemptions')
    op.drop_table('customer_visits')
    op.drop_table('customer_loyalty_balances')
    op.drop_table('promotion_rules')
    op.drop_table('customers')

    # ### end Alembic commands ###
