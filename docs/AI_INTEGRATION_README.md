# AI Integration for Attendance Management System

## Overview

This comprehensive AI integration adds intelligent insights and analytics to your attendance management system using Google Gemini Flash 2.0 and other AI providers. The system transforms raw attendance data into actionable business intelligence.

## Features

### 🤖 AI-Powered Insights
- **Daily Attendance Summaries**: Automated analysis of daily attendance patterns
- **Trend Analysis**: Weekly and monthly attendance trend identification
- **Risk Assessment**: Early warning system for attendance issues
- **Smart Recommendations**: Actionable suggestions for improvement

### 📢 Smart Announcements
- **Automated Announcements**: AI-generated company announcements based on attendance data
- **Policy Reminders**: Targeted reminders based on attendance patterns
- **Recognition Posts**: Celebrate achievements and positive trends

### 📊 Executive Intelligence
- **Strategic Summaries**: Executive-level insights for decision making
- **ROI Analysis**: Cost-benefit analysis of attendance initiatives
- **Predictive Analytics**: Forecast future attendance patterns

### 🔧 Advanced Features
- **Multi-Provider Support**: Gemini, OpenAI, Claude (extensible architecture)
- **Intelligent Caching**: Reduce API costs with smart caching
- **Custom Templates**: Create custom insight templates
- **Rate Limiting**: Built-in rate limiting and cost management
- **Analytics Dashboard**: Track AI usage, costs, and performance

## Architecture

### Core Components

1. **AI Models** (`application/Models/ai/`)
   - `AIProvider`: Manage different AI providers
   - `AIInsightTemplate`: Reusable prompt templates
   - `AIInsightRequest`: Track all AI requests
   - `AIInsightResponse`: Store generated insights
   - `AICompanyConfig`: Company-specific AI settings

2. **AI Services** (`application/Services/ai/`)
   - `AIInsightsOrchestrator`: Main orchestration service
   - `GeminiAIService`: Google Gemini integration
   - `AIServiceFactory`: Provider management
   - `PromptBuilder`: Dynamic prompt generation
   - `AICacheService`: Intelligent caching

3. **API Endpoints** (`application/Routes/ai/`)
   - `ai_insights_api`: Generate and retrieve insights
   - `ai_config_api`: Manage AI configuration
   - `ai_analytics_api`: Usage analytics and monitoring

## Installation & Setup

### 1. Environment Variables

Add to your `.env` file:
```bash
# Google Gemini API Key (required)
GEMINI_API_KEY=your_gemini_api_key_here

# Optional: Other AI providers
OPENAI_API_KEY=your_openai_key_here
CLAUDE_API_KEY=your_claude_key_here
```

### 2. Database Migration

Run the migration script to add AI tables to existing company databases:

```bash
# Check current status
python scripts/migrate_ai_tables.py --all-companies --check-status

# Migrate all companies (dry run first)
python scripts/migrate_ai_tables.py --all-companies --dry-run

# Actually perform migration
python scripts/migrate_ai_tables.py --all-companies

# Migrate specific company
python scripts/migrate_ai_tables.py --company-id YOUR_COMPANY_ID
```

### 3. Initialize AI System

Set up default providers and templates:

```bash
# Initialize all companies (dry run first)
python scripts/initialize_ai_system.py --all-companies --dry-run

# Actually initialize
python scripts/initialize_ai_system.py --all-companies

# Initialize specific company
python scripts/initialize_ai_system.py --company-id YOUR_COMPANY_ID
```

### 4. Register AI Blueprints

Add to your main Flask app:

```python
from application.Routes.ai import ai_insights_api, ai_config_api, ai_analytics_api

app.register_blueprint(ai_insights_api)
app.register_blueprint(ai_config_api)
app.register_blueprint(ai_analytics_api)
```

## Usage

### Basic AI Insights

#### 1. Enhanced Daily Attendance (Existing Endpoint)

Add AI insights to existing attendance endpoint:

```bash
GET /api/attendance/daily?company_id=123&include_ai_insights=true
```

Response includes:
```json
{
  "status": "success",
  "summary": { ... },
  "ai_insights": {
    "content": "Your team's 87% attendance rate is 12% above industry average...",
    "summary": "Excellent attendance with room for Monday improvement",
    "recommendations": ["Consider flexible Monday start times", "..."],
    "alerts": ["Engineering team showing fatigue patterns"],
    "priority": "medium",
    "cached": false
  }
}
```

#### 2. Generate Custom Insights

```bash
POST /api/ai/insights/attendance
Content-Type: application/json

{
  "company_id": "123",
  "insight_type": "daily_summary",
  "data": {
    "company_name": "Your Company",
    "date": "2025-06-28",
    "attendance_data": { ... }
  }
}
```

#### 3. Smart Announcements

```bash
POST /api/ai/insights/announcements
Content-Type: application/json

{
  "company_id": "123",
  "announcement_type": "weekly_summary",
  "data": {
    "weekly_data": { ... }
  }
}
```

### Configuration Management

#### 1. Get AI Configuration

```bash
GET /api/ai/company-config?company_id=123
```

#### 2. Update AI Settings

```bash
PUT /api/ai/company-config
Content-Type: application/json

{
  "company_id": "123",
  "insight_frequency": "daily",
  "preferred_tone": "professional",
  "enable_ai_notifications": true,
  "daily_request_limit": 100
}
```

### Analytics & Monitoring

#### 1. Usage Statistics

```bash
GET /api/ai/analytics/usage-stats?company_id=123&period=30d
```

#### 2. Cost Analysis

```bash
GET /api/ai/analytics/cost-analysis?company_id=123&period=30d
```

#### 3. Insight Performance

```bash
GET /api/ai/analytics/insight-performance?company_id=123
```

## Pricing Strategy

### Free Tier (Gemini Flash 2.0)
- 1,500 requests/day
- Perfect for 10-15 small companies
- Ideal for proving value

### Scaling Strategy
```
Phase 1 (0-10 customers): Free Gemini tier
Phase 2 (10-50 customers): Gemini Pro ($20/month)
Phase 3 (50+ customers): Enterprise + funding
```

### Recommended Pricing
- **Basic Plan**: $400-600/month (attendance + basic AI)
- **AI Insights Plan**: +$200-500/month (advanced AI features)
- **Enterprise Plan**: +$1000+/month (custom AI, dedicated support)

## Customization

### Custom Templates

Create custom insight templates:

```bash
POST /api/ai/templates
Content-Type: application/json

{
  "company_id": "123",
  "name": "Custom Daily Report",
  "category": "attendance",
  "insight_type": "custom_daily",
  "prompt_template": "Analyze attendance for {company_name}...",
  "target_audience": "management"
}
```

### Custom Providers

Add new AI providers by extending `BaseAIService`:

```python
from application.Services.ai.base_ai_service import BaseAIService

class CustomAIService(BaseAIService):
    def generate_insight(self, prompt, **kwargs):
        # Your custom implementation
        pass
```

## Monitoring & Maintenance

### Health Checks

Monitor AI system health:
- API response times
- Success/failure rates
- Cost tracking
- Cache hit rates

### Maintenance Tasks

Set up periodic maintenance:

```bash
# Clean up expired cache (daily)
0 2 * * * python scripts/cleanup_ai_cache.py

# Generate usage reports (weekly)
0 9 * * 1 python scripts/ai_usage_report.py

# Update AI templates (monthly)
0 10 1 * * python scripts/update_ai_templates.py
```

## Security & Privacy

### Data Protection
- Automatic data anonymization
- Configurable data retention policies
- No sensitive data sent to AI providers
- Audit trail for all AI requests

### API Security
- Role-based access control
- Rate limiting per company
- Request validation and sanitization
- Secure API key management

## Troubleshooting

### Common Issues

1. **AI insights not generating**
   - Check GEMINI_API_KEY environment variable
   - Verify company AI configuration
   - Check rate limits and quotas

2. **Poor insight quality**
   - Review and update prompt templates
   - Adjust company context settings
   - Provide more detailed input data

3. **High API costs**
   - Enable caching
   - Adjust request frequency
   - Review and optimize prompts

### Debug Mode

Enable detailed logging:
```python
import logging
logging.getLogger('application.Services.ai').setLevel(logging.DEBUG)
```

## Future Enhancements

### Planned Features
- Multi-language support
- Voice-activated insights
- Integration with business intelligence tools
- Advanced predictive modeling
- Custom AI model training

### Roadmap
- Q1 2025: OpenAI integration
- Q2 2025: Advanced analytics dashboard
- Q3 2025: Mobile AI insights
- Q4 2025: Custom model training

## Support

For technical support or questions:
- Check the troubleshooting section
- Review API documentation
- Contact development team

---

**Note**: This AI integration is designed to be non-breaking. Existing functionality continues to work without any changes. AI features are opt-in and can be enabled gradually.
