# API Reference

Complete API documentation for KaziSync HRMS.

---

## Overview

The KaziSync HRMS API is a RESTful API that provides access to all system functionality. All API endpoints require authentication unless otherwise specified.

**Base URL:** `http://your-domain.com/api`

**Authentication:** JWT Bearer Token

**Content-Type:** `application/json`

---

## Authentication

### Login

**Endpoint:** `POST /api/auth/login`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGc...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGc...",
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "role": "admin"
  }
}
```

### Refresh Token

**Endpoint:** `POST /api/auth/refresh`

**Request Body:**
```json
{
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGc..."
}
```

---

## Command Management

Complete documentation for command management endpoints.

See [Command Management API Documentation](blueprints/command_management.md) for detailed information.

### Quick Reference

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/commands/failed` | GET | Get failed commands |
| `/api/commands/reset` | POST | Reset single command |
| `/api/commands/retry-all` | POST | Reset all failed commands |
| `/api/commands/<id>` | GET | Get command details |
| `/api/commands/statistics` | GET | Get command statistics |

---

## Employee Management

### Get Employees

**Endpoint:** `GET /api/employees`

**Query Parameters:**
- `company_id` (required): Company ID
- `page` (optional): Page number (default: 1)
- `per_page` (optional): Items per page (default: 10)
- `department_id` (optional): Filter by department
- `status` (optional): Filter by status (active/inactive)
- `search` (optional): Search term

**Response:**
```json
{
  "status": "success",
  "employees": [...],
  "pagination": {
    "total_count": 100,
    "page": 1,
    "per_page": 10,
    "pages": 10,
    "has_next": true,
    "has_prev": false
  }
}
```

### Create Employee

**Endpoint:** `POST /api/employees`

**Required Role:** `hr` or `admin`

**Request Body:**
```json
{
  "company_id": "uuid",
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "department_id": "uuid",
  "position": "Software Engineer",
  "hire_date": "2025-01-01"
}
```

### Update Employee

**Endpoint:** `PUT /api/employees/<employee_id>`

**Required Role:** `hr` or `admin`

### Delete Employee

**Endpoint:** `DELETE /api/employees/<employee_id>`

**Required Role:** `admin`

---

## Attendance Management

### Get Attendance Records

**Endpoint:** `GET /api/attendance`

**Query Parameters:**
- `company_id` (required): Company ID
- `employee_id` (optional): Filter by employee
- `date` (optional): Specific date (YYYY-MM-DD)
- `start_date` (optional): Date range start
- `end_date` (optional): Date range end
- `department_id` (optional): Filter by department
- `page` (optional): Page number
- `per_page` (optional): Items per page

---

## Device Management

### Get Devices

**Endpoint:** `GET /api/devices`

**Query Parameters:**
- `company_id` (required): Company ID

**Response:**
```json
{
  "status": "success",
  "devices": [
    {
      "id": 1,
      "device_sn": "AYTI10087992",
      "company_id": "uuid",
      "company_name": "Company Name",
      "status": 1,
      "created_at": "2025-01-01T00:00:00"
    }
  ]
}
```

### Register Device

**Endpoint:** `POST /api/devices`

**Required Role:** `admin`

**Request Body:**
```json
{
  "company_id": "uuid",
  "device_sn": "AYTI10087992"
}
```

---

## Department Management

### Get Departments

**Endpoint:** `GET /api/departments`

**Query Parameters:**
- `company_id` (required): Company ID

### Create Department

**Endpoint:** `POST /api/departments`

**Required Role:** `hr` or `admin`

**Request Body:**
```json
{
  "company_id": "uuid",
  "name": "Engineering",
  "description": "Engineering Department"
}
```

---

## Leave Management

### Get Leave Requests

**Endpoint:** `GET /api/leave/requests`

**Query Parameters:**
- `company_id` (required): Company ID
- `employee_id` (optional): Filter by employee
- `status` (optional): Filter by status (pending/approved/rejected)
- `start_date` (optional): Date range start
- `end_date` (optional): Date range end

### Create Leave Request

**Endpoint:** `POST /api/leave/requests`

**Request Body:**
```json
{
  "company_id": "uuid",
  "employee_id": "uuid",
  "leave_type_id": "uuid",
  "start_date": "2025-01-10",
  "end_date": "2025-01-15",
  "reason": "Vacation"
}
```

### Approve/Reject Leave Request

**Endpoint:** `PUT /api/leave/requests/<request_id>/status`

**Required Role:** `hr` or `admin`

**Request Body:**
```json
{
  "status": "approved",  // or "rejected"
  "comments": "Approved for vacation"
}
```

---

## Payroll Management

### Get Payroll Records

**Endpoint:** `GET /api/payroll/records`

**Query Parameters:**
- `company_id` (required): Company ID
- `employee_id` (optional): Filter by employee
- `month` (optional): Month (1-12)
- `year` (optional): Year (YYYY)

### Process Payroll

**Endpoint:** `POST /api/payroll/process`

**Required Role:** `admin`

**Request Body:**
```json
{
  "company_id": "uuid",
  "month": 1,
  "year": 2025,
  "employee_ids": ["uuid1", "uuid2"]  // optional, process all if not provided
}
```

---

## Shift Management

### Get Shifts

**Endpoint:** `GET /api/shifts`

**Query Parameters:**
- `company_id` (required): Company ID

### Assign Shift to Employee

**Endpoint:** `POST /api/shifts/assign`

**Required Role:** `hr` or `admin`

**Request Body:**
```json
{
  "company_id": "uuid",
  "employee_id": "uuid",
  "shift_id": "uuid",
  "start_date": "2025-01-01",
  "end_date": "2025-12-31"
}
```

---

## Approval Workflows

### Get Pending Approvals

**Endpoint:** `GET /api/approvals/pending`

**Query Parameters:**
- `company_id` (required): Company ID
- `approver_id` (optional): Filter by approver

### Approve/Reject Item

**Endpoint:** `POST /api/approvals/<approval_id>/action`

**Request Body:**
```json
{
  "action": "approve",  // or "reject"
  "comments": "Looks good"
}
```

---

## Company Management

### Get Company Details

**Endpoint:** `GET /api/companies/<company_id>`

**Required Role:** `admin`

### Update Company

**Endpoint:** `PUT /api/companies/<company_id>`

**Required Role:** `super-admin`

**Request Body:**
```json
{
  "company_name": "Updated Company Name",
  "email": "<EMAIL>",
  "phone": "+************"
}
```

---

## User Management

### Get Users

**Endpoint:** `GET /api/users`

**Required Role:** `admin`

**Query Parameters:**
- `company_id` (optional): Filter by company

### Create User

**Endpoint:** `POST /api/users`

**Required Role:** `admin`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "role": "hr",
  "company_id": "uuid"
}
```

---

## Analytics & Reports

### Get Dashboard Statistics

**Endpoint:** `GET /api/dashboard/statistics`

**Query Parameters:**
- `company_id` (required): Company ID

**Response:**
```json
{
  "status": "success",
  "statistics": {
    "total_employees": 150,
    "active_employees": 145,
    "present_today": 120,
    "on_leave": 10,
    "pending_approvals": 5
  }
}
```

### Generate Report

**Endpoint:** `POST /api/reports/generate`

**Request Body:**
```json
{
  "company_id": "uuid",
  "report_type": "attendance",  // attendance, payroll, leave
  "start_date": "2025-01-01",
  "end_date": "2025-01-31",
  "format": "pdf"  // pdf, excel, csv
}
```

---

## WebSocket API

### Device Connection

**Endpoint:** `ws://your-domain.com/websocket`

**Protocol:** WebSocket

**Connection:**
```javascript
const ws = new WebSocket('ws://your-domain.com/websocket');

ws.onopen = () => {
  // Send device registration
  ws.send(JSON.stringify({
    cmd: 'reg',
    sn: 'DEVICE_SERIAL_NUMBER'
  }));
};

ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  // Handle commands from server
};
```

**Commands:**
- `reg` - Register device
- `setuserinfo` - Set employee data on device
- `deluserinfo` - Delete employee from device
- `heartbeat` - Keep connection alive

---

## Error Handling

### Standard Error Response

```json
{
  "message": "Error description",
  "error": "Detailed error message",
  "status": "error"
}
```

### Common Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid parameters |
| 401 | Unauthorized - Invalid or missing token |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource not found |
| 409 | Conflict - Resource already exists |
| 422 | Unprocessable Entity - Validation error |
| 500 | Internal Server Error |

---

## Rate Limiting

- **Rate Limit:** 1000 requests per hour per user
- **Headers:**
  - `X-RateLimit-Limit`: Maximum requests allowed
  - `X-RateLimit-Remaining`: Remaining requests
  - `X-RateLimit-Reset`: Time when limit resets

---

## Pagination

All list endpoints support pagination:

**Query Parameters:**
- `page`: Page number (default: 1)
- `per_page`: Items per page (default: 10, max: 100)

**Response:**
```json
{
  "data": [...],
  "pagination": {
    "total_count": 100,
    "page": 1,
    "per_page": 10,
    "pages": 10,
    "has_next": true,
    "has_prev": false
  }
}
```

---

## Filtering & Sorting

### Filtering

Use query parameters to filter results:
```
GET /api/employees?status=active&department_id=uuid
```

### Sorting

Use `sort_by` and `order` parameters:
```
GET /api/employees?sort_by=hire_date&order=desc
```

---

## Best Practices

1. **Always include `company_id`** in requests for tenant-specific data
2. **Use pagination** for large datasets
3. **Handle errors gracefully** with proper error messages
4. **Implement retry logic** for failed requests
5. **Cache responses** where appropriate
6. **Use HTTPS** in production
7. **Rotate tokens regularly** for security
8. **Monitor rate limits** to avoid throttling

---

## Additional Resources

- [Command Management API](blueprints/command_management.md) - Detailed command management documentation
- [Troubleshooting Guide](TROUBLESHOOTING.md) - Common issues and solutions
- [Configuration Guide](CONFIGURATION.md) - System configuration options

