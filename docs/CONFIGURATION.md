# Configuration Guide

This guide covers system configuration options for KaziSync HRMS.

---

## Retry Mechanism Configuration

The system uses a retry mechanism for device commands that fail to receive acknowledgment from biometric devices.

### Current Settings

| Setting | Value | Location |
|---------|-------|----------|
| **Timeout** | 20 seconds | `application/job/SendOrderJob.py` line 89, 138 |
| **Max Retries** | 15 attempts | Multiple files (see below) |
| **Retry Interval** | Fixed 20 seconds | `application/job/SendOrderJob.py` |

### How It Works

1. **Command Sent**: System sends command to device via WebSocket
2. **Wait for Response**: System waits 20 seconds for device acknowledgment
3. **Retry on Timeout**: If no response, increment `err_count` and resend
4. **Max Retries**: After 15 failed attempts, command is abandoned
5. **Permanent Exclusion**: Commands with `err_count=15` are filtered out

### Files Containing Retry Configuration

#### 1. `application/Models/MachineCommand.py` (Line 179)

```python
def find_pending_command(session, send_status, serial):
    ret = session.query(MachineCommand).filter(
        MachineCommand.status == 0,
        MachineCommand.send_status == send_status,
        MachineCommand.serial == serial,
        MachineCommand.err_count != 15  # Max retry count
    ).all()
    return ret
```

**Purpose:** Excludes commands that have reached maximum retry count from being processed.

#### 2. `application/job/SendOrderJob.py` (Lines 90, 139)

```python
# First occurrence (line 90)
if difference > 20:  # 20 second timeout
    if pending_command[0].err_count < 15:  # Max retry count
        # Retry logic
        machine_command.err_count += 1
        # Resend command

# Second occurrence (line 139)
if time_diff > 20:  # 20 second timeout
    if pending_command[0].err_count < 15:  # Max retry count
        # Retry logic
```

**Purpose:** Checks if command should be retried based on timeout and retry count.

#### 3. `application/job/SendOrderJob.py` (Lines 124, 174)

```python
# Line 124
log_device_warning(f"Command failed after 15 attempts, giving up", key)

# Line 174
log_device_warning(f"Pending command failed after 15 attempts", key)
```

**Purpose:** Log messages when commands reach maximum retry count.

#### 4. `application/Routes/system_settings/command_management.py`

Multiple locations check for failed commands:

```python
# Line 90, 349 - Query filters
MachineCommand.err_count >= 15

# Line 222 - Validation
if command.err_count < 15:
    return jsonify({"note": "Only commands with err_count >= 15 need to be reset"})

# Line 486 - Status check
'is_failed': command.err_count >= 15

# Line 612 - Statistics
if cmd.err_count >= 15:
    # Count as failed
```

**Purpose:** API endpoints use this threshold to identify failed commands.

---

## Modifying Retry Settings

### Changing Maximum Retry Count

To change from 15 to a different value (e.g., 20):

1. **Update MachineCommand.py** (line 179):
   ```python
   MachineCommand.err_count != 20
   ```

2. **Update SendOrderJob.py** (lines 90, 139):
   ```python
   if pending_command[0].err_count < 20:
   ```

3. **Update SendOrderJob.py** (lines 124, 174):
   ```python
   log_device_warning(f"Command failed after 20 attempts, giving up", key)
   ```

4. **Update command_management.py** (lines 90, 222, 349, 486, 612):
   ```python
   # Change all occurrences of 15 to 20
   MachineCommand.err_count >= 20
   if command.err_count < 20:
   'is_failed': command.err_count >= 20
   ```

### Changing Timeout Duration

To change from 20 seconds to a different value (e.g., 30 seconds):

**File:** `application/job/SendOrderJob.py`

```python
# Line 89
if difference > 30:  # Changed from 20 to 30

# Line 138
if time_diff > 30:  # Changed from 20 to 30
```

### Implementing Exponential Backoff (Advanced)

For more sophisticated retry logic, implement exponential backoff:

**File:** `application/job/SendOrderJob.py` (line 89)

```python
# Replace fixed timeout with exponential backoff
# Timeout progression: 20s, 40s, 80s, 160s, 320s, ...
timeout = 20 * (2 ** pending_command[0].err_count)
if difference > timeout:
    if pending_command[0].err_count < 15:
        # Retry logic
```

**Benefits:**
- Reduces server load during issues
- Gives devices more time to recover
- Industry best practice

**Timeout Progression:**
- Attempt 1: 20 seconds
- Attempt 2: 40 seconds
- Attempt 3: 80 seconds (1.3 minutes)
- Attempt 4: 160 seconds (2.7 minutes)
- Attempt 5: 320 seconds (5.3 minutes)
- ...continues doubling

---

## Database Connection Configuration

### Multi-Tenant Database Pattern

The system uses a standard pattern for tenant database connections:

```python
# Get company_id from request
company_id = request.args.get('company_id')

# Validate company_id
if not company_id:
    return jsonify({"message": "Company ID is required"}), 400

# Get database name
database_name = Company.get_database_given_company_id(company_id)
if not database_name:
    return jsonify({"message": f"Company not found"}), 404

# Use context manager for session
from app import db_connection
with db_connection.get_session(database_name) as session:
    # Your database operations
```

### Alternative: Device-Based Lookup

When you have `device_sn` instead of `company_id`:

```python
device_sn = request.args.get('device_sn')
database_name = CompanyDevice.get_database_name_by_sn(device_sn)
```

---

## WebSocket Configuration

### Connection Settings

**File:** `app.py`

WebSocket endpoint for device communication:
```python
@sock.route('/websocket')
def websocket(sock):
    # WebSocket handler
```

### Device Status Tracking

Devices are tracked in the `ws_device` dictionary:
```python
from application.web_socket.WebSocketPool import ws_device

# Check if device is connected
device_status = ws_device.get(device_sn)
is_connected = device_status is not None
```

---

## Logging Configuration

### Log Levels

Configure in `app.py`:

```python
import logging

# Set log level
app.logger.setLevel(logging.INFO)  # Options: DEBUG, INFO, WARNING, ERROR, CRITICAL
```

### Log Files

- **Application logs:** `app.log`
- **Device logs:** `logs/device.log`

### Device Logger

**File:** `application/Helpers/device_logger.py`

```python
from application.Helpers.device_logger import device_logger

# Log device activity
device_logger.log_device_status(device_sn, "Device connected")
device_logger.log_websocket_activity(device_sn, "command_sent", "Details")
```

---

## Security Configuration

### JWT Token Settings

**File:** `app.py`

```python
app.config['JWT_SECRET_KEY'] = 'your-secret-key'
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=1)
app.config['JWT_REFRESH_TOKEN_EXPIRES'] = timedelta(days=30)
```

### Role-Based Access Control

Endpoints can require specific roles:

```python
from application.decorators.role_required import roles_required

@app.route('/api/admin/endpoint')
@token_required
@roles_required('super-admin')
def admin_endpoint():
    # Only super-admin can access
```

Available roles:
- `super-admin` - Full system access
- `admin` - Company admin access
- `hr` - HR operations access
- `employee` - Basic employee access

---

## Performance Tuning

### Database Connection Pool

**File:** `app.py`

```python
# Configure connection pool
app.config['SQLALCHEMY_POOL_SIZE'] = 10
app.config['SQLALCHEMY_MAX_OVERFLOW'] = 20
app.config['SQLALCHEMY_POOL_TIMEOUT'] = 30
```

### Query Optimization

Add indexes for frequently queried fields:

```sql
-- Index on machine_command for faster lookups
CREATE INDEX idx_machine_command_serial_status 
ON machine_command(serial, status, err_count);

-- Index on device serial number
CREATE INDEX idx_device_serial ON device(serial_num);
```

---

## Environment Variables

Create a `.env` file in the project root:

```bash
# Database
DATABASE_URL=postgresql://user:password@localhost/kazisync_central

# Flask
FLASK_ENV=production
SECRET_KEY=your-secret-key-here

# Azure (if using)
AZURE_STORAGE_CONNECTION_STRING=your-connection-string
AZURE_STORAGE_CONTAINER_NAME=your-container-name

# Logging
LOG_LEVEL=INFO
```

---

## Backup Configuration

### Database Backup

```bash
# Backup central database
pg_dump -U postgres kazisync_central > backup_central.sql

# Backup tenant database
pg_dump -U postgres company_database_name > backup_company.sql
```

### Automated Backups

Set up cron job:

```bash
# Daily backup at 2 AM
0 2 * * * /path/to/backup_script.sh
```

---

## Monitoring Configuration

### Health Check Endpoint

```bash
curl http://localhost:5000/health
```

### Command Queue Monitoring

Use the statistics endpoint:

```bash
curl -X GET "http://localhost:5000/api/commands/statistics?company_id=YOUR_ID" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

Monitor:
- Success rate
- Failed command count
- Device connectivity
- Pending commands

---

## Troubleshooting Configuration Issues

If configuration changes don't take effect:

1. **Restart the application**
2. **Clear Python cache:** `find . -type d -name __pycache__ -exec rm -r {} +`
3. **Check for syntax errors:** `python -m py_compile filename.py`
4. **Review logs:** Check `app.log` for errors

---

## Best Practices

1. **Always test configuration changes in development first**
2. **Document any custom configuration changes**
3. **Keep retry count reasonable** (15-20 is recommended)
4. **Monitor system performance after changes**
5. **Maintain backups before major configuration changes**

