# Customer Loyalty System - Quick Start Guide

## 🚀 Getting Started in 5 Steps

This guide will help you get the customer loyalty system up and running quickly.

---

## Step 1: Run Database Migrations

### For Each Tenant Database

You need to run these SQL commands on **each tenant database** (not the central database):

```sql
-- 1. Create customers table
CREATE TABLE customers (
    customer_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    first_name VARCHAR(255) NOT NULL,
    last_name VA<PERSON>HAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE,
    phone_number VARCHAR(20) UNIQUE,
    membership_number VARCHAR(50) UNIQUE,
    customer_segment VARCHAR(50) DEFAULT 'Regular' NOT NULL,
    registration_date DATE DEFAULT CURRENT_DATE NOT NULL,
    date_of_birth DATE,
    status VARCHAR(50) DEFAULT 'active' NOT NULL,
    preferred_contact_method VARCHAR(50),
    marketing_consent BOOLEAN DEFAULT FALSE NOT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID
);

CREATE INDEX idx_customers_email ON customers(email);
CREATE INDEX idx_customers_phone ON customers(phone_number);
CREATE INDEX idx_customers_segment ON customers(customer_segment);
CREATE INDEX idx_customers_status ON customers(status);

-- 2. Create customer_visits table
CREATE TABLE customer_visits (
    visit_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES customers(customer_id) ON DELETE CASCADE,
    visit_date DATE NOT NULL,
    visit_time TIMESTAMP NOT NULL,
    source VARCHAR(50) NOT NULL,
    source_record_id VARCHAR(255),
    device_serial_num VARCHAR(255),
    visit_type VARCHAR(50) DEFAULT 'regular' NOT NULL,
    duration_minutes INTEGER,
    is_loyalty_visit BOOLEAN DEFAULT TRUE NOT NULL,
    loyalty_points_earned INTEGER DEFAULT 0 NOT NULL,
    reward_redeemed BOOLEAN DEFAULT FALSE NOT NULL,
    redemption_id UUID,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_customer_visits_customer ON customer_visits(customer_id);
CREATE INDEX idx_customer_visits_date ON customer_visits(visit_date);
CREATE INDEX idx_customer_visits_time ON customer_visits(visit_time);

-- 3. Create promotion_rules table
CREATE TABLE promotion_rules (
    rule_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    rule_type VARCHAR(50) NOT NULL,
    trigger_value INTEGER NOT NULL,
    trigger_period_days INTEGER,
    reward_type VARCHAR(50) NOT NULL,
    reward_value NUMERIC(10, 2) NOT NULL,
    reward_description VARCHAR(255),
    applicable_customer_segments JSONB,
    applicable_visit_types JSONB,
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    valid_from DATE,
    valid_until DATE,
    max_redemptions_per_customer INTEGER,
    reward_expiry_days INTEGER,
    priority INTEGER DEFAULT 0 NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID
);

CREATE INDEX idx_promotion_rules_active ON promotion_rules(is_active);

-- 4. Create customer_loyalty_balances table
CREATE TABLE customer_loyalty_balances (
    balance_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES customers(customer_id) ON DELETE CASCADE,
    rule_id UUID NOT NULL REFERENCES promotion_rules(rule_id) ON DELETE CASCADE,
    current_count INTEGER DEFAULT 0 NOT NULL,
    target_count INTEGER NOT NULL,
    rewards_earned INTEGER DEFAULT 0 NOT NULL,
    rewards_redeemed INTEGER DEFAULT 0 NOT NULL,
    rewards_available INTEGER DEFAULT 0 NOT NULL,
    rewards_expired INTEGER DEFAULT 0 NOT NULL,
    period_start_date DATE,
    period_end_date DATE,
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    last_activity_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(customer_id, rule_id)
);

CREATE INDEX idx_loyalty_balances_customer ON customer_loyalty_balances(customer_id);
CREATE INDEX idx_loyalty_balances_rule ON customer_loyalty_balances(rule_id);

-- 5. Create reward_redemptions table
CREATE TABLE reward_redemptions (
    redemption_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES customers(customer_id) ON DELETE CASCADE,
    rule_id UUID NOT NULL REFERENCES promotion_rules(rule_id) ON DELETE CASCADE,
    balance_id UUID REFERENCES customer_loyalty_balances(balance_id) ON DELETE CASCADE,
    visit_id UUID REFERENCES customer_visits(visit_id) ON DELETE SET NULL,
    redemption_date DATE NOT NULL,
    redemption_time TIMESTAMP NOT NULL,
    reward_type VARCHAR(50) NOT NULL,
    reward_value NUMERIC(10, 2) NOT NULL,
    reward_description VARCHAR(255),
    status VARCHAR(50) DEFAULT 'completed' NOT NULL,
    redeemed_by UUID,
    redemption_method VARCHAR(50),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_redemptions_customer ON reward_redemptions(customer_id);
CREATE INDEX idx_redemptions_date ON reward_redemptions(redemption_date);

-- 6. Create promotion_audit_logs table
CREATE TABLE promotion_audit_logs (
    log_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID REFERENCES customers(customer_id) ON DELETE CASCADE,
    action_type VARCHAR(50) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id UUID,
    old_value JSONB,
    new_value JSONB,
    description TEXT,
    performed_by UUID,
    ip_address VARCHAR(45),
    user_agent VARCHAR(255),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

CREATE INDEX idx_audit_logs_customer ON promotion_audit_logs(customer_id);
CREATE INDEX idx_audit_logs_timestamp ON promotion_audit_logs(timestamp);

-- 7. Modify person table
ALTER TABLE person ADD COLUMN customer_id UUID;
ALTER TABLE person ADD CONSTRAINT fk_person_customer 
    FOREIGN KEY (customer_id) REFERENCES customers(customer_id) ON DELETE CASCADE;
ALTER TABLE person ADD CONSTRAINT check_person_type 
    CHECK (
        (employee_id IS NOT NULL AND customer_id IS NULL) OR 
        (employee_id IS NULL AND customer_id IS NOT NULL)
    );
CREATE INDEX idx_person_customer_id ON person(customer_id);
CREATE UNIQUE INDEX idx_person_customer_unique ON person(customer_id) WHERE customer_id IS NOT NULL;

-- 8. Add foreign key for redemption_id in customer_visits
ALTER TABLE customer_visits ADD CONSTRAINT fk_visit_redemption
    FOREIGN KEY (redemption_id) REFERENCES reward_redemptions(redemption_id) ON DELETE SET NULL;
```

---

## Step 2: Create a Promotion Rule

Use the API to create your first loyalty program rule:

```bash
curl -X POST http://your-server/api/promotions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "company_id": "your-company-id",
    "name": "10 Visits Free Session",
    "description": "Get 1 free session after 10 visits",
    "rule_type": "visit_count",
    "trigger_value": 10,
    "reward_type": "free_visit",
    "reward_value": 1,
    "reward_description": "1 Free Sauna Session",
    "applicable_customer_segments": ["Regular", "VIP"],
    "is_active": true
  }'
```

---

## Step 3: Create a Customer

Create your first customer:

```bash
curl -X POST http://your-server/api/customers \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "company_id": "your-company-id",
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "phone_number": "+1234567890",
    "customer_segment": "Regular"
  }'
```

**Save the `customer_id` from the response!**

---

## Step 4: Link Customer to Biometric Device

### Option A: Using Existing Person Record

If you already have a person record in the biometric system:

```sql
-- Update existing person to link to customer
UPDATE person 
SET customer_id = 'customer-uuid-from-step-3'
WHERE id = 123;  -- The enroll_id from biometric device
```

### Option B: Create New Person Record

If this is a new person:

```python
# Use the existing PersonService to add biometric data
# This is the same process as adding an employee

# 1. Create person record
person_data = {
    'id': 123,  # enroll_id from device
    'name': 'John Doe',
    'roll_id': 123,
    'customer_id': 'customer-uuid-from-step-3'
}

# 2. Add biometric data (fingerprint, face, etc.)
# Use your existing device sync process

# 3. Sync to devices
# The person will now be recognized by biometric devices
```

---

## Step 5: Test the System

### Test 1: Record a Visit

Have the customer use the biometric device (fingerprint or face scan). The system will:
1. Recognize the person
2. Create a customer visit record
3. Update loyalty balance
4. Award reward if threshold reached

### Test 2: Check Loyalty Status

```bash
curl -X GET "http://your-server/api/customers/CUSTOMER_ID/loyalty?company_id=COMPANY_ID" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Test 3: View Visit History

```bash
curl -X GET "http://your-server/api/customers/CUSTOMER_ID/visits?company_id=COMPANY_ID" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Test 4: Redeem a Reward

Once the customer has earned a reward:

```bash
curl -X POST "http://your-server/api/customers/CUSTOMER_ID/rewards/redeem" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "company_id": "your-company-id",
    "rule_id": "promotion-rule-id"
  }'
```

---

## 🔍 Monitoring and Debugging

### Check Application Logs

```bash
# Look for these log entries:
grep "Processing customer record" your-app.log
grep "Customer earned reward" your-app.log
grep "Redeemed reward" your-app.log
```

### Check Database

```sql
-- View all customers
SELECT * FROM customers;

-- View recent visits
SELECT * FROM customer_visits ORDER BY visit_time DESC LIMIT 10;

-- View loyalty balances
SELECT c.first_name, c.last_name, pr.name, clb.current_count, clb.target_count, clb.rewards_available
FROM customer_loyalty_balances clb
JOIN customers c ON clb.customer_id = c.customer_id
JOIN promotion_rules pr ON clb.rule_id = pr.rule_id;

-- View audit log
SELECT * FROM promotion_audit_logs ORDER BY timestamp DESC LIMIT 20;
```

---

## 🎯 Common Use Cases

### Use Case 1: Simple Visit-Based Loyalty

"Get 1 free session after 10 visits"

```json
{
  "rule_type": "visit_count",
  "trigger_value": 10,
  "reward_type": "free_visit",
  "reward_value": 1
}
```

### Use Case 2: Time-Limited Promotion

"Get 1 free session after 10 visits within 30 days"

```json
{
  "rule_type": "visit_count",
  "trigger_value": 10,
  "trigger_period_days": 30,
  "reward_type": "free_visit",
  "reward_value": 1
}
```

### Use Case 3: VIP-Only Promotion

"VIP customers: Get 1 free session after 5 visits"

```json
{
  "rule_type": "visit_count",
  "trigger_value": 5,
  "reward_type": "free_visit",
  "reward_value": 1,
  "applicable_customer_segments": ["VIP"]
}
```

---

## ✅ Verification Checklist

- [ ] Database migrations completed successfully
- [ ] Promotion rule created
- [ ] Customer created
- [ ] Customer linked to biometric device
- [ ] Customer can check in via biometric device
- [ ] Visit record created in database
- [ ] Loyalty balance updated
- [ ] Reward earned after reaching threshold
- [ ] Reward redeemed successfully
- [ ] Employee attendance still works (no regression)

---

## 🆘 Troubleshooting

### Issue: Customer visit not recorded

**Check:**
1. Is `person.customer_id` set correctly?
2. Is customer status 'active'?
3. Check application logs for errors
4. Verify biometric device is synced

### Issue: Loyalty balance not updating

**Check:**
1. Is promotion rule active?
2. Is customer segment eligible?
3. Is `visit.is_loyalty_visit` set to true?
4. Check `promotion_audit_logs` for errors

### Issue: Cannot redeem reward

**Check:**
1. Does customer have available rewards?
2. Has max redemption limit been reached?
3. Is reward expired?
4. Check `customer_loyalty_balances.rewards_available`

---

## 📚 Additional Resources

- **Full Documentation**: See `CUSTOMER_LOYALTY_IMPLEMENTATION.md`
- **Implementation Details**: See `IMPLEMENTATION_SUMMARY.md`
- **API Reference**: Check the API routes in the documentation

---

**You're all set! 🎉**

The customer loyalty system is now ready to use. Start by creating customers and promotion rules, then watch as the system automatically tracks visits and awards rewards!

