# KaziSync HRMS Documentation

Welcome to the KaziSync HRMS documentation. This directory contains comprehensive guides for using, configuring, and troubleshooting the system.

---

## 📖 Documentation Index

### Getting Started

- **[Main README](../README.md)** - System overview, features, and quick start guide

### Core Documentation

- **[API Reference](API_REFERENCE.md)** - Complete API documentation for all endpoints
- **[Troubleshooting Guide](TROUBLESHOOTING.md)** - Diagnose and fix common issues
- **[Configuration Guide](CONFIGURATION.md)** - System configuration and settings

### Customer Management Extensions

#### Customer Loyalty System
- **[Customer Loyalty Implementation Guide](CUSTOMER_LOYALTY_IMPLEMENTATION.md)** - Complete technical documentation and database schema
- **[Quick Start Guide](QUICK_START_GUIDE.md)** - Get started in 5 steps
- **[Implementation Summary](IMPLEMENTATION_SUMMARY.md)** - Executive overview and feature list

#### Service & Storage Management System
- **[Service & Storage Management Guide](SERVICE_STORAGE_MANAGEMENT.md)** - Comprehensive documentation for service consumption tracking and customer item storage management for sauna and massage businesses

### Blueprint Documentation

Individual documentation for each API blueprint:

- **[Command Management API](blueprints/command_management.md)** - Device command synchronization management

---

## 🔍 Quick Navigation

### By Topic

#### Device Synchronization
- [Device Sync Troubleshooting](TROUBLESHOOTING.md#device-synchronization-issues)
- [Command Management API](blueprints/command_management.md)
- [Retry Mechanism Configuration](CONFIGURATION.md#retry-mechanism-configuration)

#### API Development
- [API Reference](API_REFERENCE.md)
- [Authentication](API_REFERENCE.md#authentication)
- [Error Handling](API_REFERENCE.md#error-handling)
- [Pagination](API_REFERENCE.md#pagination)

#### System Configuration
- [Retry Settings](CONFIGURATION.md#retry-mechanism-configuration)
- [Database Connections](CONFIGURATION.md#database-connection-configuration)
- [WebSocket Configuration](CONFIGURATION.md#websocket-configuration)
- [Security Settings](CONFIGURATION.md#security-configuration)

#### Troubleshooting
- [Device Issues](TROUBLESHOOTING.md#device-synchronization-issues)
- [Database Issues](TROUBLESHOOTING.md#database-connection-issues)
- [Authentication Issues](TROUBLESHOOTING.md#authentication-issues)
- [Performance Issues](TROUBLESHOOTING.md#performance-issues)
- [Customer Loyalty Setup](QUICK_START_GUIDE.md)

---

## 🎯 Common Tasks

### For Administrators

#### Monitor System Health
```bash
# Check command queue statistics
curl -X GET "http://localhost:5000/api/commands/statistics?company_id=YOUR_ID" \
  -H "Authorization: Bearer YOUR_TOKEN"
```
See: [Command Statistics API](blueprints/command_management.md#5-get-command-statistics)

#### Troubleshoot Device Sync Issues
1. Check failed commands: [Get Failed Commands](blueprints/command_management.md#1-get-failed-commands)
2. Reset failed commands: [Reset Commands](blueprints/command_management.md#2-reset-single-command)
3. Monitor retry: [Troubleshooting Guide](TROUBLESHOOTING.md#monitoring-commands)

#### Configure Retry Mechanism
See: [Retry Configuration](CONFIGURATION.md#retry-mechanism-configuration)

### For Developers

#### Integrate with API
1. Review [API Reference](API_REFERENCE.md)
2. Implement [Authentication](API_REFERENCE.md#authentication)
3. Handle [Errors](API_REFERENCE.md#error-handling)
4. Use [Pagination](API_REFERENCE.md#pagination)

#### Add New Blueprint
1. Follow existing patterns in `application/Routes/`
2. Document in `docs/blueprints/`
3. Update this index

#### Debug Issues
1. Check [Troubleshooting Guide](TROUBLESHOOTING.md)
2. Review logs: `app.log` and `logs/device.log`
3. Use diagnostic endpoints

---

## 📚 Documentation Structure

```
docs/
├── README.md                          # This file - documentation index
├── API_REFERENCE.md                   # Complete API documentation
├── TROUBLESHOOTING.md                 # Common issues and solutions
├── CONFIGURATION.md                   # System configuration guide
├── CUSTOMER_LOYALTY_IMPLEMENTATION.md # Customer loyalty technical guide (NEW)
├── IMPLEMENTATION_SUMMARY.md          # Customer loyalty summary (NEW)
├── QUICK_START_GUIDE.md               # Customer loyalty quick start (NEW)
├── SERVICE_STORAGE_MANAGEMENT.md      # Service & storage management guide (NEW)
└── blueprints/                        # Blueprint-specific documentation
    └── command_management.md          # Command management API
```

---

## 🔧 System Architecture

### Multi-Tenant Design

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Central DB    │    │  Company A DB   │    │  Company B DB   │
│                 │    │                 │    │                 │
│ • Companies     │    │ • Employees     │    │ • Employees     │
│ • Users         │    │ • Payroll       │    │ • Payroll       │
│ • Countries     │    │ • Attendance    │    │ • Attendance    │
│ • Global Config │    │ • Leave Mgmt    │    │ • Leave Mgmt    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Database Connection Pattern

All API endpoints follow this pattern:

```python
# Get company_id from request
company_id = request.args.get('company_id')

# Validate
if not company_id:
    return jsonify({"message": "Company ID is required"}), 400

# Get database name
database_name = Company.get_database_given_company_id(company_id)
if not database_name:
    return jsonify({"message": "Company not found"}), 404

# Use context manager
from app import db_connection
with db_connection.get_session(database_name) as session:
    # Your operations
```

See: [Database Configuration](CONFIGURATION.md#database-connection-configuration)

---

## 🚨 Important Concepts

### Command Status Fields

Understanding command synchronization:

| Field | Values | Meaning |
|-------|--------|---------|
| `status` | 0 | Command not completed |
| | 1 | Command successfully completed |
| `send_status` | 0 | Command not yet sent |
| | 1 | Command sent (waiting for response) |
| `err_count` | 0-14 | Failed attempts (will retry) |
| | 15+ | Maximum retries exceeded (abandoned) |

See: [Device Synchronization](TROUBLESHOOTING.md#device-synchronization-issues)

### Retry Mechanism

- **Timeout:** 20 seconds between attempts
- **Max Retries:** 15 attempts
- **Behavior:** Commands with `err_count=15` are permanently excluded

See: [Retry Configuration](CONFIGURATION.md#retry-mechanism-configuration)

---

## 🆘 Getting Help

### Self-Service Resources

1. **Search this documentation** - Use Ctrl+F or search in your editor
2. **Check troubleshooting guide** - [TROUBLESHOOTING.md](TROUBLESHOOTING.md)
3. **Review API reference** - [API_REFERENCE.md](API_REFERENCE.md)
4. **Check configuration** - [CONFIGURATION.md](CONFIGURATION.md)

### Diagnostic Tools

```bash
# Check system health
curl -X GET "http://localhost:5000/health"

# Check command queue
curl -X GET "http://localhost:5000/api/commands/statistics?company_id=YOUR_ID" \
  -H "Authorization: Bearer YOUR_TOKEN"

# View logs
tail -f app.log
tail -f logs/device.log
```

### Common Issues

- **Employee not syncing to device:** [Device Sync Issues](TROUBLESHOOTING.md#device-synchronization-issues)
- **API returns 401:** [Authentication Issues](TROUBLESHOOTING.md#authentication-issues)
- **Database connection failed:** [Database Issues](TROUBLESHOOTING.md#database-connection-issues)
- **Slow performance:** [Performance Issues](TROUBLESHOOTING.md#performance-issues)

---

## 📝 Contributing to Documentation

When adding new features:

1. **Update API Reference** - Document all new endpoints
2. **Add Blueprint Documentation** - Create detailed guide in `blueprints/`
3. **Update Troubleshooting** - Add common issues and solutions
4. **Update Configuration** - Document new settings
5. **Update this index** - Add links to new documentation

### Documentation Standards

- Use clear, concise language
- Include code examples
- Provide curl commands for API endpoints
- Add troubleshooting steps
- Cross-reference related documentation

---

## 🔄 Recent Updates

### Latest Changes

- **Command Management API** - Added comprehensive device command management
- **Retry Mechanism** - Increased from 3 to 15 retry attempts
- **Documentation Structure** - Reorganized into hybrid approach
- **Troubleshooting Guide** - Added device synchronization section

---

## 📞 Support

For issues not covered in this documentation:

1. Check logs: `app.log` and `logs/device.log`
2. Review error messages carefully
3. Search existing documentation
4. Contact system administrator

---

**Last Updated:** 2025-10-01

**Documentation Version:** 1.0.0

