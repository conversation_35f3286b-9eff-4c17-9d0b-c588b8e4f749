# Service and Storage Management System

## Overview

The Service and Storage Management System is a comprehensive extension to the KaziSync HRMS customer management module, specifically designed for sauna and massage businesses. This system enables tracking of customer service consumption and personal item storage during visits.

## Key Features

### 1. Service Management
- **Service Catalog**: Maintain a catalog of available services (sauna, massage, food, beverages)
- **Service Categories**: Organize services by category for better management
- **Service Codes**: Unique identifiers for each service for easy reference

### 2. Dynamic Pricing System
- **Historical Pricing**: Track price changes over time while maintaining historical data
- **Effective Date Ranges**: Set start and end dates for pricing periods
- **Current Price Lookup**: Automatically determine current price for any service on any date
- **Multi-Currency Support**: Support for different currencies (default: USD)

### 3. Service Consumption Tracking
- **Visit-Based Tracking**: Link service consumption to specific customer visits
- **Quantity and Pricing**: Track quantity consumed and pricing at time of consumption
- **Total Calculation**: Automatic calculation of total amounts based on quantity and unit price
- **Consumption History**: Complete audit trail of all service consumption

### 4. Personal Item Storage
- **Storage Locations**: Manage numbered lockers, boxes, and cabinets
- **Availability Tracking**: Real-time tracking of storage location availability
- **Item Tracking**: Track what items customers store and when
- **Retrieval Management**: Mark items as retrieved or abandoned
- **Status Tracking**: Monitor storage status (stored, retrieved, abandoned)

### 5. Integration with Existing System
- **Zero-Impact Design**: New features don't modify existing customer/loyalty functionality
- **Seamless Integration**: Works with existing customer visits and loyalty systems
- **Backward Compatibility**: 100% compatible with existing customer management features

## Database Schema

### Services Table
```sql
CREATE TABLE services (
    service_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    category VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    created_by UUID
);
```

### Service Prices Table
```sql
CREATE TABLE service_prices (
    price_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    service_id UUID NOT NULL REFERENCES services(service_id) ON DELETE CASCADE,
    price_amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    effective_from DATE NOT NULL DEFAULT CURRENT_DATE,
    effective_to DATE NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    created_by UUID
);
```

### Customer Service Consumption Table
```sql
CREATE TABLE customer_service_consumption (
    consumption_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    visit_id UUID REFERENCES customer_visits(visit_id) ON DELETE CASCADE,
    customer_id UUID NOT NULL REFERENCES customers(customer_id) ON DELETE CASCADE,
    service_id UUID NOT NULL REFERENCES services(service_id) ON DELETE RESTRICT,
    price_id UUID NOT NULL REFERENCES service_prices(price_id) ON DELETE RESTRICT,
    quantity INTEGER DEFAULT 1 NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    consumed_at TIMESTAMP DEFAULT NOW(),
    notes TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### Storage Locations Table
```sql
CREATE TABLE storage_locations (
    location_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    location_number VARCHAR(50) UNIQUE NOT NULL,
    location_type VARCHAR(50) DEFAULT 'locker' NOT NULL,
    is_available BOOLEAN DEFAULT TRUE NOT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### Customer Item Storage Table
```sql
CREATE TABLE customer_item_storage (
    storage_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES customers(customer_id) ON DELETE CASCADE,
    location_id UUID NOT NULL REFERENCES storage_locations(location_id) ON DELETE RESTRICT,
    visit_id UUID REFERENCES customer_visits(visit_id) ON DELETE SET NULL,
    items_description TEXT NOT NULL,
    stored_at TIMESTAMP DEFAULT NOW(),
    retrieved_at TIMESTAMP NULL,
    status VARCHAR(50) DEFAULT 'stored' NOT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

## Model Descriptions

### Service Model
- **Purpose**: Represents available services in the business
- **Key Methods**: 
  - `create_service()`: Create new service
  - `get_all_services()`: List services with filtering
  - `get_service_by_code()`: Find service by unique code
  - `update_service()`: Modify service details
  - `deactivate_service()`: Soft delete service

### ServicePrice Model
- **Purpose**: Manages historical pricing for services
- **Key Methods**:
  - `create_price()`: Add new price with effective dates
  - `get_current_price()`: Get active price for specific date
  - `expire_price()`: Set end date for current price
  - `get_all_prices()`: List prices with filtering

### CustomerServiceConsumption Model
- **Purpose**: Tracks service usage by customers
- **Key Methods**:
  - `create_consumption()`: Record service consumption
  - `get_all_consumptions()`: List consumption with filtering
  - `get_consumption_total()`: Calculate total spending
  - `update_consumption()`: Modify consumption record

### StorageLocation Model
- **Purpose**: Manages storage spaces (lockers, boxes, cabinets)
- **Key Methods**:
  - `create_location()`: Add new storage location
  - `get_available_locations()`: Find available storage
  - `set_availability()`: Manually control availability
  - `get_location_by_number()`: Find by location number

### CustomerItemStorage Model
- **Purpose**: Tracks customer belongings in storage
- **Key Methods**:
  - `create_storage()`: Store customer items
  - `retrieve_items()`: Mark items as retrieved
  - `abandon_items()`: Mark items as abandoned
  - `get_active_storages_by_customer()`: Find customer's stored items

## API Endpoints

### Service Management API (`/api/services`)
- `GET /api/services` - List all services
- `GET /api/services/{id}` - Get specific service
- `POST /api/services` - Create new service
- `PUT /api/services/{id}` - Update service
- `DELETE /api/services/{id}` - Deactivate service
- `GET /api/services/code/{code}` - Get service by code

### Service Price API (`/api/service-prices`)
- `GET /api/service-prices` - List all prices
- `GET /api/service-prices/{id}` - Get specific price
- `POST /api/service-prices` - Create new price
- `PUT /api/service-prices/{id}` - Update price
- `POST /api/service-prices/{id}/expire` - Expire price
- `GET /api/services/{id}/current-price` - Get current price

### Service Consumption API (`/api/service-consumptions`)
- `GET /api/service-consumptions` - List all consumption
- `GET /api/service-consumptions/{id}` - Get specific consumption
- `POST /api/service-consumptions` - Record single service consumption
- `POST /api/service-consumptions/bulk` - Record multiple services in one transaction
- `PUT /api/service-consumptions/{id}` - Update consumption
- `DELETE /api/service-consumptions/{id}` - Delete consumption
- `GET /api/service-consumptions/total` - Calculate totals

#### 🔄 Automatic Price Lookup Feature

When creating service consumption records, the system automatically determines the correct price:

**Single Service Request:**
```json
POST /api/service-consumptions
{
    "customer_id": "uuid",
    "service_id": "uuid",
    "quantity": 2,                        // Optional, defaults to 1
    "consumed_at": "2024-01-15 14:30:00", // Optional
    "notes": "Customer notes"             // Optional
}
```

#### 🚀 Bulk Service Consumption Feature

For customers consuming multiple services in a single visit, use the bulk endpoint:

**Bulk Services Request:**
```json
POST /api/service-consumptions/bulk
{
    "customer_id": "uuid",
    "visit_id": "uuid",                   // Optional
    "consumed_at": "2024-01-15 14:30:00", // Optional, applies to all services
    "services": [
        {
            "service_id": "uuid-sauna",
            "quantity": 2,
            "notes": "Extended sauna session"
        },
        {
            "service_id": "uuid-massage",
            "quantity": 1,
            "notes": "Deep tissue massage"
        },
        {
            "service_id": "uuid-beverage",
            "quantity": 3,
            "notes": "Herbal tea"
        }
    ]
}
```

**Bulk Response:**
```json
{
    "success": true,
    "bulk_consumption": {
        "consumptions": [...],            // Array of created consumption records
        "summary": {
            "total_services": 3,
            "total_amount": 145.00,
            "services": [
                {
                    "service_name": "Sauna Session",
                    "quantity": 2,
                    "unit_price": 25.00,
                    "total_price": 50.00
                }
            ]
        }
    }
}
```

**Key Features:**
- **`price_id` is optional** - System auto-determines current active price for each service
- **Date-based pricing** - Uses `consumed_at` date to find correct historical price
- **Transactional** - All services succeed or all fail (atomicity)
- **Automatic totals** - Calculates total amount across all services
- **Service validation** - Validates all services exist and are active before processing
- **Backward compatible** - Still accepts explicit `price_id` if provided

### Storage Location API (`/api/storage-locations`)
- `GET /api/storage-locations` - List all locations
- `GET /api/storage-locations/{id}` - Get specific location
- `POST /api/storage-locations` - Create new location
- `PUT /api/storage-locations/{id}` - Update location
- `DELETE /api/storage-locations/{id}` - Delete location
- `GET /api/storage-locations/available` - Get available locations
- `POST /api/storage-locations/{id}/availability` - Set availability

### Customer Storage API (`/api/customer-storages`)
- `GET /api/customer-storages` - List all storages
- `GET /api/customer-storages/{id}` - Get specific storage
- `POST /api/customer-storages` - Store customer items
- `PUT /api/customer-storages/{id}` - Update storage
- `POST /api/customer-storages/{id}/retrieve` - Retrieve items
- `POST /api/customer-storages/{id}/abandon` - Abandon items
- `GET /api/customers/{id}/active-storages` - Get customer's active storage

## Authentication and Authorization

All API endpoints require:
- **Authentication**: `@token_required` decorator
- **Authorization**: `@roles_required('hr')` decorator
- **Company Context**: Automatic company_id extraction for multi-tenant support

## Error Handling

The system implements comprehensive error handling:
- **Validation Errors**: 400 Bad Request with descriptive messages
- **Not Found Errors**: 404 Not Found for missing resources
- **Server Errors**: 500 Internal Server Error with logging
- **Authentication Errors**: 401 Unauthorized for invalid tokens

## Logging and Monitoring

All operations are logged with:
- **Info Level**: Successful operations and business logic
- **Error Level**: Failures and exceptions
- **User Context**: All logs include user_id and company_id
- **Operation Details**: Specific details about what was performed

## Integration Points

### With Existing Customer System
- Links to `customers` table via `customer_id`
- Links to `customer_visits` table via `visit_id`
- Maintains existing customer loyalty functionality

### With Approval System
- Ready for integration with existing approval workflow
- Can be extended to require approval for high-value consumptions
- Audit trail compatible with existing audit log patterns

## Future Enhancements

### Planned Features
1. **Approval Workflow Integration**: Require approval for expensive services
2. **Reporting Dashboard**: Analytics and reporting for service consumption
3. **Mobile App Integration**: Mobile interface for receptionists
4. **Inventory Management**: Track service inventory and availability
5. **Customer Notifications**: Notify customers about stored items

### Extensibility
The system is designed for easy extension:
- **New Service Types**: Easy to add new service categories
- **Additional Storage Types**: Support for new storage location types
- **Enhanced Pricing**: Support for complex pricing rules
- **Integration APIs**: Ready for third-party integrations

## Technical Implementation

### Design Patterns Used
- **Separation of Concerns**: Dedicated tables per domain
- **Soft Delete Pattern**: Using `is_active` flags
- **Audit Trail Pattern**: Comprehensive logging
- **Historical Data Pattern**: Effective date ranges for pricing

### Performance Considerations
- **Indexed Columns**: All foreign keys and frequently queried columns
- **Efficient Queries**: Optimized database queries with proper filtering
- **Lazy Loading**: Relationships loaded only when needed
- **Connection Pooling**: Efficient database connection management

### Security Features
- **Multi-Tenant Isolation**: Company-specific database access
- **Role-Based Access**: HR role required for all operations
- **Input Validation**: Comprehensive validation of all inputs
- **SQL Injection Prevention**: Parameterized queries throughout

## Conclusion

The Service and Storage Management System provides a robust, scalable solution for sauna and massage businesses to track service consumption and manage customer belongings. The system maintains 100% backward compatibility while adding powerful new features that enhance the customer experience and provide valuable business insights.
