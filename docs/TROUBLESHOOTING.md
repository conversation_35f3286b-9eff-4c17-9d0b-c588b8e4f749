# Troubleshooting Guide

This guide helps you diagnose and resolve common issues in the KaziSync HRMS system.

---

## Device Synchronization Issues

### Problem: Employee Data Not Appearing on Biometric Device

**Symptoms:**
- Employee registered successfully in web interface
- `machine_command` entries created in database
- Employee data not showing on physical device
- Commands show `err_count >= 15` (failed status)

**Understanding Command Status Fields:**

| Field | Values | Meaning |
|-------|--------|---------|
| `status` | 0 | Command not completed/acknowledged by device |
| | 1 | Command successfully completed |
| `send_status` | 0 | Command not yet sent to device |
| | 1 | Command sent to device (waiting for acknowledgment) |
| `err_count` | 0-14 | Number of failed attempts (will retry) |
| | 15+ | Maximum retries exceeded (command abandoned) |

**Command Processing Flow:**

1. **Command Creation**: Employee registration creates `machine_command` with:
   - `status=0` (pending)
   - `send_status=0` (not sent)
   - `err_count=0` (no failures)

2. **Command Sending**: Background job (`SendOrderJob`) sends via WebSocket:
   - Updates to `send_status=1` (sent, waiting for response)

3. **Waiting for Response**:
   - System waits 20 seconds for device acknowledgment
   - Device should respond with `ret="setuserinfo"`

4. **Retry Logic**:
   - If no response after 20 seconds, `err_count` increments
   - Command is resent to device
   - After 15 failed attempts, command is **permanently excluded**

5. **Abandoned Commands**:
   - Commands with `err_count=15` are filtered out by `find_pending_command()`
   - They remain in database but **never retry automatically**

### Root Causes

#### 1. Device Not Responding
- Device is online but not processing commands
- WebSocket connection is unstable
- Device firmware has bugs or compatibility issues

#### 2. Network Issues
- Intermittent connectivity
- Packet loss
- Firewall blocking responses

#### 3. Device Capacity Issues
- Device memory full
- Maximum employee capacity reached
- Storage corruption

#### 4. Command Format Issues
- Malformed JSON payload
- Data too large for device
- Unsupported characters in employee name

#### 5. Device Firmware Issues
- Outdated firmware
- Known bugs in device software
- Incompatible command protocol

### Diagnostic Steps

#### Step 1: Check Device Connectivity

Use the command management API to check device status:

```bash
curl -X GET "http://localhost:5000/api/commands/statistics?company_id=YOUR_COMPANY_ID&device_sn=DEVICE_SN" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

Look for:
- `devices_online` vs `devices_offline`
- `connected: true/false` for specific device

#### Step 2: Check Failed Commands

```bash
curl -X GET "http://localhost:5000/api/commands/failed?company_id=YOUR_COMPANY_ID&device_sn=DEVICE_SN" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

This shows:
- All commands with `err_count >= 15`
- Employee information
- Time since failure
- Device connectivity status

#### Step 3: Check Specific Command Details

```bash
curl -X GET "http://localhost:5000/api/commands/COMMAND_ID?company_id=YOUR_COMPANY_ID" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Solutions

#### Solution 1: Reset Failed Command (Immediate)

Reset a specific failed command to retry:

```bash
curl -X POST "http://localhost:5000/api/commands/reset" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "command_id": 12,
    "company_id": "YOUR_COMPANY_ID"
  }'
```

The command will be automatically retried by `SendOrderJob`.

#### Solution 2: Reset All Failed Commands for Device

```bash
curl -X POST "http://localhost:5000/api/commands/retry-all" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "company_id": "YOUR_COMPANY_ID",
    "device_sn": "DEVICE_SN"
  }'
```

#### Solution 3: Reset for Specific Employee

```bash
curl -X POST "http://localhost:5000/api/commands/retry-all" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "company_id": "YOUR_COMPANY_ID",
    "device_sn": "DEVICE_SN",
    "employee_id": 5
  }'
```

#### Solution 4: Manual Device Reboot

If commands continue to fail:

1. Reboot the device physically
2. Wait for device to reconnect
3. Reset failed commands using API
4. Monitor logs for success

#### Solution 5: Check Device Capacity

If device is full:
- Delete old/inactive employees from device
- Clear device logs
- Factory reset if necessary (last resort)

### Monitoring Commands

#### View Application Logs

```bash
# Application logs
tail -f app.log | grep DEVICE_SN

# Device-specific logs
tail -f logs/device.log | grep DEVICE_SN
```

#### Query Database Directly

```sql
-- Check failed commands
SELECT id, serial, name, status, send_status, err_count, run_time, content
FROM machine_command
WHERE serial = 'DEVICE_SN'
  AND status = 0
  AND err_count >= 15
ORDER BY gmt_crate DESC;

-- Check command history for employee
SELECT id, serial, name, status, send_status, err_count, run_time
FROM machine_command
WHERE serial = 'DEVICE_SN'
  AND content LIKE '%"enrollid":5%'
ORDER BY gmt_crate DESC;
```

---

## Database Connection Issues

### Problem: "Company not found" or "Database connection failed"

**Symptoms:**
- API returns 404 "Company not found"
- Database connection errors in logs

**Solutions:**

1. **Verify company exists:**
   ```sql
   SELECT company_id, company_name, database_name FROM companies;
   ```

2. **Check database name is set:**
   ```sql
   SELECT company_id, database_name FROM companies WHERE company_id = 'YOUR_ID';
   ```

3. **Verify database exists:**
   ```sql
   SELECT datname FROM pg_database WHERE datname = 'company_database_name';
   ```

---

## Authentication Issues

### Problem: 401 Unauthorized

**Solutions:**

1. **Check token is valid:**
   - Token not expired
   - Token format: `Bearer <token>`

2. **Verify user role:**
   - Some endpoints require `super-admin` role
   - Check user roles in database

3. **Refresh token if expired:**
   ```bash
   curl -X POST "http://localhost:5000/api/auth/refresh" \
     -H "Content-Type: application/json" \
     -d '{"refresh_token": "YOUR_REFRESH_TOKEN"}'
   ```

---

## Performance Issues

### Problem: Slow API Response

**Diagnostic Steps:**

1. **Check database query performance:**
   ```sql
   EXPLAIN ANALYZE SELECT * FROM machine_command WHERE serial = 'DEVICE_SN';
   ```

2. **Monitor active connections:**
   ```sql
   SELECT count(*) FROM pg_stat_activity;
   ```

3. **Check for missing indexes:**
   ```sql
   SELECT tablename, indexname FROM pg_indexes WHERE schemaname = 'public';
   ```

---

## WebSocket Connection Issues

### Problem: Device Not Connecting

**Symptoms:**
- Device shows as offline
- No WebSocket connection in logs

**Solutions:**

1. **Check device network connectivity**
2. **Verify WebSocket endpoint is accessible**
3. **Check firewall rules**
4. **Review device configuration**

---

## Getting Help

If you encounter issues not covered here:

1. **Check logs:** `app.log` and `logs/device.log`
2. **Review API documentation:** See `docs/API_REFERENCE.md`
3. **Check configuration:** See `docs/CONFIGURATION.md`
4. **Contact support:** Provide logs and error messages

