# Command Management API

The Command Management API provides endpoints for monitoring and managing device synchronization commands in the KaziSync HRMS system.

---

## Overview

This API allows you to:
- View failed commands that couldn't sync to devices
- Reset failed commands to retry them
- Get detailed command information
- Monitor command queue statistics
- Bulk reset commands for devices or employees

**Base Path:** `/api/commands`

**Authentication:** All endpoints require JWT token authentication

**Required Role:** `super-admin` (except for GET endpoints which require any authenticated user)

---

## Endpoints

### 1. Get Failed Commands

Retrieve all failed commands for a company or specific device.

**Endpoint:** `GET /api/commands/failed`

**Authentication:** Required (any authenticated user)

**Query Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `company_id` | string (UUID) | Yes* | Company ID |
| `device_sn` | string | Yes* | Device serial number |
| `employee_id` | integer | No | Filter by employee enrollid |
| `include_content` | boolean | No | Include full command content (default: false) |

*Either `company_id` or `device_sn` is required

**Example Request:**

```bash
# Get all failed commands for a company
curl -X GET "http://localhost:5000/api/commands/failed?company_id=550e8400-e29b-41d4-a716-************" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Get failed commands for specific device
curl -X GET "http://localhost:5000/api/commands/failed?company_id=550e8400-e29b-41d4-a716-************&device_sn=AYTI10087992" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Get failed commands for specific employee
curl -X GET "http://localhost:5000/api/commands/failed?company_id=550e8400-e29b-41d4-a716-************&employee_id=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**Response:**

```json
{
  "status": "success",
  "failed_commands": [
    {
      "id": 12,
      "serial": "AYTI10087992",
      "name": "setuserinfo",
      "status": 0,
      "status_text": "Pending",
      "send_status": 1,
      "send_status_text": "Sent",
      "err_count": 15,
      "run_time": "2025-10-01T10:30:00",
      "created": "2025-10-01T10:00:00",
      "modified": "2025-10-01T10:30:00",
      "time_since_failure_seconds": 1800,
      "device_connected": true,
      "employee_id": 5,
      "employee_name": "Innocent Wakanda",
      "content_preview": "{\"cmd\":\"setuserinfo\",\"enrollid\":5..."
    }
  ],
  "count": 1,
  "devices_checked": [
    {
      "device_sn": "AYTI10087992",
      "failed_count": 1
    }
  ],
  "summary": {
    "total_failed": 1,
    "devices_with_failures": 1,
    "total_devices_checked": 2
  }
}
```

---

### 2. Reset Single Command

Reset a specific failed command to retry it.

**Endpoint:** `POST /api/commands/reset`

**Authentication:** Required (`super-admin` role)

**Request Body:**

```json
{
  "command_id": 12,
  "company_id": "550e8400-e29b-41d4-a716-************",
  "device_sn": "AYTI10087992"  // optional, for validation
}
```

**Example Request:**

```bash
curl -X POST "http://localhost:5000/api/commands/reset" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "command_id": 12,
    "company_id": "550e8400-e29b-41d4-a716-************"
  }'
```

**Response:**

```json
{
  "status": "success",
  "message": "Command 12 has been reset and will be retried automatically",
  "command": {
    "id": 12,
    "serial": "AYTI10087992",
    "name": "setuserinfo",
    "status": 0,
    "send_status": 0,
    "err_count": 0,
    "previous_err_count": 15,
    "device_connected": true
  },
  "warning": null
}
```

**Error Responses:**

```json
// Command not failed
{
  "message": "Command 12 is not failed (err_count=2)",
  "note": "Only commands with err_count >= 15 need to be reset"
}

// Command already completed
{
  "message": "Command 12 is already completed",
  "note": "No need to reset completed commands"
}

// Device offline warning
{
  "status": "success",
  "warning": "Device is currently offline - command will be sent when device reconnects"
}
```

---

### 3. Reset All Failed Commands

Reset all failed commands for a device, company, or specific employee.

**Endpoint:** `POST /api/commands/retry-all`

**Authentication:** Required (`super-admin` role)

**Request Body:**

```json
{
  "company_id": "550e8400-e29b-41d4-a716-************",
  "device_sn": "AYTI10087992",  // optional - if not provided, resets for all company devices
  "employee_id": 5  // optional - filter by specific employee
}
```

**Example Requests:**

```bash
# Reset all failed commands for entire company
curl -X POST "http://localhost:5000/api/commands/retry-all" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "company_id": "550e8400-e29b-41d4-a716-************"
  }'

# Reset for specific device
curl -X POST "http://localhost:5000/api/commands/retry-all" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "company_id": "550e8400-e29b-41d4-a716-************",
    "device_sn": "AYTI10087992"
  }'

# Reset for specific employee across all devices
curl -X POST "http://localhost:5000/api/commands/retry-all" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "company_id": "550e8400-e29b-41d4-a716-************",
    "employee_id": 5
  }'
```

**Response:**

```json
{
  "status": "success",
  "message": "Reset 3 failed command(s) across 2 device(s)",
  "count": 3,
  "devices_processed": {
    "AYTI10087992": 2,
    "AYTI10087994": 1
  },
  "commands": [
    {
      "id": 12,
      "device_sn": "AYTI10087992",
      "name": "setuserinfo",
      "employee_id": 5,
      "employee_name": "Innocent Wakanda",
      "previous_err_count": 15
    },
    {
      "id": 13,
      "device_sn": "AYTI10087992",
      "name": "setuserinfo",
      "employee_id": 6,
      "employee_name": "John Doe",
      "previous_err_count": 15
    }
  ]
}
```

---

### 4. Get Command Details

Get detailed information about a specific command.

**Endpoint:** `GET /api/commands/<command_id>`

**Authentication:** Required (any authenticated user)

**Query Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `company_id` | string (UUID) | Yes | Company ID for efficient lookup |

**Example Request:**

```bash
curl -X GET "http://localhost:5000/api/commands/12?company_id=550e8400-e29b-41d4-a716-************" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**Response:**

```json
{
  "status": "success",
  "command": {
    "id": 12,
    "serial": "AYTI10087992",
    "name": "setuserinfo",
    "command_type": "setuserinfo",
    "status": 0,
    "status_text": "Pending",
    "send_status": 1,
    "send_status_text": "Sent",
    "err_count": 15,
    "is_failed": true,
    "run_time": "2025-10-01T10:30:00",
    "created": "2025-10-01T10:00:00",
    "modified": "2025-10-01T10:30:00",
    "time_since_creation_seconds": 3600,
    "time_since_last_attempt_seconds": 1800,
    "content": "{\"cmd\":\"setuserinfo\",\"enrollid\":5,\"name\":\"Innocent Wakanda\",\"backupnum\":-1,\"admin\":0,\"record\":\"\"}",
    "device_connected": true,
    "employee_id": 5,
    "employee_name": "Innocent Wakanda"
  }
}
```

---

### 5. Get Command Statistics

Get comprehensive statistics about command queue status.

**Endpoint:** `GET /api/commands/statistics`

**Authentication:** Required (any authenticated user)

**Query Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `company_id` | string (UUID) | Yes | Company ID |
| `device_sn` | string | No | Device serial number for device-specific stats |

**Example Requests:**

```bash
# Company-wide statistics
curl -X GET "http://localhost:5000/api/commands/statistics?company_id=550e8400-e29b-41d4-a716-************" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Device-specific statistics
curl -X GET "http://localhost:5000/api/commands/statistics?company_id=550e8400-e29b-41d4-a716-************&device_sn=AYTI10087992" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**Response:**

```json
{
  "status": "success",
  "statistics": {
    "overall": {
      "total_commands": 150,
      "completed": 140,
      "pending": 5,
      "failed": 5,
      "not_sent": 2,
      "sent_waiting": 3,
      "devices_online": 1,
      "devices_offline": 1,
      "success_rate_percent": 93.33,
      "failure_rate_percent": 3.33
    },
    "by_device": [
      {
        "device_sn": "AYTI10087992",
        "connected": true,
        "total_commands": 75,
        "completed": 70,
        "pending": 2,
        "failed": 3,
        "not_sent": 1,
        "sent_waiting": 1,
        "oldest_pending": "2025-10-01T09:00:00",
        "newest_failed": "2025-10-01T10:30:00"
      },
      {
        "device_sn": "AYTI10087994",
        "connected": false,
        "total_commands": 75,
        "completed": 70,
        "pending": 3,
        "failed": 2,
        "not_sent": 1,
        "sent_waiting": 2,
        "oldest_pending": "2025-10-01T09:30:00",
        "newest_failed": "2025-10-01T10:15:00"
      }
    ]
  }
}
```

---

## Common Use Cases

### Use Case 1: Monitor Command Queue Health

```bash
# Get statistics to check overall health
curl -X GET "http://localhost:5000/api/commands/statistics?company_id=YOUR_COMPANY_ID" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Check for high failure rate or many pending commands
```

### Use Case 2: Troubleshoot Employee Not Syncing

```bash
# 1. Check if there are failed commands for the employee
curl -X GET "http://localhost:5000/api/commands/failed?company_id=YOUR_COMPANY_ID&employee_id=5" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 2. Reset the failed commands
curl -X POST "http://localhost:5000/api/commands/retry-all" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"company_id": "YOUR_COMPANY_ID", "employee_id": 5}'

# 3. Monitor logs to see if retry succeeds
tail -f logs/device.log | grep "employee_name"
```

### Use Case 3: Device Maintenance

```bash
# Before device reboot, check pending commands
curl -X GET "http://localhost:5000/api/commands/statistics?company_id=YOUR_COMPANY_ID&device_sn=DEVICE_SN" \
  -H "Authorization: Bearer YOUR_TOKEN"

# After device comes back online, reset any failed commands
curl -X POST "http://localhost:5000/api/commands/retry-all" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"company_id": "YOUR_COMPANY_ID", "device_sn": "DEVICE_SN"}'
```

---

## Error Codes

| Status Code | Description |
|-------------|-------------|
| 200 | Success |
| 400 | Bad Request - Missing or invalid parameters |
| 401 | Unauthorized - Invalid or missing token |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Company, device, or command not found |
| 500 | Internal Server Error - Server-side error |

---

## Best Practices

1. **Monitor regularly:** Use the statistics endpoint to monitor command queue health
2. **Reset promptly:** Reset failed commands as soon as issues are resolved
3. **Check device connectivity:** Always verify device is online before resetting commands
4. **Investigate root causes:** Don't just reset - find out why commands are failing
5. **Use bulk operations:** Use `retry-all` for multiple commands instead of individual resets
6. **Log monitoring:** Watch logs after resetting to ensure commands succeed

---

## Related Documentation

- [Troubleshooting Guide](../TROUBLESHOOTING.md) - Diagnose and fix sync issues
- [Configuration Guide](../CONFIGURATION.md) - Retry mechanism settings
- [API Reference](../API_REFERENCE.md) - Complete API documentation

