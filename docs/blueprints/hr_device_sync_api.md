# HR Device Synchronization API

The HR Device Synchronization API provides endpoints for HR users to monitor and manage employee/customer device synchronization without requiring super-admin privileges.

---

## Overview

This API allows HR users to:
- Check device synchronization status for specific employees or customers
- View all failed synchronizations for their company
- Retry failed device synchronizations
- Monitor biometric data sync across all company devices

**Base Path:** `/api/hr`

**Authentication:** All endpoints require JWT token authentication

**Required Roles:** `hr`, `admin`, or `super-admin`

**Multi-Tenant:** HR users can only access data for their own company

---

## Endpoints

### 1. Get Person Sync Status

Get comprehensive device synchronization status for a specific employee or customer.

**Endpoint:** `GET /api/hr/sync-status/<person_type>/<person_id>`

**Path Parameters:**
- `person_type`: Either `employee` or `customer`
- `person_id`: Employee ID (string) or Customer ID (UUID)

**Authorization:** `@token_required` and `@roles_required('hr', 'admin', 'super-admin')`

**Example Request:**
```bash
GET /api/hr/sync-status/employee/EMP001
Authorization: Bearer <jwt_token>
```

**Example Response:**
```json
{
  "person_type": "employee",
  "person_id": "EMP001",
  "person_name": "John Doe",
  "enroll_id": 123,
  "sync_summary": {
    "total_devices": 3,
    "synced_devices": 2,
    "failed_devices": 1,
    "pending_devices": 0
  },
  "device_sync_details": [
    {
      "device_sn": "AYTI10087992",
      "device_connected": true,
      "commands": [
        {
          "command_id": 456,
          "command_type": "setuserinfo",
          "biometric_type": "fingerprint",
          "backupnum": 0,
          "status": "failed",
          "err_count": 15,
          "last_attempt": "2025-10-07T14:30:00",
          "can_retry": true,
          "created": "2025-10-07T10:00:00",
          "modified": "2025-10-07T14:30:00"
        },
        {
          "command_id": 457,
          "command_type": "setuserinfo",
          "biometric_type": "face_dynamic",
          "backupnum": 50,
          "status": "completed",
          "err_count": 0,
          "last_attempt": "2025-10-07T10:05:00",
          "can_retry": false,
          "created": "2025-10-07T10:00:00",
          "modified": "2025-10-07T10:05:00"
        }
      ]
    }
  ]
}
```

**Response Fields:**
- `sync_summary`: High-level statistics
- `device_sync_details`: Detailed status per device
- `biometric_type`: Human-readable biometric type (fingerprint, face_dynamic, rfid_card, etc.)
- `status`: `completed`, `failed`, or `pending`
- `can_retry`: Whether the command can be retried (true for failed commands)

---

### 2. Get Company Sync Failures

Get all failed device synchronizations for the HR user's company.

**Endpoint:** `GET /api/hr/sync-failures`

**Query Parameters:**
- `person_type` (optional): Filter by `employee` or `customer`
- `device_sn` (optional): Filter by specific device serial number

**Authorization:** `@token_required` and `@roles_required('hr', 'admin', 'super-admin')`

**Example Request:**
```bash
GET /api/hr/sync-failures?person_type=employee&device_sn=AYTI10087992
Authorization: Bearer <jwt_token>
```

**Example Response:**
```json
{
  "company_id": "uuid",
  "total_failed_syncs": 5,
  "failed_by_type": {
    "employees": 3,
    "customers": 2
  },
  "failures": [
    {
      "person_type": "employee",
      "person_id": "EMP001",
      "person_name": "John Doe",
      "enroll_id": 123,
      "device_sn": "AYTI10087992",
      "failed_commands": 2,
      "oldest_failure": "2025-10-06T10:00:00",
      "biometric_types_failed": ["fingerprint", "face_dynamic"]
    },
    {
      "person_type": "customer",
      "person_id": "550e8400-e29b-41d4-a716-************",
      "person_name": "Jane Smith",
      "enroll_id": 124,
      "device_sn": "AYTI10087993",
      "failed_commands": 1,
      "oldest_failure": "2025-10-07T08:00:00",
      "biometric_types_failed": ["rfid_card"]
    }
  ]
}
```

**Response Fields:**
- `total_failed_syncs`: Total number of person-device combinations with failures
- `failed_by_type`: Count breakdown by employee vs customer
- `failures`: List of failed synchronizations grouped by person and device

---

### 3. Retry Failed Sync

Retry failed device synchronization for a specific employee or customer.

**Endpoint:** `POST /api/hr/retry-sync`

**Authorization:** `@token_required` and `@roles_required('hr', 'admin', 'super-admin')`

**Request Body:**
```json
{
  "person_type": "employee",
  "person_id": "EMP001",
  "device_sn": "AYTI10087992",
  "command_ids": [456, 789]
}
```

**Request Fields:**
- `person_type` (required): `employee` or `customer`
- `person_id` (required): Employee ID or Customer ID
- `device_sn` (optional): Specific device (if omitted, retry on all devices)
- `command_ids` (optional): Specific commands (if omitted, retry all failed commands)

**Example Response:**
```json
{
  "status": "success",
  "message": "Reset 3 failed sync command(s) for employee EMP001",
  "person_type": "employee",
  "person_id": "EMP001",
  "person_name": "John Doe",
  "commands_reset": 3,
  "devices_affected": ["AYTI10087992", "AYTI10087993"],
  "reset_details": [
    {
      "command_id": 456,
      "device_sn": "AYTI10087992",
      "command_type": "setuserinfo",
      "biometric_type": "fingerprint",
      "previous_err_count": 15,
      "device_connected": true
    },
    {
      "command_id": 789,
      "device_sn": "AYTI10087993",
      "command_type": "setuserinfo",
      "biometric_type": "face_dynamic",
      "previous_err_count": 15,
      "device_connected": false
    }
  ],
  "warnings": [
    "Device AYTI10087993 is currently offline - command will be sent when device reconnects"
  ]
}
```

**Response Fields:**
- `commands_reset`: Number of commands that were reset
- `devices_affected`: List of device serial numbers affected
- `reset_details`: Detailed information about each reset command
- `warnings`: Notifications about offline devices or other issues

---

## Biometric Types

The API translates device protocol backup numbers to human-readable biometric types:

| Backup Number | Biometric Type | Description |
|---------------|----------------|-------------|
| 0-9 | `fingerprint` | Fingerprint templates |
| 10 | `password` | PIN/password |
| 11 | `rfid_card` | RFID card data |
| 20-27 | `face_static` | Static face templates |
| 50 | `face_dynamic` | Dynamic face recognition |
| Other | `unknown_type_X` | Unknown type with backup number |

---

## Error Handling

All endpoints return standardized error responses:

**Authentication Error (401):**
```json
{
  "message": "Authentication required"
}
```

**Authorization Error (403):**
```json
{
  "message": "You do not have permission to access this resource."
}
```

**Not Found Error (404):**
```json
{
  "message": "Employee EMP001 not found"
}
```

**Validation Error (400):**
```json
{
  "message": "person_type must be 'employee' or 'customer'"
}
```

**Server Error (500):**
```json
{
  "message": "Internal server error",
  "error": "Detailed error message"
}
```

---

## Security Features

1. **Multi-Tenant Isolation**: HR users can only access data for their own company
2. **Role-Based Access**: Requires `hr`, `admin`, or `super-admin` role
3. **Audit Logging**: All retry operations are logged with user information
4. **Input Validation**: All inputs are validated and sanitized
5. **Company Verification**: Person records are verified to belong to the user's company

---

## Usage Examples

### Check Employee Sync Status
```bash
curl -X GET "https://api.kazisync.com/api/hr/sync-status/employee/EMP001" \
  -H "Authorization: Bearer <jwt_token>"
```

### View All Failed Customer Syncs
```bash
curl -X GET "https://api.kazisync.com/api/hr/sync-failures?person_type=customer" \
  -H "Authorization: Bearer <jwt_token>"
```

### Retry Failed Sync for Specific Device
```bash
curl -X POST "https://api.kazisync.com/api/hr/retry-sync" \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "person_type": "employee",
    "person_id": "EMP001",
    "device_sn": "AYTI10087992"
  }'
```

---

## Integration Notes

- These endpoints complement the existing super-admin command management API
- Failed commands are automatically retried by the background job (`SendOrderJob`) up to 15 times
- Device connectivity status is checked in real-time via WebSocket connections
- Commands are reset to initial state (`status=0`, `send_status=0`, `err_count=0`) for retry
- The background job will automatically pick up reset commands and attempt to send them to devices
