# Leave Management Edge Cases & Production Guide

## 🚨 Production Edge Cases Handled

### 1. Employee Data Issues

#### **Missing/Invalid Hire Dates**
- **Problem**: Employee created without hire_date, hire date set to future, multiple changes
- **Solution**: 
  - Automatic leave balance recalculation on hire_date changes
  - Validation prevents future hire dates
  - Audit trail tracks all hire_date changes

#### **Employee Status Changes**
- **Problem**: Active → Inactive → Active (rehired), terminated with pending leave
- **Solution**:
  - Leave balances preserved during status changes
  - Pending requests handled appropriately
  - Audit trail maintains complete history

### 2. Leave Balance Corruption

#### **Data Inconsistency**
- **Problem**: Negative balances, incorrect calculations, duplicate records
- **Solution**:
  - Daily validation jobs detect and fix inconsistencies
  - Database constraints prevent negative balances
  - Duplicate detection and removal tools

#### **Year-End Rollover Issues**
- **Problem**: Carryover limits exceeded, balances not reset
- **Solution**:
  - Automated compliance checks for Rwanda law (max 6 days carryover)
  - Monthly compliance reports
  - Automatic year-end processing

### 3. Leave Request Chaos

#### **Approval Workflow Breakdown**
- **Problem**: Approver leaves, multiple approvals, orphaned requests
- **Solution**:
  - Flexible approval workflow system
  - Request reassignment capabilities
  - Timeout handling for pending requests

#### **Retroactive Changes**
- **Problem**: Past date requests, cancelled approved leave
- **Solution**:
  - Audit trail tracks all changes
  - Balance adjustments with reasons
  - Payroll integration considerations

## 🛠️ Emergency Tools

### Leave Balance Doctor API

**Endpoint**: `POST /api/leave/balances/doctor`

**Actions Available**:
- `initialize_missing`: Create missing leave balances
- `fix_inconsistencies`: Fix negative balances and calculation errors
- `remove_duplicates`: Remove duplicate balance records
- `recalculate_all`: Recalculate all balances based on current policies

**Sample Request**:
```json
{
    "company_id": "uuid",
    "employee_id": "uuid",  // optional
    "year": 2024,
    "actions": ["initialize_missing", "fix_inconsistencies"],
    "dry_run": true  // optional - preview changes without applying
}
```

### Health Check API

**Endpoint**: `GET /api/leave/balances/health-check`

**Purpose**: Diagnose leave balance issues without making changes

**Sample Response**:
```json
{
    "code": 100,
    "extend": {
        "health_report": {
            "company_id": "uuid",
            "year": 2024,
            "summary": {
                "total_employees": 50,
                "employees_with_balances": 45,
                "employees_missing_balances": 3,
                "negative_balances": 2,
                "inconsistent_calculations": 1,
                "duplicate_balances": 0
            },
            "issues_found": [...]
        }
    }
}
```

## 📊 Audit Trail System

### Audit Log Model
- Tracks all leave-related changes
- Stores old/new values for comparison
- Records user, IP address, timestamp
- Supports filtering and reporting

### Audit API Endpoints

#### Get Employee Audit Trail
```
GET /api/leave/audit/employee/{employee_id}?company_id=uuid&limit=50&action_type=balance_created
```

#### Get Recent Activity
```
GET /api/leave/audit/recent?company_id=uuid&hours=24&limit=100
```

#### Get Audit Summary
```
GET /api/leave/audit/summary?company_id=uuid&days=7
```

## 🔄 Automated Maintenance Jobs

### Daily Jobs (2:00 AM)
- **Balance Validation**: Check for inconsistencies across all companies
- **Auto-Heal Balances**: Create missing balances for employees with new hire dates
- **Data Integrity**: Fix minor calculation errors

### Weekly Jobs (Sunday 3:00 AM)
- **Duplicate Cleanup**: Remove duplicate balance records
- **Audit Log Cleanup**: Archive logs older than 2 years
- **Performance Optimization**: Update database statistics

### Monthly Jobs
- **Compliance Check**: Verify Rwanda labor law compliance
- **Carryover Validation**: Check annual leave carryover limits
- **Policy Alignment**: Ensure balances match current policies

## 🚀 Production Deployment Checklist

### 1. Database Setup
- [ ] Run central database migrations: `alembic upgrade head`
- [ ] Run tenant database migrations: `alembic -c alembic_company.ini upgrade head`
- [ ] Verify new models are created: `leave_audit_logs`

### 2. Rwanda Leave Setup
- [ ] Run Rwanda setup script: `python setup_rwanda_leave_policies.py {company_id}`
- [ ] Initialize employee balances: `python initialize_employee_leave_balances.py {company_id} 2024`
- [ ] Verify leave types and policies are created

### 3. Maintenance Jobs
- [ ] Start maintenance scheduler in production
- [ ] Configure monitoring for job failures
- [ ] Set up alerts for compliance violations

### 4. Testing Sequence
- [ ] Test Leave Balance Doctor with dry_run=true
- [ ] Test hire_date changes trigger balance recalculation
- [ ] Verify audit trail is working
- [ ] Test health check endpoint

## 📋 Common Production Scenarios

### Scenario 1: Employee Added Without Hire Date
1. HR creates employee record
2. Employee shows in system but no leave balances
3. HR later updates hire_date
4. **Automatic**: System recalculates and creates leave balances
5. **Audit**: All changes logged with timestamps

### Scenario 2: Negative Leave Balance
1. Employee takes leave exceeding balance
2. **Detection**: Daily validation job identifies negative balance
3. **Alert**: HR receives notification
4. **Resolution**: Use Balance Doctor to fix or adjust manually

### Scenario 3: Duplicate Balances
1. **Detection**: Weekly cleanup job finds duplicates
2. **Resolution**: Automatically removes duplicates (keeps latest)
3. **Audit**: Logs which records were removed and why

### Scenario 4: Policy Change Mid-Year
1. Rwanda updates leave policies
2. **Update**: Central database policies updated
3. **Recalculation**: Use Balance Doctor to recalculate all balances
4. **Compliance**: Monthly job verifies all companies comply

## 🔧 Troubleshooting Guide

### Issue: Employee Has No Leave Balances
**Diagnosis**: Check hire_date, run health check
**Solution**: Use Balance Doctor with `initialize_missing` action

### Issue: Incorrect Leave Calculations
**Diagnosis**: Compare with policy, check audit trail
**Solution**: Use Balance Doctor with `recalculate_all` action

### Issue: System Performance Slow
**Diagnosis**: Check audit log size, database indexes
**Solution**: Run weekly cleanup, optimize queries

### Issue: Compliance Violations
**Diagnosis**: Run monthly compliance check
**Solution**: Use Balance Doctor to fix violations, update policies

## 📈 Monitoring & Alerts

### Key Metrics to Monitor
- Number of employees without leave balances
- Negative balance occurrences
- Audit log growth rate
- Maintenance job success rate
- API response times

### Alert Conditions
- More than 5% of employees missing balances
- Any negative balances detected
- Maintenance job failures
- Compliance violations found
- API error rate > 1%

## 🔐 Security Considerations

### Access Control
- Balance Doctor requires admin/hr role
- Audit trails require admin/hr role
- All actions logged with user identification

### Data Protection
- Audit logs contain sensitive employee data
- Automatic cleanup after 2 years
- IP address and user agent tracking

### Compliance
- Rwanda labor law compliance built-in
- Audit trail for legal requirements
- Data retention policies enforced
