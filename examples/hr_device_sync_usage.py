"""
Usage examples for HR Device Synchronization API.

This script demonstrates how to use the new HR-accessible endpoints
for managing employee/customer device synchronization.
"""

import requests
import json
from datetime import datetime


class HRDeviceSyncClient:
    """Client for interacting with HR Device Sync API."""
    
    def __init__(self, base_url, jwt_token):
        """Initialize the client.
        
        Args:
            base_url: Base URL of the KaziSync API (e.g., 'https://api.kazisync.com')
            jwt_token: JWT token for HR user authentication
        """
        self.base_url = base_url.rstrip('/')
        self.headers = {
            'Authorization': f'Bearer {jwt_token}',
            'Content-Type': 'application/json'
        }
    
    def get_person_sync_status(self, person_type, person_id):
        """Get device synchronization status for a specific person.
        
        Args:
            person_type: 'employee' or 'customer'
            person_id: Employee ID or Customer ID
            
        Returns:
            dict: Sync status response
        """
        url = f"{self.base_url}/api/hr/sync-status/{person_type}/{person_id}"
        response = requests.get(url, headers=self.headers)
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"Error {response.status_code}: {response.text}")
            return None
    
    def get_company_sync_failures(self, person_type=None, device_sn=None):
        """Get all failed synchronizations for the company.
        
        Args:
            person_type: Optional filter by 'employee' or 'customer'
            device_sn: Optional filter by device serial number
            
        Returns:
            dict: Sync failures response
        """
        url = f"{self.base_url}/api/hr/sync-failures"
        params = {}
        
        if person_type:
            params['person_type'] = person_type
        if device_sn:
            params['device_sn'] = device_sn
        
        response = requests.get(url, headers=self.headers, params=params)
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"Error {response.status_code}: {response.text}")
            return None
    
    def retry_person_sync(self, person_type, person_id, device_sn=None, command_ids=None):
        """Retry failed synchronization for a person.
        
        Args:
            person_type: 'employee' or 'customer'
            person_id: Employee ID or Customer ID
            device_sn: Optional specific device to retry
            command_ids: Optional specific command IDs to retry
            
        Returns:
            dict: Retry response
        """
        url = f"{self.base_url}/api/hr/retry-sync"
        data = {
            'person_type': person_type,
            'person_id': person_id
        }
        
        if device_sn:
            data['device_sn'] = device_sn
        if command_ids:
            data['command_ids'] = command_ids
        
        response = requests.post(url, headers=self.headers, json=data)
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"Error {response.status_code}: {response.text}")
            return None


def example_usage():
    """Demonstrate usage of the HR Device Sync API."""
    
    # Initialize client (replace with your actual API URL and token)
    client = HRDeviceSyncClient(
        base_url="https://api.kazisync.com",
        jwt_token="your_hr_jwt_token_here"
    )
    
    print("=== HR Device Synchronization API Examples ===\n")
    
    # Example 1: Check employee sync status
    print("1. Checking employee sync status...")
    employee_status = client.get_person_sync_status('employee', 'EMP001')
    
    if employee_status:
        print(f"Employee: {employee_status['person_name']}")
        print(f"Total devices: {employee_status['sync_summary']['total_devices']}")
        print(f"Failed devices: {employee_status['sync_summary']['failed_devices']}")
        
        # Show failed commands
        for device in employee_status['device_sync_details']:
            failed_commands = [cmd for cmd in device['commands'] if cmd['status'] == 'failed']
            if failed_commands:
                print(f"  Device {device['device_sn']} has {len(failed_commands)} failed commands")
                for cmd in failed_commands:
                    print(f"    - {cmd['biometric_type']} (err_count: {cmd['err_count']})")
    
    print("\n" + "="*50 + "\n")
    
    # Example 2: Check customer sync status
    print("2. Checking customer sync status...")
    customer_status = client.get_person_sync_status('customer', '550e8400-e29b-41d4-a716-446655440000')
    
    if customer_status:
        print(f"Customer: {customer_status['person_name']}")
        print(f"Sync summary: {customer_status['sync_summary']}")
    
    print("\n" + "="*50 + "\n")
    
    # Example 3: View all company sync failures
    print("3. Viewing all company sync failures...")
    failures = client.get_company_sync_failures()
    
    if failures:
        print(f"Total failed syncs: {failures['total_failed_syncs']}")
        print(f"Failed employees: {failures['failed_by_type']['employees']}")
        print(f"Failed customers: {failures['failed_by_type']['customers']}")
        
        print("\nFailed synchronizations:")
        for failure in failures['failures'][:5]:  # Show first 5
            print(f"  - {failure['person_type'].title()}: {failure['person_name']}")
            print(f"    Device: {failure['device_sn']}")
            print(f"    Failed commands: {failure['failed_commands']}")
            print(f"    Biometric types: {', '.join(failure['biometric_types_failed'])}")
            print(f"    Oldest failure: {failure['oldest_failure']}")
            print()
    
    print("\n" + "="*50 + "\n")
    
    # Example 4: View failures for specific device
    print("4. Viewing failures for specific device...")
    device_failures = client.get_company_sync_failures(device_sn='AYTI10087992')
    
    if device_failures:
        print(f"Failures for device AYTI10087992: {device_failures['total_failed_syncs']}")
    
    print("\n" + "="*50 + "\n")
    
    # Example 5: View only employee failures
    print("5. Viewing only employee failures...")
    employee_failures = client.get_company_sync_failures(person_type='employee')
    
    if employee_failures:
        print(f"Employee failures: {employee_failures['total_failed_syncs']}")
    
    print("\n" + "="*50 + "\n")
    
    # Example 6: Retry sync for specific employee
    print("6. Retrying sync for specific employee...")
    retry_result = client.retry_person_sync('employee', 'EMP001')
    
    if retry_result:
        print(f"Status: {retry_result['status']}")
        print(f"Message: {retry_result['message']}")
        print(f"Commands reset: {retry_result['commands_reset']}")
        print(f"Devices affected: {retry_result['devices_affected']}")
        
        if retry_result['warnings']:
            print("Warnings:")
            for warning in retry_result['warnings']:
                print(f"  - {warning}")
    
    print("\n" + "="*50 + "\n")
    
    # Example 7: Retry sync for specific device only
    print("7. Retrying sync for specific employee on specific device...")
    retry_result = client.retry_person_sync(
        person_type='employee',
        person_id='EMP001',
        device_sn='AYTI10087992'
    )
    
    if retry_result:
        print(f"Reset {retry_result['commands_reset']} commands for device AYTI10087992")
    
    print("\n" + "="*50 + "\n")
    
    # Example 8: Retry specific command IDs
    print("8. Retrying specific command IDs...")
    retry_result = client.retry_person_sync(
        person_type='employee',
        person_id='EMP001',
        command_ids=[456, 789]
    )
    
    if retry_result:
        print(f"Reset specific commands: {retry_result['commands_reset']}")
        for detail in retry_result['reset_details']:
            print(f"  - Command {detail['command_id']}: {detail['biometric_type']} on {detail['device_sn']}")


def monitoring_workflow():
    """Example workflow for monitoring and fixing sync issues."""
    
    client = HRDeviceSyncClient(
        base_url="https://api.kazisync.com",
        jwt_token="your_hr_jwt_token_here"
    )
    
    print("=== Sync Monitoring Workflow ===\n")
    
    # Step 1: Check for any failures
    print("Step 1: Checking for sync failures...")
    failures = client.get_company_sync_failures()
    
    if not failures or failures['total_failed_syncs'] == 0:
        print("✅ No sync failures found!")
        return
    
    print(f"⚠️  Found {failures['total_failed_syncs']} failed synchronizations")
    
    # Step 2: Analyze failures by type
    print("\nStep 2: Analyzing failures...")
    print(f"Employee failures: {failures['failed_by_type']['employees']}")
    print(f"Customer failures: {failures['failed_by_type']['customers']}")
    
    # Step 3: Fix failures automatically
    print("\nStep 3: Attempting to fix failures...")
    
    for failure in failures['failures']:
        person_type = failure['person_type']
        person_id = failure['person_id']
        person_name = failure['person_name']
        device_sn = failure['device_sn']
        
        print(f"\nRetrying sync for {person_type} {person_name} on device {device_sn}...")
        
        retry_result = client.retry_person_sync(
            person_type=person_type,
            person_id=person_id,
            device_sn=device_sn
        )
        
        if retry_result and retry_result['status'] == 'success':
            print(f"✅ Successfully reset {retry_result['commands_reset']} commands")
            
            if retry_result['warnings']:
                for warning in retry_result['warnings']:
                    print(f"⚠️  {warning}")
        else:
            print(f"❌ Failed to retry sync for {person_name}")
    
    print("\n✅ Sync monitoring workflow completed!")


if __name__ == "__main__":
    print("HR Device Sync API Usage Examples")
    print("=" * 40)
    print()
    print("This script demonstrates how to use the HR Device Sync API.")
    print("Please update the base_url and jwt_token in the script before running.")
    print()
    print("Uncomment the function calls below to run the examples:")
    print()
    
    # Uncomment these lines to run the examples:
    # example_usage()
    # monitoring_workflow()
    
    print("Examples are commented out. Please update credentials and uncomment to run.")
