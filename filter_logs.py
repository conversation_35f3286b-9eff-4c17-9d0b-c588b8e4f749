#!/usr/bin/env python3
"""
Log filtering utility to help debug announcement creation issues
by filtering out device synchronization logs.
"""

import sys
import re
from datetime import datetime

def filter_logs(log_file_path=None, follow=False):
    """
    Filter out device synchronization logs and show only relevant application logs.
    
    Args:
        log_file_path: Path to log file (default: app.log)
        follow: Whether to follow the log file like 'tail -f'
    """
    
    if log_file_path is None:
        log_file_path = "app.log"
    
    # Patterns to filter out (device sync related)
    filter_patterns = [
        r"SendOrderJob",
        r"find_pending_command",
        r"in_sending",
        r"database_name.*_db",
        r"device_status",
        r"websocket.*object",
        r"wd_list len:",
        r"items return:",
        r"The status of the device",
        r"key which is the device serial number",
        r"Sending the content:",
        r"return value of send:",
        r"updating the command status:",
        r"pending_command found",
        r"difference < 20"
    ]
    
    # Compile regex patterns for efficiency
    compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in filter_patterns]
    
    def should_filter_line(line):
        """Check if line should be filtered out."""
        for pattern in compiled_patterns:
            if pattern.search(line):
                return True
        return False
    
    try:
        if follow:
            # Follow mode (like tail -f)
            import time
            with open(log_file_path, 'r') as f:
                # Go to end of file
                f.seek(0, 2)
                while True:
                    line = f.readline()
                    if line:
                        if not should_filter_line(line):
                            print(line.rstrip())
                    else:
                        time.sleep(0.1)
        else:
            # Read existing logs
            with open(log_file_path, 'r') as f:
                for line in f:
                    if not should_filter_line(line):
                        print(line.rstrip())
                        
    except FileNotFoundError:
        print(f"Log file not found: {log_file_path}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\nLog filtering stopped.")
        sys.exit(0)

def show_recent_errors(log_file_path=None, lines=50):
    """Show recent error messages from logs."""
    
    if log_file_path is None:
        log_file_path = "app.log"
    
    error_patterns = [
        r"ERROR",
        r"Exception",
        r"Traceback",
        r"Failed to create announcement",
        r"Internal server error"
    ]
    
    compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in error_patterns]
    
    try:
        with open(log_file_path, 'r') as f:
            all_lines = f.readlines()
            
        # Get last N lines
        recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
        
        print(f"=== Recent Errors (last {lines} lines) ===")
        for line in recent_lines:
            for pattern in compiled_patterns:
                if pattern.search(line):
                    print(line.rstrip())
                    break
                    
    except FileNotFoundError:
        print(f"Log file not found: {log_file_path}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Filter application logs")
    parser.add_argument("--file", "-f", default="app.log", help="Log file path")
    parser.add_argument("--follow", action="store_true", help="Follow log file (like tail -f)")
    parser.add_argument("--errors", action="store_true", help="Show recent errors only")
    parser.add_argument("--lines", "-n", type=int, default=50, help="Number of recent lines to check for errors")
    
    args = parser.parse_args()
    
    if args.errors:
        show_recent_errors(args.file, args.lines)
    else:
        filter_logs(args.file, args.follow)
