#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix all announcement endpoints to use the same pattern as the working create_announcement endpoint.
"""

import re

def fix_announcements_file():
    """Fix all endpoints in the announcements_api.py file."""
    
    # Read the file
    with open('application/Routes/employees/announcements_api.py', 'r') as f:
        content = f.read()
    
    # Pattern 1: Replace g.company_id and g.user_id with get_user_context()
    pattern1 = r'company_id = g\.company_id\s*user_id = g\.user_id'
    replacement1 = '''# Get user context from token
        company_id, user_id, error_msg = get_user_context()
        if error_msg:
            return jsonify({"message": error_msg}), 401'''
    
    content = re.sub(pattern1, replacement1, content, flags=re.MULTILINE)
    
    # Pattern 2: Replace standalone g.company_id
    pattern2 = r'company_id = g\.company_id'
    replacement2 = '''# Get user context from token
        company_id, user_id, error_msg = get_user_context()
        if error_msg:
            return jsonify({"message": error_msg}), 401'''
    
    content = re.sub(pattern2, replacement2, content)
    
    # Pattern 3: Replace standalone g.user_id (when company_id is already handled)
    pattern3 = r'(\s+)user_id = g\.user_id'
    content = re.sub(pattern3, '', content)
    
    # Write the file back
    with open('application/Routes/employees/announcements_api.py', 'w') as f:
        f.write(content)
    
    print("Fixed all announcement endpoints!")

if __name__ == "__main__":
    fix_announcements_file()
