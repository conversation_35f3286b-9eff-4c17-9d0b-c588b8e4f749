#!/usr/bin/env python3
"""
Initialize Employee Leave Balances Script

This script initializes leave balances for all employees in a company
based on the established leave policies and employee service dates.

Run this script after setting up leave policies to give employees
their initial leave allocations.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db_connection
from application.Models.employees import Employee, LeaveType, LeavePolicy, LeaveBalance
from application.Models.country import Country
from application.Models.company import Company
from datetime import datetime, date
import uuid

def calculate_prorated_days(total_days, hire_date, year):
    """
    Calculate prorated leave days based on hire date.
    
    Args:
        total_days (float): Total annual leave days
        hire_date (date): Employee hire date
        year (int): Year for which to calculate
    
    Returns:
        float: Prorated leave days
    """
    if hire_date.year > year:
        return 0  # Employee not hired yet in this year
    
    if hire_date.year < year:
        return total_days  # Employee worked full year
    
    # Calculate prorated days for partial year
    days_in_year = 366 if year % 4 == 0 else 365
    start_of_year = date(year, 1, 1)
    days_worked = (date(year, 12, 31) - hire_date).days + 1
    
    prorated_days = (days_worked / days_in_year) * total_days
    return round(prorated_days, 2)

def initialize_employee_leave_balances(company_id, year=None):
    """
    Initialize leave balances for all employees in a company.
    
    Args:
        company_id (str): The UUID of the company
        year (int): Year to initialize balances for (defaults to current year)
    """
    
    if year is None:
        year = datetime.now().year
    
    # Get Rwanda country ID
    with app.app_context():
        rwanda = Country.get_country_by_code('RW')
        if not rwanda:
            print("❌ Rwanda not found in countries table.")
            return False
        
        rwanda_id = rwanda.country_id
        print(f"✅ Found Rwanda: {rwanda.name} (ID: {rwanda_id})")
    
    # Get company database
    database_name = Company.get_database_given_company_id(company_id)
    if not database_name:
        print(f"❌ Company with ID {company_id} not found")
        return False
    
    print(f"✅ Initializing leave balances for company database: {database_name}")
    print(f"📅 Year: {year}")
    
    with db_connection.get_session(database_name) as session:
        # Get all active employees
        employees = session.query(Employee).filter(Employee.status == 'active').all()
        if not employees:
            print("❌ No active employees found")
            return False
        
        print(f"👥 Found {len(employees)} active employees")
        
        # Get all leave types and their policies
        leave_types = session.query(LeaveType).all()
        if not leave_types:
            print("❌ No leave types found. Please run setup_rwanda_leave_policies.py first")
            return False
        
        print(f"📋 Found {len(leave_types)} leave types")
        
        created_balances = 0
        skipped_balances = 0
        
        for employee in employees:
            print(f"\n👤 Processing employee: {employee.first_name} {employee.last_name}")
            print(f"   Hire Date: {employee.hire_date}")
            
            # Calculate service days as of the year
            if employee.hire_date:
                service_start = employee.hire_date
                service_end = date(year, 12, 31)
                service_days = (service_end - service_start).days
            else:
                print(f"   ⚠️  No hire date found, assuming 0 service days")
                service_days = 0
            
            print(f"   Service Days: {service_days}")
            
            for leave_type in leave_types:
                # Get the policy for this leave type
                policy = LeavePolicy.get_policy_for_leave_type_and_country(
                    session, 
                    leave_type.leave_type_id, 
                    rwanda_id,
                    employee.gender if hasattr(employee, 'gender') else None
                )
                
                if not policy:
                    print(f"   ⚠️  No policy found for {leave_type.name}")
                    continue
                
                # Check if employee meets minimum service requirement
                if service_days < policy.min_service_days:
                    print(f"   ⏳ {leave_type.name}: Not eligible (needs {policy.min_service_days} service days)")
                    continue
                
                # Check if balance already exists
                existing_balance = LeaveBalance.get_employee_balance(
                    session,
                    employee.employee_id,
                    leave_type.leave_type_id,
                    year
                )
                
                if existing_balance:
                    print(f"   ✅ {leave_type.name}: Balance already exists ({existing_balance.total_days} days)")
                    skipped_balances += 1
                    continue
                
                # Calculate leave days
                total_days = policy.days_allowed
                
                # Apply proration if needed
                if policy.is_prorated and employee.hire_date:
                    total_days = calculate_prorated_days(policy.days_allowed, employee.hire_date, year)
                    print(f"   📊 {leave_type.name}: Prorated to {total_days} days")
                else:
                    print(f"   📊 {leave_type.name}: Full allocation {total_days} days")
                
                # Create the leave balance
                balance_data = {
                    'employee_id': employee.employee_id,
                    'leave_type_id': leave_type.leave_type_id,
                    'year': year,
                    'total_days': total_days,
                    'used_days': 0,
                    'pending_days': 0,
                    'carried_over_days': 0
                }
                
                balance = LeaveBalance.create_balance(session, **balance_data)
                if balance:
                    print(f"   ✅ Created {leave_type.name} balance: {total_days} days")
                    created_balances += 1
                else:
                    print(f"   ❌ Failed to create {leave_type.name} balance")
        
        print(f"\n🎉 Balance initialization completed!")
        print(f"   ✅ Created: {created_balances} new balances")
        print(f"   ⏭️  Skipped: {skipped_balances} existing balances")
        
        return True

def main():
    """Main function to run the initialization script."""
    print("💼 Employee Leave Balances Initialization Script")
    print("=" * 55)
    
    if len(sys.argv) < 2 or len(sys.argv) > 3:
        print("Usage: python initialize_employee_leave_balances.py <company_id> [year]")
        print("\nExamples:")
        print("  python initialize_employee_leave_balances.py 12345678-1234-1234-1234-123456789012")
        print("  python initialize_employee_leave_balances.py 12345678-1234-1234-1234-123456789012 2024")
        sys.exit(1)
    
    company_id = sys.argv[1]
    year = int(sys.argv[2]) if len(sys.argv) == 3 else datetime.now().year
    
    # Validate UUID format
    try:
        uuid.UUID(company_id)
    except ValueError:
        print(f"❌ Invalid company ID format: {company_id}")
        print("   Company ID must be a valid UUID")
        sys.exit(1)
    
    # Validate year
    current_year = datetime.now().year
    if year < 2020 or year > current_year + 1:
        print(f"❌ Invalid year: {year}")
        print(f"   Year must be between 2020 and {current_year + 1}")
        sys.exit(1)
    
    print(f"Initializing leave balances for company: {company_id}")
    print(f"Year: {year}")
    print()
    
    success = initialize_employee_leave_balances(company_id, year)
    
    if success:
        print("\n✅ Initialization completed successfully!")
        print("\n📝 Next steps:")
        print("1. Review employee leave balances in your admin panel")
        print("2. Make manual adjustments if needed using the adjust balance API")
        print("3. Inform employees about their leave entitlements")
        print("4. Set up leave request approval workflows")
    else:
        print("\n❌ Initialization failed. Please check the errors above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
