#!/usr/bin/env python3
"""
KaziSync HRMS - Enhanced Application Log Monitor

This script provides filtered, real-time monitoring of ALL application logs,
excluding WebSocket/device noise and focusing on business logic.

Features:
- Real-time log following (like tail -f)
- Filters out WebSocket and device communication noise
- Shows ALL important application logs (API calls, database operations, business logic)
- Highlights important logs (employee attendance, customer visits, errors, API requests)
- Color-coded output for easy reading
- Multiple monitoring modes (all, employee, customer, errors, api, database)
- Statistics tracking

Usage:
    # Monitor all application logs (recommended)
    python monitor_biometric_logs.py

    # Monitor only employee attendance
    python monitor_biometric_logs.py --mode employee

    # Monitor only API requests
    python monitor_biometric_logs.py --mode api

    # Monitor only errors
    python monitor_biometric_logs.py --mode errors

    # Show last 100 lines
    python monitor_biometric_logs.py --lines 100 --no-follow
"""

import sys
import re
import time
import argparse
from datetime import datetime
from collections import defaultdict

# ANSI color codes for terminal output
class Colors:
    RESET = '\033[0m'
    BOLD = '\033[1m'
    
    # Foreground colors
    BLACK = '\033[30m'
    RED = '\033[31m'
    GREEN = '\033[32m'
    YELLOW = '\033[33m'
    BLUE = '\033[34m'
    MAGENTA = '\033[35m'
    CYAN = '\033[36m'
    WHITE = '\033[37m'
    
    # Background colors
    BG_RED = '\033[41m'
    BG_GREEN = '\033[42m'
    BG_YELLOW = '\033[43m'
    BG_BLUE = '\033[44m'

# Patterns to EXCLUDE (device/WebSocket noise)
EXCLUDE_PATTERNS = [
    # Device communication patterns
    r'SendOrderJob',
    r'find_pending_command',
    r'in_sending',
    r'device_status',
    r'websocket.*object',
    r'wd_list len:',
    r'items return:',
    r'The status of the device',
    r'key which is the device serial number',
    r'Sending the content:',
    r'return value of send:',
    r'updating the command status:',
    r'pending_command found',
    r'difference < 20',

    # WebSocket patterns
    r'WebSocket',
    r'ws://',
    r'wss://',
    r'\bping\b',
    r'\bpong\b',
    r'heartbeat',
    r'device.*connected',
    r'device.*disconnected',

    # Device sync patterns (routine operations that create noise)
    r'Person to be inserted:',  # Old verbose logging
    r'Enroll info:',  # Device sync enrollment info
    r'Generated employee_id:',  # UUID generation during sync
    r'\[DEVICE_SYNC\].*Person already synced:',  # Already synced persons
    r'\[DEVICE_SYNC\].*Person already exists:',  # Already existing persons
    r'Device sync - Enroll info:',  # DEBUG level device sync

    # Additional noise patterns
    r'werkzeug.*GET /static/',  # Static file requests
    r'werkzeug.*GET /favicon.ico',  # Favicon requests
    r'werkzeug.*304 -',  # Not modified responses (unless error mode)
    r'Reloader',  # Flask reloader messages
]

# Patterns to INCLUDE (important business logic)
INCLUDE_PATTERNS = {
    # Biometric processing
    'app': r'\[APP\]',
    'unified_handler': r'\[UNIFIED_HANDLER\]',
    'person_type': r'\[PERSON_TYPE\]',
    'employee_handler': r'\[EMPLOYEE_HANDLER\]',
    'customer_handler': r'\[CUSTOMER_HANDLER\]',
    'device_sync': r'\[DEVICE_SYNC\].*(?:New person|Person inserted|Created employee|Failed|Error|Updated person)',  # Only important device sync events

    # Status indicators
    'error': r'ERROR|Exception|Traceback|❌|Failed',
    'warning': r'WARNING|⚠️',
    'success': r'✅|SUCCESS',
    'processing': r'🔄|STARTING RECORD PROCESSING|RECORD PROCESSING COMPLETE',

    # Business operations
    'attendance': r'Employee attendance|Attendance processed|attendance',
    'customer': r'Customer visit|Visit processed|customer',
    'leave': r'leave|Leave',
    'payroll': r'payroll|Payroll',
    'recruitment': r'recruitment|Recruitment',
    'performance': r'performance|Performance',
    'announcement': r'announcement|Announcement',

    # API operations
    'api_request': r'werkzeug.*POST|werkzeug.*PUT|werkzeug.*DELETE|werkzeug.*PATCH',
    'api_get': r'werkzeug.*GET.*200',
    'api_error': r'werkzeug.*[45]\d{2}',  # 4xx and 5xx errors

    # Database operations
    'database': r'INSERT|UPDATE|DELETE|SELECT.*FROM',
    'migration': r'alembic|migration|upgrade|downgrade',

    # Authentication & Authorization
    'auth': r'login|logout|authentication|authorization|token',
    'user': r'User|user.*created|user.*updated',

    # System operations
    'startup': r'Starting|Initializing|Loaded',
    'shutdown': r'Stopping|Shutting down',
    'config': r'Configuration|Config',
}

class LogMonitor:
    def __init__(self, log_file='app.log', mode='all', use_colors=True, show_all=False):
        self.log_file = log_file
        self.mode = mode
        self.use_colors = use_colors
        self.show_all = show_all  # If True, show all logs except excluded patterns
        self.stats = defaultdict(int)

        # Compile regex patterns
        self.exclude_patterns = [re.compile(p, re.IGNORECASE) for p in EXCLUDE_PATTERNS]
        self.include_patterns = {k: re.compile(v, re.IGNORECASE) for k, v in INCLUDE_PATTERNS.items()}
    
    def should_exclude(self, line):
        """Check if line should be excluded (device noise)."""
        for pattern in self.exclude_patterns:
            if pattern.search(line):
                return True
        return False
    
    def should_include(self, line):
        """Check if line should be included based on mode."""
        # If show_all mode, include everything that's not excluded
        if self.show_all:
            return True

        if self.mode == 'all':
            # Include if it matches any important pattern
            for pattern in self.include_patterns.values():
                if pattern.search(line):
                    return True
            return False

        elif self.mode == 'employee':
            # Include only employee-related logs
            return (self.include_patterns['employee_handler'].search(line) or
                    self.include_patterns['attendance'].search(line) or
                    (self.include_patterns['person_type'].search(line) and 'employee' in line.lower()))

        elif self.mode == 'customer':
            # Include only customer-related logs
            return (self.include_patterns['customer_handler'].search(line) or
                    self.include_patterns['customer'].search(line) or
                    (self.include_patterns['person_type'].search(line) and 'customer' in line.lower()))

        elif self.mode == 'errors':
            # Include only errors and warnings
            return (self.include_patterns['error'].search(line) or
                    self.include_patterns['warning'].search(line))

        elif self.mode == 'processing':
            # Include processing flow logs
            return (self.include_patterns['app'].search(line) or
                    self.include_patterns['unified_handler'].search(line) or
                    self.include_patterns['person_type'].search(line))

        elif self.mode == 'api':
            # Include API-related logs
            return (self.include_patterns['api_request'].search(line) or
                    self.include_patterns['api_get'].search(line) or
                    self.include_patterns['api_error'].search(line))

        elif self.mode == 'database':
            # Include database-related logs
            return (self.include_patterns['database'].search(line) or
                    self.include_patterns['migration'].search(line))

        elif self.mode == 'auth':
            # Include authentication-related logs
            return (self.include_patterns['auth'].search(line) or
                    self.include_patterns['user'].search(line))

        return False
    
    def colorize(self, line):
        """Add color to log line based on content."""
        if not self.use_colors:
            return line
        
        # Error lines (red)
        if '❌' in line or 'ERROR' in line or 'Exception' in line:
            return f"{Colors.RED}{line}{Colors.RESET}"
        
        # Warning lines (yellow)
        if '⚠️' in line or 'WARNING' in line:
            return f"{Colors.YELLOW}{line}{Colors.RESET}"
        
        # Success lines (green)
        if '✅' in line or 'SUCCESS' in line:
            return f"{Colors.GREEN}{line}{Colors.RESET}"
        
        # Processing lines (cyan)
        if '🔄' in line or 'STARTING RECORD PROCESSING' in line:
            return f"{Colors.CYAN}{Colors.BOLD}{line}{Colors.RESET}"
        
        # Section separators (blue)
        if '=' * 20 in line:
            return f"{Colors.BLUE}{line}{Colors.RESET}"
        
        # Handler prefixes (magenta)
        if '[EMPLOYEE_HANDLER]' in line:
            return f"{Colors.MAGENTA}{line}{Colors.RESET}"
        
        if '[CUSTOMER_HANDLER]' in line:
            return f"{Colors.MAGENTA}{line}{Colors.RESET}"
        
        # Default
        return line
    
    def update_stats(self, line):
        """Update statistics based on log line."""
        # Count by category
        if '[EMPLOYEE_HANDLER]' in line:
            self.stats['employee_records'] += 1
        if '[CUSTOMER_HANDLER]' in line:
            self.stats['customer_records'] += 1
        if '❌' in line or 'ERROR' in line:
            self.stats['errors'] += 1
        if '⚠️' in line or 'WARNING' in line:
            self.stats['warnings'] += 1
        if '✅' in line and 'SUCCESS' in line:
            self.stats['successes'] += 1

        # Count API requests
        if 'werkzeug' in line:
            if 'POST' in line:
                self.stats['api_post'] += 1
            elif 'GET' in line:
                self.stats['api_get'] += 1
            elif 'PUT' in line:
                self.stats['api_put'] += 1
            elif 'DELETE' in line:
                self.stats['api_delete'] += 1

        # Count total lines shown
        self.stats['total_lines'] += 1
    
    def print_header(self):
        """Print monitoring header."""
        print("=" * 80)
        print(f"  KaziSync HRMS - Application Log Monitor")
        print("=" * 80)
        print(f"  Log File: {self.log_file}")
        print(f"  Mode: {self.mode.upper()}")

        if self.show_all:
            print(f"  Showing: ALL application logs (excluding WebSocket/device noise)")
        else:
            mode_descriptions = {
                'all': 'All important business logic',
                'employee': 'Employee attendance only',
                'customer': 'Customer visits only',
                'errors': 'Errors and warnings only',
                'processing': 'Biometric processing flow',
                'api': 'API requests and responses',
                'database': 'Database operations',
                'auth': 'Authentication and authorization'
            }
            print(f"  Showing: {mode_descriptions.get(self.mode, 'Selected logs')}")

        print(f"  Filtering out: WebSocket traffic, device heartbeats, static files")
        print(f"  Press Ctrl+C to stop and show statistics")
        print("=" * 80)
        print()
    
    def print_stats(self):
        """Print statistics summary."""
        print("\n" + "=" * 80)
        print("  Monitoring Statistics")
        print("=" * 80)
        print(f"  Total Lines Shown: {self.stats['total_lines']}")
        print()
        print("  Biometric Processing:")
        print(f"    Employee Records: {self.stats['employee_records']}")
        print(f"    Customer Records: {self.stats['customer_records']}")
        print()
        print("  API Requests:")
        print(f"    GET: {self.stats['api_get']}")
        print(f"    POST: {self.stats['api_post']}")
        print(f"    PUT: {self.stats['api_put']}")
        print(f"    DELETE: {self.stats['api_delete']}")
        print()
        print("  Status:")
        print(f"    Successes: {self.stats['successes']}")
        print(f"    Warnings: {self.stats['warnings']}")
        print(f"    Errors: {self.stats['errors']}")
        print("=" * 80)
    
    def follow(self):
        """Follow log file in real-time (like tail -f)."""
        self.print_header()
        
        try:
            with open(self.log_file, 'r') as f:
                # Go to end of file
                f.seek(0, 2)
                
                while True:
                    line = f.readline()
                    if line:
                        # Filter out device noise
                        if self.should_exclude(line):
                            continue
                        
                        # Check if line should be included
                        if self.should_include(line):
                            self.update_stats(line)
                            print(self.colorize(line.rstrip()))
                    else:
                        time.sleep(0.1)
        
        except FileNotFoundError:
            print(f"❌ Log file not found: {self.log_file}")
            sys.exit(1)
        except KeyboardInterrupt:
            self.print_stats()
            print("\n✅ Monitoring stopped.")
            sys.exit(0)
    
    def show_recent(self, lines=50):
        """Show recent log lines."""
        print(f"=== Recent Logs (last {lines} lines) ===\n")
        
        try:
            with open(self.log_file, 'r') as f:
                all_lines = f.readlines()
                recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
                
                for line in recent_lines:
                    # Filter out device noise
                    if self.should_exclude(line):
                        continue
                    
                    # Check if line should be included
                    if self.should_include(line):
                        self.update_stats(line)
                        print(self.colorize(line.rstrip()))
            
            self.print_stats()
        
        except FileNotFoundError:
            print(f"❌ Log file not found: {self.log_file}")
            sys.exit(1)

def main():
    parser = argparse.ArgumentParser(
        description='Monitor KaziSync HRMS application logs (filters out WebSocket/device noise)',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Monitor all important application logs (recommended)
  python monitor_biometric_logs.py

  # Show ALL logs except WebSocket/device noise
  python monitor_biometric_logs.py --show-all

  # Monitor only employee attendance
  python monitor_biometric_logs.py --mode employee

  # Monitor only API requests
  python monitor_biometric_logs.py --mode api

  # Monitor only errors
  python monitor_biometric_logs.py --mode errors

  # Show last 100 lines without following
  python monitor_biometric_logs.py --lines 100 --no-follow

  # Monitor without colors
  python monitor_biometric_logs.py --no-color
        """
    )

    parser.add_argument('--file', '-f', default='app.log',
                       help='Log file to monitor (default: app.log)')

    parser.add_argument('--mode', '-m',
                       choices=['all', 'employee', 'customer', 'errors', 'processing', 'api', 'database', 'auth'],
                       default='all',
                       help='Monitoring mode (default: all)')

    parser.add_argument('--show-all', action='store_true',
                       help='Show ALL logs except WebSocket/device noise (overrides --mode)')

    parser.add_argument('--lines', '-n', type=int, default=50,
                       help='Number of recent lines to show (default: 50)')

    parser.add_argument('--no-follow', action='store_true',
                       help='Show recent lines and exit (don\'t follow)')

    parser.add_argument('--no-color', action='store_true',
                       help='Disable colored output')

    args = parser.parse_args()

    monitor = LogMonitor(
        log_file=args.file,
        mode=args.mode,
        use_colors=not args.no_color,
        show_all=args.show_all
    )

    if args.no_follow:
        monitor.show_recent(args.lines)
    else:
        monitor.follow()

if __name__ == '__main__':
    main()

