#!/bin/bash

# KaziSync HRMS - Biometric Record Processing Log Monitor
# This script filters out WebSocket/device noise and shows only important business logic logs

LOG_FILE="${1:-app.log}"

echo "================================================================================"
echo "  KaziSync HRMS - Biometric Processing Monitor"
echo "================================================================================"
echo "  Monitoring: $LOG_FILE"
echo "  Filtering out: WebSocket traffic, device heartbeats, routine device messages"
echo "  Showing: Employee attendance, customer visits, errors, warnings"
echo "  Press Ctrl+C to stop"
echo "================================================================================"
echo ""

# Use tail -f with grep to filter logs
# Include: [APP], [UNIFIED_HANDLER], [PERSON_TYPE], [EMPLOYEE_HANDLER], [CUSTOMER_HANDLER]
# Include: Errors (❌), Warnings (⚠️), Success (✅)
# Exclude: WebSocket, device status, SendOrderJob, etc.

tail -f "$LOG_FILE" | grep --line-buffered -v -E \
    'SendOrderJob|find_pending_command|in_sending|device_status|websocket.*object|wd_list len:|items return:|The status of the device|key which is the device serial number|Sending the content:|return value of send:|updating the command status:|pending_command found|difference < 20|WebSocket|ws://|wss://|ping|pong|heartbeat' \
    | grep --line-buffered -E \
    '\[APP\]|\[UNIFIED_HANDLER\]|\[PERSON_TYPE\]|\[EMPLOYEE_HANDLER\]|\[CUSTOMER_HANDLER\]|ERROR|WARNING|Exception|Traceback|✅|❌|⚠️|🔄|STARTING RECORD PROCESSING|RECORD PROCESSING COMPLETE|Employee attendance|Customer visit|Failed|Success'

