#!/usr/bin/env python3
"""
Biometric Sync Monitor
Real-time monitoring script for biometric synchronization logs
"""

import os
import time
import sys
from datetime import datetime

def monitor_biometric_sync_logs():
    """Monitor biometric sync logs in real-time"""
    
    log_file = "application/logs/biometric_sync.log"
    
    if not os.path.exists(log_file):
        print(f"Log file not found: {log_file}")
        print("The log file will be created when biometric sync operations start.")
        return
    
    print("=" * 80)
    print("🔍 BIOMETRIC SYNC MONITOR")
    print("=" * 80)
    print(f"Monitoring: {log_file}")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Press Ctrl+C to stop monitoring")
    print("=" * 80)
    
    try:
        # Read existing content first
        with open(log_file, 'r') as f:
            existing_lines = f.readlines()
            if existing_lines:
                print("\n📋 EXISTING LOG ENTRIES:")
                for line in existing_lines[-10:]:  # Show last 10 lines
                    print(line.strip())
                print("\n🔄 MONITORING NEW ENTRIES...")
        
        # Monitor new entries
        with open(log_file, 'r') as f:
            # Go to end of file
            f.seek(0, 2)
            
            while True:
                line = f.readline()
                if line:
                    # Color code different log levels
                    if "[ERROR]" in line:
                        print(f"🔴 {line.strip()}")
                    elif "[WARNING]" in line:
                        print(f"🟡 {line.strip()}")
                    elif "SUCCESS" in line:
                        print(f"🟢 {line.strip()}")
                    elif "START" in line:
                        print(f"🔵 {line.strip()}")
                    elif "SUMMARY" in line:
                        print(f"📊 {line.strip()}")
                    else:
                        print(f"ℹ️  {line.strip()}")
                else:
                    time.sleep(0.1)
                    
    except KeyboardInterrupt:
        print(f"\n\n⏹️  Monitoring stopped at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    except Exception as e:
        print(f"Error monitoring logs: {e}")

def show_log_summary():
    """Show summary of recent biometric sync operations"""
    
    log_file = "application/logs/biometric_sync.log"
    
    if not os.path.exists(log_file):
        print(f"Log file not found: {log_file}")
        return
    
    print("=" * 80)
    print("📊 BIOMETRIC SYNC SUMMARY")
    print("=" * 80)
    
    try:
        with open(log_file, 'r') as f:
            lines = f.readlines()
        
        # Count different types of operations
        stats = {
            'employee_distributions': 0,
            'name_sync_success': 0,
            'name_sync_failures': 0,
            'biometric_sync_success': 0,
            'biometric_sync_failures': 0,
            'face_images_synced': 0,
            'fingerprints_synced': 0
        }
        
        for line in lines:
            if "EMPLOYEE_DISTRIBUTION_START" in line:
                stats['employee_distributions'] += 1
            elif "NAME_SYNC_SUCCESS" in line:
                stats['name_sync_success'] += 1
            elif "NAME_SYNC_FAILURE" in line:
                stats['name_sync_failures'] += 1
            elif "BIOMETRIC_SYNC_SUCCESS" in line:
                stats['biometric_sync_success'] += 1
                if "Face Image" in line or "backupnum 50" in line:
                    stats['face_images_synced'] += 1
                elif "Fingerprint" in line or "backupnum" in line:
                    stats['fingerprints_synced'] += 1
            elif "BIOMETRIC_SYNC_FAILURE" in line:
                stats['biometric_sync_failures'] += 1
        
        print(f"📋 Employee Distributions Started: {stats['employee_distributions']}")
        print(f"✅ Name Sync Successful: {stats['name_sync_success']}")
        print(f"❌ Name Sync Failed: {stats['name_sync_failures']}")
        print(f"✅ Biometric Sync Successful: {stats['biometric_sync_success']}")
        print(f"❌ Biometric Sync Failed: {stats['biometric_sync_failures']}")
        print(f"👤 Face Images Synced: {stats['face_images_synced']}")
        print(f"👆 Fingerprints Synced: {stats['fingerprints_synced']}")
        
        # Show recent entries
        print(f"\n📝 RECENT LOG ENTRIES (Last 15):")
        for line in lines[-15:]:
            print(f"   {line.strip()}")
            
    except Exception as e:
        print(f"Error reading log summary: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "summary":
        show_log_summary()
    else:
        monitor_biometric_sync_logs()
