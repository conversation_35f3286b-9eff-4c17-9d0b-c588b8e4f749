#!/usr/bin/env python3
"""
Document Upload Monitoring Script
Specifically monitors document upload operations and filters out biometric noise.
"""

import sys
import os
import subprocess
import argparse
import time
import re
from datetime import datetime

def monitor_document_uploads(follow=False, lines=100):
    """Monitor document upload logs specifically."""
    print("=== Document Upload Monitoring ===")
    print("Filtering for document upload operations...")
    print("-" * 50)
    
    # Patterns to look for (document upload related)
    document_patterns = [
        r"DOCUMENT_UPLOAD_",
        r"DocumentStorageService",
        r"documents/upload",
        r"CompanyDocument",
        r"document.*upload",
        r"storage.*upload",
        r"folder.*validation",
        r"quota.*check",
        r"document.*creation"
    ]
    
    # Patterns to filter out (biometric noise)
    filter_patterns = [
        r"SendOrderJob",
        r"find_pending_command",
        r"in_sending",
        r"device_status",
        r"websocket.*object",
        r"wd_list len:",
        r"items return:",
        r"The status of the device",
        r"key which is the device serial number",
        r"Sending the content:",
        r"return value of send:",
        r"updating the command status:",
        r"pending_command found",
        r"difference < 20",
        r"biometric",
        r"fingerprint",
        r"face.*image"
    ]
    
    # Compile regex patterns
    doc_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in document_patterns]
    filter_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in filter_patterns]
    
    def is_document_related(line):
        """Check if line is document upload related."""
        return any(pattern.search(line) for pattern in doc_patterns)
    
    def should_filter_out(line):
        """Check if line should be filtered out (biometric noise)."""
        return any(pattern.search(line) for pattern in filter_patterns)
    
    def format_log_line(line):
        """Format log line with colors and highlighting."""
        line = line.rstrip()
        
        # Color coding
        if "ERROR" in line or "Failed" in line:
            return f"🔴 {line}"
        elif "WARNING" in line or "WARN" in line:
            return f"🟡 {line}"
        elif "SUCCESS" in line or "successful" in line:
            return f"🟢 {line}"
        elif "DOCUMENT_UPLOAD_" in line:
            return f"📄 {line}"
        elif "Storage" in line:
            return f"💾 {line}"
        else:
            return f"ℹ️  {line}"
    
    try:
        if follow:
            print("Following document upload logs in real-time (press Ctrl+C to stop)...")
            print("=" * 80)
            
            with open('app.log', 'r') as f:
                # Go to end of file
                f.seek(0, 2)
                
                while True:
                    line = f.readline()
                    if line:
                        # Show document-related logs, filter out biometric noise
                        if is_document_related(line) or (not should_filter_out(line) and 
                            any(keyword in line.lower() for keyword in ['error', 'exception', '500', 'failed'])):
                            print(format_log_line(line))
                    else:
                        time.sleep(0.1)
        else:
            print(f"Showing last {lines} lines related to document uploads...")
            print("=" * 80)
            
            with open('app.log', 'r') as f:
                all_lines = f.readlines()
                recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
                
                for line in recent_lines:
                    # Show document-related logs, filter out biometric noise
                    if is_document_related(line) or (not should_filter_out(line) and 
                        any(keyword in line.lower() for keyword in ['error', 'exception', '500', 'failed'])):
                        print(format_log_line(line))
                        
    except FileNotFoundError:
        print("app.log file not found")
        sys.exit(1)
    except KeyboardInterrupt:
        print(f"\n\n⏹️  Document upload monitoring stopped at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        sys.exit(0)

def show_upload_errors_only(lines=200):
    """Show only document upload errors."""
    print("=== Document Upload Errors Only ===")
    print("-" * 50)
    
    error_patterns = [
        r"DOCUMENT_UPLOAD_.*ERROR",
        r"DOCUMENT_UPLOAD_.*Failed",
        r"DOCUMENT_UPLOAD_.*Exception",
        r"DocumentStorageService.*error",
        r"documents/upload.*500",
        r"storage.*upload.*failed"
    ]
    
    compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in error_patterns]
    
    try:
        with open('app.log', 'r') as f:
            all_lines = f.readlines()
            recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
            
            error_count = 0
            for line in recent_lines:
                if any(pattern.search(line) for pattern in compiled_patterns):
                    print(f"🔴 {line.rstrip()}")
                    error_count += 1
            
            if error_count == 0:
                print("✅ No document upload errors found in recent logs!")
            else:
                print(f"\n📊 Found {error_count} document upload errors")
                
    except FileNotFoundError:
        print("app.log file not found")

def show_upload_summary():
    """Show summary of recent document upload operations."""
    print("=== Document Upload Summary ===")
    print("-" * 50)
    
    try:
        with open('app.log', 'r') as f:
            lines = f.readlines()
        
        # Count different types of operations
        stats = {
            'upload_attempts': 0,
            'upload_successes': 0,
            'upload_failures': 0,
            'storage_errors': 0,
            'quota_exceeded': 0,
            'folder_validation_errors': 0,
            'document_creation_errors': 0
        }
        
        # Look at last 1000 lines
        recent_lines = lines[-1000:] if len(lines) > 1000 else lines
        
        for line in recent_lines:
            if "DOCUMENT_UPLOAD_" in line:
                if "Starting document upload" in line:
                    stats['upload_attempts'] += 1
                elif "Document upload completed successfully" in line:
                    stats['upload_successes'] += 1
                elif "Storage upload failed" in line:
                    stats['storage_errors'] += 1
                elif "Storage quota exceeded" in line:
                    stats['quota_exceeded'] += 1
                elif "Invalid folder specified" in line:
                    stats['folder_validation_errors'] += 1
                elif "Document creation failed" in line:
                    stats['document_creation_errors'] += 1
                elif "Unexpected error during document upload" in line:
                    stats['upload_failures'] += 1
        
        # Display summary
        print(f"📊 Upload Attempts: {stats['upload_attempts']}")
        print(f"✅ Successful Uploads: {stats['upload_successes']}")
        print(f"❌ Failed Uploads: {stats['upload_failures']}")
        print(f"💾 Storage Errors: {stats['storage_errors']}")
        print(f"📦 Quota Exceeded: {stats['quota_exceeded']}")
        print(f"📁 Folder Validation Errors: {stats['folder_validation_errors']}")
        print(f"📄 Document Creation Errors: {stats['document_creation_errors']}")
        
        if stats['upload_attempts'] > 0:
            success_rate = (stats['upload_successes'] / stats['upload_attempts']) * 100
            print(f"\n📈 Success Rate: {success_rate:.1f}%")
        
    except FileNotFoundError:
        print("app.log file not found")

def main():
    parser = argparse.ArgumentParser(description="Monitor document upload operations")
    parser.add_argument("--follow", "-f", action="store_true", 
                       help="Follow logs in real-time")
    parser.add_argument("--lines", "-n", type=int, default=100, 
                       help="Number of recent lines to show")
    parser.add_argument("--errors", "-e", action="store_true", 
                       help="Show upload errors only")
    parser.add_argument("--summary", "-s", action="store_true", 
                       help="Show upload summary statistics")
    
    args = parser.parse_args()
    
    if args.errors:
        show_upload_errors_only(args.lines)
    elif args.summary:
        show_upload_summary()
    else:
        monitor_document_uploads(args.follow, args.lines)

if __name__ == "__main__":
    main()
