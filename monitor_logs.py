#!/usr/bin/env python3
"""
Log monitoring utility to help debug announcement creation issues
by providing separate views for application and device logs.
"""

import sys
import os
import subprocess
import argparse
from datetime import datetime

def monitor_app_logs(follow=False, lines=50):
    """Monitor application logs (excluding device logs)."""
    print("=== Application Logs (excluding device operations) ===")
    
    if follow:
        print("Following app.log (press Ctrl+C to stop)...")
        try:
            # Use tail -f with grep to filter out device logs
            cmd = [
                'tail', '-f', 'app.log'
            ]
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            for line in iter(process.stdout.readline, ''):
                # Filter out device-related logs
                if not any(pattern in line.lower() for pattern in [
                    'sendorderjob', 'find_pending_command', 'in_sending', 
                    'database_name.*_db', 'device_status', 'websocket.*object',
                    'wd_list len:', 'items return:', 'the status of the device',
                    'key which is the device serial number', 'sending the content:',
                    'return value of send:', 'updating the command status:',
                    'pending_command found', 'difference < 20'
                ]):
                    print(line.rstrip())
                    
        except KeyboardInterrupt:
            process.terminate()
            print("\nStopped monitoring application logs.")
    else:
        # Show recent application logs
        try:
            with open('app.log', 'r') as f:
                all_lines = f.readlines()
                recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
                
                for line in recent_lines:
                    # Filter out device-related logs
                    if not any(pattern in line.lower() for pattern in [
                        'sendorderjob', 'find_pending_command', 'in_sending', 
                        'database_name.*_db', 'device_status', 'websocket.*object',
                        'wd_list len:', 'items return:', 'the status of the device',
                        'key which is the device serial number', 'sending the content:',
                        'return value of send:', 'updating the command status:',
                        'pending_command found', 'difference < 20'
                    ]):
                        print(line.rstrip())
        except FileNotFoundError:
            print("app.log file not found")

def monitor_device_logs(follow=False, lines=50):
    """Monitor device logs."""
    device_log_path = 'logs/device.log'
    
    print("=== Device Operation Logs ===")
    
    if not os.path.exists(device_log_path):
        print(f"Device log file not found: {device_log_path}")
        print("Device logs will be created when device operations occur.")
        return
    
    if follow:
        print("Following device.log (press Ctrl+C to stop)...")
        try:
            cmd = ['tail', '-f', device_log_path]
            subprocess.run(cmd)
        except KeyboardInterrupt:
            print("\nStopped monitoring device logs.")
    else:
        # Show recent device logs
        try:
            with open(device_log_path, 'r') as f:
                all_lines = f.readlines()
                recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
                
                for line in recent_lines:
                    print(line.rstrip())
        except FileNotFoundError:
            print(f"Device log file not found: {device_log_path}")

def show_errors_only(log_file='app.log', lines=100):
    """Show only error messages from logs."""
    print(f"=== Recent Errors from {log_file} ===")
    
    error_patterns = ['ERROR', 'Exception', 'Traceback', 'Failed', 'Error']
    
    try:
        with open(log_file, 'r') as f:
            all_lines = f.readlines()
            recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
            
            for line in recent_lines:
                if any(pattern in line for pattern in error_patterns):
                    print(line.rstrip())
    except FileNotFoundError:
        print(f"Log file not found: {log_file}")

def show_announcement_logs(lines=100):
    """Show logs related to announcement operations."""
    print("=== Announcement-related Logs ===")
    
    announcement_patterns = [
        'announcement', 'create_announcement', 'Creating announcement',
        'Failed to create announcement', 'Announcement created',
        '/api/announcements'
    ]
    
    try:
        with open('app.log', 'r') as f:
            all_lines = f.readlines()
            recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
            
            for line in recent_lines:
                if any(pattern.lower() in line.lower() for pattern in announcement_patterns):
                    print(line.rstrip())
    except FileNotFoundError:
        print("app.log file not found")

def main():
    parser = argparse.ArgumentParser(description="Monitor application and device logs")
    parser.add_argument("--type", "-t", choices=['app', 'device', 'errors', 'announcements'], 
                       default='app', help="Type of logs to monitor")
    parser.add_argument("--follow", "-f", action="store_true", 
                       help="Follow logs in real-time (like tail -f)")
    parser.add_argument("--lines", "-n", type=int, default=50, 
                       help="Number of recent lines to show")
    
    args = parser.parse_args()
    
    if args.type == 'app':
        monitor_app_logs(args.follow, args.lines)
    elif args.type == 'device':
        monitor_device_logs(args.follow, args.lines)
    elif args.type == 'errors':
        show_errors_only('app.log', args.lines)
    elif args.type == 'announcements':
        show_announcement_logs(args.lines)

if __name__ == "__main__":
    main()
