# Comprehensive Onboarding Management System

## 🎯 Overview

This comprehensive onboarding management system provides enterprise-grade employee onboarding capabilities with complete customization, automation, and analytics. The system is designed to give you a significant competitive advantage in the HRMS market.

## 🏗️ System Architecture

### **Multi-Tenant Design**
- **Tenant-Level Storage**: All onboarding data stored in company-specific tenant databases
- **Company Isolation**: Complete data separation between companies
- **Azure Blob Storage**: Document storage with company-specific folders
- **Scalable Architecture**: Supports unlimited companies and employees

### **Core Components**
1. **Workflow Engine** - Customizable onboarding workflows
2. **Task Management** - Automated task assignment and tracking
3. **Document Management** - Secure document collection and verification
4. **Asset Management** - Equipment and resource assignment
5. **Feedback System** - Multi-stakeholder feedback collection
6. **Analytics Engine** - Comprehensive reporting and insights

## 📊 Database Models

### **Core Models**
- `OnboardingWorkflow` - Workflow templates and configurations
- `OnboardingWorkflowTask` - Task templates within workflows
- `OnboardingDocumentRequirement` - Document requirements per workflow
- `OnboardingInstance` - Active onboarding processes
- `OnboardingTask` - Individual task instances
- `OnboardingDocument` - Document submissions and verification
- `OnboardingAsset` - Asset assignments and tracking
- `OnboardingFeedback` - Feedback and satisfaction data

### **Key Features**
- **Decimal Precision** - All financial calculations use Decimal type
- **Audit Trails** - Complete tracking of all changes
- **Soft Deletes** - Data preservation with deactivation
- **Version Control** - Document and workflow versioning
- **JSON Configuration** - Flexible structured data storage

## 🔧 API Endpoints

### **Workflow Management**

#### **Create Onboarding Workflow**
```bash
POST /api/onboarding/workflows
{
  "company_id": "uuid",
  "name": "Software Developer Onboarding",
  "description": "Complete onboarding process for software developers",
  "workflow_type": "TECHNICAL",
  "department": "Engineering",
  "position_level": "MID",
  "duration_days": 30,
  "auto_start": true,
  "requires_manager_approval": true,
  "requires_hr_approval": true,
  "send_welcome_email": true,
  "create_system_accounts": true,
  "assign_buddy": true,
  "schedule_orientation": true,
  "is_default": false
}

Response:
{
  "success": true,
  "workflow": {
    "workflow_id": "uuid",
    "name": "Software Developer Onboarding",
    "workflow_type": "TECHNICAL",
    "duration_days": 30,
    "is_active": true,
    "task_count": 0,
    "document_requirement_count": 0
  }
}
```

#### **Get All Workflows**
```bash
GET /api/onboarding/workflows?company_id={uuid}&include_inactive=false

Response:
{
  "success": true,
  "workflows": [
    {
      "workflow_id": "uuid",
      "name": "Software Developer Onboarding",
      "workflow_type": "TECHNICAL",
      "department": "Engineering",
      "duration_days": 30,
      "is_active": true,
      "is_default": false,
      "completion_stats": {
        "total_instances": 15,
        "completed_instances": 12,
        "completion_rate": 80.0,
        "average_completion_days": 25.5
      }
    }
  ]
}
```

#### **Add Task to Workflow**
```bash
POST /api/onboarding/workflows/{workflow_id}/tasks
{
  "company_id": "uuid",
  "task_name": "Setup Development Environment",
  "description": "Install and configure development tools",
  "instructions": "Follow the development setup guide...",
  "completion_criteria": "All tools installed and tested",
  "assigned_to_role": "IT",
  "due_days_from_start": 2,
  "estimated_duration_hours": 4,
  "is_mandatory": true,
  "is_blocking": true,
  "task_type": "SYSTEM",
  "task_category": "SETUP",
  "task_order": 1,
  "depends_on_tasks": [],
  "auto_assign": true,
  "send_reminder": true,
  "reminder_days_before": 1
}
```

#### **Add Document Requirement**
```bash
POST /api/onboarding/workflows/{workflow_id}/document-requirements
{
  "company_id": "uuid",
  "document_name": "National ID",
  "document_code": "NATIONAL_ID",
  "description": "Government issued national identification",
  "category": "IDENTITY",
  "is_mandatory": true,
  "is_sensitive": true,
  "requires_verification": true,
  "requires_original": false,
  "file_types_allowed": "pdf,jpg,jpeg,png",
  "max_file_size_mb": 5,
  "min_file_size_kb": 50,
  "max_files_count": 2,
  "validation_rules": {
    "check_expiry": true,
    "min_validity_months": 6
  },
  "expiry_check": true,
  "min_validity_days": 180,
  "display_order": 1,
  "help_text": "Upload clear photo of both sides of your national ID",
  "due_days_from_start": 3,
  "blocks_other_tasks": true,
  "auto_remind": true,
  "reminder_frequency_days": 1
}
```

### **Onboarding Process Management**

#### **Start Onboarding Process**
```bash
POST /api/onboarding/instances
{
  "company_id": "uuid",
  "employee_id": "uuid",
  "workflow_id": "uuid",  // Optional - uses default if not provided
  "start_date": "2025-02-01",
  "first_day_date": "2025-02-03",
  "assigned_hr": "hr_user_uuid",
  "assigned_manager": "manager_user_uuid",
  "assigned_buddy": "buddy_user_uuid"
}

Response:
{
  "success": true,
  "instance": {
    "instance_id": "uuid",
    "employee_id": "uuid",
    "workflow_id": "uuid",
    "status": "IN_PROGRESS",
    "start_date": "2025-02-01",
    "expected_completion_date": "2025-03-03",
    "completion_percentage": 0,
    "current_phase": "PRE_BOARDING",
    "total_tasks": 12,
    "completed_tasks": 0,
    "total_documents": 5,
    "submitted_documents": 0,
    "welcome_email_sent": true
  },
  "message": "Onboarding process started successfully"
}
```

#### **Get Onboarding Instance Details**
```bash
GET /api/onboarding/instances/{instance_id}?company_id={uuid}

Response:
{
  "success": true,
  "instance": {
    "instance_id": "uuid",
    "employee_id": "uuid",
    "status": "IN_PROGRESS",
    "completion_percentage": 45.5,
    "current_phase": "FIRST_WEEK",
    "days_since_start": 8,
    "is_overdue": false,
    "days_overdue": 0,
    "tasks": [
      {
        "task_id": "uuid",
        "task_name": "Complete Personal Information Form",
        "status": "COMPLETED",
        "assigned_to_role": "EMPLOYEE",
        "due_date": "2025-02-03",
        "completed_date": "2025-02-02T14:30:00",
        "is_overdue": false
      },
      {
        "task_id": "uuid",
        "task_name": "Setup Development Environment",
        "status": "IN_PROGRESS",
        "assigned_to_role": "IT",
        "due_date": "2025-02-05",
        "is_overdue": false,
        "can_be_started": true
      }
    ],
    "documents": [
      {
        "document_id": "uuid",
        "document_name": "National ID",
        "verification_status": "VERIFIED",
        "upload_date": "2025-02-02T10:15:00",
        "verified_date": "2025-02-02T16:20:00"
      }
    ],
    "next_tasks": [...],
    "overdue_tasks": []
  }
}
```

#### **Complete Task**
```bash
PUT /api/onboarding/tasks/{task_id}/complete
{
  "company_id": "uuid",
  "completion_notes": "Development environment successfully configured",
  "attachments": [
    "screenshot_dev_setup.png",
    "configuration_checklist.pdf"
  ]
}

Response:
{
  "success": true,
  "task": {
    "task_id": "uuid",
    "status": "COMPLETED",
    "completed_date": "2025-02-05T11:30:00",
    "completion_percentage": 100,
    "completion_notes": "Development environment successfully configured"
  },
  "message": "Task completed successfully"
}
```

#### **Upload Document**
```bash
POST /api/onboarding/documents/upload
Content-Type: multipart/form-data

{
  "company_id": "uuid",
  "instance_id": "uuid",
  "requirement_id": "uuid",
  "file": [binary file data],
  "document_title": "National ID - Front and Back",
  "document_description": "Clear photos of national ID both sides",
  "document_date": "2020-05-15",  // Issue date
  "expiry_date": "2030-05-15",
  "document_number": "ID123456789",
  "issuing_authority": "National Registration Bureau"
}

Response:
{
  "success": true,
  "document": {
    "document_id": "uuid",
    "original_filename": "national_id.pdf",
    "file_size_mb": 2.3,
    "verification_status": "PENDING",
    "upload_date": "2025-02-02T10:15:00",
    "virus_scan_status": "CLEAN",
    "download_url": "/api/onboarding/documents/uuid/download"
  },
  "message": "Document uploaded successfully"
}
```

#### **Verify Document**
```bash
PUT /api/onboarding/documents/{document_id}/verify
{
  "company_id": "uuid",
  "approved": true,
  "verification_notes": "Document is clear and valid. Expiry date confirmed."
}

Response:
{
  "success": true,
  "document": {
    "document_id": "uuid",
    "verification_status": "VERIFIED",
    "verified_date": "2025-02-02T16:20:00",
    "verification_notes": "Document is clear and valid. Expiry date confirmed."
  },
  "message": "Document verified successfully"
}
```

#### **Add Feedback**
```bash
POST /api/onboarding/instances/{instance_id}/feedback
{
  "company_id": "uuid",
  "feedback_type": "EMPLOYEE",
  "feedback_stage": "FIRST_WEEK",
  "feedback_category": "OVERALL",
  "feedback_title": "Great onboarding experience",
  "feedback_text": "The onboarding process has been very smooth and well-organized.",
  "overall_rating": 5,
  "process_rating": 5,
  "communication_rating": 4,
  "support_rating": 5,
  "training_rating": 4,
  "what_went_well": "Clear instructions, helpful buddy system, good communication",
  "what_could_improve": "Could use more technical training materials",
  "suggestions": "Add video tutorials for development setup",
  "would_recommend": true,
  "structured_responses": {
    "ease_of_process": 5,
    "clarity_of_instructions": 4,
    "support_quality": 5
  },
  "provider_role": "SOFTWARE_DEVELOPER",
  "is_anonymous": false,
  "requires_follow_up": false
}

Response:
{
  "success": true,
  "feedback": {
    "feedback_id": "uuid",
    "feedback_type": "EMPLOYEE",
    "overall_rating": 5,
    "average_rating": 4.6,
    "feedback_date": "2025-02-08T14:30:00",
    "onboarding_day": 7
  },
  "message": "Feedback added successfully"
}
```

### **Comprehensive Analytics**

#### **Overview Analytics**
```bash
GET /api/onboarding/analytics/overview?company_id={uuid}&period=month&year=2025&month=2

Response:
{
  "success": true,
  "data": {
    "period_info": {
      "period": "month",
      "year": 2025,
      "month": 2,
      "start_date": "2025-02-01",
      "end_date": "2025-02-28"
    },
    "overall_statistics": {
      "total_onboardings": 25,
      "completed_onboardings": 20,
      "in_progress_onboardings": 4,
      "overdue_onboardings": 1,
      "completion_rate": 80.0,
      "average_completion_days": 18.5,
      "on_time_completion_rate": 76.0
    },
    "workflow_breakdown": [
      {
        "workflow_name": "Software Developer Onboarding",
        "workflow_type": "TECHNICAL",
        "total_instances": 8,
        "completed_instances": 7,
        "completion_rate": 87.5,
        "average_completion_percentage": 92.3
      },
      {
        "workflow_name": "Sales Representative Onboarding",
        "workflow_type": "SALES",
        "total_instances": 6,
        "completed_instances": 5,
        "completion_rate": 83.33,
        "average_completion_percentage": 89.1
      }
    ],
    "department_breakdown": [
      {
        "department": "Engineering",
        "total_onboardings": 8,
        "completed_onboardings": 7,
        "completion_rate": 87.5,
        "average_completion_percentage": 92.3,
        "average_completion_days": 16.2
      },
      {
        "department": "Sales",
        "total_onboardings": 6,
        "completed_onboardings": 5,
        "completion_rate": 83.33,
        "average_completion_percentage": 89.1,
        "average_completion_days": 14.8
      }
    ],
    "task_statistics": [
      {
        "task_type": "DOCUMENT",
        "assigned_to_role": "EMPLOYEE",
        "total_tasks": 125,
        "completed_tasks": 118,
        "overdue_tasks": 3,
        "completion_rate": 94.4,
        "average_completion_days": 2.1
      },
      {
        "task_type": "SYSTEM",
        "assigned_to_role": "IT",
        "total_tasks": 50,
        "completed_tasks": 45,
        "overdue_tasks": 2,
        "completion_rate": 90.0,
        "average_completion_days": 3.5
      }
    ],
    "document_statistics": [
      {
        "verification_status": "VERIFIED",
        "count": 98
      },
      {
        "verification_status": "PENDING",
        "count": 12
      },
      {
        "verification_status": "REJECTED",
        "count": 5
      }
    ],
    "feedback_statistics": {
      "total_feedback": 18,
      "average_overall_rating": 4.3,
      "average_process_rating": 4.1,
      "average_communication_rating": 4.4,
      "average_support_rating": 4.2,
      "average_training_rating": 3.9,
      "recommendation_rate": 88.89,
      "follow_up_required": 2
    }
  }
}
```

#### **Performance Analytics**
```bash
GET /api/onboarding/analytics/performance?company_id={uuid}&department=Engineering&date_from=2025-01-01&date_to=2025-02-28

Response:
{
  "success": true,
  "data": {
    "filter_info": {
      "department": "Engineering",
      "date_from": "2025-01-01",
      "date_to": "2025-02-28",
      "total_instances_analyzed": 15
    },
    "completion_metrics": {
      "total_instances": 15,
      "completed_instances": 12,
      "completion_rate": 80.0,
      "overdue_instances": 2,
      "on_time_rate": 73.33,
      "average_completion_days": 19.2,
      "fastest_completion_days": 12,
      "slowest_completion_days": 28
    },
    "task_performance": {
      "total_tasks": 180,
      "completed_tasks": 165,
      "overdue_tasks": 8,
      "average_task_completion_time": 2.8
    },
    "document_performance": {
      "total_documents": 75,
      "verified_documents": 68,
      "pending_documents": 5,
      "rejected_documents": 2
    },
    "feedback_analysis": {
      "total_feedback_entries": 12,
      "average_overall_rating": 4.4,
      "average_process_rating": 4.2,
      "recommendation_rate": 91.67
    },
    "bottleneck_analysis": [
      {
        "task_type": "SYSTEM",
        "total_tasks": 45,
        "completion_rate": 86.67,
        "average_completion_days": 4.2,
        "overdue_tasks": 6,
        "bottleneck_score": 17.53
      },
      {
        "task_type": "TRAINING",
        "total_tasks": 30,
        "completion_rate": 90.0,
        "average_completion_days": 3.1,
        "overdue_tasks": 2,
        "bottleneck_score": 13.1
      }
    ]
  }
}
```

#### **Satisfaction Analytics**
```bash
GET /api/onboarding/analytics/satisfaction?company_id={uuid}&feedback_type=EMPLOYEE

Response:
{
  "success": true,
  "data": {
    "summary": {
      "total_feedback_entries": 45,
      "high_satisfaction_count": 32,
      "low_satisfaction_count": 3,
      "recommendation_rate": 86.67,
      "average_overall_rating": 4.2
    },
    "rating_distribution": {
      "overall_rating": {
        "1": 1,
        "2": 2,
        "3": 10,
        "4": 18,
        "5": 14
      },
      "process_rating": {
        "1": 0,
        "2": 3,
        "3": 12,
        "4": 20,
        "5": 10
      }
    },
    "sentiment_distribution": {
      "POSITIVE": 28,
      "NEGATIVE": 5,
      "NEUTRAL": 12
    },
    "stage_analysis": {
      "PRE_BOARDING": {
        "feedback_count": 8,
        "average_rating": 4.1
      },
      "FIRST_DAY": {
        "feedback_count": 12,
        "average_rating": 4.3
      },
      "FIRST_WEEK": {
        "feedback_count": 15,
        "average_rating": 4.2
      },
      "FIRST_MONTH": {
        "feedback_count": 10,
        "average_rating": 4.0
      }
    },
    "improvement_themes": {
      "total_suggestions": 23,
      "sample_suggestions": [
        "More technical training materials needed",
        "Buddy system could be more structured",
        "IT setup process could be faster",
        "Need better orientation materials"
      ]
    },
    "positive_themes": {
      "total_positive_feedback": 28,
      "sample_positive": [
        "Great communication throughout the process",
        "Very helpful HR team",
        "Clear instructions and expectations",
        "Smooth document submission process"
      ]
    }
  }
}
```

#### **Trends Analytics**
```bash
GET /api/onboarding/analytics/trends?company_id={uuid}&period=monthly&months_back=6

Response:
{
  "success": true,
  "data": {
    "period_type": "monthly",
    "periods_analyzed": 6,
    "trends": [
      {
        "period": "2024-09",
        "period_name": "September 2024",
        "total_onboardings": 18,
        "completed_onboardings": 15,
        "completion_rate": 83.33,
        "average_completion_days": 21.2,
        "task_completion_rate": 89.5,
        "document_verification_rate": 92.1,
        "average_feedback_rating": 4.0
      },
      {
        "period": "2024-10",
        "period_name": "October 2024",
        "total_onboardings": 22,
        "completed_onboardings": 19,
        "completion_rate": 86.36,
        "average_completion_days": 19.8,
        "task_completion_rate": 91.2,
        "document_verification_rate": 94.3,
        "average_feedback_rating": 4.1
      },
      {
        "period": "2024-11",
        "period_name": "November 2024",
        "total_onboardings": 25,
        "completed_onboardings": 22,
        "completion_rate": 88.0,
        "average_completion_days": 18.5,
        "task_completion_rate": 92.8,
        "document_verification_rate": 95.1,
        "average_feedback_rating": 4.2
      }
    ]
  }
}
```

## 📈 Analytics Capabilities

### **Overview Analytics**
- **Completion Rates** - Overall and by department/workflow
- **Time Metrics** - Average completion times and trends
- **Task Performance** - Task completion rates by type and role
- **Document Verification** - Document submission and verification rates
- **Workflow Effectiveness** - Workflow performance comparison
- **Department Analysis** - Cross-department onboarding metrics

### **Trend Analysis**
- **Monthly/Yearly Trends** - Historical performance tracking
- **Seasonal Patterns** - Identify peak onboarding periods
- **Improvement Tracking** - Monitor process improvements over time
- **Predictive Insights** - Forecast onboarding volumes and resources

### **Performance Metrics**
- **Bottleneck Analysis** - Identify process delays and inefficiencies
- **Task Type Performance** - Which tasks take longest/cause issues
- **Role Efficiency** - Performance by assigned role (HR, IT, Manager)
- **Completion Time Distribution** - Fast vs slow onboarding analysis

### **Satisfaction Analytics**
- **Rating Distributions** - 1-5 scale rating analysis
- **Feedback Themes** - Common improvement suggestions
- **Recommendation Rates** - Employee recommendation tracking
- **Stage-wise Satisfaction** - Satisfaction by onboarding stage
- **Sentiment Analysis** - Positive/negative feedback trends

## 🎯 Competitive Advantages

### **1. Complete Customization**
- **Company-Specific Workflows** - Each company defines their own process
- **Flexible Document Requirements** - Any document type, any validation rules
- **Custom Task Types** - Unlimited task categories and assignments
- **Configurable Timelines** - Flexible due dates and dependencies

### **2. Enterprise-Grade Features**
- **Multi-Level Approvals** - Manager and HR approval workflows
- **Asset Management** - Complete equipment tracking
- **Audit Trails** - Full compliance and tracking
- **Security Controls** - Role-based access and document security

### **3. Advanced Analytics**
- **Real-Time Dashboards** - Live onboarding status tracking
- **Predictive Analytics** - Forecast completion times and bottlenecks
- **ROI Calculations** - Measure onboarding investment returns
- **Benchmarking** - Compare against industry standards

### **4. International Flexibility**
- **Universal Process** - Works in any country/culture
- **Local Compliance** - Companies configure their own requirements
- **Multi-Language Support** - Ready for localization
- **Cultural Adaptation** - Flexible to local business practices

### **5. Cost Effectiveness**
- **Automated Workflows** - Reduce manual HR work by 70%
- **Faster Onboarding** - 50% reduction in time-to-productivity
- **Better Retention** - Improved employee experience and satisfaction
- **Compliance Assurance** - Automated compliance tracking

## 💰 Pricing Strategy

### **Onboarding Module Pricing**
- **Add-on to existing plans**: +$8 per employee/month
- **Standalone module**: $12 per employee/month
- **Setup and configuration**: $3,000-8,000 one-time
- **Training and support**: $200-500 per session

### **Value Proposition**
- **60-70% cost savings** vs international solutions (Workday, SAP)
- **Complete customization** usually only available in enterprise solutions
- **Faster implementation** - Days instead of months
- **No vendor lock-in** - Full data ownership and portability

## 🚀 Implementation Benefits

### **For HR Teams**
- **Streamlined Process** - Automated task assignment and tracking
- **Compliance Assurance** - Never miss required documents or steps
- **Analytics Insights** - Data-driven process improvements
- **Reduced Workload** - 70% reduction in manual onboarding tasks

### **For New Employees**
- **Clear Expectations** - Know exactly what's needed and when
- **Self-Service Portal** - Upload documents and track progress
- **Better Experience** - Smooth, professional onboarding process
- **Faster Integration** - Get productive faster with structured approach

### **For Managers**
- **Visibility** - Real-time view of team onboarding progress
- **Automated Reminders** - Never miss onboarding tasks
- **Performance Tracking** - Monitor onboarding effectiveness
- **Resource Planning** - Predict onboarding resource needs

### **For IT Teams**
- **Asset Tracking** - Complete equipment assignment and return tracking
- **Automated Provisioning** - Integrate with existing IT systems
- **Security Compliance** - Ensure proper access controls
- **Audit Trails** - Complete tracking for security reviews

## 📋 Implementation Roadmap

### **Phase 1: Foundation (Month 1)**
- Deploy core models and database schema
- Implement basic workflow and task management
- Set up document upload and storage
- Create basic analytics dashboard

### **Phase 2: Advanced Features (Month 2)**
- Add asset management capabilities
- Implement advanced workflow features
- Create comprehensive analytics
- Add feedback and satisfaction tracking

### **Phase 3: Integration (Month 3)**
- Integrate with existing HRMS modules
- Add email and notification systems
- Implement mobile-responsive interfaces
- Create reporting and export capabilities

### **Phase 4: Optimization (Month 4)**
- Performance optimization and caching
- Advanced analytics and AI insights
- Third-party integrations (Slack, Teams, etc.)
- Mobile app development

## 🏆 Market Positioning

### **Target Market**
- **Primary**: African SMEs (50-500 employees)
- **Secondary**: International companies with African operations
- **Tertiary**: Companies seeking cost-effective enterprise features

### **Competitive Positioning**
- **"Enterprise onboarding at SME prices"**
- **"The only HRMS built specifically for African businesses"**
- **"Complete customization without the complexity"**
- **"Faster implementation, better results"**

## 🎬 Usage Scenarios

### **Scenario 1: Tech Startup Onboarding**
```bash
# 1. Create workflow for developers
POST /api/onboarding/workflows
{
  "name": "Software Developer Onboarding",
  "workflow_type": "TECHNICAL",
  "department": "Engineering",
  "duration_days": 21,
  "assign_buddy": true
}

# 2. Add technical tasks
POST /api/onboarding/workflows/{workflow_id}/tasks
{
  "task_name": "Setup Development Environment",
  "assigned_to_role": "IT",
  "task_type": "SYSTEM",
  "due_days_from_start": 1
}

# 3. Add document requirements
POST /api/onboarding/workflows/{workflow_id}/document-requirements
{
  "document_name": "GitHub Profile",
  "document_code": "GITHUB_PROFILE",
  "file_types_allowed": "pdf,png,jpg",
  "is_mandatory": true
}

# 4. Start onboarding for new developer
POST /api/onboarding/instances
{
  "employee_id": "new_dev_uuid",
  "workflow_id": "tech_workflow_uuid",
  "assigned_buddy": "senior_dev_uuid"
}
```

### **Scenario 2: Manufacturing Company Onboarding**
```bash
# 1. Create safety-focused workflow
POST /api/onboarding/workflows
{
  "name": "Factory Worker Onboarding",
  "workflow_type": "SAFETY",
  "department": "Production",
  "duration_days": 14,
  "requires_manager_approval": true
}

# 2. Add safety training tasks
POST /api/onboarding/workflows/{workflow_id}/tasks
{
  "task_name": "Complete Safety Training",
  "assigned_to_role": "SAFETY_OFFICER",
  "task_type": "TRAINING",
  "is_mandatory": true,
  "is_blocking": true
}

# 3. Add safety documents
POST /api/onboarding/workflows/{workflow_id}/document-requirements
{
  "document_name": "Medical Certificate",
  "document_code": "MEDICAL_CERT",
  "is_mandatory": true,
  "expiry_check": true,
  "min_validity_days": 365
}
```

### **Scenario 3: NGO Onboarding**
```bash
# 1. Create mission-focused workflow
POST /api/onboarding/workflows
{
  "name": "Program Officer Onboarding",
  "workflow_type": "PROGRAM",
  "department": "Programs",
  "duration_days": 30,
  "schedule_orientation": true
}

# 2. Add compliance documents
POST /api/onboarding/workflows/{workflow_id}/document-requirements
{
  "document_name": "Police Clearance Certificate",
  "document_code": "POLICE_CLEARANCE",
  "is_mandatory": true,
  "is_sensitive": true,
  "requires_verification": true
}
```

## 🔄 Integration Examples

### **Email Integration**
```python
# Send welcome email when onboarding starts
def send_welcome_email(instance):
    email_data = {
        "to": employee.email,
        "subject": f"Welcome to {company.name}!",
        "template": "onboarding_welcome",
        "data": {
            "employee_name": employee.name,
            "start_date": instance.start_date,
            "buddy_name": buddy.name if instance.assigned_buddy else None,
            "tasks_count": instance.total_tasks,
            "documents_count": instance.total_documents
        }
    }
    send_email(email_data)
```

### **Slack Integration**
```python
# Notify team when new employee starts
def notify_team_slack(instance):
    slack_message = {
        "channel": f"#{employee.department.lower()}",
        "text": f"🎉 Welcome {employee.name} to the team!",
        "attachments": [
            {
                "title": "Onboarding Progress",
                "fields": [
                    {"title": "Start Date", "value": instance.start_date, "short": True},
                    {"title": "Buddy", "value": buddy.name, "short": True},
                    {"title": "Progress", "value": f"{instance.completion_percentage}%", "short": True}
                ]
            }
        ]
    }
    send_slack_message(slack_message)
```

### **Asset Management Integration**
```python
# Auto-assign assets based on role
def auto_assign_assets(instance):
    if employee.department == "Engineering":
        assets = [
            {"type": "LAPTOP", "model": "MacBook Pro"},
            {"type": "MONITOR", "model": "Dell 27inch"},
            {"type": "ACCESS_CARD", "level": "DEVELOPER"}
        ]
    elif employee.department == "Sales":
        assets = [
            {"type": "LAPTOP", "model": "ThinkPad"},
            {"type": "PHONE", "model": "iPhone"},
            {"type": "ACCESS_CARD", "level": "SALES"}
        ]

    for asset_config in assets:
        assign_asset(instance.instance_id, asset_config)
```

## 📱 Mobile App Integration

### **Employee Self-Service**
```javascript
// React Native component for document upload
const DocumentUpload = ({ requirementId, instanceId }) => {
  const uploadDocument = async (fileUri) => {
    const formData = new FormData();
    formData.append('file', {
      uri: fileUri,
      type: 'image/jpeg',
      name: 'document.jpg'
    });
    formData.append('instance_id', instanceId);
    formData.append('requirement_id', requirementId);

    const response = await fetch('/api/onboarding/documents/upload', {
      method: 'POST',
      body: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
        'Authorization': `Bearer ${token}`
      }
    });

    return response.json();
  };

  return (
    <DocumentUploadComponent onUpload={uploadDocument} />
  );
};
```

### **Manager Dashboard**
```javascript
// React component for manager onboarding overview
const ManagerOnboardingDashboard = () => {
  const [onboardings, setOnboardings] = useState([]);

  useEffect(() => {
    fetch('/api/onboarding/instances?status=IN_PROGRESS')
      .then(response => response.json())
      .then(data => setOnboardings(data.instances));
  }, []);

  return (
    <div>
      <h2>Team Onboarding Progress</h2>
      {onboardings.map(instance => (
        <OnboardingCard
          key={instance.instance_id}
          instance={instance}
          onApprove={handleApproval}
        />
      ))}
    </div>
  );
};
```

This comprehensive onboarding system positions your HRMS as a **complete talent management solution** with enterprise-grade capabilities at SME-friendly pricing, providing a significant competitive advantage in the African market and beyond.
