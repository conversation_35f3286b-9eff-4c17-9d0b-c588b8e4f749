"""
Rwanda Statutory Contributions Setup Script
This script sets up all mandatory deductions and contributions for Rwanda
"""

from application.Models.deduction_type import DeductionType
from application.Models.deduction_policy import DeductionPolicy
from application.Models.country import Country
from application.Models.employee_type import EmployeeType
from application.database import central_db as db
from decimal import Decimal
from datetime import date

def setup_rwanda_statutory_contributions():
    """Set up all Rwanda statutory contributions and deductions."""
    
    # Get Rwanda country
    rwanda = Country.get_country_by_code('RW')
    if not rwanda:
        print("Rwanda country not found!")
        return False
    
    # Get employee types
    permanent_type = EmployeeType.query.filter_by(
        country_id=rwanda.country_id, 
        code='PERMANENT'
    ).first()
    
    # 1. PENSION CONTRIBUTIONS (6% employee + 6% employer)
    pension_type = DeductionType(
        country_id=rwanda.country_id,
        name="Pension Contribution",
        code="PENSION",
        description="Rwanda Social Security Board (RSSB) Pension Scheme",
        has_employee_contribution=True,
        has_employer_contribution=True,
        calculation_base="PENSIONABLE_SALARY",  # Gross - Transport
        is_mandatory=True,
        is_active=True
    )
    db.session.add(pension_type)
    db.session.flush()
    
    # Pension Policy (6% each)
    pension_policy = DeductionPolicy(
        deduction_type_id=pension_type.deduction_type_id,
        country_id=rwanda.country_id,
        employee_type_id=None,  # Applies to all employee types
        employee_rate=Decimal('0.06'),  # 6%
        employer_rate=Decimal('0.06'),   # 6%
        min_amount=None,
        max_amount=None,
        effective_from=date(2025, 1, 1),
        is_active=True
    )
    db.session.add(pension_policy)
    
    # 2. OCCUPATIONAL HAZARD (0% employee + 2% employer)
    hazard_type = DeductionType(
        country_id=rwanda.country_id,
        name="Occupational Hazard Insurance",
        code="OCC_HAZARD",
        description="RSSB Occupational Hazard Insurance",
        has_employee_contribution=False,
        has_employer_contribution=True,
        calculation_base="PENSIONABLE_SALARY",  # Gross - Transport
        is_mandatory=True,
        is_active=True
    )
    db.session.add(hazard_type)
    db.session.flush()
    
    # Occupational Hazard Policy (2% employer only)
    hazard_policy = DeductionPolicy(
        deduction_type_id=hazard_type.deduction_type_id,
        country_id=rwanda.country_id,
        employee_type_id=None,
        employee_rate=Decimal('0.00'),  # 0%
        employer_rate=Decimal('0.02'),   # 2%
        min_amount=None,
        max_amount=None,
        effective_from=date(2025, 1, 1),
        is_active=True
    )
    db.session.add(hazard_policy)
    
    # 3. MATERNITY LEAVE (0.3% employee + 0.3% employer)
    maternity_type = DeductionType(
        country_id=rwanda.country_id,
        name="Maternity Leave Insurance",
        code="MATERNITY",
        description="RSSB Maternity Leave Insurance",
        has_employee_contribution=True,
        has_employer_contribution=True,
        calculation_base="PENSIONABLE_SALARY",  # Gross - Transport
        is_mandatory=True,
        is_active=True
    )
    db.session.add(maternity_type)
    db.session.flush()
    
    # Maternity Policy (0.3% each)
    maternity_policy = DeductionPolicy(
        deduction_type_id=maternity_type.deduction_type_id,
        country_id=rwanda.country_id,
        employee_type_id=None,
        employee_rate=Decimal('0.003'),  # 0.3%
        employer_rate=Decimal('0.003'),   # 0.3%
        min_amount=None,
        max_amount=None,
        effective_from=date(2025, 1, 1),
        is_active=True
    )
    db.session.add(maternity_policy)
    
    # 4. CBHI (Community Based Health Insurance) - varies by income
    cbhi_type = DeductionType(
        country_id=rwanda.country_id,
        name="Community Based Health Insurance",
        code="CBHI",
        description="Rwanda Community Based Health Insurance",
        has_employee_contribution=True,
        has_employer_contribution=False,
        calculation_base="GROSS_SALARY",
        is_mandatory=True,
        is_active=True
    )
    db.session.add(cbhi_type)
    db.session.flush()
    
    # CBHI Policy (varies by income level - this is simplified)
    cbhi_policy = DeductionPolicy(
        deduction_type_id=cbhi_type.deduction_type_id,
        country_id=rwanda.country_id,
        employee_type_id=None,
        employee_rate=None,  # Fixed amount, not percentage
        employer_rate=Decimal('0.00'),
        min_amount=Decimal('3000'),   # Minimum CBHI contribution
        max_amount=Decimal('7000'),   # Maximum CBHI contribution
        effective_from=date(2025, 1, 1),
        is_active=True
    )
    db.session.add(cbhi_policy)
    
    try:
        db.session.commit()
        print("✅ Rwanda statutory contributions setup completed successfully!")
        return True
    except Exception as e:
        db.session.rollback()
        print(f"❌ Error setting up Rwanda statutory contributions: {e}")
        return False

if __name__ == "__main__":
    setup_rwanda_statutory_contributions()
