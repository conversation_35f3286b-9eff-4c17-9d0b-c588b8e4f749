#!/usr/bin/env python3
"""
Cleanup script for old open attendance records.

This script can be run periodically (e.g., via cron job) to clean up
old open attendance records across all companies.

Usage:
    python scripts/cleanup_old_attendance.py [--max-hours 48] [--company-id COMPANY_ID] [--dry-run]

Examples:
    # Clean up all companies, records older than 48 hours
    python scripts/cleanup_old_attendance.py

    # Clean up specific company, records older than 24 hours
    python scripts/cleanup_old_attendance.py --company-id abc123 --max-hours 24

    # Dry run to see what would be cleaned up
    python scripts/cleanup_old_attendance.py --dry-run
"""

import sys
import os
import argparse
from datetime import datetime

# Add the parent directory to the path so we can import from application
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from application.Models.company import Company
from application.Models.employees.attendance import Attendance


def cleanup_company_attendance(company_id, company_name, database_name, max_hours=48, dry_run=False):
    """
    Clean up old open attendance records for a specific company.
    
    Args:
        company_id: Company ID
        company_name: Company name (for logging)
        database_name: Database name for the company
        max_hours: Maximum hours to consider a record as valid open record
        dry_run: If True, only report what would be cleaned up without making changes
        
    Returns:
        int: Number of records that were (or would be) cleaned up
    """
    try:
        from app import db_connection
        
        print(f"Processing company: {company_name} (ID: {company_id})")
        
        with db_connection.get_session(database_name) as session:
            if dry_run:
                # Count old open records without cleaning them
                from datetime import timedelta
                current_time = datetime.now()
                cutoff_time = current_time - timedelta(hours=max_hours)
                
                old_open_records = session.query(Attendance).filter(
                    Attendance.check_in_time.isnot(None),
                    Attendance.check_out_time.is_(None),
                    Attendance.check_in_time < cutoff_time
                ).all()
                
                count = len(old_open_records)
                if count > 0:
                    print(f"  Would clean up {count} old open records")
                    for record in old_open_records:
                        hours_old = (current_time - record.check_in_time).total_seconds() / 3600
                        print(f"    - Record {record.attendance_id}: {hours_old:.1f} hours old")
                else:
                    print(f"  No old open records found")
                
                return count
            else:
                # Actually clean up the records
                cleaned_count = Attendance.cleanup_old_open_records(session, max_hours=max_hours)
                if cleaned_count > 0:
                    print(f"  Cleaned up {cleaned_count} old open records")
                else:
                    print(f"  No old open records found")
                
                return cleaned_count
                
    except Exception as e:
        print(f"  Error processing company {company_name}: {e}")
        return 0


def main():
    parser = argparse.ArgumentParser(description='Clean up old open attendance records')
    parser.add_argument('--max-hours', type=int, default=48,
                       help='Maximum hours to consider a record as valid open record (default: 48)')
    parser.add_argument('--company-id', type=str,
                       help='Specific company ID to clean up (if not provided, cleans all companies)')
    parser.add_argument('--dry-run', action='store_true',
                       help='Show what would be cleaned up without making changes')
    
    args = parser.parse_args()
    
    print(f"Starting attendance cleanup script...")
    print(f"Max hours for open records: {args.max_hours}")
    print(f"Dry run mode: {args.dry_run}")
    print(f"Target company: {args.company_id or 'ALL'}")
    print("-" * 50)
    
    total_cleaned = 0
    
    try:
        if args.company_id:
            # Clean up specific company
            company = Company.get_company_by_id(args.company_id)
            if not company:
                print(f"Error: Company with ID {args.company_id} not found")
                return 1
            
            database_name = company.database_name
            cleaned_count = cleanup_company_attendance(
                args.company_id, 
                company.company_name, 
                database_name, 
                args.max_hours, 
                args.dry_run
            )
            total_cleaned += cleaned_count
            
        else:
            # Clean up all companies
            companies = Company.get_companies()
            print(f"Found {len(companies)} companies to process")
            
            for company in companies:
                cleaned_count = cleanup_company_attendance(
                    company.company_id,
                    company.company_name,
                    company.database_name,
                    args.max_hours,
                    args.dry_run
                )
                total_cleaned += cleaned_count
        
        print("-" * 50)
        if args.dry_run:
            print(f"Total records that would be cleaned up: {total_cleaned}")
        else:
            print(f"Total records cleaned up: {total_cleaned}")
        
        return 0
        
    except Exception as e:
        print(f"Error in cleanup script: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
