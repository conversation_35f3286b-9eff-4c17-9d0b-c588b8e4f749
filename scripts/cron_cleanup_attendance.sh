#!/bin/bash

# <PERSON>ron job script for cleaning up old attendance records
# This script should be run periodically (e.g., daily) to clean up old open attendance records

# Set the working directory to the project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
cd "$PROJECT_ROOT"

# Set up logging
LOG_FILE="/var/log/attendance_cleanup.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# Function to log messages
log_message() {
    echo "[$DATE] $1" | tee -a "$LOG_FILE"
}

# Function to send notification (customize as needed)
send_notification() {
    local message="$1"
    # You can customize this to send email, Slack notification, etc.
    log_message "NOTIFICATION: $message"
}

log_message "Starting attendance cleanup job"

# Activate virtual environment if it exists
if [ -f "venv/bin/activate" ]; then
    source venv/bin/activate
    log_message "Activated virtual environment"
elif [ -f ".venv/bin/activate" ]; then
    source .venv/bin/activate
    log_message "Activated virtual environment"
fi

# Run the cleanup script
python3 scripts/cleanup_old_attendance.py --max-hours 48 2>&1 | while read line; do
    log_message "$line"
done

# Check the exit code
EXIT_CODE=${PIPESTATUS[0]}

if [ $EXIT_CODE -eq 0 ]; then
    log_message "Attendance cleanup completed successfully"
else
    log_message "Attendance cleanup failed with exit code $EXIT_CODE"
    send_notification "Attendance cleanup job failed with exit code $EXIT_CODE"
fi

log_message "Attendance cleanup job finished"
