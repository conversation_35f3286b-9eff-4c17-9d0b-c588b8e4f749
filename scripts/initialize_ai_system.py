#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to initialize the AI system with default providers and templates.

This script should be run after deploying the AI integration to set up
default configurations for companies.

Usage:
    python scripts/initialize_ai_system.py [--company-id COMPANY_ID] [--all-companies]
"""

import sys
import os
import argparse
from datetime import datetime

# Add the parent directory to the path so we can import from application
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from application.Models.company import Company
from application.Models.ai.ai_provider import AIProvider
from application.Models.ai.ai_insight_template import AIInsightTemplate
from application.Models.ai.ai_company_config import AICompanyConfig
from app import db_connection, app

def initialize_default_providers(session):
    """Initialize default AI providers."""
    print("Initializing default AI providers...")
    
    # Check if Gemini provider already exists
    existing_gemini = session.query(AIProvider).filter_by(name='gemini').first()
    if existing_gemini:
        print("  - Gemini provider already exists, skipping...")
        return existing_gemini
    
    # Create Gemini Flash 2.0 provider
    gemini_provider = AIProvider.create_provider(
        session,
        name='gemini',
        display_name='Google Gemini Flash 2.0',
        provider_type='text_generation',
        api_endpoint='https://generativelanguage.googleapis.com/v1beta',
        api_version='v1beta',
        model_name='gemini-2.0-flash-exp',
        max_tokens=8192,
        supports_streaming=False,
        supports_function_calling=True,
        supports_vision=True,
        requests_per_minute=60,
        requests_per_day=1500,
        cost_per_1k_tokens=0.0,  # Currently free
        is_active=True,
        is_default=True
    )
    
    if gemini_provider:
        print(f"  ✓ Created Gemini provider: {gemini_provider.provider_id}")
    else:
        print("  ✗ Failed to create Gemini provider")
    
    return gemini_provider

def initialize_default_templates(session):
    """Initialize default AI insight templates."""
    print("Initializing default AI insight templates...")
    
    templates = [
        {
            'name': 'Daily Attendance Summary',
            'category': 'attendance',
            'insight_type': 'daily_summary',
            'prompt_template': '''Analyze the following attendance data for {company_name} on {date}:

{attendance_data}

Company Context: {company_context}

Please provide a comprehensive daily attendance analysis including:
1. Overall attendance summary and key metrics
2. Notable patterns or trends observed
3. Areas of concern that require attention
4. Positive developments to celebrate
5. Specific actionable recommendations for improvement

Format your response as a professional business intelligence report suitable for {target_audience}.
Keep the tone {tone} and detail level {detail_level}.''',
            'target_audience': 'management',
            'output_format': 'narrative',
            'is_default': True
        },
        {
            'name': 'Weekly Attendance Trends',
            'category': 'attendance',
            'insight_type': 'trend_analysis',
            'prompt_template': '''Analyze the following attendance trends for {company_name} over the week of {date_range}:

{trend_data}

Company Context: {company_context}

Please provide a comprehensive trend analysis including:
1. Key attendance patterns and trends identified
2. Comparison with previous weeks
3. Day-of-week patterns observed
4. Department or team-specific trends
5. Predictive insights for next week
6. Strategic recommendations for improvement

Focus on actionable insights that can drive management decisions.''',
            'target_audience': 'management',
            'output_format': 'narrative',
            'is_default': True
        },
        {
            'name': 'Weekly Team Announcement',
            'category': 'announcements',
            'insight_type': 'weekly_summary',
            'prompt_template': '''Create a professional company announcement based on this weekly attendance summary:

{weekly_data}

Company Context: {company_context}

Generate an engaging announcement that:
1. Highlights positive attendance achievements
2. Recognizes top-performing teams or individuals
3. Addresses any concerns in a constructive manner
4. Motivates continued good attendance
5. Includes any relevant reminders or updates

Target audience: {target_audience}
Tone: {tone}
Keep it under 200 words and maintain a positive, encouraging tone.''',
            'target_audience': 'all employees',
            'output_format': 'narrative',
            'is_default': True
        },
        {
            'name': 'Executive Monthly Summary',
            'category': 'executive',
            'insight_type': 'monthly_summary',
            'prompt_template': '''Prepare an executive summary of workforce attendance for {company_name} for {period}:

{executive_data}

Company Context: {company_context}

Provide a strategic overview including:
1. Executive summary of key metrics and performance
2. Strategic implications of attendance patterns
3. Impact on business operations and productivity
4. Competitive positioning and industry benchmarks
5. Resource allocation recommendations
6. Strategic initiatives for workforce optimization
7. ROI analysis of proposed improvements

Format for C-level executives with focus on business impact and strategic decisions.''',
            'target_audience': 'executives',
            'output_format': 'structured',
            'is_default': True
        }
    ]
    
    created_count = 0
    for template_data in templates:
        # Check if template already exists
        existing_template = session.query(AIInsightTemplate).filter_by(
            name=template_data['name'],
            category=template_data['category']
        ).first()
        
        if existing_template:
            print(f"  - Template '{template_data['name']}' already exists, skipping...")
            continue
        
        template = AIInsightTemplate.create_template(session, **template_data)
        if template:
            print(f"  ✓ Created template: {template_data['name']}")
            created_count += 1
        else:
            print(f"  ✗ Failed to create template: {template_data['name']}")
    
    print(f"Created {created_count} new templates")
    return created_count

def initialize_company_config(session, company_id):
    """Initialize AI configuration for a company."""
    print(f"Initializing AI configuration for company: {company_id}")
    
    # Check if config already exists
    existing_config = session.query(AICompanyConfig).filter_by(company_id=company_id).first()
    if existing_config:
        print(f"  - AI config already exists for company {company_id}, skipping...")
        return existing_config
    
    # Create default config
    config = AICompanyConfig.create_default_config(session, company_id)
    if config:
        print(f"  ✓ Created AI config for company: {company_id}")
    else:
        print(f"  ✗ Failed to create AI config for company: {company_id}")
    
    return config

def initialize_company(company_id):
    """Initialize AI system for a specific company."""
    print(f"\n{'='*60}")
    print(f"Initializing AI system for company: {company_id}")
    print(f"{'='*60}")
    
    try:
        # Get company database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            print(f"Error: Company with ID {company_id} not found")
            return False
        
        print(f"Using database: {database_name}")
        
        # Connect to company database
        with db_connection.get_session(database_name) as session:
            # Initialize providers
            provider = initialize_default_providers(session)
            
            # Initialize templates
            template_count = initialize_default_templates(session)
            
            # Initialize company config
            config = initialize_company_config(session, company_id)
            
            print(f"\n✓ AI system initialization completed for company {company_id}")
            print(f"  - Providers: {'✓' if provider else '✗'}")
            print(f"  - Templates: {template_count} created")
            print(f"  - Config: {'✓' if config else '✗'}")
            
            return True
            
    except Exception as e:
        print(f"Error initializing AI system for company {company_id}: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Initialize AI system with default providers and templates')
    parser.add_argument('--company-id', type=str, help='Initialize for specific company ID')
    parser.add_argument('--all-companies', action='store_true', help='Initialize for all companies')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be done without making changes')

    args = parser.parse_args()

    if not args.company_id and not args.all_companies:
        print("Error: Must specify either --company-id or --all-companies")
        return 1

    if args.dry_run:
        print("DRY RUN MODE - No changes will be made")
        print("-" * 40)

    print(f"AI System Initialization Script")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 60)

    success_count = 0
    total_count = 0

    # Create Flask application context
    with app.app_context():
        try:
            if args.company_id:
                # Initialize specific company
                total_count = 1
                if not args.dry_run:
                    if initialize_company(args.company_id):
                        success_count = 1
                else:
                    print(f"Would initialize AI system for company: {args.company_id}")
                    success_count = 1

            elif args.all_companies:
                # Initialize all companies
                companies = Company.get_companies()
                total_count = len(companies)

                print(f"Found {total_count} companies to initialize")

                for company in companies:
                    if not args.dry_run:
                        if initialize_company(company.company_id):
                            success_count += 1
                    else:
                        print(f"Would initialize AI system for company: {company.company_id} ({company.company_name})")
                        success_count += 1

            print(f"\n{'='*60}")
            print(f"AI System Initialization Summary")
            print(f"{'='*60}")
            print(f"Total companies: {total_count}")
            print(f"Successfully initialized: {success_count}")
            print(f"Failed: {total_count - success_count}")

            if args.dry_run:
                print("\nThis was a dry run - no changes were made")
                print("Run without --dry-run to actually initialize the AI system")
        
            return 0 if success_count == total_count else 1

        except Exception as e:
            print(f"Error in initialization script: {e}")
            return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
