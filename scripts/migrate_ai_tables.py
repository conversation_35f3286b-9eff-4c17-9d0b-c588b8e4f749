#!/usr/bin/env python3
"""
Migration script to add AI tables to existing company databases.

This script creates the necessary AI tables in company databases that were
created before the AI integration was implemented.

Usage:
    python scripts/migrate_ai_tables.py [--company-id COMPANY_ID] [--all-companies] [--dry-run]
"""

import sys
import os
import argparse
from datetime import datetime

# Add the parent directory to the path so we can import from application
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from application.Models.company import Company
from application.Models.ai.ai_provider import AIProvider
from application.Models.ai.ai_insight_template import AIInsightTemplate
from application.Models.ai.ai_insight_request import AIInsightRequest
from application.Models.ai.ai_insight_response import AIInsightResponse
from application.Models.ai.ai_company_config import AICompanyConfig
from app import db_connection
from sqlalchemy import text

def check_table_exists(session, table_name):
    """Check if a table exists in the database."""
    try:
        result = session.execute(text(f"""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = '{table_name}'
            );
        """))
        return result.scalar()
    except Exception as e:
        print(f"Error checking table {table_name}: {e}")
        return False

def create_ai_tables(session, dry_run=False):
    """Create AI tables in the database."""
    tables_to_create = [
        ('ai_providers', AIProvider),
        ('ai_insight_templates', AIInsightTemplate),
        ('ai_insight_requests', AIInsightRequest),
        ('ai_insight_responses', AIInsightResponse),
        ('ai_company_configs', AICompanyConfig)
    ]
    
    created_tables = []
    skipped_tables = []
    
    for table_name, model_class in tables_to_create:
        if check_table_exists(session, table_name):
            print(f"  - Table '{table_name}' already exists, skipping...")
            skipped_tables.append(table_name)
        else:
            if not dry_run:
                try:
                    # Create the table
                    model_class.__table__.create(session.bind, checkfirst=True)
                    print(f"  ✓ Created table: {table_name}")
                    created_tables.append(table_name)
                except Exception as e:
                    print(f"  ✗ Failed to create table {table_name}: {e}")
            else:
                print(f"  Would create table: {table_name}")
                created_tables.append(table_name)
    
    return created_tables, skipped_tables

def migrate_company_database(company_id, dry_run=False):
    """Migrate a specific company database to include AI tables."""
    print(f"\n{'='*60}")
    print(f"Migrating AI tables for company: {company_id}")
    print(f"{'='*60}")
    
    try:
        # Get company database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            print(f"Error: Company with ID {company_id} not found")
            return False
        
        print(f"Using database: {database_name}")
        
        # Connect to company database
        with db_connection.get_session(database_name) as session:
            # Create AI tables
            created_tables, skipped_tables = create_ai_tables(session, dry_run)
            
            if not dry_run:
                session.commit()
            
            print(f"\n✓ AI tables migration completed for company {company_id}")
            print(f"  - Tables created: {len(created_tables)}")
            print(f"  - Tables skipped (already exist): {len(skipped_tables)}")
            
            if created_tables:
                print(f"  - Created: {', '.join(created_tables)}")
            if skipped_tables:
                print(f"  - Skipped: {', '.join(skipped_tables)}")
            
            return True
            
    except Exception as e:
        print(f"Error migrating company {company_id}: {e}")
        return False

def check_ai_tables_status(company_id):
    """Check the status of AI tables for a company."""
    try:
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            return None
        
        with db_connection.get_session(database_name) as session:
            tables_to_check = [
                'ai_providers',
                'ai_insight_templates', 
                'ai_insight_requests',
                'ai_insight_responses',
                'ai_company_configs'
            ]
            
            status = {}
            for table_name in tables_to_check:
                status[table_name] = check_table_exists(session, table_name)
            
            return status
            
    except Exception as e:
        print(f"Error checking AI tables status for company {company_id}: {e}")
        return None

def main():
    parser = argparse.ArgumentParser(description='Migrate AI tables to existing company databases')
    parser.add_argument('--company-id', type=str, help='Migrate specific company ID')
    parser.add_argument('--all-companies', action='store_true', help='Migrate all companies')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be done without making changes')
    parser.add_argument('--check-status', action='store_true', help='Check AI tables status for companies')
    
    args = parser.parse_args()
    
    if not args.company_id and not args.all_companies:
        print("Error: Must specify either --company-id or --all-companies")
        return 1
    
    if args.dry_run:
        print("DRY RUN MODE - No changes will be made")
        print("-" * 40)
    
    if args.check_status:
        print("CHECKING AI TABLES STATUS")
        print("-" * 40)
    
    print(f"AI Tables Migration Script")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 60)
    
    success_count = 0
    total_count = 0
    
    try:
        if args.company_id:
            # Process specific company
            total_count = 1
            
            if args.check_status:
                status = check_ai_tables_status(args.company_id)
                if status:
                    print(f"AI Tables Status for company {args.company_id}:")
                    for table, exists in status.items():
                        print(f"  - {table}: {'✓ EXISTS' if exists else '✗ MISSING'}")
                    
                    all_exist = all(status.values())
                    print(f"  Status: {'✓ COMPLETE' if all_exist else '⚠ INCOMPLETE'}")
                    success_count = 1 if all_exist else 0
                else:
                    print(f"Could not check status for company {args.company_id}")
            else:
                if migrate_company_database(args.company_id, args.dry_run):
                    success_count = 1
                    
        elif args.all_companies:
            # Process all companies
            companies = Company.get_companies()
            total_count = len(companies)
            
            print(f"Found {total_count} companies to process")
            
            for company in companies:
                if args.check_status:
                    status = check_ai_tables_status(company.company_id)
                    if status:
                        all_exist = all(status.values())
                        missing_count = sum(1 for exists in status.values() if not exists)
                        
                        print(f"Company {company.company_id} ({company.company_name}): ", end="")
                        if all_exist:
                            print("✓ COMPLETE")
                            success_count += 1
                        else:
                            print(f"⚠ INCOMPLETE ({missing_count} tables missing)")
                    else:
                        print(f"Company {company.company_id}: ✗ ERROR")
                else:
                    if migrate_company_database(company.company_id, args.dry_run):
                        success_count += 1
        
        print(f"\n{'='*60}")
        print(f"AI Tables Migration Summary")
        print(f"{'='*60}")
        print(f"Total companies: {total_count}")
        
        if args.check_status:
            print(f"Companies with complete AI tables: {success_count}")
            print(f"Companies with incomplete AI tables: {total_count - success_count}")
        else:
            print(f"Successfully migrated: {success_count}")
            print(f"Failed migrations: {total_count - success_count}")
        
        if args.dry_run and not args.check_status:
            print("\nThis was a dry run - no changes were made")
            print("Run without --dry-run to actually create the AI tables")
        
        return 0 if success_count == total_count else 1
        
    except Exception as e:
        print(f"Error in migration script: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
