#!/usr/bin/env python3
"""
Test script for AI integration functionality.

This script tests the AI integration components to ensure everything is working correctly.

Usage:
    python scripts/test_ai_integration.py [--company-id COMPANY_ID]
"""

import sys
import os
import argparse
from datetime import datetime

# Add the parent directory to the path so we can import from application
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_ai_imports():
    """Test that all AI components can be imported."""
    print("Testing AI component imports...")
    
    try:
        from application.Models.ai.ai_provider import AIProvider
        print("  ✓ AIProvider model imported")
        
        from application.Models.ai.ai_insight_template import AIInsightTemplate
        print("  ✓ AIInsightTemplate model imported")
        
        from application.Models.ai.ai_insight_request import AIInsightRequest
        print("  ✓ AIInsightRequest model imported")
        
        from application.Models.ai.ai_insight_response import AIInsightResponse
        print("  ✓ AIInsightResponse model imported")
        
        from application.Models.ai.ai_company_config import AICompanyConfig
        print("  ✓ AICompanyConfig model imported")
        
        from application.Services.ai.ai_service_factory import AIServiceFactory
        print("  ✓ AIServiceFactory service imported")
        
        from application.Services.ai.gemini_service import GeminiAIService
        print("  ✓ GeminiAIService service imported")
        
        from application.Services.ai.ai_insights_orchestrator import AIInsightsOrchestrator
        print("  ✓ AIInsightsOrchestrator service imported")
        
        from application.Services.ai.prompt_builder import PromptBuilder
        print("  ✓ PromptBuilder service imported")
        
        from application.Services.ai.ai_cache_service import AICacheService
        print("  ✓ AICacheService service imported")
        
        return True
        
    except ImportError as e:
        print(f"  ✗ Import error: {e}")
        return False

def test_gemini_service():
    """Test Gemini AI service functionality."""
    print("\nTesting Gemini AI service...")
    
    try:
        from application.Services.ai.ai_service_factory import AIServiceFactory
        
        # Test service creation
        service = AIServiceFactory.get_service('gemini')
        if service:
            print("  ✓ Gemini service created successfully")
            
            # Test simple prompt
            test_prompt = "Generate a brief test response to confirm the AI service is working."
            result = service.generate_insight(
                prompt=test_prompt,
                max_tokens=100,
                temperature=0.5
            )
            
            if result.get('success'):
                print("  ✓ Gemini API call successful")
                print(f"  ✓ Generated {result.get('tokens_used', 0)} tokens")
                print(f"  ✓ Response: {result.get('content', '')[:100]}...")
                return True
            else:
                print(f"  ✗ Gemini API call failed: {result.get('error', 'Unknown error')}")
                return False
        else:
            print("  ✗ Failed to create Gemini service")
            return False
            
    except Exception as e:
        print(f"  ✗ Error testing Gemini service: {e}")
        return False

def test_prompt_builder():
    """Test prompt builder functionality."""
    print("\nTesting Prompt Builder...")
    
    try:
        from application.Services.ai.prompt_builder import PromptBuilder
        
        # Test attendance prompt
        test_data = {
            'company_name': 'Test Company',
            'date': '2025-06-28',
            'attendance_data': {
                'summary': {
                    'total_employees': 50,
                    'present_count': 45,
                    'absent_count': 5,
                    'attendance_percentage': 90.0
                }
            },
            'target_audience': 'management',
            'tone': 'professional',
            'detail_level': 'medium'
        }
        
        prompt = PromptBuilder.build_attendance_prompt(
            data=test_data,
            company_context="Technology company with 50 employees",
            insight_type='daily_summary'
        )
        
        if prompt and len(prompt) > 100:
            print("  ✓ Attendance prompt generated successfully")
            print(f"  ✓ Prompt length: {len(prompt)} characters")
            return True
        else:
            print("  ✗ Failed to generate attendance prompt")
            return False
            
    except Exception as e:
        print(f"  ✗ Error testing prompt builder: {e}")
        return False

def test_cache_service():
    """Test cache service functionality."""
    print("\nTesting Cache Service...")
    
    try:
        from application.Services.ai.ai_cache_service import AICacheService
        
        # Test cache key generation
        cache_key = AICacheService.generate_cache_key(
            prompt="test prompt",
            company_id="test-company",
            insight_type="daily_summary"
        )
        
        if cache_key and cache_key.startswith('ai_insight_'):
            print("  ✓ Cache key generated successfully")
            print(f"  ✓ Cache key: {cache_key}")
            return True
        else:
            print("  ✗ Failed to generate cache key")
            return False
            
    except Exception as e:
        print(f"  ✗ Error testing cache service: {e}")
        return False

def test_company_database(company_id):
    """Test AI functionality with a specific company database."""
    print(f"\nTesting AI functionality for company: {company_id}")
    
    try:
        from application.Models.company import Company
        from app import db_connection
        
        # Get company database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            print(f"  ✗ Company {company_id} not found")
            return False
        
        print(f"  ✓ Using database: {database_name}")
        
        # Test database connection and AI tables
        with db_connection.get_session(database_name) as session:
            from application.Models.ai.ai_provider import AIProvider
            from application.Models.ai.ai_company_config import AICompanyConfig
            
            # Check if AI tables exist
            try:
                providers = session.query(AIProvider).count()
                print(f"  ✓ AI providers table accessible ({providers} providers)")
                
                config = AICompanyConfig.get_config_by_company(session, company_id)
                if config:
                    print("  ✓ Company AI configuration exists")
                else:
                    print("  ⚠ Company AI configuration not found (will be created on first use)")
                
                return True
                
            except Exception as db_error:
                print(f"  ✗ Database error: {db_error}")
                return False
                
    except Exception as e:
        print(f"  ✗ Error testing company database: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Test AI integration functionality')
    parser.add_argument('--company-id', type=str, help='Test with specific company ID')
    
    args = parser.parse_args()
    
    print(f"AI Integration Test Suite")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 0
    
    # Test 1: Component imports
    total_tests += 1
    if test_ai_imports():
        tests_passed += 1
    
    # Test 2: Prompt builder
    total_tests += 1
    if test_prompt_builder():
        tests_passed += 1
    
    # Test 3: Cache service
    total_tests += 1
    if test_cache_service():
        tests_passed += 1
    
    # Test 4: Gemini service (if API key available)
    api_key = os.getenv('GEMINI_API_KEY')
    if api_key:
        total_tests += 1
        if test_gemini_service():
            tests_passed += 1
    else:
        print("\nSkipping Gemini service test (GEMINI_API_KEY not set)")
    
    # Test 5: Company database (if company ID provided)
    if args.company_id:
        total_tests += 1
        if test_company_database(args.company_id):
            tests_passed += 1
    
    # Summary
    print("\n" + "=" * 60)
    print(f"Test Results Summary")
    print("=" * 60)
    print(f"Tests passed: {tests_passed}/{total_tests}")
    print(f"Success rate: {(tests_passed/total_tests)*100:.1f}%")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! AI integration is ready to use.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
