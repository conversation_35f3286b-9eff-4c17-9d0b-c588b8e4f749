#!/usr/bin/env python3
"""
Test script for enhanced AI attendance summaries (weekly, monthly, annual, custom).
This script tests the new AI insight types to ensure proper functionality.
"""

import sys
import os
import json
from datetime import datetime, timed<PERSON><PERSON>

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

def test_ai_summary_endpoint():
    """Test the enhanced AI insights endpoint with different period types."""
    print("Testing Enhanced AI Attendance Summary System")
    print("=" * 50)
    
    # Test data for different insight types
    test_cases = [
        {
            "name": "Weekly Summary",
            "insight_type": "weekly_summary",
            "period": "weekly",
            "date": "2025-01-07"
        },
        {
            "name": "Monthly Summary", 
            "insight_type": "monthly_summary",
            "period": "monthly",
            "date": "2025-01-01"
        },
        {
            "name": "Annual Summary",
            "insight_type": "annual_summary", 
            "period": "annual",
            "date": "2025-01-01"
        },
        {
            "name": "Custom Period Summary",
            "insight_type": "custom_summary",
            "period": "custom",
            "start_date": "2024-12-01",
            "end_date": "2024-12-31"
        }
    ]
    
    # Sample company ID for testing (replace with actual company ID)
    test_company_id = "test-company-uuid"
    
    for test_case in test_cases:
        print(f"\n{test_case['name']} Test:")
        print("-" * 30)
        
        # Build request payload
        payload = {
            "company_id": test_company_id,
            "insight_type": test_case["insight_type"],
            "period": test_case["period"]
        }
        
        if "date" in test_case:
            payload["date"] = test_case["date"]
        if "start_date" in test_case:
            payload["start_date"] = test_case["start_date"]
        if "end_date" in test_case:
            payload["end_date"] = test_case["end_date"]
        
        print(f"Request Payload: {json.dumps(payload, indent=2)}")
        
        # Test the data fetching function
        try:
            from application.Routes.ai.ai_insights_api import _fetch_attendance_analytics_data
            
            analytics_data = _fetch_attendance_analytics_data(
                company_id=test_company_id,
                period=test_case["period"],
                date_str=test_case.get("date"),
                start_date_str=test_case.get("start_date"),
                end_date_str=test_case.get("end_date")
            )
            
            if analytics_data:
                print("✓ Analytics data fetched successfully")
                print(f"  Company: {analytics_data.get('company_name')}")
                print(f"  Period: {analytics_data.get('period_info', {}).get('description')}")
                print(f"  Total Workforce: {analytics_data.get('company_info', {}).get('total_workforce')}")
                
                # Test prompt building
                from application.Services.ai.prompt_builder import PromptBuilder
                
                prompt = PromptBuilder.build_attendance_prompt(
                    data=analytics_data,
                    company_context="Test company context",
                    insight_type=test_case["insight_type"]
                )
                
                if prompt and len(prompt) > 100:
                    print("✓ Prompt generated successfully")
                    print(f"  Prompt length: {len(prompt)} characters")
                    
                    # Check for professional language (no first person)
                    first_person_words = ["I analyze", "I recommend", "I found", "I think", "I believe"]
                    has_first_person = any(word.lower() in prompt.lower() for word in first_person_words)
                    
                    if not has_first_person:
                        print("✓ Professional third-person language confirmed")
                    else:
                        print("⚠ Warning: First-person language detected in prompt")
                        
                else:
                    print("✗ Failed to generate prompt")
                    
            else:
                print("✗ Failed to fetch analytics data")
                
        except Exception as e:
            print(f"✗ Error during test: {str(e)}")

def test_prompt_templates():
    """Test that all new prompt templates are properly formatted."""
    print("\n\nTesting Prompt Templates")
    print("=" * 30)
    
    try:
        from application.Services.ai.prompt_builder import PromptBuilder
        
        # Test data
        test_data = {
            'company_name': 'Test Company Ltd',
            'period_info': {
                'type': 'weekly',
                'start_date': '2025-01-06',
                'end_date': '2025-01-12',
                'description': 'Week of 2025-01-06',
                'total_days': 7
            },
            'company_info': {
                'country': {'name': 'Rwanda', 'currency': 'RWF'},
                'employee_positions': ['Manager', 'Developer', 'Analyst'],
                'total_workforce': 50,
                'departments': ['IT', 'HR', 'Finance']
            },
            'attendance_data': {
                'summary': {
                    'total_employees': 50,
                    'present_count': 225,
                    'absent_count': 25,
                    'attendance_percentage': 90.0
                }
            },
            'target_audience': 'management',
            'tone': 'professional',
            'detail_level': 'comprehensive'
        }
        
        insight_types = ['weekly_summary', 'monthly_summary', 'annual_summary', 'custom_summary']
        
        for insight_type in insight_types:
            print(f"\nTesting {insight_type} template:")
            
            prompt = PromptBuilder.build_attendance_prompt(
                data=test_data,
                company_context="Technology company based in Rwanda with 50 employees",
                insight_type=insight_type
            )
            
            if prompt and len(prompt) > 200:
                print(f"✓ {insight_type} template working")
                print(f"  Length: {len(prompt)} characters")
                
                # Check for key elements
                required_elements = [
                    'Test Company Ltd',
                    'Rwanda',
                    'professional',
                    'management'
                ]
                
                missing_elements = [elem for elem in required_elements if elem not in prompt]
                if not missing_elements:
                    print("✓ All required elements present")
                else:
                    print(f"⚠ Missing elements: {missing_elements}")
                    
            else:
                print(f"✗ {insight_type} template failed")
                
    except Exception as e:
        print(f"✗ Error testing templates: {str(e)}")

def test_period_detection():
    """Test automatic period detection from insight_type."""
    print("\n\nTesting Period Auto-Detection")
    print("=" * 35)
    
    test_cases = [
        ("weekly_summary", "weekly"),
        ("monthly_summary", "monthly"), 
        ("annual_summary", "annual"),
        ("custom_summary", "custom"),
        ("daily_summary", "daily"),
        ("trend_analysis", "daily")  # Default fallback
    ]
    
    for insight_type, expected_period in test_cases:
        # Simulate the auto-detection logic
        period = None
        if 'weekly' in insight_type:
            period = 'weekly'
        elif 'monthly' in insight_type:
            period = 'monthly'
        elif 'annual' in insight_type:
            period = 'annual'
        elif 'custom' in insight_type:
            period = 'custom'
        else:
            period = 'daily'
            
        if period == expected_period:
            print(f"✓ {insight_type} -> {period}")
        else:
            print(f"✗ {insight_type} -> {period} (expected {expected_period})")

if __name__ == "__main__":
    print("Enhanced AI Attendance Summary Test Suite")
    print("========================================")
    
    # Run tests
    test_period_detection()
    test_prompt_templates()
    
    print("\n\nNote: To test the full endpoint, you need:")
    print("1. A running Flask application")
    print("2. Valid company ID in the database")
    print("3. Attendance data for the test periods")
    print("4. AI provider configuration")
    
    print("\nTest completed!")
