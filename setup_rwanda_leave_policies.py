#!/usr/bin/env python3
"""
Setup script for Rwanda Leave Policies
Based on Rwanda Labor Law and Regulations

This script sets up the standard leave types and policies for Rwanda
according to the labor laws and regulations.

Run this script after setting up your company to initialize
Rwanda-specific leave policies.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db_connection
from application.Models.employees import LeaveType, LeavePolicy
from application.Models.country import Country
from application.Models.company import Company
import uuid

def setup_rwanda_leave_policies(company_id):
    """
    Set up Rwanda-specific leave policies for a company.

    Args:
        company_id (str): The UUID of the company to set up policies for
    """

    # Rwanda Leave Types and Policies based on Labor Law
    leave_configurations = [
        {
            'name': 'Annual Leave',
            'code': 'ANNUAL',
            'description': 'Annual vacation leave as per Rwanda Labor Law',
            'is_paid': True,
            'requires_approval': True,
            'requires_documentation': False,
            'days_allowed': 18,  # 18 working days per year
            'accrual_period': 'yearly',
            'accrual_rate': 1.5,  # 1.5 days per month
            'max_carryover': 6,   # Maximum 6 days can be carried over
            'min_service_days': 365,  # Must work 1 year to be eligible
            'is_prorated': True,
            'gender_specific': None
        },
        {
            'name': 'Sick Leave',
            'code': 'SICK',
            'description': 'Medical leave for illness or injury',
            'is_paid': True,
            'requires_approval': False,  # Usually doesn't require pre-approval
            'requires_documentation': True,  # Medical certificate required
            'days_allowed': 30,  # 30 days per year
            'accrual_period': 'yearly',
            'accrual_rate': 2.5,  # 2.5 days per month
            'max_carryover': 0,   # Usually doesn't carry over
            'min_service_days': 0,  # Available immediately
            'is_prorated': True,
            'gender_specific': None
        },
        {
            'name': 'Maternity Leave',
            'code': 'MATERNITY',
            'description': 'Maternity leave for female employees',
            'is_paid': True,
            'requires_approval': True,
            'requires_documentation': True,  # Medical certificate required
            'days_allowed': 98,  # 14 weeks (98 days)
            'accrual_period': 'none',  # Not accrued, available when needed
            'accrual_rate': None,
            'max_carryover': 0,
            'min_service_days': 180,  # 6 months service required
            'is_prorated': False,
            'gender_specific': 'female'
        },
        {
            'name': 'Paternity Leave',
            'code': 'PATERNITY',
            'description': 'Paternity leave for male employees',
            'is_paid': True,
            'requires_approval': True,
            'requires_documentation': True,  # Birth certificate required
            'days_allowed': 7,  # 1 week
            'accrual_period': 'none',
            'accrual_rate': None,
            'max_carryover': 0,
            'min_service_days': 180,  # 6 months service required
            'is_prorated': False,
            'gender_specific': 'male'
        },
        # Circumstantial Leave Types (Rwanda Labor Law Article 59)
        {
            'name': 'Circumstantial Leave - Marriage',
            'code': 'CIRCUMSTANTIAL_MARRIAGE',
            'description': 'Leave for employee civil marriage (Article 59a)',
            'is_paid': True,
            'requires_approval': False,  # Automatic grant per law
            'requires_documentation': True,  # Marriage certificate required
            'days_allowed': 2,  # 2 working days
            'accrual_period': 'none',  # Granted when event occurs
            'accrual_rate': None,
            'max_carryover': 0,
            'min_service_days': 0,  # Immediate eligibility - circumstantial
            'is_prorated': False,
            'gender_specific': None
        },
        {
            'name': 'Circumstantial Leave - Spouse Death',
            'code': 'CIRCUMSTANTIAL_SPOUSE_DEATH',
            'description': 'Leave for death of spouse (Article 59c)',
            'is_paid': True,
            'requires_approval': False,  # Automatic grant per law
            'requires_documentation': True,  # Death certificate required
            'days_allowed': 7,  # 7 working days
            'accrual_period': 'none',
            'accrual_rate': None,
            'max_carryover': 0,
            'min_service_days': 0,  # Immediate eligibility - circumstantial
            'is_prorated': False,
            'gender_specific': None
        },
        {
            'name': 'Circumstantial Leave - Spouse Death with Infant',
            'code': 'CIRCUMSTANTIAL_SPOUSE_DEATH_INFANT',
            'description': 'Additional leave when spouse dies leaving infant <3 months (Article 59b)',
            'is_paid': True,
            'requires_approval': False,  # Automatic grant per law
            'requires_documentation': True,  # Death certificate + birth certificate required
            'days_allowed': 30,  # 1 month additional
            'accrual_period': 'none',
            'accrual_rate': None,
            'max_carryover': 0,
            'min_service_days': 0,  # Immediate eligibility - circumstantial
            'is_prorated': False,
            'gender_specific': None
        },
        {
            'name': 'Circumstantial Leave - Child Death',
            'code': 'CIRCUMSTANTIAL_CHILD_DEATH',
            'description': 'Leave for death of child or adoptive child (Article 59d)',
            'is_paid': True,
            'requires_approval': False,  # Automatic grant per law
            'requires_documentation': True,  # Death certificate required
            'days_allowed': 5,  # 5 working days
            'accrual_period': 'none',
            'accrual_rate': None,
            'max_carryover': 0,
            'min_service_days': 0,  # Immediate eligibility - circumstantial
            'is_prorated': False,
            'gender_specific': None
        },
        {
            'name': 'Circumstantial Leave - Parent Death',
            'code': 'CIRCUMSTANTIAL_PARENT_DEATH',
            'description': 'Leave for death of father, mother, father-in-law or mother-in-law (Article 59e)',
            'is_paid': True,
            'requires_approval': False,  # Automatic grant per law
            'requires_documentation': True,  # Death certificate required
            'days_allowed': 4,  # 4 working days
            'accrual_period': 'none',
            'accrual_rate': None,
            'max_carryover': 0,
            'min_service_days': 0,  # Immediate eligibility - circumstantial
            'is_prorated': False,
            'gender_specific': None
        },
        {
            'name': 'Circumstantial Leave - Sibling Death',
            'code': 'CIRCUMSTANTIAL_SIBLING_DEATH',
            'description': 'Leave for death of brother or sister (Article 59f)',
            'is_paid': True,
            'requires_approval': False,  # Automatic grant per law
            'requires_documentation': True,  # Death certificate required
            'days_allowed': 4,  # 4 working days
            'accrual_period': 'none',
            'accrual_rate': None,
            'max_carryover': 0,
            'min_service_days': 0,  # Immediate eligibility - circumstantial
            'is_prorated': False,
            'gender_specific': None
        },
        {
            'name': 'Circumstantial Leave - Grandparent Death',
            'code': 'CIRCUMSTANTIAL_GRANDPARENT_DEATH',
            'description': 'Leave for death of grandfather or grandmother (Article 59g)',
            'is_paid': True,
            'requires_approval': False,  # Automatic grant per law
            'requires_documentation': True,  # Death certificate required
            'days_allowed': 3,  # 3 working days
            'accrual_period': 'none',
            'accrual_rate': None,
            'max_carryover': 0,
            'min_service_days': 0,  # Immediate eligibility - circumstantial
            'is_prorated': False,
            'gender_specific': None
        },
        {
            'name': 'Circumstantial Leave - Transfer',
            'code': 'CIRCUMSTANTIAL_TRANSFER',
            'description': 'Leave for transfer over 30km from usual workplace (Article 59h)',
            'is_paid': True,
            'requires_approval': False,  # Automatic grant per law
            'requires_documentation': True,  # Transfer documentation required
            'days_allowed': 3,  # 3 working days
            'accrual_period': 'none',
            'accrual_rate': None,
            'max_carryover': 0,
            'min_service_days': 0,  # Immediate eligibility - circumstantial
            'is_prorated': False,
            'gender_specific': None
        }
    ]

    # Get Rwanda country ID, company database, and create policies - all in app context
    with app.app_context():
        rwanda = Country.get_country_by_code('RW')
        if not rwanda:
            print("❌ Rwanda not found in countries table. Please add Rwanda first.")
            print("   Use: INSERT INTO countries (country_id, name, code, currency, time_zone) VALUES")
            print("        (gen_random_uuid(), 'Rwanda', 'RW', 'RWF', 'Africa/Kigali');")
            return False

        rwanda_id = rwanda.country_id
        print(f"✅ Found Rwanda: {rwanda.name} (ID: {rwanda_id})")

        # Get company database
        database_name = Company.get_database_given_company_id(company_id)
        if not database_name:
            print(f"❌ Company with ID {company_id} not found")
            return False

        print(f"✅ Setting up leave policies for company database: {database_name}")

        with db_connection.get_session(database_name) as session:
            created_policies = []

            for config in leave_configurations:
                try:
                    # Create leave type
                    leave_type_data = {
                        'name': config['name'],
                        'code': config['code'],
                        'description': config['description'],
                        'is_paid': config['is_paid'],
                        'requires_approval': config['requires_approval'],
                        'requires_documentation': config['requires_documentation']
                    }

                    # Check if leave type already exists
                    existing_type = session.query(LeaveType).filter_by(code=config['code']).first()
                    if existing_type:
                        print(f"⚠️  Leave type '{config['name']}' already exists, skipping...")
                        leave_type = existing_type
                    else:
                        leave_type = LeaveType.create_leave_type(session, **leave_type_data)
                        if not leave_type:
                            print(f"❌ Failed to create leave type: {config['name']}")
                            continue
                        print(f"✅ Created leave type: {config['name']}")

                    # Create leave policy
                    policy_data = {
                        'leave_type_id': leave_type.leave_type_id,
                        'country_id': rwanda_id,
                        'days_allowed': config['days_allowed'],
                        'accrual_period': config['accrual_period'],
                        'accrual_rate': config['accrual_rate'],
                        'max_carryover': config['max_carryover'],
                        'min_service_days': config['min_service_days'],
                        'is_prorated': config['is_prorated'],
                        'gender_specific': config['gender_specific']
                    }

                    # Check if policy already exists
                    existing_policy = LeavePolicy.get_policy_for_leave_type_and_country(
                        session,
                        leave_type.leave_type_id,
                        rwanda_id,
                        config['gender_specific']
                    )

                    if existing_policy:
                        print(f"⚠️  Leave policy for '{config['name']}' already exists, skipping...")
                        continue

                    policy = LeavePolicy.create_policy(session, **policy_data)
                    if not policy:
                        print(f"❌ Failed to create leave policy: {config['name']}")
                        continue

                    created_policies.append({
                        'leave_type': config['name'],
                        'days_allowed': config['days_allowed'],
                        'gender_specific': config['gender_specific']
                    })
                    print(f"✅ Created leave policy: {config['name']} - {config['days_allowed']} days")

                except Exception as e:
                    print(f"❌ Error creating {config['name']}: {str(e)}")
                    continue

            print(f"\n🎉 Successfully created {len(created_policies)} leave policies for Rwanda!")
            print("\n📋 Summary of created policies:")
            for policy in created_policies:
                gender_note = f" ({policy['gender_specific']} only)" if policy['gender_specific'] else ""
                print(f"   • {policy['leave_type']}: {policy['days_allowed']} days{gender_note}")

            return True

def add_additional_leave_types(company_id):
    """Add additional leave types that might be needed."""

    additional_configurations = [
        {
            'name': 'Compassionate Leave',
            'code': 'COMPASSIONATE',
            'description': 'Leave for family emergencies or bereavement',
            'is_paid': True,
            'requires_approval': True,
            'requires_documentation': True,  # Death certificate or proof required
            'days_allowed': 7,  # Up to 7 days per occurrence
            'accrual_period': 'yearly',
            'accrual_rate': None,
            'max_carryover': 0,
            'min_service_days': 90,  # 3 months service required
            'is_prorated': False,
            'gender_specific': None
        },
        {
            'name': 'Study Leave',
            'code': 'STUDY',
            'description': 'Educational leave for approved studies',
            'is_paid': False,  # Usually unpaid unless specified in contract
            'requires_approval': True,
            'requires_documentation': True,  # Admission letter, study schedule
            'days_allowed': 30,  # Varies, up to 30 days per year
            'accrual_period': 'yearly',
            'accrual_rate': None,
            'max_carryover': 0,
            'min_service_days': 365,  # 1 year service required
            'is_prorated': False,
            'gender_specific': None
        },
        {
            'name': 'Public Holiday Compensation',
            'code': 'PUBLIC_HOLIDAY',
            'description': 'Compensation leave for working on public holidays',
            'is_paid': True,
            'requires_approval': False,
            'requires_documentation': False,
            'days_allowed': 365,  # No specific limit, earned when working holidays
            'accrual_period': 'none',
            'accrual_rate': None,
            'max_carryover': 30,  # Can accumulate up to 30 days
            'min_service_days': 0,
            'is_prorated': False,
            'gender_specific': None
        }
    ]

    # Get Rwanda country ID
    with app.app_context():
        rwanda = Country.get_country_by_code('RW')
        rwanda_id = rwanda.country_id

    # Get company database
    database_name = Company.get_database_given_company_id(company_id)

    with db_connection.get_session(database_name) as session:
        for config in additional_configurations:
            # Create leave type and policy similar to main function
            # (Implementation similar to above)
            pass

def main():
    """Main function to run the setup script."""
    print("🇷🇼 Rwanda Leave Policies Setup Script")
    print("=" * 50)

    if len(sys.argv) != 2:
        print("Usage: python setup_rwanda_leave_policies.py <company_id>")
        print("\nExample:")
        print("  python setup_rwanda_leave_policies.py 12345678-1234-1234-1234-123456789012")
        sys.exit(1)

    company_id = sys.argv[1]

    # Validate UUID format (temporarily disabled for testing)
    # try:
    #     uuid.UUID(company_id)
    # except ValueError:
    #     print(f"❌ Invalid company ID format: {company_id}")
    #     print("   Company ID must be a valid UUID")
    #     sys.exit(1)
    print(f"✅ Using company ID: {company_id}")

    print(f"Setting up Rwanda leave policies for company: {company_id}")
    print()

    success = setup_rwanda_leave_policies(company_id)

    if success:
        print("\n✅ Setup completed successfully!")
        print("\n📝 Next steps:")
        print("1. Review the created leave policies in your admin panel")
        print("2. Adjust days_allowed if needed based on your company policy")
        print("3. Set up employee leave balances using the leave balances API")
        print("4. Train HR staff on the leave request approval process")
        print("\n📖 Rwanda Leave Law Summary:")
        print("• Annual Leave: 18 working days per year (after 1 year service)")
        print("• Sick Leave: 30 days per year with medical certificate")
        print("• Maternity Leave: 14 weeks (98 days) for female employees")
        print("• Paternity Leave: 1 week (7 days) for male employees")
        print("• Circumstantial Leave (Article 59):")
        print("  - Marriage: 2 days")
        print("  - Spouse death: 7 days")
        print("  - Spouse death with infant <3 months: 30 days additional")
        print("  - Child death: 5 days")
        print("  - Parent/in-law death: 4 days")
        print("  - Sibling death: 4 days")
        print("  - Grandparent death: 3 days")
        print("  - Transfer >30km: 3 days")
        print("• Public Holidays: 13 official public holidays per year")
    else:
        print("\n❌ Setup failed. Please check the errors above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
