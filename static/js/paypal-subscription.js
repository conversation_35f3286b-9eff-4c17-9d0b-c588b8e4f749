/**
 * PayPal Subscription Integration
 * Handles both PayPal account and credit card payments for subscriptions
 */

class PayPalSubscriptionManager {
    constructor(config = {}) {
        this.config = {
            clientId: config.clientId,
            currency: config.currency || 'USD',
            locale: config.locale || 'en_US',
            environment: config.environment || 'sandbox',
            apiBaseUrl: config.apiBaseUrl || '/api/paypal',
            ...config
        };
        
        this.paypalLoaded = false;
        this.currentSubscription = null;
        this.callbacks = {
            onSuccess: config.onSuccess || this.defaultSuccessHandler,
            onError: config.onError || this.defaultErrorHandler,
            onCancel: config.onCancel || this.defaultCancelHandler
        };
    }

    /**
     * Initialize PayPal SDK
     */
    async initializePayPal() {
        if (this.paypalLoaded) {
            return Promise.resolve();
        }

        return new Promise((resolve, reject) => {
            // Check if PayPal SDK is already loaded
            if (window.paypal) {
                this.paypalLoaded = true;
                resolve();
                return;
            }

            // Load PayPal SDK
            const script = document.createElement('script');
            script.src = `https://www.paypal.com/sdk/js?client-id=${this.config.clientId}&currency=${this.config.currency}&locale=${this.config.locale}&vault=true&intent=subscription`;
            script.onload = () => {
                this.paypalLoaded = true;
                resolve();
            };
            script.onerror = () => {
                reject(new Error('Failed to load PayPal SDK'));
            };
            
            document.head.appendChild(script);
        });
    }

    /**
     * Set up subscription billing with PayPal
     */
    async setupSubscriptionBilling(companyId, planId, options = {}) {
        try {
            const response = await fetch(`${this.config.apiBaseUrl}/setup-billing`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    company_id: companyId,
                    plan_id: planId,
                    return_url: options.returnUrl,
                    cancel_url: options.cancelUrl
                })
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || 'Failed to setup billing');
            }

            this.currentSubscription = {
                subscriptionId: data.subscription_id,
                agreementId: data.agreement_id,
                approvalUrl: data.approval_url
            };

            return data;

        } catch (error) {
            console.error('Error setting up subscription billing:', error);
            throw error;
        }
    }

    /**
     * Render PayPal payment buttons
     */
    async renderPayPalButtons(containerId, companyId, planId, options = {}) {
        try {
            await this.initializePayPal();

            const container = document.getElementById(containerId);
            if (!container) {
                throw new Error(`Container element with ID '${containerId}' not found`);
            }

            // Clear existing content
            container.innerHTML = '';

            // Render PayPal buttons
            window.paypal.Buttons({
                style: {
                    layout: 'vertical',
                    color: 'blue',
                    shape: 'rect',
                    label: 'subscribe',
                    height: 40
                },

                createSubscription: async (data, actions) => {
                    try {
                        // Set up billing agreement with backend
                        const billingSetup = await this.setupSubscriptionBilling(companyId, planId, options);
                        
                        // Return the approval URL for PayPal to handle
                        return billingSetup.agreement_id;
                        
                    } catch (error) {
                        console.error('Error creating subscription:', error);
                        this.callbacks.onError(error);
                        throw error;
                    }
                },

                onApprove: async (data, actions) => {
                    try {
                        // Execute the billing agreement
                        const response = await fetch(`${this.config.apiBaseUrl}/execute-agreement`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                token: data.subscriptionID || data.billingToken,
                                company_id: companyId
                            })
                        });

                        const result = await response.json();

                        if (!response.ok) {
                            throw new Error(result.message || 'Failed to activate subscription');
                        }

                        this.callbacks.onSuccess(result);

                    } catch (error) {
                        console.error('Error approving subscription:', error);
                        this.callbacks.onError(error);
                    }
                },

                onCancel: (data) => {
                    console.log('PayPal subscription cancelled:', data);
                    this.callbacks.onCancel(data);
                },

                onError: (err) => {
                    console.error('PayPal button error:', err);
                    this.callbacks.onError(err);
                }

            }).render(`#${containerId}`);

        } catch (error) {
            console.error('Error rendering PayPal buttons:', error);
            throw error;
        }
    }

    /**
     * Render credit card payment form
     */
    async renderCreditCardForm(containerId, companyId, planId, options = {}) {
        try {
            await this.initializePayPal();

            const container = document.getElementById(containerId);
            if (!container) {
                throw new Error(`Container element with ID '${containerId}' not found`);
            }

            // Create credit card form HTML
            container.innerHTML = `
                <div class="paypal-card-form">
                    <div class="form-group">
                        <label for="card-number">Card Number</label>
                        <div id="card-number-field"></div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="expiry-date">Expiry Date</label>
                            <div id="expiry-date-field"></div>
                        </div>
                        <div class="form-group">
                            <label for="cvv">CVV</label>
                            <div id="cvv-field"></div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="cardholder-name">Cardholder Name</label>
                        <input type="text" id="cardholder-name" class="form-control" required>
                    </div>
                    <button type="button" id="submit-card-payment" class="btn btn-primary">
                        Set Up Automatic Billing
                    </button>
                </div>
            `;

            // Initialize PayPal card fields
            const cardFields = window.paypal.CardFields({
                createVaultSetupToken: async () => {
                    try {
                        // Set up billing agreement for card payments
                        const billingSetup = await this.setupSubscriptionBilling(companyId, planId, options);
                        return billingSetup.agreement_id;
                        
                    } catch (error) {
                        console.error('Error creating vault setup token:', error);
                        throw error;
                    }
                },

                onApprove: async (data) => {
                    try {
                        // Execute the billing agreement
                        const response = await fetch(`${this.config.apiBaseUrl}/execute-agreement`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                token: data.vaultSetupToken,
                                company_id: companyId
                            })
                        });

                        const result = await response.json();

                        if (!response.ok) {
                            throw new Error(result.message || 'Failed to set up card billing');
                        }

                        this.callbacks.onSuccess(result);

                    } catch (error) {
                        console.error('Error setting up card billing:', error);
                        this.callbacks.onError(error);
                    }
                },

                onError: (err) => {
                    console.error('Card fields error:', err);
                    this.callbacks.onError(err);
                }
            });

            // Render individual card fields
            if (cardFields.NumberField().isEligible()) {
                cardFields.NumberField().render('#card-number-field');
            }

            if (cardFields.ExpiryField().isEligible()) {
                cardFields.ExpiryField().render('#expiry-date-field');
            }

            if (cardFields.CVVField().isEligible()) {
                cardFields.CVVField().render('#cvv-field');
            }

            // Handle form submission
            document.getElementById('submit-card-payment').addEventListener('click', () => {
                cardFields.submit();
            });

        } catch (error) {
            console.error('Error rendering credit card form:', error);
            throw error;
        }
    }

    /**
     * Get billing status for a company
     */
    async getBillingStatus(companyId) {
        try {
            const response = await fetch(`${this.config.apiBaseUrl}/billing-status/${companyId}`);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || 'Failed to get billing status');
            }

            return data.billing_status;

        } catch (error) {
            console.error('Error getting billing status:', error);
            throw error;
        }
    }

    /**
     * Cancel billing agreement
     */
    async cancelBilling(companyId, reason = 'Cancelled by user') {
        try {
            const response = await fetch(`${this.config.apiBaseUrl}/cancel-billing`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    company_id: companyId,
                    reason: reason
                })
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || 'Failed to cancel billing');
            }

            return data;

        } catch (error) {
            console.error('Error cancelling billing:', error);
            throw error;
        }
    }

    /**
     * Default success handler
     */
    defaultSuccessHandler(data) {
        console.log('PayPal subscription setup successful:', data);
        alert('Subscription billing has been set up successfully!');
    }

    /**
     * Default error handler
     */
    defaultErrorHandler(error) {
        console.error('PayPal subscription error:', error);
        alert('There was an error setting up your subscription. Please try again.');
    }

    /**
     * Default cancel handler
     */
    defaultCancelHandler(data) {
        console.log('PayPal subscription cancelled:', data);
        alert('Subscription setup was cancelled.');
    }
}

// Export for use in other scripts
window.PayPalSubscriptionManager = PayPalSubscriptionManager;
