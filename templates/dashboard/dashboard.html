<!-- application/templates/dashboard.html -->
{% extends "base.html" %}
{% block title %}Dashboard{% endblock %}
{% block content %}
<h1>Sample Company - Attendance Dashboard</h1>
<p>Database: sample_db</p>

<!-- Date Filter -->
<form method="GET" class="mb-3">
    <label for="date" class="form-label">Select Date:</label>
    <input type="date" id="date" name="date" value="2025-03-15" class="form-control d-inline w-auto">
    <button type="submit" class="btn btn-primary">View</button>
</form>

<!-- Daily Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <h5 class="card-title">Total Employees</h5>
                <p class="card-text">50</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-body">
                <h5 class="card-title">Present</h5>
                <p class="card-text">42</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-danger">
            <div class="card-body">
                <h5 class="card-title">Absent</h5>
                <p class="card-text">8</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <h5 class="card-title">Late</h5>
                <p class="card-text">5</p>
            </div>
        </div>
    </div>
</div>

<!-- Weekly Report -->
<h3>Weekly Attendance Report (Last 7 Days)</h3>
<table class="table table-bordered mb-4">
    <thead>
        <tr>
            <th>Date</th>
            <th>Present</th>
            <th>Absent</th>
        </tr>
    </thead>
    <tbody>
        <tr><td>2025-03-09</td><td>40</td><td>10</td></tr>
        <tr><td>2025-03-10</td><td>45</td><td>5</td></tr>
        <tr><td>2025-03-11</td><td>43</td><td>7</td></tr>
        <tr><td>2025-03-12</td><td>41</td><td>9</td></tr>
        <tr><td>2025-03-13</td><td>44</td><td>6</td></tr>
        <tr><td>2025-03-14</td><td>42</td><td>8</td></tr>
        <tr><td>2025-03-15</td><td>42</td><td>8</td></tr>
    </tbody>
</table>

<!-- Detailed Records -->
<h3>Records for 2025-03-15</h3>
<a href="#" class="btn btn-secondary mb-3">Download CSV</a>
<table class="table table-striped">
    <thead>
        <tr>
            <th>Employee ID</th>
            <th>Name</th>
            <th>Time</th>
            <th>In/Out</th>
            <th>Device SN</th>
            <th>Temperature</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>E001</td>
            <td>John Doe</td>
            <td>2025-03-15 08:00:00</td>
            <td>In</td>
            <td>DEV12345</td>
            <td>36.5</td>
        </tr>
        <tr>
            <td>E002</td>
            <td>Jane Smith</td>
            <td>2025-03-15 09:15:00</td>
            <td>In</td>
            <td>DEV12346</td>
            <td>36.7</td>
        </tr>
        <tr>
            <td>E003</td>
            <td>Peter Kim</td>
            <td>2025-03-15 08:45:00</td>
            <td>In</td>
            <td>DEV12345</td>
            <td>36.6</td>
        </tr>
        <tr>
            <td>E001</td>
            <td>John Doe</td>
            <td>2025-03-15 17:00:00</td>
            <td>Out</td>
            <td>DEV12345</td>
            <td>36.5</td>
        </tr>
        <tr>
            <td>E004</td>
            <td>Mary Jones</td>
            <td>2025-03-15 08:30:00</td>
            <td>In</td>
            <td>DEV12347</td>
            <td>36.8</td>
        </tr>
    </tbody>
</table>
{% endblock %}