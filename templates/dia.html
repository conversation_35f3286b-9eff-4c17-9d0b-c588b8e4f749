<!DOCTYPE html>
<html>
<head>
  <title>Bootstrap 模态框（Modal）</title>
    <meta charset="UTF-8">
 <!-- 引入jQuery -->
<script type="text/javascript" src="static/js/jquery-1.12.4.min.js"></script>
<!-- 引入样式 -->
<link href="static/bootstrap-3.3.7-dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdn.bootcss.com/smalot-bootstrap-datetimepicker/2.4.4/css/bootstrap-datetimepicker.min.css" rel="stylesheet">

<script src="static/bootstrap-3.3.7-dist/js/bootstrap.min.js"></script>
<script src="static/js/bootstrap-datetimepicker.min.js"></script>
	<script src="static/js/locales/bootstrap-datetimepicker.zh-CN.js"></script>
</head>
<body>

<div class="container">
  <!-- 触发模态框的按钮 -->
  <button type="button" class="btn btn-info btn-lg" data-toggle="modal" data-target="#myModal">打开模态框</button>

  <!-- 模态框（Modal） -->
  <div class="modal fade" id="myModal" role="dialog">
    <div class="modal-dialog">

      <!-- 模态框内容-->
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal">&times;</button>
          <h4 class="modal-title">模态框标题</h4>
        </div>
        <div class="modal-body">
          <p>这是一个简单的模态框示例。</p>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
        </div>
      </div>

    </div>
  </div>
</div>

</body>
</html>
