{% extends "emails/base/base.html" %}

{% block header_subtitle %}
<p>Your Access Code</p>
{% endblock %}

{% block content %}
<h2>Hello {{ user_name or 'User' }},</h2>

<p>
    You're trying to access your KaziSync HRMS account. Please use the verification code below to complete your login:
</p>

<!-- OTP Code Display -->
<div class="code-box">
    <div class="code-text">{{ otp_code }}</div>
    <p class="code-subtitle">
        This code expires in <strong>{{ expiry_minutes or 5 }} minutes</strong>
    </p>
</div>

<!-- Security Notice -->
<div class="alert alert-warning">
    <p style="margin: 0; font-size: 14px;">
        <strong>Security Note:</strong> If you didn't request this code, please ignore this email and contact your system administrator immediately.
    </p>
</div>

<!-- Instructions -->
<h3>How to use this code:</h3>
<ol style="color: #6b7280; line-height: 1.6;">
    <li>Return to the KaziSync login page</li>
    <li>Enter the 6-digit code exactly as shown above</li>
    <li>Complete your login process</li>
</ol>

<!-- Additional Information -->
<div class="alert alert-info">
    <p style="margin: 0; font-size: 14px;">
        <strong>Important:</strong> This code can only be used once and will expire automatically after {{ expiry_minutes or 5 }} minutes for your security.
    </p>
</div>

<!-- Support Information -->
<p style="margin-top: 30px; color: #6b7280; font-size: 14px;">
    If you're having trouble accessing your account or didn't request this code, please contact your system administrator or IT support team.
</p>

<!-- Login Details -->
{% if login_details %}
<div style="background-color: #f8fafc; padding: 20px; border-radius: 6px; margin: 20px 0;">
    <h4 style="margin: 0 0 10px 0; color: #374151; font-size: 16px;">Login Attempt Details:</h4>
    <table style="width: 100%; font-size: 14px; color: #6b7280;">
        {% if login_details.time %}
        <tr>
            <td style="padding: 5px 0; font-weight: 600;">Time:</td>
            <td style="padding: 5px 0;">{{ login_details.time }}</td>
        </tr>
        {% endif %}
        {% if login_details.ip_address %}
        <tr>
            <td style="padding: 5px 0; font-weight: 600;">IP Address:</td>
            <td style="padding: 5px 0;">{{ login_details.ip_address }}</td>
        </tr>
        {% endif %}
        {% if login_details.location %}
        <tr>
            <td style="padding: 5px 0; font-weight: 600;">Location:</td>
            <td style="padding: 5px 0;">{{ login_details.location }}</td>
        </tr>
        {% endif %}
        {% if login_details.device %}
        <tr>
            <td style="padding: 5px 0; font-weight: 600;">Device:</td>
            <td style="padding: 5px 0;">{{ login_details.device }}</td>
        </tr>
        {% endif %}
    </table>
</div>
{% endif %}

<!-- Purpose-specific messages -->
{% if purpose == 'password_reset' %}
<div class="alert alert-warning">
    <p style="margin: 0; font-size: 14px;">
        <strong>Password Reset:</strong> You requested to reset your password. After entering this code, you'll be able to set a new password for your account.
    </p>
</div>
{% elif purpose == 'email_verification' %}
<div class="alert alert-success">
    <p style="margin: 0; font-size: 14px;">
        <strong>Email Verification:</strong> Please enter this code to verify your email address and complete your account setup.
    </p>
</div>
{% elif purpose == 'sensitive_operation' %}
<div class="alert alert-danger">
    <p style="margin: 0; font-size: 14px;">
        <strong>Sensitive Operation:</strong> This code is required to complete a sensitive operation on your account. Only proceed if you initiated this action.
    </p>
</div>
{% endif %}

<!-- Quick Access Button (Optional) -->
{% if quick_access_url %}
<div style="text-align: center; margin: 30px 0;">
    <a href="{{ quick_access_url }}" class="btn">
        Complete Login Process
    </a>
    <p style="margin: 10px 0 0 0; font-size: 12px; color: #9ca3af;">
        Or copy and paste this link: {{ quick_access_url }}
    </p>
</div>
{% endif %}

<!-- Troubleshooting -->
<div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
    <h4 style="color: #374151; font-size: 16px; margin: 0 0 15px 0;">Troubleshooting:</h4>
    <ul style="color: #6b7280; font-size: 14px; line-height: 1.6; margin: 0; padding-left: 20px;">
        <li>Make sure you enter the code exactly as shown (including any spaces)</li>
        <li>The code is case-sensitive and must be entered within {{ expiry_minutes or 5 }} minutes</li>
        <li>If the code has expired, request a new one from the login page</li>
        <li>Clear your browser cache if you're experiencing issues</li>
    </ul>
</div>

{% endblock %}

{% block footer %}
<p>
    This email was sent from {{ company_name or 'KaziSync HRMS' }}<br>
    <a href="mailto:{{ company_email or support_email }}">{{ company_email or support_email }}</a>
</p>
{% if company_address %}
<p>{{ company_address }}</p>
{% endif %}
<p style="margin-top: 20px; font-size: 12px; color: #9ca3af;">
    This is an automated security email. Please do not reply to this message.<br>
    If you have questions, contact your system administrator.
</p>
<p style="margin-top: 15px;">
    © {{ current_year }} {{ company_name or 'KaziSync' }}. All rights reserved.
</p>
{% endblock %}
