{% extends "emails/base/base.html" %}

{% block header_subtitle %}
<p>Password Reset Request</p>
{% endblock %}

{% block content %}
<h2>Hello {{ user_name or 'User' }},</h2>

<p>
    We received a request to reset your password for your KaziSync HRMS account. If you made this request, please use the verification code below to proceed with resetting your password.
</p>

<!-- OTP Code Display -->
<div class="code-box">
    <div class="code-text">{{ otp_code }}</div>
    <p class="code-subtitle">
        This code expires in <strong>{{ expiry_minutes or 15 }} minutes</strong>
    </p>
</div>

<!-- Security Warning -->
<div class="alert alert-warning">
    <p style="margin: 0; font-size: 14px;">
        <strong>Security Alert:</strong> If you didn't request a password reset, please ignore this email and contact your system administrator immediately. Your account may be at risk.
    </p>
</div>

<!-- Instructions -->
<h3>How to reset your password:</h3>
<ol style="color: #6b7280; line-height: 1.6;">
    <li>Return to the password reset page</li>
    <li>Enter the 6-digit code exactly as shown above</li>
    <li>Create a new secure password</li>
    <li>Confirm your new password</li>
</ol>

<!-- Password Requirements -->
<div class="alert alert-info">
    <h4 style="margin: 0 0 10px 0; color: #1e40af; font-size: 16px;">Password Requirements:</h4>
    <ul style="margin: 0; padding-left: 20px; font-size: 14px; color: #1e40af;">
        <li>At least 8 characters long</li>
        <li>Include uppercase and lowercase letters</li>
        <li>Include at least one number</li>
        <li>Include at least one special character</li>
    </ul>
</div>

<!-- Quick Reset Button (Optional) -->
{% if reset_url %}
<div style="text-align: center; margin: 30px 0;">
    <a href="{{ reset_url }}" class="btn">
        Reset Password Now
    </a>
    <p style="margin: 10px 0 0 0; font-size: 12px; color: #9ca3af;">
        Or copy and paste this link: {{ reset_url }}
    </p>
</div>
{% endif %}

<!-- Request Details -->
{% if request_details %}
<div style="background-color: #f8fafc; padding: 20px; border-radius: 6px; margin: 20px 0;">
    <h4 style="margin: 0 0 10px 0; color: #374151; font-size: 16px;">Reset Request Details:</h4>
    <table style="width: 100%; font-size: 14px; color: #6b7280;">
        {% if request_details.time %}
        <tr>
            <td style="padding: 5px 0; font-weight: 600;">Time:</td>
            <td style="padding: 5px 0;">{{ request_details.time }}</td>
        </tr>
        {% endif %}
        {% if request_details.ip_address %}
        <tr>
            <td style="padding: 5px 0; font-weight: 600;">IP Address:</td>
            <td style="padding: 5px 0;">{{ request_details.ip_address }}</td>
        </tr>
        {% endif %}
        {% if request_details.location %}
        <tr>
            <td style="padding: 5px 0; font-weight: 600;">Location:</td>
            <td style="padding: 5px 0;">{{ request_details.location }}</td>
        </tr>
        {% endif %}
        {% if request_details.device %}
        <tr>
            <td style="padding: 5px 0; font-weight: 600;">Device:</td>
            <td style="padding: 5px 0;">{{ request_details.device }}</td>
        </tr>
        {% endif %}
    </table>
</div>
{% endif %}

<!-- Additional Security Information -->
<div class="alert alert-danger">
    <p style="margin: 0; font-size: 14px;">
        <strong>Important:</strong> After resetting your password, you will be logged out of all devices and sessions. You'll need to log in again with your new password.
    </p>
</div>

<!-- Support Information -->
<p style="margin-top: 30px; color: #6b7280; font-size: 14px;">
    If you're having trouble resetting your password or didn't request this reset, please contact your system administrator or IT support team immediately.
</p>

<!-- Troubleshooting -->
<div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
    <h4 style="color: #374151; font-size: 16px; margin: 0 0 15px 0;">Troubleshooting:</h4>
    <ul style="color: #6b7280; font-size: 14px; line-height: 1.6; margin: 0; padding-left: 20px;">
        <li>Make sure you enter the code exactly as shown (including any spaces)</li>
        <li>The code is case-sensitive and must be entered within {{ expiry_minutes or 15 }} minutes</li>
        <li>If the code has expired, request a new password reset</li>
        <li>Clear your browser cache if you're experiencing issues</li>
        <li>Make sure you're using a supported browser</li>
    </ul>
</div>

<!-- Alternative Contact -->
<div style="margin-top: 30px; padding: 20px; background-color: #f8fafc; border-radius: 6px;">
    <h4 style="margin: 0 0 10px 0; color: #374151; font-size: 16px;">Need Help?</h4>
    <p style="margin: 0; color: #6b7280; font-size: 14px;">
        If you can't access your account or are experiencing issues, contact your system administrator:
    </p>
    <ul style="margin: 10px 0 0 0; padding-left: 20px; color: #6b7280; font-size: 14px;">
        <li>Email: {{ support_email or '<EMAIL>' }}</li>
        {% if support_phone %}
        <li>Phone: {{ support_phone }}</li>
        {% endif %}
        <li>Include your username and this reset request time in your message</li>
    </ul>
</div>

{% endblock %}

{% block footer %}
<p>
    This email was sent from {{ company_name or 'KaziSync HRMS' }}<br>
    <a href="mailto:{{ company_email or support_email }}">{{ company_email or support_email }}</a>
</p>
{% if company_address %}
<p>{{ company_address }}</p>
{% endif %}
<p style="margin-top: 20px; font-size: 12px; color: #9ca3af;">
    This is an automated security email. Please do not reply to this message.<br>
    If you have questions, contact your system administrator.
</p>
<p style="margin-top: 15px;">
    © {{ current_year }} {{ company_name or 'KaziSync' }}. All rights reserved.
</p>
{% endblock %}
