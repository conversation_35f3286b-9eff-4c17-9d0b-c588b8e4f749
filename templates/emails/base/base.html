<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>{{ subject or 'KaziSync HRMS' }}</title>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    <style>
        /* Reset styles */
        body, table, td, p, a, li, blockquote {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        table, td {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        img {
            -ms-interpolation-mode: bicubic;
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
        }
        
        /* Base styles */
        body {
            margin: 0 !important;
            padding: 0 !important;
            background-color: #f4f4f4;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 16px;
            line-height: 1.6;
            color: #333333;
        }
        
        /* Container */
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        /* Header */
        .email-header {
            background: linear-gradient(135deg, {{ company_primary_color or '#1e40af' }}, {{ company_secondary_color or '#3b82f6' }});
            color: #ffffff;
            padding: 30px;
            text-align: center;
        }
        
        .email-header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
            letter-spacing: -0.5px;
        }
        
        .email-header p {
            margin: 10px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
        }
        
        /* Content */
        .email-content {
            padding: 40px 30px;
        }
        
        .email-content h2 {
            color: #1f2937;
            font-size: 24px;
            font-weight: 600;
            margin: 0 0 20px 0;
            line-height: 1.3;
        }
        
        .email-content h3 {
            color: #374151;
            font-size: 20px;
            font-weight: 600;
            margin: 30px 0 15px 0;
        }
        
        .email-content p {
            color: #6b7280;
            line-height: 1.6;
            margin: 0 0 20px 0;
        }
        
        /* Buttons */
        .btn {
            display: inline-block;
            padding: 14px 28px;
            background-color: {{ company_primary_color or '#1e40af' }};
            color: #ffffff !important;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            transition: background-color 0.3s ease;
        }
        
        .btn:hover {
            background-color: #1d4ed8;
        }
        
        .btn-secondary {
            background-color: #6b7280;
        }
        
        .btn-secondary:hover {
            background-color: #4b5563;
        }
        
        /* Code/OTP box */
        .code-box {
            background-color: #f8fafc;
            border: 2px dashed {{ company_primary_color or '#1e40af' }};
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            margin: 30px 0;
        }
        
        .code-text {
            font-size: 36px;
            font-weight: bold;
            color: {{ company_primary_color or '#1e40af' }};
            letter-spacing: 8px;
            font-family: 'Courier New', monospace;
            margin: 0;
        }
        
        .code-subtitle {
            color: #6b7280;
            margin: 15px 0 0 0;
            font-size: 14px;
        }
        
        /* Alert boxes */
        .alert {
            padding: 15px;
            margin: 20px 0;
            border-radius: 6px;
            border-left: 4px solid;
        }
        
        .alert-warning {
            background-color: #fef3c7;
            border-left-color: #f59e0b;
            color: #92400e;
        }
        
        .alert-info {
            background-color: #dbeafe;
            border-left-color: #3b82f6;
            color: #1e40af;
        }
        
        .alert-success {
            background-color: #d1fae5;
            border-left-color: #10b981;
            color: #065f46;
        }
        
        .alert-danger {
            background-color: #fee2e2;
            border-left-color: #ef4444;
            color: #991b1b;
        }
        
        /* Footer */
        .email-footer {
            background-color: #f8fafc;
            padding: 30px;
            border-top: 1px solid #e5e7eb;
            text-align: center;
        }
        
        .email-footer p {
            margin: 0 0 10px 0;
            color: #6b7280;
            font-size: 14px;
        }
        
        .email-footer a {
            color: {{ company_primary_color or '#1e40af' }};
            text-decoration: none;
        }
        
        /* Responsive */
        @media only screen and (max-width: 600px) {
            .email-container {
                width: 100% !important;
                margin: 0 !important;
                border-radius: 0 !important;
            }
            
            .email-header,
            .email-content,
            .email-footer {
                padding: 20px !important;
            }
            
            .email-header h1 {
                font-size: 24px !important;
            }
            
            .email-content h2 {
                font-size: 20px !important;
            }
            
            .code-text {
                font-size: 28px !important;
                letter-spacing: 4px !important;
            }
            
            .btn {
                display: block !important;
                width: 100% !important;
                box-sizing: border-box !important;
            }
        }
        
        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            .email-container {
                background-color: #1f2937 !important;
            }
            
            .email-content {
                background-color: #1f2937 !important;
            }
            
            .email-content h2,
            .email-content h3 {
                color: #f9fafb !important;
            }
            
            .email-content p {
                color: #d1d5db !important;
            }
            
            .email-footer {
                background-color: #111827 !important;
            }
        }
    </style>
</head>
<body>
    <div style="padding: 20px 0;">
        <div class="email-container">
            <!-- Header -->
            <div class="email-header">
                {% if company_logo_url %}
                <img src="{{ company_logo_url }}" alt="{{ company_name or 'KaziSync' }}" style="max-height: 50px; margin-bottom: 15px;">
                {% endif %}
                <h1>{{ company_name or 'KaziSync HRMS' }}</h1>
                {% block header_subtitle %}
                <p>Human Resource Management System</p>
                {% endblock %}
            </div>
            
            <!-- Content -->
            <div class="email-content">
                {% block content %}
                <h2>Hello!</h2>
                <p>This is a default email template. Please override the content block.</p>
                {% endblock %}
            </div>
            
            <!-- Footer -->
            <div class="email-footer">
                {% block footer %}
                <p>
                    This email was sent from {{ company_name or 'KaziSync HRMS' }}<br>
                    <a href="mailto:{{ company_email or support_email }}">{{ company_email or support_email }}</a>
                </p>
                {% if company_address %}
                <p>{{ company_address }}</p>
                {% endif %}
                <p style="margin-top: 20px;">
                    © {{ current_year }} {{ company_name or 'KaziSync' }}. All rights reserved.
                </p>
                {% endblock %}
            </div>
        </div>
    </div>
</body>
</html>
