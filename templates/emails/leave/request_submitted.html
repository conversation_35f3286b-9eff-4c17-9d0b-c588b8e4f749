{% extends "emails/base/base.html" %}

{% block header_subtitle %}
<p>Leave Request Notification</p>
{% endblock %}

{% block content %}
<h2>{{ notification_title or 'New Leave Request' }}</h2>

<p>
    Hello {{ approver_name or 'Manager' }},
</p>

<p>
    A new leave request has been submitted and requires your review and approval.
</p>

<!-- Request Details -->
<div style="background-color: #f8fafc; padding: 25px; border-radius: 8px; margin: 25px 0; border-left: 4px solid {{ company_primary_color or '#1e40af' }};">
    <h3 style="margin: 0 0 20px 0; color: #1f2937; font-size: 20px;">Leave Request Details</h3>
    
    <table style="width: 100%; font-size: 15px; line-height: 1.6;">
        <tr>
            <td style="padding: 8px 0; font-weight: 600; color: #374151; width: 30%;">Employee:</td>
            <td style="padding: 8px 0; color: #6b7280;">{{ employee_name or 'N/A' }}</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; font-weight: 600; color: #374151;">Department:</td>
            <td style="padding: 8px 0; color: #6b7280;">{{ department_name or 'N/A' }}</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; font-weight: 600; color: #374151;">Leave Type:</td>
            <td style="padding: 8px 0; color: #6b7280;">{{ leave_type or 'N/A' }}</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; font-weight: 600; color: #374151;">Start Date:</td>
            <td style="padding: 8px 0; color: #6b7280;">{{ start_date or 'N/A' }}</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; font-weight: 600; color: #374151;">End Date:</td>
            <td style="padding: 8px 0; color: #6b7280;">{{ end_date or 'N/A' }}</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; font-weight: 600; color: #374151;">Total Days:</td>
            <td style="padding: 8px 0; color: #6b7280; font-weight: 600;">{{ total_days or 'N/A' }} days</td>
        </tr>
        <tr>
            <td style="padding: 8px 0; font-weight: 600; color: #374151;">Submitted:</td>
            <td style="padding: 8px 0; color: #6b7280;">{{ submission_date or current_date }}</td>
        </tr>
    </table>
</div>

<!-- Reason for Leave -->
{% if reason %}
<div style="margin: 25px 0;">
    <h4 style="color: #374151; font-size: 16px; margin: 0 0 10px 0;">Reason for Leave:</h4>
    <div style="background-color: #f9fafb; padding: 15px; border-radius: 6px; border-left: 3px solid #d1d5db;">
        <p style="margin: 0; color: #6b7280; font-style: italic;">{{ reason }}</p>
    </div>
</div>
{% endif %}

<!-- Current Leave Balance -->
{% if leave_balance %}
<div style="margin: 25px 0;">
    <h4 style="color: #374151; font-size: 16px; margin: 0 0 15px 0;">Current Leave Balance:</h4>
    <div style="background-color: #ecfdf5; padding: 15px; border-radius: 6px; border-left: 3px solid #10b981;">
        <table style="width: 100%; font-size: 14px;">
            <tr>
                <td style="padding: 5px 0; color: #065f46; font-weight: 600;">Available Days:</td>
                <td style="padding: 5px 0; color: #065f46; font-weight: 600;">{{ leave_balance.available or 'N/A' }} days</td>
            </tr>
            <tr>
                <td style="padding: 5px 0; color: #059669;">Used Days:</td>
                <td style="padding: 5px 0; color: #059669;">{{ leave_balance.used or 'N/A' }} days</td>
            </tr>
            <tr>
                <td style="padding: 5px 0; color: #059669;">Total Allocation:</td>
                <td style="padding: 5px 0; color: #059669;">{{ leave_balance.total or 'N/A' }} days</td>
            </tr>
        </table>
    </div>
</div>
{% endif %}

<!-- Action Required -->
{% if action_required %}
<div class="alert alert-info">
    <p style="margin: 0; font-size: 15px;">
        <strong>Action Required:</strong> This leave request is pending your approval. Please review the details above and take appropriate action.
    </p>
</div>
{% endif %}

<!-- Action Buttons -->
{% if action_url %}
<div style="text-align: center; margin: 35px 0;">
    <a href="{{ action_url }}" class="btn" style="margin-right: 15px;">
        {{ action_text or 'Review Request' }}
    </a>
    {% if approve_url %}
    <a href="{{ approve_url }}" class="btn" style="background-color: #10b981; margin-right: 10px;">
        Approve
    </a>
    {% endif %}
    {% if reject_url %}
    <a href="{{ reject_url }}" class="btn btn-secondary">
        Reject
    </a>
    {% endif %}
</div>
{% endif %}

<!-- Approval Workflow -->
{% if approval_workflow %}
<div style="margin: 25px 0;">
    <h4 style="color: #374151; font-size: 16px; margin: 0 0 15px 0;">Approval Workflow:</h4>
    <div style="background-color: #f8fafc; padding: 15px; border-radius: 6px;">
        {% for step in approval_workflow %}
        <div style="display: flex; align-items: center; margin: 10px 0;">
            {% if step.status == 'completed' %}
            <span style="color: #10b981; margin-right: 10px;">✓</span>
            {% elif step.status == 'current' %}
            <span style="color: #f59e0b; margin-right: 10px;">⏳</span>
            {% else %}
            <span style="color: #9ca3af; margin-right: 10px;">○</span>
            {% endif %}
            <span style="color: #6b7280; font-size: 14px;">{{ step.approver_name }} ({{ step.role }})</span>
        </div>
        {% endfor %}
    </div>
</div>
{% endif %}

<!-- Important Notes -->
<div class="alert alert-warning">
    <h4 style="margin: 0 0 10px 0; color: #92400e; font-size: 16px;">Important Notes:</h4>
    <ul style="margin: 0; padding-left: 20px; font-size: 14px; color: #92400e;">
        <li>Please review this request within {{ review_deadline or '2 business days' }}</li>
        <li>Consider the employee's current workload and project commitments</li>
        <li>Check for any scheduling conflicts with other team members</li>
        {% if policy_notes %}
        <li>{{ policy_notes }}</li>
        {% endif %}
    </ul>
</div>

<!-- Contact Information -->
<div style="margin-top: 30px; padding: 20px; background-color: #f8fafc; border-radius: 6px;">
    <h4 style="margin: 0 0 10px 0; color: #374151; font-size: 16px;">Need More Information?</h4>
    <p style="margin: 0; color: #6b7280; font-size: 14px;">
        If you need additional details about this leave request, you can:
    </p>
    <ul style="margin: 10px 0 0 0; padding-left: 20px; color: #6b7280; font-size: 14px;">
        <li>Contact the employee directly: {{ employee_email or 'N/A' }}</li>
        <li>Review the request in the HRMS system</li>
        <li>Contact HR for policy clarification</li>
    </ul>
</div>

{% endblock %}

{% block footer %}
<p>
    This email was sent from {{ company_name or 'KaziSync HRMS' }}<br>
    <a href="mailto:{{ company_email or support_email }}">{{ company_email or support_email }}</a>
</p>
{% if company_address %}
<p>{{ company_address }}</p>
{% endif %}
<p style="margin-top: 20px; font-size: 12px; color: #9ca3af;">
    This is an automated notification from the HRMS system.<br>
    Please do not reply to this email directly.
</p>
<p style="margin-top: 15px;">
    © {{ current_year }} {{ company_name or 'KaziSync' }}. All rights reserved.
</p>
{% endblock %}
