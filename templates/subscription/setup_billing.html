<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Set Up Subscription Billing - KaziSync HRMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .payment-option {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .payment-option:hover {
            border-color: #0d6efd;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .payment-option.selected {
            border-color: #0d6efd;
            background-color: #f8f9fa;
        }
        
        .payment-form {
            display: none;
            margin-top: 20px;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background-color: #fff;
        }
        
        .payment-form.active {
            display: block;
        }
        
        .paypal-card-form .form-group {
            margin-bottom: 15px;
        }
        
        .paypal-card-form .form-row {
            display: flex;
            gap: 15px;
        }
        
        .paypal-card-form .form-row .form-group {
            flex: 1;
        }
        
        .paypal-card-form label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .paypal-card-form .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .paypal-card-form div[id$="-field"] {
            height: 40px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            padding: 10px;
        }
        
        .billing-summary {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .security-notice {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .success-message {
            display: none;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .error-message {
            display: none;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="mb-0">Set Up Automatic Billing</h3>
                        <p class="text-muted mb-0">Choose your preferred payment method for monthly subscription billing</p>
                    </div>
                    <div class="card-body">
                        <!-- Success Message -->
                        <div id="success-message" class="success-message">
                            <h5>✓ Billing Set Up Successfully!</h5>
                            <p>Your automatic billing has been configured. You'll be charged monthly based on your employee count.</p>
                        </div>

                        <!-- Error Message -->
                        <div id="error-message" class="error-message">
                            <h5>⚠ Error Setting Up Billing</h5>
                            <p id="error-text">There was an error setting up your billing. Please try again.</p>
                        </div>

                        <!-- Loading -->
                        <div id="loading" class="loading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Setting up your billing...</p>
                        </div>

                        <!-- Billing Summary -->
                        <div class="billing-summary">
                            <h5>Subscription Summary</h5>
                            <div class="row">
                                <div class="col-sm-6">
                                    <strong>Plan:</strong> <span id="plan-name">Professional</span>
                                </div>
                                <div class="col-sm-6">
                                    <strong>Price per Employee:</strong> $<span id="price-per-employee">12.00</span>/month
                                </div>
                                <div class="col-sm-6">
                                    <strong>Current Employees:</strong> <span id="employee-count">25</span>
                                </div>
                                <div class="col-sm-6">
                                    <strong>Monthly Total:</strong> $<span id="monthly-total">300.00</span>
                                </div>
                            </div>
                        </div>

                        <!-- Security Notice -->
                        <div class="security-notice">
                            <h6>🔒 Secure Payment Processing</h6>
                            <p class="mb-0">Your payment information is processed securely by PayPal. We never store your credit card details on our servers.</p>
                        </div>

                        <!-- Payment Options -->
                        <div id="payment-options">
                            <h5>Choose Payment Method</h5>
                            
                            <!-- PayPal Account Option -->
                            <div class="payment-option" data-method="paypal">
                                <div class="d-flex align-items-center">
                                    <input type="radio" name="payment-method" value="paypal" id="paypal-radio" class="me-3">
                                    <div>
                                        <h6 class="mb-1">PayPal Account</h6>
                                        <p class="text-muted mb-0">Pay with your existing PayPal account</p>
                                    </div>
                                    <div class="ms-auto">
                                        <img src="https://www.paypalobjects.com/webstatic/mktg/Logo/pp-logo-100px.png" alt="PayPal" height="30">
                                    </div>
                                </div>
                            </div>

                            <!-- Credit Card Option -->
                            <div class="payment-option" data-method="card">
                                <div class="d-flex align-items-center">
                                    <input type="radio" name="payment-method" value="card" id="card-radio" class="me-3">
                                    <div>
                                        <h6 class="mb-1">Credit or Debit Card</h6>
                                        <p class="text-muted mb-0">Pay with your credit or debit card (processed by PayPal)</p>
                                    </div>
                                    <div class="ms-auto">
                                        <img src="https://www.paypalobjects.com/webstatic/mktg/logos/Visa_mc_amex_disc_2x.png" alt="Cards" height="30">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- PayPal Payment Form -->
                        <div id="paypal-form" class="payment-form">
                            <h6>PayPal Payment</h6>
                            <p class="text-muted">Click the button below to set up automatic billing with your PayPal account.</p>
                            <div id="paypal-button-container"></div>
                        </div>

                        <!-- Credit Card Payment Form -->
                        <div id="card-form" class="payment-form">
                            <h6>Credit Card Payment</h6>
                            <p class="text-muted">Enter your card details to set up automatic monthly billing.</p>
                            <div id="card-form-container"></div>
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="mt-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="terms-checkbox" required>
                                <label class="form-check-label" for="terms-checkbox">
                                    I agree to the <a href="#" target="_blank">Terms of Service</a> and authorize automatic monthly billing based on my employee count.
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/paypal-subscription.js') }}"></script>
    <script>
        // Configuration - these would typically come from your backend
        const config = {
            companyId: '{{ company_id }}',
            planId: '{{ plan_id }}',
            clientId: '{{ paypal_client_id }}',
            environment: '{{ paypal_environment }}',
            apiBaseUrl: '/api/paypal'
        };

        // Initialize PayPal Subscription Manager
        const paypalManager = new PayPalSubscriptionManager({
            clientId: config.clientId,
            environment: config.environment,
            apiBaseUrl: config.apiBaseUrl,
            onSuccess: function(data) {
                document.getElementById('success-message').style.display = 'block';
                document.getElementById('payment-options').style.display = 'none';
                document.querySelector('.payment-form.active').style.display = 'none';
                document.getElementById('loading').style.display = 'none';
                
                // Redirect to dashboard after 3 seconds
                setTimeout(() => {
                    window.location.href = '/dashboard';
                }, 3000);
            },
            onError: function(error) {
                document.getElementById('error-message').style.display = 'block';
                document.getElementById('error-text').textContent = error.message || 'An error occurred';
                document.getElementById('loading').style.display = 'none';
            },
            onCancel: function(data) {
                document.getElementById('loading').style.display = 'none';
                alert('Payment setup was cancelled. You can try again anytime.');
            }
        });

        // Handle payment method selection
        document.querySelectorAll('.payment-option').forEach(option => {
            option.addEventListener('click', function() {
                const method = this.dataset.method;
                const radio = this.querySelector('input[type="radio"]');
                
                // Update selection
                document.querySelectorAll('.payment-option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
                radio.checked = true;
                
                // Show appropriate form
                document.querySelectorAll('.payment-form').forEach(form => {
                    form.classList.remove('active');
                });
                
                if (method === 'paypal') {
                    document.getElementById('paypal-form').classList.add('active');
                    renderPayPalButtons();
                } else if (method === 'card') {
                    document.getElementById('card-form').classList.add('active');
                    renderCardForm();
                }
            });
        });

        // Render PayPal buttons
        async function renderPayPalButtons() {
            try {
                await paypalManager.renderPayPalButtons(
                    'paypal-button-container',
                    config.companyId,
                    config.planId
                );
            } catch (error) {
                console.error('Error rendering PayPal buttons:', error);
            }
        }

        // Render credit card form
        async function renderCardForm() {
            try {
                await paypalManager.renderCreditCardForm(
                    'card-form-container',
                    config.companyId,
                    config.planId
                );
            } catch (error) {
                console.error('Error rendering card form:', error);
            }
        }

        // Show loading state
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('error-message').style.display = 'none';
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Check if billing is already set up
            paypalManager.getBillingStatus(config.companyId)
                .then(status => {
                    if (status.has_billing) {
                        document.getElementById('success-message').style.display = 'block';
                        document.getElementById('payment-options').style.display = 'none';
                    }
                })
                .catch(error => {
                    console.log('No existing billing found, showing setup options');
                });
        });
    </script>
</body>
</html>
