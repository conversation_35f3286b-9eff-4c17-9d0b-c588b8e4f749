<!DOCTYPE html>
<html>
<head>
    <title>Sign Up</title>
</head>
<body>
    <h1>Sign Up</h1>
    {% with messages = get_flashed_messages() %}
        {% if messages %}
            <ul>
                {% for message in messages %}
                    <li>{{ message }}</li>
                {% endfor %}
            </ul>
        {% endif %}
    {% endwith %}
    <form action="/register_user" method="post">
        <label for="username">Username</label>
        <input type="text" name="username" id="username" required>
        
        <label for="first_name">First Name</label>
        <input type="text" name="first_name" id="first_name" required>
        
        <label for="last_name">Last Name</label>
        <input type="text" name="last_name" id="last_name" required>
        
        <label for="email">Email</label>
        <input type="email" name="email" id="email" required>
        
        <label for="confirm_email">Confirm Email</label>
        <input type="email" name="confirm_email" id="confirm_email" required>
        
        <label for="password">Password</label>
        <input type="password" name="password" id="password" required>
        
        <label for="confirm_password">Confirm Password</label>
        <input type="password" name="confirm_password" id="confirm_password" required>
        
        <label for="phone_number">Phone</label>
        <input type="tel" name="phone_number" id="phone_number" required>
        
        <button type="submit">Sign Up</button>
    </form>
</body>
</html>
