#!/usr/bin/env python3
"""
Test script for automatic price lookup in service consumption creation.

This script tests the new functionality where price_id is automatically determined
based on service_id and consumption date.
"""

import sys
import os
import uuid
from datetime import datetime, date, timedelta

# Add the application directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from application.Models.customers.services import Service, ServicePrice, CustomerServiceConsumption
from application.Models.customers.customer import Customer
from application.Models.company import Company
from application.utils.db_connection import DatabaseConnection

def test_automatic_price_lookup():
    """Test automatic price lookup functionality."""
    
    print("🧪 Testing Automatic Price Lookup for Service Consumption")
    print("=" * 60)
    
    # Test configuration
    test_company_id = "123e4567-e89b-12d3-a456-************"  # Replace with actual company ID
    
    try:
        # Get database connection
        database_name = Company.get_database_given_company_id(test_company_id)
        if not database_name:
            print(f"❌ Company with ID {test_company_id} not found")
            return False
            
        db_connection = DatabaseConnection()
        
        with db_connection.get_session(database_name) as session:
            print(f"✅ Connected to database: {database_name}")
            
            # Test 1: Create a test service
            print("\n📋 Test 1: Creating test service...")
            test_service = Service(
                service_id=uuid.uuid4(),
                name="Test Sauna Session",
                description="Test sauna session for automatic pricing",
                category="Wellness",
                service_code="TEST_SAUNA_001",
                is_active=True
            )
            session.add(test_service)
            session.flush()  # Get the ID without committing
            print(f"✅ Created test service: {test_service.service_id}")
            
            # Test 2: Create price history for the service
            print("\n💰 Test 2: Creating price history...")
            
            # Old price (expired)
            old_price = ServicePrice(
                price_id=uuid.uuid4(),
                service_id=test_service.service_id,
                price_amount=25.00,
                currency="USD",
                effective_from=date.today() - timedelta(days=30),
                effective_to=date.today() - timedelta(days=1)
            )
            session.add(old_price)
            
            # Current price (active)
            current_price = ServicePrice(
                price_id=uuid.uuid4(),
                service_id=test_service.service_id,
                price_amount=30.00,
                currency="USD",
                effective_from=date.today(),
                effective_to=None  # No end date = current price
            )
            session.add(current_price)
            session.flush()
            
            print(f"✅ Created old price: ${old_price.price_amount} (expired)")
            print(f"✅ Created current price: ${current_price.price_amount} (active)")
            
            # Test 3: Get a test customer
            print("\n👤 Test 3: Getting test customer...")
            customer = session.query(Customer).first()
            if not customer:
                print("❌ No customers found in database. Please create a customer first.")
                return False
            print(f"✅ Using customer: {customer.customer_id}")
            
            # Test 4: Create consumption WITHOUT price_id (automatic lookup)
            print("\n🔄 Test 4: Creating consumption with automatic price lookup...")
            
            consumption_data = {
                'customer_id': customer.customer_id,
                'service_id': test_service.service_id,
                'quantity': 2,
                'notes': 'Test consumption with automatic price lookup'
                # Note: NO price_id provided - should be auto-determined
            }
            
            consumption = CustomerServiceConsumption.create_consumption(
                session=session, 
                **consumption_data
            )
            
            if consumption:
                print(f"✅ Created consumption: {consumption.consumption_id}")
                print(f"✅ Auto-determined price_id: {consumption.price_id}")
                print(f"✅ Expected price_id: {current_price.price_id}")
                print(f"✅ Quantity: {consumption.quantity}")
                print(f"✅ Total amount: ${consumption.total_amount}")
                print(f"✅ Expected total: ${current_price.price_amount * consumption.quantity}")
                
                # Verify correct price was selected
                if str(consumption.price_id) == str(current_price.price_id):
                    print("✅ PASS: Correct current price was automatically selected")
                else:
                    print("❌ FAIL: Wrong price was selected")
                    return False
                    
                # Verify total calculation
                expected_total = float(current_price.price_amount * consumption.quantity)
                if float(consumption.total_amount) == expected_total:
                    print("✅ PASS: Total amount calculated correctly")
                else:
                    print(f"❌ FAIL: Total calculation wrong. Expected: ${expected_total}, Got: ${consumption.total_amount}")
                    return False
            else:
                print("❌ FAIL: Failed to create consumption")
                return False
            
            # Test 5: Create consumption WITH explicit price_id (should still work)
            print("\n🎯 Test 5: Creating consumption with explicit price_id...")
            
            consumption_data_explicit = {
                'customer_id': customer.customer_id,
                'service_id': test_service.service_id,
                'price_id': current_price.price_id,  # Explicitly provided
                'quantity': 1,
                'notes': 'Test consumption with explicit price_id'
            }
            
            consumption_explicit = CustomerServiceConsumption.create_consumption(
                session=session, 
                **consumption_data_explicit
            )
            
            if consumption_explicit:
                print(f"✅ Created consumption with explicit price: {consumption_explicit.consumption_id}")
                print("✅ PASS: Explicit price_id still works")
            else:
                print("❌ FAIL: Failed to create consumption with explicit price_id")
                return False
            
            # Test 6: Test with historical date (should select old price)
            print("\n📅 Test 6: Testing historical date consumption...")
            
            historical_date = datetime.now() - timedelta(days=15)  # 15 days ago
            consumption_data_historical = {
                'customer_id': customer.customer_id,
                'service_id': test_service.service_id,
                'quantity': 1,
                'consumed_at': historical_date,
                'notes': 'Test consumption with historical date'
            }
            
            consumption_historical = CustomerServiceConsumption.create_consumption(
                session=session, 
                **consumption_data_historical
            )
            
            if consumption_historical:
                print(f"✅ Created historical consumption: {consumption_historical.consumption_id}")
                print(f"✅ Selected price_id: {consumption_historical.price_id}")
                print(f"✅ Expected old price_id: {old_price.price_id}")
                
                if str(consumption_historical.price_id) == str(old_price.price_id):
                    print("✅ PASS: Correct historical price was automatically selected")
                else:
                    print("❌ FAIL: Wrong historical price was selected")
                    return False
            else:
                print("❌ FAIL: Failed to create historical consumption")
                return False
            
            # Cleanup (rollback to not affect actual data)
            session.rollback()
            print("\n🧹 Cleaned up test data (rolled back)")
            
            print("\n🎉 ALL TESTS PASSED!")
            print("✅ Automatic price lookup is working correctly")
            return True
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_automatic_price_lookup()
    sys.exit(0 if success else 1)
