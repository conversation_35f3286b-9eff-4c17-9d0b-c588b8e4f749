#!/usr/bin/env python3
"""
Test script for bulk service consumption creation.

This script tests the new bulk endpoint that allows creating multiple
service consumption records in a single transaction.
"""

import sys
import os
import uuid
import json
from datetime import datetime, date, timedelta

# Add the application directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from application.Models.customers.services import Service, ServicePrice, CustomerServiceConsumption
from application.Models.customers.customer import Customer
from application.Models.company import Company
from application.utils.db_connection import DatabaseConnection

def test_bulk_service_consumption():
    """Test bulk service consumption creation functionality."""
    
    print("🧪 Testing Bulk Service Consumption Creation")
    print("=" * 60)
    
    # Test configuration
    test_company_id = "123e4567-e89b-12d3-a456-************"  # Replace with actual company ID
    
    try:
        # Get database connection
        database_name = Company.get_database_given_company_id(test_company_id)
        if not database_name:
            print(f"❌ Company with ID {test_company_id} not found")
            return False
            
        db_connection = DatabaseConnection()
        
        with db_connection.get_session(database_name) as session:
            print(f"✅ Connected to database: {database_name}")
            
            # Test 1: Create test services
            print("\n📋 Test 1: Creating test services...")
            
            services_data = [
                {
                    'name': 'Sauna Session',
                    'description': 'Traditional Finnish sauna',
                    'category': 'Wellness',
                    'service_code': 'SAUNA_001',
                    'price': 25.00
                },
                {
                    'name': 'Deep Tissue Massage',
                    'description': '60-minute therapeutic massage',
                    'category': 'Therapy',
                    'service_code': 'MASSAGE_001',
                    'price': 80.00
                },
                {
                    'name': 'Herbal Tea',
                    'description': 'Organic herbal tea blend',
                    'category': 'Beverage',
                    'service_code': 'TEA_001',
                    'price': 5.00
                }
            ]
            
            test_services = []
            for service_data in services_data:
                service = Service(
                    service_id=uuid.uuid4(),
                    name=service_data['name'],
                    description=service_data['description'],
                    category=service_data['category'],
                    service_code=service_data['service_code'],
                    is_active=True
                )
                session.add(service)
                session.flush()
                
                # Create current price
                price = ServicePrice(
                    price_id=uuid.uuid4(),
                    service_id=service.service_id,
                    price_amount=service_data['price'],
                    currency="USD",
                    effective_from=date.today(),
                    effective_to=None
                )
                session.add(price)
                session.flush()
                
                test_services.append({
                    'service': service,
                    'price': price
                })
                
                print(f"✅ Created service: {service.name} (${service_data['price']})")
            
            # Test 2: Get test customer
            print("\n👤 Test 2: Getting test customer...")
            customer = session.query(Customer).first()
            if not customer:
                print("❌ No customers found in database. Please create a customer first.")
                return False
            print(f"✅ Using customer: {customer.customer_id}")
            
            # Test 3: Test bulk consumption creation
            print("\n🔄 Test 3: Creating bulk service consumption...")
            
            bulk_services = [
                {
                    'service_id': test_services[0]['service'].service_id,  # Sauna
                    'quantity': 2,
                    'notes': 'Extended sauna sessions'
                },
                {
                    'service_id': test_services[1]['service'].service_id,  # Massage
                    'quantity': 1,
                    'notes': 'Deep tissue massage therapy'
                },
                {
                    'service_id': test_services[2]['service'].service_id,  # Tea
                    'quantity': 3,
                    'notes': 'Herbal tea during relaxation'
                }
            ]
            
            result = CustomerServiceConsumption.create_bulk_consumption(
                session=session,
                customer_id=customer.customer_id,
                services=bulk_services,
                consumed_at=datetime.now()
            )
            
            if result['success']:
                print(f"✅ Created {result['summary']['total_services']} consumption records")
                print(f"✅ Total amount: ${result['summary']['total_amount']}")
                
                # Verify calculations
                expected_total = (25.00 * 2) + (80.00 * 1) + (5.00 * 3)  # $50 + $80 + $15 = $145
                if result['summary']['total_amount'] == expected_total:
                    print("✅ PASS: Total amount calculated correctly")
                else:
                    print(f"❌ FAIL: Total calculation wrong. Expected: ${expected_total}, Got: ${result['summary']['total_amount']}")
                    return False
                
                # Verify service details
                print("\n📊 Service breakdown:")
                for service_info in result['summary']['services']:
                    print(f"  • {service_info['service_name']}: {service_info['quantity']}x ${service_info['unit_price']} = ${service_info['total_price']}")
                
            else:
                print("❌ FAIL: Failed to create bulk consumption")
                return False
            
            # Test 4: Test validation errors
            print("\n🚨 Test 4: Testing validation errors...")
            
            # Test empty services list
            try:
                CustomerServiceConsumption.create_bulk_consumption(
                    session=session,
                    customer_id=customer.customer_id,
                    services=[],
                    consumed_at=datetime.now()
                )
                print("❌ FAIL: Should have failed with empty services list")
                return False
            except ValueError as e:
                print("✅ PASS: Correctly rejected empty services list")
            
            # Test invalid service_id
            try:
                CustomerServiceConsumption.create_bulk_consumption(
                    session=session,
                    customer_id=customer.customer_id,
                    services=[{'service_id': uuid.uuid4(), 'quantity': 1}],  # Non-existent service
                    consumed_at=datetime.now()
                )
                print("❌ FAIL: Should have failed with invalid service_id")
                return False
            except ValueError as e:
                print("✅ PASS: Correctly rejected invalid service_id")
            
            # Test 5: Test transaction rollback
            print("\n🔄 Test 5: Testing transaction rollback...")
            
            # Create a mix of valid and invalid services (should rollback all)
            mixed_services = [
                {
                    'service_id': test_services[0]['service'].service_id,  # Valid
                    'quantity': 1,
                    'notes': 'Valid service'
                },
                {
                    'service_id': uuid.uuid4(),  # Invalid - doesn't exist
                    'quantity': 1,
                    'notes': 'Invalid service'
                }
            ]
            
            try:
                CustomerServiceConsumption.create_bulk_consumption(
                    session=session,
                    customer_id=customer.customer_id,
                    services=mixed_services,
                    consumed_at=datetime.now()
                )
                print("❌ FAIL: Should have failed and rolled back")
                return False
            except ValueError as e:
                print("✅ PASS: Correctly failed and rolled back transaction")
            
            # Cleanup (rollback to not affect actual data)
            session.rollback()
            print("\n🧹 Cleaned up test data (rolled back)")
            
            print("\n🎉 ALL TESTS PASSED!")
            print("✅ Bulk service consumption is working correctly")
            return True
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_bulk_service_consumption()
    sys.exit(0 if success else 1)
