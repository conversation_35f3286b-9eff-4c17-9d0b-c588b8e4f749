#!/usr/bin/env python3
"""
Test script to verify that Rwanda leave policies are automatically set up
when creating a new company.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import json
import uuid
from datetime import datetime

class CompanyCreationTest:
    def __init__(self, base_url, auth_token):
        self.base_url = base_url.rstrip('/')
        self.headers = {
            'Authorization': f'Bearer {auth_token}',
            'Content-Type': 'application/json'
        }
    
    def test_create_company_with_rwanda_setup(self):
        """Test creating a company and verify Rwanda leave setup happens automatically."""
        print("🧪 Testing Automatic Rwanda Leave Setup on Company Creation")
        print("=" * 60)
        
        # Generate unique company data
        company_tin = f"TEST{datetime.now().strftime('%Y%m%d%H%M%S')}"
        company_name = f"Test Company {datetime.now().strftime('%H:%M:%S')}"
        
        print(f"📋 Creating test company:")
        print(f"   • Name: {company_name}")
        print(f"   • TIN: {company_tin}")
        
        # Create company
        url = f"{self.base_url}/add_company"
        data = {
            'company_name': company_name,
            'company_tin': company_tin,
            'phone_number': '+250788123456'
        }
        
        response = requests.post(url, json=data, headers=self.headers)
        
        if response.status_code != 200:
            print(f"❌ Failed to create company: {response.status_code} - {response.text}")
            return False
        
        response_data = response.json()
        company = response_data.get('company', {})
        company_id = company.get('company_id')
        
        print(f"✅ Company created successfully!")
        print(f"   • Company ID: {company_id}")
        print(f"   • Database: {company.get('database_name')}")
        
        # Check if leave setup was included in response
        leave_setup = response_data.get('leave_setup')
        if leave_setup:
            print(f"\n🇷🇼 Leave Setup Result:")
            print(f"   • Success: {leave_setup.get('success')}")
            print(f"   • Message: {leave_setup.get('message')}")

            created_policies = leave_setup.get('created_policies', [])
            skipped_policies = leave_setup.get('skipped_policies', [])

            if created_policies:
                print(f"   • Created {len(created_policies)} new leave policies:")
                for policy in created_policies:
                    gender_note = f" ({policy['gender_specific']})" if policy['gender_specific'] else ""
                    print(f"     - {policy['leave_type']}: {policy['days_allowed']} days{gender_note}")

            if skipped_policies:
                print(f"   • Skipped {len(skipped_policies)} existing policies:")
                for policy in skipped_policies:
                    print(f"     - {policy['leave_type']}: {policy['days_allowed']} days ({policy['reason']})")
        else:
            print(f"\n⚠️  No leave setup information in response")
        
        # Verify leave types were created by checking the API
        print(f"\n🔍 Verifying leave types were created...")
        
        leave_types_url = f"{self.base_url}/api/leave/types"
        params = {'company_id': company_id}
        
        leave_response = requests.get(leave_types_url, params=params, headers=self.headers)
        
        if leave_response.status_code == 200:
            leave_data = leave_response.json()
            leave_types = leave_data.get('leave_types', [])
            
            print(f"✅ Found {len(leave_types)} leave types:")
            
            # Expected Rwanda leave types
            expected_types = [
                'ANNUAL', 'SICK', 'MATERNITY', 'PATERNITY',
                'CIRCUMSTANTIAL_MARRIAGE', 'CIRCUMSTANTIAL_SPOUSE_DEATH',
                'CIRCUMSTANTIAL_SPOUSE_DEATH_INFANT', 'CIRCUMSTANTIAL_CHILD_DEATH',
                'CIRCUMSTANTIAL_PARENT_DEATH', 'CIRCUMSTANTIAL_SIBLING_DEATH',
                'CIRCUMSTANTIAL_GRANDPARENT_DEATH', 'CIRCUMSTANTIAL_TRANSFER'
            ]
            
            found_codes = [lt['code'] for lt in leave_types]
            
            for leave_type in leave_types:
                status = "✅" if leave_type['code'] in expected_types else "❓"
                print(f"   {status} {leave_type['name']} ({leave_type['code']})")
            
            # Check if all expected types are present
            missing_types = [code for code in expected_types if code not in found_codes]
            if missing_types:
                print(f"\n❌ Missing expected leave types: {missing_types}")
                return False
            else:
                print(f"\n🎉 All expected Rwanda leave types are present!")
                
        else:
            print(f"❌ Failed to get leave types: {leave_response.status_code} - {leave_response.text}")
            return False
        
        # Test leave policies
        print(f"\n🔍 Verifying leave policies were created...")
        
        policies_url = f"{self.base_url}/api/leave/policies"
        policies_response = requests.get(policies_url, params=params, headers=self.headers)
        
        if policies_response.status_code == 200:
            policies_data = policies_response.json()
            policies = policies_data.get('leave_policies', [])
            
            print(f"✅ Found {len(policies)} leave policies:")
            
            for policy in policies:
                gender_note = f" ({policy.get('gender_specific', 'all')})" if policy.get('gender_specific') else ""
                print(f"   • {policy.get('leave_type_name', 'Unknown')}: {policy['days_allowed']} days{gender_note}")
                
        else:
            print(f"❌ Failed to get leave policies: {policies_response.status_code} - {policies_response.text}")
        
        print(f"\n📊 SUMMARY:")
        print(f"   • Company Creation: ✅ Success")
        print(f"   • Leave Setup: {'✅ Success' if leave_setup and leave_setup.get('success') else '❌ Failed'}")
        print(f"   • Leave Types Created: ✅ {len(leave_types)} types")
        print(f"   • Leave Policies Created: ✅ {len(policies)} policies")
        
        return True

def main():
    """Main function to run the test."""
    if len(sys.argv) != 3:
        print("Usage: python test_company_creation_with_rwanda_setup.py <base_url> <auth_token>")
        print("\nExample:")
        print("  python test_company_creation_with_rwanda_setup.py http://localhost:9001 your_jwt_token")
        sys.exit(1)
    
    base_url = sys.argv[1]
    auth_token = sys.argv[2]
    
    tester = CompanyCreationTest(base_url, auth_token)
    success = tester.test_create_company_with_rwanda_setup()
    
    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()
