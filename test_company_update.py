#!/usr/bin/env python3
"""
Test script for company update functionality
"""

import requests
import json

# Configuration
BASE_URL = "http://localhost:5000"
TEST_TOKEN = "your-test-token-here"  # Replace with actual token

def test_company_update():
    """Test the company update endpoint"""
    
    # Headers for authentication
    headers = {
        "Authorization": f"Bearer {TEST_TOKEN}",
        "Content-Type": "application/json"
    }
    
    # Test data for updating company
    test_data = {
        "company_name": "Updated Test Company",
        "company_tin": "*********",
        "phone_number": "+************",
        "country_id": "some-country-uuid"  # Replace with actual country ID
    }
    
    company_id = "test-company-id"  # Replace with actual company ID
    
    print("Testing Company Update Endpoint")
    print("=" * 50)
    
    # Test PATCH request
    try:
        response = requests.patch(
            f"{BASE_URL}/api/companies/{company_id}",
            headers=headers,
            json=test_data
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ Company update successful!")
                
                # Check if country details are included
                company = data.get('company', {})
                country = company.get('country')
                
                if country:
                    print(f"✅ Country details included:")
                    print(f"   - Code: {country.get('code')}")
                    print(f"   - Name: {country.get('name')}")
                    print(f"   - Currency: {country.get('currency')}")
                else:
                    print("ℹ️  No country details (country_id not provided or null)")
            else:
                print(f"❌ Update failed: {data.get('message')}")
        else:
            print(f"❌ Request failed with status {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {e}")
    except Exception as e:
        print(f"❌ Error: {e}")

def test_partial_update():
    """Test partial update (only some fields)"""
    
    headers = {
        "Authorization": f"Bearer {TEST_TOKEN}",
        "Content-Type": "application/json"
    }
    
    # Test updating only company name
    test_data = {
        "company_name": "Partially Updated Company"
    }
    
    company_id = "test-company-id"  # Replace with actual company ID
    
    print("\nTesting Partial Company Update")
    print("=" * 50)
    
    try:
        response = requests.patch(
            f"{BASE_URL}/api/companies/{company_id}",
            headers=headers,
            json=test_data
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 200:
            print("✅ Partial update successful!")
        else:
            print(f"❌ Partial update failed")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def test_countries_endpoint():
    """Test the existing countries endpoint"""
    
    headers = {
        "Authorization": f"Bearer {TEST_TOKEN}",
        "Content-Type": "application/json"
    }
    
    print("\nTesting Countries Endpoint")
    print("=" * 50)
    
    try:
        response = requests.get(
            f"{BASE_URL}/api/countries",
            headers=headers
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            countries = data.get('countries', [])
            print(f"✅ Found {len(countries)} countries")
            
            # Show first few countries
            for i, country in enumerate(countries[:3]):
                print(f"   {i+1}. {country.get('name')} ({country.get('code')}) - {country.get('currency')}")
                
            if len(countries) > 3:
                print(f"   ... and {len(countries) - 3} more")
        else:
            print(f"❌ Countries request failed")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("Company Update API Test")
    print("=" * 50)
    print("⚠️  Make sure to:")
    print("   1. Update BASE_URL if needed")
    print("   2. Set valid TEST_TOKEN")
    print("   3. Use actual company_id and country_id")
    print("   4. Ensure server is running")
    print()
    
    # Uncomment to run tests
    # test_countries_endpoint()
    # test_partial_update()
    # test_company_update()
    
    print("Tests are commented out. Update configuration and uncomment to run.")
