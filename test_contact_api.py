#!/usr/bin/env python3
"""
Test script for Contact Us API
Run this to test the contact form endpoint
"""

import requests
import json

# Test data
test_data = {
    "name": "Test User",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "message": "This is a test message for the contact form API. Testing if everything works correctly.",
    "company": "Test Company",
    "subject": "API Test",
    "inquiry_type": "demo"
}

def test_contact_api():
    """Test the contact API endpoint"""

    # API endpoint (adjust URL as needed) - using the simple contact endpoint
    url = "http://localhost:5000/api/contact/send"
    
    headers = {
        "Content-Type": "application/json"
    }
    
    print("Testing Contact Us API...")
    print(f"URL: {url}")
    print(f"Data: {json.dumps(test_data, indent=2)}")
    print("-" * 50)
    
    try:
        response = requests.post(url, json=test_data, headers=headers, timeout=30)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print(f"Response Body: {response.text}")
        
        if response.status_code == 200:
            print("✅ SUCCESS: Contact form submitted successfully!")
        elif response.status_code == 429:
            print("⚠️  RATE LIMITED: Too many requests")
        elif response.status_code == 400:
            print("❌ VALIDATION ERROR: Check the request data")
        else:
            print(f"❌ ERROR: Unexpected status code {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ CONNECTION ERROR: Could not connect to the API")
        print("Make sure the Flask app is running on localhost:9000")
    except requests.exceptions.Timeout:
        print("❌ TIMEOUT ERROR: Request took too long")
    except Exception as e:
        print(f"❌ UNEXPECTED ERROR: {str(e)}")

def test_health_check():
    """Test the health check endpoint"""

    url = "http://localhost:5000/api/contact/health"
    
    print("\nTesting Health Check...")
    print(f"URL: {url}")
    print("-" * 50)
    
    try:
        response = requests.get(url, timeout=10)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ SUCCESS: Contact service is healthy!")
        else:
            print(f"❌ ERROR: Health check failed with status {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ CONNECTION ERROR: Could not connect to the API")
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")

def test_validation():
    """Test validation with invalid data"""
    
    print("\nTesting Validation...")
    print("-" * 50)
    
    # Test with missing required fields
    invalid_data = {
        "name": "",  # Empty name
        "email": "invalid-email",  # Invalid email
        "phone": "123",  # Too short phone
        "message": "Hi"  # Too short message
    }
    
    url = "http://localhost:5000/api/contact/send"
    headers = {"Content-Type": "application/json"}
    
    try:
        response = requests.post(url, json=invalid_data, headers=headers, timeout=10)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 400:
            print("✅ SUCCESS: Validation working correctly!")
        else:
            print(f"⚠️  UNEXPECTED: Expected 400 but got {response.status_code}")
            
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")

if __name__ == "__main__":
    print("🧪 Contact Us API Test Suite")
    print("=" * 60)
    
    # Run tests
    test_health_check()
    test_contact_api()
    test_validation()
    
    print("\n" + "=" * 60)
    print("Test completed!")
    print("\nNote: Check your email (<EMAIL>) and the test email")
    print("for the actual email delivery if the API test was successful.")
