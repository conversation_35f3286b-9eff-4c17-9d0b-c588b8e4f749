import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import os

# Try to load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ Loaded environment variables from .env file")
except ImportError:
    print("⚠️  dotenv not available, using system environment variables")

def send_email(sender_email, receiver_email, password, smtp_server, smtp_port, subject, html_message):
    try:
        # Connect to SMTP server
        server = smtplib.SMTP_SSL(smtp_server, smtp_port)
        server.login(sender_email, password)

        # Create a multipart message
        message = MIMEMultipart()
        message["From"] = sender_email
        message["To"] = receiver_email
        message["Subject"] = subject

        # Attach HTML message
        message.attach(MIMEText(html_message, "html"))

        # Send email
        server.sendmail(sender_email, receiver_email, message.as_string())
        server.quit()
        print("Email sent successfully!")
        return True
    except Exception as e:
        print(f"Failed to send email. Error: {e}")
        return False

if __name__ == '__main__':
    # Get email config from environment
    sender_email = os.getenv('MAIL_USERNAME')
    password = os.getenv('MAIL_PASSWORD')
    smtp_server = os.getenv('MAIL_SERVER')
    receiver_email = "<EMAIL>"
    smtp_port = 465  # SSL port
    subject = "TEST - Contact Form API - KaziSync"
    
    print(f"Email config:")
    print(f"SMTP Server: {smtp_server}")
    print(f"Sender: {sender_email}")
    print(f"Receiver: {receiver_email}")
    print(f"Port: {smtp_port}")
    print(f"Password set: {'Yes' if password else 'No'}")
    print("-" * 50)
    
    # Test data (simulating contact form submission)
    test_data = {
        "name": "John Doe",
        "email": "<EMAIL>",
        "phone": "+1234567890",
        "message": "This is a test of the contact form API. I'm interested in learning more about KaziSync HRMS.",
        "company": "Test Company",
        "subject": "Contact API Test"
    }
    
    # Create HTML message like the contact API does
    html_message = f"""
    <html>
    <body>
    <h2>TEST - Contact Form Submission - KaziSync</h2>
    <p><strong>Name:</strong> {test_data['name']}</p>
    <p><strong>Email:</strong> {test_data['email']}</p>
    <p><strong>Phone:</strong> {test_data['phone']}</p>
    <p><strong>Company:</strong> {test_data['company']}</p>
    <p><strong>Subject:</strong> {test_data['subject']}</p>
    <p><strong>Message:</strong></p>
    <p>{test_data['message']}</p>
    <hr>
    <p><em>This is a TEST from the Contact Form API Test Script</em></p>
    <p><em>Testing the same email method used in the /api/contact/send endpoint</em></p>
    </body>
    </html>
    """

    print("Attempting to send contact form test email...")
    success = send_email(sender_email, receiver_email, password, smtp_server, smtp_port, subject, html_message)
    
    if success:
        print("✅ SUCCESS: Contact form API email sent successfully!")
        print("Check your <NAME_EMAIL>")
        print("\nThis confirms that your contact form API will work correctly.")
        print("The API endpoint is: POST /api/contact/send")
        print("\nExample usage from your Next.js frontend:")
        print("""
fetch('/api/contact/send', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    name: 'John Doe',
    email: '<EMAIL>',
    phone: '+1234567890',
    message: 'I am interested in KaziSync HRMS',
    company: 'Example Corp',
    subject: 'Demo Request'
  })
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    console.log('Contact form submitted successfully!');
  } else {
    console.error('Error:', data.message);
  }
});
        """)
    else:
        print("❌ FAILED: Could not send contact form email")
        print("Check your email configuration and try again")
