#!/usr/bin/env python3
"""
Test script to verify that the customer API authentication refactoring works correctly.

This script tests:
1. Company users can access customer endpoints without passing company_id
2. Central users still need to pass company_id in requests
3. Security: Company users can't access other companies' data
4. All customer CRUD operations work with the new authentication
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import json
from datetime import datetime

# Test configuration
BASE_URL = "http://localhost:5000"  # Adjust if your Flask app runs on different port
TEST_COMPANY_ID = "10bd5d3a-1b7b-41ba-8cb1-63e5b31876f1"  # Bank of Kigali test company

def test_company_user_token():
    """Test that company users can access endpoints without passing company_id."""
    print("\n=== Testing Company User Authentication ===")
    
    # This would be a real company user token in practice
    # For testing, you'd need to login as a company user and get their token
    company_user_token = "COMPANY_USER_JWT_TOKEN_HERE"
    
    headers = {
        "Authorization": f"Bearer {company_user_token}",
        "Content-Type": "application/json"
    }
    
    # Test GET customers (should work without company_id in request)
    print("Testing GET /api/customers (company user)...")
    response = requests.get(f"{BASE_URL}/api/customers", headers=headers)
    
    if response.status_code == 200:
        print("✅ Company user can access customers without passing company_id")
    else:
        print(f"❌ Company user access failed: {response.status_code} - {response.text}")
    
    return response.status_code == 200

def test_central_user_token():
    """Test that central users need to pass company_id in requests."""
    print("\n=== Testing Central User Authentication ===")
    
    # This would be a real central user (HR/admin) token in practice
    central_user_token = "CENTRAL_USER_JWT_TOKEN_HERE"
    
    headers = {
        "Authorization": f"Bearer {central_user_token}",
        "Content-Type": "application/json"
    }
    
    # Test GET customers without company_id (should fail)
    print("Testing GET /api/customers without company_id (central user)...")
    response = requests.get(f"{BASE_URL}/api/customers", headers=headers)
    
    if response.status_code == 401:
        print("✅ Central user correctly rejected without company_id")
    else:
        print(f"❌ Central user should be rejected without company_id: {response.status_code}")
        return False
    
    # Test GET customers with company_id (should work)
    print("Testing GET /api/customers with company_id (central user)...")
    response = requests.get(f"{BASE_URL}/api/customers?company_id={TEST_COMPANY_ID}", headers=headers)
    
    if response.status_code == 200:
        print("✅ Central user can access customers when passing company_id")
        return True
    else:
        print(f"❌ Central user access failed even with company_id: {response.status_code} - {response.text}")
        return False

def test_customer_crud_operations():
    """Test that all customer CRUD operations work with new authentication."""
    print("\n=== Testing Customer CRUD Operations ===")
    
    # Use a test token (you'd need a real one for actual testing)
    test_token = "TEST_JWT_TOKEN_HERE"
    
    headers = {
        "Authorization": f"Bearer {test_token}",
        "Content-Type": "application/json"
    }
    
    # Test data
    customer_data = {
        "first_name": "Test",
        "last_name": "Customer",
        "email": f"test.customer.{datetime.now().strftime('%Y%m%d%H%M%S')}@example.com",
        "customer_segment": "Regular"
    }
    
    # Test CREATE customer (should work without company_id in body for company users)
    print("Testing POST /api/customers...")
    response = requests.post(f"{BASE_URL}/api/customers", headers=headers, json=customer_data)
    
    if response.status_code == 200:
        print("✅ Customer creation works with new authentication")
        customer_id = response.json().get("customer", {}).get("customer_id")
        
        if customer_id:
            # Test GET single customer
            print(f"Testing GET /api/customers/{customer_id}...")
            response = requests.get(f"{BASE_URL}/api/customers/{customer_id}", headers=headers)
            
            if response.status_code == 200:
                print("✅ Get single customer works")
            else:
                print(f"❌ Get single customer failed: {response.status_code}")
            
            # Test UPDATE customer
            print(f"Testing PATCH /api/customers/{customer_id}...")
            update_data = {"first_name": "Updated"}
            response = requests.patch(f"{BASE_URL}/api/customers/{customer_id}", headers=headers, json=update_data)
            
            if response.status_code == 200:
                print("✅ Customer update works")
            else:
                print(f"❌ Customer update failed: {response.status_code}")
            
            # Test DELETE customer
            print(f"Testing DELETE /api/customers/{customer_id}...")
            response = requests.delete(f"{BASE_URL}/api/customers/{customer_id}", headers=headers)
            
            if response.status_code == 200:
                print("✅ Customer deletion works")
                return True
            else:
                print(f"❌ Customer deletion failed: {response.status_code}")
        
    else:
        print(f"❌ Customer creation failed: {response.status_code} - {response.text}")
    
    return False

def main():
    """Main test function."""
    print("🧪 Testing Customer API Authentication Refactoring")
    print("=" * 55)
    
    print("⚠️  NOTE: This test requires real JWT tokens to work properly.")
    print("⚠️  Update the token variables in this script with real tokens from your system.")
    print("⚠️  For now, this script shows the test structure and expected behavior.")
    
    # In a real test environment, you would:
    # 1. Login as a company user and get their JWT token
    # 2. Login as a central user and get their JWT token  
    # 3. Run the actual HTTP requests against your running Flask app
    
    print("\n📋 Expected Behavior After Refactoring:")
    print("✅ Company users: Can access all customer endpoints without passing company_id")
    print("✅ Central users: Must pass company_id in query params or request body")
    print("✅ Security: Company users automatically limited to their company's data")
    print("✅ Backward compatibility: Central users can still manage multiple companies")
    
    print("\n🔧 To run actual tests:")
    print("1. Start your Flask application")
    print("2. Login to get real JWT tokens for company and central users")
    print("3. Update the token variables in this script")
    print("4. Run this script again")
    
    return True

if __name__ == "__main__":
    with app.app_context() if 'app' in globals() else nullcontext():
        success = main()
        sys.exit(0 if success else 1)

# Null context manager for when app is not available
from contextlib import nullcontext
