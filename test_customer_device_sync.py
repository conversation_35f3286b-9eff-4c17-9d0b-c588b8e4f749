#!/usr/bin/env python3
"""
Test script to verify that the refactored Employee.sync_with_devices() method
works correctly for both Employee and Customer objects.

This script tests:
1. Employee sync (backward compatibility)
2. Customer sync (new functionality)
3. Proper Person record creation with correct linking
4. EnrollInfo record creation
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from application.database import DatabaseConnection
from application.Models.employees.employee import Employee
from application.Models.customers.customer import Customer
from application.Models.Person import Person
from application.Models.EnrollInfo import EnrollInfo
from application.Models.company import Company
import uuid
from datetime import date

def test_employee_sync(session, company_id):
    """Test that employee sync still works (backward compatibility)."""
    print("\n=== Testing Employee Sync (Backward Compatibility) ===")
    
    # Create a test employee
    employee_data = {
        'first_name': 'Test',
        'last_name': 'Employee',
        'email': f'test.employee.{uuid.uuid4().hex[:8]}@example.com',
        'status': 'active'
    }
    
    employee = Employee.create_employee(session, **employee_data)
    if not employee:
        print("❌ Failed to create test employee")
        return False
    
    print(f"✅ Created test employee: {employee.employee_id}")
    
    # Test sync with devices
    sync_result = Employee.sync_with_devices(session, employee, company_id, "create")
    
    if not sync_result["success"]:
        print(f"❌ Employee sync failed: {sync_result['message']}")
        return False
    
    print(f"✅ Employee sync successful: {sync_result['message']}")
    
    # Verify Person record was created with employee_id
    person = session.query(Person).filter_by(employee_id=str(employee.employee_id)).first()
    if not person:
        print("❌ Person record not found for employee")
        return False
    
    print(f"✅ Person record created: ID={person.id}, Name='{person.name}', employee_id={person.employee_id}")
    
    # Verify EnrollInfo record was created
    enroll_info = session.query(EnrollInfo).filter_by(enroll_id=person.id, backupnum=-1).first()
    if not enroll_info:
        print("❌ EnrollInfo record not found")
        return False
    
    print(f"✅ EnrollInfo record created: ID={enroll_info.id}, enroll_id={enroll_info.enroll_id}")
    
    return True

def test_customer_sync(session, company_id):
    """Test that customer sync works (new functionality)."""
    print("\n=== Testing Customer Sync (New Functionality) ===")
    
    # Create a test customer
    customer_data = {
        'first_name': 'Test',
        'last_name': 'Customer',
        'email': f'test.customer.{uuid.uuid4().hex[:8]}@example.com',
        'customer_segment': 'Regular',
        'status': 'active'
    }
    
    customer = Customer.create_customer(session, **customer_data)
    if not customer:
        print("❌ Failed to create test customer")
        return False
    
    print(f"✅ Created test customer: {customer.customer_id}")
    
    # Test sync with devices (using the same method!)
    sync_result = Employee.sync_with_devices(session, customer, company_id, "create")
    
    if not sync_result["success"]:
        print(f"❌ Customer sync failed: {sync_result['message']}")
        return False
    
    print(f"✅ Customer sync successful: {sync_result['message']}")
    
    # Verify Person record was created with customer_id
    person = session.query(Person).filter_by(customer_id=customer.customer_id).first()
    if not person:
        print("❌ Person record not found for customer")
        return False
    
    print(f"✅ Person record created: ID={person.id}, Name='{person.name}', customer_id={person.customer_id}")
    
    # Verify EnrollInfo record was created
    enroll_info = session.query(EnrollInfo).filter_by(enroll_id=person.id, backupnum=-1).first()
    if not enroll_info:
        print("❌ EnrollInfo record not found")
        return False
    
    print(f"✅ EnrollInfo record created: ID={enroll_info.id}, enroll_id={enroll_info.enroll_id}")
    
    return True

def main():
    """Main test function."""
    print("🧪 Testing Customer Device Sync Implementation")
    print("=" * 50)
    
    # Use a test company ID (you may need to adjust this)
    test_company_id = "10bd5d3a-1b7b-41ba-8cb1-63e5b31876f1"  # Bank of Kigali test company
    
    # Get database name for the test company
    database_name = Company.get_database_given_company_id(test_company_id)
    if not database_name:
        print(f"❌ Could not find database for company {test_company_id}")
        print("Please update the test_company_id in this script to a valid company ID")
        return False
    
    print(f"📊 Using database: {database_name}")
    print(f"🏢 Using company: {test_company_id}")
    
    # Connect to the company database
    db_connection = DatabaseConnection()
    
    try:
        with db_connection.get_session(database_name) as session:
            # Test employee sync (backward compatibility)
            employee_success = test_employee_sync(session, test_company_id)
            
            # Test customer sync (new functionality)
            customer_success = test_customer_sync(session, test_company_id)
            
            # Overall result
            if employee_success and customer_success:
                print("\n🎉 ALL TESTS PASSED!")
                print("✅ Employee sync works (backward compatibility maintained)")
                print("✅ Customer sync works (new functionality implemented)")
                print("✅ Both employees and customers can be enrolled on biometric devices")
                return True
            else:
                print("\n❌ SOME TESTS FAILED!")
                return False
                
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    with app.app_context():
        success = main()
        sys.exit(0 if success else 1)
