import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def send_email(sender_email, receiver_email, password, smtp_server, smtp_port, subject, html_message):
    try:
        # Connect to SMTP server
        server = smtplib.SMTP_SSL(smtp_server, smtp_port)
        server.login(sender_email, password)

        # Create a multipart message
        message = MIMEMultipart()
        message["From"] = sender_email
        message["To"] = receiver_email
        message["Subject"] = subject

        # Attach HTML message
        message.attach(MIMEText(html_message, "html"))

        # Send email
        server.sendmail(sender_email, receiver_email, message.as_string())
        server.quit()
        print("Email sent successfully!")
        return True
    except Exception as e:
        print(f"Failed to send email. Error: {e}")
        return False

if __name__ == '__main__':
    # Get email config from environment
    sender_email = os.getenv('MAIL_USERNAME')
    password = os.getenv('MAIL_PASSWORD')
    smtp_server = os.getenv('MAIL_SERVER')
    receiver_email = "<EMAIL>"
    smtp_port = 465  # SSL port
    subject = "Test Contact Form - KaziSync - Rugema"
    
    print(f"Email config:")
    print(f"SMTP Server: {smtp_server}")
    print(f"Sender: {sender_email}")
    print(f"Receiver: {receiver_email}")
    print(f"Port: {smtp_port}")
    print(f"Password set: {'Yes' if password else 'No'}")
    print("-" * 50)
    
    html_message = f"""
    <html>
    <body>
    <h2>Test Contact Form Submission</h2>
    <p><strong>Name:</strong> John Doe</p>
    <p><strong>Email:</strong> <EMAIL></p>
    <p><strong>Phone:</strong> +1234567890</p>
    <p><strong>Message:</strong></p>
    <p>This is a test message from the KaziSync contact form to verify email sending is working correctly.</p>
    <hr>
    <p><em>Sent from KaziSync HRMS Contact Form</em></p>
    </body>
    </html>
    """

    print("Attempting to send test email...")
    success = send_email(sender_email, receiver_email, password, smtp_server, smtp_port, subject, html_message)
    
    if success:
        print("✅ SUCCESS: Email sent successfully!")
        print("Check your <NAME_EMAIL>")
    else:
        print("❌ FAILED: Could not send email")
        print("Check your email configuration and try again")
