import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import os
from dotenv import load_dotenv
import socket

# Load environment variables
load_dotenv()

def test_smtp_connection(smtp_server, port, use_ssl=True):
    """Test if we can connect to SMTP server"""
    try:
        print(f"Testing connection to {smtp_server}:{port} (SSL: {use_ssl})")
        
        if use_ssl:
            server = smtplib.SMTP_SSL(smtp_server, port, timeout=10)
        else:
            server = smtplib.SMTP(smtp_server, port, timeout=10)
            server.starttls()
        
        server.quit()
        print(f"✅ Connection successful to {smtp_server}:{port}")
        return True
    except Exception as e:
        print(f"❌ Connection failed to {smtp_server}:{port}: {e}")
        return False

def send_email_test(smtp_server, port, use_ssl=True):
    """Test sending email with specific configuration"""
    try:
        sender_email = os.getenv('MAIL_USERNAME')
        password = os.getenv('MAIL_PASSWORD')
        receiver_email = "<EMAIL>"
        
        print(f"\nTrying to send email via {smtp_server}:{port} (SSL: {use_ssl})")
        
        if use_ssl:
            server = smtplib.SMTP_SSL(smtp_server, port, timeout=10)
        else:
            server = smtplib.SMTP(smtp_server, port, timeout=10)
            server.starttls()
        
        server.login(sender_email, password)

        # Create a simple message
        message = MIMEMultipart()
        message["From"] = sender_email
        message["To"] = receiver_email
        message["Subject"] = "Test Email from KaziSync"

        html_message = """
        <html>
        <body>
        <h2>Test Email</h2>
        <p>This is a test email to verify SMTP configuration.</p>
        </body>
        </html>
        """
        message.attach(MIMEText(html_message, "html"))

        server.sendmail(sender_email, receiver_email, message.as_string())
        server.quit()
        print(f"✅ Email sent successfully via {smtp_server}:{port}")
        return True
    except Exception as e:
        print(f"❌ Failed to send email via {smtp_server}:{port}: {e}")
        return False

if __name__ == '__main__':
    smtp_server = os.getenv('MAIL_SERVER', 'mail.kazisync.com')
    
    print("KaziSync Email Configuration Test")
    print("=" * 50)
    print(f"SMTP Server: {smtp_server}")
    print(f"Username: {os.getenv('MAIL_USERNAME')}")
    print(f"Password set: {'Yes' if os.getenv('MAIL_PASSWORD') else 'No'}")
    print("=" * 50)
    
    # Test different configurations
    configs_to_test = [
        (smtp_server, 465, True),   # SSL
        (smtp_server, 587, False),  # TLS
        (smtp_server, 25, False),   # Plain SMTP with STARTTLS
    ]
    
    print("\n1. Testing SMTP connections...")
    working_configs = []
    for server, port, use_ssl in configs_to_test:
        if test_smtp_connection(server, port, use_ssl):
            working_configs.append((server, port, use_ssl))
    
    if not working_configs:
        print("\n❌ No SMTP connections work. Possible issues:")
        print("1. SMTP server address is wrong")
        print("2. Firewall blocking SMTP ports")
        print("3. Hosting provider blocks SMTP")
        print("4. Need to enable SMTP in cPanel")
        
        # Test if we can resolve the hostname
        try:
            ip = socket.gethostbyname(smtp_server)
            print(f"✅ DNS resolution works: {smtp_server} -> {ip}")
        except Exception as e:
            print(f"❌ DNS resolution failed: {e}")
    else:
        print(f"\n✅ Found {len(working_configs)} working SMTP configuration(s)")
        
        print("\n2. Testing email sending...")
        for server, port, use_ssl in working_configs:
            if send_email_test(server, port, use_ssl):
                print(f"\n🎉 SUCCESS! Use this configuration:")
                print(f"   Server: {server}")
                print(f"   Port: {port}")
                print(f"   SSL: {use_ssl}")
                break
        else:
            print("\n❌ All email sending attempts failed")
            print("Check your email credentials or contact your hosting provider")
