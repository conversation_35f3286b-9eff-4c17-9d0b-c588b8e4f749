#!/usr/bin/env python3
"""
Test script for KaziSync Email Management System
Tests OTP generation, email sending, and template rendering
"""

import os
import sys
import uuid
from datetime import datetime

# Add the application directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'application'))

from flask import Flask
from flask_mail import Mail
from application.config.email_config import EmailConfig
from application.services.email.email_service import EmailService
from application.services.email.template_manager import TemplateManager
from application.services.auth.otp_service import OTPService
from application.utils.db_connection import DatabaseConnection

def create_test_app():
    """Create a test Flask app for email testing"""
    app = Flask(__name__)
    
    # Configure email
    email_config = EmailConfig.get_mail_config()
    for key, value in email_config.items():
        app.config[key] = value
    
    # Override for testing (disable actual sending)
    app.config['MAIL_SUPPRESS_SEND'] = True
    
    return app

def test_template_rendering():
    """Test email template rendering"""
    print("🧪 Testing Email Template Rendering...")
    
    try:
        template_manager = TemplateManager()
        
        # Test OTP template
        context = {
            'user_name': 'John Doe',
            'otp_code': '123456',
            'expiry_minutes': 5,
            'purpose': 'login'
        }
        
        rendered_html = template_manager.render_template('auth/otp_code.html', context)
        
        if 'John Doe' in rendered_html and '123456' in rendered_html:
            print("✅ OTP template rendering: PASSED")
        else:
            print("❌ OTP template rendering: FAILED")
            
        # Test leave notification template
        leave_context = {
            'employee_name': 'Jane Smith',
            'leave_type': 'Annual Leave',
            'start_date': '2024-01-15',
            'end_date': '2024-01-20',
            'total_days': 5,
            'reason': 'Family vacation'
        }
        
        leave_html = template_manager.render_template('leave/request_submitted.html', leave_context)
        
        if 'Jane Smith' in leave_html and 'Annual Leave' in leave_html:
            print("✅ Leave template rendering: PASSED")
        else:
            print("❌ Leave template rendering: FAILED")
            
    except Exception as e:
        print(f"❌ Template rendering test failed: {str(e)}")

def test_email_service():
    """Test email service functionality"""
    print("\n🧪 Testing Email Service...")
    
    try:
        app = create_test_app()
        
        with app.app_context():
            mail = Mail(app)
            email_service = EmailService(mail)
            email_service.initialize_app(app, mail)
            
            # Test email validation
            valid_email = email_service.validate_email_address('<EMAIL>')
            invalid_email = email_service.validate_email_address('invalid-email')
            
            if valid_email and not invalid_email:
                print("✅ Email validation: PASSED")
            else:
                print("❌ Email validation: FAILED")
            
            # Test templated email (won't actually send due to MAIL_SUPPRESS_SEND)
            success = email_service.send_templated_email(
                recipients=['<EMAIL>'],
                subject='Test Email',
                template_path='auth/otp_code.html',
                context={
                    'user_name': 'Test User',
                    'otp_code': '654321',
                    'expiry_minutes': 5
                },
                send_async=False
            )
            
            if success:
                print("✅ Templated email sending: PASSED")
            else:
                print("❌ Templated email sending: FAILED")
                
    except Exception as e:
        print(f"❌ Email service test failed: {str(e)}")

def test_otp_service():
    """Test OTP service functionality"""
    print("\n🧪 Testing OTP Service...")
    
    try:
        otp_service = OTPService()
        
        # Test OTP code generation
        otp_code = otp_service.generate_otp_code()
        
        if len(otp_code) == 6 and otp_code.isdigit():
            print("✅ OTP code generation: PASSED")
        else:
            print("❌ OTP code generation: FAILED")
            
        # Test OTP code generation with custom length
        custom_otp = otp_service.generate_otp_code(8)
        
        if len(custom_otp) == 8 and custom_otp.isdigit():
            print("✅ Custom length OTP generation: PASSED")
        else:
            print("❌ Custom length OTP generation: FAILED")
            
    except Exception as e:
        print(f"❌ OTP service test failed: {str(e)}")

def test_email_masking():
    """Test email masking functionality"""
    print("\n🧪 Testing Email Masking...")
    
    try:
        from application.Helpers.helper_methods import HelperMethods
        
        test_cases = [
            ('<EMAIL>', 'j**<EMAIL>'),
            ('<EMAIL>', '<EMAIL>'),
            ('<EMAIL>', 'a*@test.com'),
            ('<EMAIL>', 't*******<EMAIL>')
        ]
        
        all_passed = True
        for email, expected in test_cases:
            masked = HelperMethods.mask_email(email)
            if masked == expected:
                print(f"✅ {email} -> {masked}")
            else:
                print(f"❌ {email} -> {masked} (expected: {expected})")
                all_passed = False
        
        if all_passed:
            print("✅ Email masking: ALL TESTS PASSED")
        else:
            print("❌ Email masking: SOME TESTS FAILED")
            
    except Exception as e:
        print(f"❌ Email masking test failed: {str(e)}")

def test_configuration():
    """Test email configuration"""
    print("\n🧪 Testing Email Configuration...")
    
    try:
        config = EmailConfig()
        
        # Test configuration retrieval
        mail_config = config.get_mail_config()
        
        required_keys = ['MAIL_SERVER', 'MAIL_PORT', 'MAIL_USERNAME', 'MAIL_DEFAULT_SENDER']
        missing_keys = [key for key in required_keys if key not in mail_config]
        
        if not missing_keys:
            print("✅ Email configuration: PASSED")
            print(f"   📧 Mail Server: {mail_config['MAIL_SERVER']}")
            print(f"   🔌 Mail Port: {mail_config['MAIL_PORT']}")
            print(f"   👤 Username: {mail_config['MAIL_USERNAME']}")
        else:
            print(f"❌ Email configuration: MISSING KEYS: {missing_keys}")
            
    except Exception as e:
        print(f"❌ Email configuration test failed: {str(e)}")

def main():
    """Run all email system tests"""
    print("🚀 KaziSync Email Management System - Test Suite")
    print("=" * 60)
    
    # Run all tests
    test_configuration()
    test_template_rendering()
    test_email_service()
    test_otp_service()
    test_email_masking()
    
    print("\n" + "=" * 60)
    print("📋 Test Summary:")
    print("   - Email configuration and setup")
    print("   - Template rendering (OTP, Leave notifications)")
    print("   - Email service functionality")
    print("   - OTP generation and validation")
    print("   - Email address masking")
    print("\n💡 Note: Actual email sending is disabled in test mode")
    print("   To test real email sending, update your .env file with:")
    print("   - MAIL_SERVER=your_cpanel_mail_server")
    print("   - MAIL_USERNAME=<EMAIL>")
    print("   - MAIL_PASSWORD=your_email_password")
    print("   - Set MAIL_SUPPRESS_SEND=False in email_config.py")

if __name__ == '__main__':
    main()
