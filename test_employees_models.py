import os
import sys
from flask import Flask
from dotenv import load_dotenv
from application.database import central_db
from application.Models.employees import Employee, Department, Attendance
from datetime import datetime, date

# Load environment variables
load_dotenv()

# Create a Flask app for testing
app = Flask(__name__)
app.config["SQLALCHEMY_DATABASE_URI"] = os.getenv("SQLALCHEMY_DATABASE_URI")
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY')

# Initialize the database
central_db.init_app(app)

def test_create_tables():
    """Test creating the employee-related tables."""
    with app.app_context():
        # Create tables
        central_db.create_all()
        print("Tables created successfully!")

def test_create_employee():
    """Test creating an employee."""
    with app.app_context():
        # Create a department first
        department = Department(
            name="Engineering",
            description="Software Engineering Department"
        )
        central_db.session.add(department)
        central_db.session.commit()
        print(f"Department created: {department.name} (ID: {department.id})")
        
        # Create an employee
        employee = Employee(
            employee_id="EMP001",
            first_name="John",
            last_name="Doe",
            email="<EMAIL>",
            phone_number="1234567890",
            department=department.name,
            position="Software Engineer",
            hire_date=date.today(),
            status="active"
        )
        central_db.session.add(employee)
        central_db.session.commit()
        print(f"Employee created: {employee.first_name} {employee.last_name} (ID: {employee.id})")
        
        # Create an attendance record
        attendance = Attendance(
            employee_id=employee.id,
            date=date.today(),
            check_in_time=datetime.now(),
            status="present",
            source="manual",
            notes="Test attendance record"
        )
        central_db.session.add(attendance)
        central_db.session.commit()
        print(f"Attendance record created for {employee.first_name} {employee.last_name}")
        
        return employee.id, department.id, attendance.id

def test_query_data(employee_id, department_id, attendance_id):
    """Test querying the created data."""
    with app.app_context():
        # Query employee
        employee = Employee.query.get(employee_id)
        print(f"Retrieved employee: {employee.first_name} {employee.last_name}")
        print(f"Employee details: {employee.to_dict()}")
        
        # Query department
        department = Department.query.get(department_id)
        print(f"Retrieved department: {department.name}")
        print(f"Department details: {department.to_dict()}")
        
        # Query attendance
        attendance = Attendance.query.get(attendance_id)
        print(f"Retrieved attendance record for date: {attendance.date}")
        print(f"Attendance details: {attendance.to_dict()}")

def cleanup(employee_id, department_id, attendance_id):
    """Clean up test data."""
    with app.app_context():
        # Delete attendance
        attendance = Attendance.query.get(attendance_id)
        if attendance:
            central_db.session.delete(attendance)
            central_db.session.commit()
            print(f"Deleted attendance record with ID: {attendance_id}")
        
        # Delete employee
        employee = Employee.query.get(employee_id)
        if employee:
            central_db.session.delete(employee)
            central_db.session.commit()
            print(f"Deleted employee with ID: {employee_id}")
        
        # Delete department
        department = Department.query.get(department_id)
        if department:
            central_db.session.delete(department)
            central_db.session.commit()
            print(f"Deleted department with ID: {department_id}")

if __name__ == "__main__":
    # Test creating tables
    test_create_tables()
    
    # Test creating and querying data
    try:
        employee_id, department_id, attendance_id = test_create_employee()
        test_query_data(employee_id, department_id, attendance_id)
        
        # Uncomment to clean up test data
        # cleanup(employee_id, department_id, attendance_id)
    except Exception as e:
        print(f"Error during testing: {str(e)}")
