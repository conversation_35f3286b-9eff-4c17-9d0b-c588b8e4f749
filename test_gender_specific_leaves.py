#!/usr/bin/env python3
"""
Test script to verify gender-specific leave allocation is working correctly.

This script tests that:
1. Male employees only get paternity leave (not maternity)
2. Female employees only get maternity leave (not paternity for males)
3. Both genders get annual and sick leave
4. Circumstantial leaves are available to all genders
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import json
from datetime import datetime, date, timedelta

class GenderLeaveTest:
    def __init__(self, base_url, company_id, auth_token):
        self.base_url = base_url.rstrip('/')
        self.company_id = company_id
        self.headers = {
            'Authorization': f'Bearer {auth_token}',
            'Content-Type': 'application/json'
        }
    
    def test_employee_leave_balances_by_gender(self):
        """Test that employees get correct leave types based on gender."""
        print("🧪 Testing Gender-Specific Leave Allocation")
        print("=" * 50)
        
        # Get employees
        url = f"{self.base_url}/api/employees"
        params = {'company_id': self.company_id, 'per_page': 20}
        
        response = requests.get(url, params=params, headers=self.headers)
        
        if response.status_code != 200:
            print(f"❌ Failed to get employees: {response.status_code} - {response.text}")
            return False
        
        data = response.json()
        employees = data.get('employees', [])
        
        if not employees:
            print("❌ No employees found")
            return False
        
        print(f"✅ Found {len(employees)} employees")
        
        # Test each employee's leave balances
        gender_results = {'male': [], 'female': [], 'unknown': []}
        
        for employee in employees:
            employee_id = employee['employee_id']
            gender = employee.get('gender', 'unknown').lower()
            name = f"{employee['first_name']} {employee['last_name']}"
            
            print(f"\n👤 Testing {name} (Gender: {gender})")
            
            # Get leave balances
            balance_url = f"{self.base_url}/api/leave/balances"
            balance_params = {
                'company_id': self.company_id,
                'employee_id': employee_id
            }
            
            balance_response = requests.get(balance_url, params=balance_params, headers=self.headers)
            
            if balance_response.status_code != 200:
                print(f"   ❌ Failed to get balances: {balance_response.status_code}")
                continue
            
            balance_data = balance_response.json()
            balances = balance_data.get('leave_balances', [])
            
            # Analyze leave types
            leave_types = [balance['leave_type_name'] for balance in balances]
            
            result = {
                'name': name,
                'gender': gender,
                'leave_types': leave_types,
                'has_maternity': 'Maternity Leave' in leave_types,
                'has_paternity': 'Paternity Leave' in leave_types,
                'has_annual': 'Annual Leave' in leave_types,
                'has_sick': 'Sick Leave' in leave_types,
                'circumstantial_count': len([lt for lt in leave_types if 'Circumstantial' in lt])
            }
            
            gender_results[gender].append(result)
            
            # Print results for this employee
            print(f"   📋 Leave types: {len(leave_types)}")
            for leave_type in leave_types:
                print(f"      • {leave_type}")
        
        # Analyze results
        print("\n📊 GENDER-SPECIFIC LEAVE ANALYSIS")
        print("=" * 50)
        
        # Test male employees
        male_employees = gender_results['male']
        if male_employees:
            print(f"\n👨 MALE EMPLOYEES ({len(male_employees)}):")
            for emp in male_employees:
                status = "✅" if not emp['has_maternity'] and emp['has_paternity'] else "❌"
                print(f"   {status} {emp['name']}")
                if emp['has_maternity']:
                    print(f"      ❌ ERROR: Male employee has maternity leave!")
                if not emp['has_paternity']:
                    print(f"      ⚠️  WARNING: Male employee missing paternity leave")
        
        # Test female employees  
        female_employees = gender_results['female']
        if female_employees:
            print(f"\n👩 FEMALE EMPLOYEES ({len(female_employees)}):")
            for emp in female_employees:
                status = "✅" if emp['has_maternity'] and not emp['has_paternity'] else "❌"
                print(f"   {status} {emp['name']}")
                if emp['has_paternity']:
                    print(f"      ❌ ERROR: Female employee has paternity leave!")
                if not emp['has_maternity']:
                    print(f"      ⚠️  WARNING: Female employee missing maternity leave")
        
        # Test universal leaves
        print(f"\n🌍 UNIVERSAL LEAVES:")
        all_employees = male_employees + female_employees + gender_results['unknown']
        
        for emp in all_employees:
            issues = []
            if not emp['has_annual']:
                issues.append("Missing Annual Leave")
            if not emp['has_sick']:
                issues.append("Missing Sick Leave")
            if emp['circumstantial_count'] == 0:
                issues.append("Missing Circumstantial Leaves")
            
            status = "✅" if not issues else "❌"
            print(f"   {status} {emp['name']}")
            for issue in issues:
                print(f"      ❌ {issue}")
        
        # Summary
        print(f"\n📈 SUMMARY:")
        total_employees = len(all_employees)
        correct_gender_allocation = 0
        
        for emp in male_employees:
            if not emp['has_maternity'] and emp['has_paternity']:
                correct_gender_allocation += 1
        
        for emp in female_employees:
            if emp['has_maternity'] and not emp['has_paternity']:
                correct_gender_allocation += 1
        
        success_rate = (correct_gender_allocation / total_employees * 100) if total_employees > 0 else 0
        
        print(f"   • Total Employees: {total_employees}")
        print(f"   • Correct Gender Allocation: {correct_gender_allocation}")
        print(f"   • Success Rate: {success_rate:.1f}%")
        
        if success_rate == 100:
            print(f"\n🎉 PERFECT! All employees have correct gender-specific leave allocation!")
            return True
        else:
            print(f"\n⚠️  Issues found with gender-specific leave allocation")
            return False

def main():
    """Main function to run the test."""
    if len(sys.argv) != 4:
        print("Usage: python test_gender_specific_leaves.py <base_url> <company_id> <auth_token>")
        print("\nExample:")
        print("  python test_gender_specific_leaves.py http://localhost:9001 12345678-1234-1234-1234-123456789012 your_jwt_token")
        sys.exit(1)
    
    base_url = sys.argv[1]
    company_id = sys.argv[2]
    auth_token = sys.argv[3]
    
    tester = GenderLeaveTest(base_url, company_id, auth_token)
    success = tester.test_employee_leave_balances_by_gender()
    
    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()
