#!/usr/bin/env python3
"""
Test script for the Leave Management System

This script tests the basic functionality of the leave management system
to ensure everything is working correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import json
from datetime import datetime, date, timedelta

class LeaveSystemTester:
    def __init__(self, base_url, company_id, auth_token):
        self.base_url = base_url.rstrip('/')
        self.company_id = company_id
        self.headers = {
            'Authorization': f'Bearer {auth_token}',
            'Content-Type': 'application/json'
        }
    
    def test_get_leave_types(self):
        """Test getting leave types."""
        print("🧪 Testing: Get Leave Types")
        
        url = f"{self.base_url}/api/leave/types"
        params = {'company_id': self.company_id}
        
        response = requests.get(url, params=params, headers=self.headers)
        
        if response.status_code == 200:
            data = response.json()
            leave_types = data.get('leave_types', [])
            print(f"   ✅ Found {len(leave_types)} leave types")
            for lt in leave_types:
                print(f"      • {lt['name']} ({lt['code']})")
            return leave_types
        else:
            print(f"   ❌ Failed: {response.status_code} - {response.text}")
            return []
    
    def test_get_leave_policies(self):
        """Test getting leave policies."""
        print("\n🧪 Testing: Get Leave Policies")
        
        url = f"{self.base_url}/api/leave/policies"
        params = {'company_id': self.company_id}
        
        response = requests.get(url, params=params, headers=self.headers)
        
        if response.status_code == 200:
            data = response.json()
            policies = data.get('leave_policies', [])
            print(f"   ✅ Found {len(policies)} leave policies")
            for policy in policies:
                print(f"      • {policy.get('leave_type_name', 'Unknown')}: {policy['days_allowed']} days")
            return policies
        else:
            print(f"   ❌ Failed: {response.status_code} - {response.text}")
            return []
    
    def test_get_employees(self):
        """Test getting employees."""
        print("\n🧪 Testing: Get Employees")
        
        url = f"{self.base_url}/api/employees"
        params = {'company_id': self.company_id, 'per_page': 5}
        
        response = requests.get(url, params=params, headers=self.headers)
        
        if response.status_code == 200:
            data = response.json()
            employees = data.get('employees', [])
            print(f"   ✅ Found {len(employees)} employees (showing first 5)")
            for emp in employees:
                print(f"      • {emp['first_name']} {emp['last_name']} (ID: {emp['employee_id']})")
            return employees
        else:
            print(f"   ❌ Failed: {response.status_code} - {response.text}")
            return []
    
    def test_get_leave_balances(self, employee_id):
        """Test getting leave balances for an employee."""
        print(f"\n🧪 Testing: Get Leave Balances for Employee {employee_id}")
        
        url = f"{self.base_url}/api/leave/balances"
        params = {
            'company_id': self.company_id,
            'employee_id': employee_id
        }
        
        response = requests.get(url, params=params, headers=self.headers)
        
        if response.status_code == 200:
            data = response.json()
            balances = data.get('leave_balances', [])
            print(f"   ✅ Found {len(balances)} leave balances")
            for balance in balances:
                available = balance['total_days'] - balance['used_days'] - balance['pending_days']
                print(f"      • {balance['leave_type_name']}: {available}/{balance['total_days']} days available")
            return balances
        else:
            print(f"   ❌ Failed: {response.status_code} - {response.text}")
            return []
    
    def test_create_leave_request(self, employee_id, leave_type_id):
        """Test creating a leave request."""
        print(f"\n🧪 Testing: Create Leave Request")
        
        # Create a leave request for next week
        start_date = (datetime.now() + timedelta(days=7)).date()
        end_date = start_date + timedelta(days=2)  # 3-day leave
        
        url = f"{self.base_url}/api/leave/requests"
        data = {
            'company_id': self.company_id,
            'employee_id': employee_id,
            'leave_type_id': leave_type_id,
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d'),
            'reason': 'Test leave request from automated test'
        }
        
        response = requests.post(url, json=data, headers=self.headers)
        
        if response.status_code in [200, 201]:
            data = response.json()
            leave_request = data.get('leave_request')
            print(f"   ✅ Created leave request (ID: {leave_request['request_id']})")
            print(f"      • Dates: {leave_request['start_date']} to {leave_request['end_date']}")
            print(f"      • Status: {leave_request['status']}")
            print(f"      • Total Days: {leave_request['total_days']}")
            return leave_request
        else:
            print(f"   ❌ Failed: {response.status_code} - {response.text}")
            return None
    
    def test_get_leave_requests(self, employee_id=None):
        """Test getting leave requests."""
        print(f"\n🧪 Testing: Get Leave Requests")
        
        url = f"{self.base_url}/api/leave/requests"
        params = {'company_id': self.company_id}
        if employee_id:
            params['employee_id'] = employee_id
        
        response = requests.get(url, params=params, headers=self.headers)
        
        if response.status_code == 200:
            data = response.json()
            requests_data = data.get('leave_requests', [])
            print(f"   ✅ Found {len(requests_data)} leave requests")
            for req in requests_data[:3]:  # Show first 3
                print(f"      • {req['start_date']} to {req['end_date']} - {req['status']}")
            return requests_data
        else:
            print(f"   ❌ Failed: {response.status_code} - {response.text}")
            return []
    
    def run_full_test(self):
        """Run a comprehensive test of the leave system."""
        print("🚀 Starting Leave Management System Test")
        print("=" * 50)
        
        # Test 1: Get leave types
        leave_types = self.test_get_leave_types()
        if not leave_types:
            print("❌ Cannot continue without leave types")
            return False
        
        # Test 2: Get leave policies
        policies = self.test_get_leave_policies()
        if not policies:
            print("❌ Cannot continue without leave policies")
            return False
        
        # Test 3: Get employees
        employees = self.test_get_employees()
        if not employees:
            print("❌ Cannot continue without employees")
            return False
        
        # Use first employee for testing
        test_employee = employees[0]
        employee_id = test_employee['employee_id']
        
        # Test 4: Get leave balances
        balances = self.test_get_leave_balances(employee_id)
        
        # Test 5: Get existing leave requests
        existing_requests = self.test_get_leave_requests(employee_id)
        
        # Test 6: Create a new leave request (if we have balances)
        if balances and leave_types:
            # Find annual leave type
            annual_leave = next((lt for lt in leave_types if lt['code'] == 'ANNUAL'), None)
            if annual_leave:
                new_request = self.test_create_leave_request(employee_id, annual_leave['leave_type_id'])
                if new_request:
                    print(f"\n✅ Test leave request created successfully!")
        
        print("\n🎉 Leave Management System Test Completed!")
        print("\n📊 Test Summary:")
        print(f"   • Leave Types: {len(leave_types)}")
        print(f"   • Leave Policies: {len(policies)}")
        print(f"   • Employees: {len(employees)}")
        print(f"   • Leave Balances: {len(balances)}")
        print(f"   • Existing Requests: {len(existing_requests)}")
        
        return True

def main():
    """Main function to run the test."""
    if len(sys.argv) != 4:
        print("Usage: python test_leave_system.py <base_url> <company_id> <auth_token>")
        print("\nExample:")
        print("  python test_leave_system.py http://localhost:9001 12345678-1234-1234-1234-123456789012 your_jwt_token")
        sys.exit(1)
    
    base_url = sys.argv[1]
    company_id = sys.argv[2]
    auth_token = sys.argv[3]
    
    tester = LeaveSystemTester(base_url, company_id, auth_token)
    success = tester.run_full_test()
    
    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()
