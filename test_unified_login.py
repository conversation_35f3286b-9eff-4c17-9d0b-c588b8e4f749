#!/usr/bin/env python3
"""
Test script for the unified login system.
Tests both company user and central user login.
"""

import requests
import json
import sys

def test_login(base_url, username, password, user_type):
    """Test login for a specific user type."""
    print(f"\n🧪 Testing {user_type} login...")
    print(f"Username: {username}")
    print(f"Password: {password}")
    
    login_data = {
        'username': username,
        'password': password
    }
    
    try:
        response = requests.post(f'{base_url}/login', 
                               json=login_data, 
                               headers={'Content-Type': 'application/json'},
                               timeout=10)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Login successful!")
            print(f"User Type: {result.get('user_type', 'unknown')}")
            print(f"User Role: {result.get('user', {}).get('role', 'unknown')}")
            print(f"Redirect To: {result.get('redirect_to', 'unknown')}")
            
            if result.get('user_type') == 'company_user':
                print(f"Company: {result.get('company', {}).get('company_name', 'unknown')}")
                print(f"Employee ID: {result.get('user', {}).get('employee_id', 'unknown')}")
            elif result.get('user_type') == 'central_user':
                companies = result.get('companies', [])
                print(f"Associated Companies: {len(companies)}")
                for company in companies:
                    print(f"  - {company.get('company_name', 'unknown')}")
            
            return True, result
        else:
            result = response.json()
            print(f"❌ Login failed: {result.get('message', 'Unknown error')}")
            return False, result
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - Server not running")
        return False, {"error": "connection_failed"}
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False, {"error": str(e)}

def main():
    """Main test function."""
    base_url = "http://localhost:9001"
    
    print("🚀 Testing Unified Login System")
    print("=" * 50)
    
    # Test cases
    test_cases = [
        {
            "username": "kiarafbo.jnabdia",  # Encoded company user
            "password": "Q2!jMGJ53n6h",
            "type": "Company User (Employee)"
        },
        {
            "username": "<EMAIL>",  # Example central user
            "password": "admin_password",
            "type": "Central User (HR/Admin)"
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        success, result = test_login(
            base_url, 
            test_case["username"], 
            test_case["password"], 
            test_case["type"]
        )
        results.append({
            "type": test_case["type"],
            "success": success,
            "result": result
        })
    
    # Summary
    print("\n📊 Test Summary")
    print("=" * 50)
    
    for result in results:
        status = "✅ PASS" if result["success"] else "❌ FAIL"
        print(f"{result['type']}: {status}")
        
        if not result["success"] and "error" in result["result"]:
            if result["result"]["error"] == "connection_failed":
                print("  Note: Start your Flask server with: python app.py")
            else:
                print(f"  Error: {result['result'].get('message', result['result']['error'])}")
    
    print("\n🔧 How to test:")
    print("1. Start your Flask server: python app.py")
    print("2. Run this script: python test_unified_login.py")
    print("3. Check the logs for detailed authentication flow")

if __name__ == "__main__":
    main()
