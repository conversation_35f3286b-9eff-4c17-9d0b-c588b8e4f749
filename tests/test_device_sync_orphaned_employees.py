"""
Test Device Sync - Prevent Orphaned Employees

This test suite verifies that the device sync code does not create orphaned
employee records (employees not linked to any person record).

Created: 2025-10-01
Purpose: Prevent regression of the 33,371 orphaned employees issue
"""

import unittest
import uuid
from unittest.mock import Mock, patch, MagicMock
from application.Services.EmployeeValidationService import EmployeeValidationService


class TestDeviceSyncOrphanedEmployees(unittest.TestCase):
    """Test cases to prevent orphaned employee creation during device sync."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.session = Mock()
        self.test_employee_id = uuid.uuid4()
        self.test_person_id = 1
    
    def test_validate_employee_creation_success(self):
        """Test that validation passes when employee can be safely created."""
        # Mock: Employee doesn't exist
        self.session.query().filter_by().first.return_value = None
        
        # Mock: Person with employee_id exists
        mock_person = Mock()
        mock_person.id = self.test_person_id
        mock_person.employee_id = str(self.test_employee_id)
        
        # First call returns None (employee doesn't exist)
        # Second call returns person (person exists)
        self.session.query().filter_by().first.side_effect = [None, mock_person]
        
        is_valid, error_msg = EmployeeValidationService.validate_employee_creation(
            self.session, self.test_employee_id, self.test_person_id
        )
        
        self.assertTrue(is_valid)
        self.assertEqual(error_msg, "")
    
    def test_validate_employee_creation_already_exists(self):
        """Test that validation fails when employee already exists."""
        # Mock: Employee already exists
        mock_employee = Mock()
        mock_employee.employee_id = self.test_employee_id
        self.session.query().filter_by().first.return_value = mock_employee
        
        is_valid, error_msg = EmployeeValidationService.validate_employee_creation(
            self.session, self.test_employee_id
        )
        
        self.assertFalse(is_valid)
        self.assertIn("already exists", error_msg)
    
    def test_validate_employee_creation_no_person_linkage(self):
        """Test that validation fails when no person record exists with the employee_id."""
        # Mock: Employee doesn't exist
        # Mock: Person with employee_id doesn't exist
        self.session.query().filter_by().first.side_effect = [None, None]
        
        is_valid, error_msg = EmployeeValidationService.validate_employee_creation(
            self.session, self.test_employee_id
        )
        
        self.assertFalse(is_valid)
        self.assertIn("No person record found", error_msg)
        self.assertIn("Cannot create orphaned employee", error_msg)
    
    def test_safe_get_employee_by_id_found(self):
        """Test safe employee retrieval when employee exists."""
        mock_employee = Mock()
        mock_employee.employee_id = self.test_employee_id
        self.session.query().filter_by().first.return_value = mock_employee
        
        result = EmployeeValidationService.safe_get_employee_by_id(
            self.session, self.test_employee_id
        )
        
        self.assertIsNotNone(result)
        self.assertEqual(result.employee_id, self.test_employee_id)
    
    def test_safe_get_employee_by_id_not_found(self):
        """Test safe employee retrieval when employee doesn't exist."""
        self.session.query().filter_by().first.return_value = None
        
        result = EmployeeValidationService.safe_get_employee_by_id(
            self.session, self.test_employee_id
        )
        
        self.assertIsNone(result)
    
    def test_safe_get_employee_by_id_database_error(self):
        """Test that database errors are raised, not swallowed."""
        self.session.query().filter_by().first.side_effect = Exception("Database connection failed")
        
        with self.assertRaises(Exception) as context:
            EmployeeValidationService.safe_get_employee_by_id(
                self.session, self.test_employee_id
            )
        
        self.assertIn("Database connection failed", str(context.exception))
    
    def test_verify_employee_person_linkage_linked(self):
        """Test verification when employee is properly linked to person."""
        mock_person = Mock()
        mock_person.id = self.test_person_id
        mock_person.employee_id = str(self.test_employee_id)
        self.session.query().filter_by().first.return_value = mock_person
        
        is_linked, person = EmployeeValidationService.verify_employee_person_linkage(
            self.session, self.test_employee_id
        )
        
        self.assertTrue(is_linked)
        self.assertIsNotNone(person)
        self.assertEqual(person.id, self.test_person_id)
    
    def test_verify_employee_person_linkage_orphaned(self):
        """Test verification when employee is orphaned (no person linkage)."""
        self.session.query().filter_by().first.return_value = None
        
        is_linked, person = EmployeeValidationService.verify_employee_person_linkage(
            self.session, self.test_employee_id
        )
        
        self.assertFalse(is_linked)
        self.assertIsNone(person)
    
    def test_create_employee_safely_success(self):
        """Test safe employee creation when all validations pass."""
        employee_data = {
            'employee_id': self.test_employee_id,
            'first_name': 'Test',
            'last_name': 'Employee',
            'status': 'active'
        }
        
        # Mock validation success
        mock_person = Mock()
        mock_person.id = self.test_person_id
        self.session.query().filter_by().first.side_effect = [None, mock_person]
        
        # Mock employee creation success
        with patch('application.Services.EmployeeValidationService.Employee') as MockEmployee:
            mock_employee = Mock()
            MockEmployee.create_employee.return_value = mock_employee
            
            success, employee, message = EmployeeValidationService.create_employee_safely(
                self.session, employee_data, self.test_person_id
            )
        
        self.assertTrue(success)
        self.assertIsNotNone(employee)
        self.assertIn("successfully", message)
    
    def test_create_employee_safely_validation_fails(self):
        """Test that employee creation is prevented when validation fails."""
        employee_data = {
            'employee_id': self.test_employee_id,
            'first_name': 'Test',
            'last_name': 'Employee',
            'status': 'active'
        }
        
        # Mock validation failure (no person linkage)
        self.session.query().filter_by().first.side_effect = [None, None]
        
        success, employee, message = EmployeeValidationService.create_employee_safely(
            self.session, employee_data
        )
        
        self.assertFalse(success)
        self.assertIsNone(employee)
        self.assertIn("No person record found", message)


class TestDeviceSyncIntegration(unittest.TestCase):
    """Integration tests for device sync employee creation."""
    
    def test_device_sync_does_not_create_orphaned_employees(self):
        """
        Integration test: Verify device sync doesn't create orphaned employees.
        
        This test simulates the scenario that caused 33,371 orphaned employees:
        1. Person exists with employee_id
        2. Employee record exists
        3. Device sync runs
        4. Should NOT create a new employee record
        """
        # This would be an integration test that requires a test database
        # For now, we'll mark it as a placeholder
        pass
    
    def test_device_sync_handles_database_errors_gracefully(self):
        """
        Test that device sync handles database errors without creating orphaned employees.
        
        This test verifies that when Employee.get_employee_by_id() raises an exception,
        the code doesn't create a new employee record.
        """
        # This would be an integration test
        pass


if __name__ == '__main__':
    unittest.main()

