"""
Test EnrollInfo ImagePath Fix

This test suite verifies that the device sync code properly handles enrollment
information updates for different backup number types without causing Key<PERSON>rro<PERSON>
for missing 'imagepath' keys.

Created: 2025-10-01
Purpose: Prevent KeyError: 'imagepath' exceptions during device sync
"""

import unittest
import uuid
from unittest.mock import Mock, patch, MagicMock


class TestEnrollInfoImagePathFix(unittest.TestCase):
    """Test cases to prevent KeyError: 'imagepath' during device sync."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.session = Mock()
        self.enroll_id = 123
        self.signatures = "base64_encoded_biometric_data"
        
    def test_update_enroll_info_by_backupnum_fingerprint(self):
        """Test updating fingerprint data (backupnum 0-9) without imagepath."""
        from application.Models.EnrollInfo import update_enroll_info_by_backupnum
        
        # Mock existing enrollment info record
        mock_enroll_info = Mock()
        mock_enroll_info.signatures = "old_signatures"
        self.session.query().filter_by().first.return_value = mock_enroll_info
        
        # Update fingerprint data (no imagepath)
        result = update_enroll_info_by_backupnum(
            self.session, 
            self.enroll_id, 
            backupnum=1,  # Fingerprint
            signatures=self.signatures
        )
        
        # Verify update was successful
        self.assertTrue(result)
        self.assertEqual(mock_enroll_info.signatures, self.signatures)
        self.session.commit.assert_called_once()
    
    def test_update_enroll_info_by_backupnum_password(self):
        """Test updating password data (backupnum 10) without imagepath."""
        from application.Models.EnrollInfo import update_enroll_info_by_backupnum
        
        # Mock existing enrollment info record
        mock_enroll_info = Mock()
        mock_enroll_info.signatures = "old_password"
        self.session.query().filter_by().first.return_value = mock_enroll_info
        
        # Update password data (no imagepath)
        result = update_enroll_info_by_backupnum(
            self.session, 
            self.enroll_id, 
            backupnum=10,  # Password
            signatures="new_password_hash"
        )
        
        # Verify update was successful
        self.assertTrue(result)
        self.assertEqual(mock_enroll_info.signatures, "new_password_hash")
        self.session.commit.assert_called_once()
    
    def test_update_enroll_info_by_backupnum_card(self):
        """Test updating card data (backupnum 11) without imagepath."""
        from application.Models.EnrollInfo import update_enroll_info_by_backupnum
        
        # Mock existing enrollment info record
        mock_enroll_info = Mock()
        mock_enroll_info.signatures = "old_card_number"
        self.session.query().filter_by().first.return_value = mock_enroll_info
        
        # Update card data (no imagepath)
        result = update_enroll_info_by_backupnum(
            self.session, 
            self.enroll_id, 
            backupnum=11,  # Card
            signatures="1234567890"
        )
        
        # Verify update was successful
        self.assertTrue(result)
        self.assertEqual(mock_enroll_info.signatures, "1234567890")
        self.session.commit.assert_called_once()
    
    def test_update_enroll_info_by_backupnum_face_image(self):
        """Test updating face image data (backupnum 50) with imagepath."""
        from application.Models.EnrollInfo import update_enroll_info_by_backupnum
        
        # Mock existing enrollment info record
        mock_enroll_info = Mock()
        mock_enroll_info.signatures = "old_image_data"
        mock_enroll_info.imagepath = "old_image.jpg"
        self.session.query().filter_by().first.return_value = mock_enroll_info
        
        # Update face image data (with imagepath)
        result = update_enroll_info_by_backupnum(
            self.session, 
            self.enroll_id, 
            backupnum=50,  # Face image
            signatures="new_base64_image_data",
            imagepath="new_image.jpg"
        )
        
        # Verify update was successful
        self.assertTrue(result)
        self.assertEqual(mock_enroll_info.signatures, "new_base64_image_data")
        self.assertEqual(mock_enroll_info.imagepath, "new_image.jpg")
        self.session.commit.assert_called_once()
    
    def test_update_enroll_info_by_backupnum_face_image_no_imagepath(self):
        """Test updating face image data (backupnum 50) without imagepath parameter."""
        from application.Models.EnrollInfo import update_enroll_info_by_backupnum
        
        # Mock existing enrollment info record
        mock_enroll_info = Mock()
        mock_enroll_info.signatures = "old_image_data"
        mock_enroll_info.imagepath = "old_image.jpg"
        self.session.query().filter_by().first.return_value = mock_enroll_info
        
        # Update face image data (without imagepath - should only update signatures)
        result = update_enroll_info_by_backupnum(
            self.session, 
            self.enroll_id, 
            backupnum=50,  # Face image
            signatures="new_base64_image_data"
            # No imagepath parameter
        )
        
        # Verify update was successful
        self.assertTrue(result)
        self.assertEqual(mock_enroll_info.signatures, "new_base64_image_data")
        # imagepath should remain unchanged
        self.assertEqual(mock_enroll_info.imagepath, "old_image.jpg")
        self.session.commit.assert_called_once()
    
    def test_update_enroll_info_by_backupnum_record_not_found(self):
        """Test updating when no existing record is found."""
        from application.Models.EnrollInfo import update_enroll_info_by_backupnum
        
        # Mock no existing record
        self.session.query().filter_by().first.return_value = None
        
        # Try to update non-existent record
        result = update_enroll_info_by_backupnum(
            self.session, 
            self.enroll_id, 
            backupnum=1,
            signatures=self.signatures
        )
        
        # Verify update failed (record not found)
        self.assertFalse(result)
        self.session.commit.assert_not_called()
    
    def test_update_enroll_info_by_backupnum_database_error(self):
        """Test handling database errors during update."""
        from application.Models.EnrollInfo import update_enroll_info_by_backupnum
        
        # Mock database error
        self.session.query().filter_by().first.side_effect = Exception("Database connection failed")
        
        # Try to update - should raise exception
        with self.assertRaises(Exception) as context:
            update_enroll_info_by_backupnum(
                self.session, 
                self.enroll_id, 
                backupnum=1,
                signatures=self.signatures
            )
        
        self.assertIn("Database connection failed", str(context.exception))
        self.session.rollback.assert_called_once()
    
    def test_update_enroll_info2_backward_compatibility(self):
        """Test that update_enroll_info2 still works for face images."""
        from application.Models.EnrollInfo import update_enroll_info2
        
        # Mock existing face image records
        mock_enroll_info1 = Mock()
        mock_enroll_info2 = Mock()
        self.session.query().filter_by().all.return_value = [mock_enroll_info1, mock_enroll_info2]
        
        # Update face image data using old function
        result = update_enroll_info2(
            self.session, 
            self.enroll_id, 
            imagepath="test_image.jpg",
            signatures="base64_image_data"
        )
        
        # Verify both records were updated
        self.assertTrue(result)
        self.assertEqual(mock_enroll_info1.imagepath, "test_image.jpg")
        self.assertEqual(mock_enroll_info1.signatures, "base64_image_data")
        self.assertEqual(mock_enroll_info2.imagepath, "test_image.jpg")
        self.assertEqual(mock_enroll_info2.signatures, "base64_image_data")
        self.session.commit.assert_called_once()


class TestDeviceSyncEnrollInfoIntegration(unittest.TestCase):
    """Integration tests for device sync enrollment info handling."""
    
    def test_device_sync_handles_missing_imagepath_gracefully(self):
        """Test that device sync doesn't crash when imagepath is missing."""
        # This would be an integration test that simulates the device sync process
        # For now, we'll mark it as a placeholder for future implementation
        pass
    
    def test_device_sync_updates_different_backup_types(self):
        """Test that device sync can update all types of biometric data."""
        # This would test the complete flow:
        # 1. Fingerprint data (backupnum 0-9) - no imagepath
        # 2. Password data (backupnum 10) - no imagepath  
        # 3. Card data (backupnum 11) - no imagepath
        # 4. Face image data (backupnum 50) - has imagepath
        pass


if __name__ == '__main__':
    unittest.main()
