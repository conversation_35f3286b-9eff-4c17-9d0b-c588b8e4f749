"""
Test cases for HR Device Synchronization API endpoints.

These tests validate the new HR-accessible endpoints for managing
employee/customer device synchronization.
"""

import unittest
import json
import uuid
from datetime import datetime
from unittest.mock import patch, MagicMock

from app import app
from application.database import DatabaseConnection
from application.Models.MachineCommand import MachineCommand
from application.Models.Person import Person
from application.Models.employees.employee import Employee
from application.Models.customers.customer import Customer
from application.Models.company import Company


class TestHRDeviceSyncAPI(unittest.TestCase):
    """Test cases for HR Device Sync API endpoints."""

    def setUp(self):
        """Set up test fixtures."""
        self.app = app.test_client()
        self.app.testing = True
        
        # Mock JWT token for HR user
        self.hr_token = "mock_hr_token"
        self.headers = {
            'Authorization': f'Bearer {self.hr_token}',
            'Content-Type': 'application/json'
        }
        
        # Test data
        self.company_id = str(uuid.uuid4())
        self.employee_id = "EMP001"
        self.customer_id = str(uuid.uuid4())
        self.device_sn = "AYTI10087992"

    @patch('application.Routes.employees.employee_device_sync_api.get_user_context')
    @patch('application.Models.company.Company.get_database_given_company_id')
    @patch('app.db_connection.get_session')
    def test_get_employee_sync_status_success(self, mock_get_session, mock_get_db, mock_get_context):
        """Test successful retrieval of employee sync status."""
        # Mock user context
        mock_get_context.return_value = (self.company_id, "user123", None)
        
        # Mock database
        mock_get_db.return_value = "test_db"
        
        # Mock session and data
        mock_session = MagicMock()
        mock_get_session.return_value.__enter__.return_value = mock_session
        
        # Mock person and employee data
        mock_person = MagicMock()
        mock_person.id = 123
        mock_person.employee_id = self.employee_id
        mock_person.customer_id = None
        
        mock_employee = MagicMock()
        mock_employee.first_name = "John"
        mock_employee.last_name = "Doe"
        
        mock_session.query.return_value.filter_by.return_value.first.side_effect = [
            mock_employee,  # Employee query
            mock_person     # Person query
        ]
        
        # Mock machine commands
        mock_command = MagicMock()
        mock_command.id = 456
        mock_command.serial = self.device_sn
        mock_command.name = "setuserinfo"
        mock_command.status = 0
        mock_command.err_count = 15
        mock_command.content = '{"cmd":"setuserinfo","enrollid":123,"backupnum":0}'
        mock_command.run_time = datetime.now()
        mock_command.gmt_crate = datetime.now()
        mock_command.gmt_modified = datetime.now()
        
        mock_session.query.return_value.filter.return_value.all.return_value = [mock_command]
        
        # Mock WebSocket device status
        with patch('application.Routes.employees.employee_device_sync_api.ws_device') as mock_ws:
            mock_ws.get.return_value = MagicMock()  # Device is connected
            
            response = self.app.get(
                f'/api/hr/sync-status/employee/{self.employee_id}',
                headers=self.headers
            )
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        
        self.assertEqual(data['person_type'], 'employee')
        self.assertEqual(data['person_id'], self.employee_id)
        self.assertEqual(data['person_name'], 'John Doe')
        self.assertEqual(data['enroll_id'], 123)
        self.assertIn('sync_summary', data)
        self.assertIn('device_sync_details', data)

    @patch('application.Routes.employees.employee_device_sync_api.get_user_context')
    def test_get_sync_status_invalid_person_type(self, mock_get_context):
        """Test sync status with invalid person type."""
        mock_get_context.return_value = (self.company_id, "user123", None)
        
        response = self.app.get(
            '/api/hr/sync-status/invalid_type/EMP001',
            headers=self.headers
        )
        
        # Should return 404 or 400 for invalid person type
        self.assertIn(response.status_code, [400, 404])

    @patch('application.Routes.employees.employee_device_sync_api.get_user_context')
    @patch('application.Models.company.Company.get_database_given_company_id')
    @patch('app.db_connection.get_session')
    def test_get_company_sync_failures_success(self, mock_get_session, mock_get_db, mock_get_context):
        """Test successful retrieval of company sync failures."""
        # Mock user context
        mock_get_context.return_value = (self.company_id, "user123", None)
        
        # Mock database
        mock_get_db.return_value = "test_db"
        
        # Mock session
        mock_session = MagicMock()
        mock_get_session.return_value.__enter__.return_value = mock_session
        
        # Mock failed commands
        mock_command = MagicMock()
        mock_command.id = 456
        mock_command.serial = self.device_sn
        mock_command.name = "setuserinfo"
        mock_command.err_count = 15
        mock_command.status = 0
        mock_command.content = '{"cmd":"setuserinfo","enrollid":123,"backupnum":0}'
        mock_command.run_time = datetime.now()
        
        mock_session.query.return_value.filter.return_value.all.return_value = [mock_command]
        
        # Mock person and employee data
        mock_person = MagicMock()
        mock_person.id = 123
        mock_person.employee_id = self.employee_id
        mock_person.customer_id = None
        
        mock_employee = MagicMock()
        mock_employee.first_name = "John"
        mock_employee.last_name = "Doe"
        
        mock_session.query.return_value.filter_by.return_value.first.side_effect = [
            mock_person,    # Person query
            mock_employee   # Employee query
        ]
        
        response = self.app.get(
            '/api/hr/sync-failures',
            headers=self.headers
        )
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        
        self.assertEqual(data['company_id'], self.company_id)
        self.assertIn('total_failed_syncs', data)
        self.assertIn('failed_by_type', data)
        self.assertIn('failures', data)

    @patch('application.Routes.employees.employee_device_sync_api.get_user_context')
    @patch('application.Models.company.Company.get_database_given_company_id')
    @patch('app.db_connection.get_session')
    def test_retry_sync_success(self, mock_get_session, mock_get_db, mock_get_context):
        """Test successful retry of failed sync."""
        # Mock user context
        mock_get_context.return_value = (self.company_id, "user123", None)
        
        # Mock database
        mock_get_db.return_value = "test_db"
        
        # Mock session
        mock_session = MagicMock()
        mock_get_session.return_value.__enter__.return_value = mock_session
        
        # Mock person and employee data
        mock_person = MagicMock()
        mock_person.id = 123
        mock_person.employee_id = self.employee_id
        mock_person.customer_id = None
        
        mock_employee = MagicMock()
        mock_employee.first_name = "John"
        mock_employee.last_name = "Doe"
        
        mock_session.query.return_value.filter_by.return_value.first.side_effect = [
            mock_employee,  # Employee query
            mock_person     # Person query
        ]
        
        # Mock failed commands
        mock_command = MagicMock()
        mock_command.id = 456
        mock_command.serial = self.device_sn
        mock_command.name = "setuserinfo"
        mock_command.err_count = 15
        mock_command.status = 0
        mock_command.content = '{"cmd":"setuserinfo","enrollid":123,"backupnum":0}'
        
        mock_session.query.return_value.filter.return_value.all.return_value = [mock_command]
        
        # Mock WebSocket device status
        with patch('application.Routes.employees.employee_device_sync_api.ws_device') as mock_ws:
            mock_ws.get.return_value = MagicMock()  # Device is connected
            
            request_data = {
                "person_type": "employee",
                "person_id": self.employee_id,
                "device_sn": self.device_sn
            }
            
            response = self.app.post(
                '/api/hr/retry-sync',
                headers=self.headers,
                data=json.dumps(request_data)
            )
        
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        
        self.assertEqual(data['status'], 'success')
        self.assertEqual(data['person_type'], 'employee')
        self.assertEqual(data['person_id'], self.employee_id)
        self.assertGreater(data['commands_reset'], 0)
        self.assertIn('reset_details', data)

    def test_retry_sync_missing_data(self):
        """Test retry sync with missing request data."""
        response = self.app.post(
            '/api/hr/retry-sync',
            headers=self.headers,
            data=json.dumps({})
        )
        
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertIn('person_type and person_id are required', data['message'])

    def test_retry_sync_invalid_person_type(self):
        """Test retry sync with invalid person type."""
        request_data = {
            "person_type": "invalid",
            "person_id": self.employee_id
        }
        
        response = self.app.post(
            '/api/hr/retry-sync',
            headers=self.headers,
            data=json.dumps(request_data)
        )
        
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertIn('person_type must be', data['message'])

    @patch('application.Routes.employees.employee_device_sync_api.get_user_context')
    def test_authentication_required(self, mock_get_context):
        """Test that authentication is required for all endpoints."""
        mock_get_context.return_value = (None, None, "Authentication required")
        
        # Test without token
        response = self.app.get('/api/hr/sync-status/employee/EMP001')
        self.assertEqual(response.status_code, 401)
        
        response = self.app.get('/api/hr/sync-failures')
        self.assertEqual(response.status_code, 401)
        
        response = self.app.post('/api/hr/retry-sync')
        self.assertEqual(response.status_code, 401)


if __name__ == '__main__':
    unittest.main()
